#!/usr/bin/env node

/**
 * Static Mobile UX and Accessibility Audit
 * Analyzes source code and static files for mobile and accessibility compliance
 */

const fs = require('fs').promises;
const path = require('path');
const glob = require('glob');

class StaticMobileAccessibilityAuditor {
  constructor() {
    this.results = {
      sourceCodeAudit: {},
      mobileComponentAudit: {},
      accessibilityImplementation: {},
      pwaConfiguration: {},
      performanceOptimization: {},
      summary: {}
    };
    
    this.auditStartTime = new Date().toISOString();
    this.projectRoot = path.join(__dirname, '..');
  }

  async runStaticAudit() {
    console.log('\n🔍 Starting Static Mobile UX & Accessibility Audit...\n');

    try {
      // 1. Audit source code for mobile patterns
      await this.auditSourceCode();
      
      // 2. Audit mobile-specific components
      await this.auditMobileComponents();
      
      // 3. Audit accessibility implementation
      await this.auditAccessibilityImplementation();
      
      // 4. Audit PWA configuration
      await this.auditPWAConfiguration();
      
      // 5. Audit performance optimizations
      await this.auditPerformanceOptimizations();
      
      // Generate comprehensive report
      await this.generateStaticAuditReport();
      
    } catch (error) {
      console.error('❌ Audit failed:', error.message);
      this.results.summary.error = error.message;
    }
  }

  async auditSourceCode() {
    console.log('\n📱 Auditing Source Code for Mobile Patterns...');
    
    // Find all React/TypeScript files
    const sourceFiles = await this.getSourceFiles();
    
    const mobilePatterns = {
      responsiveDesign: 0,
      touchEvents: 0,
      mobileComponents: 0,
      accessibilityFeatures: 0,
      performanceOptimizations: 0
    };

    const detailedResults = {
      responsiveImplementations: [],
      touchImplementations: [],
      accessibilityImplementations: [],
      performanceImplementations: []
    };

    for (const file of sourceFiles) {
      try {
        const content = await fs.readFile(file, 'utf8');
        
        // Check for responsive design patterns
        if (this.hasResponsivePatterns(content)) {
          mobilePatterns.responsiveDesign++;
          detailedResults.responsiveImplementations.push({
            file: file,
            patterns: this.extractResponsivePatterns(content)
          });
        }
        
        // Check for touch event handling
        if (this.hasTouchEvents(content)) {
          mobilePatterns.touchEvents++;
          detailedResults.touchImplementations.push({
            file: file,
            touchEvents: this.extractTouchEvents(content)
          });
        }
        
        // Check for mobile-specific components
        if (this.hasMobileComponents(content)) {
          mobilePatterns.mobileComponents++;
        }
        
        // Check for accessibility features
        if (this.hasAccessibilityFeatures(content)) {
          mobilePatterns.accessibilityFeatures++;
          detailedResults.accessibilityImplementations.push({
            file: file,
            features: this.extractAccessibilityFeatures(content)
          });
        }
        
        // Check for performance optimizations
        if (this.hasPerformanceOptimizations(content)) {
          mobilePatterns.performanceOptimizations++;
          detailedResults.performanceImplementations.push({
            file: file,
            optimizations: this.extractPerformanceOptimizations(content)
          });
        }
        
      } catch (error) {
        console.warn(`Warning: Could not read file ${file}: ${error.message}`);
      }
    }

    this.results.sourceCodeAudit = {
      totalFiles: sourceFiles.length,
      patterns: mobilePatterns,
      detailedResults: detailedResults,
      coverage: {
        responsiveDesign: Math.round((mobilePatterns.responsiveDesign / sourceFiles.length) * 100),
        touchEvents: Math.round((mobilePatterns.touchEvents / sourceFiles.length) * 100),
        accessibility: Math.round((mobilePatterns.accessibilityFeatures / sourceFiles.length) * 100),
        performance: Math.round((mobilePatterns.performanceOptimizations / sourceFiles.length) * 100)
      }
    };

    console.log(`  ✅ Source Code Audit: ${sourceFiles.length} files analyzed`);
  }

  async auditMobileComponents() {
    console.log('\n📱 Auditing Mobile-Specific Components...');
    
    const mobileComponentsDir = path.join(this.projectRoot, 'src/components/calculator');
    const mobileLibDir = path.join(this.projectRoot, 'src/lib/mobile');
    
    // Audit mobile components
    const mobileComponents = await this.analyzeMobileComponents(mobileComponentsDir);
    
    // Audit mobile libraries
    const mobileLibraries = await this.analyzeMobileLibraries(mobileLibDir);
    
    // Check for mobile UI components
    const uiComponentsDir = path.join(this.projectRoot, 'src/components/ui');
    const mobileUIComponents = await this.analyzeMobileUIComponents(uiComponentsDir);

    this.results.mobileComponentAudit = {
      mobileComponents: mobileComponents,
      mobileLibraries: mobileLibraries,
      mobileUIComponents: mobileUIComponents,
      totalMobileFiles: mobileComponents.length + mobileLibraries.length + mobileUIComponents.filter(c => c.isMobileOptimized).length
    };

    console.log(`  ✅ Mobile Components: ${this.results.mobileComponentAudit.totalMobileFiles} mobile-optimized files found`);
  }

  async auditAccessibilityImplementation() {
    console.log('\n♿ Auditing Accessibility Implementation...');
    
    const accessibilityDir = path.join(this.projectRoot, 'src/lib/accessibility');
    
    // Check if accessibility directory exists
    let accessibilityFiles = [];
    try {
      const files = await fs.readdir(accessibilityDir);
      accessibilityFiles = files.filter(file => file.endsWith('.ts') || file.endsWith('.tsx'));
    } catch (error) {
      console.warn('Accessibility directory not found');
    }

    const accessibilityFeatures = {
      focusManagement: false,
      keyboardNavigation: false,
      screenReaderSupport: false,
      themeManagement: false,
      voiceNavigation: false,
      mobileTouchAccessibility: false,
      testing: false
    };

    const implementationDetails = {};

    for (const file of accessibilityFiles) {
      const filePath = path.join(accessibilityDir, file);
      try {
        const content = await fs.readFile(filePath, 'utf8');
        const fileName = path.basename(file, path.extname(file));
        
        implementationDetails[fileName] = {
          fileSize: content.length,
          functions: this.extractFunctions(content),
          hooks: this.extractHooks(content),
          exports: this.extractExports(content)
        };
        
        // Check for specific accessibility features
        if (fileName.includes('focus')) accessibilityFeatures.focusManagement = true;
        if (fileName.includes('keyboard')) accessibilityFeatures.keyboardNavigation = true;
        if (fileName.includes('screen-reader')) accessibilityFeatures.screenReaderSupport = true;
        if (fileName.includes('theme')) accessibilityFeatures.themeManagement = true;
        if (fileName.includes('voice')) accessibilityFeatures.voiceNavigation = true;
        if (fileName.includes('mobile-touch')) accessibilityFeatures.mobileTouchAccessibility = true;
        if (fileName.includes('testing')) accessibilityFeatures.testing = true;
        
      } catch (error) {
        console.warn(`Could not read accessibility file ${file}: ${error.message}`);
      }
    }

    // Check for ARIA implementation in components
    const componentFiles = await this.getComponentFiles();
    const ariaImplementation = await this.analyzeAriaImplementation(componentFiles);

    this.results.accessibilityImplementation = {
      accessibilityFiles: accessibilityFiles.length,
      features: accessibilityFeatures,
      implementationDetails: implementationDetails,
      ariaImplementation: ariaImplementation,
      completeness: Object.values(accessibilityFeatures).filter(Boolean).length / Object.keys(accessibilityFeatures).length * 100
    };

    console.log(`  ✅ Accessibility Implementation: ${Math.round(this.results.accessibilityImplementation.completeness)}% complete`);
  }

  async auditPWAConfiguration() {
    console.log('\n⚡ Auditing PWA Configuration...');
    
    const pwaConfig = {
      manifest: false,
      serviceWorker: false,
      offlineSupport: false,
      webAppCapable: false,
      iconConfiguration: false
    };

    const configDetails = {};

    // Check for manifest.json
    try {
      const manifestPath = path.join(this.projectRoot, 'public/manifest.json');
      const manifestContent = await fs.readFile(manifestPath, 'utf8');
      const manifest = JSON.parse(manifestContent);
      
      pwaConfig.manifest = true;
      configDetails.manifest = {
        hasName: !!manifest.name,
        hasShortName: !!manifest.short_name,
        hasStartUrl: !!manifest.start_url,
        hasDisplay: !!manifest.display,
        hasIcons: Array.isArray(manifest.icons) && manifest.icons.length > 0,
        hasThemeColor: !!manifest.theme_color,
        hasBackgroundColor: !!manifest.background_color
      };
      
      if (configDetails.manifest.hasIcons) {
        pwaConfig.iconConfiguration = true;
      }
      
    } catch (error) {
      console.warn('PWA manifest not found');
    }

    // Check for service worker
    try {
      const swPath = path.join(this.projectRoot, 'public/sw.js');
      await fs.access(swPath);
      pwaConfig.serviceWorker = true;
      
      const swContent = await fs.readFile(swPath, 'utf8');
      configDetails.serviceWorker = {
        hasOfflineSupport: swContent.includes('offline') || swContent.includes('cache'),
        hasPrecaching: swContent.includes('precache'),
        hasRuntimeCaching: swContent.includes('runtime'),
        fileSize: swContent.length
      };
      
      if (configDetails.serviceWorker.hasOfflineSupport) {
        pwaConfig.offlineSupport = true;
      }
      
    } catch (error) {
      console.warn('Service worker not found');
    }

    // Check next.config.js for PWA configuration
    try {
      const nextConfigPath = path.join(this.projectRoot, 'next.config.ts');
      const nextConfigContent = await fs.readFile(nextConfigPath, 'utf8');
      
      configDetails.nextConfig = {
        hasPWAConfig: nextConfigContent.includes('pwa') || nextConfigContent.includes('workbox'),
        hasWebAppManifest: nextConfigContent.includes('manifest'),
        content: nextConfigContent.substring(0, 500) // First 500 chars for analysis
      };
      
    } catch (error) {
      console.warn('Next.js config not found or not accessible');
    }

    this.results.pwaConfiguration = {
      features: pwaConfig,
      configDetails: configDetails,
      pwaScore: Object.values(pwaConfig).filter(Boolean).length / Object.keys(pwaConfig).length * 100
    };

    console.log(`  ✅ PWA Configuration: ${Math.round(this.results.pwaConfiguration.pwaScore)}% configured`);
  }

  async auditPerformanceOptimizations() {
    console.log('\n⚡ Auditing Performance Optimizations...');
    
    const performanceFeatures = {
      codeSpitting: false,
      lazyLoading: false,
      imageOptimization: false,
      bundleOptimization: false,
      caching: false,
      compressionGzip: false
    };

    const optimizationDetails = {};

    // Check Next.js configuration for performance features
    try {
      const nextConfigPath = path.join(this.projectRoot, 'next.config.ts');
      const nextConfigContent = await fs.readFile(nextConfigPath, 'utf8');
      
      if (nextConfigContent.includes('splitChunks') || nextConfigContent.includes('chunks')) {
        performanceFeatures.codeSpitting = true;
      }
      
      if (nextConfigContent.includes('compress') || nextConfigContent.includes('gzip')) {
        performanceFeatures.compressionGzip = true;
      }
      
      optimizationDetails.nextConfig = {
        hasOptimization: true,
        content: nextConfigContent.substring(0, 1000)
      };
      
    } catch (error) {
      console.warn('Next.js config not accessible for performance analysis');
    }

    // Check for image optimization
    const componentFiles = await this.getComponentFiles();
    let imageOptimizationFound = false;
    let lazyLoadingFound = false;

    for (const file of componentFiles.slice(0, 20)) { // Check first 20 files
      try {
        const content = await fs.readFile(file, 'utf8');
        
        if (content.includes('next/image') || content.includes('Image from')) {
          imageOptimizationFound = true;
        }
        
        if (content.includes('lazy') || content.includes('Suspense') || content.includes('loading="lazy"')) {
          lazyLoadingFound = true;
        }
        
      } catch (error) {
        // Skip file
      }
    }

    performanceFeatures.imageOptimization = imageOptimizationFound;
    performanceFeatures.lazyLoading = lazyLoadingFound;

    // Check for bundle optimization tools
    try {
      const packageJsonPath = path.join(this.projectRoot, 'package.json');
      const packageContent = await fs.readFile(packageJsonPath, 'utf8');
      const packageJson = JSON.parse(packageContent);
      
      const bundleTools = ['webpack-bundle-analyzer', 'next-bundle-analyzer', '@next/bundle-analyzer'];
      performanceFeatures.bundleOptimization = bundleTools.some(tool => 
        packageJson.dependencies?.[tool] || packageJson.devDependencies?.[tool]
      );
      
      // Check for caching libraries
      const cachingLibs = ['swr', 'react-query', '@tanstack/react-query'];
      performanceFeatures.caching = cachingLibs.some(lib => 
        packageJson.dependencies?.[lib] || packageJson.devDependencies?.[lib]
      );
      
    } catch (error) {
      console.warn('Package.json not accessible for dependency analysis');
    }

    this.results.performanceOptimization = {
      features: performanceFeatures,
      optimizationDetails: optimizationDetails,
      performanceScore: Object.values(performanceFeatures).filter(Boolean).length / Object.keys(performanceFeatures).length * 100
    };

    console.log(`  ✅ Performance Optimizations: ${Math.round(this.results.performanceOptimization.performanceScore)}% implemented`);
  }

  // Helper methods for pattern detection
  hasResponsivePatterns(content) {
    const responsivePatterns = [
      '@media',
      'breakpoint',
      'sm:', 'md:', 'lg:', 'xl:', '2xl:', // Tailwind responsive
      'viewport',
      'mobile',
      'tablet',
      'desktop'
    ];
    
    return responsivePatterns.some(pattern => content.includes(pattern));
  }

  hasTouchEvents(content) {
    const touchPatterns = [
      'onTouchStart',
      'onTouchEnd',
      'onTouchMove',
      'touchstart',
      'touchend',
      'touchmove',
      'gesture',
      'swipe',
      'pinch',
      'hammer',
      'pan'
    ];
    
    return touchPatterns.some(pattern => content.includes(pattern));
  }

  hasMobileComponents(content) {
    const mobileComponentPatterns = [
      'MobileCalculator',
      'MobileInput',
      'MobileButton',
      'TouchButton',
      'SwipeableCard',
      'BottomSheet',
      'PullToRefresh',
      'HamburgerMenu'
    ];
    
    return mobileComponentPatterns.some(pattern => content.includes(pattern));
  }

  hasAccessibilityFeatures(content) {
    const a11yPatterns = [
      'aria-',
      'role=',
      'tabindex',
      'sr-only',
      'screen-reader',
      'focus:',
      'keyboard',
      'accessible'
    ];
    
    return a11yPatterns.some(pattern => content.includes(pattern));
  }

  hasPerformanceOptimizations(content) {
    const performancePatterns = [
      'lazy',
      'Suspense',
      'memo',
      'useMemo',
      'useCallback',
      'dynamic',
      'loading=',
      'prefetch'
    ];
    
    return performancePatterns.some(pattern => content.includes(pattern));
  }

  extractResponsivePatterns(content) {
    const patterns = [];
    if (content.includes('@media')) patterns.push('CSS Media Queries');
    if (content.includes('sm:') || content.includes('md:')) patterns.push('Tailwind Responsive');
    if (content.includes('viewport')) patterns.push('Viewport Meta');
    if (content.includes('breakpoint')) patterns.push('Breakpoint System');
    return patterns;
  }

  extractTouchEvents(content) {
    const events = [];
    if (content.includes('onTouchStart')) events.push('Touch Start');
    if (content.includes('onTouchEnd')) events.push('Touch End');
    if (content.includes('onTouchMove')) events.push('Touch Move');
    if (content.includes('swipe')) events.push('Swipe Gestures');
    if (content.includes('pinch')) events.push('Pinch Gestures');
    return events;
  }

  extractAccessibilityFeatures(content) {
    const features = [];
    if (content.includes('aria-label')) features.push('ARIA Labels');
    if (content.includes('role=')) features.push('ARIA Roles');
    if (content.includes('tabindex')) features.push('Tab Index');
    if (content.includes('sr-only')) features.push('Screen Reader Only');
    if (content.includes('focus:')) features.push('Focus Styles');
    return features;
  }

  extractPerformanceOptimizations(content) {
    const optimizations = [];
    if (content.includes('lazy')) optimizations.push('Lazy Loading');
    if (content.includes('Suspense')) optimizations.push('React Suspense');
    if (content.includes('memo')) optimizations.push('Memoization');
    if (content.includes('dynamic')) optimizations.push('Dynamic Imports');
    return optimizations;
  }

  extractFunctions(content) {
    const functionRegex = /(?:function\s+(\w+)|const\s+(\w+)\s*=\s*(?:async\s+)?(?:\([^)]*\)|[^=]+)\s*=>|(\w+)\s*\([^)]*\)\s*{)/g;
    const functions = [];
    let match;
    
    while ((match = functionRegex.exec(content)) !== null) {
      const functionName = match[1] || match[2] || match[3];
      if (functionName) {
        functions.push(functionName);
      }
    }
    
    return functions.slice(0, 10); // Limit to first 10 functions
  }

  extractHooks(content) {
    const hookRegex = /use[A-Z]\w*/g;
    const hooks = content.match(hookRegex) || [];
    return [...new Set(hooks)].slice(0, 10); // Unique hooks, limit to 10
  }

  extractExports(content) {
    const exportRegex = /export\s+(?:default\s+)?(?:const\s+|function\s+|class\s+)?(\w+)/g;
    const exports = [];
    let match;
    
    while ((match = exportRegex.exec(content)) !== null) {
      exports.push(match[1]);
    }
    
    return [...new Set(exports)].slice(0, 10); // Unique exports, limit to 10
  }

  async getSourceFiles() {
    return new Promise((resolve, reject) => {
      const pattern = path.join(this.projectRoot, 'src/**/*.{ts,tsx,js,jsx}');
      glob(pattern, (err, files) => {
        if (err) reject(err);
        else resolve(files);
      });
    });
  }

  async getComponentFiles() {
    return new Promise((resolve, reject) => {
      const pattern = path.join(this.projectRoot, 'src/components/**/*.{ts,tsx}');
      glob(pattern, (err, files) => {
        if (err) reject(err);
        else resolve(files);
      });
    });
  }

  async analyzeMobileComponents(dir) {
    const components = [];
    try {
      const files = await fs.readdir(dir);
      
      for (const file of files) {
        if (file.endsWith('.tsx') || file.endsWith('.ts')) {
          const filePath = path.join(dir, file);
          const content = await fs.readFile(filePath, 'utf8');
          
          components.push({
            file: file,
            isMobileSpecific: file.toLowerCase().includes('mobile'),
            hasResponsiveFeatures: this.hasResponsivePatterns(content),
            hasTouchEvents: this.hasTouchEvents(content),
            size: content.length
          });
        }
      }
    } catch (error) {
      console.warn(`Could not analyze mobile components in ${dir}: ${error.message}`);
    }
    
    return components;
  }

  async analyzeMobileLibraries(dir) {
    const libraries = [];
    try {
      const files = await fs.readdir(dir);
      
      for (const file of files) {
        if (file.endsWith('.ts')) {
          const filePath = path.join(dir, file);
          const content = await fs.readFile(filePath, 'utf8');
          
          libraries.push({
            file: file,
            purpose: this.inferLibraryPurpose(file),
            exports: this.extractExports(content),
            size: content.length
          });
        }
      }
    } catch (error) {
      console.warn(`Could not analyze mobile libraries in ${dir}: ${error.message}`);
    }
    
    return libraries;
  }

  async analyzeMobileUIComponents(dir) {
    const components = [];
    try {
      const files = await fs.readdir(dir);
      
      for (const file of files) {
        if (file.endsWith('.tsx') || file.endsWith('.ts')) {
          const filePath = path.join(dir, file);
          const content = await fs.readFile(filePath, 'utf8');
          
          components.push({
            file: file,
            isMobileOptimized: file.toLowerCase().includes('mobile') || file.toLowerCase().includes('touch'),
            hasAccessibilityFeatures: this.hasAccessibilityFeatures(content),
            hasResponsiveFeatures: this.hasResponsivePatterns(content),
            size: content.length
          });
        }
      }
    } catch (error) {
      console.warn(`Could not analyze mobile UI components in ${dir}: ${error.message}`);
    }
    
    return components;
  }

  async analyzeAriaImplementation(files) {
    const ariaAnalysis = {
      totalFiles: files.length,
      filesWithAria: 0,
      ariaFeatures: {
        labels: 0,
        roles: 0,
        describedBy: 0,
        expanded: 0,
        hidden: 0,
        live: 0
      }
    };

    for (const file of files.slice(0, 50)) { // Analyze first 50 component files
      try {
        const content = await fs.readFile(file, 'utf8');
        
        let hasAria = false;
        
        if (content.includes('aria-label')) {
          ariaAnalysis.ariaFeatures.labels++;
          hasAria = true;
        }
        if (content.includes('role=')) {
          ariaAnalysis.ariaFeatures.roles++;
          hasAria = true;
        }
        if (content.includes('aria-describedby')) {
          ariaAnalysis.ariaFeatures.describedBy++;
          hasAria = true;
        }
        if (content.includes('aria-expanded')) {
          ariaAnalysis.ariaFeatures.expanded++;
          hasAria = true;
        }
        if (content.includes('aria-hidden')) {
          ariaAnalysis.ariaFeatures.hidden++;
          hasAria = true;
        }
        if (content.includes('aria-live')) {
          ariaAnalysis.ariaFeatures.live++;
          hasAria = true;
        }
        
        if (hasAria) {
          ariaAnalysis.filesWithAria++;
        }
        
      } catch (error) {
        // Skip file
      }
    }

    return ariaAnalysis;
  }

  inferLibraryPurpose(filename) {
    const purposeMap = {
      'gesture': 'Gesture Navigation',
      'haptic': 'Haptic Feedback',
      'touch': 'Touch Handling',
      'offline': 'Offline Management',
      'performance': 'Performance Optimization',
      'pwa': 'PWA Management'
    };

    for (const [key, purpose] of Object.entries(purposeMap)) {
      if (filename.toLowerCase().includes(key)) {
        return purpose;
      }
    }

    return 'General Mobile Utility';
  }

  async generateStaticAuditReport() {
    console.log('\n📋 Generating Static Audit Report...');

    const categories = ['sourceCodeAudit', 'mobileComponentAudit', 'accessibilityImplementation', 'pwaConfiguration', 'performanceOptimization'];
    
    const scores = {
      sourceCode: this.calculateSourceCodeScore(),
      mobileComponents: this.calculateMobileComponentScore(),
      accessibility: this.results.accessibilityImplementation.completeness || 0,
      pwa: this.results.pwaConfiguration.pwaScore || 0,
      performance: this.results.performanceOptimization.performanceScore || 0
    };

    const overallScore = Math.round(Object.values(scores).reduce((sum, score) => sum + score, 0) / Object.values(scores).length);

    this.results.summary = {
      auditStartTime: this.auditStartTime,
      auditEndTime: new Date().toISOString(),
      overallScore: overallScore,
      categoryScores: scores,
      recommendations: this.generateStaticRecommendations(scores)
    };

    const report = this.createStaticAuditReport();
    
    const reportPath = path.join(this.projectRoot, `COMPREHENSIVE_MOBILE_ACCESSIBILITY_TEST_REPORT.md`);
    await fs.writeFile(reportPath, report);

    console.log(`\n✅ Static audit report saved to: ${reportPath}`);
    console.log(`📊 Overall Score: ${overallScore}%`);
    
    return this.results;
  }

  calculateSourceCodeScore() {
    if (!this.results.sourceCodeAudit.coverage) return 0;
    
    const coverage = this.results.sourceCodeAudit.coverage;
    return Math.round((coverage.responsiveDesign + coverage.accessibility + coverage.performance) / 3);
  }

  calculateMobileComponentScore() {
    const audit = this.results.mobileComponentAudit;
    if (!audit) return 0;
    
    const mobileOptimizedComponents = audit.mobileComponents.filter(c => c.isMobileSpecific || c.hasResponsiveFeatures).length;
    const totalComponents = audit.mobileComponents.length || 1;
    
    return Math.round((mobileOptimizedComponents / totalComponents) * 100);
  }

  generateStaticRecommendations(scores) {
    const recommendations = [];

    if (scores.accessibility < 80) {
      recommendations.push({
        category: 'Accessibility',
        priority: 'HIGH',
        action: 'Implement missing accessibility features (focus management, keyboard navigation, screen reader support)'
      });
    }

    if (scores.mobileComponents < 70) {
      recommendations.push({
        category: 'Mobile Components',
        priority: 'HIGH',
        action: 'Create more mobile-specific components and improve responsive design'
      });
    }

    if (scores.performance < 70) {
      recommendations.push({
        category: 'Performance',
        priority: 'MEDIUM',
        action: 'Implement more performance optimizations (lazy loading, code splitting, image optimization)'
      });
    }

    if (scores.pwa < 60) {
      recommendations.push({
        category: 'PWA',
        priority: 'MEDIUM',
        action: 'Complete PWA implementation (service worker, offline support, manifest configuration)'
      });
    }

    return recommendations;
  }

  createStaticAuditReport() {
    const timestamp = new Date().toISOString();
    
    return `# Comprehensive Mobile UX & Accessibility Testing Report

**Project**: Nirmaan AI Construction Calculator  
**Test Date**: ${timestamp}  
**Test Type**: Static Code Analysis  
**Overall Score**: ${this.results.summary.overallScore}%  
**Status**: ${this.results.summary.overallScore >= 80 ? '✅ EXCELLENT' : this.results.summary.overallScore >= 70 ? '✅ GOOD' : this.results.summary.overallScore >= 60 ? '⚠️ FAIR' : '❌ NEEDS IMPROVEMENT'}

## Executive Summary

This comprehensive static analysis evaluates the mobile user experience and accessibility implementation of the Nirmaan AI Construction Calculator platform. The analysis examines source code patterns, component implementations, accessibility features, PWA configuration, and performance optimizations.

### Overall Results
- **Overall Score**: ${this.results.summary.overallScore}%
- **Source Code Quality**: ${this.results.summary.categoryScores.sourceCode}%
- **Mobile Component Implementation**: ${this.results.summary.categoryScores.mobileComponents}%
- **Accessibility Implementation**: ${this.results.summary.categoryScores.accessibility}%
- **PWA Configuration**: ${this.results.summary.categoryScores.pwa}%
- **Performance Optimization**: ${this.results.summary.categoryScores.performance}%

## Detailed Analysis Results

### 1. Source Code Audit 📱
**Score**: ${this.results.summary.categoryScores.sourceCode}%

**Files Analyzed**: ${this.results.sourceCodeAudit.totalFiles || 0}

**Pattern Coverage**:
- Responsive Design: ${this.results.sourceCodeAudit.coverage?.responsiveDesign || 0}% of files
- Touch Events: ${this.results.sourceCodeAudit.coverage?.touchEvents || 0}% of files
- Accessibility Features: ${this.results.sourceCodeAudit.coverage?.accessibility || 0}% of files
- Performance Optimizations: ${this.results.sourceCodeAudit.coverage?.performance || 0}% of files

**Implementations Found**:
- Responsive Design Patterns: ${this.results.sourceCodeAudit.patterns?.responsiveDesign || 0} files
- Touch Event Handling: ${this.results.sourceCodeAudit.patterns?.touchEvents || 0} files
- Mobile Components: ${this.results.sourceCodeAudit.patterns?.mobileComponents || 0} files
- Accessibility Features: ${this.results.sourceCodeAudit.patterns?.accessibilityFeatures || 0} files
- Performance Optimizations: ${this.results.sourceCodeAudit.patterns?.performanceOptimizations || 0} files

### 2. Mobile Component Audit 📱
**Score**: ${this.results.summary.categoryScores.mobileComponents}%

**Mobile Components Found**: ${this.results.mobileComponentAudit?.mobileComponents?.length || 0}
**Mobile Libraries Found**: ${this.results.mobileComponentAudit?.mobileLibraries?.length || 0}
**Mobile UI Components**: ${this.results.mobileComponentAudit?.mobileUIComponents?.filter(c => c.isMobileOptimized).length || 0}

**Mobile Component Details**:
${this.results.mobileComponentAudit?.mobileComponents?.map(comp => 
  `- ${comp.file}: ${comp.isMobileSpecific ? '✅ Mobile-specific' : '⚠️ General'} (${comp.size} bytes)`
).join('\n') || 'No mobile components analyzed'}

**Mobile Libraries**:
${this.results.mobileComponentAudit?.mobileLibraries?.map(lib => 
  `- ${lib.file}: ${lib.purpose} (${lib.exports?.length || 0} exports)`
).join('\n') || 'No mobile libraries found'}

### 3. Accessibility Implementation Audit ♿
**Score**: ${this.results.summary.categoryScores.accessibility}%

**Accessibility Files Found**: ${this.results.accessibilityImplementation?.accessibilityFiles || 0}

**Features Implemented**:
${Object.entries(this.results.accessibilityImplementation?.features || {}).map(([feature, implemented]) => 
  `- ${feature}: ${implemented ? '✅ Implemented' : '❌ Missing'}`
).join('\n')}

**ARIA Implementation**:
- Files with ARIA: ${this.results.accessibilityImplementation?.ariaImplementation?.filesWithAria || 0}/${this.results.accessibilityImplementation?.ariaImplementation?.totalFiles || 0}
- ARIA Labels: ${this.results.accessibilityImplementation?.ariaImplementation?.ariaFeatures?.labels || 0} instances
- ARIA Roles: ${this.results.accessibilityImplementation?.ariaImplementation?.ariaFeatures?.roles || 0} instances
- ARIA Live Regions: ${this.results.accessibilityImplementation?.ariaImplementation?.ariaFeatures?.live || 0} instances

### 4. PWA Configuration Audit ⚡
**Score**: ${this.results.summary.categoryScores.pwa}%

**PWA Features**:
${Object.entries(this.results.pwaConfiguration?.features || {}).map(([feature, configured]) => 
  `- ${feature}: ${configured ? '✅ Configured' : '❌ Missing'}`
).join('\n')}

**Configuration Details**:
${this.results.pwaConfiguration?.configDetails?.manifest ? `
- **Manifest**: ✅ Found
  - Name: ${this.results.pwaConfiguration.configDetails.manifest.hasName ? '✅' : '❌'}
  - Short Name: ${this.results.pwaConfiguration.configDetails.manifest.hasShortName ? '✅' : '❌'}
  - Start URL: ${this.results.pwaConfiguration.configDetails.manifest.hasStartUrl ? '✅' : '❌'}
  - Icons: ${this.results.pwaConfiguration.configDetails.manifest.hasIcons ? '✅' : '❌'}
  - Theme Color: ${this.results.pwaConfiguration.configDetails.manifest.hasThemeColor ? '✅' : '❌'}
` : '- **Manifest**: ❌ Not found'}

${this.results.pwaConfiguration?.configDetails?.serviceWorker ? `
- **Service Worker**: ✅ Found
  - Offline Support: ${this.results.pwaConfiguration.configDetails.serviceWorker.hasOfflineSupport ? '✅' : '❌'}
  - Precaching: ${this.results.pwaConfiguration.configDetails.serviceWorker.hasPrecaching ? '✅' : '❌'}
  - Runtime Caching: ${this.results.pwaConfiguration.configDetails.serviceWorker.hasRuntimeCaching ? '✅' : '❌'}
` : '- **Service Worker**: ❌ Not found'}

### 5. Performance Optimization Audit ⚡
**Score**: ${this.results.summary.categoryScores.performance}%

**Performance Features**:
${Object.entries(this.results.performanceOptimization?.features || {}).map(([feature, implemented]) => 
  `- ${feature}: ${implemented ? '✅ Implemented' : '❌ Missing'}`
).join('\n')}

## Recommendations

${this.results.summary.recommendations?.map(rec => 
  `### ${rec.category} (Priority: ${rec.priority})
${rec.action}`
).join('\n\n') || 'No specific recommendations generated'}

## Mobile UX Implementation Status

### ✅ Strengths Found
${this.generateStrengthsList()}

### ⚠️ Areas for Improvement
${this.generateImprovementsList()}

### 🚀 Next Steps
1. **High Priority**: ${this.results.summary.recommendations?.filter(r => r.priority === 'HIGH').map(r => r.action).join(', ') || 'Continue current implementation'}
2. **Medium Priority**: ${this.results.summary.recommendations?.filter(r => r.priority === 'MEDIUM').map(r => r.action).join(', ') || 'Enhance existing features'}
3. **Low Priority**: Focus on advanced mobile features and optimizations

## Technical Implementation Details

### Code Quality Metrics
- **Total Source Files**: ${this.results.sourceCodeAudit?.totalFiles || 0}
- **Mobile-Optimized Components**: ${this.results.mobileComponentAudit?.totalMobileFiles || 0}
- **Accessibility Coverage**: ${this.results.accessibilityImplementation?.completeness || 0}%
- **PWA Readiness**: ${this.results.pwaConfiguration?.pwaScore || 0}%

### Architecture Assessment
The project demonstrates a ${this.getArchitectureAssessment()} approach to mobile UX and accessibility implementation.

### Compliance Status
- **Mobile Responsiveness**: ${this.results.summary.categoryScores.mobileComponents >= 70 ? '✅ Compliant' : '❌ Non-compliant'}
- **Accessibility Standards**: ${this.results.summary.categoryScores.accessibility >= 80 ? '✅ WCAG 2.1 Ready' : '❌ Needs WCAG Work'}
- **PWA Standards**: ${this.results.summary.categoryScores.pwa >= 60 ? '✅ PWA Ready' : '❌ Needs PWA Work'}
- **Performance Standards**: ${this.results.summary.categoryScores.performance >= 70 ? '✅ Optimized' : '❌ Needs Optimization'}

---

**Report Generated**: ${timestamp}  
**Analysis Type**: Static Code Analysis  
**Files Analyzed**: ${this.results.sourceCodeAudit?.totalFiles || 0} source files
`;
  }

  generateStrengthsList() {
    const strengths = [];
    
    if (this.results.accessibilityImplementation?.accessibilityFiles > 5) {
      strengths.push('Comprehensive accessibility implementation with dedicated modules');
    }
    
    if (this.results.mobileComponentAudit?.mobileLibraries?.length > 3) {
      strengths.push('Well-structured mobile library implementation');
    }
    
    if (this.results.pwaConfiguration?.features?.manifest) {
      strengths.push('PWA manifest configuration present');
    }
    
    if (this.results.performanceOptimization?.features?.lazyLoading) {
      strengths.push('Performance optimizations implemented (lazy loading)');
    }
    
    if (this.results.sourceCodeAudit?.coverage?.accessibility > 50) {
      strengths.push('Good accessibility pattern coverage across components');
    }
    
    return strengths.length > 0 ? strengths.map(s => `- ${s}`).join('\n') : '- Implementation analysis needed for detailed assessment';
  }

  generateImprovementsList() {
    const improvements = [];
    
    if (this.results.summary.categoryScores.accessibility < 80) {
      improvements.push('Accessibility implementation needs completion');
    }
    
    if (this.results.summary.categoryScores.mobileComponents < 70) {
      improvements.push('Mobile component coverage needs improvement');
    }
    
    if (this.results.summary.categoryScores.pwa < 60) {
      improvements.push('PWA configuration needs completion');
    }
    
    if (this.results.summary.categoryScores.performance < 70) {
      improvements.push('Performance optimization implementation needed');
    }
    
    if (this.results.sourceCodeAudit?.coverage?.touchEvents < 30) {
      improvements.push('Touch event handling needs enhancement');
    }
    
    return improvements.length > 0 ? improvements.map(i => `- ${i}`).join('\n') : '- Implementation appears comprehensive';
  }

  getArchitectureAssessment() {
    const score = this.results.summary.overallScore;
    
    if (score >= 85) return 'excellent and comprehensive';
    if (score >= 75) return 'solid and well-structured';
    if (score >= 65) return 'good foundation with room for improvement';
    if (score >= 50) return 'basic implementation requiring enhancement';
    return 'foundational implementation needing significant development';
  }
}

// Export for use in other modules
module.exports = StaticMobileAccessibilityAuditor;

// Run the audit if this file is executed directly
if (require.main === module) {
  (async () => {
    const auditor = new StaticMobileAccessibilityAuditor();
    await auditor.runStaticAudit();
  })().catch(console.error);
}