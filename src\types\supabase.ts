export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[];

export interface Database {
  public: {
    Tables: {
      projects: {
        Row: {
          id: string;
          user_id: string | null;
          name: string;
          location: string;
          area_sqft: number;
          floors: number;
          quality_tier: 'smart' | 'premium' | 'luxury';
          calculation_data: Json;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id?: string | null;
          name: string;
          location: string;
          area_sqft: number;
          floors: number;
          quality_tier: 'smart' | 'premium' | 'luxury';
          calculation_data?: Json;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string | null;
          name?: string;
          location?: string;
          area_sqft?: number;
          floors?: number;
          quality_tier?: 'smart' | 'premium' | 'luxury';
          calculation_data?: Json;
          created_at?: string;
          updated_at?: string;
        };
      };
      materials: {
        Row: {
          id: string;
          category: string;
          name: string;
          brand: string | null;
          unit: string;
          base_price: number | null;
          specifications: Json;
          pricing: Json;
          quality_score: number | null;
          popularity_rank: number | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          category: string;
          name: string;
          brand?: string | null;
          unit: string;
          base_price?: number | null;
          specifications?: Json;
          pricing?: Json;
          quality_score?: number | null;
          popularity_rank?: number | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          category?: string;
          name?: string;
          brand?: string | null;
          unit?: string;
          base_price?: number | null;
          specifications?: Json;
          pricing?: Json;
          quality_score?: number | null;
          popularity_rank?: number | null;
          created_at?: string;
          updated_at?: string;
        };
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      [_ in never]: never;
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
}
