"use client";

import React, { useRef, useEffect, useState } from 'react';
import { cn } from '@/lib/utils';
import TouchHandler, { SwipeGesture } from '@/lib/mobile/touch-handler';
import hapticFeedback from '@/lib/mobile/haptic-feedback';

interface GestureCardProps {
  children: React.ReactNode;
  className?: string;
  swipeEnabled?: boolean;
  onSwipeLeft?: (gesture: SwipeGesture) => void;
  onSwipeRight?: (gesture: SwipeGesture) => void;
  onSwipeUp?: (gesture: SwipeGesture) => void;
  onSwipeDown?: (gesture: SwipeGesture) => void;
  onTap?: () => void;
  onLongPress?: () => void;
  dismissible?: boolean;
  onDismiss?: () => void;
  hapticEnabled?: boolean;
  elevationOnPress?: boolean;
  scaleOnPress?: boolean;
  dragEnabled?: boolean;
  snapBack?: boolean;
}

const GestureCard: React.FC<GestureCardProps> = ({
  children,
  className,
  swipeEnabled = true,
  onSwipeLeft,
  onSwipeRight,
  onSwipeUp,
  onSwipeDown,
  onTap,
  onLongPress,
  dismissible = false,
  onDismiss,
  hapticEnabled = true,
  elevationOnPress = true,
  scaleOnPress = true,
  dragEnabled = false,
  snapBack = true
}) => {
  const cardRef = useRef<HTMLDivElement>(null);
  const touchHandlerRef = useRef<TouchHandler | null>(null);
  const [isPressed, setIsPressed] = useState(false);
  const [transform, setTransform] = useState({ x: 0, y: 0, scale: 1 });
  const [isDragging, setIsDragging] = useState(false);
  const longPressTimer = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (!cardRef.current || !swipeEnabled) return;

    const touchHandler = new TouchHandler(cardRef.current, {
      swipeThreshold: 50,
      enableHaptic: hapticEnabled,
      preventScrolling: dragEnabled
    });

    touchHandlerRef.current = touchHandler;

    // Swipe handlers
    if (onSwipeLeft) {
      touchHandler.on('swipeleft', ({ gesture }: { gesture: SwipeGesture }) => {
        if (hapticEnabled) hapticFeedback.swipe();
        onSwipeLeft(gesture);
        
        if (dismissible) {
          animateDismiss('left');
        }
      });
    }

    if (onSwipeRight) {
      touchHandler.on('swiperight', ({ gesture }: { gesture: SwipeGesture }) => {
        if (hapticEnabled) hapticFeedback.swipe();
        onSwipeRight(gesture);
        
        if (dismissible) {
          animateDismiss('right');
        }
      });
    }

    if (onSwipeUp) {
      touchHandler.on('swipeup', ({ gesture }: { gesture: SwipeGesture }) => {
        if (hapticEnabled) hapticFeedback.swipe();
        onSwipeUp(gesture);
        
        if (dismissible) {
          animateDismiss('up');
        }
      });
    }

    if (onSwipeDown) {
      touchHandler.on('swipedown', ({ gesture }: { gesture: SwipeGesture }) => {
        if (hapticEnabled) hapticFeedback.swipe();
        onSwipeDown(gesture);
        
        if (dismissible) {
          animateDismiss('down');
        }
      });
    }

    // Touch handlers
    touchHandler.on('touchstart', handleTouchStart);
    touchHandler.on('touchmove', handleTouchMove);
    touchHandler.on('touchend', handleTouchEnd);

    return () => {
      touchHandler.destroy();
      if (longPressTimer.current) {
        clearTimeout(longPressTimer.current);
      }
    };
  }, [swipeEnabled, hapticEnabled, dragEnabled, onSwipeLeft, onSwipeRight, onSwipeUp, onSwipeDown]);

  const handleTouchStart = ({ touchData }: any) => {
    setIsPressed(true);
    
    if (hapticEnabled) {
      hapticFeedback.tap();
    }
    
    if (scaleOnPress) {
      setTransform(prev => ({ ...prev, scale: 0.98 }));
    }
    
    // Long press detection
    if (onLongPress) {
      longPressTimer.current = setTimeout(() => {
        if (hapticEnabled) hapticFeedback.longPress();
        onLongPress();
      }, 500);
    }
  };

  const handleTouchMove = ({ touchData }: any) => {
    if (!dragEnabled) return;
    
    setIsDragging(true);
    
    // Update transform based on drag
    const maxDrag = 100;
    const dragX = Math.max(-maxDrag, Math.min(maxDrag, touchData.deltaX));
    const dragY = Math.max(-maxDrag, Math.min(maxDrag, touchData.deltaY));
    
    setTransform(prev => ({
      ...prev,
      x: dragX,
      y: dragY
    }));
  };

  const handleTouchEnd = ({ touchData }: any) => {
    setIsPressed(false);
    setIsDragging(false);
    
    if (longPressTimer.current) {
      clearTimeout(longPressTimer.current);
      longPressTimer.current = null;
    }
    
    // Reset scale
    if (scaleOnPress) {
      setTransform(prev => ({ ...prev, scale: 1 }));
    }
    
    // Snap back if dragging
    if (dragEnabled && snapBack) {
      setTransform(prev => ({ ...prev, x: 0, y: 0 }));
    }
    
    // Handle tap
    if (onTap && !isDragging && touchData.deltaX < 10 && touchData.deltaY < 10) {
      onTap();
    }
  };

  const animateDismiss = (direction: 'left' | 'right' | 'up' | 'down') => {
    if (!cardRef.current) return;
    
    const card = cardRef.current;
    const rect = card.getBoundingClientRect();
    
    let translateX = 0;
    let translateY = 0;
    
    switch (direction) {
      case 'left':
        translateX = -rect.width - 100;
        break;
      case 'right':
        translateX = rect.width + 100;
        break;
      case 'up':
        translateY = -rect.height - 100;
        break;
      case 'down':
        translateY = rect.height + 100;
        break;
    }
    
    card.style.transition = 'transform 0.3s ease-out, opacity 0.3s ease-out';
    card.style.transform = `translate(${translateX}px, ${translateY}px) scale(0.8)`;
    card.style.opacity = '0';
    
    setTimeout(() => {
      onDismiss?.();
    }, 300);
  };

  const cardStyle = {
    transform: `translate(${transform.x}px, ${transform.y}px) scale(${transform.scale})`,
    transition: isDragging ? 'none' : 'transform 0.2s ease-out'
  };

  return (
    <div
      ref={cardRef}
      className={cn(
        // Base styles
        'relative bg-white rounded-lg border border-gray-200',
        'transition-all duration-200 ease-out',
        'touch-manipulation select-none',
        
        // Interactive states
        swipeEnabled && 'cursor-grab active:cursor-grabbing',
        
        // Elevation on press
        elevationOnPress && isPressed && [
          'shadow-lg shadow-black/10',
          'border-gray-300'
        ],
        
        // Default elevation
        !isPressed && 'shadow-sm hover:shadow-md',
        
        // Drag state
        isDragging && [
          'z-50',
          'shadow-xl shadow-black/20'
        ],
        
        className
      )}
      style={cardStyle}
    >
      {/* Swipe indicators */}
      {swipeEnabled && (
        <>
          {dismissible && (
            <>
              {/* Left swipe indicator */}
              <div className={cn(
                'absolute top-1/2 -left-8 transform -translate-y-1/2',
                'w-6 h-6 bg-red-500 rounded-full flex items-center justify-center',
                'text-white text-sm opacity-0 transition-opacity',
                transform.x < -20 && 'opacity-100'
              )}>
                ×
              </div>
              
              {/* Right swipe indicator */}
              <div className={cn(
                'absolute top-1/2 -right-8 transform -translate-y-1/2',
                'w-6 h-6 bg-green-500 rounded-full flex items-center justify-center',
                'text-white text-sm opacity-0 transition-opacity',
                transform.x > 20 && 'opacity-100'
              )}>
                ✓
              </div>
            </>
          )}
          
          {/* Drag affordance */}
          {dragEnabled && (
            <div className="absolute top-2 left-1/2 transform -translate-x-1/2">
              <div className="w-8 h-1 bg-gray-300 rounded-full" />
            </div>
          )}
        </>
      )}
      
      {/* Content */}
      <div className={cn(
        'relative z-10',
        dragEnabled && 'pt-4'
      )}>
        {children}
      </div>
      
      {/* Gesture hints */}
      {swipeEnabled && !isDragging && (
        <div className="absolute inset-0 pointer-events-none">
          {/* Swipe left hint */}
          {onSwipeLeft && (
            <div className={cn(
              'absolute left-2 top-1/2 transform -translate-y-1/2',
              'text-gray-400 text-xs opacity-0 transition-opacity',
              'group-hover:opacity-100'
            )}>
              ←
            </div>
          )}
          
          {/* Swipe right hint */}
          {onSwipeRight && (
            <div className={cn(
              'absolute right-2 top-1/2 transform -translate-y-1/2',
              'text-gray-400 text-xs opacity-0 transition-opacity',
              'group-hover:opacity-100'
            )}>
              →
            </div>
          )}
        </div>
      )}
      
      {/* Touch feedback overlay */}
      {isPressed && (
        <div className="absolute inset-0 bg-black/5 rounded-lg pointer-events-none" />
      )}
    </div>
  );
};

export { GestureCard };
export type { GestureCardProps };