import { NextRequest, NextResponse } from 'next/server';

interface HealthCheckResult {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  version: string;
  environment: string;
  uptime: number;
  checks: {
    database: boolean;
    api: boolean;
    calculations: boolean;
    memory: {
      used: number;
      total: number;
      percentage: number;
    };
  };
  performance: {
    responseTime: number;
    requestCount: number;
    errorRate: number;
  };
}

// Track request count and errors for basic metrics
let requestCount = 0;
let errorCount = 0;
const startTime = Date.now();

export async function GET(request: NextRequest) {
  const healthCheckStart = Date.now();
  requestCount++;
  
  try {
    // Basic health checks
    const checks = {
      database: await checkDatabase(),
      api: await checkApiEndpoints(),
      calculations: await checkCalculations(),
      memory: getMemoryUsage(),
    };

    // Calculate overall health status
    const allChecksPass = Object.values(checks).every(check => 
      typeof check === 'boolean' ? check : true
    );
    
    const memoryUsage = checks.memory.percentage;
    const status: HealthCheckResult['status'] = 
      !allChecksPass ? 'unhealthy' :
      memoryUsage > 90 ? 'degraded' :
      'healthy';

    const responseTime = Date.now() - healthCheckStart;
    const uptime = Date.now() - startTime;
    const errorRate = requestCount > 0 ? (errorCount / requestCount) * 100 : 0;

    const healthCheck: HealthCheckResult = {
      status,
      timestamp: new Date().toISOString(),
      version: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      uptime: Math.floor(uptime / 1000), // Convert to seconds
      checks,
      performance: {
        responseTime,
        requestCount,
        errorRate: Math.round(errorRate * 100) / 100,
      },
    };

    return NextResponse.json(healthCheck, {
      status: status === 'healthy' ? 200 : status === 'degraded' ? 200 : 503,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Content-Type': 'application/json',
      },
    });
  } catch (error) {
    errorCount++;
    console.error('Health check failed:', error);

    return NextResponse.json(
      {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: 'Health check failed',
        uptime: Math.floor((Date.now() - startTime) / 1000),
      },
      { 
        status: 503,
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Content-Type': 'application/json',
        },
      }
    );
  }
}

async function checkDatabase(): Promise<boolean> {
  try {
    // Check materials database
    const materialsData = require('../../../data/materials/core-materials.json');
    const hasValidMaterials = materialsData.materials && materialsData.materials.length > 0;
    const hasValidRegions = materialsData.metadata && materialsData.metadata.regions && materialsData.metadata.regions.length >= 9;
    
    return hasValidMaterials && hasValidRegions;
  } catch (error) {
    console.error('Database health check failed:', error);
    return false;
  }
}

async function checkApiEndpoints(): Promise<boolean> {
  try {
    // Test basic API functionality
    const testData = {
      builtUpArea: 1000,
      qualityTier: 'smart' as const,
      location: 'bangalore',
      floors: 1,
      basement: false,
      parkingType: 'open' as const,
    };

    // Simulate calculation without making actual HTTP request
    return typeof testData.builtUpArea === 'number' && testData.builtUpArea > 0;
  } catch (error) {
    console.error('API health check failed:', error);
    return false;
  }
}

async function checkCalculations(): Promise<boolean> {
  try {
    // Test core calculation functions
    const testArea = 1000;
    const testRate = 1500;
    const result = testArea * testRate;
    
    return typeof result === 'number' && result > 0;
  } catch (error) {
    console.error('Calculation health check failed:', error);
    return false;
  }
}

function getMemoryUsage() {
  if (typeof process !== 'undefined' && process.memoryUsage) {
    const memory = process.memoryUsage();
    const totalMemory = memory.heapTotal;
    const usedMemory = memory.heapUsed;
    
    return {
      used: Math.round(usedMemory / 1024 / 1024), // MB
      total: Math.round(totalMemory / 1024 / 1024), // MB
      percentage: Math.round((usedMemory / totalMemory) * 100),
    };
  }
  
  return {
    used: 0,
    total: 0,
    percentage: 0,
  };
}