import { calculateStructureCost, calculateFoundationCost, calculateConcreteCost, calculateSteelCost } from '../structure';
import type { CalculationInput } from '../../types';

describe('Structure Cost Calculations', () => {
  describe('calculateStructureCost', () => {
    it('should calculate correct structure cost for smart tier', () => {
      const input: CalculationInput = {
        builtUpArea: 1000,
        floors: 1, // G+1 = 1 additional floor
        qualityTier: 'smart',
        location: 'bangalore',
        hasBasement: false,
      };
      
      const result = calculateStructureCost(input);
      
      // Expected: builtUpArea * (floors + 1) * baseRate * floorMultiplier
      // 1000 * (1+1) * 600 * 1.05 = 2000 * 600 * 1.05 = 1,260,000
      expect(result.amount).toBe(1260000);
      expect(result.subCategories).toBeDefined();
      expect(result.subCategories).toHaveLength(5);
    });

    it('should calculate correct structure cost for premium tier', () => {
      const input: CalculationInput = {
        builtUpArea: 1000,
        floors: 1,
        qualityTier: 'premium',
        location: 'bangalore',
        hasBasement: false,
      };
      
      const result = calculateStructureCost(input);
      
      // Expected: 1000 * (1+1) * 750 * 1.05 = 1,575,000
      expect(result.amount).toBe(1575000);
      expect(result.subCategories).toBeDefined();
    });

    it('should calculate correct structure cost for luxury tier', () => {
      const input: CalculationInput = {
        builtUpArea: 1000,
        floors: 1,
        qualityTier: 'luxury',
        location: 'bangalore',
        hasBasement: false,
      };
      
      const result = calculateStructureCost(input);
      
      // Expected: 1000 * (1+1) * 900 * 1.05 = 1,890,000
      expect(result.amount).toBe(1890000);
      expect(result.subCategories).toBeDefined();
    });

    it('should apply floor multipliers correctly', () => {
      const baseInput: CalculationInput = {
        builtUpArea: 1000,
        floors: 1,
        qualityTier: 'smart',
        location: 'bangalore',
        hasBasement: false,
      };

      const ground = calculateStructureCost({ ...baseInput, floors: 0 });
      const g1 = calculateStructureCost({ ...baseInput, floors: 1 });
      const g2 = calculateStructureCost({ ...baseInput, floors: 2 });
      const g3 = calculateStructureCost({ ...baseInput, floors: 3 });
      const g4 = calculateStructureCost({ ...baseInput, floors: 4 });
      
      // G: 1000 * 1 * 600 * 1.0 = 600,000
      expect(ground.amount).toBe(600000);
      // G+1: 1000 * 2 * 600 * 1.05 = 1,260,000
      expect(g1.amount).toBe(1260000);
      // G+2: 1000 * 3 * 600 * 1.10 = 1,980,000
      expect(g2.amount).toBe(1980000);
      // G+3: 1000 * 4 * 600 * 1.15 = 2,760,000
      expect(g3.amount).toBe(2760000);
      // G+4: 1000 * 5 * 600 * 1.20 = 3,600,000
      expect(g4.amount).toBe(3600000);
    });

    it('should add basement cost correctly', () => {
      const input: CalculationInput = {
        builtUpArea: 1000,
        floors: 1,
        qualityTier: 'smart',
        location: 'bangalore',
        hasBasement: false,
      };

      const withoutBasement = calculateStructureCost(input);
      const withBasement = calculateStructureCost({ ...input, hasBasement: true });
      
      // With basement: (1000 * 2 + 1000 * 1.3) * 600 * 1.05 = 3300 * 600 * 1.05 = 2,079,000
      // Without basement: 1000 * 2 * 600 * 1.05 = 1,260,000
      // Difference: 819,000 (basement adds 1000 * 1.3 * 600 * 1.05)
      expect(withBasement.amount).toBe(2079000);
      expect(withBasement.amount - withoutBasement.amount).toBe(819000);
    });

    it('should calculate breakdown percentages correctly', () => {
      const input: CalculationInput = {
        builtUpArea: 1000,
        floors: 1,
        qualityTier: 'smart',
        location: 'bangalore',
        hasBasement: false,
      };
      
      const result = calculateStructureCost(input);
      
      // Check that all subcategories exist
      const subCatNames = result.subCategories.map(sub => sub.name);
      expect(subCatNames).toContain('Foundation');
      expect(subCatNames).toContain('Columns & Beams');
      expect(subCatNames).toContain('Slabs');
      expect(subCatNames).toContain('Walls');
      expect(subCatNames).toContain('Staircase');
      
      // Check that amounts sum up correctly
      const totalSubCategories = result.subCategories.reduce((sum, sub) => sum + sub.amount, 0);
      expect(totalSubCategories).toBe(result.amount);
    });

    it('should have proper descriptions for each component', () => {
      const input: CalculationInput = {
        builtUpArea: 1000,
        floors: 1,
        qualityTier: 'smart',
        location: 'bangalore',
        hasBasement: false,
      };
      
      const result = calculateStructureCost(input);
      
      const foundation = result.subCategories.find(sub => sub.name === 'Foundation');
      const columns = result.subCategories.find(sub => sub.name === 'Columns & Beams');
      const slabs = result.subCategories.find(sub => sub.name === 'Slabs');
      const walls = result.subCategories.find(sub => sub.name === 'Walls');
      const staircase = result.subCategories.find(sub => sub.name === 'Staircase');
      
      expect(foundation?.description).toContain('IS 456');
      expect(columns?.description).toContain('IS 13920');
      expect(slabs?.description).toContain('IS 456');
      expect(walls?.description).toContain('IS 1905');
      expect(staircase?.description).toContain('RCC');
    });

    it('should handle edge cases', () => {
      // Minimum area
      const minInput: CalculationInput = {
        builtUpArea: 100,
        floors: 0,
        qualityTier: 'smart',
        location: 'bangalore',
        hasBasement: false,
      };
      
      const minResult = calculateStructureCost(minInput);
      expect(minResult.amount).toBe(60000); // 100 * 1 * 600 * 1.0
      
      // Large area
      const largeInput: CalculationInput = {
        builtUpArea: 10000,
        floors: 4,
        qualityTier: 'luxury',
        location: 'bangalore',
        hasBasement: true,
      };
      
      const largeResult = calculateStructureCost(largeInput);
      // (10000 * 5 + 10000 * 1.3) * 900 * 1.20 = 63000 * 900 * 1.20 = 68,040,000
      expect(largeResult.amount).toBe(68040000);
    });

    it('should round costs to nearest integer', () => {
      const input: CalculationInput = {
        builtUpArea: 1234,
        floors: 1,
        qualityTier: 'smart',
        location: 'bangalore',
        hasBasement: false,
      };
      
      const result = calculateStructureCost(input);
      
      expect(Number.isInteger(result.amount)).toBe(true);
      result.subCategories.forEach(sub => {
        expect(Number.isInteger(sub.amount)).toBe(true);
      });
    });
  });

  describe('Quality Tier Variations', () => {
    const baseInput: CalculationInput = {
      builtUpArea: 1000,
      floors: 1,
      qualityTier: 'smart',
      location: 'bangalore',
      hasBasement: false,
    };

    it('should have increasing costs across quality tiers', () => {
      const smartCost = calculateStructureCost({ ...baseInput, qualityTier: 'smart' });
      const premiumCost = calculateStructureCost({ ...baseInput, qualityTier: 'premium' });
      const luxuryCost = calculateStructureCost({ ...baseInput, qualityTier: 'luxury' });
      
      expect(smartCost.amount).toBeLessThan(premiumCost.amount);
      expect(premiumCost.amount).toBeLessThan(luxuryCost.amount);
      
      // Check ratios (750/600 = 1.25, 900/600 = 1.5)
      expect(premiumCost.amount / smartCost.amount).toBeCloseTo(1.25, 2);
      expect(luxuryCost.amount / smartCost.amount).toBeCloseTo(1.5, 2);
    });
  });

  describe('calculateFoundationCost', () => {
    it('should calculate foundation cost based on soil type', () => {
      const area = 1000;
      const floors = 1;
      
      const goodSoil = calculateFoundationCost(area, floors, 'good');
      const mediumSoil = calculateFoundationCost(area, floors, 'medium');
      const poorSoil = calculateFoundationCost(area, floors, 'poor');
      
      // Base cost: 1000 * 180 * (1 + 1 * 0.1) = 1000 * 180 * 1.1 = 198,000
      expect(goodSoil).toBe(198000); // * 1.0
      expect(mediumSoil).toBe(237600); // * 1.2
      expect(poorSoil).toBe(297000); // * 1.5
    });

    it('should increase cost with more floors', () => {
      const area = 1000;
      const soilType = 'medium';
      
      const oneFloor = calculateFoundationCost(area, 1, soilType);
      const twoFloors = calculateFoundationCost(area, 2, soilType);
      const fourFloors = calculateFoundationCost(area, 4, soilType);
      
      expect(twoFloors).toBeGreaterThan(oneFloor);
      expect(fourFloors).toBeGreaterThan(twoFloors);
    });
  });

  describe('calculateConcreteCost', () => {
    it('should calculate concrete volume and cost correctly', () => {
      const area = 1000;
      const floors = 1;
      
      const smart = calculateConcreteCost(area, floors, 'smart');
      const premium = calculateConcreteCost(area, floors, 'premium');
      const luxury = calculateConcreteCost(area, floors, 'luxury');
      
      // Smart: totalArea = 1000 * 2 = 2000, volume = 2000 * 0.125 = 250 cum
      expect(smart.volume).toBe(250);
      expect(smart.cost).toBe(1125000); // 250 * 4500
      
      // Premium: volume = 2000 * 0.135 = 270 cum
      expect(premium.volume).toBe(270);
      expect(premium.cost).toBe(1404000); // 270 * 5200
      
      // Luxury: volume = 2000 * 0.145 = 290 cum
      expect(luxury.volume).toBe(290);
      expect(luxury.cost).toBe(1740000); // 290 * 6000
    });
  });

  describe('calculateSteelCost', () => {
    it('should calculate steel weight and cost correctly', () => {
      const area = 1000;
      const floors = 1;
      
      const smart = calculateSteelCost(area, floors, 'smart');
      const premium = calculateSteelCost(area, floors, 'premium');
      const luxury = calculateSteelCost(area, floors, 'luxury');
      
      // Smart: totalArea = 1000 * 2 = 2000, weight = 2000 * 3.8 = 7600 kg
      expect(smart.weight).toBe(7600);
      expect(smart.cost).toBe(494000); // 7600 * 65
      
      // Premium: weight = 2000 * 4.0 = 8000 kg
      expect(premium.weight).toBe(8000);
      expect(premium.cost).toBe(560000); // 8000 * 70
      
      // Luxury: weight = 2000 * 4.5 = 9000 kg
      expect(luxury.weight).toBe(9000);
      expect(luxury.cost).toBe(675000); // 9000 * 75
    });
  });
});