#!/usr/bin/env node

/**
 * Comprehensive API Integration Testing Suite
 * Nirmaan AI Construction Calculator
 * 
 * Tests all endpoints with various scenarios including:
 * - Valid and invalid inputs
 * - Request/response format validation
 * - Error handling and status codes
 * - Rate limiting and authentication
 * - Database integration
 * - Performance benchmarking
 * - Edge cases and stress testing
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

class ComprehensiveAPITester {
    constructor(baseURL = 'http://localhost:3001/api') {
        this.baseURL = baseURL;
        this.results = {
            passed: 0,
            failed: 0,
            total: 0,
            tests: [],
            performance: {
                averageResponseTime: 0,
                maxResponseTime: 0,
                minResponseTime: Infinity,
                responseTimes: []
            },
            errors: [],
            rateLimitTests: [],
            securityTests: []
        };
        
        this.client = axios.create({
            baseURL: this.baseURL,
            timeout: 30000,
            validateStatus: () => true, // Don't throw on any status code
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'Comprehensive-API-Tester/1.0'
            }
        });
    }

    // Utility method to record test results
    recordTest(name, passed, response, error = null, details = {}) {
        const responseTime = response?.config?.metadata?.endTime - response?.config?.metadata?.startTime || 0;
        
        this.results.total++;
        if (passed) {
            this.results.passed++;
        } else {
            this.results.failed++;
            this.results.errors.push({
                test: name,
                error: error?.message || 'Test failed',
                response: response?.data,
                status: response?.status,
                details
            });
        }

        if (responseTime) {
            this.results.performance.responseTimes.push(responseTime);
            this.results.performance.maxResponseTime = Math.max(this.results.performance.maxResponseTime, responseTime);
            this.results.performance.minResponseTime = Math.min(this.results.performance.minResponseTime, responseTime);
        }

        this.results.tests.push({
            name,
            passed,
            responseTime,
            status: response?.status,
            error: error?.message,
            timestamp: new Date().toISOString(),
            details
        });

        const status = passed ? '✅' : '❌';
        const timing = responseTime ? ` (${responseTime}ms)` : '';
        console.log(`${status} ${name}${timing}`);
        
        if (error && !passed) {
            console.log(`   Error: ${error.message}`);
        }
    }

    // Add request timing interceptor
    setupInterceptors() {
        this.client.interceptors.request.use(request => {
            request.metadata = { startTime: Date.now() };
            return request;
        });

        this.client.interceptors.response.use(
            response => {
                response.config.metadata.endTime = Date.now();
                return response;
            },
            error => {
                if (error.config) {
                    error.config.metadata.endTime = Date.now();
                }
                return Promise.reject(error);
            }
        );
    }

    // Test 1: Health Check Endpoint
    async testHealthCheck() {
        try {
            const response = await this.client.get('/health');
            const passed = response.status === 200 && 
                          response.data.status === 'healthy' &&
                          response.data.timestamp &&
                          response.data.version &&
                          response.data.checks;
            
            this.recordTest('Health Check Endpoint', passed, response, null, {
                expectedStatus: 200,
                requiredFields: ['status', 'timestamp', 'version', 'checks']
            });
        } catch (error) {
            this.recordTest('Health Check Endpoint', false, null, error);
        }
    }

    // Test 2: API Documentation Endpoint
    async testAPIDocumentation() {
        try {
            const response = await this.client.get('/calculate');
            const passed = response.status === 200 && 
                          response.data.name &&
                          response.data.version &&
                          response.data.endpoints;
            
            this.recordTest('API Documentation', passed, response, null, {
                expectedStatus: 200,
                requiredFields: ['name', 'version', 'endpoints']
            });
        } catch (error) {
            this.recordTest('API Documentation', false, null, error);
        }
    }

    // Test 3: Basic Cost Calculation - Valid Inputs
    async testBasicCalculation() {
        const testCases = [
            {
                name: 'Smart Quality - Bangalore',
                data: {
                    builtUpArea: 1000,
                    floors: 1,
                    qualityTier: 'smart',
                    location: 'bangalore'
                }
            },
            {
                name: 'Premium Quality - Mumbai',
                data: {
                    builtUpArea: 1500,
                    floors: 2,
                    qualityTier: 'premium',
                    location: 'mumbai'
                }
            },
            {
                name: 'Luxury Quality - Delhi',
                data: {
                    builtUpArea: 2000,
                    floors: 3,
                    qualityTier: 'luxury',
                    location: 'delhi'
                }
            }
        ];

        for (const testCase of testCases) {
            try {
                const response = await this.client.post('/calculate', testCase.data);
                const passed = response.status === 200 &&
                              response.data.success &&
                              response.data.data.totalCost &&
                              response.data.data.costPerSqft &&
                              response.data.data.breakdown;
                
                this.recordTest(`Basic Calculation - ${testCase.name}`, passed, response, null, {
                    input: testCase.data,
                    expectedFields: ['totalCost', 'costPerSqft', 'breakdown']
                });
            } catch (error) {
                this.recordTest(`Basic Calculation - ${testCase.name}`, false, null, error);
            }
        }
    }

    // Test 4: Advanced Calculation with Special Features
    async testAdvancedCalculation() {
        const testData = {
            builtUpArea: 2500,
            floors: 3,
            qualityTier: 'luxury',
            location: 'mumbai',
            plotArea: 3000,
            hasStilt: true,
            parkingType: 'covered',
            hasBasement: true,
            specialFeatures: [
                {
                    name: 'Swimming Pool',
                    cost: 800000,
                    description: 'Olympic size swimming pool'
                },
                {
                    name: 'Home Theater',
                    cost: 500000,
                    description: '7.1 surround sound system'
                }
            ]
        };

        try {
            const response = await this.client.post('/calculate', testData);
            const passed = response.status === 200 &&
                          response.data.success &&
                          response.data.data.totalCost &&
                          response.data.data.materials &&
                          response.data.data.timeline;
            
            this.recordTest('Advanced Calculation with Features', passed, response, null, {
                input: testData,
                expectedFields: ['totalCost', 'materials', 'timeline', 'specialFeatures']
            });
        } catch (error) {
            this.recordTest('Advanced Calculation with Features', false, null, error);
        }
    }

    // Test 5: Input Validation Tests
    async testInputValidation() {
        const invalidInputs = [
            {
                name: 'Missing Required Fields',
                data: { builtUpArea: 1000 }
            },
            {
                name: 'Invalid Data Types',
                data: {
                    builtUpArea: 'not a number',
                    floors: 'not a number',
                    qualityTier: 'premium',
                    location: 'bangalore'
                }
            },
            {
                name: 'Invalid Quality Tier',
                data: {
                    builtUpArea: 1000,
                    floors: 1,
                    qualityTier: 'invalid_tier',
                    location: 'bangalore'
                }
            },
            {
                name: 'Invalid Location',
                data: {
                    builtUpArea: 1000,
                    floors: 1,
                    qualityTier: 'premium',
                    location: 'invalid_location'
                }
            },
            {
                name: 'Negative Values',
                data: {
                    builtUpArea: -1000,
                    floors: -1,
                    qualityTier: 'premium',
                    location: 'bangalore'
                }
            },
            {
                name: 'Zero Built-up Area',
                data: {
                    builtUpArea: 0,
                    floors: 1,
                    qualityTier: 'premium',
                    location: 'bangalore'
                }
            }
        ];

        for (const testCase of invalidInputs) {
            try {
                const response = await this.client.post('/calculate', testCase.data);
                const passed = response.status === 400 && 
                              response.data.success === false &&
                              response.data.error;
                
                this.recordTest(`Input Validation - ${testCase.name}`, passed, response, null, {
                    input: testCase.data,
                    expectedStatus: 400,
                    shouldFail: true
                });
            } catch (error) {
                this.recordTest(`Input Validation - ${testCase.name}`, false, null, error);
            }
        }
    }

    // Test 6: Edge Cases
    async testEdgeCases() {
        const edgeCases = [
            {
                name: 'Minimum Values',
                data: {
                    builtUpArea: 1,
                    floors: 0,
                    qualityTier: 'smart',
                    location: 'bangalore'
                }
            },
            {
                name: 'Maximum Realistic Values',
                data: {
                    builtUpArea: 50000,
                    floors: 10,
                    qualityTier: 'luxury',
                    location: 'mumbai'
                }
            },
            {
                name: 'Single Floor Ground Only',
                data: {
                    builtUpArea: 1200,
                    floors: 0,
                    qualityTier: 'premium',
                    location: 'bangalore'
                }
            }
        ];

        for (const testCase of edgeCases) {
            try {
                const response = await this.client.post('/calculate', testCase.data);
                const passed = response.status === 200 && response.data.success;
                
                this.recordTest(`Edge Case - ${testCase.name}`, passed, response, null, {
                    input: testCase.data,
                    description: 'Testing boundary conditions'
                });
            } catch (error) {
                this.recordTest(`Edge Case - ${testCase.name}`, false, null, error);
            }
        }
    }

    // Test 7: Rate Limiting
    async testRateLimiting() {
        console.log('Testing rate limiting (this may take a moment)...');
        
        const testData = {
            builtUpArea: 1000,
            floors: 1,
            qualityTier: 'smart',
            location: 'bangalore'
        };

        let rateLimitTriggered = false;
        let successCount = 0;
        let requestCount = 0;
        
        // Make rapid requests to trigger rate limiting
        for (let i = 0; i < 20; i++) {
            try {
                const response = await this.client.post('/calculate', testData);
                requestCount++;
                
                if (response.status === 200) {
                    successCount++;
                } else if (response.status === 429) {
                    rateLimitTriggered = true;
                    this.results.rateLimitTests.push({
                        triggeredAt: i + 1,
                        successBefore: successCount,
                        headers: response.headers
                    });
                    break;
                }
                
                // Small delay to avoid overwhelming
                await new Promise(resolve => setTimeout(resolve, 50));
            } catch (error) {
                // Handle any errors
                break;
            }
        }

        this.recordTest('Rate Limiting', rateLimitTriggered, null, null, {
            requestCount,
            successCount,
            rateLimitTriggered,
            description: 'Rate limiting should activate after multiple requests'
        });
    }

    // Test 8: Monitoring Endpoints
    async testMonitoringEndpoints() {
        try {
            const response = await this.client.get('/monitoring');
            const passed = response.status === 200 && 
                          response.data.status &&
                          response.data.metrics;
            
            this.recordTest('Monitoring Endpoint', passed, response, null, {
                expectedFields: ['status', 'metrics', 'timestamp']
            });
        } catch (error) {
            this.recordTest('Monitoring Endpoint', false, null, error);
        }

        // Test detailed monitoring
        try {
            const response = await this.client.get('/monitoring?detail=detailed');
            const passed = response.status === 200 && 
                          response.data.metrics &&
                          response.data.endpoints;
            
            this.recordTest('Detailed Monitoring', passed, response, null, {
                queryParam: 'detail=detailed',
                expectedFields: ['metrics', 'endpoints']
            });
        } catch (error) {
            this.recordTest('Detailed Monitoring', false, null, error);
        }
    }

    // Test 9: Performance Metrics Endpoints
    async testPerformanceMetrics() {
        // Test POST performance metrics
        const metricsData = {
            metric: {
                name: 'LCP',
                value: 1234,
                rating: 'good',
                url: 'http://localhost:3001/calculator',
                timestamp: Date.now(),
                userAgent: 'Test-Agent/1.0',
                deviceType: 'desktop'
            }
        };

        try {
            const response = await this.client.post('/performance/metrics', metricsData);
            const passed = response.status === 200 && response.data.status;
            
            this.recordTest('Submit Performance Metrics', passed, response, null, {
                input: metricsData,
                endpoint: 'POST /performance/metrics'
            });
        } catch (error) {
            this.recordTest('Submit Performance Metrics', false, null, error);
        }

        // Test GET performance metrics
        try {
            const response = await this.client.get('/performance/metrics?metric=LCP&limit=10');
            const passed = response.status === 200;
            
            this.recordTest('Retrieve Performance Metrics', passed, response, null, {
                queryParams: 'metric=LCP&limit=10',
                endpoint: 'GET /performance/metrics'
            });
        } catch (error) {
            this.recordTest('Retrieve Performance Metrics', false, null, error);
        }
    }

    // Test 10: Analytics Endpoints
    async testAnalyticsEndpoints() {
        // Test Web Vitals submission
        const webVitalsData = {
            metrics: [
                {
                    metric: 'LCP',
                    value: 1500,
                    rating: 'good',
                    url: 'http://localhost:3001/calculator',
                    timestamp: Date.now(),
                    deviceType: 'desktop',
                    sessionId: 'test-session-123'
                },
                {
                    metric: 'FID',
                    value: 45,
                    rating: 'good',
                    url: 'http://localhost:3001/calculator',
                    timestamp: Date.now(),
                    deviceType: 'desktop',
                    sessionId: 'test-session-123'
                }
            ]
        };

        try {
            const response = await this.client.post('/analytics/web-vitals', webVitalsData);
            const passed = response.status === 200 && response.data.status;
            
            this.recordTest('Submit Web Vitals', passed, response, null, {
                input: webVitalsData,
                endpoint: 'POST /analytics/web-vitals'
            });
        } catch (error) {
            this.recordTest('Submit Web Vitals', false, null, error);
        }

        // Test Web Vitals retrieval
        try {
            const response = await this.client.get('/analytics/web-vitals?timeRange=24h&aggregated=true');
            const passed = response.status === 200;
            
            this.recordTest('Retrieve Web Vitals', passed, response, null, {
                queryParams: 'timeRange=24h&aggregated=true',
                endpoint: 'GET /analytics/web-vitals'
            });
        } catch (error) {
            this.recordTest('Retrieve Web Vitals', false, null, error);
        }
    }

    // Test 11: Support Endpoints
    async testSupportEndpoints() {
        // Test robots.txt
        try {
            const response = await this.client.get('/robots');
            const passed = response.status === 200 && 
                          response.headers['content-type'].includes('text/plain');
            
            this.recordTest('Robots.txt Endpoint', passed, response, null, {
                expectedContentType: 'text/plain'
            });
        } catch (error) {
            this.recordTest('Robots.txt Endpoint', false, null, error);
        }

        // Test sitemap
        try {
            const response = await this.client.get('/sitemap');
            const passed = response.status === 200 && 
                          response.headers['content-type'].includes('xml');
            
            this.recordTest('Sitemap Endpoint', passed, response, null, {
                expectedContentType: 'application/xml'
            });
        } catch (error) {
            this.recordTest('Sitemap Endpoint', false, null, error);
        }
    }

    // Test 12: Invalid JSON Handling
    async testInvalidJSONHandling() {
        try {
            // Create a request with invalid JSON
            const response = await axios.post(`${this.baseURL}/calculate`, 'invalid json', {
                headers: { 'Content-Type': 'application/json' },
                validateStatus: () => true
            });
            
            const passed = response.status === 400 && 
                          response.data.success === false &&
                          response.data.error;
            
            this.recordTest('Invalid JSON Handling', passed, response, null, {
                input: 'invalid json',
                expectedStatus: 400,
                description: 'Should handle malformed JSON gracefully'
            });
        } catch (error) {
            this.recordTest('Invalid JSON Handling', false, null, error);
        }
    }

    // Test 13: Response Format Validation
    async testResponseFormat() {
        try {
            const response = await this.client.post('/calculate', {
                builtUpArea: 1000,
                floors: 1,
                qualityTier: 'smart',
                location: 'bangalore'
            });

            if (response.status === 200 && response.data.success) {
                const data = response.data.data;
                const requiredFields = [
                    'totalCost', 'costPerSqft', 'breakdown', 'materials', 
                    'timeline', 'summary'
                ];
                
                const hasAllFields = requiredFields.every(field => 
                    data.hasOwnProperty(field)
                );
                
                const breakdownValid = data.breakdown && 
                    data.breakdown.structure && 
                    data.breakdown.finishing &&
                    data.breakdown.mep &&
                    data.breakdown.external &&
                    data.breakdown.other;

                const passed = hasAllFields && breakdownValid;
                
                this.recordTest('Response Format Validation', passed, response, null, {
                    requiredFields,
                    foundFields: Object.keys(data),
                    breakdownStructure: Object.keys(data.breakdown || {})
                });
            } else {
                this.recordTest('Response Format Validation', false, response, 
                    new Error('Calculation request failed'));
            }
        } catch (error) {
            this.recordTest('Response Format Validation', false, null, error);
        }
    }

    // Test 14: Concurrent Requests
    async testConcurrentRequests() {
        console.log('Testing concurrent requests...');
        
        const testData = {
            builtUpArea: 1000,
            floors: 1,
            qualityTier: 'smart',
            location: 'bangalore'
        };

        const concurrentRequests = 10;
        const promises = [];

        for (let i = 0; i < concurrentRequests; i++) {
            promises.push(this.client.post('/calculate', testData));
        }

        try {
            const responses = await Promise.allSettled(promises);
            const successful = responses.filter(r => 
                r.status === 'fulfilled' && r.value.status === 200
            ).length;
            
            const passed = successful >= concurrentRequests * 0.8; // 80% success rate
            
            this.recordTest('Concurrent Requests', passed, null, null, {
                totalRequests: concurrentRequests,
                successfulRequests: successful,
                successRate: `${(successful / concurrentRequests * 100).toFixed(1)}%`
            });
        } catch (error) {
            this.recordTest('Concurrent Requests', false, null, error);
        }
    }

    // Test 15: Large Payload Handling
    async testLargePayload() {
        const largePayload = {
            builtUpArea: 2000,
            floors: 2,
            qualityTier: 'luxury',
            location: 'mumbai',
            specialFeatures: Array(50).fill().map((_, i) => ({
                name: `Feature ${i + 1}`,
                cost: 10000 * (i + 1),
                description: `This is a test feature number ${i + 1} with a longer description to test payload handling. `.repeat(10)
            }))
        };

        try {
            const response = await this.client.post('/calculate', largePayload);
            const passed = response.status === 200 && response.data.success;
            
            this.recordTest('Large Payload Handling', passed, response, null, {
                payloadSize: JSON.stringify(largePayload).length,
                specialFeaturesCount: largePayload.specialFeatures.length
            });
        } catch (error) {
            this.recordTest('Large Payload Handling', false, null, error);
        }
    }

    // Calculate performance statistics
    calculatePerformanceStats() {
        const times = this.results.performance.responseTimes;
        if (times.length > 0) {
            this.results.performance.averageResponseTime = 
                times.reduce((sum, time) => sum + time, 0) / times.length;
        }
    }

    // Generate comprehensive report
    generateReport() {
        this.calculatePerformanceStats();
        
        const report = {
            summary: {
                totalTests: this.results.total,
                passed: this.results.passed,
                failed: this.results.failed,
                successRate: `${((this.results.passed / this.results.total) * 100).toFixed(1)}%`,
                timestamp: new Date().toISOString(),
                testDuration: Date.now() - this.startTime
            },
            performance: {
                averageResponseTime: `${this.results.performance.averageResponseTime.toFixed(2)}ms`,
                maxResponseTime: `${this.results.performance.maxResponseTime}ms`,
                minResponseTime: this.results.performance.minResponseTime === Infinity ? 
                    '0ms' : `${this.results.performance.minResponseTime}ms`,
                totalRequests: this.results.performance.responseTimes.length
            },
            rateLimiting: {
                tested: this.results.rateLimitTests.length > 0,
                results: this.results.rateLimitTests
            },
            errors: this.results.errors,
            allTests: this.results.tests,
            recommendations: this.generateRecommendations()
        };

        return report;
    }

    // Generate recommendations based on test results
    generateRecommendations() {
        const recommendations = [];
        
        if (this.results.failed > 0) {
            recommendations.push('Review failed tests and fix identified issues');
        }
        
        if (this.results.performance.averageResponseTime > 1000) {
            recommendations.push('Consider optimizing API response times (average > 1000ms)');
        }
        
        if (this.results.rateLimitTests.length === 0) {
            recommendations.push('Rate limiting was not triggered - verify rate limit configuration');
        }
        
        if (this.results.errors.length > 0) {
            recommendations.push('Address error scenarios to improve API robustness');
        }
        
        recommendations.push('Implement comprehensive monitoring and alerting');
        recommendations.push('Consider adding API versioning for future compatibility');
        recommendations.push('Set up automated testing in CI/CD pipeline');
        
        return recommendations;
    }

    // Save report to file
    saveReport(report) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `api-integration-test-report-${timestamp}.json`;
        const filepath = path.join(process.cwd(), filename);
        
        fs.writeFileSync(filepath, JSON.stringify(report, null, 2));
        console.log(`\n📊 Detailed report saved to: ${filepath}`);
        
        return filepath;
    }

    // Print summary to console
    printSummary(report) {
        console.log('\n' + '='.repeat(60));
        console.log('🏆 COMPREHENSIVE API INTEGRATION TEST RESULTS');
        console.log('='.repeat(60));
        console.log(`📊 Total Tests: ${report.summary.totalTests}`);
        console.log(`✅ Passed: ${report.summary.passed}`);
        console.log(`❌ Failed: ${report.summary.failed}`);
        console.log(`📈 Success Rate: ${report.summary.successRate}`);
        console.log(`⏱️  Test Duration: ${(report.summary.testDuration / 1000).toFixed(1)}s`);
        console.log('\n📊 PERFORMANCE METRICS:');
        console.log(`⚡ Average Response Time: ${report.performance.averageResponseTime}`);
        console.log(`🚀 Fastest Response: ${report.performance.minResponseTime}`);
        console.log(`🐌 Slowest Response: ${report.performance.maxResponseTime}`);
        console.log(`📊 Total Requests: ${report.performance.totalRequests}`);
        
        if (report.errors.length > 0) {
            console.log('\n❌ CRITICAL ISSUES:');
            report.errors.forEach((error, index) => {
                console.log(`${index + 1}. ${error.test}: ${error.error}`);
            });
        }
        
        if (report.recommendations.length > 0) {
            console.log('\n💡 RECOMMENDATIONS:');
            report.recommendations.forEach((rec, index) => {
                console.log(`${index + 1}. ${rec}`);
            });
        }
        
        console.log('\n' + '='.repeat(60));
    }

    // Main test execution
    async runAllTests() {
        this.startTime = Date.now();
        this.setupInterceptors();
        
        console.log('🚀 Starting Comprehensive API Integration Testing...');
        console.log(`🎯 Target: ${this.baseURL}`);
        console.log('='.repeat(60));

        // Wait for server to be ready
        console.log('⏳ Waiting for server to be ready...');
        await new Promise(resolve => setTimeout(resolve, 3000));

        // Execute all test suites
        await this.testHealthCheck();
        await this.testAPIDocumentation();
        await this.testBasicCalculation();
        await this.testAdvancedCalculation();
        await this.testInputValidation();
        await this.testEdgeCases();
        await this.testRateLimiting();
        await this.testMonitoringEndpoints();
        await this.testPerformanceMetrics();
        await this.testAnalyticsEndpoints();
        await this.testSupportEndpoints();
        await this.testInvalidJSONHandling();
        await this.testResponseFormat();
        await this.testConcurrentRequests();
        await this.testLargePayload();

        // Generate and save report
        const report = this.generateReport();
        const reportPath = this.saveReport(report);
        this.printSummary(report);
        
        return report;
    }
}

// Run the tests if this file is executed directly
if (require.main === module) {
    const tester = new ComprehensiveAPITester();
    tester.runAllTests()
        .then(report => {
            process.exit(report.summary.failed > 0 ? 1 : 0);
        })
        .catch(error => {
            console.error('❌ Test suite failed:', error);
            process.exit(1);
        });
}

module.exports = ComprehensiveAPITester;