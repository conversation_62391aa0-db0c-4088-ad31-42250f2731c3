/**
 * Smart Defaults Hook
 * Manages intelligent form defaults and suggestions
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import { CalculatorFormData } from '@/components/calculator/types/wizard';
import { 
  SmartDefaultsEngine, 
  SmartDefault, 
  DefaultsContext 
} from '@/lib/recommendations/smart-defaults';

export interface UseSmartDefaultsOptions {
  context?: DefaultsContext;
  enabled?: boolean;
  autoApply?: boolean; // Automatically apply high-confidence defaults
  confidenceThreshold?: number; // Minimum confidence to show suggestions
}

export interface SmartDefaultsSuggestion extends SmartDefault {
  isApplied: boolean;
  canApply: boolean;
}

export interface UseSmartDefaultsReturn {
  suggestions: SmartDefaultsSuggestion[];
  applySuggestion: (field: keyof CalculatorFormData, value: string | boolean) => void;
  dismissSuggestion: (field: keyof CalculatorFormData) => void;
  applyAllHighConfidence: () => void;
  clearAllSuggestions: () => void;
  updateContext: (newContext: Partial<DefaultsContext>) => void;
  isLoading: boolean;
  stats: {
    total: number;
    applied: number;
    highConfidence: number;
    dismissed: number;
  };
}

export function useSmartDefaults(
  formData: Partial<CalculatorFormData>,
  updateFormData: (updates: Partial<CalculatorFormData>) => void,
  options: UseSmartDefaultsOptions = {}
): UseSmartDefaultsReturn {
  const {
    context = {},
    enabled = true,
    autoApply = false,
    confidenceThreshold = 0.6
  } = options;

  const [engine] = useState(() => new SmartDefaultsEngine(context));
  const [appliedSuggestions, setAppliedSuggestions] = useState<Set<string>>(new Set());
  const [dismissedSuggestions, setDismissedSuggestions] = useState<Set<string>>(new Set());
  const [isLoading, setIsLoading] = useState(false);

  // Update engine context when context changes
  useEffect(() => {
    engine.updateContext(context);
  }, [engine, context]);

  // Determine current step based on form data
  const currentStep = useMemo(() => {
    if (!formData.plotSize || !formData.location) return 'basic';
    if (!formData.bedrooms || !formData.bathrooms) return 'rooms';
    if (!formData.quality) return 'quality';
    return 'features';
  }, [formData]);

  // Generate suggestions based on current form data
  const rawSuggestions = useMemo(() => {
    if (!enabled) return [];
    
    setIsLoading(true);
    const suggestions = engine.generateDefaults(formData, currentStep);
    setIsLoading(false);
    
    return suggestions.filter(s => s.confidence >= confidenceThreshold);
  }, [formData, currentStep, enabled, confidenceThreshold, engine]);

  // Process suggestions with application status
  const suggestions = useMemo((): SmartDefaultsSuggestion[] => {
    return rawSuggestions.map(suggestion => {
      const fieldKey = suggestion.field.toString();
      const currentValue = formData[suggestion.field];
      const isApplied = appliedSuggestions.has(fieldKey);
      const isDismissed = dismissedSuggestions.has(fieldKey);
      
      // Can apply if not dismissed and current value is empty/default
      const canApply = !isDismissed && (
        currentValue === undefined || 
        currentValue === '' || 
        currentValue === false ||
        (suggestion.confidence > 0.9 && suggestion.userVisible)
      );

      return {
        ...suggestion,
        isApplied,
        canApply: canApply && !isApplied
      };
    }).filter(s => s.canApply || s.isApplied); // Only show applicable or already applied suggestions
  }, [rawSuggestions, formData, appliedSuggestions, dismissedSuggestions]);

  // Auto-apply high confidence suggestions
  useEffect(() => {
    if (!autoApply) return;

    const autoApplicable = suggestions.filter(s => 
      s.confidence > 0.85 && 
      s.canApply && 
      !s.isApplied &&
      s.userVisible === false // Only auto-apply background defaults
    );

    if (autoApplicable.length > 0) {
      const updates: Partial<CalculatorFormData> = {};
      const newApplied = new Set(appliedSuggestions);

      autoApplicable.forEach(suggestion => {
        updates[suggestion.field] = suggestion.value;
        newApplied.add(suggestion.field.toString());
      });

      updateFormData(updates);
      setAppliedSuggestions(newApplied);
    }
  }, [suggestions, autoApply, appliedSuggestions, updateFormData]);

  // Apply a specific suggestion
  const applySuggestion = useCallback((field: keyof CalculatorFormData, value: string | boolean) => {
    updateFormData({ [field]: value });
    setAppliedSuggestions(prev => new Set([...prev, field.toString()]));
  }, [updateFormData]);

  // Dismiss a suggestion
  const dismissSuggestion = useCallback((field: keyof CalculatorFormData) => {
    setDismissedSuggestions(prev => new Set([...prev, field.toString()]));
  }, []);

  // Apply all high confidence suggestions
  const applyAllHighConfidence = useCallback(() => {
    const highConfidenceSuggestions = suggestions.filter(s => 
      s.confidence > 0.8 && s.canApply && s.userVisible
    );

    if (highConfidenceSuggestions.length === 0) return;

    const updates: Partial<CalculatorFormData> = {};
    const newApplied = new Set(appliedSuggestions);

    highConfidenceSuggestions.forEach(suggestion => {
      updates[suggestion.field] = suggestion.value;
      newApplied.add(suggestion.field.toString());
    });

    updateFormData(updates);
    setAppliedSuggestions(newApplied);
  }, [suggestions, appliedSuggestions, updateFormData]);

  // Clear all suggestions (dismiss all)
  const clearAllSuggestions = useCallback(() => {
    const allFields = suggestions.map(s => s.field.toString());
    setDismissedSuggestions(prev => new Set([...prev, ...allFields]));
  }, [suggestions]);

  // Update context
  const updateContext = useCallback((newContext: Partial<DefaultsContext>) => {
    engine.updateContext(newContext);
  }, [engine]);

  // Calculate stats
  const stats = useMemo(() => {
    const total = rawSuggestions.length;
    const applied = appliedSuggestions.size;
    const highConfidence = rawSuggestions.filter(s => s.confidence > 0.8).length;
    const dismissed = dismissedSuggestions.size;

    return { total, applied, highConfidence, dismissed };
  }, [rawSuggestions, appliedSuggestions, dismissedSuggestions]);

  return {
    suggestions,
    applySuggestion,
    dismissSuggestion,
    applyAllHighConfidence,
    clearAllSuggestions,
    updateContext,
    isLoading,
    stats
  };
}

// Helper hook for specific field suggestions
export function useFieldSuggestion(
  field: keyof CalculatorFormData,
  formData: Partial<CalculatorFormData>,
  options: UseSmartDefaultsOptions = {}
): SmartDefaultsSuggestion | null {
  const { suggestions } = useSmartDefaults(formData, () => {}, options);
  
  return suggestions.find(s => s.field === field) || null;
}

// Hook for budget estimation
export function useBudgetEstimate(
  formData: Partial<CalculatorFormData>,
  context: DefaultsContext = {}
): {
  estimate: number | null;
  confidence: number;
  breakdown: {
    base: number;
    features: number;
    regional: number;
  } | null;
} {
  const [engine] = useState(() => new SmartDefaultsEngine(context));

  return useMemo(() => {
    const estimate = engine.getBudgetEstimate(formData);
    
    if (!estimate || !formData.builtUpArea) {
      return { estimate: null, confidence: 0, breakdown: null };
    }

    const area = parseFloat(formData.builtUpArea);
    const quality = formData.quality || 'smart';
    const qualityCosts = { smart: 1800, premium: 2500, luxury: 3500 };
    const base = area * qualityCosts[quality as keyof typeof qualityCosts];

    // Calculate feature costs
    let features = 0;
    if (formData.swimmingPool) features += 1150000;
    if (formData.elevator) features += 1600000;
    if (formData.homeAutomation) features += 400000;
    if (formData.solarPanels) features += 550000;
    if (formData.garden) features += 350000;

    // Regional multiplier
    const locationMultipliers: Record<string, number> = {
      mumbai: 1.2, delhi: 1.05, bangalore: 1.0, chennai: 0.95,
      hyderabad: 0.9, pune: 1.0, kolkata: 0.85, ahmedabad: 0.8
    };
    const multiplier = locationMultipliers[formData.location || 'delhi'] || 1.0;
    const regional = base * (multiplier - 1);

    // Confidence based on data completeness
    let confidence = 0.5;
    if (formData.plotSize) confidence += 0.1;
    if (formData.bedrooms && formData.bathrooms) confidence += 0.2;
    if (formData.quality) confidence += 0.2;

    return {
      estimate,
      confidence: Math.min(confidence, 1.0),
      breakdown: { base, features, regional }
    };
  }, [formData, engine]);
}