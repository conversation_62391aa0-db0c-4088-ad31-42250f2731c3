'use client';

import { useEffect } from 'react';
import <PERSON>rip<PERSON> from 'next/script';
import { usePathname, useSearchParams } from 'next/navigation';
import { analyticsConfig, isAnalyticsEnabled, isPerformanceMonitoringEnabled } from '@/lib/env';
import { webVitalsMonitor } from '@/lib/performance/web-vitals';

// Google Analytics 4
declare global {
  interface Window {
    gtag: (...args: any[]) => void;
    dataLayer: any[];
  }
}

// PostHog Analytics
declare global {
  interface Window {
    posthog?: any;
  }
}

export function Analytics() {
  const pathname = usePathname();
  const searchParams = useSearchParams();

  // Track page views
  useEffect(() => {
    if (!isAnalyticsEnabled) return;

    const url = pathname + (searchParams?.toString() ? `?${searchParams.toString()}` : '');

    // Google Analytics page view
    if (analyticsConfig.gaId && window.gtag) {
      window.gtag('config', analyticsConfig.gaId, {
        page_location: url,
        page_title: document.title,
      });
    }

    // PostHog page view
    if (analyticsConfig.posthogKey && window.posthog) {
      window.posthog.capture('$pageview', {
        $current_url: url,
        $pathname: pathname,
        $search: searchParams?.toString(),
      });
    }
  }, [pathname, searchParams]);

  if (!isAnalyticsEnabled) {
    return null;
  }

  return (
    <>
      {/* Google Analytics 4 */}
      {analyticsConfig.gaId && (
        <>
          <Script
            src={`https://www.googletagmanager.com/gtag/js?id=${analyticsConfig.gaId}`}
            strategy="afterInteractive"
          />
          <Script id="google-analytics" strategy="afterInteractive">
            {`
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', '${analyticsConfig.gaId}', {
                page_location: window.location.href,
                page_title: document.title,
                custom_map: {'custom_parameter_1': 'construction_calculator'},
                send_page_view: true
              });
            `}
          </Script>
        </>
      )}

      {/* PostHog Analytics */}
      {analyticsConfig.posthogKey && analyticsConfig.posthogHost && (
        <Script id="posthog-analytics" strategy="afterInteractive">
          {`
            !function(t,e){var o,n,p,r;e.__SV||(window.posthog=e,e._i=[],e.init=function(i,s,a){function g(t,e){var o=e.split(".");2==o.length&&(t=t[o[0]],e=o[1]);var n=t;if("undefined"!=typeof n){var p=n[e];if("function"==typeof p)return function(){n[e].apply(n,arguments)};if("object"==typeof p)return p}return e}(p=t.createElement("script")).type="text/javascript",p.async=!0,p.src=s.api_host+"/static/array.js",(r=t.getElementsByTagName("script")[0]).parentNode.insertBefore(p,r);var u=e;for(void 0!==a?u=e[a]=[]:a="posthog",u.people=u.people||[],u.toString=function(t){var e="posthog";return"posthog"!==a&&(e+="."+a),t||(e+=" (stub)"),e},u.people.toString=function(){return u.toString(1)+".people (stub)"},o="capture identify alias people.set people.set_once set_config register register_once unregister opt_out_capturing has_opted_out_capturing opt_in_capturing reset isFeatureEnabled onFeatureFlags".split(" "),n=0;n<o.length;n++)g(u,o[n]);e._i.push([i,s,a])},e.__SV=1)}(document,window.posthog||[]);
            posthog.init('${analyticsConfig.posthogKey}', {
              api_host: '${analyticsConfig.posthogHost}',
              autocapture: true,
              capture_pageview: true,
              capture_pageleave: true,
              session_recording: {
                enabled: ${isPerformanceMonitoringEnabled},
              },
              person_profiles: 'identified_only'
            });
          `}
        </Script>
      )}

      {/* Performance Monitoring */}
      {isPerformanceMonitoringEnabled && <PerformanceMonitor />}
      
      {/* Performance Dashboard Script */}
      {isPerformanceMonitoringEnabled && (
        <Script id="performance-dashboard" strategy="afterInteractive">
          {`
            // Enable performance dashboard in development
            if (typeof window !== 'undefined' && window.location.hostname === 'localhost') {
              console.log('Performance monitoring enabled. Access dashboard at /performance-dashboard');
            }
          `}
        </Script>
      )}
    </>
  );
}

// Performance monitoring component
function PerformanceMonitor() {
  useEffect(() => {
    // Initialize comprehensive Web Vitals monitoring
    webVitalsMonitor.onReport((report) => {
      // Send to Google Analytics
      if (window.gtag) {
        // Send overall performance score
        window.gtag('event', 'performance_score', {
          event_category: 'Web Vitals',
          value: Math.round(report.performanceScore),
          custom_parameter_1: report.route,
          custom_parameter_2: report.deviceInfo?.type || 'unknown',
        });

        // Send individual metrics
        const metrics = ['cls', 'fid', 'fcp', 'lcp', 'ttfb', 'inp'];
        metrics.forEach(metricName => {
          const metric = report[metricName as keyof typeof report];
          if (metric && typeof metric === 'object' && 'value' in metric) {
            window.gtag('event', metricName.toUpperCase(), {
              event_category: 'Web Vitals',
              value: Math.round(metric.value),
              event_label: metric.rating,
              custom_parameter_1: metric.id,
              custom_parameter_2: report.route,
            });
          }
        });
      }

      // Send to PostHog
      if (window.posthog) {
        window.posthog.capture('performance_report', {
          performance_score: report.performanceScore,
          load_time: report.loadTime,
          resource_count: report.resourceCount,
          cache_hit_rate: report.cacheHitRate,
          device_type: report.deviceInfo?.type,
          network_type: report.networkInfo?.effectiveType,
          route: report.route,
          alerts_count: report.alerts.length,
          memory_usage: report.memoryUsage?.usedJSHeapSize,
        });
      }
    });

    // Handle performance alerts
    webVitalsMonitor.onAlert((alert) => {
      // Send alert to analytics
      if (window.gtag) {
        window.gtag('event', 'performance_alert', {
          event_category: 'Performance',
          event_label: alert.metric,
          value: Math.round(alert.actualValue),
          custom_parameter_1: alert.severity,
          custom_parameter_2: alert.message,
        });
      }

      if (window.posthog) {
        window.posthog.capture('performance_alert', {
          metric: alert.metric,
          severity: alert.severity,
          message: alert.message,
          threshold: alert.threshold,
          actual_value: alert.actualValue,
          timestamp: alert.timestamp,
        });
      }

      // Log critical alerts
      if (alert.severity === 'critical') {
        console.error('[Performance Alert]', alert);
      }
    });

    // Start real-time monitoring
    webVitalsMonitor.startRealTimeMonitoring();

    // Custom performance monitoring for navigation timing
    const performanceObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.entryType === 'navigation') {
          const navigationEntry = entry as PerformanceNavigationTiming;
          
          // Track detailed navigation metrics
          const metrics = {
            dns_lookup: navigationEntry.domainLookupEnd - navigationEntry.domainLookupStart,
            tcp_connect: navigationEntry.connectEnd - navigationEntry.connectStart,
            ssl_handshake: navigationEntry.requestStart - navigationEntry.secureConnectionStart,
            ttfb: navigationEntry.responseStart - navigationEntry.requestStart,
            download: navigationEntry.responseEnd - navigationEntry.responseStart,
            dom_interactive: navigationEntry.domInteractive - navigationEntry.fetchStart,
            dom_complete: navigationEntry.domComplete - navigationEntry.fetchStart,
            load_event: navigationEntry.loadEventEnd - navigationEntry.loadEventStart,
            redirect_time: navigationEntry.redirectEnd - navigationEntry.redirectStart,
            unload_time: navigationEntry.unloadEventEnd - navigationEntry.unloadEventStart,
          };

          // Send to analytics
          if (window.posthog) {
            window.posthog.capture('navigation_timing', metrics);
          }

          // Send to Google Analytics as custom events
          if (window.gtag) {
            Object.entries(metrics).forEach(([key, value]) => {
              if (value > 0) {
                window.gtag('event', 'timing_' + key, {
                  event_category: 'Navigation Timing',
                  value: Math.round(value),
                  non_interaction: true,
                });
              }
            });
          }
        }
      }
    });

    performanceObserver.observe({ entryTypes: ['navigation'] });

    return () => {
      performanceObserver.disconnect();
    };
  }, []);

  return null;
}

// Utility functions for tracking events
export const trackEvent = (eventName: string, properties?: Record<string, any>) => {
  if (!isAnalyticsEnabled) return;

  // Add performance context to events
  const enhancedProperties = {
    ...properties,
    performance_score: webVitalsMonitor.getPerformanceScore(),
    current_lcp: webVitalsMonitor.getMetric('LCP')?.value,
    current_cls: webVitalsMonitor.getMetric('CLS')?.value,
    current_fid: webVitalsMonitor.getMetric('FID')?.value,
    device_type: webVitalsMonitor.getDeviceInfo()?.type,
    network_type: webVitalsMonitor.getNetworkInfo()?.effectiveType,
  };

  // Google Analytics
  if (window.gtag) {
    window.gtag('event', eventName, {
      event_category: 'User Interaction',
      ...enhancedProperties,
    });
  }

  // PostHog
  if (window.posthog) {
    window.posthog.capture(eventName, enhancedProperties);
  }
};

// New utility functions for performance tracking
export const trackPagePerformance = (pageName: string) => {
  if (!isAnalyticsEnabled) return;

  const report = webVitalsMonitor.generateReport();
  
  trackEvent('page_performance_measured', {
    page_name: pageName,
    performance_score: report.performanceScore,
    load_time: report.loadTime,
    resource_count: report.resourceCount,
    cache_hit_rate: report.cacheHitRate,
    memory_usage: report.memoryUsage?.usedJSHeapSize,
    alerts_count: report.alerts.length,
  });
};

export const trackUserInteraction = (interactionType: string, details?: Record<string, any>) => {
  if (!isAnalyticsEnabled) return;

  const interactionStart = performance.now();
  
  // Return a function to call when interaction completes
  return () => {
    const interactionTime = performance.now() - interactionStart;
    
    trackEvent('user_interaction', {
      interaction_type: interactionType,
      interaction_time: Math.round(interactionTime),
      ...details,
    });

    // Track slow interactions
    if (interactionTime > 1000) {
      trackEvent('slow_interaction', {
        interaction_type: interactionType,
        interaction_time: Math.round(interactionTime),
        severity: interactionTime > 3000 ? 'critical' : 'warning',
        ...details,
      });
    }
  };
}

export const trackAsyncOperation = async function <T>(
  operation: () => Promise<T>,
  operationName: string,
  metadata?: Record<string, any>
): Promise<T> {
  const startTime = performance.now();
  
  try {
    const result = await operation();
    const duration = performance.now() - startTime;
    
    trackEvent('async_operation_success', {
      operation_name: operationName,
      duration: Math.round(duration),
      ...metadata,
    });
    
    return result;
  } catch (error) {
    const duration = performance.now() - startTime;
    
    trackEvent('async_operation_error', {
      operation_name: operationName,
      duration: Math.round(duration),
      error_message: error instanceof Error ? error.message : 'Unknown error',
      ...metadata,
    });
    
    throw error;
  }
};

export const trackCalculation = (data: {
  qualityTier: string;
  location: string;
  totalArea: number;
  totalCost: number;
  duration: number;
}) => {
  trackEvent('calculation_completed', {
    quality_tier: data.qualityTier,
    location: data.location,
    total_area: data.totalArea,
    total_cost: data.totalCost,
    calculation_duration_ms: data.duration,
    cost_per_sqft: Math.round(data.totalCost / data.totalArea),
  });

  // Track calculation performance
  if (typeof window !== 'undefined' && window.fetch) {
    fetch('/api/performance/custom-metrics', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        name: 'calculation-time',
        value: data.duration,
        unit: 'ms',
        timestamp: Date.now(),
        url: window.location.href,
        route: window.location.pathname,
        userAgent: navigator.userAgent,
        metadata: {
          qualityTier: data.qualityTier,
          location: data.location,
          totalArea: data.totalArea,
          totalCost: data.totalCost,
        }
      })
    }).catch(() => {
      // Silently fail
    });
  }
};

export const trackError = (error: Error, context?: Record<string, any>) => {
  if (!isAnalyticsEnabled) return;

  const errorData = {
    error_message: error.message,
    error_stack: error.stack,
    error_name: error.name,
    performance_score: webVitalsMonitor.getPerformanceScore(),
    memory_usage: typeof window !== 'undefined' && 'memory' in performance ? 
      (performance as any).memory?.usedJSHeapSize : null,
    ...context,
  };

  // Google Analytics
  if (window.gtag) {
    window.gtag('event', 'exception', {
      description: error.message,
      fatal: false,
      ...errorData,
    });
  }

  // PostHog
  if (window.posthog) {
    window.posthog.capture('error_occurred', errorData);
  }

  // Send to performance monitoring
  if (typeof window !== 'undefined' && window.fetch) {
    fetch('/api/performance/custom-metrics', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        name: 'error-occurred',
        value: 1,
        unit: 'count',
        timestamp: Date.now(),
        url: window.location.href,
        route: window.location.pathname,
        userAgent: navigator.userAgent,
        metadata: errorData
      })
    }).catch(() => {
      // Silently fail
    });
  }
};