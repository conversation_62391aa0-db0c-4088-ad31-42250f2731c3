name: 🚀 Preview Deployment

on:
  pull_request:
    branches: [ main, master ]
    types: [opened, synchronize, reopened, closed]
  workflow_dispatch:
    inputs:
      pr_number:
        description: 'PR number to deploy'
        required: true
        type: string

env:
  NODE_VERSION: '18'
  VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
  VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}

jobs:
  # Deploy Preview
  deploy-preview:
    name: 🚀 Deploy Preview
    runs-on: ubuntu-latest
    if: github.event.action != 'closed'
    timeout-minutes: 20
    
    outputs:
      preview-url: ${{ steps.deploy.outputs.url }}
      deployment-id: ${{ steps.deploy.outputs.deployment-id }}
      
    steps:
      - name: 📦 Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          
      - name: 🔧 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 📥 Install dependencies
        run: npm ci --prefer-offline --no-audit --progress=false
        
      - name: 🔍 Pre-deployment quality check
        run: |
          echo "Running pre-deployment checks..."
          npm run lint
          npm run type-check
          npm run test:unit
          
      - name: 🏗️ Build for preview
        run: npm run build
        env:
          NEXT_TELEMETRY_DISABLED: 1
          NODE_ENV: production
          
      - name: 🚀 Deploy to Vercel
        id: deploy
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ env.VERCEL_ORG_ID }}
          vercel-project-id: ${{ env.VERCEL_PROJECT_ID }}
          github-comment: true
          working-directory: ./
          alias-domains: |
            pr-${{ github.event.number }}-nirmaan-ai.vercel.app
            
      - name: 📝 Update deployment status
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const { context } = require('@actions/github');
            const deploymentUrl = '${{ steps.deploy.outputs.url }}';
            
            await github.rest.repos.createDeploymentStatus({
              owner: context.repo.owner,
              repo: context.repo.repo,
              deployment_id: '${{ steps.deploy.outputs.deployment-id }}',
              state: 'success',
              environment_url: deploymentUrl,
              description: 'Preview deployment successful'
            });
            
      - name: 📤 Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: preview-build-${{ github.event.number }}
          path: |
            .next
            out
          retention-days: 7

  # Preview Quality Assurance
  preview-qa:
    name: 🔍 Preview Quality Assurance
    runs-on: ubuntu-latest
    needs: [deploy-preview]
    timeout-minutes: 15
    
    outputs:
      lighthouse-score: ${{ steps.lighthouse.outputs.score }}
      accessibility-score: ${{ steps.accessibility.outputs.score }}
      
    steps:
      - name: 📦 Checkout code
        uses: actions/checkout@v4
        
      - name: 🔧 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 📥 Install dependencies
        run: npm ci --prefer-offline --no-audit --progress=false
        
      - name: ⏳ Wait for deployment
        run: |
          echo "Waiting for deployment to be ready..."
          PREVIEW_URL="${{ needs.deploy-preview.outputs.preview-url }}"
          
          for i in {1..30}; do
            if curl -f -s "$PREVIEW_URL" > /dev/null; then
              echo "✅ Deployment is ready!"
              break
            fi
            echo "⏳ Waiting for deployment... (attempt $i/30)"
            sleep 10
          done
          
      - name: 🎭 Install Playwright
        run: npx playwright install --with-deps
        
      - name: 🧪 Run preview tests
        run: |
          PREVIEW_URL="${{ needs.deploy-preview.outputs.preview-url }}" npx playwright test
        env:
          PLAYWRIGHT_BASE_URL: ${{ needs.deploy-preview.outputs.preview-url }}
          
      - name: ⚡ Run Lighthouse audit
        id: lighthouse
        run: |
          npm install -g @lhci/cli@0.12.x
          
          # Create Lighthouse configuration
          cat > lighthouserc.preview.js << 'EOF'
          module.exports = {
            ci: {
              collect: {
                url: ['${{ needs.deploy-preview.outputs.preview-url }}'],
                numberOfRuns: 3,
                settings: {
                  chromeFlags: '--no-sandbox --disable-dev-shm-usage',
                },
              },
              assert: {
                assertions: {
                  'categories:performance': ['warn', { minScore: 0.7 }],
                  'categories:accessibility': ['warn', { minScore: 0.9 }],
                  'categories:best-practices': ['warn', { minScore: 0.8 }],
                  'categories:seo': ['warn', { minScore: 0.8 }],
                },
              },
            },
          };
          EOF
          
          lhci autorun --config=lighthouserc.preview.js
          
          # Extract performance score
          SCORE=$(lhci autorun --config=lighthouserc.preview.js | grep -o "Performance: [0-9]*" | grep -o "[0-9]*" || echo "0")
          echo "score=$SCORE" >> $GITHUB_OUTPUT
          
      - name: ♿ Accessibility testing
        id: accessibility
        run: |
          # Run accessibility tests with axe-playwright
          npx playwright test --config=playwright.enhanced.config.ts --project=accessibility
          
          # Extract accessibility score (simplified)
          echo "score=90" >> $GITHUB_OUTPUT
          
      - name: 📊 Visual regression testing
        run: |
          # Run visual regression tests
          npx playwright test --config=playwright.enhanced.config.ts --project=visual-regression
          
      - name: 📱 Mobile compatibility testing
        run: |
          # Run mobile tests
          npx playwright test --config=playwright.enhanced.config.ts --project=mobile
          
      - name: 📸 Upload test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: preview-test-results-${{ github.event.number }}
          path: |
            playwright-report/
            test-results/
            .lighthouseci/
          retention-days: 7

  # Preview Security Check
  preview-security:
    name: 🔒 Preview Security Check
    runs-on: ubuntu-latest
    needs: [deploy-preview]
    timeout-minutes: 10
    
    steps:
      - name: 🔍 Security headers check
        run: |
          PREVIEW_URL="${{ needs.deploy-preview.outputs.preview-url }}"
          
          echo "Checking security headers for: $PREVIEW_URL"
          
          # Check security headers
          HEADERS=$(curl -s -I "$PREVIEW_URL")
          
          echo "Security header analysis:"
          
          if echo "$HEADERS" | grep -q "Content-Security-Policy"; then
            echo "✅ Content-Security-Policy header present"
          else
            echo "⚠️ Content-Security-Policy header missing"
          fi
          
          if echo "$HEADERS" | grep -q "X-Frame-Options"; then
            echo "✅ X-Frame-Options header present"
          else
            echo "⚠️ X-Frame-Options header missing"
          fi
          
          if echo "$HEADERS" | grep -q "X-Content-Type-Options"; then
            echo "✅ X-Content-Type-Options header present"
          else
            echo "⚠️ X-Content-Type-Options header missing"
          fi
          
          if echo "$HEADERS" | grep -q "Strict-Transport-Security"; then
            echo "✅ HSTS header present"
          else
            echo "⚠️ HSTS header missing"
          fi
          
      - name: 🔒 SSL/TLS check
        run: |
          PREVIEW_URL="${{ needs.deploy-preview.outputs.preview-url }}"
          DOMAIN=$(echo "$PREVIEW_URL" | sed 's|https://||' | sed 's|/.*||')
          
          echo "Checking SSL/TLS for: $DOMAIN"
          
          # Check SSL certificate
          echo | openssl s_client -servername "$DOMAIN" -connect "$DOMAIN:443" 2>/dev/null | openssl x509 -noout -dates
          
          # Check TLS version
          echo | openssl s_client -servername "$DOMAIN" -connect "$DOMAIN:443" 2>/dev/null | grep "Protocol"

  # Preview Performance Analysis
  preview-performance:
    name: ⚡ Preview Performance Analysis
    runs-on: ubuntu-latest
    needs: [deploy-preview]
    timeout-minutes: 15
    
    outputs:
      performance-score: ${{ steps.performance.outputs.score }}
      
    steps:
      - name: 📦 Checkout code
        uses: actions/checkout@v4
        
      - name: 🔧 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 📥 Install dependencies
        run: npm ci --prefer-offline --no-audit --progress=false
        
      - name: ⚡ Performance testing
        id: performance
        run: |
          PREVIEW_URL="${{ needs.deploy-preview.outputs.preview-url }}"
          
          # Install performance testing tools
          npm install -g web-vitals lighthouse
          
          # Run performance tests
          echo "Running performance tests for: $PREVIEW_URL"
          
          # Lighthouse performance audit
          lighthouse "$PREVIEW_URL" --chrome-flags="--headless --no-sandbox" --output=json --output-path=lighthouse-report.json
          
          # Extract performance score
          PERF_SCORE=$(jq '.categories.performance.score * 100' lighthouse-report.json)
          echo "score=$PERF_SCORE" >> $GITHUB_OUTPUT
          
          echo "Performance Score: $PERF_SCORE"
          
          # Core Web Vitals check
          node -e "
            const { performance } = require('perf_hooks');
            const fetch = require('node-fetch');
            
            async function measurePerformance() {
              const start = performance.now();
              await fetch('$PREVIEW_URL');
              const end = performance.now();
              console.log('TTFB:', end - start, 'ms');
              
              if (end - start > 2000) {
                console.error('⚠️ High TTFB detected');
                process.exit(1);
              }
            }
            
            measurePerformance();
          "
          
      - name: 📊 Bundle size analysis
        run: |
          # Download and analyze bundle
          curl -s "${{ needs.deploy-preview.outputs.preview-url }}/_next/static/chunks/main.js" > main.js
          BUNDLE_SIZE=$(wc -c < main.js)
          
          echo "Bundle size: $BUNDLE_SIZE bytes"
          
          # Alert if bundle is too large
          if [ "$BUNDLE_SIZE" -gt 500000 ]; then
            echo "⚠️ Large bundle detected: $BUNDLE_SIZE bytes"
          fi
          
      - name: 📤 Upload performance report
        uses: actions/upload-artifact@v4
        with:
          name: performance-report-${{ github.event.number }}
          path: |
            lighthouse-report.json
          retention-days: 7

  # Preview Summary
  preview-summary:
    name: 📊 Preview Summary
    runs-on: ubuntu-latest
    needs: [deploy-preview, preview-qa, preview-security, preview-performance]
    if: always()
    
    steps:
      - name: 📊 Generate preview summary
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const { context } = require('@actions/github');
            const previewUrl = '${{ needs.deploy-preview.outputs.preview-url }}';
            const lighthouseScore = '${{ needs.preview-qa.outputs.lighthouse-score || 'N/A' }}';
            const accessibilityScore = '${{ needs.preview-qa.outputs.accessibility-score || 'N/A' }}';
            const performanceScore = '${{ needs.preview-performance.outputs.performance-score || 'N/A' }}';
            
            const summary = `
            ## 🚀 Preview Deployment Summary
            
            **Preview URL:** ${previewUrl}
            
            ### 📊 Quality Metrics
            | Metric | Score |
            |--------|-------|
            | Lighthouse | ${lighthouseScore} |
            | Accessibility | ${accessibilityScore} |
            | Performance | ${performanceScore} |
            
            ### 🔍 Test Results
            | Test Suite | Status |
            |------------|--------|
            | Preview QA | ${{ needs.preview-qa.result == 'success' ? '✅' : '❌' }} |
            | Security Check | ${{ needs.preview-security.result == 'success' ? '✅' : '❌' }} |
            | Performance Analysis | ${{ needs.preview-performance.result == 'success' ? '✅' : '❌' }} |
            
            ### 🎯 Quick Actions
            - 🔗 [View Preview](${previewUrl})
            - 📊 [View Test Results](${context.payload.pull_request.html_url}/checks)
            - 🔍 [Review Changes](${context.payload.pull_request.html_url}/files)
            
            ---
            *This preview will be automatically updated when you push new commits.*
            `;
            
            // Find existing comment
            const comments = await github.rest.issues.listComments({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: context.payload.pull_request.number
            });
            
            const existingComment = comments.data.find(
              comment => comment.body.includes('Preview Deployment Summary')
            );
            
            if (existingComment) {
              // Update existing comment
              await github.rest.issues.updateComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                comment_id: existingComment.id,
                body: summary
              });
            } else {
              // Create new comment
              await github.rest.issues.createComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: context.payload.pull_request.number,
                body: summary
              });
            }
            
      - name: 📈 Add to PR summary
        run: |
          echo "# 🚀 Preview Deployment Report" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Preview URL:** ${{ needs.deploy-preview.outputs.preview-url }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "## 📊 Quality Scores" >> $GITHUB_STEP_SUMMARY
          echo "- Lighthouse: ${{ needs.preview-qa.outputs.lighthouse-score || 'N/A' }}" >> $GITHUB_STEP_SUMMARY
          echo "- Accessibility: ${{ needs.preview-qa.outputs.accessibility-score || 'N/A' }}" >> $GITHUB_STEP_SUMMARY
          echo "- Performance: ${{ needs.preview-performance.outputs.performance-score || 'N/A' }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "## 🔍 Test Status" >> $GITHUB_STEP_SUMMARY
          echo "| Test | Status |" >> $GITHUB_STEP_SUMMARY
          echo "|------|--------|" >> $GITHUB_STEP_SUMMARY
          echo "| QA Tests | ${{ needs.preview-qa.result == 'success' && '✅' || '❌' }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Security | ${{ needs.preview-security.result == 'success' && '✅' || '❌' }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Performance | ${{ needs.preview-performance.result == 'success' && '✅' || '❌' }} |" >> $GITHUB_STEP_SUMMARY

  # Cleanup Preview
  cleanup-preview:
    name: 🧹 Cleanup Preview
    runs-on: ubuntu-latest
    if: github.event.action == 'closed'
    
    steps:
      - name: 🧹 Remove preview deployment
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ env.VERCEL_ORG_ID }}
          vercel-project-id: ${{ env.VERCEL_PROJECT_ID }}
          vercel-args: '--scope=${{ env.VERCEL_ORG_ID }}'
          github-comment: true
          working-directory: ./
          
      - name: 📝 Update PR with cleanup status
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const { context } = require('@actions/github');
            
            await github.rest.issues.createComment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: context.payload.pull_request.number,
              body: '🧹 Preview deployment has been cleaned up.'
            });