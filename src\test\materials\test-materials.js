/**
 * Material Database Test Suite
 * Comprehensive testing for material loading, searching, and cost calculations
 */

const path = require('path');
const fs = require('fs');

// Mock TypeScript modules for Node.js testing
console.log('🧪 Starting Material Database Tests...\n');

// Test 1: Validate core materials JSON structure
function testMaterialDataStructure() {
  console.log('📋 Test 1: Validating core materials JSON structure...');

  try {
    const materialsPath = path.join(__dirname, '../../data/materials/core-materials.json');
    const rawData = fs.readFileSync(materialsPath, 'utf8');
    const materialsData = JSON.parse(rawData);

    // Check metadata
    if (!materialsData.metadata) {
      throw new Error('Missing metadata section');
    }

    if (!materialsData.materials || !Array.isArray(materialsData.materials)) {
      throw new Error('Missing or invalid materials array');
    }

    console.log(`✅ Found ${materialsData.materials.length} materials in database`);
    console.log(`📅 Last updated: ${materialsData.metadata.lastUpdated}`);
    console.log(`🌍 Regions: ${materialsData.metadata.regions.join(', ')}`);

    // Validate required fields for each material
    const requiredFields = ['id', 'category', 'name', 'brand', 'unit', 'specifications', 'pricing', 'qualityScore'];
    const missingFields = [];

    materialsData.materials.forEach((material, index) => {
      requiredFields.forEach(field => {
        if (!(field in material)) {
          missingFields.push(`Material ${index} (${material.id || 'unknown'}): missing ${field}`);
        }
      });

      // Validate pricing structure
      if (material.pricing && !material.pricing.default) {
        missingFields.push(`Material ${material.id}: missing default pricing`);
      }
    });

    if (missingFields.length > 0) {
      console.log('⚠️  Found issues:');
      missingFields.forEach(issue => console.log(`   - ${issue}`));
    } else {
      console.log('✅ All materials have required fields');
    }

    return materialsData;

  } catch (error) {
    console.error('❌ Error loading materials data:', error.message);
    return null;
  }
}

// Test 2: Material categories and coverage
function testMaterialCoverage(materialsData) {
  console.log('\n📊 Test 2: Analyzing material coverage...');

  const categories = {};
  const brands = new Set();

  materialsData.materials.forEach(material => {
    if (!categories[material.category]) {
      categories[material.category] = [];
    }
    categories[material.category].push(material);
    brands.add(material.brand);
  });

  console.log('📂 Categories and counts:');
  Object.entries(categories).forEach(([category, materials]) => {
    console.log(`   ${category}: ${materials.length} materials`);
    const subcategories = [...new Set(materials.map(m => m.subcategory))];
    console.log(`      Subcategories: ${subcategories.join(', ')}`);
  });

  console.log(`\n🏭 Total brands: ${brands.size}`);
  console.log(`   Brands: ${Array.from(brands).slice(0, 10).join(', ')}${brands.size > 10 ? '...' : ''}`);

  // Check if we have materials for key construction phases
  const requiredCategories = ['Cement', 'Steel', 'Bricks', 'Sand', 'Aggregate', 'Electrical', 'Plumbing', 'Tiles', 'Paint'];
  const missingCategories = requiredCategories.filter(cat => !categories[cat]);

  if (missingCategories.length > 0) {
    console.log(`⚠️  Missing categories: ${missingCategories.join(', ')}`);
  } else {
    console.log('✅ All essential construction categories covered');
  }

  return categories;
}

// Test 3: Pricing validation
function testPricingValidation(materialsData) {
  console.log('\n💰 Test 3: Validating pricing data...');

  const regions = materialsData.metadata.regions;
  const pricingIssues = [];
  const priceRanges = {};

  materialsData.materials.forEach(material => {
    const pricing = material.pricing;

    // Check if all regions have pricing
    regions.forEach(region => {
      if (!pricing[region]) {
        pricingIssues.push(`Material ${material.id}: missing pricing for ${region}`);
      } else {
        const regionPricing = pricing[region];

        // Check pricing hierarchy (wholesale < bulk < retail)
        if (regionPricing.wholesale >= regionPricing.bulk) {
          pricingIssues.push(`Material ${material.id} (${region}): wholesale price not less than bulk`);
        }
        if (regionPricing.bulk >= regionPricing.retail) {
          pricingIssues.push(`Material ${material.id} (${region}): bulk price not less than retail`);
        }
      }
    });

    // Track price ranges by category
    if (!priceRanges[material.category]) {
      priceRanges[material.category] = { min: Infinity, max: 0, unit: material.unit };
    }

    const defaultPrice = pricing.default?.retail || 0;
    if (defaultPrice > 0) {
      priceRanges[material.category].min = Math.min(priceRanges[material.category].min, defaultPrice);
      priceRanges[material.category].max = Math.max(priceRanges[material.category].max, defaultPrice);
    }
  });

  if (pricingIssues.length > 0) {
    console.log('⚠️  Pricing issues found:');
    pricingIssues.slice(0, 5).forEach(issue => console.log(`   - ${issue}`));
    if (pricingIssues.length > 5) {
      console.log(`   ... and ${pricingIssues.length - 5} more issues`);
    }
  } else {
    console.log('✅ All pricing data is valid');
  }

  console.log('\n💸 Price ranges by category (retail):');
  Object.entries(priceRanges).forEach(([category, range]) => {
    if (range.min !== Infinity) {
      console.log(`   ${category}: ₹${range.min.toFixed(2)} - ₹${range.max.toFixed(2)} per ${range.unit}`);
    }
  });
}

// Test 4: Quality and specifications validation
function testQualityValidation(materialsData) {
  console.log('\n⭐ Test 4: Validating quality and specifications...');

  const qualityIssues = [];
  const qualityStats = {
    high: 0,    // 8.5+
    medium: 0,  // 6.0-8.4
    low: 0      // <6.0
  };

  materialsData.materials.forEach(material => {
    // Check quality score range
    if (material.qualityScore < 1 || material.qualityScore > 10) {
      qualityIssues.push(`Material ${material.id}: quality score out of range (${material.qualityScore})`);
    }

    // Categorize by quality
    if (material.qualityScore >= 8.5) {
      qualityStats.high++;
    } else if (material.qualityScore >= 6.0) {
      qualityStats.medium++;
    } else {
      qualityStats.low++;
    }

    // Check if specifications include IS code compliance
    if (!material.specifications.standardCompliance || !material.specifications.standardCompliance.includes('IS')) {
      qualityIssues.push(`Material ${material.id}: missing or invalid IS code compliance`);
    }

    // Check wastage percentage
    if (material.wastagePercentage < 0 || material.wastagePercentage > 30) {
      qualityIssues.push(`Material ${material.id}: unusual wastage percentage (${material.wastagePercentage}%)`);
    }
  });

  console.log(`📊 Quality distribution:`);
  console.log(`   High Quality (8.5+): ${qualityStats.high} materials`);
  console.log(`   Medium Quality (6.0-8.4): ${qualityStats.medium} materials`);
  console.log(`   Lower Quality (<6.0): ${qualityStats.low} materials`);

  if (qualityIssues.length > 0) {
    console.log('\n⚠️  Quality issues found:');
    qualityIssues.slice(0, 5).forEach(issue => console.log(`   - ${issue}`));
    if (qualityIssues.length > 5) {
      console.log(`   ... and ${qualityIssues.length - 5} more issues`);
    }
  } else {
    console.log('✅ All quality data is valid');
  }
}

// Test 5: Material search simulation
function testMaterialSearch(materialsData) {
  console.log('\n🔍 Test 5: Testing material search functionality...');

  // Simulate search by category
  const cementMaterials = materialsData.materials.filter(m => m.category === 'Cement');
  console.log(`   Found ${cementMaterials.length} cement materials`);

  // Simulate search by quality tier
  const premiumMaterials = materialsData.materials.filter(m => m.qualityScore >= 8.5);
  console.log(`   Found ${premiumMaterials.length} premium materials (quality 8.5+)`);

  // Simulate regional pricing comparison
  const testMaterial = materialsData.materials[0];
  if (testMaterial && testMaterial.pricing) {
    console.log(`\n💰 Regional pricing for ${testMaterial.name}:`);
    Object.entries(testMaterial.pricing).forEach(([region, pricing]) => {
      if (typeof pricing === 'object' && pricing.retail) {
        console.log(`   ${region}: ₹${pricing.retail} (retail), ₹${pricing.bulk} (bulk)`);
      }
    });
  }

  // Test brand filtering
  const brands = [...new Set(materialsData.materials.map(m => m.brand))];
  const popularBrands = ['UltraTech Cement', 'Tata Steel', 'Asian Paints', 'Havells'];
  const availableBrands = popularBrands.filter(brand => brands.includes(brand));

  console.log(`\n🏭 Popular brands available: ${availableBrands.join(', ')}`);
}

// Test 6: Cost calculation simulation
function testCostCalculation(materialsData) {
  console.log('\n💻 Test 6: Testing cost calculation logic...');

  // Simulate basic project requirements
  const projectSpecs = {
    builtUpArea: 1000, // sqft
    region: 'bangalore',
    qualityTier: 'Premium Selection'
  };

  console.log(`📋 Project simulation: ${projectSpecs.builtUpArea} sqft in ${projectSpecs.region}`);

  // Calculate sample material requirements
  const sampleRequirements = [
    { materialId: 'cement_opc53_ultratech', quantity: 20, unit: 'bag' },
    { materialId: 'steel_tmt_fe500d_tata', quantity: 2000, unit: 'kg' },
    { materialId: 'brick_red_clay_standard', quantity: 8000, unit: 'piece' },
    { materialId: 'tile_vitrified_kajaria', quantity: 100, unit: 'sqm' }
  ];

  let totalCost = 0;
  let totalWastage = 0;

  console.log('\n🧮 Sample cost calculation:');

  sampleRequirements.forEach(req => {
    const material = materialsData.materials.find(m => m.id === req.materialId);
    if (material) {
      const pricing = material.pricing[projectSpecs.region] || material.pricing.default;
      const unitCost = pricing.bulk; // Use bulk pricing
      const subtotal = unitCost * req.quantity;
      const wastage = (subtotal * material.wastagePercentage) / 100;
      const finalCost = subtotal + wastage;

      console.log(`   ${material.name}: ${req.quantity} ${req.unit} × ₹${unitCost} = ₹${subtotal.toFixed(2)} + wastage(${material.wastagePercentage}%) = ₹${finalCost.toFixed(2)}`);

      totalCost += finalCost;
      totalWastage += wastage;
    }
  });

  console.log(`\n💰 Total estimated cost: ₹${totalCost.toFixed(2)}`);
  console.log(`⚠️  Total wastage: ₹${totalWastage.toFixed(2)} (${((totalWastage/totalCost)*100).toFixed(1)}%)`);
  console.log(`📊 Cost per sqft: ₹${(totalCost/projectSpecs.builtUpArea).toFixed(2)}`);
}

// Run all tests
function runAllTests() {
  console.log('🚀 Nirmaan AI Material Database Test Suite');
  console.log('==========================================');

  const materialsData = testMaterialDataStructure();

  if (!materialsData) {
    console.log('\n❌ Cannot proceed with tests due to data loading failure');
    return;
  }

  const categories = testMaterialCoverage(materialsData);
  testPricingValidation(materialsData);
  testQualityValidation(materialsData);
  testMaterialSearch(materialsData);
  testCostCalculation(materialsData);

  console.log('\n🎉 Test Summary:');
  console.log(`   📦 Materials loaded: ${materialsData.materials.length}`);
  console.log(`   📂 Categories: ${Object.keys(categories).length}`);
  console.log(`   🌍 Regions: ${materialsData.metadata.regions.length}`);
  console.log(`   ⭐ Average quality: ${(materialsData.materials.reduce((sum, m) => sum + m.qualityScore, 0) / materialsData.materials.length).toFixed(1)}`);

  console.log('\n✅ Material database tests completed successfully!');
  console.log('🔗 Integration with calculator engine ready for implementation.');
}

// Execute tests
runAllTests();