/**
 * Accessibility Testing and Validation Utilities
 * Provides comprehensive accessibility testing for WCAG 2.1 AA compliance
 */

export interface AccessibilityIssue {
  level: 'error' | 'warning' | 'info';
  criterion: string;
  message: string;
  element?: HTMLElement;
  selector?: string;
  fix?: string;
}

export interface AccessibilityTestResult {
  passes: AccessibilityIssue[];
  violations: AccessibilityIssue[];
  warnings: AccessibilityIssue[];
  incomplete: AccessibilityIssue[];
  summary: {
    total: number;
    errors: number;
    warnings: number;
    score: number;
  };
}

export interface AccessibilityTestConfig {
  includeTags?: string[];
  excludeTags?: string[];
  level?: 'A' | 'AA' | 'AAA';
  selector?: string;
  skipElements?: string[];
}

/**
 * Accessibility Testing Manager
 */
export class AccessibilityTester {
  private static instance: AccessibilityTester;
  private issues: AccessibilityIssue[] = [];

  constructor() {
    this.setupObserver();
  }

  static getInstance(): AccessibilityTester {
    if (!AccessibilityTester.instance) {
      AccessibilityTester.instance = new AccessibilityTester();
    }
    return AccessibilityTester.instance;
  }

  /**
   * Run comprehensive accessibility audit
   */
  async audit(config: AccessibilityTestConfig = {}): Promise<AccessibilityTestResult> {
    const results: AccessibilityTestResult = {
      passes: [],
      violations: [],
      warnings: [],
      incomplete: [],
      summary: {
        total: 0,
        errors: 0,
        warnings: 0,
        score: 0
      }
    };

    const container = config.selector ? 
      document.querySelector(config.selector) as HTMLElement : 
      document.body;

    if (!container) {
      throw new Error('Container element not found');
    }

    // Run all accessibility tests
    const tests = [
      this.testColorContrast(container),
      this.testFocusManagement(container),
      this.testAriaLabels(container),
      this.testHeadingStructure(container),
      this.testFormLabels(container),
      this.testKeyboardNavigation(container),
      this.testImages(container),
      this.testLinks(container),
      this.testTables(container),
      this.testLandmarks(container),
      this.testLiveRegions(container),
      this.testTextAlternatives(container),
      this.testButtonLabels(container),
      this.testMotionAndAnimation(container)
    ];

    // Collect all test results
    const allIssues = (await Promise.all(tests)).flat();

    // Categorize issues
    allIssues.forEach(issue => {
      switch (issue.level) {
        case 'error':
          results.violations.push(issue);
          break;
        case 'warning':
          results.warnings.push(issue);
          break;
        case 'info':
          results.passes.push(issue);
          break;
      }
    });

    // Calculate summary
    results.summary = {
      total: allIssues.length,
      errors: results.violations.length,
      warnings: results.warnings.length,
      score: this.calculateScore(results)
    };

    return results;
  }

  /**
   * Test color contrast ratios
   */
  async testColorContrast(container: HTMLElement): Promise<AccessibilityIssue[]> {
    const issues: AccessibilityIssue[] = [];
    const textElements = container.querySelectorAll('p, span, div, h1, h2, h3, h4, h5, h6, a, button, label, li');

    textElements.forEach(element => {
      const computed = window.getComputedStyle(element);
      const color = computed.color;
      const backgroundColor = computed.backgroundColor;
      
      if (color && backgroundColor && backgroundColor !== 'rgba(0, 0, 0, 0)') {
        const ratio = this.calculateContrastRatio(color, backgroundColor);
        const fontSize = parseFloat(computed.fontSize);
        const fontWeight = computed.fontWeight;
        
        const isLarge = fontSize >= 18 || (fontSize >= 14 && (fontWeight === 'bold' || parseInt(fontWeight) >= 700));
        const minimumRatio = isLarge ? 3 : 4.5;
        
        if (ratio < minimumRatio) {
          issues.push({
            level: 'error',
            criterion: '1.4.3 Contrast (Minimum)',
            message: `Text contrast ratio is ${ratio.toFixed(2)}:1, but should be at least ${minimumRatio}:1`,
            element: element as HTMLElement,
            selector: this.getElementSelector(element),
            fix: 'Increase color contrast between text and background'
          });
        } else if (ratio < 7 && !isLarge) {
          issues.push({
            level: 'warning',
            criterion: '1.4.6 Contrast (Enhanced)',
            message: `Text contrast ratio is ${ratio.toFixed(2)}:1. Consider improving to 7:1 for AAA compliance`,
            element: element as HTMLElement,
            selector: this.getElementSelector(element),
            fix: 'Consider increasing contrast for enhanced accessibility'
          });
        }
      }
    });

    return issues;
  }

  /**
   * Test focus management
   */
  async testFocusManagement(container: HTMLElement): Promise<AccessibilityIssue[]> {
    const issues: AccessibilityIssue[] = [];
    const focusableElements = container.querySelectorAll(
      'button, input, select, textarea, a[href], [tabindex]:not([tabindex="-1"])'
    );

    focusableElements.forEach(element => {
      const computed = window.getComputedStyle(element);
      
      // Check for focus indicators
      if (!element.matches(':focus-visible')) {
        // Simulate focus to test
        (element as HTMLElement).focus();
        const focusedStyle = window.getComputedStyle(element);
        
        if (focusedStyle.outline === 'none' || focusedStyle.outline === '0px' || 
            focusedStyle.outline === 'medium none') {
          issues.push({
            level: 'error',
            criterion: '2.4.7 Focus Visible',
            message: 'Element has no visible focus indicator',
            element: element as HTMLElement,
            selector: this.getElementSelector(element),
            fix: 'Add visible focus indicator with outline or box-shadow'
          });
        }
      }

      // Check tabindex values
      const tabindex = element.getAttribute('tabindex');
      if (tabindex && parseInt(tabindex) > 0) {
        issues.push({
          level: 'warning',
          criterion: '2.4.3 Focus Order',
          message: 'Positive tabindex values can disrupt natural focus order',
          element: element as HTMLElement,
          selector: this.getElementSelector(element),
          fix: 'Use tabindex="0" or rely on natural DOM order'
        });
      }
    });

    return issues;
  }

  /**
   * Test ARIA labels and roles
   */
  async testAriaLabels(container: HTMLElement): Promise<AccessibilityIssue[]> {
    const issues: AccessibilityIssue[] = [];
    
    // Test buttons without accessible names
    const buttons = container.querySelectorAll('button');
    buttons.forEach(button => {
      const accessibleName = this.getAccessibleName(button);
      if (!accessibleName) {
        issues.push({
          level: 'error',
          criterion: '4.1.2 Name, Role, Value',
          message: 'Button has no accessible name',
          element: button,
          selector: this.getElementSelector(button),
          fix: 'Add aria-label, aria-labelledby, or visible text content'
        });
      }
    });

    // Test form controls without labels
    const formControls = container.querySelectorAll('input, select, textarea');
    formControls.forEach(control => {
      const accessibleName = this.getAccessibleName(control);
      if (!accessibleName) {
        issues.push({
          level: 'error',
          criterion: '3.3.2 Labels or Instructions',
          message: 'Form control has no accessible label',
          element: control as HTMLElement,
          selector: this.getElementSelector(control),
          fix: 'Add a label element or aria-label attribute'
        });
      }
    });

    // Test custom interactive elements
    const customElements = container.querySelectorAll('[role="button"], [role="link"], [role="menuitem"], [role="tab"]');
    customElements.forEach(element => {
      const accessibleName = this.getAccessibleName(element);
      if (!accessibleName) {
        issues.push({
          level: 'error',
          criterion: '4.1.2 Name, Role, Value',
          message: 'Interactive element has no accessible name',
          element: element as HTMLElement,
          selector: this.getElementSelector(element),
          fix: 'Add aria-label or aria-labelledby attribute'
        });
      }
    });

    return issues;
  }

  /**
   * Test heading structure
   */
  async testHeadingStructure(container: HTMLElement): Promise<AccessibilityIssue[]> {
    const issues: AccessibilityIssue[] = [];
    const headings = container.querySelectorAll('h1, h2, h3, h4, h5, h6, [role="heading"]');
    
    let previousLevel = 0;
    let hasH1 = false;

    headings.forEach((heading, index) => {
      const level = heading.tagName.startsWith('H') 
        ? parseInt(heading.tagName.charAt(1))
        : parseInt(heading.getAttribute('aria-level') || '1');

      if (level === 1) {
        hasH1 = true;
      }

      // Check for skipped heading levels
      if (index > 0 && level > previousLevel + 1) {
        issues.push({
          level: 'warning',
          criterion: '1.3.1 Info and Relationships',
          message: `Heading level skipped from h${previousLevel} to h${level}`,
          element: heading as HTMLElement,
          selector: this.getElementSelector(heading),
          fix: 'Use consecutive heading levels without skipping'
        });
      }

      // Check for empty headings
      const text = heading.textContent?.trim();
      if (!text) {
        issues.push({
          level: 'error',
          criterion: '2.4.6 Headings and Labels',
          message: 'Heading is empty',
          element: heading as HTMLElement,
          selector: this.getElementSelector(heading),
          fix: 'Add descriptive text to the heading'
        });
      }

      previousLevel = level;
    });

    // Check for missing H1
    if (headings.length > 0 && !hasH1) {
      issues.push({
        level: 'warning',
        criterion: '2.4.6 Headings and Labels',
        message: 'Page has no H1 heading',
        selector: 'body',
        fix: 'Add an H1 heading to identify the main content'
      });
    }

    return issues;
  }

  /**
   * Test form labels
   */
  async testFormLabels(container: HTMLElement): Promise<AccessibilityIssue[]> {
    const issues: AccessibilityIssue[] = [];
    const formFields = container.querySelectorAll('input:not([type="hidden"]), select, textarea');

    formFields.forEach(field => {
      const fieldId = field.id;
      const associatedLabel = fieldId ? container.querySelector(`label[for="${fieldId}"]`) : null;
      const ariaLabel = field.getAttribute('aria-label');
      const ariaLabelledby = field.getAttribute('aria-labelledby');

      if (!associatedLabel && !ariaLabel && !ariaLabelledby) {
        issues.push({
          level: 'error',
          criterion: '3.3.2 Labels or Instructions',
          message: 'Form field has no associated label',
          element: field as HTMLElement,
          selector: this.getElementSelector(field),
          fix: 'Add a label element with for attribute, aria-label, or aria-labelledby'
        });
      }

      // Check for required field indicators
      const isRequired = field.hasAttribute('required') || field.getAttribute('aria-required') === 'true';
      if (isRequired) {
        const labelText = associatedLabel?.textContent || ariaLabel || '';
        if (!labelText.includes('*') && !labelText.toLowerCase().includes('required')) {
          issues.push({
            level: 'warning',
            criterion: '3.3.2 Labels or Instructions',
            message: 'Required field should indicate requirement to users',
            element: field as HTMLElement,
            selector: this.getElementSelector(field),
            fix: 'Add visual and text indication that field is required'
          });
        }
      }
    });

    return issues;
  }

  /**
   * Test keyboard navigation
   */
  async testKeyboardNavigation(container: HTMLElement): Promise<AccessibilityIssue[]> {
    const issues: AccessibilityIssue[] = [];
    const interactiveElements = container.querySelectorAll(
      'button, input, select, textarea, a, [tabindex]:not([tabindex="-1"]), [role="button"], [role="link"]'
    );

    interactiveElements.forEach(element => {
      // Check if element is keyboard accessible
      const tabindex = element.getAttribute('tabindex');
      const isNativelyFocusable = ['button', 'input', 'select', 'textarea', 'a'].includes(
        element.tagName.toLowerCase()
      );

      if (!isNativelyFocusable && (tabindex === null || tabindex === '-1')) {
        issues.push({
          level: 'error',
          criterion: '2.1.1 Keyboard',
          message: 'Interactive element is not keyboard accessible',
          element: element as HTMLElement,
          selector: this.getElementSelector(element),
          fix: 'Add tabindex="0" or use native interactive elements'
        });
      }

      // Check for click handlers without keyboard handlers
      const hasClickHandler = element.getAttribute('onclick') !== null;
      const hasKeyHandler = element.getAttribute('onkeydown') !== null || 
                           element.getAttribute('onkeypress') !== null;

      if (hasClickHandler && !hasKeyHandler && !isNativelyFocusable) {
        issues.push({
          level: 'warning',
          criterion: '2.1.1 Keyboard',
          message: 'Element has click handler but no keyboard handler',
          element: element as HTMLElement,
          selector: this.getElementSelector(element),
          fix: 'Add keyboard event handlers for Enter and Space keys'
        });
      }
    });

    return issues;
  }

  /**
   * Test images
   */
  async testImages(container: HTMLElement): Promise<AccessibilityIssue[]> {
    const issues: AccessibilityIssue[] = [];
    const images = container.querySelectorAll('img');

    images.forEach(img => {
      const alt = img.getAttribute('alt');
      const role = img.getAttribute('role');

      if (alt === null) {
        issues.push({
          level: 'error',
          criterion: '1.1.1 Non-text Content',
          message: 'Image is missing alt attribute',
          element: img,
          selector: this.getElementSelector(img),
          fix: 'Add alt attribute with descriptive text or empty alt="" for decorative images'
        });
      } else if (alt === '' && role !== 'presentation') {
        // Empty alt should be used carefully
        const isDecorative = img.closest('[role="presentation"]') || 
                           img.classList.contains('decorative');
        if (!isDecorative) {
          issues.push({
            level: 'warning',
            criterion: '1.1.1 Non-text Content',
            message: 'Image has empty alt attribute but may not be decorative',
            element: img,
            selector: this.getElementSelector(img),
            fix: 'Verify if image is decorative or add descriptive alt text'
          });
        }
      }
    });

    return issues;
  }

  /**
   * Test links
   */
  async testLinks(container: HTMLElement): Promise<AccessibilityIssue[]> {
    const issues: AccessibilityIssue[] = [];
    const links = container.querySelectorAll('a');

    links.forEach(link => {
      const href = link.getAttribute('href');
      const text = link.textContent?.trim();
      const ariaLabel = link.getAttribute('aria-label');

      // Check for missing href
      if (!href) {
        issues.push({
          level: 'warning',
          criterion: '4.1.2 Name, Role, Value',
          message: 'Link is missing href attribute',
          element: link,
          selector: this.getElementSelector(link),
          fix: 'Add href attribute or use button element instead'
        });
      }

      // Check for accessible name
      const accessibleName = ariaLabel || text;
      if (!accessibleName) {
        issues.push({
          level: 'error',
          criterion: '2.4.4 Link Purpose (In Context)',
          message: 'Link has no accessible name',
          element: link,
          selector: this.getElementSelector(link),
          fix: 'Add descriptive text content or aria-label'
        });
      } else if (accessibleName.toLowerCase().trim() === 'click here' || 
                 accessibleName.toLowerCase().trim() === 'read more') {
        issues.push({
          level: 'warning',
          criterion: '2.4.4 Link Purpose (In Context)',
          message: 'Link text is not descriptive',
          element: link,
          selector: this.getElementSelector(link),
          fix: 'Use more descriptive link text that explains the destination'
        });
      }

      // Check for target="_blank" without warning
      if (link.getAttribute('target') === '_blank' && 
          !link.getAttribute('aria-label')?.includes('opens in new')) {
        issues.push({
          level: 'warning',
          criterion: '3.2.5 Change on Request',
          message: 'Link opens in new window without warning',
          element: link,
          selector: this.getElementSelector(link),
          fix: 'Add indication that link opens in new window/tab'
        });
      }
    });

    return issues;
  }

  /**
   * Test tables
   */
  async testTables(container: HTMLElement): Promise<AccessibilityIssue[]> {
    const issues: AccessibilityIssue[] = [];
    const tables = container.querySelectorAll('table');

    tables.forEach(table => {
      // Check for caption
      const caption = table.querySelector('caption');
      if (!caption) {
        issues.push({
          level: 'warning',
          criterion: '1.3.1 Info and Relationships',
          message: 'Table is missing caption',
          element: table,
          selector: this.getElementSelector(table),
          fix: 'Add caption element to describe table purpose'
        });
      }

      // Check for headers
      const headers = table.querySelectorAll('th');
      const rows = table.querySelectorAll('tr');
      
      if (headers.length === 0 && rows.length > 1) {
        issues.push({
          level: 'error',
          criterion: '1.3.1 Info and Relationships',
          message: 'Data table is missing header cells',
          element: table,
          selector: this.getElementSelector(table),
          fix: 'Add th elements for column and row headers'
        });
      }

      // Check header scope
      headers.forEach(header => {
        const scope = header.getAttribute('scope');
        if (!scope) {
          issues.push({
            level: 'warning',
            criterion: '1.3.1 Info and Relationships',
            message: 'Table header is missing scope attribute',
            element: header,
            selector: this.getElementSelector(header),
            fix: 'Add scope="col" or scope="row" to header cells'
          });
        }
      });
    });

    return issues;
  }

  /**
   * Test landmarks
   */
  async testLandmarks(container: HTMLElement): Promise<AccessibilityIssue[]> {
    const issues: AccessibilityIssue[] = [];
    
    // Check for main landmark
    const mainLandmarks = container.querySelectorAll('main, [role="main"]');
    if (mainLandmarks.length === 0) {
      issues.push({
        level: 'warning',
        criterion: '1.3.6 Identify Purpose',
        message: 'Page is missing main landmark',
        selector: 'body',
        fix: 'Add main element or role="main" to identify main content'
      });
    } else if (mainLandmarks.length > 1) {
      issues.push({
        level: 'error',
        criterion: '1.3.6 Identify Purpose',
        message: 'Page has multiple main landmarks',
        selector: 'body',
        fix: 'Use only one main landmark per page'
      });
    }

    // Check for navigation landmarks
    const navElements = container.querySelectorAll('nav, [role="navigation"]');
    navElements.forEach(nav => {
      const accessibleName = this.getAccessibleName(nav);
      if (navElements.length > 1 && !accessibleName) {
        issues.push({
          level: 'warning',
          criterion: '2.4.1 Bypass Blocks',
          message: 'Navigation landmark should have accessible name when multiple exist',
          element: nav as HTMLElement,
          selector: this.getElementSelector(nav),
          fix: 'Add aria-label or aria-labelledby to distinguish navigation areas'
        });
      }
    });

    return issues;
  }

  /**
   * Test live regions
   */
  async testLiveRegions(container: HTMLElement): Promise<AccessibilityIssue[]> {
    const issues: AccessibilityIssue[] = [];
    const liveRegions = container.querySelectorAll('[aria-live]');

    liveRegions.forEach(region => {
      const liveValue = region.getAttribute('aria-live');
      if (liveValue !== 'polite' && liveValue !== 'assertive' && liveValue !== 'off') {
        issues.push({
          level: 'error',
          criterion: '4.1.2 Name, Role, Value',
          message: 'Invalid aria-live value',
          element: region as HTMLElement,
          selector: this.getElementSelector(region),
          fix: 'Use aria-live="polite" or aria-live="assertive"'
        });
      }
    });

    return issues;
  }

  /**
   * Test text alternatives
   */
  async testTextAlternatives(container: HTMLElement): Promise<AccessibilityIssue[]> {
    const issues: AccessibilityIssue[] = [];
    
    // Test videos
    const videos = container.querySelectorAll('video');
    videos.forEach(video => {
      const track = video.querySelector('track[kind="captions"]');
      if (!track) {
        issues.push({
          level: 'warning',
          criterion: '1.2.2 Captions (Prerecorded)',
          message: 'Video is missing captions',
          element: video,
          selector: this.getElementSelector(video),
          fix: 'Add track element with captions'
        });
      }
    });

    // Test audio
    const audioElements = container.querySelectorAll('audio');
    audioElements.forEach(audio => {
      const hasTranscript = audio.getAttribute('data-transcript') || 
                           audio.nextElementSibling?.classList.contains('transcript');
      if (!hasTranscript) {
        issues.push({
          level: 'warning',
          criterion: '1.2.1 Audio-only and Video-only (Prerecorded)',
          message: 'Audio content should have transcript',
          element: audio,
          selector: this.getElementSelector(audio),
          fix: 'Provide text transcript for audio content'
        });
      }
    });

    return issues;
  }

  /**
   * Test button labels
   */
  async testButtonLabels(container: HTMLElement): Promise<AccessibilityIssue[]> {
    const issues: AccessibilityIssue[] = [];
    const buttons = container.querySelectorAll('button, [role="button"]');

    buttons.forEach(button => {
      const accessibleName = this.getAccessibleName(button);
      if (!accessibleName) {
        issues.push({
          level: 'error',
          criterion: '4.1.2 Name, Role, Value',
          message: 'Button has no accessible name',
          element: button as HTMLElement,
          selector: this.getElementSelector(button),
          fix: 'Add text content, aria-label, or aria-labelledby'
        });
      }
    });

    return issues;
  }

  /**
   * Test motion and animation
   */
  async testMotionAndAnimation(container: HTMLElement): Promise<AccessibilityIssue[]> {
    const issues: AccessibilityIssue[] = [];
    
    // Check for auto-playing media
    const autoplayMedia = container.querySelectorAll('video[autoplay], audio[autoplay]');
    autoplayMedia.forEach(media => {
      const duration = (media as HTMLMediaElement).duration;
      if (duration > 3) {
        issues.push({
          level: 'warning',
          criterion: '2.2.2 Pause, Stop, Hide',
          message: 'Auto-playing media longer than 3 seconds should have controls',
          element: media as HTMLElement,
          selector: this.getElementSelector(media),
          fix: 'Add controls attribute or reduce duration to 3 seconds or less'
        });
      }
    });

    // Check for CSS animations
    const animatedElements = container.querySelectorAll('*');
    animatedElements.forEach(element => {
      const computed = window.getComputedStyle(element);
      const animation = computed.animation;
      
      if (animation && animation !== 'none') {
        // Check if respects prefers-reduced-motion
        const reducedMotionQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
        if (reducedMotionQuery.matches) {
          issues.push({
            level: 'info',
            criterion: '2.3.3 Animation from Interactions',
            message: 'Element has animation but user prefers reduced motion',
            element: element as HTMLElement,
            selector: this.getElementSelector(element),
            fix: 'Respect prefers-reduced-motion media query'
          });
        }
      }
    });

    return issues;
  }

  /**
   * Generate accessibility report
   */
  generateReport(results: AccessibilityTestResult): string {
    const { summary, violations, warnings } = results;
    
    let report = `# Accessibility Test Report\n\n`;
    report += `**Score: ${summary.score}%**\n\n`;
    report += `- Total Issues: ${summary.total}\n`;
    report += `- Errors: ${summary.errors}\n`;
    report += `- Warnings: ${summary.warnings}\n\n`;

    if (violations.length > 0) {
      report += `## Violations (${violations.length})\n\n`;
      violations.forEach((issue, index) => {
        report += `### ${index + 1}. ${issue.criterion}\n`;
        report += `**Message:** ${issue.message}\n`;
        if (issue.selector) {
          report += `**Element:** \`${issue.selector}\`\n`;
        }
        report += `**Fix:** ${issue.fix}\n\n`;
      });
    }

    if (warnings.length > 0) {
      report += `## Warnings (${warnings.length})\n\n`;
      warnings.forEach((issue, index) => {
        report += `### ${index + 1}. ${issue.criterion}\n`;
        report += `**Message:** ${issue.message}\n`;
        if (issue.selector) {
          report += `**Element:** \`${issue.selector}\`\n`;
        }
        report += `**Fix:** ${issue.fix}\n\n`;
      });
    }

    return report;
  }

  private calculateScore(results: AccessibilityTestResult): number {
    const { violations, warnings } = results;
    const totalIssues = violations.length + warnings.length;
    
    if (totalIssues === 0) return 100;
    
    // Weight errors more heavily than warnings
    const errorWeight = 10;
    const warningWeight = 3;
    const totalWeight = violations.length * errorWeight + warnings.length * warningWeight;
    const maxWeight = totalIssues * errorWeight;
    
    return Math.max(0, Math.round((1 - totalWeight / maxWeight) * 100));
  }

  private getAccessibleName(element: Element): string {
    const ariaLabel = element.getAttribute('aria-label');
    if (ariaLabel) return ariaLabel;

    const ariaLabelledby = element.getAttribute('aria-labelledby');
    if (ariaLabelledby) {
      const referencedElement = document.getElementById(ariaLabelledby);
      if (referencedElement) return referencedElement.textContent?.trim() || '';
    }

    const labelledBy = element.id ? document.querySelector(`label[for="${element.id}"]`) : null;
    if (labelledBy) return labelledBy.textContent?.trim() || '';

    return element.textContent?.trim() || '';
  }

  private getElementSelector(element: Element): string {
    if (element.id) return `#${element.id}`;
    
    const tagName = element.tagName.toLowerCase();
    const className = element.className ? `.${element.className.split(' ').join('.')}` : '';
    
    return `${tagName}${className}`;
  }

  private calculateContrastRatio(color1: string, color2: string): number {
    // Simplified contrast calculation - in practice, you'd want a more robust implementation
    const rgb1 = this.parseColor(color1);
    const rgb2 = this.parseColor(color2);
    
    if (!rgb1 || !rgb2) return 1;
    
    const lum1 = this.getLuminance(rgb1);
    const lum2 = this.getLuminance(rgb2);
    
    const brightest = Math.max(lum1, lum2);
    const darkest = Math.min(lum1, lum2);
    
    return (brightest + 0.05) / (darkest + 0.05);
  }

  private parseColor(color: string): { r: number; g: number; b: number } | null {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (!ctx) return null;
    
    ctx.fillStyle = color;
    const computedColor = ctx.fillStyle;
    
    if (computedColor.startsWith('#')) {
      const hex = computedColor.slice(1);
      return {
        r: parseInt(hex.slice(0, 2), 16),
        g: parseInt(hex.slice(2, 4), 16),
        b: parseInt(hex.slice(4, 6), 16)
      };
    }
    
    const match = computedColor.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/);
    if (match) {
      return {
        r: parseInt(match[1]),
        g: parseInt(match[2]),
        b: parseInt(match[3])
      };
    }
    
    return null;
  }

  private getLuminance(rgb: { r: number; g: number; b: number }): number {
    const [r, g, b] = [rgb.r, rgb.g, rgb.b].map(c => {
      c = c / 255;
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    });
    
    return 0.2126 * r + 0.7152 * g + 0.0722 * b;
  }

  private setupObserver(): void {
    // Watch for DOM changes to identify new accessibility issues
    const observer = new MutationObserver(mutations => {
      mutations.forEach(mutation => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach(node => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              this.quickCheck(node as HTMLElement);
            }
          });
        }
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  }

  private quickCheck(element: HTMLElement): void {
    // Quick accessibility check for dynamically added elements
    if (element.tagName === 'BUTTON' && !this.getAccessibleName(element)) {
      console.warn('Button added without accessible name:', element);
    }
    
    if (element.tagName === 'IMG' && !element.hasAttribute('alt')) {
      console.warn('Image added without alt attribute:', element);
    }
  }
}

// Export singleton instance
export const accessibilityTester = AccessibilityTester.getInstance();

/**
 * React hook for accessibility testing
 */
export function useAccessibilityTesting() {
  return {
    audit: accessibilityTester.audit.bind(accessibilityTester),
    generateReport: accessibilityTester.generateReport.bind(accessibilityTester)
  };
}