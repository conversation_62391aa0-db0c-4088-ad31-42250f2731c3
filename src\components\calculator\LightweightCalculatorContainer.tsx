'use client';

import React, { useState, useCallback, useMemo } from 'react';
import { motion, AnimatePresence, type Variants } from 'framer-motion';
import { Enhanced<PERSON><PERSON>, CardHeader, CardTitle, CardContent, CardFooter } from '@/components/ui/enhanced-card';
import { EnhancedButton } from '@/components/ui/enhanced-button';
import { EnhancedInput } from '@/components/ui/enhanced-input';
import { EnhancedSelect } from '@/components/ui/enhanced-select';
import { EnhancedProgress } from '@/components/ui/enhanced-progress';
import { 
  Calculator, 
  Download, 
  Save, 
  RefreshCw, 
  MapPin, 
  Building2, 
  Layers3, 
  Sparkles,
  TrendingUp,
  Clock,
  CheckCircle,
  AlertCircle,
  HelpCircle,
  ArrowRight,
  ArrowLeft,
  Zap,
  FileText,
  Info
} from 'lucide-react';
import jsPDF from 'jspdf';

interface CalculationInputs {
  plotSize: string;
  floors: string;
  quality: string;
  location: string;
  buildingType: string;
}

interface CalculationResult {
  totalCost: number;
  costPerSqft: number;
  breakdown: {
    structure: { amount: number };
    finishing: { amount: number };
    mep: { amount: number };
    external: { amount: number };
    other: { amount: number };
  };
  materials?: Array<{
    name: string;
    quantity: number;
    unit: string;
    totalCost: number;
  }>;
  timeline?: Array<{
    name: string;
    duration: number;
    startAfter: number;
  }>;
}

export function LightweightCalculatorContainer() {
  const [inputs, setInputs] = useState<CalculationInputs>({
    plotSize: '',
    floors: '1',
    quality: 'smart',
    location: 'delhi',
    buildingType: 'residential',
  });

  const [result, setResult] = useState<CalculationResult | null>(null);
  const [isCalculating, setIsCalculating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentStep, setCurrentStep] = useState(1);
  const [completedSteps, setCompletedSteps] = useState<Set<number>>(new Set());
  const [isFormValid, setIsFormValid] = useState(false);
  
  // Animation variants
  const containerVariants: Variants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  };
  
  const stepVariants: Variants = {
    hidden: { opacity: 0, x: 20 },
    visible: { 
      opacity: 1, 
      x: 0,
      transition: { duration: 0.3 }
    },
    exit: { 
      opacity: 0, 
      x: -20,
      transition: { duration: 0.3 }
    }
  };
  
  const resultVariants: Variants = {
    hidden: { opacity: 0, scale: 0.9 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.5,
        type: 'spring',
        stiffness: 100
      }
    }
  };

  const handleInputChange = useCallback((field: keyof CalculationInputs, value: string) => {
    setInputs(prev => ({ ...prev, [field]: value }));
    setError(null);
    
    // Validate form after each input change
    const newInputs = { ...inputs, [field]: value };
    validateForm(newInputs);
  }, [inputs]);
  
  const validateForm = useCallback((inputsToValidate: CalculationInputs) => {
    const isValid = 
      inputsToValidate.plotSize && 
      parseFloat(inputsToValidate.plotSize) >= 500 && 
      parseFloat(inputsToValidate.plotSize) <= 50000 &&
      inputsToValidate.floors &&
      inputsToValidate.quality &&
      inputsToValidate.location &&
      inputsToValidate.buildingType;
    
    setIsFormValid(isValid);
    return isValid;
  }, []);
  
  const nextStep = useCallback(() => {
    if (currentStep < 3) {
      setCompletedSteps(prev => new Set([...prev, currentStep]));
      setCurrentStep(prev => prev + 1);
    }
  }, [currentStep]);
  
  const prevStep = useCallback(() => {
    if (currentStep > 1) {
      setCurrentStep(prev => prev - 1);
    }
  }, [currentStep]);

  const handleCalculate = async () => {
    if (!inputs.plotSize || parseFloat(inputs.plotSize) < 500) {
      setError('Plot size must be at least 500 sq ft');
      return;
    }

    setIsCalculating(true);
    setError(null);

    try {
      const response = await fetch('/api/calculate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          builtUpArea: parseFloat(inputs.plotSize),
          floors: parseInt(inputs.floors),
          qualityTier: inputs.quality,
          location: inputs.location,
          buildingType: inputs.buildingType,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      if (data.success) {
        const resultData = data.data;
        
        // Add mock materials list if not provided by API
        if (!resultData.materials || resultData.materials.length === 0) {
          const totalCost = resultData.totalCost || 0;
          resultData.materials = [
            {
              name: 'Cement (OPC 53 Grade)',
              quantity: Math.round((totalCost / 3500) * 0.08),
              unit: 'bags',
              totalCost: Math.round(totalCost * 0.12)
            },
            {
              name: 'TMT Steel Bars (Fe 500)',
              quantity: Math.round((totalCost / 3500) * 0.06),
              unit: 'tonnes',
              totalCost: Math.round(totalCost * 0.15)
            },
            {
              name: 'Concrete Blocks',
              quantity: Math.round((totalCost / 3500) * 0.8),
              unit: 'pieces',
              totalCost: Math.round(totalCost * 0.08)
            },
            {
              name: 'River Sand',
              quantity: Math.round((totalCost / 3500) * 0.12),
              unit: 'tonnes',
              totalCost: Math.round(totalCost * 0.06)
            },
            {
              name: 'Aggregates (20mm)',
              quantity: Math.round((totalCost / 3500) * 0.15),
              unit: 'tonnes',
              totalCost: Math.round(totalCost * 0.05)
            },
            {
              name: 'Tiles (Vitrified)',
              quantity: Math.round(parseFloat(inputs.plotSize) * 0.8),
              unit: 'sq ft',
              totalCost: Math.round(totalCost * 0.08)
            },
            {
              name: 'Paint (Interior)',
              quantity: Math.round(parseFloat(inputs.plotSize) * 1.2),
              unit: 'sq ft',
              totalCost: Math.round(totalCost * 0.04)
            },
            {
              name: 'Electrical Fittings',
              quantity: Math.round(parseFloat(inputs.plotSize) / 100),
              unit: 'set',
              totalCost: Math.round(totalCost * 0.06)
            }
          ];
        }

        // Add mock timeline if not provided by API
        if (!resultData.timeline || resultData.timeline.length === 0) {
          resultData.timeline = [
            {
              name: 'Foundation & Plinth',
              duration: 30,
              startAfter: 0
            },
            {
              name: 'Superstructure',
              duration: 60,
              startAfter: 30
            },
            {
              name: 'Roofing & Slab',
              duration: 45,
              startAfter: 90
            },
            {
              name: 'Masonry & Plastering',
              duration: 40,
              startAfter: 135
            },
            {
              name: 'Flooring & Tiling',
              duration: 30,
              startAfter: 175
            },
            {
              name: 'Electrical & Plumbing',
              duration: 35,
              startAfter: 175
            },
            {
              name: 'Finishing & Painting',
              duration: 25,
              startAfter: 210
            },
            {
              name: 'Final Touches',
              duration: 15,
              startAfter: 235
            }
          ];
        }

        setResult(resultData);
      } else {
        throw new Error(data.error?.message || 'Calculation failed');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Calculation failed');
    } finally {
      setIsCalculating(false);
    }
  };

  const handleReset = () => {
    setInputs({
      plotSize: '',
      floors: '1',
      quality: 'smart',
      location: 'delhi',
      buildingType: 'residential',
    });
    setResult(null);
    setError(null);
  };

  const formatCurrency = useCallback((amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  }, []);
  
  // Enhanced form options with descriptions and icons
  const qualityOptions = useMemo(() => [
    {
      value: 'smart',
      label: 'Smart Choice',
      description: '₹1,800/sqft - Standard finishes, reliable quality',
      icon: <CheckCircle className="h-4 w-4" />
    },
    {
      value: 'premium',
      label: 'Premium Selection',
      description: '₹2,500/sqft - Branded materials, superior finish',
      icon: <Sparkles className="h-4 w-4" />
    },
    {
      value: 'luxury',
      label: 'Luxury Collection',
      description: '₹3,500/sqft - International brands, automation',
      icon: <TrendingUp className="h-4 w-4" />
    }
  ], []);
  
  const locationOptions = useMemo(() => [
    { value: 'delhi', label: 'Delhi NCR', description: 'National Capital Region' },
    { value: 'mumbai', label: 'Mumbai', description: 'Financial Capital' },
    { value: 'bangalore', label: 'Bangalore', description: 'Silicon Valley of India' },
    { value: 'chennai', label: 'Chennai', description: 'Detroit of India' },
    { value: 'hyderabad', label: 'Hyderabad', description: 'Cyberabad' },
    { value: 'pune', label: 'Pune', description: 'Oxford of the East' }
  ], []);
  
  const floorsOptions = useMemo(() => [
    { value: '1', label: '1 Floor', description: 'Single story construction' },
    { value: '2', label: '2 Floors', description: 'Double story with staircase' },
    { value: '3', label: '3 Floors', description: 'Triple story construction' },
    { value: '4', label: '4 Floors', description: 'Four story building' }
  ], []);
  
  const buildingTypeOptions = useMemo(() => [
    { value: 'residential', label: 'Residential', description: 'Home/Villa construction' },
    { value: 'commercial', label: 'Commercial', description: 'Office/Shop construction' }
  ], []);

  const generatePDF = () => {
    if (!result) return;

    const doc = new jsPDF();
    const pageWidth = doc.internal.pageSize.width;
    let yPosition = 20;

    // Title
    doc.setFontSize(20);
    doc.text('Construction Cost Report', pageWidth / 2, yPosition, { align: 'center' });
    yPosition += 20;

    // Project details
    doc.setFontSize(16);
    doc.text('Project Details', 20, yPosition);
    yPosition += 10;

    doc.setFontSize(12);
    doc.text(`Plot Size: ${inputs.plotSize} sq ft`, 20, yPosition);
    yPosition += 8;
    doc.text(`Floors: ${inputs.floors}`, 20, yPosition);
    yPosition += 8;
    doc.text(`Quality: ${inputs.quality.charAt(0).toUpperCase() + inputs.quality.slice(1)}`, 20, yPosition);
    yPosition += 8;
    doc.text(`Location: ${inputs.location.charAt(0).toUpperCase() + inputs.location.slice(1)}`, 20, yPosition);
    yPosition += 15;

    // Cost summary
    doc.setFontSize(16);
    doc.text('Cost Summary', 20, yPosition);
    yPosition += 10;

    doc.setFontSize(14);
    doc.text(`Total Cost: ${formatCurrency(result.totalCost)}`, 20, yPosition);
    yPosition += 8;
    doc.text(`Cost per sq ft: ${formatCurrency(result.costPerSqft)}`, 20, yPosition);
    yPosition += 15;

    // Cost breakdown
    doc.setFontSize(16);
    doc.text('Cost Breakdown', 20, yPosition);
    yPosition += 10;

    doc.setFontSize(12);
    doc.text(`Structure: ${formatCurrency(result.breakdown.structure.amount)}`, 20, yPosition);
    yPosition += 8;
    doc.text(`Finishing: ${formatCurrency(result.breakdown.finishing.amount)}`, 20, yPosition);
    yPosition += 8;
    doc.text(`MEP: ${formatCurrency(result.breakdown.mep.amount)}`, 20, yPosition);
    yPosition += 8;
    doc.text(`External: ${formatCurrency(result.breakdown.external.amount)}`, 20, yPosition);
    yPosition += 8;
    doc.text(`Other: ${formatCurrency(result.breakdown.other.amount)}`, 20, yPosition);
    yPosition += 15;

    // Materials list
    if (result.materials && result.materials.length > 0) {
      doc.setFontSize(16);
      doc.text('Materials List', 20, yPosition);
      yPosition += 10;

      doc.setFontSize(12);
      result.materials.slice(0, 10).forEach((material, index) => {
        doc.text(`${material.name}: ${material.quantity} ${material.unit} - ${formatCurrency(material.totalCost)}`, 20, yPosition);
        yPosition += 6;
      });
    }

    // Footer
    doc.setFontSize(10);
    doc.text('Generated by Clarity Engine - Construction Intelligence Platform', pageWidth / 2, 280, { align: 'center' });
    doc.text(`Report generated on ${new Date().toLocaleDateString('en-IN')}`, pageWidth / 2, 290, { align: 'center' });

    // Save the PDF
    doc.save(`construction-report-${new Date().toISOString().split('T')[0]}.pdf`);
  };

  const getStepProgress = useCallback(() => {
    const totalSteps = 3;
    const progress = ((currentStep - 1) / (totalSteps - 1)) * 100;
    return Math.min(progress, 100);
  }, [currentStep]);
  
  const getCurrentStepTitle = useCallback(() => {
    switch (currentStep) {
      case 1: return 'Project Details';
      case 2: return 'Quality & Location';
      case 3: return 'Review & Calculate';
      default: return 'Project Details';
    }
  }, [currentStep]);
  
  const getCurrentStepDescription = useCallback(() => {
    switch (currentStep) {
      case 1: return 'Tell us about your construction project';
      case 2: return 'Choose quality tier and location';
      case 3: return 'Review details and get your estimate';
      default: return 'Tell us about your construction project';
    }
  }, [currentStep]);

  return (
    <motion.div 
      className="max-w-6xl mx-auto space-y-8" 
      data-testid="calculator-container"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Progress Header */}
      <motion.div 
        className="text-center space-y-4"
        variants={stepVariants}
      >
        <EnhancedProgress
          value={getStepProgress()}
          variant="gradient"
          size="lg"
          label={`Step ${currentStep} of 3`}
          showPercentage={false}
          animated
          className="max-w-md mx-auto"
        />
        
        <div>
          <h2 className="heading-3 mb-2">{getCurrentStepTitle()}</h2>
          <p className="body-normal text-secondary-600">{getCurrentStepDescription()}</p>
        </div>
      </motion.div>
      {/* Main Form Container */}
      <EnhancedCard 
        variant="elevated" 
        size="lg"
        data-testid="form-container"
        className="overflow-hidden"
      >
        <AnimatePresence mode="wait">
          {currentStep === 1 && (
            <motion.div
              key="step1"
              variants={stepVariants}
              initial="hidden"
              animate="visible"
              exit="exit"
              transition={{ duration: 0.3 }}
            >
              <CardHeader className="bg-gradient-to-r from-primary-50 to-primary-100/50 border-b">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-primary-100 rounded-lg">
                    <Building2 className="h-5 w-5 text-primary-600" />
                  </div>
                  <div>
                    <CardTitle className="text-xl">Project Basics</CardTitle>
                    <p className="text-sm text-secondary-600 mt-1">Let's start with your project specifications</p>
                  </div>
                </div>
              </CardHeader>
              
              <CardContent className="p-8">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  <div className="space-y-6">
                    <EnhancedInput
                      label="Built-up Area"
                      placeholder="Enter area in sq ft"
                      value={inputs.plotSize}
                      onChange={(e) => handleInputChange('plotSize', e.target.value)}
                      type="number"
                      min="500"
                      max="50000"
                      size="lg"
                      leftIcon={<Building2 className="h-5 w-5" />}
                      helperText="Minimum 500 sq ft, Maximum 50,000 sq ft"
                      errorMessage={inputs.plotSize && (parseFloat(inputs.plotSize) < 500 || parseFloat(inputs.plotSize) > 50000) ? 'Area must be between 500 and 50,000 sq ft' : undefined}
                      data-testid="plot-size-input"
                    />
                    
                    <EnhancedSelect
                      label="Number of Floors"
                      placeholder="Choose floors"
                      options={floorsOptions}
                      value={inputs.floors}
                      onValueChange={(value) => handleInputChange('floors', value)}
                      size="lg"
                      data-testid="floors-select"
                    />
                  </div>
                  
                  <div className="space-y-6">
                    <EnhancedSelect
                      label="Building Type"
                      placeholder="Select type"
                      options={buildingTypeOptions}
                      value={inputs.buildingType}
                      onValueChange={(value) => handleInputChange('buildingType', value)}
                      size="lg"
                      data-testid="building-type-select"
                    />
                    
                    {/* Info Card */}
                    <div className="bg-info-50 border border-info-200 rounded-lg p-4">
                      <div className="flex items-start gap-3">
                        <Info className="h-5 w-5 text-info-600 mt-0.5 flex-shrink-0" />
                        <div>
                          <h4 className="font-medium text-info-900 mb-1">Why we need this?</h4>
                          <p className="text-sm text-info-700">
                            These details help us calculate accurate material requirements, 
                            structural specifications, and labor costs specific to your project type.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </motion.div>
          )}
          
          {currentStep === 2 && (
            <motion.div
              key="step2"
              variants={stepVariants}
              initial="hidden"
              animate="visible"
              exit="exit"
              transition={{ duration: 0.3 }}
            >
              <CardHeader className="bg-gradient-to-r from-primary-50 to-primary-100/50 border-b">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-primary-100 rounded-lg">
                    <Sparkles className="h-5 w-5 text-primary-600" />
                  </div>
                  <div>
                    <CardTitle className="text-xl">Quality & Location</CardTitle>
                    <p className="text-sm text-secondary-600 mt-1">Choose your preferred quality tier and location</p>
                  </div>
                </div>
              </CardHeader>
              
              <CardContent className="p-8">
                <div className="space-y-8">
                  <div>
                    <h3 className="heading-6 mb-4 flex items-center gap-2">
                      <Sparkles className="h-5 w-5 text-primary-600" />
                      Quality Tier
                    </h3>
                    <EnhancedSelect
                      placeholder="Choose quality level"
                      options={qualityOptions}
                      value={inputs.quality}
                      onValueChange={(value) => handleInputChange('quality', value)}
                      size="lg"
                      data-testid="quality-select"
                    />
                  </div>
                  
                  <div>
                    <h3 className="heading-6 mb-4 flex items-center gap-2">
                      <MapPin className="h-5 w-5 text-primary-600" />
                      Construction Location
                    </h3>
                    <EnhancedSelect
                      placeholder="Select your city"
                      options={locationOptions}
                      value={inputs.location}
                      onValueChange={(value) => handleInputChange('location', value)}
                      size="lg"
                      searchable
                      data-testid="location-select"
                    />
                  </div>
                  
                  {/* Quality Comparison */}
                  <div className="bg-secondary-50 rounded-xl p-6">
                    <h4 className="font-medium text-secondary-900 mb-4">Quality Tier Comparison</h4>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      {qualityOptions.map((option, index) => (
                        <div 
                          key={option.value}
                          className={`p-4 rounded-lg border-2 transition-all ${
                            inputs.quality === option.value 
                              ? 'border-primary-300 bg-primary-50' 
                              : 'border-secondary-200 bg-white'
                          }`}
                        >
                          <div className="flex items-center gap-2 mb-2">
                            {option.icon}
                            <span className="font-medium">{option.label}</span>
                          </div>
                          <p className="text-sm text-secondary-600">{option.description}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </motion.div>
          )}
          
          {currentStep === 3 && (
            <motion.div
              key="step3"
              variants={stepVariants}
              initial="hidden"
              animate="visible"
              exit="exit"
              transition={{ duration: 0.3 }}
            >
              <CardHeader className="bg-gradient-to-r from-primary-50 to-primary-100/50 border-b">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-primary-100 rounded-lg">
                    <Calculator className="h-5 w-5 text-primary-600" />
                  </div>
                  <div>
                    <CardTitle className="text-xl">Review & Calculate</CardTitle>
                    <p className="text-sm text-secondary-600 mt-1">Review your inputs and get your estimate</p>
                  </div>
                </div>
              </CardHeader>
              
              <CardContent className="p-8">
                {/* Summary Cards */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
                  <div className="bg-gradient-to-br from-primary-50 to-primary-100 p-4 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <Building2 className="h-4 w-4 text-primary-600" />
                      <span className="text-sm font-medium text-primary-900">Area</span>
                    </div>
                    <p className="text-lg font-bold text-primary-900">{inputs.plotSize} sq ft</p>
                  </div>
                  
                  <div className="bg-gradient-to-br from-secondary-50 to-secondary-100 p-4 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <Layers3 className="h-4 w-4 text-secondary-600" />
                      <span className="text-sm font-medium text-secondary-900">Floors</span>
                    </div>
                    <p className="text-lg font-bold text-secondary-900">{inputs.floors} Floor{inputs.floors !== '1' ? 's' : ''}</p>
                  </div>
                  
                  <div className="bg-gradient-to-br from-success-50 to-success-100 p-4 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <Sparkles className="h-4 w-4 text-success-600" />
                      <span className="text-sm font-medium text-success-900">Quality</span>
                    </div>
                    <p className="text-lg font-bold text-success-900 capitalize">{inputs.quality}</p>
                  </div>
                  
                  <div className="bg-gradient-to-br from-warning-50 to-warning-100 p-4 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <MapPin className="h-4 w-4 text-warning-600" />
                      <span className="text-sm font-medium text-warning-900">Location</span>
                    </div>
                    <p className="text-lg font-bold text-warning-900 capitalize">{inputs.location}</p>
                  </div>
                </div>
                
                {error && (
                  <motion.div 
                    className="bg-error-50 border border-error-200 rounded-lg p-4 mb-6"
                    data-testid="error-message"
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                  >
                    <div className="flex items-start gap-3">
                      <AlertCircle className="h-5 w-5 text-error-600 mt-0.5 flex-shrink-0" />
                      <div>
                        <h4 className="font-medium text-error-900 mb-1">Calculation Error</h4>
                        <p className="text-sm text-error-700" data-testid="validation-error">{error}</p>
                      </div>
                    </div>
                  </motion.div>
                )}
                
                <div className="flex gap-4">
                  <EnhancedButton
                    onClick={handleCalculate}
                    disabled={isCalculating || !isFormValid}
                    loading={isCalculating}
                    size="lg"
                    variant="primary"
                    leftIcon={!isCalculating ? <Zap className="h-5 w-5" /> : undefined}
                    fullWidth
                    data-testid="calculate-button"
                  >
                    {isCalculating ? 'Calculating Your Estimate...' : 'Calculate Construction Cost'}
                  </EnhancedButton>
                  
                  <EnhancedButton
                    onClick={handleReset}
                    variant="outline"
                    size="lg"
                    leftIcon={<RefreshCw className="h-5 w-5" />}
                    data-testid="reset-button"
                  >
                    Reset
                  </EnhancedButton>
                </div>
                
                {result && (
                  <div className="flex gap-4 mt-6" data-testid="action-buttons">
                    <EnhancedButton
                      onClick={() => console.log('Save functionality not implemented')}
                      variant="secondary"
                      size="lg"
                      leftIcon={<Save className="h-5 w-5" />}
                      fullWidth
                      data-testid="save-button"
                    >
                      Save Project
                    </EnhancedButton>
                    <EnhancedButton
                      onClick={generatePDF}
                      variant="outline"
                      size="lg"
                      leftIcon={<Download className="h-5 w-5" />}
                      fullWidth
                      disabled={!result}
                      data-testid="download-pdf-button"
                    >
                      Download PDF Report
                    </EnhancedButton>
                  </div>
                )}
              </CardContent>
            </motion.div>
          )}
        </AnimatePresence>
        
        {/* Navigation Footer */}
        <CardFooter className="bg-secondary-50/50 border-t">
          <div className="flex justify-between items-center w-full">
            <EnhancedButton
              onClick={prevStep}
              variant="ghost"
              size="md"
              leftIcon={<ArrowLeft className="h-4 w-4" />}
              disabled={currentStep === 1}
            >
              Previous
            </EnhancedButton>
            
            <div className="flex items-center gap-2">
              {[1, 2, 3].map((step) => (
                <div
                  key={step}
                  className={`w-3 h-3 rounded-full transition-all ${
                    step === currentStep
                      ? 'bg-primary-600 scale-110'
                      : completedSteps.has(step)
                      ? 'bg-success-500'
                      : 'bg-secondary-300'
                  }`}
                />
              ))}
            </div>
            
            <EnhancedButton
              onClick={nextStep}
              variant={currentStep === 3 ? "ghost" : "primary"}
              size="md"
              rightIcon={currentStep < 3 ? <ArrowRight className="h-4 w-4" /> : undefined}
              disabled={currentStep === 3 || (currentStep === 1 && !inputs.plotSize)}
            >
              {currentStep === 3 ? 'Complete' : 'Next'}
            </EnhancedButton>
          </div>
        </CardFooter>
      </EnhancedCard>

      {result && (
        <motion.div
          variants={resultVariants}
          initial="hidden"
          animate="visible"
          className="space-y-8"
        >
          {/* Main Results Card */}
          <EnhancedCard 
            variant="gradient" 
            size="lg"
            data-testid="results-container"
            className="overflow-hidden"
          >
            <CardHeader className="text-center pb-6">
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.2, type: 'spring', stiffness: 100 }}
                className="inline-flex items-center justify-center w-16 h-16 bg-success-100 rounded-full mx-auto mb-4"
              >
                <CheckCircle className="h-8 w-8 text-success-600" />
              </motion.div>
              <CardTitle className="heading-2 text-gradient-primary" data-testid="results-title">
                Your Construction Estimate
              </CardTitle>
              <p className="body-normal text-secondary-600 mt-2">
                Based on current market rates and Indian construction standards
              </p>
            </CardHeader>
            
            <CardContent className="space-y-8">
              {/* Cost Summary Cards */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6" data-testid="cost-summary">
                <motion.div
                  className="bg-white/90 backdrop-blur-sm rounded-xl p-6 text-center shadow-lg"
                  whileHover={{ scale: 1.02, y: -2 }}
                  transition={{ duration: 0.2 }}
                >
                  <div className="inline-flex items-center justify-center w-12 h-12 bg-success-100 rounded-lg mb-4">
                    <TrendingUp className="h-6 w-6 text-success-600" />
                  </div>
                  <p className="body-small text-secondary-600 mb-2">Total Construction Cost</p>
                  <p className="heading-2 text-success-600" data-testid="total-cost">
                    {formatCurrency(result.totalCost)}
                  </p>
                </motion.div>
                
                <motion.div
                  className="bg-white/90 backdrop-blur-sm rounded-xl p-6 text-center shadow-lg"
                  whileHover={{ scale: 1.02, y: -2 }}
                  transition={{ duration: 0.2 }}
                >
                  <div className="inline-flex items-center justify-center w-12 h-12 bg-primary-100 rounded-lg mb-4">
                    <Calculator className="h-6 w-6 text-primary-600" />
                  </div>
                  <p className="body-small text-secondary-600 mb-2">Cost per Square Foot</p>
                  <p className="heading-3 text-primary-600" data-testid="cost-per-sqft">
                    {formatCurrency(result.costPerSqft)}<span className="text-lg">/sqft</span>
                  </p>
                </motion.div>
              </div>
              
              {/* Detailed Breakdown */}
              <div className="bg-white/90 backdrop-blur-sm rounded-xl p-6">
                <h4 className="heading-5 mb-6 flex items-center gap-2" data-testid="breakdown-title">
                  <FileText className="h-5 w-5 text-primary-600" />
                  Detailed Cost Breakdown
                </h4>
                
                <div className="space-y-4" data-testid="cost-breakdown">
                  {[
                    { key: 'structure', label: 'Structure & RCC', percentage: '35%', color: 'bg-blue-500' },
                    { key: 'finishing', label: 'Finishing Work', percentage: '30%', color: 'bg-green-500' },
                    { key: 'mep', label: 'MEP Work', percentage: '20%', color: 'bg-yellow-500' },
                    { key: 'external', label: 'External Works', percentage: '10%', color: 'bg-purple-500' },
                    { key: 'other', label: 'Other Costs', percentage: '5%', color: 'bg-red-500' }
                  ].map((item, index) => (
                    <motion.div 
                      key={item.key}
                      className="flex items-center justify-between p-4 bg-secondary-50 rounded-lg"
                      data-testid={`breakdown-${item.key}`}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                    >
                      <div className="flex items-center gap-3">
                        <div className={`w-4 h-4 rounded ${item.color}`} />
                        <span className="font-medium text-secondary-900">
                          {item.label} ({item.percentage})
                        </span>
                      </div>
                      <span 
                        className="heading-6 text-secondary-900" 
                        data-testid={`${item.key}-cost`}
                      >
                        {formatCurrency(result.breakdown[item.key as keyof typeof result.breakdown].amount)}
                      </span>
                    </motion.div>
                  ))}
                </div>
              </div>
              
              {/* Trust Indicators */}
              <div className="bg-info-50 border border-info-200 rounded-xl p-4" data-testid="estimate-disclaimer">
                <div className="flex items-start gap-3">
                  <Shield className="h-5 w-5 text-info-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="font-medium text-info-900 mb-1">Estimate Accuracy</h4>
                    <p className="body-small text-info-700">
                      This estimate is based on current market rates, IS code standards, and includes 
                      quality-specific material specifications. Actual costs may vary ±10% based on 
                      site conditions and material availability.
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </EnhancedCard>

          {/* Materials List */}
          <EnhancedCard 
            variant="outlined" 
            size="lg"
            data-testid="materials-list"
          >
            <CardHeader>
              <div className="flex items-center gap-3">
                <div className="p-2 bg-primary-100 rounded-lg">
                  <FileText className="h-5 w-5 text-primary-600" />
                </div>
                <div>
                  <CardTitle className="heading-4" data-testid="materials-title">Materials Breakdown</CardTitle>
                  <p className="body-small text-secondary-600 mt-1">Detailed material requirements for your project</p>
                </div>
              </div>
            </CardHeader>
            
            <CardContent>
              {result.materials && result.materials.length > 0 ? (
                <div className="space-y-4" data-testid="materials-content">
                  {result.materials.map((material, index) => (
                    <motion.div 
                      key={index} 
                      className="bg-secondary-50 rounded-lg p-4 hover:bg-secondary-100 transition-colors"
                      data-testid={`material-item-${index}`}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.05 }}
                      whileHover={{ scale: 1.01 }}
                    >
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <h4 className="font-semibold text-secondary-900 mb-1" data-testid={`material-name-${index}`}>
                            {material.name}
                          </h4>
                          <p className="body-small text-secondary-600" data-testid={`material-quantity-${index}`}>
                            Quantity: {material.quantity} {material.unit}
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="heading-6 text-success-600" data-testid={`material-cost-${index}`}>
                            {formatCurrency(material.totalCost)}
                          </p>
                          <p className="caption text-secondary-500">
                            Total Cost
                          </p>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12" data-testid="materials-empty">
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-secondary-100 rounded-full mb-4">
                    <FileText className="h-8 w-8 text-secondary-400" />
                  </div>
                  <p className="body-normal text-secondary-500">Materials breakdown will appear here after calculation</p>
                </div>
              )}
            </CardContent>
          </EnhancedCard>

          {/* Timeline */}
          <EnhancedCard 
            variant="elevated" 
            size="lg"
            data-testid="timeline"
          >
            <CardHeader>
              <div className="flex items-center gap-3">
                <div className="p-2 bg-primary-100 rounded-lg">
                  <Clock className="h-5 w-5 text-primary-600" />
                </div>
                <div>
                  <CardTitle className="heading-4" data-testid="timeline-title">Construction Timeline</CardTitle>
                  <p className="body-small text-secondary-600 mt-1">Project phases and estimated duration</p>
                </div>
              </div>
            </CardHeader>
            
            <CardContent>
              {result.timeline && result.timeline.length > 0 ? (
                <div className="space-y-6" data-testid="timeline-content">
                  {result.timeline.map((phase, index) => (
                    <motion.div 
                      key={index} 
                      className="relative"
                      data-testid={`timeline-phase-${index}`}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                    >
                      <div className="flex items-start gap-4">
                        {/* Timeline Connector */}
                        <div className="flex flex-col items-center">
                          <div className={`w-4 h-4 rounded-full ${
                            index === 0 ? 'bg-success-500' : 
                            index <= 3 ? 'bg-primary-500' : 'bg-secondary-300'
                          }`} />
                          {index < result.timeline!.length - 1 && (
                            <div className="w-0.5 h-12 bg-secondary-200 mt-2" />
                          )}
                        </div>
                        
                        {/* Phase Content */}
                        <div className="flex-1 bg-secondary-50 rounded-lg p-4">
                          <div className="flex justify-between items-start mb-2">
                            <h4 className="heading-6 text-secondary-900" data-testid={`phase-name-${index}`}>
                              {phase.name}
                            </h4>
                            <div className="text-right">
                              <span className="inline-flex items-center gap-1 bg-primary-100 text-primary-700 px-2 py-1 rounded text-xs font-medium">
                                <Clock className="h-3 w-3" />
                                Week {phase.startAfter + 1}
                              </span>
                            </div>
                          </div>
                          <p className="body-small text-secondary-600" data-testid={`phase-duration-${index}`}>
                            Duration: {phase.duration} days
                          </p>
                          
                          {/* Progress Bar */}
                          <div className="mt-3">
                            <EnhancedProgress
                              value={index === 0 ? 100 : index <= 2 ? 60 : 0}
                              variant={index === 0 ? 'success' : index <= 2 ? 'default' : 'default'}
                              size="sm"
                              showPercentage={false}
                            />
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                  
                  {/* Timeline Summary */}
                  <div className="bg-info-50 border border-info-200 rounded-lg p-4 mt-6">
                    <div className="flex items-start gap-3">
                      <Info className="h-5 w-5 text-info-600 mt-0.5 flex-shrink-0" />
                      <div>
                        <h4 className="font-medium text-info-900 mb-1">Estimated Project Duration</h4>
                        <p className="body-small text-info-700">
                          Total estimated time: {Math.max(...result.timeline.map(p => p.startAfter + p.duration))} days
                          ({Math.ceil(Math.max(...result.timeline.map(p => p.startAfter + p.duration)) / 7)} weeks)
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-12" data-testid="timeline-empty">
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-secondary-100 rounded-full mb-4">
                    <Clock className="h-8 w-8 text-secondary-400" />
                  </div>
                  <p className="body-normal text-secondary-500">Construction timeline will appear here after calculation</p>
                </div>
              )}
            </CardContent>
          </EnhancedCard>
        </motion.div>
      )}
    </motion.div>
  );
}