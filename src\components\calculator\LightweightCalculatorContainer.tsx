'use client';

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import jsPDF from 'jspdf';

interface CalculationInputs {
  plotSize: string;
  floors: string;
  quality: string;
  location: string;
  buildingType: string;
}

interface CalculationResult {
  totalCost: number;
  costPerSqft: number;
  breakdown: {
    structure: { amount: number };
    finishing: { amount: number };
    mep: { amount: number };
    external: { amount: number };
    other: { amount: number };
  };
  materials?: Array<{
    name: string;
    quantity: number;
    unit: string;
    totalCost: number;
  }>;
  timeline?: Array<{
    name: string;
    duration: number;
    startAfter: number;
  }>;
}

export function LightweightCalculatorContainer() {
  const [inputs, setInputs] = useState<CalculationInputs>({
    plotSize: '',
    floors: '1',
    quality: 'smart',
    location: 'delhi',
    buildingType: 'residential',
  });

  const [result, setResult] = useState<CalculationResult | null>(null);
  const [isCalculating, setIsCalculating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleInputChange = (field: keyof CalculationInputs, value: string) => {
    setInputs(prev => ({ ...prev, [field]: value }));
    setError(null);
  };

  const handleCalculate = async () => {
    if (!inputs.plotSize || parseFloat(inputs.plotSize) < 500) {
      setError('Plot size must be at least 500 sq ft');
      return;
    }

    setIsCalculating(true);
    setError(null);

    try {
      const response = await fetch('/api/calculate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          builtUpArea: parseFloat(inputs.plotSize),
          floors: parseInt(inputs.floors),
          qualityTier: inputs.quality,
          location: inputs.location,
          buildingType: inputs.buildingType,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      if (data.success) {
        const resultData = data.data;
        
        // Add mock materials list if not provided by API
        if (!resultData.materials || resultData.materials.length === 0) {
          const totalCost = resultData.totalCost || 0;
          resultData.materials = [
            {
              name: 'Cement (OPC 53 Grade)',
              quantity: Math.round((totalCost / 3500) * 0.08),
              unit: 'bags',
              totalCost: Math.round(totalCost * 0.12)
            },
            {
              name: 'TMT Steel Bars (Fe 500)',
              quantity: Math.round((totalCost / 3500) * 0.06),
              unit: 'tonnes',
              totalCost: Math.round(totalCost * 0.15)
            },
            {
              name: 'Concrete Blocks',
              quantity: Math.round((totalCost / 3500) * 0.8),
              unit: 'pieces',
              totalCost: Math.round(totalCost * 0.08)
            },
            {
              name: 'River Sand',
              quantity: Math.round((totalCost / 3500) * 0.12),
              unit: 'tonnes',
              totalCost: Math.round(totalCost * 0.06)
            },
            {
              name: 'Aggregates (20mm)',
              quantity: Math.round((totalCost / 3500) * 0.15),
              unit: 'tonnes',
              totalCost: Math.round(totalCost * 0.05)
            },
            {
              name: 'Tiles (Vitrified)',
              quantity: Math.round(parseFloat(inputs.plotSize) * 0.8),
              unit: 'sq ft',
              totalCost: Math.round(totalCost * 0.08)
            },
            {
              name: 'Paint (Interior)',
              quantity: Math.round(parseFloat(inputs.plotSize) * 1.2),
              unit: 'sq ft',
              totalCost: Math.round(totalCost * 0.04)
            },
            {
              name: 'Electrical Fittings',
              quantity: Math.round(parseFloat(inputs.plotSize) / 100),
              unit: 'set',
              totalCost: Math.round(totalCost * 0.06)
            }
          ];
        }

        // Add mock timeline if not provided by API
        if (!resultData.timeline || resultData.timeline.length === 0) {
          resultData.timeline = [
            {
              name: 'Foundation & Plinth',
              duration: 30,
              startAfter: 0
            },
            {
              name: 'Superstructure',
              duration: 60,
              startAfter: 30
            },
            {
              name: 'Roofing & Slab',
              duration: 45,
              startAfter: 90
            },
            {
              name: 'Masonry & Plastering',
              duration: 40,
              startAfter: 135
            },
            {
              name: 'Flooring & Tiling',
              duration: 30,
              startAfter: 175
            },
            {
              name: 'Electrical & Plumbing',
              duration: 35,
              startAfter: 175
            },
            {
              name: 'Finishing & Painting',
              duration: 25,
              startAfter: 210
            },
            {
              name: 'Final Touches',
              duration: 15,
              startAfter: 235
            }
          ];
        }

        setResult(resultData);
      } else {
        throw new Error(data.error?.message || 'Calculation failed');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Calculation failed');
    } finally {
      setIsCalculating(false);
    }
  };

  const handleReset = () => {
    setInputs({
      plotSize: '',
      floors: '1',
      quality: 'smart',
      location: 'delhi',
      buildingType: 'residential',
    });
    setResult(null);
    setError(null);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const generatePDF = () => {
    if (!result) return;

    const doc = new jsPDF();
    const pageWidth = doc.internal.pageSize.width;
    let yPosition = 20;

    // Title
    doc.setFontSize(20);
    doc.text('Construction Cost Report', pageWidth / 2, yPosition, { align: 'center' });
    yPosition += 20;

    // Project details
    doc.setFontSize(16);
    doc.text('Project Details', 20, yPosition);
    yPosition += 10;

    doc.setFontSize(12);
    doc.text(`Plot Size: ${inputs.plotSize} sq ft`, 20, yPosition);
    yPosition += 8;
    doc.text(`Floors: ${inputs.floors}`, 20, yPosition);
    yPosition += 8;
    doc.text(`Quality: ${inputs.quality.charAt(0).toUpperCase() + inputs.quality.slice(1)}`, 20, yPosition);
    yPosition += 8;
    doc.text(`Location: ${inputs.location.charAt(0).toUpperCase() + inputs.location.slice(1)}`, 20, yPosition);
    yPosition += 15;

    // Cost summary
    doc.setFontSize(16);
    doc.text('Cost Summary', 20, yPosition);
    yPosition += 10;

    doc.setFontSize(14);
    doc.text(`Total Cost: ${formatCurrency(result.totalCost)}`, 20, yPosition);
    yPosition += 8;
    doc.text(`Cost per sq ft: ${formatCurrency(result.costPerSqft)}`, 20, yPosition);
    yPosition += 15;

    // Cost breakdown
    doc.setFontSize(16);
    doc.text('Cost Breakdown', 20, yPosition);
    yPosition += 10;

    doc.setFontSize(12);
    doc.text(`Structure: ${formatCurrency(result.breakdown.structure.amount)}`, 20, yPosition);
    yPosition += 8;
    doc.text(`Finishing: ${formatCurrency(result.breakdown.finishing.amount)}`, 20, yPosition);
    yPosition += 8;
    doc.text(`MEP: ${formatCurrency(result.breakdown.mep.amount)}`, 20, yPosition);
    yPosition += 8;
    doc.text(`External: ${formatCurrency(result.breakdown.external.amount)}`, 20, yPosition);
    yPosition += 8;
    doc.text(`Other: ${formatCurrency(result.breakdown.other.amount)}`, 20, yPosition);
    yPosition += 15;

    // Materials list
    if (result.materials && result.materials.length > 0) {
      doc.setFontSize(16);
      doc.text('Materials List', 20, yPosition);
      yPosition += 10;

      doc.setFontSize(12);
      result.materials.slice(0, 10).forEach((material, index) => {
        doc.text(`${material.name}: ${material.quantity} ${material.unit} - ${formatCurrency(material.totalCost)}`, 20, yPosition);
        yPosition += 6;
      });
    }

    // Footer
    doc.setFontSize(10);
    doc.text('Generated by Clarity Engine - Construction Intelligence Platform', pageWidth / 2, 280, { align: 'center' });
    doc.text(`Report generated on ${new Date().toLocaleDateString('en-IN')}`, pageWidth / 2, 290, { align: 'center' });

    // Save the PDF
    doc.save(`construction-report-${new Date().toISOString().split('T')[0]}.pdf`);
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6" data-testid="calculator-container">
      <Card data-testid="form-container">
        <CardHeader>
          <CardTitle data-testid="form-title">Project Details</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="plotSize">Plot Size (sq ft)</Label>
              <Input
                id="plotSize"
                data-testid="plot-size-input"
                type="number"
                placeholder="Enter plot size"
                value={inputs.plotSize}
                onChange={(e) => handleInputChange('plotSize', e.target.value)}
                min="500"
                max="50000"
                aria-label="Plot size in square feet"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="floors">Number of Floors</Label>
              <Select
                value={inputs.floors}
                onValueChange={(value) => handleInputChange('floors', value)}
              >
                <SelectTrigger id="floors" data-testid="floors-select" aria-label="Number of floors">
                  <SelectValue placeholder="Select floors" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1" data-testid="floors-1">1 Floor</SelectItem>
                  <SelectItem value="2" data-testid="floors-2">2 Floors</SelectItem>
                  <SelectItem value="3" data-testid="floors-3">3 Floors</SelectItem>
                  <SelectItem value="4" data-testid="floors-4">4 Floors</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="quality">Quality Tier</Label>
              <Select
                value={inputs.quality}
                onValueChange={(value) => handleInputChange('quality', value)}
              >
                <SelectTrigger id="quality" data-testid="quality-select" aria-label="Quality tier selection">
                  <SelectValue placeholder="Select quality" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="smart" data-testid="quality-smart">Smart Choice (₹1,800/sqft)</SelectItem>
                  <SelectItem value="premium" data-testid="quality-premium">Premium Selection (₹2,500/sqft)</SelectItem>
                  <SelectItem value="luxury" data-testid="quality-luxury">Luxury Collection (₹3,500/sqft)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="location">Location</Label>
              <Select
                value={inputs.location}
                onValueChange={(value) => handleInputChange('location', value)}
              >
                <SelectTrigger id="location" data-testid="location-select" aria-label="Location selection">
                  <SelectValue placeholder="Select location" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="delhi" data-testid="location-delhi">Delhi NCR</SelectItem>
                  <SelectItem value="mumbai" data-testid="location-mumbai">Mumbai</SelectItem>
                  <SelectItem value="bangalore" data-testid="location-bangalore">Bangalore</SelectItem>
                  <SelectItem value="chennai" data-testid="location-chennai">Chennai</SelectItem>
                  <SelectItem value="hyderabad" data-testid="location-hyderabad">Hyderabad</SelectItem>
                  <SelectItem value="pune" data-testid="location-pune">Pune</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="buildingType">Building Type</Label>
              <Select
                value={inputs.buildingType}
                onValueChange={(value) => handleInputChange('buildingType', value)}
              >
                <SelectTrigger id="buildingType" data-testid="building-type-select" aria-label="Building type selection">
                  <SelectValue placeholder="Select building type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="residential" data-testid="building-type-residential">Residential</SelectItem>
                  <SelectItem value="commercial" data-testid="building-type-commercial">Commercial</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-3" data-testid="error-message">
              <p className="text-sm text-red-600" data-testid="validation-error">{error}</p>
            </div>
          )}

          <div className="flex gap-3">
            <Button 
              onClick={handleCalculate} 
              disabled={isCalculating || !inputs.plotSize}
              data-testid="calculate-button"
              className="flex-1"
              aria-label="Calculate construction cost"
            >
              {isCalculating ? (
                <>
                  <span data-testid="loading-spinner" className="inline-block animate-spin mr-2">⚪</span>
                  Calculating...
                </>
              ) : (
                'Calculate Construction Cost'
              )}
            </Button>
            <Button 
              onClick={handleReset} 
              variant="outline"
              data-testid="reset-button"
              aria-label="Reset form"
            >
              Reset
            </Button>
          </div>

          {result && (
            <div className="flex gap-3 mt-4" data-testid="action-buttons">
              <Button
                onClick={() => console.log('Save functionality not implemented')}
                variant="outline"
                data-testid="save-button"
                className="flex-1"
                aria-label="Save project"
              >
                Save Project
              </Button>
              <Button
                onClick={generatePDF}
                variant="outline"
                data-testid="download-pdf-button"
                className="flex-1"
                aria-label="Download PDF report"
                disabled={!result}
              >
                Download PDF
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {result && (
        <>
          <Card data-testid="results-container">
            <CardHeader>
              <CardTitle data-testid="results-title">Construction Cost Estimate</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4" data-testid="cost-summary">
                <div>
                  <p className="text-sm text-gray-600">Total Cost</p>
                  <p className="text-2xl font-bold text-green-600" data-testid="total-cost">
                    {formatCurrency(result.totalCost)}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Cost per sq ft</p>
                  <p className="text-xl font-semibold" data-testid="cost-per-sqft">
                    {formatCurrency(result.costPerSqft)}/sqft
                  </p>
                </div>
              </div>

              <div className="space-y-2">
                <h4 className="font-semibold text-gray-900" data-testid="breakdown-title">Cost Breakdown</h4>
                <div className="space-y-2" data-testid="cost-breakdown">
                  <div className="flex justify-between" data-testid="breakdown-structure">
                    <span>Structure & RCC (35%)</span>
                    <span data-testid="structure-cost">{formatCurrency(result.breakdown.structure.amount)}</span>
                  </div>
                  <div className="flex justify-between" data-testid="breakdown-finishing">
                    <span>Finishing Work (30%)</span>
                    <span data-testid="finishing-cost">{formatCurrency(result.breakdown.finishing.amount)}</span>
                  </div>
                  <div className="flex justify-between" data-testid="breakdown-mep">
                    <span>MEP Work (20%)</span>
                    <span data-testid="mep-cost">{formatCurrency(result.breakdown.mep.amount)}</span>
                  </div>
                  <div className="flex justify-between" data-testid="breakdown-external">
                    <span>External Works (10%)</span>
                    <span data-testid="external-cost">{formatCurrency(result.breakdown.external.amount)}</span>
                  </div>
                  <div className="flex justify-between" data-testid="breakdown-other">
                    <span>Other Costs (5%)</span>
                    <span data-testid="other-cost">{formatCurrency(result.breakdown.other.amount)}</span>
                  </div>
                </div>
              </div>

              <div className="pt-4 border-t" data-testid="estimate-disclaimer">
                <p className="text-sm text-gray-600">
                  * Estimate based on current market rates and Indian construction standards
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Materials List */}
          <Card data-testid="materials-list">
            <CardHeader>
              <CardTitle data-testid="materials-title">Materials List</CardTitle>
            </CardHeader>
            <CardContent>
              {result.materials && result.materials.length > 0 ? (
                <div className="space-y-3" data-testid="materials-content">
                  {result.materials.map((material, index) => (
                    <div key={index} className="flex justify-between items-center py-2 border-b" data-testid={`material-item-${index}`}>
                      <div>
                        <p className="font-medium" data-testid={`material-name-${index}`}>{material.name}</p>
                        <p className="text-sm text-gray-600" data-testid={`material-quantity-${index}`}>{material.quantity} {material.unit}</p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium" data-testid={`material-cost-${index}`}>{formatCurrency(material.totalCost)}</p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8" data-testid="materials-empty">
                  <p className="text-gray-500">Materials list will be displayed here after calculation</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Timeline */}
          <Card data-testid="timeline">
            <CardHeader>
              <CardTitle data-testid="timeline-title">Construction Timeline</CardTitle>
            </CardHeader>
            <CardContent>
              {result.timeline && result.timeline.length > 0 ? (
                <div className="space-y-3" data-testid="timeline-content">
                  {result.timeline.map((phase, index) => (
                    <div key={index} className="flex justify-between items-center py-2 border-b" data-testid={`timeline-phase-${index}`}>
                      <div>
                        <p className="font-medium" data-testid={`phase-name-${index}`}>{phase.name}</p>
                        <p className="text-sm text-gray-600" data-testid={`phase-duration-${index}`}>Duration: {phase.duration} weeks</p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm text-gray-600" data-testid={`phase-start-${index}`}>Week {phase.startAfter + 1}</p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8" data-testid="timeline-empty">
                  <p className="text-gray-500">Construction timeline will be displayed here after calculation</p>
                </div>
              )}
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
}