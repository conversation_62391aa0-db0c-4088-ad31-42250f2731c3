/**
 * Performance monitoring utilities for bundle size analysis
 * Tracks core web vitals and bundle loading performance
 */

interface PerformanceMetrics {
  bundleSize: number;
  loadTime: number;
  firstContentfulPaint: number;
  largestContentfulPaint: number;
  firstInputDelay: number;
  cumulativeLayoutShift: number;
  timeToInteractive: number;
  jsHeapSizeLimit: number;
  jsHeapSizeUsed: number;
  jsHeapSizeFree: number;
}

interface BundleAnalysis {
  totalSize: number;
  gzippedSize: number;
  chunks: {
    name: string;
    size: number;
    modules: string[];
  }[];
  loadTimes: {
    [key: string]: number;
  };
}

class PerformanceMonitor {
  private metrics: Partial<PerformanceMetrics> = {};
  private bundleAnalysis: Partial<BundleAnalysis> = {};
  private observers: PerformanceObserver[] = [];

  constructor() {
    if (typeof window !== 'undefined') {
      this.initializeObservers();
      this.measureInitialLoad();
    }
  }

  private initializeObservers() {
    // Core Web Vitals observer
    if ('PerformanceObserver' in window) {
      // First Contentful Paint
      const fcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          if (entry.name === 'first-contentful-paint') {
            this.metrics.firstContentfulPaint = entry.startTime;
          }
        });
      });
      fcpObserver.observe({ entryTypes: ['paint'] });
      this.observers.push(fcpObserver);

      // Largest Contentful Paint
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        this.metrics.largestContentfulPaint = lastEntry.startTime;
      });
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
      this.observers.push(lcpObserver);

      // First Input Delay
      const fidObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          this.metrics.firstInputDelay = (entry as any).processingStart - entry.startTime;
        });
      });
      fidObserver.observe({ entryTypes: ['first-input'] });
      this.observers.push(fidObserver);

      // Cumulative Layout Shift
      const clsObserver = new PerformanceObserver((list) => {
        let clsValue = 0;
        const entries = list.getEntries();
        entries.forEach((entry) => {
          if (!(entry as any).hadRecentInput) {
            clsValue += (entry as any).value;
          }
        });
        this.metrics.cumulativeLayoutShift = clsValue;
      });
      clsObserver.observe({ entryTypes: ['layout-shift'] });
      this.observers.push(clsObserver);

      // Resource loading times
      const resourceObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          if (entry.name.includes('.js') || entry.name.includes('.css')) {
            this.bundleAnalysis.loadTimes = {
              ...this.bundleAnalysis.loadTimes,
              [entry.name]: entry.duration,
            };
          }
        });
      });
      resourceObserver.observe({ entryTypes: ['resource'] });
      this.observers.push(resourceObserver);
    }
  }

  private measureInitialLoad() {
    window.addEventListener('load', () => {
      this.metrics.loadTime = performance.now();
      this.measureMemoryUsage();
      this.analyzeBundle();
    });
  }

  private measureMemoryUsage() {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      this.metrics.jsHeapSizeLimit = memory.jsHeapSizeLimit;
      this.metrics.jsHeapSizeUsed = memory.usedJSHeapSize;
      this.metrics.jsHeapSizeFree = memory.jsHeapSizeLimit - memory.usedJSHeapSize;
    }
  }

  private analyzeBundle() {
    const resources = performance.getEntriesByType('resource');
    let totalSize = 0;
    const chunks: { name: string; size: number; modules: string[] }[] = [];

    resources.forEach((resource) => {
      if (resource.name.includes('.js')) {
        const size = (resource as any).transferSize || 0;
        totalSize += size;
        
        chunks.push({
          name: resource.name.split('/').pop() || 'unknown',
          size,
          modules: [], // This would need to be populated by webpack stats
        });
      }
    });

    this.bundleAnalysis = {
      totalSize,
      gzippedSize: totalSize * 0.7, // Approximate gzip compression
      chunks,
      loadTimes: this.bundleAnalysis.loadTimes || {},
    };
  }

  // Public methods
  public getMetrics(): Partial<PerformanceMetrics> {
    return { ...this.metrics };
  }

  public getBundleAnalysis(): Partial<BundleAnalysis> {
    return { ...this.bundleAnalysis };
  }

  public measureFunction<T>(name: string, fn: () => T): T {
    const start = performance.now();
    const result = fn();
    const end = performance.now();
    
    console.log(`${name} took ${end - start} milliseconds`);
    return result;
  }

  public async measureAsyncFunction<T>(name: string, fn: () => Promise<T>): Promise<T> {
    const start = performance.now();
    const result = await fn();
    const end = performance.now();
    
    console.log(`${name} took ${end - start} milliseconds`);
    return result;
  }

  public markBundleOptimization(optimizationType: string, sizeBefore: number, sizeAfter: number) {
    const improvement = ((sizeBefore - sizeAfter) / sizeBefore) * 100;
    console.log(`Bundle optimization (${optimizationType}): ${improvement.toFixed(1)}% reduction`);
    
    // Store optimization data
    localStorage.setItem('bundleOptimizations', JSON.stringify({
      ...JSON.parse(localStorage.getItem('bundleOptimizations') || '{}'),
      [optimizationType]: {
        sizeBefore,
        sizeAfter,
        improvement,
        timestamp: Date.now(),
      },
    }));
  }

  public generateReport(): string {
    const metrics = this.getMetrics();
    const bundle = this.getBundleAnalysis();
    
    return `
Performance Report:
==================
Bundle Size: ${(bundle.totalSize || 0) / 1024}KB
Gzipped Size: ${(bundle.gzippedSize || 0) / 1024}KB
Load Time: ${metrics.loadTime?.toFixed(2)}ms
First Contentful Paint: ${metrics.firstContentfulPaint?.toFixed(2)}ms
Largest Contentful Paint: ${metrics.largestContentfulPaint?.toFixed(2)}ms
First Input Delay: ${metrics.firstInputDelay?.toFixed(2)}ms
Cumulative Layout Shift: ${metrics.cumulativeLayoutShift?.toFixed(4)}
Memory Usage: ${(metrics.jsHeapSizeUsed || 0) / 1024 / 1024}MB
    `;
  }

  public destroy() {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
  }
}

// Singleton instance
let performanceMonitor: PerformanceMonitor | null = null;

export function getPerformanceMonitor(): PerformanceMonitor {
  if (!performanceMonitor) {
    performanceMonitor = new PerformanceMonitor();
  }
  return performanceMonitor;
}

// Utility functions
export function measureBundleSize(bundleName: string): Promise<number> {
  return new Promise((resolve) => {
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const entry = entries.find(e => e.name.includes(bundleName));
      if (entry) {
        resolve((entry as any).transferSize || 0);
        observer.disconnect();
      }
    });
    observer.observe({ entryTypes: ['resource'] });
  });
}

export function trackCodeSplitting(chunkName: string) {
  const start = performance.now();
  
  return {
    end: () => {
      const end = performance.now();
      console.log(`Code splitting chunk "${chunkName}" loaded in ${end - start}ms`);
    },
  };
}

export function analyzeRenderPerformance(componentName: string) {
  const start = performance.now();
  
  return {
    end: () => {
      const end = performance.now();
      if (end - start > 16) { // 16ms for 60fps
        console.warn(`Component "${componentName}" render took ${end - start}ms (>16ms threshold)`);
      }
    },
  };
}

// React hook for performance monitoring
export function usePerformanceMonitor() {
  const monitor = getPerformanceMonitor();
  
  return {
    measureRender: (componentName: string) => analyzeRenderPerformance(componentName),
    measureFunction: monitor.measureFunction.bind(monitor),
    measureAsyncFunction: monitor.measureAsyncFunction.bind(monitor),
    getMetrics: monitor.getMetrics.bind(monitor),
    getBundleAnalysis: monitor.getBundleAnalysis.bind(monitor),
    generateReport: monitor.generateReport.bind(monitor),
  };
}

// Export for development use
export { PerformanceMonitor };