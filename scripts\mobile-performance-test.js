#!/usr/bin/env node

/**
 * Mobile Performance Testing Script
 * Tests mobile performance, Core Web Vitals, and battery optimization
 */

const fs = require('fs').promises;
const path = require('path');

class MobilePerformanceTester {
  constructor() {
    this.results = {
      bundleAnalysis: {},
      performanceConfig: {},
      optimizationFeatures: {},
      coreWebVitals: {},
      recommendations: []
    };
    
    this.projectRoot = path.join(__dirname, '..');
  }

  async runPerformanceTest() {
    console.log('\n⚡ Starting Mobile Performance Testing...\n');

    try {
      await this.analyzeBundleSize();
      await this.checkPerformanceConfigurations();
      await this.analyzeOptimizationFeatures();
      await this.assessCoreWebVitals();
      await this.generatePerformanceReport();
      
    } catch (error) {
      console.error('❌ Performance test failed:', error.message);
    }
  }

  async analyzeBundleSize() {
    console.log('📦 Analyzing Bundle Size and Optimization...');
    
    // Check for bundle analyzer reports
    const reportsDir = path.join(this.projectRoot, 'reports');
    const bundleAnalysis = {
      hasBundleReport: false,
      bundleSize: 0,
      optimizations: []
    };

    try {
      const reportFiles = await fs.readdir(reportsDir);
      const bundleReportFile = reportFiles.find(file => file.includes('bundle-report'));
      
      if (bundleReportFile) {
        bundleAnalysis.hasBundleReport = true;
        
        try {
          const bundleReportPath = path.join(reportsDir, bundleReportFile);
          const bundleReport = await fs.readFile(bundleReportPath, 'utf8');
          
          if (bundleReportFile.endsWith('.json')) {
            const report = JSON.parse(bundleReport);
            bundleAnalysis.bundleData = this.extractBundleMetrics(report);
          }
          
        } catch (error) {
          console.warn('    Could not parse bundle report');
        }
      }
    } catch (error) {
      console.warn('    Reports directory not accessible');
    }

    // Check package.json for performance-related dependencies
    try {
      const packageJsonPath = path.join(this.projectRoot, 'package.json');
      const packageContent = await fs.readFile(packageJsonPath, 'utf8');
      const packageJson = JSON.parse(packageContent);
      
      const performanceDeps = [
        '@next/bundle-analyzer',
        'webpack-bundle-analyzer',
        'next-optimized-images',
        'next-pwa',
        'workbox',
        'sharp'
      ];
      
      bundleAnalysis.performanceDependencies = performanceDeps.filter(dep => 
        packageJson.dependencies?.[dep] || packageJson.devDependencies?.[dep]
      );
      
    } catch (error) {
      console.warn('    Package.json not accessible');
    }

    this.results.bundleAnalysis = bundleAnalysis;
    console.log(`  ✅ Bundle Analysis: ${bundleAnalysis.hasBundleReport ? 'Report found' : 'No report'}`);
  }

  async checkPerformanceConfigurations() {
    console.log('⚙️ Checking Performance Configurations...');
    
    const performanceConfig = {
      nextConfig: {},
      webVitalsConfig: false,
      compressionConfig: false,
      cachingConfig: false
    };

    // Check Next.js configuration
    try {
      const nextConfigPath = path.join(this.projectRoot, 'next.config.ts');
      const nextConfigContent = await fs.readFile(nextConfigPath, 'utf8');
      
      performanceConfig.nextConfig = {
        hasImageOptimization: nextConfigContent.includes('images'),
        hasCompression: nextConfigContent.includes('compress') || nextConfigContent.includes('gzip'),
        hasBundleAnalyzer: nextConfigContent.includes('bundle') || nextConfigContent.includes('analyzer'),
        hasExperimentalFeatures: nextConfigContent.includes('experimental'),
        hasOptimizedFonts: nextConfigContent.includes('optimizeFonts'),
        configSize: nextConfigContent.length
      };
      
    } catch (error) {
      console.warn('    Next.js config not accessible');
    }

    // Check for Web Vitals configuration
    try {
      const vitalsPath = path.join(this.projectRoot, 'src/lib/performance/web-vitals.ts');
      await fs.access(vitalsPath);
      performanceConfig.webVitalsConfig = true;
    } catch (error) {
      console.warn('    Web Vitals config not found');
    }

    // Check for performance monitoring
    try {
      const monitoringPath = path.join(this.projectRoot, 'src/lib/performance/monitoring.ts');
      await fs.access(monitoringPath);
      performanceConfig.performanceMonitoring = true;
    } catch (error) {
      console.warn('    Performance monitoring not found');
    }

    this.results.performanceConfig = performanceConfig;
    console.log(`  ✅ Performance Config: ${Object.values(performanceConfig).filter(Boolean).length} features configured`);
  }

  async analyzeOptimizationFeatures() {
    console.log('🚀 Analyzing Optimization Features...');
    
    const optimizationFeatures = {
      lazyLoading: false,
      codeSpitting: false,
      imagaOptimization: false,
      treeshaking: false,
      minification: false,
      prefetching: false
    };

    // Check component files for optimization patterns
    const componentFiles = [
      'src/components/calculator/LazyCalculatorContainer.tsx',
      'src/components/calculator/OptimizedCalculatorContainer.tsx',
      'src/components/ui/optimized-image.tsx',
      'src/components/ui/optimized-motion.tsx'
    ];

    for (const componentFile of componentFiles) {
      try {
        const componentPath = path.join(this.projectRoot, componentFile);
        const content = await fs.readFile(componentPath, 'utf8');
        
        if (content.includes('lazy') || content.includes('Suspense')) {
          optimizationFeatures.lazyLoading = true;
        }
        
        if (content.includes('dynamic') || content.includes('import(')) {
          optimizationFeatures.codeSpitting = true;
        }
        
        if (content.includes('next/image') || content.includes('loading="lazy"')) {
          optimizationFeatures.imagaOptimization = true;
        }
        
        if (content.includes('prefetch') || content.includes('preload')) {
          optimizationFeatures.prefetching = true;
        }
        
      } catch (error) {
        // File doesn't exist, continue
      }
    }

    // Check for performance utilities
    try {
      const utilsPath = path.join(this.projectRoot, 'src/lib/utils/optimized-utils.ts');
      const utilsContent = await fs.readFile(utilsPath, 'utf8');
      
      if (utilsContent.includes('memo') || utilsContent.includes('useMemo') || utilsContent.includes('useCallback')) {
        optimizationFeatures.memoization = true;
      }
      
    } catch (error) {
      console.warn('    Optimized utils not found');
    }

    this.results.optimizationFeatures = optimizationFeatures;
    console.log(`  ✅ Optimization Features: ${Object.values(optimizationFeatures).filter(Boolean).length}/6 implemented`);
  }

  async assessCoreWebVitals() {
    console.log('📊 Assessing Core Web Vitals Readiness...');
    
    const coreWebVitals = {
      lcpOptimization: false,
      fidOptimization: false,
      clsOptimization: false,
      monitoring: false,
      budgets: false
    };

    // Check for Core Web Vitals monitoring
    try {
      const webVitalsPath = path.join(this.projectRoot, 'src/lib/performance/web-vitals.ts');
      const webVitalsContent = await fs.readFile(webVitalsPath, 'utf8');
      
      coreWebVitals.monitoring = true;
      coreWebVitals.lcpOptimization = webVitalsContent.includes('LCP') || webVitalsContent.includes('largest-contentful-paint');
      coreWebVitals.fidOptimization = webVitalsContent.includes('FID') || webVitalsContent.includes('first-input-delay');
      coreWebVitals.clsOptimization = webVitalsContent.includes('CLS') || webVitalsContent.includes('cumulative-layout-shift');
      
    } catch (error) {
      console.warn('    Web Vitals monitoring not implemented');
    }

    // Check for performance budgets
    try {
      const budgetPath = path.join(this.projectRoot, 'src/lib/performance/performance-budget.ts');
      await fs.access(budgetPath);
      coreWebVitals.budgets = true;
    } catch (error) {
      console.warn('    Performance budgets not configured');
    }

    // Check for lighthouse configuration
    try {
      const lighthousePath = path.join(this.projectRoot, 'lighthouserc.js');
      await fs.access(lighthousePath);
      coreWebVitals.lighthouseConfig = true;
    } catch (error) {
      console.warn('    Lighthouse configuration not found');
    }

    this.results.coreWebVitals = coreWebVitals;
    console.log(`  ✅ Core Web Vitals: ${Object.values(coreWebVitals).filter(Boolean).length} optimizations implemented`);
  }

  extractBundleMetrics(bundleReport) {
    // Extract basic metrics from bundle report
    const metrics = {
      totalSize: 0,
      chunks: 0,
      modules: 0,
      assets: 0
    };

    try {
      if (bundleReport.chunks) {
        metrics.chunks = bundleReport.chunks.length;
        metrics.totalSize = bundleReport.chunks.reduce((total, chunk) => total + (chunk.size || 0), 0);
      }
      
      if (bundleReport.modules) {
        metrics.modules = bundleReport.modules.length;
      }
      
      if (bundleReport.assets) {
        metrics.assets = bundleReport.assets.length;
      }
    } catch (error) {
      console.warn('    Could not extract bundle metrics');
    }

    return metrics;
  }

  generateRecommendations() {
    const recommendations = [];

    if (!this.results.optimizationFeatures.lazyLoading) {
      recommendations.push({
        category: 'Lazy Loading',
        priority: 'HIGH',
        action: 'Implement lazy loading for components and images to improve initial load time'
      });
    }

    if (!this.results.optimizationFeatures.codeSpitting) {
      recommendations.push({
        category: 'Code Splitting',
        priority: 'HIGH',
        action: 'Implement code splitting to reduce bundle size and improve performance'
      });
    }

    if (!this.results.coreWebVitals.monitoring) {
      recommendations.push({
        category: 'Performance Monitoring',
        priority: 'MEDIUM',
        action: 'Implement Core Web Vitals monitoring for performance tracking'
      });
    }

    if (!this.results.performanceConfig.webVitalsConfig) {
      recommendations.push({
        category: 'Web Vitals',
        priority: 'MEDIUM',
        action: 'Configure Web Vitals tracking and optimization'
      });
    }

    if (!this.results.bundleAnalysis.hasBundleReport) {
      recommendations.push({
        category: 'Bundle Analysis',
        priority: 'LOW',
        action: 'Add bundle analyzer to track and optimize bundle size'
      });
    }

    return recommendations;
  }

  async generatePerformanceReport() {
    console.log('\n📋 Generating Performance Report...');

    this.results.recommendations = this.generateRecommendations();
    
    const performanceScore = this.calculatePerformanceScore();
    
    const report = this.createPerformanceReport(performanceScore);
    
    const reportPath = path.join(this.projectRoot, 'MOBILE_PERFORMANCE_TEST_REPORT.md');
    await fs.writeFile(reportPath, report);

    console.log(`\n✅ Performance test report saved to: ${reportPath}`);
    console.log(`📊 Performance Score: ${performanceScore}%`);
    
    return this.results;
  }

  calculatePerformanceScore() {
    const scores = {
      bundleOptimization: this.results.bundleAnalysis.hasBundleReport ? 100 : 0,
      configOptimization: this.calculateConfigScore(),
      featureOptimization: this.calculateFeatureScore(),
      webVitalsReadiness: this.calculateWebVitalsScore()
    };

    return Math.round(Object.values(scores).reduce((sum, score) => sum + score, 0) / Object.values(scores).length);
  }

  calculateConfigScore() {
    const config = this.results.performanceConfig;
    const features = [
      config.webVitalsConfig,
      config.performanceMonitoring,
      config.nextConfig?.hasImageOptimization,
      config.nextConfig?.hasCompression,
      config.nextConfig?.hasBundleAnalyzer
    ];
    
    return Math.round((features.filter(Boolean).length / features.length) * 100);
  }

  calculateFeatureScore() {
    const features = Object.values(this.results.optimizationFeatures);
    return Math.round((features.filter(Boolean).length / features.length) * 100);
  }

  calculateWebVitalsScore() {
    const vitals = Object.values(this.results.coreWebVitals);
    return Math.round((vitals.filter(Boolean).length / vitals.length) * 100);
  }

  createPerformanceReport(score) {
    const timestamp = new Date().toISOString();
    
    return `# Mobile Performance Testing Report

**Project**: Nirmaan AI Construction Calculator  
**Test Date**: ${timestamp}  
**Performance Score**: ${score}%  
**Status**: ${score >= 80 ? '✅ EXCELLENT' : score >= 70 ? '✅ GOOD' : score >= 60 ? '⚠️ FAIR' : '❌ NEEDS IMPROVEMENT'}

## Executive Summary

This report evaluates the mobile performance optimization implementation of the Nirmaan AI Construction Calculator platform, including bundle optimization, Core Web Vitals readiness, and performance monitoring capabilities.

## Performance Analysis Results

### 1. Bundle Analysis 📦
**Score**: ${this.results.bundleAnalysis.hasBundleReport ? 100 : 0}%

- **Bundle Report Available**: ${this.results.bundleAnalysis.hasBundleReport ? '✅ Yes' : '❌ No'}
- **Performance Dependencies**: ${this.results.bundleAnalysis.performanceDependencies?.length || 0} installed

${this.results.bundleAnalysis.performanceDependencies?.length > 0 ? `
**Installed Performance Tools**:
${this.results.bundleAnalysis.performanceDependencies.map(dep => `- ${dep}`).join('\n')}
` : '**No performance dependencies found**'}

${this.results.bundleAnalysis.bundleData ? `
**Bundle Metrics**:
- Total Size: ${this.formatBytes(this.results.bundleAnalysis.bundleData.totalSize)}
- Chunks: ${this.results.bundleAnalysis.bundleData.chunks}
- Modules: ${this.results.bundleAnalysis.bundleData.modules}
- Assets: ${this.results.bundleAnalysis.bundleData.assets}
` : ''}

### 2. Performance Configuration ⚙️
**Score**: ${this.calculateConfigScore()}%

**Next.js Configuration**:
${Object.entries(this.results.performanceConfig.nextConfig || {}).map(([feature, enabled]) => 
  `- ${feature}: ${enabled ? '✅ Enabled' : '❌ Disabled'}`
).join('\n')}

**Performance Features**:
- Web Vitals Config: ${this.results.performanceConfig.webVitalsConfig ? '✅ Configured' : '❌ Not configured'}
- Performance Monitoring: ${this.results.performanceConfig.performanceMonitoring ? '✅ Implemented' : '❌ Not implemented'}

### 3. Optimization Features 🚀
**Score**: ${this.calculateFeatureScore()}%

**Implementation Status**:
${Object.entries(this.results.optimizationFeatures).map(([feature, implemented]) => 
  `- ${feature}: ${implemented ? '✅ Implemented' : '❌ Not implemented'}`
).join('\n')}

### 4. Core Web Vitals Readiness 📊
**Score**: ${this.calculateWebVitalsScore()}%

**Web Vitals Optimization**:
${Object.entries(this.results.coreWebVitals).map(([vital, optimized]) => 
  `- ${vital}: ${optimized ? '✅ Optimized' : '❌ Needs optimization'}`
).join('\n')}

## Performance Recommendations

${this.results.recommendations.map((rec, index) => 
  `### ${index + 1}. ${rec.category} (Priority: ${rec.priority})
${rec.action}
`).join('\n')}

## Mobile Performance Best Practices Assessment

### ✅ Current Strengths
${this.generateStrengths()}

### ⚠️ Areas for Improvement
${this.generateImprovements()}

## Performance Optimization Roadmap

### Immediate Actions (High Priority)
${this.results.recommendations.filter(r => r.priority === 'HIGH').map(r => `- ${r.action}`).join('\n') || '- Continue current optimization approach'}

### Short-term Improvements (Medium Priority)
${this.results.recommendations.filter(r => r.priority === 'MEDIUM').map(r => `- ${r.action}`).join('\n') || '- Focus on monitoring and measurement'}

### Long-term Enhancements (Low Priority)
${this.results.recommendations.filter(r => r.priority === 'LOW').map(r => `- ${r.action}`).join('\n') || '- Advanced optimization techniques'}

## Performance Metrics Targets

### Core Web Vitals Targets
- **LCP (Largest Contentful Paint)**: < 2.5 seconds
- **FID (First Input Delay)**: < 100 milliseconds  
- **CLS (Cumulative Layout Shift)**: < 0.1

### Mobile Performance Targets
- **Load Time**: < 3 seconds on 3G
- **Bundle Size**: < 250KB initial load
- **Time to Interactive**: < 3.5 seconds
- **Speed Index**: < 3 seconds

## Implementation Guidelines

### Bundle Optimization
1. **Code Splitting**: Implement route-based and component-based splitting
2. **Tree Shaking**: Remove unused code from bundles
3. **Compression**: Enable gzip/brotli compression
4. **Minification**: Minify JavaScript, CSS, and HTML

### Image Optimization
1. **Next.js Image**: Use Next.js Image component for automatic optimization
2. **WebP Format**: Serve modern image formats with fallbacks
3. **Lazy Loading**: Implement lazy loading for images
4. **Responsive Images**: Use responsive image sets

### Performance Monitoring
1. **Web Vitals**: Track Core Web Vitals metrics
2. **Real User Monitoring**: Implement RUM for production insights
3. **Performance Budgets**: Set and enforce performance budgets
4. **Lighthouse CI**: Automate performance testing

---

**Report Generated**: ${timestamp}  
**Performance Assessment**: ${this.getPerformanceAssessment(score)}  
**Next Review**: Recommended after performance optimizations
`;
  }

  generateStrengths() {
    const strengths = [];
    
    if (this.results.bundleAnalysis.performanceDependencies?.length > 0) {
      strengths.push('- Performance optimization tools installed');
    }
    
    if (this.results.optimizationFeatures.lazyLoading) {
      strengths.push('- Lazy loading implementation');
    }
    
    if (this.results.optimizationFeatures.imagaOptimization) {
      strengths.push('- Image optimization features');
    }
    
    if (this.results.coreWebVitals.monitoring) {
      strengths.push('- Core Web Vitals monitoring');
    }
    
    if (this.results.performanceConfig.nextConfig?.hasImageOptimization) {
      strengths.push('- Next.js image optimization configured');
    }
    
    return strengths.length > 0 ? strengths.join('\n') : '- Basic performance infrastructure in place';
  }

  generateImprovements() {
    const improvements = [];
    
    if (!this.results.optimizationFeatures.codeSpitting) {
      improvements.push('- Implement code splitting for better bundle management');
    }
    
    if (!this.results.coreWebVitals.monitoring) {
      improvements.push('- Add Core Web Vitals monitoring and optimization');
    }
    
    if (!this.results.bundleAnalysis.hasBundleReport) {
      improvements.push('- Add bundle analysis for size monitoring');
    }
    
    if (!this.results.performanceConfig.webVitalsConfig) {
      improvements.push('- Configure Web Vitals tracking');
    }
    
    return improvements.length > 0 ? improvements.join('\n') : '- Performance optimization appears comprehensive';
  }

  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  getPerformanceAssessment(score) {
    if (score >= 80) return 'Excellent performance optimization with comprehensive features';
    if (score >= 70) return 'Good performance foundation with some optimization opportunities';
    if (score >= 60) return 'Fair performance implementation requiring enhancement';
    return 'Performance optimization needs significant development';
  }
}

// Run the performance test if this file is executed directly
if (require.main === module) {
  (async () => {
    const tester = new MobilePerformanceTester();
    await tester.runPerformanceTest();
  })().catch(console.error);
}

module.exports = MobilePerformanceTester;