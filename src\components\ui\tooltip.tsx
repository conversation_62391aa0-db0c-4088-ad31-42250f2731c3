/**
 * Tooltip Component
 * Accessible tooltip implementation with positioning and animations
 */

import * as React from "react";
import { motion, AnimatePresence, type Variants } from "framer-motion";
import { cn } from "@/lib/utils";

interface TooltipProps {
  children: React.ReactNode;
  side?: "top" | "bottom" | "left" | "right";
  align?: "start" | "center" | "end";
  delayDuration?: number;
  className?: string;
}

interface TooltipTriggerProps {
  asChild?: boolean;
  children: React.ReactNode;
}

interface TooltipContentProps extends React.HTMLAttributes<HTMLDivElement> {
  side?: "top" | "bottom" | "left" | "right";
  align?: "start" | "center" | "end";
  sideOffset?: number;
  alignOffset?: number;
}

// Animation variants
const tooltipVariants: Variants = {
  hidden: {
    opacity: 0,
    scale: 0.9,
    y: 4,
    transition: { duration: 0.1 }
  },
  visible: {
    opacity: 1,
    scale: 1,
    y: 0,
    transition: { duration: 0.15, ease: "easeOut" }
  },
  exit: {
    opacity: 0,
    scale: 0.9,
    y: 4,
    transition: { duration: 0.1 }
  }
};

// Context for tooltip state
const TooltipContext = React.createContext<{
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  side: TooltipProps["side"];
  align: TooltipProps["align"];
  delayDuration: number;
}>({
  isOpen: false,
  setIsOpen: () => {},
  side: "top",
  align: "center",
  delayDuration: 700,
});

export const TooltipProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return <>{children}</>;
};

export const Tooltip: React.FC<TooltipProps> = ({ 
  children, 
  side = "top", 
  align = "center", 
  delayDuration = 700,
  className 
}) => {
  const [isOpen, setIsOpen] = React.useState(false);
  const timeoutRef = React.useRef<NodeJS.Timeout>();

  const handleMouseEnter = () => {
    timeoutRef.current = setTimeout(() => {
      setIsOpen(true);
    }, delayDuration);
  };

  const handleMouseLeave = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setIsOpen(false);
  };

  React.useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return (
    <TooltipContext.Provider value={{ isOpen, setIsOpen, side, align, delayDuration }}>
      <div 
        className={cn("relative inline-block", className)}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        {children}
      </div>
    </TooltipContext.Provider>
  );
};

export const TooltipTrigger: React.FC<TooltipTriggerProps> = ({ 
  asChild = false, 
  children 
}) => {
  if (asChild && React.isValidElement(children)) {
    return React.cloneElement(children);
  }

  return <>{children}</>;
};

export const TooltipContent: React.FC<TooltipContentProps> = ({ 
  children, 
  className,
  side = "top",
  align = "center",
  sideOffset = 8,
  alignOffset = 0,
  ...props 
}) => {
  const { isOpen } = React.useContext(TooltipContext);

  // Position classes based on side and align
  const getPositionClasses = () => {
    const baseClasses = "absolute z-50 pointer-events-none";
    
    switch (side) {
      case "top":
        return cn(
          baseClasses,
          "bottom-full mb-2",
          align === "start" && "left-0",
          align === "center" && "left-1/2 -translate-x-1/2",
          align === "end" && "right-0"
        );
      case "bottom":
        return cn(
          baseClasses,
          "top-full mt-2",
          align === "start" && "left-0",
          align === "center" && "left-1/2 -translate-x-1/2",
          align === "end" && "right-0"
        );
      case "left":
        return cn(
          baseClasses,
          "right-full mr-2",
          align === "start" && "top-0",
          align === "center" && "top-1/2 -translate-y-1/2",
          align === "end" && "bottom-0"
        );
      case "right":
        return cn(
          baseClasses,
          "left-full ml-2",
          align === "start" && "top-0",
          align === "center" && "top-1/2 -translate-y-1/2",
          align === "end" && "bottom-0"
        );
      default:
        return baseClasses;
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className={cn(
            getPositionClasses(),
            "px-3 py-2 text-sm text-white bg-gray-900 rounded-md shadow-lg",
            "max-w-xs whitespace-normal break-words",
            className
          )}
          variants={tooltipVariants}
          initial="hidden"
          animate="visible"
          exit="exit"
          style={{
            marginTop: side === "bottom" ? sideOffset : undefined,
            marginBottom: side === "top" ? sideOffset : undefined,
            marginLeft: side === "right" ? sideOffset : undefined,
            marginRight: side === "left" ? sideOffset : undefined,
          }}
          {...props}
        >
          {children}
          
          {/* Arrow */}
          <div
            className={cn(
              "absolute w-2 h-2 bg-gray-900 rotate-45",
              side === "top" && "top-full -translate-y-1/2 left-1/2 -translate-x-1/2",
              side === "bottom" && "bottom-full translate-y-1/2 left-1/2 -translate-x-1/2",
              side === "left" && "left-full -translate-x-1/2 top-1/2 -translate-y-1/2",
              side === "right" && "right-full translate-x-1/2 top-1/2 -translate-y-1/2"
            )}
          />
        </motion.div>
      )}
    </AnimatePresence>
  );
};

// Export types
export type { TooltipProps, TooltipTriggerProps, TooltipContentProps };