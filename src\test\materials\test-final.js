/**
 * Final Material Database Test Suite
 * Comprehensive end-to-end testing of the complete materials system
 */

const path = require('path');
const fs = require('fs');

console.log('🎯 Final Material Database Test Suite');
console.log('=====================================\n');

// Test configuration
const testProjects = [
  {
    name: 'Small Residential',
    builtUpArea: 800,
    floors: 0,
    qualityTier: 'smart',
    location: 'bangalore'
  },
  {
    name: 'Medium Residential',
    builtUpArea: 1500,
    floors: 1,
    qualityTier: 'premium',
    location: 'mumbai'
  },
  {
    name: 'Large Residential',
    builtUpArea: 3000,
    floors: 2,
    qualityTier: 'luxury',
    location: 'delhi'
  }
];

const allTestResults = [];

// Comprehensive test function
function runComprehensiveTest(project) {
  console.log(`🏠 Testing: ${project.name}`);
  console.log(`   ${project.builtUpArea} sqft | ${project.qualityTier} | ${project.location}`);

  try {
    // Load materials database
    const materialsPath = path.join(__dirname, '../../data/materials/core-materials.json');
    const materialsData = JSON.parse(fs.readFileSync(materialsPath, 'utf8'));

    // Calculate areas
    const areas = {
      builtUpArea: project.builtUpArea,
      wallArea: project.builtUpArea * 3.2,
      floorArea: project.builtUpArea,
      roofArea: project.builtUpArea / (project.floors + 1),
      foundationArea: project.builtUpArea / (project.floors + 1) * 1.1
    };

    // Quality tier material selection
    const qualityMultipliers = { smart: 1.0, premium: 1.2, luxury: 1.5 };
    const multiplier = qualityMultipliers[project.qualityTier];

    const materialSelection = {
      smart: {
        cement: 'cement_opc43_acc',
        steel: 'steel_tmt_fe500_jsw',
        brick: 'brick_red_clay_standard',
        tile: 'tile_ceramic_somany'
      },
      premium: {
        cement: 'cement_opc53_ultratech',
        steel: 'steel_tmt_fe500d_tata',
        brick: 'brick_flyash_billtech',
        tile: 'tile_vitrified_kajaria'
      },
      luxury: {
        cement: 'cement_opc53_ultratech',
        steel: 'steel_tmt_fe500d_tata',
        brick: 'block_aac_siporex',
        tile: 'tile_vitrified_kajaria'
      }
    };

    const selectedMaterials = materialSelection[project.qualityTier];

    // Calculate material requirements
    const requirements = [
      {
        materialId: selectedMaterials.cement,
        quantity: Math.ceil(areas.builtUpArea * 0.35 * multiplier),
        unit: 'bag',
        category: 'Cement'
      },
      {
        materialId: selectedMaterials.steel,
        quantity: Math.ceil(areas.builtUpArea * 4.2 * multiplier),
        unit: 'kg',
        category: 'Steel'
      },
      {
        materialId: selectedMaterials.brick,
        quantity: Math.ceil(areas.wallArea * (selectedMaterials.brick === 'block_aac_siporex' ? 1.8 : 12.0)),
        unit: 'piece',
        category: 'Bricks'
      },
      {
        materialId: selectedMaterials.tile,
        quantity: Math.ceil(areas.floorArea * 1.1 * 100) / 100,
        unit: 'sqm',
        category: 'Tiles'
      },
      {
        materialId: 'wire_copper_havells',
        quantity: Math.ceil(areas.builtUpArea * 8.5),
        unit: 'meter',
        category: 'Electrical'
      },
      {
        materialId: 'pipe_pvc_supreme',
        quantity: Math.ceil(areas.builtUpArea * 2.2),
        unit: 'meter',
        category: 'Plumbing'
      },
      {
        materialId: 'paint_interior_asian',
        quantity: Math.ceil(areas.wallArea * 0.007 * 100) / 100,
        unit: 'liter',
        category: 'Paint'
      }
    ];

    // Add luxury features
    if (project.qualityTier === 'luxury' || project.qualityTier === 'premium') {
      requirements.push({
        materialId: 'waterproofing_fosroc',
        quantity: Math.ceil(areas.roofArea * 0.012 * 100) / 100,
        unit: 'liter',
        category: 'Waterproofing'
      });
    }

    // Calculate costs
    let totalCost = 0;
    let totalWastage = 0;
    const categoryTotals = {};
    const qualityScores = [];

    const detailedBreakdown = requirements.map(req => {
      const material = materialsData.materials.find(m => m.id === req.materialId);
      if (!material) return null;

      const pricing = material.pricing[project.location] || material.pricing.default;
      const unitPrice = pricing.bulk;
      const subtotal = unitPrice * req.quantity;
      const wastageAmount = (subtotal * material.wastagePercentage) / 100;
      const finalCost = subtotal + wastageAmount;

      totalCost += finalCost;
      totalWastage += wastageAmount;
      categoryTotals[req.category] = (categoryTotals[req.category] || 0) + finalCost;
      qualityScores.push(material.qualityScore);

      return {
        materialName: material.name,
        category: req.category,
        quantity: req.quantity,
        unit: req.unit,
        unitPrice,
        subtotal,
        wastage: material.wastagePercentage,
        wastageAmount,
        finalCost,
        qualityScore: material.qualityScore,
        supplier: material.brand
      };
    }).filter(Boolean);

    const avgQuality = qualityScores.reduce((sum, score) => sum + score, 0) / qualityScores.length;
    const costPerSqft = totalCost / project.builtUpArea;

    // Project summary
    const result = {
      projectName: project.name,
      specs: project,
      areas,
      totalMaterialCost: totalCost,
      totalWastage,
      costPerSqft,
      categoryTotals,
      averageQuality: avgQuality,
      materialCount: requirements.length,
      detailedBreakdown
    };

    // Display results
    console.log(`   💰 Total Cost: ₹${totalCost.toFixed(2)} (₹${costPerSqft.toFixed(2)}/sqft)`);
    console.log(`   ⭐ Avg Quality: ${avgQuality.toFixed(1)}/10`);
    console.log(`   📦 Materials: ${requirements.length} types`);
    console.log(`   🗑️  Wastage: ₹${totalWastage.toFixed(2)} (${((totalWastage/totalCost)*100).toFixed(1)}%)`);

    // Top 3 cost categories
    const topCategories = Object.entries(categoryTotals)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3);

    console.log('   🏗️  Top Categories:');
    topCategories.forEach(([category, cost]) => {
      const percentage = (cost / totalCost) * 100;
      console.log(`      ${category}: ₹${cost.toFixed(2)} (${percentage.toFixed(1)}%)`);
    });

    console.log('');
    allTestResults.push(result);
    return result;

  } catch (error) {
    console.error(`   ❌ Error: ${error.message}\n`);
    return null;
  }
}

// Test material recommendations
function testMaterialRecommendations() {
  console.log('🎯 Testing Material Recommendations...\n');

  try {
    const materialsPath = path.join(__dirname, '../../data/materials/core-materials.json');
    const materialsData = JSON.parse(fs.readFileSync(materialsPath, 'utf8'));

    const qualityTiers = ['smart', 'premium', 'luxury'];

    qualityTiers.forEach(tier => {
      console.log(`${tier.toUpperCase()} TIER RECOMMENDATIONS:`);

      const minQuality = tier === 'luxury' ? 9.0 : tier === 'premium' ? 8.0 : 6.0;
      const recommendations = materialsData.materials
        .filter(m => m.qualityScore >= minQuality && m.availability === 'High')
        .sort((a, b) => b.qualityScore - a.qualityScore)
        .slice(0, 5);

      recommendations.forEach((material, index) => {
        console.log(`   ${index + 1}. ${material.name}`);
        console.log(`      Quality: ${material.qualityScore}/10 | Brand: ${material.brand}`);
        console.log(`      Category: ${material.category} | Price: ₹${material.pricing.default.bulk}/${material.unit}`);
      });
      console.log('');
    });

  } catch (error) {
    console.error(`❌ Error in recommendations: ${error.message}\n`);
  }
}

// Test regional cost variations
function testRegionalVariations() {
  console.log('🌍 Testing Regional Cost Variations...\n');

  try {
    const materialsPath = path.join(__dirname, '../../data/materials/core-materials.json');
    const materialsData = JSON.parse(fs.readFileSync(materialsPath, 'utf8'));

    const regions = materialsData.metadata.regions;
    const sampleProject = {
      builtUpArea: 1000,
      materials: [
        { id: 'cement_opc53_ultratech', quantity: 20 },
        { id: 'steel_tmt_fe500d_tata', quantity: 1000 },
        { id: 'brick_flyash_billtech', quantity: 5000 },
        { id: 'tile_vitrified_kajaria', quantity: 100 }
      ]
    };

    console.log('Sample Project Regional Costs (1000 sqft):');
    console.log('Material basket: Cement, Steel, Bricks, Tiles\n');

    const regionCosts = regions.map(region => {
      let totalCost = 0;

      sampleProject.materials.forEach(item => {
        const material = materialsData.materials.find(m => m.id === item.id);
        if (material) {
          const pricing = material.pricing[region] || material.pricing.default;
          totalCost += pricing.bulk * item.quantity;
        }
      });

      return { region, totalCost, costPerSqft: totalCost / sampleProject.builtUpArea };
    });

    regionCosts.sort((a, b) => a.totalCost - b.totalCost);

    regionCosts.forEach((region, index) => {
      const marker = index === 0 ? '💰 (Cheapest)' : index === regionCosts.length - 1 ? '💸 (Most Expensive)' : '';
      console.log(`   ${region.region.toUpperCase()}: ₹${region.totalCost.toFixed(2)} (₹${region.costPerSqft.toFixed(2)}/sqft) ${marker}`);
    });

    const cheapest = regionCosts[0];
    const mostExpensive = regionCosts[regionCosts.length - 1];
    const variation = ((mostExpensive.totalCost - cheapest.totalCost) / cheapest.totalCost) * 100;

    console.log(`\n📊 Regional Variation: ${variation.toFixed(1)}%`);
    console.log(`   Savings by choosing optimal region: ₹${(mostExpensive.totalCost - cheapest.totalCost).toFixed(2)}`);

  } catch (error) {
    console.error(`❌ Error in regional variations: ${error.message}\n`);
  }
}

// Test database performance
function testDatabasePerformance() {
  console.log('⚡ Testing Database Performance...\n');

  try {
    const materialsPath = path.join(__dirname, '../../data/materials/core-materials.json');

    // Test loading time
    const loadStart = Date.now();
    const materialsData = JSON.parse(fs.readFileSync(materialsPath, 'utf8'));
    const loadTime = Date.now() - loadStart;

    // Test search performance
    const searchStart = Date.now();
    const searchResults = materialsData.materials.filter(m =>
      m.category === 'Cement' && m.qualityScore >= 8.0
    );
    const searchTime = Date.now() - searchStart;

    // Test price calculation performance
    const calcStart = Date.now();
    let calculations = 0;
    materialsData.materials.forEach(material => {
      Object.values(material.pricing).forEach(pricing => {
        if (typeof pricing === 'object' && pricing.retail) {
          calculations++;
        }
      });
    });
    const calcTime = Date.now() - calcStart;

    console.log('Performance Metrics:');
    console.log(`   📁 Database Load: ${loadTime}ms`);
    console.log(`   🔍 Search Query: ${searchTime}ms (${searchResults.length} results)`);
    console.log(`   🧮 Price Calculations: ${calcTime}ms (${calculations} calculations)`);
    console.log(`   📊 Database Size: ${JSON.stringify(materialsData).length} characters`);
    console.log(`   💾 Memory Usage: ${(JSON.stringify(materialsData).length / 1024).toFixed(2)} KB`);

  } catch (error) {
    console.error(`❌ Error in performance testing: ${error.message}\n`);
  }
}

// Generate final summary
function generateFinalSummary() {
  console.log('📋 Final Test Summary');
  console.log('====================\n');

  if (allTestResults.length === 0) {
    console.log('❌ No test results to summarize');
    return;
  }

  const totalProjects = allTestResults.length;
  const avgCostPerSqft = allTestResults.reduce((sum, result) => sum + result.costPerSqft, 0) / totalProjects;
  const avgQuality = allTestResults.reduce((sum, result) => sum + result.averageQuality, 0) / totalProjects;
  const totalMaterialTypes = new Set(allTestResults.flatMap(r => r.detailedBreakdown.map(d => d.materialName))).size;

  console.log('📊 Aggregate Statistics:');
  console.log(`   🏠 Projects Tested: ${totalProjects}`);
  console.log(`   📦 Unique Materials: ${totalMaterialTypes}`);
  console.log(`   💰 Avg Cost/Sqft: ₹${avgCostPerSqft.toFixed(2)}`);
  console.log(`   ⭐ Avg Quality: ${avgQuality.toFixed(1)}/10`);

  console.log('\n🏗️  Cost by Project Type:');
  allTestResults.forEach(result => {
    console.log(`   ${result.projectName}: ₹${result.costPerSqft.toFixed(2)}/sqft (${result.specs.qualityTier})`);
  });

  // Quality tier analysis
  const tierResults = allTestResults.reduce((acc, result) => {
    const tier = result.specs.qualityTier;
    if (!acc[tier]) acc[tier] = { costs: [], qualities: [] };
    acc[tier].costs.push(result.costPerSqft);
    acc[tier].qualities.push(result.averageQuality);
    return acc;
  }, {});

  console.log('\n⭐ Quality Tier Analysis:');
  Object.entries(tierResults).forEach(([tier, data]) => {
    const avgCost = data.costs.reduce((sum, cost) => sum + cost, 0) / data.costs.length;
    const avgQual = data.qualities.reduce((sum, qual) => sum + qual, 0) / data.qualities.length;
    console.log(`   ${tier.toUpperCase()}: ₹${avgCost.toFixed(2)}/sqft, Quality ${avgQual.toFixed(1)}/10`);
  });

  console.log('\n✅ Material Database Integration: SUCCESSFUL');
  console.log('🎯 All systems operational and ready for production!');
}

// Run the complete test suite
function runCompleteTestSuite() {
  console.log('🚀 Starting Complete Material Database Test Suite...\n');

  // Test each project
  testProjects.forEach(project => {
    runComprehensiveTest(project);
  });

  // Additional tests
  testMaterialRecommendations();
  testRegionalVariations();
  testDatabasePerformance();

  // Final summary
  generateFinalSummary();

  console.log('\n🎉 Complete test suite finished successfully!');
}

// Execute the complete test suite
runCompleteTestSuite();