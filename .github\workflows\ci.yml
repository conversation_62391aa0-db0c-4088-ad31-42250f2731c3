name: 🚀 Continuous Integration

on:
  push:
    branches: [ main, master, develop ]
  pull_request:
    branches: [ main, master, develop ]
  schedule:
    # Run daily at 2 AM UTC for dependency updates
    - cron: '0 2 * * *'
  workflow_dispatch:

env:
  NODE_VERSION: '18'
  CACHE_KEY_VERSION: 'v2'
  FORCE_COLOR: 3
  CI: true

jobs:
  # Job 1: Code Quality & Linting
  quality:
    name: 🔍 Code Quality
    runs-on: ubuntu-latest
    timeout-minutes: 15
    
    outputs:
      lint-score: ${{ steps.quality-score.outputs.lint-score }}
      type-check-passed: ${{ steps.quality-score.outputs.type-check-passed }}
      format-check-passed: ${{ steps.quality-score.outputs.format-check-passed }}
    
    steps:
      - name: 📦 Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
        
      - name: 🔧 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 📥 Install dependencies
        run: npm ci --prefer-offline --no-audit --progress=false
        
      - name: 🔍 Run ESLint with detailed output
        id: eslint
        run: |
          npm run lint -- --format=json --output-file=eslint-report.json || true
          npm run lint
        continue-on-error: true
        
      - name: 🎨 Check Prettier formatting
        id: prettier
        run: |
          npm run format:check
          echo "format-check-passed=true" >> $GITHUB_OUTPUT
        continue-on-error: true
        
      - name: 📝 TypeScript type checking
        id: typecheck
        run: |
          npm run type-check
          echo "type-check-passed=true" >> $GITHUB_OUTPUT
        continue-on-error: true
        
      - name: 📊 Calculate quality score
        id: quality-score
        run: |
          LINT_SCORE=0
          if [ -f eslint-report.json ]; then
            ERRORS=$(jq '.[] | select(.errorCount > 0) | .errorCount' eslint-report.json | jq -s 'add // 0')
            WARNINGS=$(jq '.[] | select(.warningCount > 0) | .warningCount' eslint-report.json | jq -s 'add // 0')
            LINT_SCORE=$((100 - ERRORS * 10 - WARNINGS * 2))
          fi
          echo "lint-score=$LINT_SCORE" >> $GITHUB_OUTPUT
          echo "type-check-passed=${{ steps.typecheck.outcome == 'success' }}" >> $GITHUB_OUTPUT
          echo "format-check-passed=${{ steps.prettier.outcome == 'success' }}" >> $GITHUB_OUTPUT
        
      - name: 📊 Upload ESLint results
        uses: github/super-linter@v5
        if: always()
        env:
          DEFAULT_BRANCH: main
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          VALIDATE_TYPESCRIPT_ES: true
          VALIDATE_JAVASCRIPT_ES: true
          VALIDATE_JSON: true
          VALIDATE_CSS: true
          VALIDATE_MARKDOWN: true
          
      - name: 📈 Upload quality metrics
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: quality-metrics
          path: |
            eslint-report.json
            coverage/
          retention-days: 7

  # Job 2: Unit Tests
  test-unit:
    name: 🧪 Unit Tests
    runs-on: ubuntu-latest
    timeout-minutes: 15
    
    strategy:
      matrix:
        node-version: ['18', '20']
    
    steps:
      - name: 📦 Checkout code
        uses: actions/checkout@v4
        
      - name: 🔧 Setup Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'npm'
          
      - name: 📥 Install dependencies
        run: npm ci --prefer-offline --no-audit --progress=false
        
      - name: 🧪 Run unit tests
        run: npm run test:coverage
        
      - name: 📊 Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          file: ./coverage/lcov.info
          flags: unittests
          name: codecov-umbrella
          fail_ci_if_error: false

  # Job 3: Build Verification
  build:
    name: 🏗️ Build Verification
    runs-on: ubuntu-latest
    timeout-minutes: 15
    needs: [quality]
    
    steps:
      - name: 📦 Checkout code
        uses: actions/checkout@v4
        
      - name: 🔧 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 📥 Install dependencies
        run: npm ci --prefer-offline --no-audit --progress=false
        
      - name: 🏗️ Build application
        run: npm run build
        env:
          NEXT_TELEMETRY_DISABLED: 1
          
      - name: 📏 Bundle analyzer
        run: npm run analyze
        
      - name: 📦 Upload build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: build-artifacts
          path: |
            .next
            out
          retention-days: 7

  # Job 4: End-to-End Tests
  test-e2e:
    name: 🎭 E2E Tests
    runs-on: ubuntu-latest
    timeout-minutes: 30
    needs: [build]
    
    steps:
      - name: 📦 Checkout code
        uses: actions/checkout@v4
        
      - name: 🔧 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 📥 Install dependencies
        run: npm ci --prefer-offline --no-audit --progress=false
        
      - name: 🎭 Install Playwright
        run: npx playwright install --with-deps
        
      - name: 🏗️ Build application
        run: npm run build
        
      - name: 🚀 Start application
        run: npm start &
        env:
          PORT: 3000
          
      - name: ⏳ Wait for application
        run: npx wait-on http://localhost:3000 --timeout 60000
        
      - name: 🎭 Run Playwright tests
        run: npm run test:e2e
        
      - name: 📸 Upload test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: playwright-report
          path: playwright-report/
          retention-days: 30

  # Job 5: Performance Tests
  performance:
    name: ⚡ Performance Tests
    runs-on: ubuntu-latest
    timeout-minutes: 20
    needs: [build]
    
    steps:
      - name: 📦 Checkout code
        uses: actions/checkout@v4
        
      - name: 🔧 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 📥 Install dependencies
        run: npm ci --prefer-offline --no-audit --progress=false
        
      - name: 🏗️ Build application
        run: npm run build
        
      - name: 🚀 Start application
        run: npm start &
        env:
          PORT: 3000
          
      - name: ⏳ Wait for application
        run: npx wait-on http://localhost:3000 --timeout 60000
        
      - name: ⚡ Run Lighthouse CI
        run: |
          npm install -g @lhci/cli@0.12.x
          lhci autorun
        env:
          LHCI_GITHUB_APP_TOKEN: ${{ secrets.LHCI_GITHUB_APP_TOKEN }}

  # Job 6: Security Scan
  security:
    name: 🔒 Security Scan
    runs-on: ubuntu-latest
    timeout-minutes: 15
    
    outputs:
      security-score: ${{ steps.security-score.outputs.security-score }}
      vulnerabilities: ${{ steps.security-score.outputs.vulnerabilities }}
    
    steps:
      - name: 📦 Checkout code
        uses: actions/checkout@v4
        
      - name: 🔧 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 📥 Install dependencies
        run: npm ci --prefer-offline --no-audit --progress=false
        
      - name: 🔒 Run npm audit
        id: npm-audit
        run: |
          npm audit --audit-level=moderate --json > npm-audit-report.json || true
          npm audit --audit-level=moderate
        continue-on-error: true
        
      - name: 🔍 Run Snyk security scan
        uses: snyk/actions/node@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: --severity-threshold=medium --json > snyk-report.json
        continue-on-error: true
        
      - name: 🛡️ Run CodeQL analysis
        uses: github/codeql-action/init@v3
        with:
          languages: javascript,typescript
          
      - name: 🛡️ Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v3
        with:
          category: "/language:javascript"
          
      - name: 📊 Calculate security score
        id: security-score
        run: |
          SECURITY_SCORE=100
          VULNERABILITIES=0
          
          if [ -f npm-audit-report.json ]; then
            HIGH_VULNS=$(jq '.vulnerabilities | to_entries | map(select(.value.severity == "high")) | length' npm-audit-report.json || echo "0")
            MODERATE_VULNS=$(jq '.vulnerabilities | to_entries | map(select(.value.severity == "moderate")) | length' npm-audit-report.json || echo "0")
            VULNERABILITIES=$((HIGH_VULNS + MODERATE_VULNS))
            SECURITY_SCORE=$((100 - HIGH_VULNS * 20 - MODERATE_VULNS * 10))
          fi
          
          echo "security-score=$SECURITY_SCORE" >> $GITHUB_OUTPUT
          echo "vulnerabilities=$VULNERABILITIES" >> $GITHUB_OUTPUT
          echo "Security Score: $SECURITY_SCORE"
          echo "Vulnerabilities: $VULNERABILITIES"
          
      - name: 📤 Upload security reports
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: security-reports
          path: |
            npm-audit-report.json
            snyk-report.json
          retention-days: 30

  # Job 7: Deployment Preview (for PRs)
  deploy-preview:
    name: 🚀 Deploy Preview
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'
    needs: [quality, test-unit, build]
    timeout-minutes: 10
    
    steps:
      - name: 📦 Checkout code
        uses: actions/checkout@v4
        
      - name: 🚀 Deploy to Vercel
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          github-comment: true
          working-directory: ./

  # Final Quality Gate
  quality-gate:
    name: ✅ Quality Gate
    runs-on: ubuntu-latest
    needs: [quality, test-unit, build, test-e2e, performance, security]
    if: always()
    
    outputs:
      gate-passed: ${{ steps.gate-decision.outputs.gate-passed }}
      overall-score: ${{ steps.calculate-score.outputs.overall-score }}
      
    steps:
      - name: 📦 Checkout code
        uses: actions/checkout@v4
        
      - name: 📊 Calculate overall quality score
        id: calculate-score
        run: |
          # Quality scores from individual jobs
          LINT_SCORE="${{ needs.quality.outputs.lint-score || 0 }}"
          SECURITY_SCORE="${{ needs.security.outputs.security-score || 0 }}"
          VULNERABILITIES="${{ needs.security.outputs.vulnerabilities || 0 }}"
          
          # Job success rates (100 for success, 0 for failure)
          QUALITY_SUCCESS=$([ "${{ needs.quality.result }}" == "success" ] && echo 100 || echo 0)
          UNIT_TEST_SUCCESS=$([ "${{ needs.test-unit.result }}" == "success" ] && echo 100 || echo 0)
          BUILD_SUCCESS=$([ "${{ needs.build.result }}" == "success" ] && echo 100 || echo 0)
          E2E_SUCCESS=$([ "${{ needs.test-e2e.result }}" == "success" ] && echo 100 || echo 0)
          PERFORMANCE_SUCCESS=$([ "${{ needs.performance.result }}" == "success" ] && echo 100 || echo 0)
          SECURITY_SUCCESS=$([ "${{ needs.security.result }}" == "success" ] && echo 100 || echo 0)
          
          # Calculate weighted average
          OVERALL_SCORE=$(((LINT_SCORE * 15 + SECURITY_SCORE * 20 + QUALITY_SUCCESS * 10 + UNIT_TEST_SUCCESS * 25 + BUILD_SUCCESS * 10 + E2E_SUCCESS * 15 + PERFORMANCE_SUCCESS * 5) / 100))
          
          echo "overall-score=$OVERALL_SCORE" >> $GITHUB_OUTPUT
          echo "Overall Quality Score: $OVERALL_SCORE/100"
          
      - name: 📊 Generate quality report
        run: |
          echo "# 🎯 Quality Gate Report" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "## 📊 Overall Score: ${{ steps.calculate-score.outputs.overall-score }}/100" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "| Check | Status | Score |" >> $GITHUB_STEP_SUMMARY
          echo "|-------|---------|-------|" >> $GITHUB_STEP_SUMMARY
          echo "| Code Quality | ${{ needs.quality.result == 'success' && '✅' || '❌' }} | ${{ needs.quality.outputs.lint-score || 'N/A' }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Unit Tests | ${{ needs.test-unit.result == 'success' && '✅' || '❌' }} | ${{ needs.test-unit.result == 'success' && '100' || '0' }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Build | ${{ needs.build.result == 'success' && '✅' || '❌' }} | ${{ needs.build.result == 'success' && '100' || '0' }} |" >> $GITHUB_STEP_SUMMARY
          echo "| E2E Tests | ${{ needs.test-e2e.result == 'success' && '✅' || '❌' }} | ${{ needs.test-e2e.result == 'success' && '100' || '0' }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Performance | ${{ needs.performance.result == 'success' && '✅' || '❌' }} | ${{ needs.performance.result == 'success' && '100' || '0' }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Security | ${{ needs.security.result == 'success' && '✅' || '❌' }} | ${{ needs.security.outputs.security-score || 'N/A' }} |" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "## 🔒 Security Summary" >> $GITHUB_STEP_SUMMARY
          echo "- Vulnerabilities Found: ${{ needs.security.outputs.vulnerabilities || 0 }}" >> $GITHUB_STEP_SUMMARY
          echo "- Security Score: ${{ needs.security.outputs.security-score || 'N/A' }}/100" >> $GITHUB_STEP_SUMMARY
          
      - name: 🎯 Quality gate decision
        id: gate-decision
        run: |
          OVERALL_SCORE=${{ steps.calculate-score.outputs.overall-score }}
          CRITICAL_FAILURES=0
          
          # Check for critical failures
          if [[ "${{ needs.quality.result }}" == "failure" ]]; then
            echo "❌ Critical failure: Code quality checks failed"
            CRITICAL_FAILURES=$((CRITICAL_FAILURES + 1))
          fi
          
          if [[ "${{ needs.test-unit.result }}" == "failure" ]]; then
            echo "❌ Critical failure: Unit tests failed"
            CRITICAL_FAILURES=$((CRITICAL_FAILURES + 1))
          fi
          
          if [[ "${{ needs.build.result }}" == "failure" ]]; then
            echo "❌ Critical failure: Build failed"
            CRITICAL_FAILURES=$((CRITICAL_FAILURES + 1))
          fi
          
          if [[ "${{ needs.test-e2e.result }}" == "failure" ]]; then
            echo "❌ Critical failure: E2E tests failed"
            CRITICAL_FAILURES=$((CRITICAL_FAILURES + 1))
          fi
          
          # Quality gate criteria
          if [[ $CRITICAL_FAILURES -gt 0 ]]; then
            echo "❌ Quality gate FAILED - Critical failures detected"
            echo "gate-passed=false" >> $GITHUB_OUTPUT
            exit 1
          elif [[ $OVERALL_SCORE -lt 70 ]]; then
            echo "❌ Quality gate FAILED - Overall score below threshold (70)"
            echo "gate-passed=false" >> $GITHUB_OUTPUT
            exit 1
          elif [[ "${{ needs.security.outputs.vulnerabilities || 0 }}" -gt 5 ]]; then
            echo "❌ Quality gate FAILED - Too many security vulnerabilities"
            echo "gate-passed=false" >> $GITHUB_OUTPUT
            exit 1
          else
            echo "✅ Quality gate PASSED - All checks successful"
            echo "gate-passed=true" >> $GITHUB_OUTPUT
          fi
          
      - name: 📢 Quality gate notification
        if: always()
        run: |
          if [ "${{ steps.gate-decision.outputs.gate-passed }}" == "true" ]; then
            echo "🎉 Quality gate passed! Score: ${{ steps.calculate-score.outputs.overall-score }}/100"
          else
            echo "💥 Quality gate failed! Score: ${{ steps.calculate-score.outputs.overall-score }}/100"
          fi