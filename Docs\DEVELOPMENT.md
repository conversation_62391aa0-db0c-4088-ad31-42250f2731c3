# 🛠️ Development Guide

*Comprehensive guide for developers working on Nirmaan AI Construction Calculator*

## 🎯 Overview

This guide provides detailed instructions for setting up the development environment, understanding the codebase architecture, and contributing to the Nirmaan AI Construction Calculator project.

## 🏗️ Architecture Overview

### Technology Stack

**Frontend Architecture**
```
┌─────────────────────────────────────────┐
│                 Browser                 │
├─────────────────────────────────────────┤
│            Next.js 15 (React)          │
├─────────────────────────────────────────┤
│        Tailwind CSS + shadcn/ui        │
├─────────────────────────────────────────┤
│         TypeScript + Zod               │
└─────────────────────────────────────────┘
```

**Backend Architecture**
```
┌─────────────────────────────────────────┐
│              Vercel Edge                │
├─────────────────────────────────────────┤
│           Next.js API Routes           │
├─────────────────────────────────────────┤
│         Supabase (PostgreSQL)          │
├─────────────────────────────────────────┤
│           Row Level Security            │
└─────────────────────────────────────────┘
```

### Project Structure

```
nirmaan-ai/
├── .github/                     # GitHub workflows and templates
│   ├── workflows/              # CI/CD pipelines
│   │   ├── ci.yml              # Continuous integration
│   │   ├── test.yml            # Comprehensive testing
│   │   ├── deploy.yml          # Production deployment
│   │   └── monitoring.yml      # Health monitoring
│   └── ISSUE_TEMPLATE/         # Issue templates
├── public/                     # Static assets
│   ├── icons/                  # Application icons
│   ├── images/                 # Static images
│   └── manifest.json           # PWA manifest
├── src/                        # Source code
│   ├── app/                    # Next.js App Router
│   │   ├── (auth)/            # Authentication pages
│   │   ├── calculator/        # Calculator page
│   │   ├── api/               # API routes
│   │   │   ├── calculate/     # Cost calculation endpoints
│   │   │   ├── projects/      # Project management
│   │   │   ├── health/        # Health check
│   │   │   └── monitoring/    # System monitoring
│   │   ├── globals.css        # Global styles
│   │   ├── layout.tsx         # Root layout
│   │   └── page.tsx           # Home page
│   ├── components/            # Reusable components
│   │   ├── ui/               # Base UI components
│   │   │   ├── button.tsx     # Button component
│   │   │   ├── input.tsx      # Input component
│   │   │   ├── card.tsx       # Card component
│   │   │   └── index.ts       # Component exports
│   │   ├── calculator/       # Calculator-specific components
│   │   │   ├── CalculatorContainer.tsx
│   │   │   ├── QualityTierSelector.tsx
│   │   │   ├── LocationSelector.tsx
│   │   │   └── ResultsDisplay.tsx
│   │   ├── layout/           # Layout components
│   │   │   ├── Header.tsx     # Navigation header
│   │   │   ├── Footer.tsx     # Site footer
│   │   │   └── Container.tsx  # Page container
│   │   └── forms/            # Form components
│   ├── lib/                  # Utility libraries
│   │   ├── supabase/         # Database client
│   │   │   ├── client.ts     # Client-side instance
│   │   │   ├── server.ts     # Server-side instance
│   │   │   └── service.ts    # Database services
│   │   ├── calculations/     # Calculation engine
│   │   │   ├── engine.ts     # Core calculation logic
│   │   │   ├── materials/    # Material calculations
│   │   │   └── validation.ts # Input validation
│   │   ├── materials/        # Material data management
│   │   │   ├── loader.ts     # Data loading utilities
│   │   │   └── consumption.ts # Consumption calculations
│   │   ├── utils.ts          # General utilities
│   │   └── validation/       # Schema validation
│   ├── hooks/                # Custom React hooks
│   │   ├── useCalculator.ts  # Calculator state management
│   │   ├── useProjects.ts    # Project management
│   │   └── usePDFExport.ts   # PDF generation
│   ├── types/                # TypeScript definitions
│   │   ├── materials.ts      # Material type definitions
│   │   ├── supabase.ts       # Database types
│   │   └── calculator.ts     # Calculator types
│   ├── stores/               # State management
│   │   └── calculator.ts     # Zustand store
│   └── styles/               # Styling utilities
├── tests/                    # Test files
│   ├── e2e/                  # End-to-end tests
│   ├── integration/          # Integration tests
│   └── __mocks__/           # Test mocks
├── docs/                     # Documentation
├── supabase/                # Database configuration
│   ├── migrations/          # Database migrations
│   └── config.toml         # Supabase configuration
└── data/                    # Static data files
    ├── materials/           # Material specifications
    └── locations/           # Location data
```

## 🚀 Development Setup

### Prerequisites

Ensure you have the following installed:

```bash
# Node.js (v18 or higher)
node --version  # Should be 18.0.0+

# npm (v9 or higher)
npm --version   # Should be 9.0.0+

# Git
git --version
```

### Environment Setup

1. **Clone the Repository**
```bash
git clone https://github.com/nirmaan-ai/construction-calculator.git
cd construction-calculator
```

2. **Install Dependencies**
```bash
npm install
```

3. **Environment Configuration**
```bash
# Copy environment template
cp .env.example .env.local

# Edit with your configurations
nano .env.local
```

**Required Environment Variables:**
```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Optional: Development Tools
NEXT_PUBLIC_VERCEL_URL=http://localhost:3000
NEXTAUTH_SECRET=your-auth-secret
NEXTAUTH_URL=http://localhost:3000

# Optional: Analytics (Development)
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=G-XXXXXXXXXX
NEXT_PUBLIC_HOTJAR_ID=your-hotjar-id
```

4. **Database Setup**
```bash
# Start Supabase locally (optional)
npx supabase start

# Run migrations
npx supabase db reset

# Generate TypeScript types
npx supabase gen types typescript --local > src/types/supabase.ts
```

5. **Start Development Server**
```bash
npm run dev
```

Visit `http://localhost:3000` to see the application.

### Development Commands

```bash
# Development
npm run dev              # Start development server
npm run build            # Build for production
npm run start            # Start production server
npm run lint             # Run ESLint
npm run lint:fix         # Fix linting issues
npm run type-check       # TypeScript type checking
npm run format           # Format code with Prettier

# Testing
npm run test             # Run unit tests
npm run test:watch       # Run tests in watch mode
npm run test:coverage    # Generate coverage report
npm run test:e2e         # Run end-to-end tests
npm run test:integration # Run integration tests

# Database
npm run db:reset         # Reset local database
npm run db:seed          # Seed with sample data
npm run db:migrate       # Run pending migrations
npm run db:generate      # Generate TypeScript types

# Analysis & Optimization
npm run analyze          # Bundle analysis
npm run lighthouse       # Performance audit
npm run size-limit       # Check bundle size limits
```

## 🧪 Testing Strategy

### Testing Pyramid

```
           🔺 E2E Tests (10%)
         🔺🔺 Integration Tests (20%)
     🔺🔺🔺🔺 Unit Tests (70%)
```

### Unit Testing

**Framework**: Jest + React Testing Library

**Test Structure:**
```typescript
// Example: components/calculator/__tests__/CalculatorContainer.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { CalculatorContainer } from '../CalculatorContainer';

describe('CalculatorContainer', () => {
  it('should render calculator form', () => {
    render(<CalculatorContainer />);
    
    expect(screen.getByText('Construction Calculator')).toBeInTheDocument();
    expect(screen.getByLabelText('Location')).toBeInTheDocument();
    expect(screen.getByLabelText('Area (sq ft)')).toBeInTheDocument();
  });

  it('should calculate cost on form submission', async () => {
    render(<CalculatorContainer />);
    
    fireEvent.change(screen.getByLabelText('Location'), {
      target: { value: 'bangalore' }
    });
    fireEvent.change(screen.getByLabelText('Area (sq ft)'), {
      target: { value: '1000' }
    });
    fireEvent.click(screen.getByText('Calculate'));

    await expect(screen.findByText(/Total Cost:/)).toBeInTheDocument();
  });
});
```

**Running Unit Tests:**
```bash
# Run all unit tests
npm run test

# Run specific test file
npm run test CalculatorContainer.test.tsx

# Run tests in watch mode
npm run test:watch

# Generate coverage report
npm run test:coverage
```

### Integration Testing

**Focus Areas:**
- API endpoint integration
- Database operations
- Third-party service integration
- Component interaction workflows

**Example Integration Test:**
```typescript
// tests/integration/api-integration.test.ts
import { createMocks } from 'node-mocks-http';
import handler from '../../src/app/api/calculate/route';

describe('/api/calculate', () => {
  it('should return cost calculation', async () => {
    const { req, res } = createMocks({
      method: 'POST',
      body: {
        location: 'bangalore',
        area: 1000,
        quality_tier: 'premium'
      }
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(200);
    
    const data = JSON.parse(res._getData());
    expect(data.total_cost).toBeGreaterThan(0);
    expect(data.cost_per_sqft).toBe(2500);
  });
});
```

### End-to-End Testing

**Framework**: Playwright

**Test Structure:**
```typescript
// tests/e2e/calculator.spec.ts
import { test, expect } from '@playwright/test';

test.describe('Calculator Workflow', () => {
  test('should complete full calculation workflow', async ({ page }) => {
    await page.goto('/calculator');

    // Fill form
    await page.selectOption('[data-testid="location-select"]', 'bangalore');
    await page.fill('[data-testid="area-input"]', '1000');
    await page.click('[data-testid="quality-premium"]');

    // Submit calculation
    await page.click('[data-testid="calculate-button"]');

    // Verify results
    await expect(page.locator('[data-testid="total-cost"]')).toContainText('₹25,00,000');
    await expect(page.locator('[data-testid="cost-breakdown"]')).toBeVisible();
  });

  test('should generate PDF report', async ({ page }) => {
    // ... calculation setup ...

    const downloadPromise = page.waitForEvent('download');
    await page.click('[data-testid="pdf-export-button"]');
    const download = await downloadPromise;

    expect(download.suggestedFilename()).toMatch(/construction-estimate-.*\.pdf/);
  });
});
```

**Running E2E Tests:**
```bash
# Run all E2E tests
npm run test:e2e

# Run in headed mode (see browser)
npm run test:e2e -- --headed

# Run specific test file
npm run test:e2e calculator.spec.ts

# Generate test report
npm run test:e2e -- --reporter=html
```

## 🎨 Code Style & Standards

### TypeScript Configuration

**tsconfig.json Key Settings:**
```json
{
  "compilerOptions": {
    "strict": true,
    "noUncheckedIndexedAccess": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true
  }
}
```

### ESLint Configuration

**Key Rules:**
- `@typescript-eslint/strict` - Strict TypeScript rules
- `react-hooks/exhaustive-deps` - Hook dependency checking
- `import/order` - Import statement ordering
- `@next/next/no-img-element` - Use Next.js Image component

### Code Formatting

**Prettier Configuration:**
```json
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2
}
```

### Naming Conventions

**Files and Directories:**
- Components: `PascalCase.tsx`
- Hooks: `camelCase.ts`
- Utilities: `camelCase.ts`
- Constants: `UPPER_SNAKE_CASE.ts`
- Directories: `kebab-case`

**Variables and Functions:**
- Variables: `camelCase`
- Constants: `UPPER_SNAKE_CASE`
- Functions: `camelCase`
- Components: `PascalCase`
- Types/Interfaces: `PascalCase`

**Example:**
```typescript
// ✅ Good
const calculateTotalCost = (area: number): number => { ... };
const QUALITY_TIERS = ['smart', 'premium', 'luxury'] as const;
interface CalculationResult { ... }
type QualityTier = typeof QUALITY_TIERS[number];

// ❌ Bad
const Calculate_total_cost = (area: number): number => { ... };
const qualityTiers = ['smart', 'premium', 'luxury'] as const;
interface calculationResult { ... }
```

## 🔧 Component Development

### Component Structure

**Standard Component Template:**
```typescript
// components/calculator/QualityTierSelector.tsx
import React from 'react';
import { cn } from '@/lib/utils';

interface QualityTierSelectorProps {
  value: QualityTier;
  onChange: (tier: QualityTier) => void;
  className?: string;
}

export const QualityTierSelector: React.FC<QualityTierSelectorProps> = ({
  value,
  onChange,
  className
}) => {
  return (
    <div className={cn('space-y-4', className)}>
      {/* Component implementation */}
    </div>
  );
};

QualityTierSelector.displayName = 'QualityTierSelector';
```

### Design System Usage

**Using shadcn/ui Components:**
```typescript
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';

// Consistent spacing and styling
<Card className="w-full max-w-md">
  <CardHeader>
    <CardTitle>Calculator</CardTitle>
  </CardHeader>
  <CardContent className="space-y-4">
    <Input placeholder="Enter area" />
    <Button className="w-full">Calculate</Button>
  </CardContent>
</Card>
```

### State Management

**Using Zustand Store:**
```typescript
// stores/calculator.ts
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

interface CalculatorState {
  location: string;
  area: number;
  qualityTier: QualityTier;
  results: CalculationResult | null;
  setLocation: (location: string) => void;
  setArea: (area: number) => void;
  setQualityTier: (tier: QualityTier) => void;
  calculate: () => Promise<void>;
}

export const useCalculatorStore = create<CalculatorState>()(
  devtools(
    (set, get) => ({
      location: '',
      area: 0,
      qualityTier: 'premium',
      results: null,
      
      setLocation: (location) => set({ location }),
      setArea: (area) => set({ area }),
      setQualityTier: (qualityTier) => set({ qualityTier }),
      
      calculate: async () => {
        const { location, area, qualityTier } = get();
        // Calculation logic
      }
    }),
    { name: 'calculator-store' }
  )
);
```

### Custom Hooks

**Hook Development Pattern:**
```typescript
// hooks/useCalculator.ts
import { useState, useCallback } from 'react';
import { calculateCost } from '@/lib/calculations';

export const useCalculator = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const calculate = useCallback(async (params: CalculationParams) => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await calculateCost(params);
      return result;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Calculation failed');
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  return { calculate, loading, error };
};
```

## 🗄️ Database Development

### Schema Design

**Migration Example:**
```sql
-- supabase/migrations/001_initial_schema.sql
CREATE TABLE projects (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  location TEXT NOT NULL,
  area INTEGER NOT NULL,
  quality_tier TEXT NOT NULL CHECK (quality_tier IN ('smart', 'premium', 'luxury')),
  total_cost INTEGER NOT NULL,
  calculation_data JSONB NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Row Level Security
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;

-- Policies
CREATE POLICY "Users can view own projects" ON projects
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create own projects" ON projects
  FOR INSERT WITH CHECK (auth.uid() = user_id);
```

### Database Service Layer

**Service Pattern:**
```typescript
// lib/supabase/service.ts
import { supabase } from './client';

export class ProjectService {
  static async create(project: CreateProjectParams): Promise<Project> {
    const { data, error } = await supabase
      .from('projects')
      .insert([project])
      .select()
      .single();

    if (error) throw new Error(error.message);
    return data;
  }

  static async getByUserId(userId: string): Promise<Project[]> {
    const { data, error } = await supabase
      .from('projects')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) throw new Error(error.message);
    return data || [];
  }
}
```

## 🚀 API Development

### API Route Structure

**Next.js API Route Example:**
```typescript
// app/api/calculate/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { calculateCost } from '@/lib/calculations';

const CalculateRequestSchema = z.object({
  location: z.string().min(1),
  area: z.number().min(200).max(50000),
  quality_tier: z.enum(['smart', 'premium', 'luxury'])
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const params = CalculateRequestSchema.parse(body);
    
    const result = await calculateCost(params);
    
    return NextResponse.json({
      success: true,
      data: result
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid input parameters' },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
```

### Error Handling

**Centralized Error Handler:**
```typescript
// lib/errors.ts
export class CalculationError extends Error {
  constructor(message: string, public code: string) {
    super(message);
    this.name = 'CalculationError';
  }
}

export const handleApiError = (error: unknown) => {
  if (error instanceof CalculationError) {
    return NextResponse.json(
      { success: false, error: error.message, code: error.code },
      { status: 400 }
    );
  }
  
  if (error instanceof z.ZodError) {
    return NextResponse.json(
      { success: false, error: 'Validation failed', details: error.errors },
      { status: 400 }
    );
  }
  
  console.error('Unexpected error:', error);
  return NextResponse.json(
    { success: false, error: 'Internal server error' },
    { status: 500 }
  );
};
```

## 📊 Performance Optimization

### Bundle Optimization

**Dynamic Imports:**
```typescript
// Lazy load heavy components
const PDFViewer = dynamic(() => import('./PDFViewer'), {
  ssr: false,
  loading: () => <div>Loading PDF viewer...</div>
});

// Code splitting for routes
const Calculator = dynamic(() => import('./Calculator'), {
  loading: () => <CalculatorSkeleton />
});
```

**Image Optimization:**
```typescript
import Image from 'next/image';

// Optimized images
<Image
  src="/images/hero.jpg"
  alt="Construction site"
  width={800}
  height={600}
  priority // For above-the-fold images
  placeholder="blur"
  blurDataURL="data:image/jpeg;base64,..."
/>
```

### Caching Strategy

**React Query Implementation:**
```typescript
// hooks/useProjects.ts
import { useQuery } from '@tanstack/react-query';

export const useProjects = () => {
  return useQuery({
    queryKey: ['projects'],
    queryFn: () => ProjectService.getByUserId(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000,   // 10 minutes
  });
};
```

### Memory Management

**Cleanup Patterns:**
```typescript
useEffect(() => {
  const controller = new AbortController();
  
  fetchData(controller.signal)
    .then(setData)
    .catch(error => {
      if (!controller.signal.aborted) {
        setError(error);
      }
    });
  
  return () => controller.abort();
}, []);
```

## 🔧 Debugging

### Development Tools

**Browser DevTools Setup:**
1. Install React Developer Tools
2. Install Zustand DevTools
3. Enable Next.js DevTools

**VS Code Extensions:**
- ES7+ React/Redux/React-Native snippets
- TypeScript Importer
- Tailwind CSS IntelliSense
- ESLint
- Prettier

### Logging Strategy

**Development Logging:**
```typescript
// lib/logger.ts
const logger = {
  debug: (message: string, data?: any) => {
    if (process.env.NODE_ENV === 'development') {
      console.log(`[DEBUG] ${message}`, data);
    }
  },
  
  error: (message: string, error?: Error) => {
    console.error(`[ERROR] ${message}`, error);
    // Send to error tracking service in production
  }
};
```

**Error Boundary:**
```typescript
// components/ErrorBoundary.tsx
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    logger.error('Component error caught:', error);
  }

  render() {
    if (this.state.hasError) {
      return <ErrorFallback />;
    }

    return this.props.children;
  }
}
```

## 🔒 Security Best Practices

### Input Validation

**Zod Schema Validation:**
```typescript
const ProjectSchema = z.object({
  name: z.string().min(1).max(100),
  location: z.enum(SUPPORTED_LOCATIONS),
  area: z.number().min(200).max(50000),
  quality_tier: z.enum(['smart', 'premium', 'luxury'])
});
```

### Authentication Guards

**API Route Protection:**
```typescript
// lib/auth/middleware.ts
export const withAuth = (handler: ApiHandler) => {
  return async (req: NextRequest) => {
    const token = req.headers.get('authorization')?.replace('Bearer ', '');
    
    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    try {
      const { data: { user } } = await supabase.auth.getUser(token);
      if (!user) throw new Error('Invalid token');
      
      return handler(req, user);
    } catch (error) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
  };
};
```

### Data Sanitization

**Input Cleaning:**
```typescript
import DOMPurify from 'isomorphic-dompurify';

const sanitizeInput = (input: string): string => {
  return DOMPurify.sanitize(input, { ALLOWED_TAGS: [] });
};
```

## 📈 Monitoring & Analytics

### Performance Monitoring

**Web Vitals Tracking:**
```typescript
// lib/performance/web-vitals.ts
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals';

const vitalsUrl = 'https://vitals.vercel-analytics.com/v1/vitals';

function getConnectionSpeed() {
  return (navigator as any)?.connection?.effectiveType || '';
}

function sendToAnalytics(metric: any) {
  const body = JSON.stringify({
    dsn: process.env.NEXT_PUBLIC_VERCEL_ANALYTICS_ID,
    id: metric.id,
    page: window.location.pathname,
    href: window.location.href,
    event_name: metric.name,
    value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
    speed: getConnectionSpeed(),
  });

  fetch(vitalsUrl, { body, method: 'POST', keepalive: true });
}

export function reportWebVitals() {
  getCLS(sendToAnalytics);
  getFID(sendToAnalytics);
  getFCP(sendToAnalytics);
  getLCP(sendToAnalytics);
  getTTFB(sendToAnalytics);
}
```

### Error Tracking

**Sentry Integration:**
```typescript
// lib/monitoring/sentry.ts
import * as Sentry from '@sentry/nextjs';

Sentry.init({
  dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,
  environment: process.env.NODE_ENV,
  tracesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,
});

export const captureException = (error: Error, context?: any) => {
  Sentry.captureException(error, { extra: context });
};
```

## 🚀 Deployment

### Build Process

**Production Build:**
```bash
# Build optimization
npm run build

# Check build output
npm run start

# Analyze bundle
npm run analyze
```

**Environment-Specific Builds:**
```json
{
  "scripts": {
    "build:staging": "NODE_ENV=staging next build",
    "build:production": "NODE_ENV=production next build"
  }
}
```

### Vercel Deployment

**vercel.json Configuration:**
```json
{
  "framework": "nextjs",
  "buildCommand": "npm run build",
  "devCommand": "npm run dev",
  "installCommand": "npm install",
  "functions": {
    "app/api/**/*.ts": {
      "maxDuration": 30
    }
  },
  "headers": [
    {
      "source": "/(.*)",
      "headers": [
        {
          "key": "X-Content-Type-Options",
          "value": "nosniff"
        },
        {
          "key": "X-Frame-Options",
          "value": "DENY"
        }
      ]
    }
  ]
}
```

## 🤝 Contributing

### Git Workflow

**Branch Naming:**
- Feature: `feature/calculator-improvements`
- Bug fix: `fix/calculation-precision`
- Hotfix: `hotfix/critical-security-fix`
- Documentation: `docs/api-documentation`

**Commit Messages:**
```
feat: add PDF export functionality
fix: resolve calculation precision issue
docs: update API documentation
style: format code according to prettier
refactor: simplify calculator component logic
test: add unit tests for cost calculation
perf: optimize bundle size
```

### Pull Request Process

1. **Create Feature Branch**
```bash
git checkout -b feature/new-calculator-feature
```

2. **Make Changes and Test**
```bash
# Make your changes
npm run test
npm run lint
npm run type-check
```

3. **Commit Changes**
```bash
git add .
git commit -m "feat: add new calculator feature"
```

4. **Push and Create PR**
```bash
git push origin feature/new-calculator-feature
```

5. **PR Checklist**
- [ ] All tests pass
- [ ] Code follows style guidelines
- [ ] Documentation updated
- [ ] Screenshots for UI changes
- [ ] Breaking changes documented

### Code Review Guidelines

**Reviewer Checklist:**
- [ ] Code follows project conventions
- [ ] Tests cover new functionality
- [ ] Performance implications considered
- [ ] Security vulnerabilities checked
- [ ] Documentation is clear and complete

---

## 📞 Support

### Getting Help

- **Documentation**: Check this guide and other docs
- **Issues**: Create GitHub issue for bugs
- **Discussions**: Use GitHub Discussions for questions
- **Slack**: Join our development channel

### Development Resources

- **Design System**: [Storybook](http://localhost:6006)
- **API Documentation**: [OpenAPI Spec](./API.md)
- **Database Schema**: [Supabase Dashboard](https://app.supabase.com)
- **Performance**: [Lighthouse Reports](https://pagespeed.web.dev)

---

*This development guide is regularly updated. For the latest version, check our [GitHub repository](https://github.com/nirmaan-ai/construction-calculator)*