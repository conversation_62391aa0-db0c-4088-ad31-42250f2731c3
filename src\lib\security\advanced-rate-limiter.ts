/**
 * Advanced Rate Limiting System
 * Multi-tier rate limiting with intelligent adaptation and threat detection
 */

interface RateLimitConfig {
  windowMs: number;
  maxRequests: number;
  burstLimit?: number;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
  blockDuration?: number;
  whitelist?: string[];
  blacklist?: string[];
}

interface RateLimitTier {
  name: string;
  config: RateLimitConfig;
  matcher: (req: any) => boolean;
  priority: number;
}

interface RateLimitResult {
  success: boolean;
  limit: number;
  remaining: number;
  resetTime: number;
  retryAfter?: number;
  tier: string;
  blocked?: boolean;
  blockExpiry?: number;
}

interface RequestInfo {
  ip: string;
  path: string;
  method: string;
  userAgent: string;
  timestamp: number;
  success: boolean;
  responseTime?: number;
}

interface ClientStats {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  lastRequest: number;
  suspiciousActivity: number;
  blockExpiry?: number;
  tier: string;
}

export class AdvancedRateLimiter {
  private store: Map<string, { count: number; resetTime: number; requests: RequestInfo[] }> = new Map();
  private clientStats: Map<string, ClientStats> = new Map();
  private blockedIPs: Map<string, number> = new Map();
  private tiers: RateLimitTier[];
  private cleanupInterval: NodeJS.Timeout | undefined;

  constructor() {
    this.tiers = this.initializeTiers();
    this.startCleanupTask();
  }

  private initializeTiers(): RateLimitTier[] {
    return [
      // VIP Tier - Authenticated premium users
      {
        name: 'vip',
        priority: 1,
        config: {
          windowMs: 60 * 1000, // 1 minute
          maxRequests: 1000,
          burstLimit: 200,
          skipSuccessfulRequests: false,
          blockDuration: 5 * 60 * 1000, // 5 minutes
        },
        matcher: (req) => this.isVIPUser(req),
      },
      
      // Premium Tier - Authenticated users
      {
        name: 'premium',
        priority: 2,
        config: {
          windowMs: 60 * 1000, // 1 minute
          maxRequests: 300,
          burstLimit: 60,
          skipSuccessfulRequests: false,
          blockDuration: 10 * 60 * 1000, // 10 minutes
        },
        matcher: (req) => this.isPremiumUser(req),
      },

      // Standard Tier - Regular authenticated users
      {
        name: 'standard',
        priority: 3,
        config: {
          windowMs: 60 * 1000, // 1 minute
          maxRequests: 100,
          burstLimit: 20,
          skipSuccessfulRequests: false,
          blockDuration: 15 * 60 * 1000, // 15 minutes
        },
        matcher: (req) => this.isAuthenticatedUser(req),
      },

      // Public API Tier - High rate limits for public endpoints
      {
        name: 'public-api',
        priority: 4,
        config: {
          windowMs: 60 * 1000, // 1 minute
          maxRequests: 150,
          burstLimit: 30,
          skipSuccessfulRequests: true,
          blockDuration: 30 * 60 * 1000, // 30 minutes
        },
        matcher: (req) => this.isPublicAPIEndpoint(req),
      },

      // Calculator Tier - Specific for calculation endpoints
      {
        name: 'calculator',
        priority: 5,
        config: {
          windowMs: 60 * 1000, // 1 minute
          maxRequests: 60,
          burstLimit: 15,
          skipSuccessfulRequests: false,
          blockDuration: 20 * 60 * 1000, // 20 minutes
        },
        matcher: (req) => this.isCalculatorEndpoint(req),
      },

      // Anonymous Tier - Unauthenticated users
      {
        name: 'anonymous',
        priority: 6,
        config: {
          windowMs: 60 * 1000, // 1 minute
          maxRequests: 30,
          burstLimit: 10,
          skipSuccessfulRequests: false,
          blockDuration: 60 * 60 * 1000, // 1 hour
        },
        matcher: (req) => true, // Default catch-all
      },

      // Suspicious Tier - Detected suspicious activity
      {
        name: 'suspicious',
        priority: 7,
        config: {
          windowMs: 60 * 1000, // 1 minute
          maxRequests: 5,
          burstLimit: 2,
          skipSuccessfulRequests: false,
          blockDuration: 4 * 60 * 60 * 1000, // 4 hours
        },
        matcher: (req) => this.isSuspiciousClient(req),
      },

      // Bot Tier - Detected bots (very restrictive)
      {
        name: 'bot',
        priority: 8,
        config: {
          windowMs: 60 * 1000, // 1 minute
          maxRequests: 2,
          burstLimit: 1,
          skipSuccessfulRequests: false,
          blockDuration: 24 * 60 * 60 * 1000, // 24 hours
        },
        matcher: (req) => this.isBotRequest(req),
      },
    ];
  }

  /**
   * Main rate limiting check
   */
  async check(req: any): Promise<RateLimitResult> {
    const ip = this.extractIP(req);
    const now = Date.now();

    // Check if IP is currently blocked
    const blockExpiry = this.blockedIPs.get(ip);
    if (blockExpiry && now < blockExpiry) {
      return {
        success: false,
        limit: 0,
        remaining: 0,
        resetTime: blockExpiry,
        retryAfter: Math.ceil((blockExpiry - now) / 1000),
        tier: 'blocked',
        blocked: true,
        blockExpiry,
      };
    }

    // Determine appropriate tier
    const tier = this.determineTier(req);
    const key = `${ip}:${tier.name}`;
    
    // Get or create client entry
    let entry = this.store.get(key);
    if (!entry || now >= entry.resetTime) {
      entry = {
        count: 0,
        resetTime: now + tier.config.windowMs,
        requests: [],
      };
      this.store.set(key, entry);
    }

    // Record request info
    const requestInfo: RequestInfo = {
      ip,
      path: req.nextUrl?.pathname || req.url,
      method: req.method,
      userAgent: req.headers.get('user-agent') || '',
      timestamp: now,
      success: true, // Will be updated after response
    };

    // Update client statistics
    this.updateClientStats(ip, requestInfo, tier.name);

    // Check burst limit
    const recentRequests = entry.requests.filter(r => now - r.timestamp < 5000); // 5 seconds
    if (tier.config.burstLimit && recentRequests.length >= tier.config.burstLimit) {
      this.handleRateLimitViolation(ip, tier, 'burst');
      return {
        success: false,
        limit: tier.config.maxRequests,
        remaining: 0,
        resetTime: entry.resetTime,
        retryAfter: 5,
        tier: tier.name,
      };
    }

    // Check main rate limit
    if (entry.count >= tier.config.maxRequests) {
      this.handleRateLimitViolation(ip, tier, 'rate_limit');
      return {
        success: false,
        limit: tier.config.maxRequests,
        remaining: 0,
        resetTime: entry.resetTime,
        retryAfter: Math.ceil((entry.resetTime - now) / 1000),
        tier: tier.name,
      };
    }

    // Increment counter and record request
    entry.count++;
    entry.requests.push(requestInfo);

    // Cleanup old requests (keep only last 100)
    if (entry.requests.length > 100) {
      entry.requests = entry.requests.slice(-100);
    }

    return {
      success: true,
      limit: tier.config.maxRequests,
      remaining: tier.config.maxRequests - entry.count,
      resetTime: entry.resetTime,
      tier: tier.name,
    };
  }

  /**
   * Record successful or failed request for analytics
   */
  recordRequestResult(req: any, success: boolean, responseTime?: number): void {
    const ip = this.extractIP(req);
    const stats = this.clientStats.get(ip);
    
    if (stats) {
      if (success) {
        stats.successfulRequests++;
      } else {
        stats.failedRequests++;
        stats.suspiciousActivity++;
      }
      
      if (responseTime) {
        stats.averageResponseTime = 
          (stats.averageResponseTime * (stats.totalRequests - 1) + responseTime) / stats.totalRequests;
      }
    }
  }

  /**
   * Determine the appropriate tier for a request
   */
  private determineTier(req: any): RateLimitTier {
    // Sort tiers by priority and find the first match
    const sortedTiers = [...this.tiers].sort((a, b) => a.priority - b.priority);
    
    for (const tier of sortedTiers) {
      if (tier.matcher(req)) {
        return tier;
      }
    }
    
    // Fallback to anonymous tier
    return this.tiers.find(t => t.name === 'anonymous')!;
  }

  /**
   * Handle rate limit violations
   */
  private handleRateLimitViolation(ip: string, tier: RateLimitTier, type: string): void {
    const stats = this.clientStats.get(ip);
    if (stats) {
      stats.suspiciousActivity++;
      
      // Progressive blocking for repeat offenders
      if (stats.suspiciousActivity >= 5) {
        const blockDuration = tier.config.blockDuration! * Math.pow(2, Math.min(stats.suspiciousActivity - 5, 3));
        this.blockedIPs.set(ip, Date.now() + blockDuration);
        stats.blockExpiry = Date.now() + blockDuration;
      }
    }

    console.warn(`Rate limit violation: ${type} from ${ip}, tier: ${tier.name}`);
  }

  /**
   * Update client statistics
   */
  private updateClientStats(ip: string, request: RequestInfo, tier: string): void {
    let stats = this.clientStats.get(ip);
    
    if (!stats) {
      stats = {
        totalRequests: 0,
        successfulRequests: 0,
        failedRequests: 0,
        averageResponseTime: 0,
        lastRequest: 0,
        suspiciousActivity: 0,
        tier,
      };
      this.clientStats.set(ip, stats);
    }
    
    stats.totalRequests++;
    stats.lastRequest = request.timestamp;
    stats.tier = tier;
  }

  /**
   * Extract IP address from request
   */
  private extractIP(req: any): string {
    return req.ip || 
           req.headers.get('x-forwarded-for')?.split(',')[0]?.trim() ||
           req.headers.get('x-real-ip') ||
           req.headers.get('cf-connecting-ip') ||
           req.connection?.remoteAddress ||
           'unknown';
  }

  /**
   * Tier detection methods
   */
  private isVIPUser(req: any): boolean {
    // Check for VIP user indicators (premium subscription, etc.)
    const auth = req.headers.get('authorization');
    // Implement VIP user detection logic
    return false; // Placeholder
  }

  private isPremiumUser(req: any): boolean {
    // Check for premium user indicators
    const auth = req.headers.get('authorization');
    // Implement premium user detection logic
    return false; // Placeholder
  }

  private isAuthenticatedUser(req: any): boolean {
    const auth = req.headers.get('authorization');
    return !!auth && auth.startsWith('Bearer ');
  }

  private isPublicAPIEndpoint(req: any): boolean {
    const path = req.nextUrl?.pathname || req.url;
    return path.startsWith('/api/public/') || 
           path.startsWith('/api/health') ||
           path.startsWith('/api/status');
  }

  private isCalculatorEndpoint(req: any): boolean {
    const path = req.nextUrl?.pathname || req.url;
    return path.startsWith('/api/calculate') ||
           path.startsWith('/api/materials') ||
           path.startsWith('/api/pricing');
  }

  private isSuspiciousClient(req: any): boolean {
    const ip = this.extractIP(req);
    const stats = this.clientStats.get(ip);
    
    if (!stats) return false;
    
    // Check for suspicious patterns
    const suspiciousIndicators = [
      stats.suspiciousActivity > 3,
      stats.failedRequests / stats.totalRequests > 0.5,
      stats.averageResponseTime > 10000, // Very slow responses
    ];
    
    return suspiciousIndicators.some(Boolean);
  }

  private isBotRequest(req: any): boolean {
    const userAgent = req.headers.get('user-agent') || '';
    const botPatterns = [
      /bot/i,
      /crawler/i,
      /spider/i,
      /scraper/i,
      /automated/i,
      /curl/i,
      /wget/i,
      /python-requests/i,
      /postman/i,
    ];
    
    return botPatterns.some(pattern => pattern.test(userAgent));
  }

  /**
   * Administrative methods
   */
  
  /**
   * Manually block an IP
   */
  blockIP(ip: string, duration: number = 24 * 60 * 60 * 1000): void {
    this.blockedIPs.set(ip, Date.now() + duration);
    console.warn(`Manually blocked IP: ${ip} for ${duration}ms`);
  }

  /**
   * Unblock an IP
   */
  unblockIP(ip: string): void {
    this.blockedIPs.delete(ip);
    console.info(`Unblocked IP: ${ip}`);
  }

  /**
   * Get client statistics
   */
  getClientStats(ip: string): ClientStats | null {
    return this.clientStats.get(ip) || null;
  }

  /**
   * Get all blocked IPs
   */
  getBlockedIPs(): { ip: string; expiry: number }[] {
    const now = Date.now();
    const blocked: { ip: string; expiry: number }[] = [];
    
    this.blockedIPs.forEach((expiry, ip) => {
      if (expiry > now) {
        blocked.push({ ip, expiry });
      }
    });
    
    return blocked;
  }

  /**
   * Get rate limiting statistics
   */
  getStatistics(): {
    totalClients: number;
    blockedIPs: number;
    tierDistribution: Record<string, number>;
    suspiciousClients: number;
    totalRequests: number;
  } {
    const tierDistribution: Record<string, number> = {};
    let suspiciousClients = 0;
    let totalRequests = 0;

    this.clientStats.forEach((stats) => {
      tierDistribution[stats.tier] = (tierDistribution[stats.tier] || 0) + 1;
      if (stats.suspiciousActivity > 0) suspiciousClients++;
      totalRequests += stats.totalRequests;
    });

    return {
      totalClients: this.clientStats.size,
      blockedIPs: this.blockedIPs.size,
      tierDistribution,
      suspiciousClients,
      totalRequests,
    };
  }

  /**
   * Cleanup task to remove expired entries
   */
  private startCleanupTask(): void {
    this.cleanupInterval = setInterval(() => {
      const now = Date.now();
      
      // Clean up expired rate limit entries
      this.store.forEach((entry, key) => {
        if (now >= entry.resetTime) {
          this.store.delete(key);
        }
      });
      
      // Clean up expired IP blocks
      this.blockedIPs.forEach((expiry, ip) => {
        if (now >= expiry) {
          this.blockedIPs.delete(ip);
        }
      });
      
      // Clean up old client stats (older than 24 hours)
      this.clientStats.forEach((stats, ip) => {
        if (now - stats.lastRequest > 24 * 60 * 60 * 1000) {
          this.clientStats.delete(ip);
        }
      });
      
    }, 5 * 60 * 1000); // Run every 5 minutes
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
  }
}

// Export singleton instance
export const advancedRateLimiter = new AdvancedRateLimiter();

// Export types
export type { RateLimitConfig, RateLimitTier, RateLimitResult, ClientStats };