/**
 * Material Types and Interfaces
 * Comprehensive type definitions for the construction materials database
 */

// Price structure for different purchase types and regions
export interface MaterialPricing {
  retail: number;
  bulk: number;
  wholesale: number;
}

// Regional pricing structure
export interface RegionalPricing {
  bangalore: MaterialPricing;
  mumbai: MaterialPricing;
  delhi: MaterialPricing;
  hyderabad: MaterialPricing;
  pune: MaterialPricing;
  chennai: MaterialPricing;
  kolkata: MaterialPricing;
  ahmedabad: MaterialPricing;
  jaipur: MaterialPricing;
  default: MaterialPricing;
}

// Material specifications structure
export interface MaterialSpecifications {
  grade: string;
  standardCompliance: string;
  [key: string]: string | number | string[] | undefined;
}

// Core material interface
export interface Material {
  id: string;
  category: string;
  subcategory: string;
  name: string;
  brand: string;
  unit: string;
  packSize: string;
  specifications: MaterialSpecifications;
  pricing: RegionalPricing;
  qualityScore: number; // 1-10 scale
  popularityRank: number; // 1-100 scale
  availability: 'High' | 'Medium' | 'Low';
  leadTimeDays: number;
  wastagePercentage: number;
  seasonalPricing?: {
    season: 'summer' | 'monsoon' | 'winter';
    priceMultiplier: number;
  }[];
  availabilityStatus?: 'in-stock' | 'limited' | 'out-of-stock';
  supplierInfo?: {
    name: string;
    contact: string;
    location: string;
    rating: number;
  };
}

// Material database metadata
export interface MaterialDatabaseMetadata {
  version: string;
  lastUpdated: string;
  priceValidityMonths: number;
  disclaimer: string;
  regions: string[];
}

// Complete material database structure
export interface MaterialDatabase {
  metadata: MaterialDatabaseMetadata;
  materials: Material[];
}

// Material categories enum
export enum MaterialCategory {
  CEMENT = 'Cement',
  STEEL = 'Steel',
  BRICKS = 'Bricks',
  SAND = 'Sand',
  AGGREGATE = 'Aggregate',
  ELECTRICAL = 'Electrical',
  PLUMBING = 'Plumbing',
  TILES = 'Tiles',
  PAINT = 'Paint',
  WATERPROOFING = 'Waterproofing',
  INSULATION = 'Insulation',
  ROOFING = 'Roofing',
  FLOORING = 'Flooring',
  BATHROOM_FITTINGS = 'Bathroom Fittings'
}

// Purchase types enum
export enum PurchaseType {
  RETAIL = 'retail',
  BULK = 'bulk',
  WHOLESALE = 'wholesale'
}

// Availability levels enum
export enum AvailabilityLevel {
  HIGH = 'High',
  MEDIUM = 'Medium',
  LOW = 'Low'
}

// Material search/filter criteria
export interface MaterialSearchCriteria {
  category?: string;
  subcategory?: string;
  brand?: string;
  region?: string;
  purchaseType?: PurchaseType;
  maxPrice?: number;
  minQualityScore?: number;
  availability?: AvailabilityLevel;
  maxLeadTime?: number;
  searchText?: string;
  quantity?: number;
}

// Material search result with calculated price
export interface MaterialSearchResult extends Material {
  calculatedPrice: number;
  pricePerUnit: number;
  totalCostForQuantity?: number;
  quantity?: number;
}

// Material consumption data for calculations
export interface MaterialConsumption {
  materialId: string;
  quantityRequired: number;
  unit: string;
  consumptionRate: number; // per unit area/volume
  category: string;
  isOptional?: boolean;
  alternativeMaterials?: string[]; // Alternative material IDs
}

// Material cost breakdown for project calculations
export interface MaterialCostBreakdown {
  materialId: string;
  materialName: string;
  category: string;
  quantity: number;
  unit: string;
  unitPrice: number;
  totalCost: number;
  wastage: number;
  wastageAmount: number;
  finalCost: number;
  supplier?: string;
  leadTime: number;
}

// Project material requirements
export interface ProjectMaterialRequirements {
  projectId: string;
  materials: MaterialConsumption[];
  totalEstimatedCost: number;
  qualityTier: 'Smart Choice' | 'Premium Selection' | 'Luxury Collection';
  region: string;
  purchaseType: PurchaseType;
  calculatedOn: Date;
  validUntil: Date;
}

// Material alternative suggestions
export interface MaterialAlternative {
  originalMaterial: Material;
  alternatives: Material[];
  costSavings: number;
  qualityDifference: number;
  reason: string;
}

// Material market trends
export interface MaterialMarketTrend {
  materialId: string;
  currentPrice: number;
  priceHistory: Array<{
    date: string;
    price: number;
    region: string;
  }>;
  priceChangePercentage: number;
  trend: 'rising' | 'falling' | 'stable';
  forecast: 'increase' | 'decrease' | 'stable';
  lastUpdated: Date;
}

// Material supplier information
export interface MaterialSupplier {
  id: string;
  name: string;
  contactInfo: {
    phone: string;
    email: string;
    address: string;
  };
  regions: string[];
  materials: string[]; // Material IDs they supply
  rating: number; // 1-5 scale
  reliability: 'High' | 'Medium' | 'Low';
  paymentTerms: string;
  deliveryTimePromise: number; // days
  minimumOrderValue: number;
}

// Bulk pricing tier structure
export interface BulkPricingTier {
  minimumQuantity: number;
  maximumQuantity?: number;
  discountPercentage: number;
  pricePerUnit: number;
}

// Enhanced material with bulk pricing
export interface MaterialWithBulkPricing extends Material {
  bulkPricingTiers: BulkPricingTier[];
  negotiablePricing: boolean;
  seasonalPricing?: {
    season: 'summer' | 'monsoon' | 'winter';
    priceMultiplier: number;
  }[];
}

// Material quality certification
export interface QualityCertification {
  certificationBody: string;
  certificateNumber: string;
  validFrom: Date;
  validUntil: Date;
  testResults: {
    parameter: string;
    result: string | number;
    standard: string;
    status: 'Pass' | 'Fail' | 'Marginal';
  }[];
}

// Material with quality certifications
export interface CertifiedMaterial extends Material {
  certifications: QualityCertification[];
  qualityGuarantee: {
    warrantyPeriod: number; // months
    replacementPolicy: string;
    qualityStandards: string[];
  };
}

// Material usage statistics
export interface MaterialUsageStats {
  materialId: string;
  popularityScore: number;
  usageFrequency: number; // times used in projects
  averageOrderQuantity: number;
  customerSatisfactionRating: number;
  returnRate: number;
  recommendationScore: number;
}

// Export type for the main materials data loader
export type MaterialsData = MaterialDatabase;