/**
 * Validation schemas for calculator forms
 */

import { z } from 'zod';
import { VALIDATION_LIMITS, REGIONAL_MULTIPLIERS } from '@/core/calculator/constants';

// Extract valid location keys from the constant
const locationKeys = Object.keys(REGIONAL_MULTIPLIERS) as [string, ...string[]];

export const calculatorFormSchema = z.object({
  builtUpArea: z
    .number()
    .min(VALIDATION_LIMITS.minArea, `Minimum area is ${VALIDATION_LIMITS.minArea} sq ft`)
    .max(VALIDATION_LIMITS.maxArea, `Maximum area is ${VALIDATION_LIMITS.maxArea} sq ft`),

  plotArea: z
    .number()
    .min(VALIDATION_LIMITS.minArea, `Minimum plot area is ${VALIDATION_LIMITS.minArea} sq ft`)
    .max(VALIDATION_LIMITS.maxArea * 2, `Maximum plot area is ${VALIDATION_LIMITS.maxArea * 2} sq ft`)
    .optional(),

  floors: z
    .number()
    .min(VALIDATION_LIMITS.minFloors, 'Invalid floor count')
    .max(VALIDATION_LIMITS.maxFloors, `Maximum ${VALIDATION_LIMITS.maxFloors} floors allowed`),

  qualityTier: z.enum(['smart', 'premium', 'luxury']),

  location: z.enum(locationKeys),

  hasStilt: z.boolean().default(false),

  parkingType: z.enum(['open', 'covered', 'none']).default('none'),

  hasBasement: z.boolean().default(false),

  specialFeatures: z
    .array(
      z.object({
        name: z.string(),
        cost: z.number(),
        description: z.string(),
      })
    )
    .optional(),
});

export type CalculatorFormData = z.infer<typeof calculatorFormSchema>;

// Validation helper functions
export const validateBuiltUpArea = (value: number, plotArea?: number): string | true => {
  if (plotArea && value > plotArea * 0.8) {
    return 'Built-up area cannot exceed 80% of plot area';
  }
  return true;
};

export const formatAreaInput = (value: string): number | undefined => {
  const cleaned = value.replace(/[^0-9.]/g, '');
  const parsed = parseFloat(cleaned);
  return isNaN(parsed) ? undefined : parsed;
};

export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    maximumFractionDigits: 0,
  }).format(amount);
};

export const formatNumber = (value: number): string => {
  return new Intl.NumberFormat('en-IN').format(value);
};