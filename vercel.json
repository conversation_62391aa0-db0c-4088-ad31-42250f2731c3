{"framework": "nextjs", "buildCommand": "npm run build", "outputDirectory": ".next", "installCommand": "npm ci", "devCommand": "npm run dev", "regions": ["bom1", "sin1"], "functions": {"src/app/api/**/*.ts": {"maxDuration": 30}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}]}, {"source": "/api/(.*)", "headers": [{"key": "Cache-Control", "value": "s-maxage=300, stale-while-revalidate=60"}]}, {"source": "/(.*\\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2))", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}], "redirects": [{"source": "/home", "destination": "/", "permanent": true}, {"source": "/calc", "destination": "/calculator", "permanent": true}], "rewrites": [{"source": "/sitemap.xml", "destination": "/api/sitemap"}, {"source": "/robots.txt", "destination": "/api/robots"}], "env": {"NEXT_PUBLIC_APP_NAME": "Clarity Engine", "NEXT_PUBLIC_APP_VERSION": "1.0.0", "NEXT_PUBLIC_APP_DESCRIPTION": "AI-Powered Construction Cost Calculator for India"}, "build": {"env": {"NODE_ENV": "production", "NEXT_TELEMETRY_DISABLED": "1"}}, "crons": [{"path": "/api/cron/health-check", "schedule": "*/5 * * * *"}, {"path": "/api/cron/analytics", "schedule": "0 0 * * *"}]}