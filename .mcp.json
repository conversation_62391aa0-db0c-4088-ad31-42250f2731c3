{"mcpServers": {"filesystem": {"type": "stdio", "command": "npx", "args": ["@modelcontextprotocol/server-filesystem", "/home/<USER>"], "env": {}}, "github": {"type": "stdio", "command": "npx", "args": ["@modelcontextprotocol/server-github"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "*********************************************************************************************"}}, "youtube": {"type": "stdio", "command": "npx", "args": ["@kirbah/mcp-youtube"], "env": {"YOUTUBE_API_KEY": "AIzaSyCRc6xoPnCe3DC_zKs1mWHvSSRQzV6F-00"}}, "brave-search": {"type": "stdio", "command": "npx", "args": ["@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_API_KEY": "BSADUC_XbZSfAfy5yvkKQVWiPflKia8"}}, "google-maps": {"type": "stdio", "command": "npx", "args": ["@modelcontextprotocol/server-google-maps"], "env": {"GOOGLE_MAPS_API_KEY": "AIzaSyCRc6xoPnCe3DC_zKs1mWHvSSRQzV6F-00", "GOOGLE_APPLICATION_CREDENTIALS": ""}}, "memory": {"type": "stdio", "command": "npx", "args": ["@modelcontextprotocol/server-memory"], "env": {}}, "context7": {"type": "stdio", "command": "npx", "args": ["@upstash/context7-mcp"], "env": {}}, "sequential-thinking": {"type": "stdio", "command": "npx", "args": ["@modelcontextprotocol/server-sequential-thinking"], "env": {}}, "puppeteer": {"type": "stdio", "command": "npx", "args": ["@modelcontextprotocol/server-puppeteer"], "env": {}}, "selenium": {"type": "stdio", "command": "npx", "args": ["@angiejones/mcp-selenium"], "env": {}}, "playwright": {"type": "stdio", "command": "npx", "args": ["@playwright/mcp"], "env": {}}, "notion": {"type": "stdio", "command": "npx", "args": ["@notionhq/notion-mcp-server"], "env": {"NOTION_API_KEY": "ntn_F3p8K7mN9qR2sT6vY0wE4xZ1cH5jL8dA"}}, "google-drive": {"type": "stdio", "command": "npx", "args": ["@modelcontextprotocol/server-gdrive"], "env": {"GOOGLE_API_KEY": "AIzaSyCRc6xoPnCe3DC_zKs1mWHvSSRQzV6F-00"}}, "zerodha": {"type": "stdio", "command": "npx", "args": ["mcp-remote", "https://mcp.kite.trade/sse"], "env": {}}, "supabase": {"type": "stdio", "command": "npx", "args": ["-y", "@supabase/mcp-server-supabase@latest", "--read-only", "--project-ref=fsppzopxkxdnyqleuhpl"], "env": {"SUPABASE_ACCESS_TOKEN": "********************************************"}}, "postgres": {"type": "stdio", "command": "npx", "args": ["@modelcontextprotocol/server-postgres", "postgresql://postgres.bprnearwznzrbcpnzphq:<EMAIL>:6543/postgres"]}}}