import { NextRequest, NextResponse } from 'next/server';
import { performanceMonitor } from '@/lib/performance/monitoring';
import { securityMonitor } from '@/lib/security/middleware';

export async function GET(request: NextRequest) {
  try {
    // Authentication check (in production, verify admin token)
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get performance metrics
    const performanceMetrics = performanceMonitor.getMetrics();
    const healthStatus = performanceMonitor.getHealthStatus();
    
    // Get security metrics
    const securitySummary = securityMonitor.getSecuritySummary();
    
    // Get system information
    const systemInfo = {
      environment: process.env.NODE_ENV || 'development',
      version: process.env.APP_VERSION || '1.0.0',
      nodeVersion: process.version,
      platform: process.platform,
      uptime: process.uptime(),
      memoryUsage: process.memoryUsage(),
      cpuUsage: process.cpuUsage(),
    };
    
    // Get materials database info
    const materialsInfo = await getMaterialsInfo();
    
    const dashboard = {
      timestamp: new Date().toISOString(),
      status: healthStatus,
      system: systemInfo,
      performance: {
        requests: {
          total: performanceMetrics.requestCount,
          errors: performanceMetrics.errorCount,
          errorRate: performanceMonitor.getErrorRate(),
          throughput: performanceMetrics.throughput,
        },
        response: {
          average: Math.round(performanceMetrics.responseTimeAvg),
          maximum: performanceMetrics.responseTimeMax,
          minimum: performanceMetrics.responseTimeMin === Infinity ? 0 : performanceMetrics.responseTimeMin,
        },
        memory: {
          heap: {
            used: Math.round(systemInfo.memoryUsage.heapUsed / 1024 / 1024),
            total: Math.round(systemInfo.memoryUsage.heapTotal / 1024 / 1024),
            percentage: Math.round((systemInfo.memoryUsage.heapUsed / systemInfo.memoryUsage.heapTotal) * 100),
          },
          rss: Math.round(systemInfo.memoryUsage.rss / 1024 / 1024),
          external: Math.round(systemInfo.memoryUsage.external / 1024 / 1024),
        },
        topEndpoints: performanceMonitor.getTopEndpoints(10),
        slowRequests: performanceMonitor.getSlowRequests(1000).slice(-5),
      },
      security: {
        summary: securitySummary,
        blockedIPs: securityMonitor.getBlockedIPs(),
        recentEvents: securityMonitor.getSecurityEvents().slice(-10),
      },
      materials: materialsInfo,
    };
    
    return NextResponse.json(dashboard, {
      status: 200,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Content-Type': 'application/json',
      },
    });
  } catch (error) {
    console.error('Dashboard API error:', error);
    
    return NextResponse.json(
      {
        error: 'Dashboard data unavailable',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

async function getMaterialsInfo() {
  try {
    const materialsData = require('../../../../data/materials/core-materials.json');
    
    return {
      totalMaterials: materialsData.materials.length,
      regions: materialsData.metadata.regions.length,
      categories: [...new Set(materialsData.materials.map((m: any) => m.category))].length,
      brands: [...new Set(materialsData.materials.map((m: any) => m.brand))].length,
      version: materialsData.metadata.version,
      lastUpdated: materialsData.metadata.lastUpdated,
      regionalCoverage: materialsData.metadata.regions,
      categoryBreakdown: materialsData.materials.reduce((acc: any, material: any) => {
        acc[material.category] = (acc[material.category] || 0) + 1;
        return acc;
      }, {}),
    };
  } catch (error) {
    console.error('Failed to load materials info:', error);
    return {
      error: 'Materials data unavailable',
      totalMaterials: 0,
      regions: 0,
      categories: 0,
      brands: 0,
    };
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Authentication check
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    if (body.action === 'unblock_ip' && body.ip) {
      securityMonitor.unblockIP(body.ip);
      return NextResponse.json({ message: `IP ${body.ip} unblocked` });
    }
    
    if (body.action === 'block_ip' && body.ip) {
      securityMonitor.blockIP(body.ip);
      return NextResponse.json({ message: `IP ${body.ip} blocked` });
    }
    
    if (body.action === 'clear_security_events') {
      // Clear security events (for development/testing)
      if (process.env.NODE_ENV === 'development') {
        return NextResponse.json({ message: 'Security events cleared' });
      } else {
        return NextResponse.json({ error: 'Action not allowed in production' }, { status: 403 });
      }
    }
    
    return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
  } catch (error) {
    return NextResponse.json({ error: 'Invalid request' }, { status: 400 });
  }
}