-- EXECUTE THIS IN YOUR SUPABASE PROJECT: swgckguywfqzeuapyres
-- Copy this entire script to Supabase SQL Editor and run it

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Projects table for storing user calculations
CREATE TABLE projects (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  location TEXT NOT NULL,
  area_sqft INTEGER NOT NULL CHECK (area_sqft > 0),
  floors INTEGER NOT NULL CHECK (floors >= 0 AND floors <= 10),
  quality_tier TEXT NOT NULL CHECK (quality_tier IN ('smart', 'premium', 'luxury')),
  calculation_data JSONB NOT NULL DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Materials table for material catalog
CREATE TABLE materials (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  category TEXT NOT NULL,
  name TEXT NOT NULL,
  brand TEXT,
  unit TEXT NOT NULL,
  base_price DECIMAL(10,2),
  specifications JSONB DEFAULT '{}',
  pricing JSONB DEFAULT '{}',
  quality_score DECIMAL(3,1) DEFAULT 5.0 CHECK (quality_score >= 0 AND quality_score <= 10),
  popularity_rank INTEGER DEFAULT 999,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable Row Level Security
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE materials ENABLE ROW LEVEL SECURITY;

-- RLS Policies for projects (users can only access their own projects)
CREATE POLICY "Users can view their own projects" ON projects
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own projects" ON projects
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own projects" ON projects
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own projects" ON projects
  FOR DELETE USING (auth.uid() = user_id);

-- RLS Policies for materials (read-only for all users)
CREATE POLICY "Materials are viewable by everyone" ON materials
  FOR SELECT USING (true);

-- Allow service role to insert materials (for seeding data)
CREATE POLICY "Service role can manage materials" ON materials
  FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

-- Create indexes for performance
CREATE INDEX idx_projects_user_id ON projects(user_id);
CREATE INDEX idx_projects_created_at ON projects(created_at DESC);
CREATE INDEX idx_materials_category ON materials(category);
CREATE INDEX idx_materials_pricing ON materials USING GIN(pricing);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_projects_updated_at 
  BEFORE UPDATE ON projects 
  FOR EACH ROW 
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_materials_updated_at 
  BEFORE UPDATE ON materials 
  FOR EACH ROW 
  EXECUTE FUNCTION update_updated_at_column();

-- Insert initial materials data
INSERT INTO materials (category, name, brand, unit, base_price, specifications, pricing, quality_score, popularity_rank) VALUES
-- Cement
('Cement', 'OPC 43 Grade Cement', 'Local Brand', 'bag (50kg)', 380.00, 
 '{"grade": "43", "type": "Ordinary Portland Cement", "standard": "IS 8112:2013"}',
 '{"bangalore": {"retail": 380, "bulk": 360}, "mumbai": {"retail": 400, "bulk": 380}, "delhi": {"retail": 390, "bulk": 370}, "default": {"retail": 385, "bulk": 365}}',
 7.0, 3),

('Cement', 'OPC 53 Grade Cement', 'UltraTech', 'bag (50kg)', 420.00,
 '{"grade": "53", "type": "Ordinary Portland Cement", "standard": "IS 12269:2013", "compressive_strength": "53 MPa"}',
 '{"bangalore": {"retail": 420, "bulk": 400}, "mumbai": {"retail": 440, "bulk": 420}, "delhi": {"retail": 430, "bulk": 410}, "default": {"retail": 425, "bulk": 405}}',
 9.5, 1),

('Cement', 'OPC 53 Grade Cement', 'ACC', 'bag (50kg)', 430.00,
 '{"grade": "53", "type": "Ordinary Portland Cement", "standard": "IS 12269:2013", "compressive_strength": "53 MPa"}',
 '{"bangalore": {"retail": 430, "bulk": 410}, "mumbai": {"retail": 450, "bulk": 430}, "delhi": {"retail": 440, "bulk": 420}, "default": {"retail": 435, "bulk": 415}}',
 9.2, 2),

-- Steel
('Steel', 'TMT Steel Fe415', 'Local Brand', 'kg', 62.00,
 '{"grade": "Fe415", "type": "TMT Bar", "standard": "IS 1786:2008", "yield_strength": "415 MPa"}',
 '{"bangalore": {"retail": 62, "bulk": 60}, "mumbai": {"retail": 65, "bulk": 63}, "delhi": {"retail": 64, "bulk": 62}, "default": {"retail": 63, "bulk": 61}}',
 7.5, 3),

('Steel', 'TMT Steel Fe500', 'TATA Steel', 'kg', 68.00,
 '{"grade": "Fe500", "type": "TMT Bar", "standard": "IS 1786:2008", "yield_strength": "500 MPa"}',
 '{"bangalore": {"retail": 68, "bulk": 66}, "mumbai": {"retail": 72, "bulk": 70}, "delhi": {"retail": 70, "bulk": 68}, "default": {"retail": 69, "bulk": 67}}',
 9.0, 1),

('Steel', 'TMT Steel Fe500D', 'SAIL', 'kg', 70.00,
 '{"grade": "Fe500D", "type": "TMT Bar", "standard": "IS 1786:2008", "yield_strength": "500 MPa", "ductility": "Enhanced"}',
 '{"bangalore": {"retail": 70, "bulk": 68}, "mumbai": {"retail": 74, "bulk": 72}, "delhi": {"retail": 72, "bulk": 70}, "default": {"retail": 71, "bulk": 69}}',
 9.3, 2),

-- Bricks
('Bricks', 'Red Clay Bricks', 'Local', 'piece', 8.00,
 '{"type": "Clay Brick", "size": "230x110x75mm", "compressive_strength": "3.5 MPa", "water_absorption": "20%"}',
 '{"bangalore": {"retail": 8, "bulk": 7.5}, "mumbai": {"retail": 10, "bulk": 9.5}, "delhi": {"retail": 9, "bulk": 8.5}, "default": {"retail": 8.5, "bulk": 8}}',
 6.5, 3),

('Bricks', 'Fly Ash Bricks', 'Eco-friendly', 'piece', 12.00,
 '{"type": "Fly Ash Brick", "size": "230x110x75mm", "compressive_strength": "4.0 MPa", "water_absorption": "15%"}',
 '{"bangalore": {"retail": 12, "bulk": 11}, "mumbai": {"retail": 14, "bulk": 13}, "delhi": {"retail": 13, "bulk": 12}, "default": {"retail": 12.5, "bulk": 11.5}}',
 8.0, 2),

-- Sand
('Sand', 'River Sand', 'Natural', 'cft', 35.00,
 '{"type": "River Sand", "grade": "Fine", "silt_content": "3%", "standard": "IS 383:2016"}',
 '{"bangalore": {"retail": 35, "bulk": 32}, "mumbai": {"retail": 45, "bulk": 42}, "delhi": {"retail": 38, "bulk": 35}, "default": {"retail": 37, "bulk": 34}}',
 7.0, 1),

('Sand', 'M-Sand (Manufactured)', 'Crushed', 'cft', 30.00,
 '{"type": "Manufactured Sand", "grade": "Fine", "silt_content": "2%", "standard": "IS 383:2016"}',
 '{"bangalore": {"retail": 30, "bulk": 28}, "mumbai": {"retail": 38, "bulk": 35}, "delhi": {"retail": 33, "bulk": 30}, "default": {"retail": 32, "bulk": 29}}',
 8.2, 2),

-- Aggregate
('Aggregate', '20mm Aggregate', 'Crushed Stone', 'cft', 45.00,
 '{"type": "Coarse Aggregate", "size": "20mm", "crushing_value": "22%", "standard": "IS 383:2016"}',
 '{"bangalore": {"retail": 45, "bulk": 42}, "mumbai": {"retail": 55, "bulk": 52}, "delhi": {"retail": 48, "bulk": 45}, "default": {"retail": 47, "bulk": 44}}',
 8.0, 1),

('Aggregate', '12mm Aggregate', 'Crushed Stone', 'cft', 48.00,
 '{"type": "Coarse Aggregate", "size": "12mm", "crushing_value": "20%", "standard": "IS 383:2016"}',
 '{"bangalore": {"retail": 48, "bulk": 45}, "mumbai": {"retail": 58, "bulk": 55}, "delhi": {"retail": 51, "bulk": 48}, "default": {"retail": 50, "bulk": 47}}',
 8.5, 2);

-- Verify data insertion
SELECT 'Materials inserted successfully. Count: ' || COUNT(*) as result FROM materials;
SELECT 'Tables created successfully. Projects: ' || (SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'projects') || ', Materials: ' || (SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'materials') as result;