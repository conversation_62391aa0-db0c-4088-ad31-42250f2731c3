Spoke Document 2: Technical Design Document for "The Clarity Engine" v2.0
=========================================================================

**Document ID:** SD-002  
**Version:** 2.0  
**Owner:** Lead Engineer / System Architect  
**Audience:** Backend Developers, Frontend Developers, Database Engineers, DevOps, QA Team  
**Linked to:** Master PRD v2.0, UI/UX Specification v2.0  
**Status:** Final - Ready for Development

* * *

Table of Contents
-----------------

**Part 1: System Architecture**

1.  High-Level Architecture Overview
2.  Technology Stack & Rationale
3.  System Components & Services
4.  Data Flow Architecture
5.  Security Architecture

**Part 2: Database Design** 6. Complete Database Schema 7. Data Models & Relationships 8. Indexing Strategy 9. Data Migration & Versioning 10. Backup & Recovery Strategy

**Part 3: API Design** 11. RESTful API Specifications 12. GraphQL Schema (Future) 13. WebSocket Communications 14. API Security & Rate Limiting 15. Error Handling & Logging

**Part 4: Frontend Architecture** 16. Application Structure 17. State Management Design 18. Component Architecture 19. Performance Optimization 20. Build & Deployment Pipeline

**Part 5: Backend Services** 21. Calculation Engine Architecture 22. File Processing Services 23. Notification Services 24. Analytics & Monitoring 25. Third-Party Integrations

**Part 6: Infrastructure & DevOps** 26. Cloud Infrastructure Design 27. CI/CD Pipeline 28. Monitoring & Alerting 29. Disaster Recovery Plan 30. Scaling Strategy

* * *

Part 1: System Architecture
---------------------------

### 1\. High-Level Architecture Overview

#### 1.1 System Architecture Diagram

mermaid

    graph TB
        subgraph "Client Layer"
            WEB[Web App<br/>Next.js/React]
            MOB[Mobile Web<br/>PWA]
            ADMIN[Admin Panel<br/>Next.js/React]
        end
        
        subgraph "CDN Layer"
            CDN[Vercel Edge Network]
            STATIC[Static Assets]
        end
        
        subgraph "API Gateway"
            GATEWAY[Supabase API Gateway]
            AUTH[Authentication Service]
            RATE[Rate Limiter]
        end
        
        subgraph "Application Services"
            REST[RESTful API<br/>PostgREST]
            REALTIME[Realtime<br/>WebSocket]
            STORAGE[File Storage<br/>S3 Compatible]
            EDGE[Edge Functions<br/>Deno]
        end
        
        subgraph "Core Services"
            CALC[Calculation Engine]
            PRICE[Pricing Service]
            USER[User Service]
            MATERIAL[Material Service]
            REPORT[Report Generator]
        end
        
        subgraph "Data Layer"
            PG[(PostgreSQL 15+<br/>Primary DB)]
            REDIS[(Redis<br/>Cache)]
            QUEUE[Job Queue<br/>pg-boss]
        end
        
        subgraph "External Services"
            AI[AI Services<br/>OpenAI/Claude]
            EMAIL[Email Service<br/>SendGrid]
            SMS[SMS Gateway<br/>Twilio]
            PAY[Payment<br/>Razorpay]
        end
        
        WEB --> CDN
        MOB --> CDN
        ADMIN --> CDN
        CDN --> GATEWAY
        
        GATEWAY --> AUTH
        GATEWAY --> RATE
        AUTH --> REST
        RATE --> REST
        
        REST --> CALC
        REST --> PRICE
        REST --> USER
        REST --> MATERIAL
        
        CALC --> PG
        PRICE --> REDIS
        USER --> PG
        MATERIAL --> PG
        
        REPORT --> QUEUE
        QUEUE --> PG
        
        EDGE --> AI
        REST --> EMAIL
        REST --> SMS
        REST --> PAY

#### 1.2 Architecture Principles

javascript

    const architecturePrinciples = {
      // Core Principles
      principles: {
        scalability: {
          description: 'System must handle 100K+ concurrent users',
          implementation: [
            'Stateless services',
            'Horizontal scaling',
            'Database connection pooling',
            'Caching at multiple levels'
          ]
        },
        
        reliability: {
          description: '99.9% uptime SLA',
          implementation: [
            'Redundancy at every layer',
            'Circuit breakers',
            'Graceful degradation',
            'Health checks'
          ]
        },
        
        security: {
          description: 'Defense in depth',
          implementation: [
            'JWT-based authentication',
            'Row-level security',
            'API rate limiting',
            'Input validation'
          ]
        },
        
        maintainability: {
          description: 'Easy to understand and modify',
          implementation: [
            'Clean architecture',
            'Comprehensive documentation',
            'Automated testing',
            'Monitoring and logging'
          ]
        }
      },
      
      // Design Patterns
      patterns: {
        microservices: 'Logical separation of concerns',
        eventDriven: 'Async processing for heavy operations',
        cqrs: 'Separate read and write models where needed',
        repository: 'Abstract data access layer'
      }
    };

### 2\. Technology Stack & Rationale

#### 2.1 Complete Technology Stack

javascript

    const technologyStack = {
      // Frontend Stack
      frontend: {
        framework: {
          choice: 'Next.js 14+',
          rationale: 'Server-side rendering, API routes, excellent DX',
          alternatives: ['Remix', 'Vite + React']
        },
        
        language: {
          choice: 'TypeScript 5+',
          rationale: 'Type safety, better IDE support, fewer runtime errors',
          strictMode: true
        },
        
        styling: {
          choice: 'Tailwind CSS 3+',
          rationale: 'Utility-first, small bundle, rapid development',
          withComponents: 'shadcn/ui for complex components'
        },
        
        stateManagement: {
          local: 'React hooks (useState, useReducer)',
          global: 'Zustand 4+',
          server: 'TanStack Query 5+',
          forms: 'React Hook Form 7+ with Zod'
        },
        
        testing: {
          unit: 'Jest + React Testing Library',
          e2e: 'Cypress 13+',
          visual: 'Storybook 7+ with Chromatic'
        }
      },
      
      // Backend Stack
      backend: {
        platform: {
          choice: 'Supabase',
          rationale: 'Managed PostgreSQL, Auth, Realtime, Storage in one',
          selfHosted: false // Use cloud version for reliability
        },
        
        database: {
          primary: 'PostgreSQL 15+',
          rationale: 'ACID compliance, JSON support, proven reliability',
          extensions: ['uuid-ossp', 'pg_trgm', 'postgis']
        },
        
        cache: {
          choice: 'Redis (via Upstash)',
          rationale: 'In-memory performance, pub/sub, TTL support',
          usage: ['Session cache', 'Calculation results', 'Rate limiting']
        },
        
        serverless: {
          choice: 'Supabase Edge Functions',
          rationale: 'Deno runtime, close to data, auto-scaling',
          alternatives: ['Vercel Functions', 'AWS Lambda']
        },
        
        fileStorage: {
          choice: 'Supabase Storage',
          rationale: 'S3-compatible, integrated auth, CDN included',
          usage: ['PDFs', 'Images', 'Import files']
        }
      },
      
      // Infrastructure
      infrastructure: {
        hosting: {
          frontend: 'Vercel',
          rationale: 'Optimized for Next.js, global CDN, preview deployments'
        },
        
        monitoring: {
          apm: 'Sentry',
          logs: 'Supabase Logs + Sentry',
          analytics: 'PostHog',
          uptime: 'BetterStack'
        },
        
        ci_cd: {
          vcs: 'GitHub',
          ci: 'GitHub Actions',
          cd: 'Vercel (auto-deploy)',
          environments: ['Development', 'Staging', 'Production']
        }
      },
      
      // External Services
      external: {
        ai: {
          provider: 'OpenAI',
          models: ['GPT-4 for catalog parsing', 'GPT-3.5 for search'],
          fallback: 'Claude API'
        },
        
        communications: {
          email: 'SendGrid',
          sms: 'Twilio',
          whatsapp: 'Twilio WhatsApp Business API'
        },
        
        payments: {
          provider: 'Razorpay',
          methods: ['Cards', 'UPI', 'Net Banking', 'Wallets']
        }
      }
    };

#### 2.2 Technology Decision Matrix

javascript

    const decisionMatrix = {
      // Why Supabase over custom backend?
      supabaseVsCustom: {
        factors: {
          timeToMarket: { supabase: 10, custom: 3 },
          cost: { supabase: 8, custom: 5 },
          flexibility: { supabase: 7, custom: 10 },
          maintenance: { supabase: 9, custom: 4 },
          scalability: { supabase: 8, custom: 9 }
        },
        decision: 'Supabase wins on speed and maintenance',
        mitigation: 'Can eject to custom if needed'
      },
      
      // Why PostgreSQL over MongoDB?
      postgresVsMongo: {
        factors: {
          relationships: { postgres: 10, mongo: 6 },
          consistency: { postgres: 10, mongo: 7 },
          jsonSupport: { postgres: 9, mongo: 10 },
          transactions: { postgres: 10, mongo: 7 },
          maturity: { postgres: 10, mongo: 8 }
        },
        decision: 'PostgreSQL for ACID and relationships',
        jsonUsage: 'JSONB for flexible fields'
      },
      
      // Why Next.js over SPA?
      nextVsSpa: {
        factors: {
          seo: { nextjs: 10, spa: 4 },
          performance: { nextjs: 9, spa: 7 },
          complexity: { nextjs: 7, spa: 8 },
          ecosystem: { nextjs: 9, spa: 9 }
        },
        decision: 'Next.js for SEO and performance',
        rendering: 'SSG for marketing, SSR for app'
      }
    };

### 3\. System Components & Services

#### 3.1 Service Architecture

typescript

    // Service Definitions
    interface ServiceArchitecture {
      // Core Services
      coreServices: {
        calculationEngine: {
          responsibility: 'All cost calculations',
          api: '/api/calculations/*',
          dependencies: ['Database', 'Cache'],
          sla: {
            availability: '99.9%',
            responseTime: '<200ms for cached',
            throughput: '1000 req/s'
          }
        };
        
        userService: {
          responsibility: 'User management and auth',
          api: '/api/users/*',
          dependencies: ['Database', 'Auth'],
          features: [
            'Registration/Login',
            'Profile management',
            'Preferences',
            'Project ownership'
          ]
        };
        
        materialService: {
          responsibility: 'Material catalog management',
          api: '/api/materials/*',
          dependencies: ['Database', 'Search', 'Cache'],
          features: [
            'CRUD operations',
            'Search and filter',
            'Price management',
            'Inventory tracking'
          ]
        };
        
        pricingService: {
          responsibility: 'Dynamic pricing engine',
          api: '/api/pricing/*',
          dependencies: ['Database', 'Cache', 'Queue'],
          features: [
            'Location-based pricing',
            'Bulk updates',
            'Historical tracking',
            'Market intelligence'
          ]
        };
      };
      
      // Supporting Services
      supportingServices: {
        fileProcessor: {
          type: 'Async worker',
          responsibility: 'Process uploaded files',
          triggers: ['File upload event'],
          operations: [
            'PDF parsing',
            'Excel processing',
            'Image optimization',
            'Virus scanning'
          ]
        };
        
        reportGenerator: {
          type: 'Async worker',
          responsibility: 'Generate PDF reports',
          triggers: ['User request'],
          operations: [
            'Data aggregation',
            'PDF creation',
            'Storage upload',
            'User notification'
          ]
        };
        
        notificationService: {
          type: 'Event-driven',
          responsibility: 'Multi-channel notifications',
          channels: ['Email', 'SMS', 'Push', 'In-app'],
          triggers: [
            'Project milestones',
            'Price changes',
            'System alerts'
          ]
        };
      };
    }

#### 3.2 Service Communication Patterns

typescript

    // Communication Patterns
    const communicationPatterns = {
      // Synchronous Communication
      sync: {
        pattern: 'Request-Response',
        useCase: 'Real-time calculations',
        implementation: `
          // Client -> API Gateway -> Service -> Database
          async function calculateCost(projectData: ProjectData) {
            const response = await fetch('/api/calculations/detailed', {
              method: 'POST',
              body: JSON.stringify(projectData)
            });
            return response.json();
          }
        `,
        timeout: 30000, // 30 seconds
        retry: {
          attempts: 3,
          backoff: 'exponential'
        }
      },
      
      // Asynchronous Communication
      async: {
        pattern: 'Message Queue',
        useCase: 'Report generation, file processing',
        implementation: `
          // Using pg-boss for job queue
          async function queueReportGeneration(projectId: string) {
            const jobId = await boss.send('generate-report', {
              projectId,
              userId: currentUser.id,
              timestamp: new Date()
            });
            
            return { jobId, status: 'queued' };
          }
          
          // Worker process
          boss.work('generate-report', async (job) => {
            const { projectId, userId } = job.data;
            
            // Generate report
            const report = await generateReport(projectId);
            
            // Store in S3
            const url = await uploadToStorage(report);
            
            // Notify user
            await notifyUser(userId, { reportUrl: url });
          });
        `,
        queue: {
          provider: 'pg-boss',
          concurrency: 10,
          retention: '7 days'
        }
      },
      
      // Real-time Communication
      realtime: {
        pattern: 'WebSocket',
        useCase: 'Live price updates, collaboration',
        implementation: `
          // Supabase Realtime
          const subscription = supabase
            .channel('price-updates')
            .on('postgres_changes', {
              event: 'UPDATE',
              schema: 'public',
              table: 'prices',
              filter: 'location_id=eq.delhi'
            }, (payload) => {
              updateLocalPrice(payload.new);
            })
            .subscribe();
        `,
        channels: [
          'price-updates',
          'project-collaboration',
          'system-notifications'
        ]
      }
    };

### 4\. Data Flow Architecture

#### 4.1 Request Flow

typescript

    // Request Flow Architecture
    const requestFlow = {
      // Client to Server Flow
      clientToServer: {
        flow: [
          'Client Application',
          'CDN (Cached responses)',
          'API Gateway (Auth & Rate Limit)',
          'Application Service',
          'Database/Cache',
          'Response'
        ],
        
        example: `
          // 1. Client Request
          const projectData = {
            location: 'delhi',
            plotArea: 2000,
            floors: 3,
            structure: 'rcc_frame'
          };
          
          // 2. Request hits CDN
          // - Check if cached response exists
          // - If yes, return immediately
          
          // 3. API Gateway
          // - Validate JWT token
          // - Check rate limits
          // - Log request
          
          // 4. Application Service
          const calculation = await calculationEngine.process(projectData);
          
          // 5. Database/Cache
          // - Check Redis cache first
          // - If miss, calculate and store
          // - Return result
          
          // 6. Response transformation
          return {
            success: true,
            data: calculation,
            metadata: {
              timestamp: new Date(),
              version: '1.0',
              cached: false
            }
          };
        `
      },
      
      // Data Processing Flow
      dataProcessing: {
        flow: [
          'Data Input',
          'Validation',
          'Transformation',
          'Business Logic',
          'Persistence',
          'Cache Update',
          'Response'
        ],
        
        implementation: `
          class CalculationProcessor {
            async process(input: CalculationInput): Promise<CalculationResult> {
              // 1. Validation
              const validated = await this.validate(input);
              
              // 2. Transformation
              const normalized = this.normalize(validated);
              
              // 3. Business Logic
              const calculations = await this.calculate(normalized);
              
              // 4. Persistence
              await this.saveToDatabase(calculations);
              
              // 5. Cache Update
              await this.updateCache(calculations);
              
              // 6. Response
              return this.formatResponse(calculations);
            }
            
            private async calculate(data: NormalizedData) {
              // Check cache first
              const cached = await redis.get(data.cacheKey);
              if (cached) return cached;
              
              // Perform calculations
              const result = {
                structure: await this.calculateStructure(data),
                finishes: await this.calculateFinishes(data),
                mep: await this.calculateMEP(data),
                total: 0 // Sum all components
              };
              
              // Cache result
              await redis.setex(data.cacheKey, 3600, result);
              
              return result;
            }
          }
        `
      }
    };

#### 4.2 Data Flow Patterns

typescript

    // Data Flow Patterns
    const dataFlowPatterns = {
      // Read-Heavy Optimization
      readOptimization: {
        pattern: 'Cache-Aside',
        implementation: `
          async function getMaterial(id: string): Promise<Material> {
            // 1. Check L1 Cache (Application Memory)
            const l1Cache = memoryCache.get(\`material:\${id}\`);
            if (l1Cache) return l1Cache;
            
            // 2. Check L2 Cache (Redis)
            const l2Cache = await redis.get(\`material:\${id}\`);
            if (l2Cache) {
              memoryCache.set(\`material:\${id}\`, l2Cache, 300); // 5 min
              return JSON.parse(l2Cache);
            }
            
            // 3. Database Query
            const material = await db.materials.findUnique({ where: { id } });
            
            // 4. Update Caches
            await redis.setex(\`material:\${id}\`, 3600, JSON.stringify(material));
            memoryCache.set(\`material:\${id}\`, material, 300);
            
            return material;
          }
        `,
        cacheInvalidation: `
          async function updateMaterial(id: string, data: Partial<Material>) {
            // 1. Update Database
            const updated = await db.materials.update({
              where: { id },
              data
            });
            
            // 2. Invalidate Caches
            memoryCache.delete(\`material:\${id}\`);
            await redis.del(\`material:\${id}\`);
            
            // 3. Publish Update Event
            await pubsub.publish('material.updated', { id, data: updated });
            
            return updated;
          }
        `
      },
      
      // Write-Heavy Optimization
      writeOptimization: {
        pattern: 'Write-Behind Cache',
        implementation: `
          class PriceUpdateService {
            private updateQueue: Map<string, PriceUpdate> = new Map();
            private flushInterval: NodeJS.Timer;
            
            constructor() {
              // Flush every 5 seconds
              this.flushInterval = setInterval(() => this.flush(), 5000);
            }
            
            async updatePrice(materialId: string, price: number) {
              // 1. Update cache immediately
              await redis.hset('prices', materialId, price);
              
              // 2. Queue database update
              this.updateQueue.set(materialId, {
                materialId,
                price,
                timestamp: new Date()
              });
              
              // 3. Return immediately
              return { success: true };
            }
            
            private async flush() {
              if (this.updateQueue.size === 0) return;
              
              // Batch update database
              const updates = Array.from(this.updateQueue.values());
              await db.prices.updateMany(updates);
              
              // Clear queue
              this.updateQueue.clear();
            }
          }
        `
      }
    };

### 5\. Security Architecture

#### 5.1 Security Layers

typescript

    // Security Architecture
    const securityArchitecture = {
      // Authentication & Authorization
      auth: {
        provider: 'Supabase Auth',
        
        strategy: {
          method: 'JWT with refresh tokens',
          storage: 'HTTP-only cookies',
          expiry: {
            access: '15 minutes',
            refresh: '7 days'
          }
        },
        
        implementation: `
          // Middleware for protected routes
          export async function requireAuth(
            req: NextApiRequest,
            res: NextApiResponse,
            next: () => void
          ) {
            const token = req.cookies['sb-access-token'];
            
            if (!token) {
              return res.status(401).json({ error: 'Unauthorized' });
            }
            
            try {
              const { data: { user }, error } = await supabase.auth.getUser(token);
              
              if (error || !user) {
                return res.status(401).json({ error: 'Invalid token' });
              }
              
              req.user = user;
              next();
            } catch (error) {
              return res.status(401).json({ error: 'Token verification failed' });
            }
          }
        `,
        
        rbac: {
          roles: ['user', 'admin', 'super_admin'],
          implementation: `
            // Row Level Security Policy
            CREATE POLICY "Users can only access own projects"
            ON projects
            FOR ALL
            USING (auth.uid() = user_id);
            
            CREATE POLICY "Admins can access all projects"
            ON projects
            FOR ALL
            USING (
              EXISTS (
                SELECT 1 FROM admin_users
                WHERE user_id = auth.uid()
                AND role IN ('admin', 'super_admin')
              )
            );
          `
        }
      },
      
      // API Security
      apiSecurity: {
        rateLimiting: {
          provider: 'Upstash Redis',
          implementation: `
            import { Ratelimit } from '@upstash/ratelimit';
            import { Redis } from '@upstash/redis';
            
            const ratelimit = new Ratelimit({
              redis: Redis.fromEnv(),
              limiter: Ratelimit.slidingWindow(10, '10 s'),
              analytics: true
            });
            
            export async function rateLimitMiddleware(
              req: NextApiRequest,
              res: NextApiResponse
            ) {
              const identifier = req.headers['x-forwarded-for'] || 'anonymous';
              const { success, limit, reset, remaining } = await ratelimit.limit(
                identifier
              );
              
              res.setHeader('X-RateLimit-Limit', limit);
              res.setHeader('X-RateLimit-Remaining', remaining);
              res.setHeader('X-RateLimit-Reset', reset);
              
              if (!success) {
                return res.status(429).json({
                  error: 'Too many requests'
                });
              }
            }
          `,
          
          limits: {
            anonymous: '10 requests per 10 seconds',
            authenticated: '100 requests per minute',
            calculation: '20 calculations per hour',
            admin: 'No limit'
          }
        },
        
        inputValidation: {
          library: 'Zod',
          implementation: `
            import { z } from 'zod';
            
            const CalculationInputSchema = z.object({
              location: z.enum(['delhi', 'gurgaon', 'noida', 'bangalore']),
              plotArea: z.number().min(100).max(100000),
              floors: z.number().int().min(1).max(10),
              structure: z.enum(['rcc_frame', 'load_bearing']),
              rooms: z.array(z.object({
                type: z.string(),
                count: z.number().int().min(0).max(20)
              }))
            });
            
            export function validateInput(data: unknown) {
              try {
                return {
                  success: true,
                  data: CalculationInputSchema.parse(data)
                };
              } catch (error) {
                return {
                  success: false,
                  error: error.errors
                };
              }
            }
          `
        },
        
        cors: {
          config: {
            origin: process.env.ALLOWED_ORIGINS?.split(',') || '*',
            credentials: true,
            methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
            allowedHeaders: ['Content-Type', 'Authorization']
          }
        }
      },
      
      // Data Security
      dataSecurity: {
        encryption: {
          atRest: 'AES-256 (Supabase managed)',
          inTransit: 'TLS 1.3',
          sensitive: `
            // Encrypt sensitive fields
            import crypto from 'crypto';
            
            const algorithm = 'aes-256-gcm';
            const key = Buffer.from(process.env.ENCRYPTION_KEY, 'hex');
            
            export function encrypt(text: string): string {
              const iv = crypto.randomBytes(16);
              const cipher = crypto.createCipheriv(algorithm, key, iv);
              
              let encrypted = cipher.update(text, 'utf8', 'hex');
              encrypted += cipher.final('hex');
              
              const authTag = cipher.getAuthTag();
              
              return iv.toString('hex') + ':' + authTag.toString('hex') + ':' + encrypted;
            }
            
            export function decrypt(encrypted: string): string {
              const parts = encrypted.split(':');
              const iv = Buffer.from(parts[0], 'hex');
              const authTag = Buffer.from(parts[1], 'hex');
              const encryptedText = parts[2];
              
              const decipher = crypto.createDecipheriv(algorithm, key, iv);
              decipher.setAuthTag(authTag);
              
              let decrypted = decipher.update(encryptedText, 'hex', 'utf8');
              decrypted += decipher.final('utf8');
              
              return decrypted;
            }
          `
        },
        
        pii: {
          fields: ['email', 'phone', 'address', 'pan', 'aadhaar'],
          handling: {
            storage: 'Encrypted',
            logs: 'Redacted',
            exports: 'Anonymized'
          }
        },
        
        audit: {
          events: [
            'Login attempts',
            'Data access',
            'Configuration changes',
            'Admin actions'
          ],
          retention: '1 year',
          implementation: 'Database triggers + Sentry'
        }
      }
    };

#### 5.2 Security Best Practices

typescript

    // Security Best Practices Implementation
    const securityBestPractices = {
      // SQL Injection Prevention
      sqlInjection: {
        prevention: 'Parameterized queries only',
        example: `
          // ❌ Bad - SQL Injection vulnerable
          const query = \`SELECT * FROM users WHERE email = '\${email}'\`;
          
          // ✅ Good - Parameterized query
          const user = await db.users.findFirst({
            where: { email: email }
          });
          
          // ✅ Good - Raw query with parameters
          const users = await db.$queryRaw\`
            SELECT * FROM users 
            WHERE created_at > \${startDate}
            AND location = \${location}
          \`;
        `
      },
      
      // XSS Prevention
      xssPrevention: {
        strategy: 'Sanitize all user input',
        implementation: `
          import DOMPurify from 'isomorphic-dompurify';
          
          export function sanitizeInput(input: string): string {
            return DOMPurify.sanitize(input, {
              ALLOWED_TAGS: ['b', 'i', 'em', 'strong', 'a'],
              ALLOWED_ATTR: ['href']
            });
          }
          
          // React automatically escapes values
          // But be careful with dangerouslySetInnerHTML
          function SafeComponent({ userContent }) {
            return (
              <div>{userContent}</div> // Safe - auto escaped
            );
          }
        `
      },
      
      // CSRF Protection
      csrfProtection: {
        method: 'Double Submit Cookie',
        implementation: `
          import csrf from 'csrf';
          
          const tokens = new csrf();
          
          export function csrfMiddleware(req, res, next) {
            if (req.method === 'GET') {
              // Generate token
              const secret = tokens.secretSync();
              const token = tokens.create(secret);
              
              res.cookie('csrf-secret', secret, { httpOnly: true });
              res.locals.csrfToken = token;
            } else {
              // Verify token
              const secret = req.cookies['csrf-secret'];
              const token = req.headers['x-csrf-token'];
              
              if (!tokens.verify(secret, token)) {
                return res.status(403).json({ error: 'Invalid CSRF token' });
              }
            }
            
            next();
          }
        `
      },
      
      // Secret Management
      secretManagement: {
        storage: 'Environment variables',
        rotation: 'Quarterly',
        implementation: `
          // .env.local (never commit)
          DATABASE_URL=postgresql://...
          SUPABASE_ANON_KEY=...
          SUPABASE_SERVICE_KEY=...
          ENCRYPTION_KEY=...
          
          // Validation on startup
          const requiredEnvVars = [
            'DATABASE_URL',
            'SUPABASE_URL',
            'SUPABASE_ANON_KEY',
            'NEXT_PUBLIC_SUPABASE_URL'
          ];
          
          for (const envVar of requiredEnvVars) {
            if (!process.env[envVar]) {
              throw new Error(\`Missing required environment variable: \${envVar}\`);
            }
          }
        `
      }
    };

* * *

Part 2: Database Design
-----------------------

### 6\. Complete Database Schema

#### 6.1 Schema Overview

sql

    -- Enable required extensions
    CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
    CREATE EXTENSION IF NOT EXISTS "pg_trgm"; -- For fuzzy text search
    CREATE EXTENSION IF NOT EXISTS "postgis"; -- For location data (future)
    
    -- Create schemas for logical separation
    CREATE SCHEMA IF NOT EXISTS auth; -- Managed by Supabase
    CREATE SCHEMA IF NOT EXISTS public; -- Main application data
    CREATE SCHEMA IF NOT EXISTS audit; -- Audit logs
    CREATE SCHEMA IF NOT EXISTS analytics; -- Analytics data

#### 6.2 Core Domain Tables

sql

    -- Users and Authentication (extends Supabase auth)
    CREATE TABLE public.users (
      id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
      email TEXT UNIQUE NOT NULL,
      full_name TEXT,
      phone TEXT UNIQUE,
      phone_verified BOOLEAN DEFAULT FALSE,
      avatar_url TEXT,
      preferences JSONB DEFAULT '{}',
      metadata JSONB DEFAULT '{}',
      created_at TIMESTAMPTZ DEFAULT NOW(),
      updated_at TIMESTAMPTZ DEFAULT NOW()
    );
    
    -- User roles and permissions
    CREATE TABLE public.roles (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      name TEXT UNIQUE NOT NULL,
      description TEXT,
      permissions JSONB DEFAULT '[]',
      created_at TIMESTAMPTZ DEFAULT NOW()
    );
    
    CREATE TABLE public.user_roles (
      user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
      role_id UUID REFERENCES public.roles(id) ON DELETE CASCADE,
      granted_at TIMESTAMPTZ DEFAULT NOW(),
      granted_by UUID REFERENCES public.users(id),
      PRIMARY KEY (user_id, role_id)
    );
    
    -- Locations and regional data
    CREATE TABLE public.locations (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      code TEXT UNIQUE NOT NULL, -- 'delhi', 'gurgaon', etc.
      name TEXT NOT NULL,
      state TEXT NOT NULL,
      country TEXT DEFAULT 'India',
      timezone TEXT DEFAULT 'Asia/Kolkata',
      
      -- Regional factors
      base_cost_index DECIMAL(3,2) DEFAULT 1.00,
      labor_availability TEXT CHECK (labor_availability IN ('high', 'medium', 'low')),
      
      -- Municipal rules
      setback_rules JSONB NOT NULL DEFAULT '{
        "front": 3.0,
        "rear": 3.0,
        "sides": 2.0
      }',
      max_ground_coverage DECIMAL(3,2) DEFAULT 0.75,
      max_far DECIMAL(3,2) DEFAULT 2.50,
      height_restrictions JSONB,
      
      -- Environmental factors
      seismic_zone TEXT CHECK (seismic_zone IN ('II', 'III', 'IV', 'V')),
      soil_type_common TEXT,
      avg_water_table_depth DECIMAL(4,1), -- in feet
      
      -- Seasonal factors
      monsoon_months INTEGER[] DEFAULT '{6,7,8,9}',
      extreme_heat_months INTEGER[] DEFAULT '{4,5,6}',
      extreme_cold_months INTEGER[] DEFAULT '{12,1}',
      
      -- Special requirements
      special_requirements JSONB DEFAULT '[]',
      
      -- Metadata
      is_active BOOLEAN DEFAULT TRUE,
      created_at TIMESTAMPTZ DEFAULT NOW(),
      updated_at TIMESTAMPTZ DEFAULT NOW()
    );
    
    -- Material categories (hierarchical)
    CREATE TABLE public.categories (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      parent_id UUID REFERENCES public.categories(id) ON DELETE CASCADE,
      code TEXT UNIQUE NOT NULL,
      name TEXT NOT NULL,
      description TEXT,
      icon TEXT,
      display_order INTEGER DEFAULT 0,
      is_active BOOLEAN DEFAULT TRUE,
      metadata JSONB DEFAULT '{}',
      created_at TIMESTAMPTZ DEFAULT NOW(),
      updated_at TIMESTAMPTZ DEFAULT NOW()
    );
    
    -- Brands
    CREATE TABLE public.brands (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      code TEXT UNIQUE NOT NULL,
      name TEXT NOT NULL,
      logo_url TEXT,
      website TEXT,
      tier TEXT CHECK (tier IN ('economy', 'standard', 'premium')) DEFAULT 'standard',
      categories UUID[] DEFAULT '{}', -- Array of category IDs
      
      -- Business details
      manufacturer_name TEXT,
      country_of_origin TEXT,
      
      -- Ratings and certifications
      quality_rating DECIMAL(2,1) CHECK (quality_rating >= 0 AND quality_rating <= 5),
      certifications JSONB DEFAULT '[]',
      
      -- Status
      is_verified BOOLEAN DEFAULT FALSE,
      is_active BOOLEAN DEFAULT TRUE,
      
      created_at TIMESTAMPTZ DEFAULT NOW(),
      updated_at TIMESTAMPTZ DEFAULT NOW()
    );
    
    -- Materials/Components master table
    CREATE TABLE public.materials (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      category_id UUID REFERENCES public.categories(id) NOT NULL,
      brand_id UUID REFERENCES public.brands(id),
      
      -- Basic info
      code TEXT UNIQUE NOT NULL, -- Internal SKU
      name TEXT NOT NULL,
      display_name TEXT NOT NULL,
      variant_name TEXT, -- Size, color, model variant
      manufacturer_sku TEXT,
      
      -- Specifications
      base_unit TEXT NOT NULL, -- 'sqft', 'piece', 'bag', 'cum', 'kg'
      base_unit_quantity DECIMAL(10,3) DEFAULT 1, -- For packs
      dimensions JSONB, -- {length, width, height, thickness}
      weight DECIMAL(10,3), -- in kg
      specifications JSONB DEFAULT '{}', -- All technical specs
      
      -- Quality and features
      quality_tier TEXT CHECK (quality_tier IN ('smart', 'premium', 'luxury')),
      features JSONB DEFAULT '[]',
      suitable_for JSONB DEFAULT '[]', -- ['bedroom', 'bathroom', 'kitchen']
      
      -- Installation
      installation_complexity TEXT CHECK (installation_complexity IN ('diy', 'simple', 'moderate', 'complex')),
      installation_time_hours DECIMAL(5,2),
      requires_specialist BOOLEAN DEFAULT FALSE,
      
      -- Sustainability
      eco_friendly BOOLEAN DEFAULT FALSE,
      eco_certifications JSONB DEFAULT '[]',
      recyclable BOOLEAN DEFAULT FALSE,
      voc_level TEXT CHECK (voc_level IN ('none', 'low', 'medium', 'high')),
      
      -- Media
      primary_image_url TEXT,
      image_urls TEXT[] DEFAULT '{}',
      video_urls TEXT[] DEFAULT '{}',
      document_urls JSONB DEFAULT '[]', -- [{name, url, type}]
      
      -- Content
      description TEXT,
      key_benefits JSONB DEFAULT '[]',
      installation_guide TEXT,
      maintenance_guide TEXT,
      
      -- Search
      search_vector tsvector GENERATED ALWAYS AS (
        setweight(to_tsvector('english', coalesce(name, '')), 'A') ||
        setweight(to_tsvector('english', coalesce(display_name, '')), 'A') ||
        setweight(to_tsvector('english', coalesce(variant_name, '')), 'B') ||
        setweight(to_tsvector('english', coalesce(description, '')), 'C')
      ) STORED,
      
      -- Status
      is_active BOOLEAN DEFAULT TRUE,
      is_featured BOOLEAN DEFAULT FALSE,
      launch_date DATE,
      discontinue_date DATE,
      
      created_at TIMESTAMPTZ DEFAULT NOW(),
      updated_at TIMESTAMPTZ DEFAULT NOW()
    );
    
    -- Material pricing (location and time specific)
    CREATE TABLE public.material_prices (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      material_id UUID REFERENCES public.materials(id) ON DELETE CASCADE NOT NULL,
      location_id UUID REFERENCES public.locations(id) NOT NULL,
      
      -- Pricing
      base_price DECIMAL(12,2) NOT NULL, -- Pre-GST price
      currency TEXT DEFAULT 'INR',
      price_unit TEXT NOT NULL, -- Usually same as base_unit
      
      -- GST
      gst_rate DECIMAL(4,2) DEFAULT 18.00,
      is_gst_inclusive BOOLEAN DEFAULT FALSE,
      
      -- Validity
      valid_from DATE DEFAULT CURRENT_DATE,
      valid_to DATE,
      
      -- Market info
      market_price DECIMAL(12,2), -- MRP if different
      discount_percentage DECIMAL(4,2),
      
      -- Supply info
      availability TEXT CHECK (availability IN ('in_stock', 'low_stock', 'out_of_stock', 'discontinued')) DEFAULT 'in_stock',
      lead_time_days INTEGER DEFAULT 0,
      min_order_quantity DECIMAL(10,2) DEFAULT 1,
      
      -- Source
      source TEXT CHECK (source IN ('vendor', 'market_survey', 'online', 'manufacturer')) DEFAULT 'vendor',
      source_reference TEXT,
      verified_date DATE,
      verified_by UUID REFERENCES public.users(id),
      
      created_at TIMESTAMPTZ DEFAULT NOW(),
      updated_at TIMESTAMPTZ DEFAULT NOW(),
      
      UNIQUE(material_id, location_id, valid_from)
    );
    
    -- Engineering parameters
    CREATE TABLE public.engineering_parameters (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      category TEXT NOT NULL,
      parameter_key TEXT UNIQUE NOT NULL,
      parameter_value TEXT NOT NULL,
      parameter_type TEXT NOT NULL CHECK (parameter_type IN ('number', 'text', 'boolean', 'json')),
      unit TEXT,
      description TEXT,
      
      -- Validation
      min_value DECIMAL(15,5),
      max_value DECIMAL(15,5),
      allowed_values JSONB,
      
      -- Applicability
      applicable_locations UUID[] DEFAULT '{}',
      applicable_structure_types TEXT[] DEFAULT '{}',
      
      -- Source and verification
      source_reference TEXT,
      is_code_mandated BOOLEAN DEFAULT FALSE,
      code_reference TEXT, -- IS code reference
      
      created_at TIMESTAMPTZ DEFAULT NOW(),
      updated_at TIMESTAMPTZ DEFAULT NOW()
    );
    
    -- Consumption recipes (material requirements for work items)
    CREATE TABLE public.work_items (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      category TEXT NOT NULL,
      code TEXT UNIQUE NOT NULL,
      name TEXT NOT NULL,
      description TEXT,
      output_unit TEXT NOT NULL,
      output_quantity DECIMAL(10,3) DEFAULT 1,
      
      -- Categorization
      work_category TEXT CHECK (work_category IN ('structure', 'finishing', 'mep', 'external')),
      work_phase TEXT,
      
      is_active BOOLEAN DEFAULT TRUE,
      created_at TIMESTAMPTZ DEFAULT NOW(),
      updated_at TIMESTAMPTZ DEFAULT NOW()
    );
    
    CREATE TABLE public.work_item_materials (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      work_item_id UUID REFERENCES public.work_items(id) ON DELETE CASCADE,
      material_category TEXT NOT NULL, -- Generic category like 'cement', 'sand'
      
      -- Consumption
      consumption_quantity DECIMAL(10,5) NOT NULL,
      consumption_unit TEXT NOT NULL,
      wastage_percentage DECIMAL(4,2) DEFAULT 0,
      
      -- Rules
      quality_tier_mapping JSONB DEFAULT '{
        "smart": "standard_grade",
        "premium": "premium_grade",
        "luxury": "premium_grade"
      }',
      
      notes TEXT,
      
      created_at TIMESTAMPTZ DEFAULT NOW()
    );
    
    -- Labor types and rates
    CREATE TABLE public.labor_types (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      code TEXT UNIQUE NOT NULL,
      name TEXT NOT NULL,
      category TEXT NOT NULL,
      skill_level TEXT CHECK (skill_level IN ('unskilled', 'semi_skilled', 'skilled', 'highly_skilled')),
      
      -- Work types
      work_types JSONB DEFAULT '[]', -- ['masonry', 'plaster', 'tile']
      tools_included BOOLEAN DEFAULT FALSE,
      helper_included BOOLEAN DEFAULT FALSE,
      
      -- Productivity
      avg_productivity JSONB DEFAULT '{}', -- {masonry: '100 sqft/day'}
      
      is_active BOOLEAN DEFAULT TRUE,
      created_at TIMESTAMPTZ DEFAULT NOW(),
      updated_at TIMESTAMPTZ DEFAULT NOW()
    );
    
    CREATE TABLE public.labor_rates (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      labor_type_id UUID REFERENCES public.labor_types(id) NOT NULL,
      location_id UUID REFERENCES public.locations(id) NOT NULL,
      
      -- Rate structure
      rate_type TEXT CHECK (rate_type IN ('daily', 'hourly', 'sqft', 'piece', 'contract')) NOT NULL,
      base_rate DECIMAL(10,2) NOT NULL,
      overtime_rate DECIMAL(10,2),
      
      -- Additional costs
      food_allowance DECIMAL(8,2) DEFAULT 0,
      transport_allowance DECIMAL(8,2) DEFAULT 0,
      
      -- Validity
      valid_from DATE DEFAULT CURRENT_DATE,
      valid_to DATE,
      
      -- Seasonal multipliers
      season_multipliers JSONB DEFAULT '{
        "normal": 1.0,
        "monsoon": 1.1,
        "festival": 1.15,
        "peak": 1.05
      }',
      
      created_at TIMESTAMPTZ DEFAULT NOW(),
      updated_at TIMESTAMPTZ DEFAULT NOW(),
      
      UNIQUE(labor_type_id, location_id, rate_type, valid_from)
    );

#### 6.3 Project and User Data Tables

sql

    -- User projects
    CREATE TABLE public.projects (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      user_id UUID REFERENCES public.users(id) NOT NULL,
      
      -- Basic info
      name TEXT NOT NULL,
      description TEXT,
      project_type TEXT CHECK (project_type IN ('new_construction', 'renovation', 'extension')) DEFAULT 'new_construction',
      
      -- Location and plot
      location_id UUID REFERENCES public.locations(id) NOT NULL,
      plot_address TEXT,
      plot_details JSONB NOT NULL DEFAULT '{}', -- {length, width, area, shape}
      
      -- Structure
      structure_type TEXT CHECK (structure_type IN ('rcc_frame', 'load_bearing', 'steel_frame', 'composite')),
      floors_above_ground INTEGER NOT NULL,
      has_stilt BOOLEAN DEFAULT FALSE,
      has_basement BOOLEAN DEFAULT FALSE,
      
      -- Configuration
      building_type TEXT CHECK (building_type IN ('single_family', 'multi_family', 'mixed_use')),
      family_type TEXT CHECK (family_type IN ('nuclear', 'joint', 'investment')),
      quality_tier TEXT CHECK (quality_tier IN ('smart', 'premium', 'luxury')) DEFAULT 'premium',
      
      -- Layout
      layout_config JSONB DEFAULT '{}', -- Room counts and types
      total_built_up_area DECIMAL(10,2),
      
      -- Selections
      material_selections JSONB DEFAULT '{}', -- {category: material_id}
      custom_specifications JSONB DEFAULT '{}',
      
      -- Site conditions
      site_conditions JSONB DEFAULT '{}', -- {soil_type, water_table, slope}
      
      -- Calculations
      latest_calculation_id UUID,
      calculation_version INTEGER DEFAULT 0,
      
      -- Status
      status TEXT CHECK (status IN ('draft', 'configured', 'finalized', 'archived')) DEFAULT 'draft',
      completion_percentage DECIMAL(5,2) DEFAULT 0,
      
      -- Sharing
      share_token TEXT UNIQUE,
      is_public BOOLEAN DEFAULT FALSE,
      
      -- Metadata
      tags TEXT[] DEFAULT '{}',
      source TEXT, -- How user found us
      
      created_at TIMESTAMPTZ DEFAULT NOW(),
      updated_at TIMESTAMPTZ DEFAULT NOW()
    );
    
    -- Project calculations (versioned)
    CREATE TABLE public.project_calculations (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE NOT NULL,
      version INTEGER NOT NULL,
      
      -- Calculation input snapshot
      input_data JSONB NOT NULL, -- Complete snapshot of inputs
      
      -- Results
      calculation_results JSONB NOT NULL, -- Detailed breakdown
      
      -- Summary
      total_cost DECIMAL(15,2) NOT NULL,
      cost_per_sqft DECIMAL(10,2),
      
      -- Breakdown
      structure_cost DECIMAL(15,2),
      finishing_cost DECIMAL(15,2),
      mep_cost DECIMAL(15,2),
      external_cost DECIMAL(15,2),
      professional_cost DECIMAL(15,2),
      statutory_cost DECIMAL(15,2),
      
      -- Metadata
      confidence_score DECIMAL(3,2) DEFAULT 0.85, -- 0-1
      assumptions JSONB DEFAULT '[]',
      warnings JSONB DEFAULT '[]',
      
      -- Performance
      calculation_time_ms INTEGER,
      
      created_at TIMESTAMPTZ DEFAULT NOW(),
      created_by UUID REFERENCES public.users(id),
      
      UNIQUE(project_id, version)
    );
    
    -- Project collaborators
    CREATE TABLE public.project_collaborators (
      project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE,
      user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
      role TEXT CHECK (role IN ('owner', 'editor', 'viewer')) DEFAULT 'viewer',
      permissions JSONB DEFAULT '[]',
      
      invited_by UUID REFERENCES public.users(id),
      invited_at TIMESTAMPTZ DEFAULT NOW(),
      accepted_at TIMESTAMPTZ,
      
      PRIMARY KEY (project_id, user_id)
    );
    
    -- Project activity log
    CREATE TABLE public.project_activities (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE NOT NULL,
      user_id UUID REFERENCES public.users(id),
      
      activity_type TEXT NOT NULL,
      activity_data JSONB DEFAULT '{}',
      
      ip_address INET,
      user_agent TEXT,
      
      created_at TIMESTAMPTZ DEFAULT NOW()
    );
    
    -- Saved material combinations (Style bundles)
    CREATE TABLE public.style_bundles (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      name TEXT NOT NULL,
      description TEXT,
      category TEXT CHECK (category IN ('complete_home', 'room_specific', 'custom')),
      
      -- Target
      quality_tier TEXT CHECK (quality_tier IN ('smart', 'premium', 'luxury')),
      suitable_for JSONB DEFAULT '[]', -- ['modern', 'traditional', 'minimalist']
      
      -- Materials
      material_selections JSONB NOT NULL, -- {flooring: material_id, walls: material_id}
      
      -- Imagery
      preview_image_url TEXT,
      gallery_urls TEXT[] DEFAULT '{}',
      
      -- Metadata
      created_by UUID REFERENCES public.users(id),
      is_featured BOOLEAN DEFAULT FALSE,
      usage_count INTEGER DEFAULT 0,
      
      created_at TIMESTAMPTZ DEFAULT NOW(),
      updated_at TIMESTAMPTZ DEFAULT NOW()
    );

#### 6.4 Professional Services and Compliance Tables

sql

    -- Professional services catalog
    CREATE TABLE public.professional_services (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      code TEXT UNIQUE NOT NULL,
      name TEXT NOT NULL,
      category TEXT CHECK (category IN ('design', 'engineering', 'supervision', 'consultation')),
      
      -- Calculation methods
      calculation_methods JSONB NOT NULL DEFAULT '[]', -- Array of {type, rate, base, min, max}
      
      -- Deliverables
      deliverables JSONB DEFAULT '[]',
      typical_duration_days INTEGER,
      
      -- Requirements
      is_mandatory BOOLEAN DEFAULT FALSE,
      mandatory_conditions JSONB, -- {floors: '>3', area: '>5000'}
      
      -- Display
      display_order INTEGER DEFAULT 0,
      description TEXT,
      
      is_active BOOLEAN DEFAULT TRUE,
      created_at TIMESTAMPTZ DEFAULT NOW(),
      updated_at TIMESTAMPTZ DEFAULT NOW()
    );
    
    -- Location-specific professional service rates
    CREATE TABLE public.professional_service_rates (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      service_id UUID REFERENCES public.professional_services(id) NOT NULL,
      location_id UUID REFERENCES public.locations(id) NOT NULL,
      
      -- Rates for different calculation methods
      rates JSONB NOT NULL, -- {percentage: 3.5, per_sqft: 50, lumpsum: 200000}
      
      -- Validity
      valid_from DATE DEFAULT CURRENT_DATE,
      valid_to DATE,
      
      created_at TIMESTAMPTZ DEFAULT NOW(),
      updated_at TIMESTAMPTZ DEFAULT NOW(),
      
      UNIQUE(service_id, location_id, valid_from)
    );
    
    -- Statutory charges and compliance
    CREATE TABLE public.statutory_charges (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      location_id UUID REFERENCES public.locations(id) NOT NULL,
      
      -- Charge details
      code TEXT NOT NULL,
      name TEXT NOT NULL,
      authority TEXT NOT NULL,
      category TEXT CHECK (category IN ('approval', 'noc', 'utility', 'certificate', 'tax')),
      
      -- Calculation
      calculation_type TEXT CHECK (calculation_type IN ('fixed', 'percentage', 'per_sqft', 'per_unit')) NOT NULL,
      calculation_value DECIMAL(12,4) NOT NULL,
      calculation_base TEXT, -- 'project_cost', 'built_up_area', 'plot_area'
      
      -- Conditions
      applicable_conditions JSONB DEFAULT '{}', -- {min_area: 500, floors: '>2'}
      
      -- Process
      typical_duration_days INTEGER,
      documents_required JSONB DEFAULT '[]',
      
      -- Validity
      valid_from DATE DEFAULT CURRENT_DATE,
      valid_to DATE,
      
      is_mandatory BOOLEAN DEFAULT TRUE,
      is_active BOOLEAN DEFAULT TRUE,
      
      created_at TIMESTAMPTZ DEFAULT NOW(),
      updated_at TIMESTAMPTZ DEFAULT NOW(),
      
      UNIQUE(location_id, code, valid_from)
    );

#### 6.5 Vendor and Marketplace Tables

sql

    -- Vendors
    CREATE TABLE public.vendors (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      
      -- Business details
      business_name TEXT NOT NULL,
      legal_name TEXT,
      vendor_type TEXT CHECK (vendor_type IN ('manufacturer', 'distributor', 'retailer', 'contractor')),
      
      -- Registration
      gstin TEXT UNIQUE,
      pan TEXT,
      registration_number TEXT,
      
      -- Contact
      primary_contact_name TEXT,
      primary_contact_phone TEXT,
      primary_contact_email TEXT,
      
      -- Address
      address JSONB NOT NULL,
      service_locations UUID[] DEFAULT '{}', -- Array of location IDs
      
      -- Business info
      establishment_year INTEGER,
      employee_count TEXT CHECK (employee_count IN ('1-10', '11-50', '51-200', '200+')),
      annual_turnover TEXT CHECK (annual_turnover IN ('<1cr', '1-10cr', '10-50cr', '50cr+')),
      
      -- Categories
      material_categories UUID[] DEFAULT '{}',
      brands_handled UUID[] DEFAULT '{}',
      
      -- Commercial terms
      payment_terms JSONB DEFAULT '{}',
      delivery_terms JSONB DEFAULT '{}',
      return_policy JSONB DEFAULT '{}',
      
      -- Performance
      rating DECIMAL(2,1) DEFAULT 0.0,
      total_orders INTEGER DEFAULT 0,
      fulfilled_orders INTEGER DEFAULT 0,
      
      -- Verification
      is_verified BOOLEAN DEFAULT FALSE,
      verified_at TIMESTAMPTZ,
      verified_by UUID REFERENCES public.users(id),
      verification_documents JSONB DEFAULT '[]',
      
      -- Status
      status TEXT CHECK (status IN ('pending', 'active', 'suspended', 'blacklisted')) DEFAULT 'pending',
      
      created_at TIMESTAMPTZ DEFAULT NOW(),
      updated_at TIMESTAMPTZ DEFAULT NOW()
    );
    
    -- Vendor catalogs
    CREATE TABLE public.vendor_catalogs (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      vendor_id UUID REFERENCES public.vendors(id) NOT NULL,
      
      -- Catalog info
      name TEXT NOT NULL,
      description TEXT,
      valid_from DATE DEFAULT CURRENT_DATE,
      valid_to DATE,
      
      -- Upload details
      file_url TEXT,
      file_type TEXT CHECK (file_type IN ('pdf', 'excel', 'csv', 'json')),
      file_size_kb INTEGER,
      
      -- Processing
      status TEXT CHECK (status IN ('uploaded', 'processing', 'processed', 'failed')) DEFAULT 'uploaded',
      processed_at TIMESTAMPTZ,
      error_details JSONB,
      
      -- Extracted data
      item_count INTEGER DEFAULT 0,
      processed_items JSONB DEFAULT '[]',
      
      created_at TIMESTAMPTZ DEFAULT NOW(),
      created_by UUID REFERENCES public.users(id)
    );
    
    -- Vendor material pricing
    CREATE TABLE public.vendor_material_prices (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      vendor_id UUID REFERENCES public.vendors(id) NOT NULL,
      material_id UUID REFERENCES public.materials(id) NOT NULL,
      location_id UUID REFERENCES public.locations(id) NOT NULL,
      
      -- Pricing
      dealer_price DECIMAL(12,2) NOT NULL,
      retail_price DECIMAL(12,2),
      bulk_price DECIMAL(12,2),
      
      -- Bulk pricing tiers
      pricing_tiers JSONB DEFAULT '[]', -- [{min_qty: 100, price: 45}, {min_qty: 500, price: 42}]
      
      -- Terms
      min_order_quantity DECIMAL(10,2) DEFAULT 1,
      delivery_charges JSONB DEFAULT '{}',
      
      -- Validity
      valid_from DATE DEFAULT CURRENT_DATE,
      valid_to DATE,
      
      created_at TIMESTAMPTZ DEFAULT NOW(),
      updated_at TIMESTAMPTZ DEFAULT NOW(),
      
      UNIQUE(vendor_id, material_id, location_id, valid_from)
    );
    
    -- Quotations
    CREATE TABLE public.quotations (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      project_id UUID REFERENCES public.projects(id) NOT NULL,
      vendor_id UUID REFERENCES public.vendors(id) NOT NULL,
      
      -- Quote details
      quote_number TEXT UNIQUE NOT NULL,
      quote_date DATE DEFAULT CURRENT_DATE,
      valid_until DATE,
      
      -- Items
      line_items JSONB NOT NULL, -- Array of {material_id, quantity, rate, amount}
      
      -- Totals
      subtotal DECIMAL(15,2) NOT NULL,
      tax_amount DECIMAL(15,2) DEFAULT 0,
      delivery_charges DECIMAL(10,2) DEFAULT 0,
      other_charges JSONB DEFAULT '{}',
      total_amount DECIMAL(15,2) NOT NULL,
      
      -- Terms
      payment_terms TEXT,
      delivery_terms TEXT,
      special_conditions TEXT,
      
      -- Status
      status TEXT CHECK (status IN ('draft', 'sent', 'viewed', 'accepted', 'rejected', 'expired')) DEFAULT 'draft',
      
      -- Tracking
      sent_at TIMESTAMPTZ,
      viewed_at TIMESTAMPTZ,
      responded_at TIMESTAMPTZ,
      response_notes TEXT,
      
      created_at TIMESTAMPTZ DEFAULT NOW(),
      updated_at TIMESTAMPTZ DEFAULT NOW()
    );

#### 6.6 Analytics and System Tables

sql

    -- User events for analytics
    CREATE TABLE analytics.user_events (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      user_id UUID REFERENCES public.users(id),
      session_id TEXT NOT NULL,
      
      -- Event details
      event_type TEXT NOT NULL,
      event_category TEXT,
      event_label TEXT,
      event_value JSONB,
      
      -- Context
      page_url TEXT,
      referrer_url TEXT,
      
      -- Device info
      device_type TEXT,
      browser TEXT,
      os TEXT,
      screen_resolution TEXT,
      
      -- Location
      ip_address INET,
      country TEXT,
      city TEXT,
      
      created_at TIMESTAMPTZ DEFAULT NOW()
    );
    
    -- Calculation performance metrics
    CREATE TABLE analytics.calculation_metrics (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      project_id UUID REFERENCES public.projects(id),
      calculation_id UUID,
      
      -- Performance
      total_time_ms INTEGER NOT NULL,
      db_time_ms INTEGER,
      compute_time_ms INTEGER,
      
      -- Cache performance
      cache_hits INTEGER DEFAULT 0,
      cache_misses INTEGER DEFAULT 0,
      
      -- Complexity metrics
      total_materials INTEGER,
      total_line_items INTEGER,
      total_calculations INTEGER,
      
      created_at TIMESTAMPTZ DEFAULT NOW()
    );
    
    -- Material popularity tracking
    CREATE TABLE analytics.material_selections (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      material_id UUID REFERENCES public.materials(id) NOT NULL,
      project_id UUID REFERENCES public.projects(id),
      user_id UUID REFERENCES public.users(id),
      
      -- Context
      selection_context TEXT, -- 'search', 'recommendation', 'bundle'
      quality_tier TEXT,
      location_id UUID REFERENCES public.locations(id),
      
      -- Action
      action TEXT CHECK (action IN ('viewed', 'selected', 'removed', 'finalized')),
      
      created_at TIMESTAMPTZ DEFAULT NOW()
    );
    
    -- System audit logs
    CREATE TABLE audit.audit_logs (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      
      -- Actor
      user_id UUID,
      admin_id UUID,
      ip_address INET,
      user_agent TEXT,
      
      -- Action
      action TEXT NOT NULL, -- 'INSERT', 'UPDATE', 'DELETE'
      table_name TEXT NOT NULL,
      record_id UUID,
      
      -- Changes
      old_values JSONB,
      new_values JSONB,
      changed_fields TEXT[],
      
      -- Context
      request_id TEXT,
      api_endpoint TEXT,
      
      created_at TIMESTAMPTZ DEFAULT NOW()
    );
    
    -- Feature flags
    CREATE TABLE public.feature_flags (
      flag_name TEXT PRIMARY KEY,
      description TEXT,
      
      -- Targeting
      is_enabled BOOLEAN DEFAULT FALSE,
      rollout_percentage INTEGER DEFAULT 0 CHECK (rollout_percentage >= 0 AND rollout_percentage <= 100),
      
      -- User targeting
      whitelist_users UUID[] DEFAULT '{}',
      blacklist_users UUID[] DEFAULT '{}',
      
      -- Location targeting
      enabled_locations UUID[] DEFAULT '{}',
      
      -- Configuration
      configuration JSONB DEFAULT '{}',
      
      -- Metadata
      created_at TIMESTAMPTZ DEFAULT NOW(),
      updated_at TIMESTAMPTZ DEFAULT NOW(),
      updated_by UUID REFERENCES public.users(id)
    );
    
    -- Job queue (using pg_boss schema)
    -- This is managed by pg_boss, but here's the structure for reference
    /*
    CREATE TABLE pgboss.job (
      id UUID PRIMARY KEY,
      name TEXT NOT NULL,
      priority INTEGER NOT NULL DEFAULT 0,
      data JSONB,
      state TEXT NOT NULL,
      retryLimit INTEGER NOT NULL DEFAULT 0,
      retryCount INTEGER NOT NULL DEFAULT 0,
      retryDelay INTEGER NOT NULL DEFAULT 0,
      retryBackoff BOOLEAN NOT NULL DEFAULT false,
      startAfter TIMESTAMPTZ NOT NULL DEFAULT now(),
      startedOn TIMESTAMPTZ,
      singletonKey TEXT,
      singletonOn TIMESTAMPTZ,
      expireIn INTERVAL NOT NULL DEFAULT interval '15 minutes',
      createdOn TIMESTAMPTZ NOT NULL DEFAULT now(),
      completedOn TIMESTAMPTZ,
      keepUntil TIMESTAMPTZ NOT NULL DEFAULT now() + interval '30 days'
    );
    */

### 7\. Data Models & Relationships

#### 7.1 Entity Relationship Diagram

mermaid


    erDiagram
       Users ||--o{ Projects : creates
       Users ||--o{ UserRoles : has
       Users ||--o{ ProjectCollaborators : collaborates
       
       Projects ||--|| Locations : located_in
       Projects ||--o{ ProjectCalculations : has_versions
       Projects ||--o{ ProjectActivities : tracks
       Projects ||--o{ Quotations : receives
       
       Materials ||--|| Categories : belongs_to
       Materials ||--o| Brands : manufactured_by
       Materials ||--o{ MaterialPrices : has_prices
       Materials ||--o{ VendorMaterialPrices : vendor_prices
       
       Locations ||--o{ MaterialPrices : price_varies_by
       Locations ||--o{ LaborRates : labor_varies_by
       Locations ||--o{ StatutoryCharges : charges_by
       
       Vendors ||--o{ VendorCatalogs : uploads
       Vendors ||--o{ VendorMaterialPrices : offers
       Vendors ||--o{ Quotations : provides
       
       WorkItems ||--o{ WorkItemMaterials : requires
       
       Categories ||--o| Categories : parent_child

#### 7.2 Data Model Specifications

typescript

    // TypeScript interfaces matching database schema
    interface User {
      id: string;
      email: string;
      fullName?: string;
      phone?: string;
      phoneVerified: boolean;
      avatarUrl?: string;
      preferences: UserPreferences;
      metadata: Record<string, any>;
      createdAt: Date;
      updatedAt: Date;
    }
    
    interface UserPreferences {
      theme: 'light' | 'dark' | 'system';
      language: 'en' | 'hi';
      notifications: {
        email: boolean;
        sms: boolean;
        whatsapp: boolean;
      };
      defaultLocation?: string;
      defaultQualityTier?: 'smart' | 'premium' | 'luxury';
    }
    
    interface Project {
      id: string;
      userId: string;
      name: string;
      description?: string;
      projectType: 'new_construction' | 'renovation' | 'extension';
      
      // Location and plot
      locationId: string;
      plotAddress?: string;
      plotDetails: PlotDetails;
      
      // Structure
      structureType: 'rcc_frame' | 'load_bearing' | 'steel_frame' | 'composite';
      floorsAboveGround: number;
      hasStilt: boolean;
      hasBasement: boolean;
      
      // Configuration
      buildingType: 'single_family' | 'multi_family' | 'mixed_use';
      familyType: 'nuclear' | 'joint' | 'investment';
      qualityTier: 'smart' | 'premium' | 'luxury';
      
      // Layout
      layoutConfig: LayoutConfig;
      totalBuiltUpArea: number;
      
      // Selections
      materialSelections: Record<string, string>; // category -> materialId
      customSpecifications: Record<string, any>;
      
      // Site conditions
      siteConditions: SiteConditions;
      
      // Status
      status: 'draft' | 'configured' | 'finalized' | 'archived';
      completionPercentage: number;
      
      // Calculations
      latestCalculationId?: string;
      calculationVersion: number;
      
      // Sharing
      shareToken?: string;
      isPublic: boolean;
      
      // Metadata
      tags: string[];
      source?: string;
      createdAt: Date;
      updatedAt: Date;
    }
    
    interface PlotDetails {
      length: number;
      width: number;
      area: number;
      shape: 'regular' | 'irregular';
      cornerPlot: boolean;
      roadFacing: 'north' | 'south' | 'east' | 'west' | 'corner';
      customShape?: GeoJSON; // For irregular plots
    }
    
    interface LayoutConfig {
      bedrooms: number;
      bathrooms: number;
      kitchen: {
        count: number;
        type: 'open' | 'closed';
      };
      livingDining: {
        combined: boolean;
        area?: number;
      };
      additionalRooms: {
        poojaRoom?: boolean;
        studyRoom?: boolean;
        servantQuarter?: boolean;
        storeRooms?: number;
      };
      balconies: number;
      terraces: number;
    }
    
    interface Material {
      id: string;
      categoryId: string;
      brandId?: string;
      
      // Basic info
      code: string;
      name: string;
      displayName: string;
      variantName?: string;
      manufacturerSku?: string;
      
      // Specifications
      baseUnit: 'sqft' | 'piece' | 'bag' | 'cum' | 'kg' | 'liter';
      baseUnitQuantity: number;
      dimensions?: {
        length?: number;
        width?: number;
        height?: number;
        thickness?: number;
      };
      weight?: number;
      specifications: Record<string, any>;
      
      // Quality
      qualityTier: 'smart' | 'premium' | 'luxury';
      features: string[];
      suitableFor: string[];
      
      // Installation
      installationComplexity: 'diy' | 'simple' | 'moderate' | 'complex';
      installationTimeHours?: number;
      requiresSpecialist: boolean;
      
      // Media and content
      primaryImageUrl?: string;
      imageUrls: string[];
      description?: string;
      keyBenefits: string[];
      
      // Status
      isActive: boolean;
      isFeatured: boolean;
      
      createdAt: Date;
      updatedAt: Date;
    }
    
    interface Calculation {
      id: string;
      projectId: string;
      version: number;
      
      // Input snapshot
      inputData: Project;
      
      // Results
      totalCost: number;
      costPerSqft: number;
      
      // Breakdown
      breakdown: {
        structure: CategoryBreakdown;
        finishing: CategoryBreakdown;
        mep: CategoryBreakdown;
        external: CategoryBreakdown;
        professional: CategoryBreakdown;
        statutory: CategoryBreakdown;
      };
      
      // Additional costs
      contingency: number;
      escalation: number;
      taxes: {
        gst: number;
        otherTaxes: number;
      };
      
      // Metadata
      confidenceScore: number;
      assumptions: string[];
      warnings: string[];
      
      // Performance
      calculationTimeMs: number;
      
      createdAt: Date;
      createdBy: string;
    }
    
    interface CategoryBreakdown {
      total: number;
      percentage: number;
      items: LineItem[];
    }
    
    interface LineItem {
      id: string;
      name: string;
      description?: string;
      quantity: number;
      unit: string;
      rate: number;
      amount: number;
      materialId?: string;
      workItemId?: string;
      notes?: string;
    }

### 8\. Indexing Strategy

#### 8.1 Performance Indexes

sql

    -- Primary lookup indexes
    CREATE INDEX idx_materials_category ON public.materials(category_id);
    CREATE INDEX idx_materials_brand ON public.materials(brand_id);
    CREATE INDEX idx_materials_quality_tier ON public.materials(quality_tier);
    CREATE INDEX idx_materials_active ON public.materials(is_active) WHERE is_active = true;
    
    -- Search indexes
    CREATE INDEX idx_materials_search ON public.materials USING GIN(search_vector);
    CREATE INDEX idx_materials_name_trgm ON public.materials USING gin(name gin_trgm_ops);
    
    -- Price lookup indexes
    CREATE INDEX idx_material_prices_material_location ON public.material_prices(material_id, location_id);
    CREATE INDEX idx_material_prices_location ON public.material_prices(location_id);
    CREATE INDEX idx_material_prices_valid ON public.material_prices(valid_from, valid_to);
    
    -- Project indexes
    CREATE INDEX idx_projects_user ON public.projects(user_id);
    CREATE INDEX idx_projects_status ON public.projects(status);
    CREATE INDEX idx_projects_share_token ON public.projects(share_token) WHERE share_token IS NOT NULL;
    CREATE INDEX idx_projects_created ON public.projects(created_at DESC);
    
    -- Calculation indexes
    CREATE INDEX idx_calculations_project ON public.project_calculations(project_id);
    CREATE INDEX idx_calculations_version ON public.project_calculations(project_id, version DESC);
    
    -- Analytics indexes
    CREATE INDEX idx_user_events_user ON analytics.user_events(user_id);
    CREATE INDEX idx_user_events_session ON analytics.user_events(session_id);
    CREATE INDEX idx_user_events_type ON analytics.user_events(event_type);
    CREATE INDEX idx_user_events_created ON analytics.user_events(created_at DESC);
    
    -- Vendor indexes
    CREATE INDEX idx_vendors_status ON public.vendors(status);
    CREATE INDEX idx_vendor_materials_vendor ON public.vendor_material_prices(vendor_id);
    CREATE INDEX idx_vendor_materials_material ON public.vendor_material_prices(material_id);
    
    -- Composite indexes for common queries
    CREATE INDEX idx_material_location_price ON public.material_prices(material_id, location_id, valid_from DESC);
    CREATE INDEX idx_project_user_status ON public.projects(user_id, status, created_at DESC);

#### 8.2 Partial Indexes for Optimization

sql

    -- Active records only
    CREATE INDEX idx_materials_active_category ON public.materials(category_id) 
    WHERE is_active = true;
    
    CREATE INDEX idx_vendors_active ON public.vendors(id) 
    WHERE status = 'active';
    
    -- Recent data
    CREATE INDEX idx_projects_recent ON public.projects(created_at DESC) 
    WHERE created_at > CURRENT_DATE - INTERVAL '90 days';
    
    CREATE INDEX idx_calculations_recent ON public.project_calculations(created_at DESC) 
    WHERE created_at > CURRENT_DATE - INTERVAL '30 days';
    
    -- Specific status
    CREATE INDEX idx_projects_draft ON public.projects(user_id, updated_at DESC) 
    WHERE status = 'draft';
    
    CREATE INDEX idx_quotations_pending ON public.quotations(vendor_id, created_at DESC) 
    WHERE status IN ('sent', 'viewed');

### 9\. Data Migration & Versioning

#### 9.1 Migration Strategy

typescript

    // Database migration structure
    const migrationStrategy = {
      // Migration tool configuration
      tool: 'Supabase CLI migrations',
      
      structure: `
        /supabase
        ├── /migrations
        │   ├── 20240101000001_initial_schema.sql
        │   ├── 20240102000001_add_materials.sql
        │   ├── 20240103000001_add_indexes.sql
        │   └── 20240104000001_add_analytics.sql
        ├── /seed
        │   ├── 01_locations.sql
        │   ├── 02_categories.sql
        │   ├── 03_materials.sql
        │   └── 04_engineering_params.sql
        └── config.toml
      `,
      
      // Migration template
      template: `
        -- Migration: Add vendor rating system
        -- Version: 20240201000001
        -- Author: Team
        -- Description: Adds rating tables for vendor performance
        
        BEGIN;
        
        -- Create new tables
        CREATE TABLE public.vendor_ratings (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          vendor_id UUID REFERENCES public.vendors(id) NOT NULL,
          project_id UUID REFERENCES public.projects(id),
          user_id UUID REFERENCES public.users(id) NOT NULL,
          
          rating INTEGER CHECK (rating >= 1 AND rating <= 5) NOT NULL,
          review TEXT,
          
          created_at TIMESTAMPTZ DEFAULT NOW()
        );
        
        -- Add indexes
        CREATE INDEX idx_vendor_ratings_vendor ON public.vendor_ratings(vendor_id);
        
        -- Update vendor table
        ALTER TABLE public.vendors 
        ADD COLUMN IF NOT EXISTS avg_rating DECIMAL(2,1) DEFAULT 0.0,
        ADD COLUMN IF NOT EXISTS rating_count INTEGER DEFAULT 0;
        
        -- Create trigger to update average
        CREATE OR REPLACE FUNCTION update_vendor_rating()
        RETURNS TRIGGER AS $$
        BEGIN
          UPDATE public.vendors
          SET 
            avg_rating = (
              SELECT AVG(rating)::DECIMAL(2,1) 
              FROM public.vendor_ratings 
              WHERE vendor_id = NEW.vendor_id
            ),
            rating_count = (
              SELECT COUNT(*) 
              FROM public.vendor_ratings 
              WHERE vendor_id = NEW.vendor_id
            )
          WHERE id = NEW.vendor_id;
          
          RETURN NEW;
        END;
        $$ LANGUAGE plpgsql;
        
        CREATE TRIGGER trigger_update_vendor_rating
        AFTER INSERT OR UPDATE ON public.vendor_ratings
        FOR EACH ROW
        EXECUTE FUNCTION update_vendor_rating();
        
        COMMIT;
      `,
      
      // Rollback strategy
      rollback: `
        -- Always include rollback scripts
        BEGIN;
        
        DROP TRIGGER IF EXISTS trigger_update_vendor_rating ON public.vendor_ratings;
        DROP FUNCTION IF EXISTS update_vendor_rating();
        DROP TABLE IF EXISTS public.vendor_ratings;
        
        ALTER TABLE public.vendors 
        DROP COLUMN IF EXISTS avg_rating,
        DROP COLUMN IF EXISTS rating_count;
        
        COMMIT;
      `
    };

#### 9.2 Data Versioning Strategy

typescript

    // Version control for critical data
    const dataVersioning = {
      // Material price versioning
      materialPrices: {
        strategy: 'Temporal tables with validity periods',
        implementation: `
          -- Prices are never updated, only new versions inserted
          INSERT INTO public.material_prices (
            material_id,
            location_id,
            base_price,
            valid_from,
            valid_to
          ) VALUES (
            $1, $2, $3, 
            CURRENT_DATE,
            NULL
          );
          
          -- Mark previous price as expired
          UPDATE public.material_prices
          SET valid_to = CURRENT_DATE - INTERVAL '1 day'
          WHERE material_id = $1 
            AND location_id = $2
            AND valid_to IS NULL
            AND id != $newly_inserted_id;
        `
      },
      
      // Project calculation versioning
      calculations: {
        strategy: 'Immutable records with version numbers',
        implementation: `
          -- Each calculation is a new record
          INSERT INTO public.project_calculations (
            project_id,
            version,
            input_data,
            calculation_results,
            total_cost
          ) VALUES (
            $1,
            (SELECT COALESCE(MAX(version), 0) + 1 
             FROM project_calculations 
             WHERE project_id = $1),
            $2,
            $3,
            $4
          );
        `
      },
      
      // Audit trail
      auditTrail: {
        strategy: 'Trigger-based audit logging',
        implementation: `
          CREATE OR REPLACE FUNCTION audit_trigger()
          RETURNS TRIGGER AS $$
          BEGIN
            INSERT INTO audit.audit_logs (
              user_id,
              action,
              table_name,
              record_id,
              old_values,
              new_values,
              changed_fields
            ) VALUES (
              current_setting('app.current_user_id', true)::UUID,
              TG_OP,
              TG_TABLE_NAME,
              CASE 
                WHEN TG_OP = 'DELETE' THEN OLD.id
                ELSE NEW.id
              END,
              CASE WHEN TG_OP IN ('UPDATE', 'DELETE') 
                THEN row_to_json(OLD) 
                ELSE NULL 
              END,
              CASE WHEN TG_OP IN ('INSERT', 'UPDATE') 
                THEN row_to_json(NEW) 
                ELSE NULL 
              END,
              CASE WHEN TG_OP = 'UPDATE'
                THEN (
                  SELECT array_agg(key)
                  FROM jsonb_each(row_to_json(NEW)::jsonb)
                  WHERE value IS DISTINCT FROM (row_to_json(OLD)::jsonb)->>key
                )
                ELSE NULL
              END
            );
            
            RETURN NEW;
          END;
          $$ LANGUAGE plpgsql;
        `
      }
    };

### 10\. Backup & Recovery Strategy

#### 10.1 Backup Architecture

typescript

    const backupStrategy = {
      // Automated backups
      automated: {
        provider: 'Supabase managed backups',
        frequency: {
          full: 'Daily at 2 AM IST',
          incremental: 'Every 6 hours',
          wal: 'Continuous'
        },
        retention: {
          daily: '7 days',
          weekly: '4 weeks',
          monthly: '6 months'
        }
      },
      
      // Manual backup procedures
      manual: {
        // Full database backup
        fullBackup: `
          #!/bin/bash
          # backup.sh
          
          TIMESTAMP=$(date +%Y%m%d_%H%M%S)
          BACKUP_DIR="/backups/manual"
          DB_NAME="clarity_engine"
          
          # Create backup
          pg_dump $DATABASE_URL \
            --format=custom \
            --no-owner \
            --no-acl \
            --verbose \
            --file="$BACKUP_DIR/backup_$TIMESTAMP.dump"
          
          # Compress
          gzip "$BACKUP_DIR/backup_$TIMESTAMP.dump"
          
          # Upload to S3
          aws s3 cp \
            "$BACKUP_DIR/backup_$TIMESTAMP.dump.gz" \
            "s3://clarity-backups/manual/"
          
          # Clean old local backups (keep 7 days)
          find $BACKUP_DIR -name "*.dump.gz" -mtime +7 -delete
        `,
        
        // Selective backup
        selectiveBackup: `
          # Backup specific tables
          pg_dump $DATABASE_URL \
            --table=public.materials \
            --table=public.material_prices \
            --table=public.engineering_parameters \
            --data-only \
            --format=plain \
            > materials_data.sql
        `
      },
      
      // Point-in-time recovery
      pitr: {
        enabled: true,
        window: '7 days',
        procedure: `
          -- Restore to specific timestamp
          -- 1. Create new database from backup
          -- 2. Apply WAL logs up to target time
          -- 3. Verify data integrity
          -- 4. Switch application to restored database
        `
      }
    };

#### 10.2 Recovery Procedures

typescript

    const recoveryProcedures = {
      // Recovery scenarios
      scenarios: {
        // Data corruption
        dataCorruption: {
          detection: 'Integrity checks in monitoring',
          procedure: [
            'Identify corrupted tables/records',
            'Restore from latest clean backup',
            'Apply WAL logs to minimize data loss',
            'Verify data integrity',
            'Investigate root cause'
          ],
          rto: '2 hours', // Recovery Time Objective
          rpo: '1 hour'   // Recovery Point Objective
        },
        
        // Accidental deletion
        accidentalDeletion: {
          procedure: [
            'Check audit logs for deletion details',
            'Use PITR to restore before deletion',
            'Extract deleted records',
            'Insert back into production',
            'Update audit trail'
          ],
          rto: '30 minutes',
          rpo: '0 minutes'
        },
        
        // Complete disaster
        disaster: {
          procedure: [
            'Activate DR site',
            'Restore from geo-replicated backup',
            'Update DNS to point to DR site',
            'Verify all services operational',
            'Communicate with users'
          ],
          rto: '4 hours',
          rpo: '1 hour'
        }
      },
      
      // Testing procedures
      testing: {
        frequency: 'Monthly',
        procedure: `
          # Monthly DR test
          1. Create test environment
          2. Restore random backup
          3. Verify data integrity
          4. Test application functionality
          5. Document results
          6. Update procedures if needed
        `,
        
        validation: `
          -- Data integrity checks
          SELECT 
            'materials' as table_name,
            COUNT(*) as record_count,
            MAX(updated_at) as last_update
          FROM public.materials
          UNION ALL
          SELECT 
            'projects',
            COUNT(*),
            MAX(updated_at)
          FROM public.projects
          -- ... for all critical tables
        `
      }
    };

* * *

Part 3: API Design
------------------

### 11\. RESTful API Specifications

#### 11.1 API Architecture Overview

typescript

    // API Design Principles
    const apiDesignPrinciples = {
      style: 'RESTful with some RPC-style endpoints for complex operations',
      versioning: 'URL path versioning (/api/v1/)',
      authentication: 'JWT Bearer token',
      contentType: 'application/json',
      
      conventions: {
        urls: 'Kebab-case, plural for collections',
        methods: {
          GET: 'Read operations',
          POST: 'Create new resources',
          PUT: 'Full updates',
          PATCH: 'Partial updates',
          DELETE: 'Remove resources'
        },
        responses: {
          success: '2xx status codes',
          clientError: '4xx status codes',
          serverError: '5xx status codes'
        }
      },
      
      structure: {
        baseUrl: 'https://api.clarityengine.in',
        pattern: '/api/v1/{resource}/{id?}/{action?}'
      }
    };

#### 11.2 Core API Endpoints

typescript

    // API Endpoint Specifications
    const apiEndpoints = {
      // Authentication endpoints
      auth: {
        login: {
          method: 'POST',
          path: '/api/v1/auth/login',
          body: {
            email: 'string',
            password: 'string'
          },
          response: {
            success: true,
            data: {
              user: 'User object',
              token: 'JWT token',
              refreshToken: 'Refresh token',
              expiresAt: 'ISO timestamp'
            }
          }
        },
        
        register: {
          method: 'POST',
          path: '/api/v1/auth/register',
          body: {
            email: 'string',
            password: 'string',
            fullName: 'string',
            phone: 'string?'
          },
          response: {
            success: true,
            data: {
              user: 'User object',
              token: 'JWT token'
            }
          }
        },
        
        refresh: {
          method: 'POST',
          path: '/api/v1/auth/refresh',
          body: {
            refreshToken: 'string'
          },
          response: {
            success: true,
            data: {
              token: 'New JWT token',
              expiresAt: 'ISO timestamp'
            }
          }
        },
        
        logout: {
          method: 'POST',
          path: '/api/v1/auth/logout',
          headers: {
            Authorization: 'Bearer {token}'
          },
          response: {
            success: true,
            message: 'Logged out successfully'
          }
        }
      },
      
      // Project endpoints
      projects: {
        list: {
          method: 'GET',
          path: '/api/v1/projects',
          query: {
            page: 'number?',
            limit: 'number?',
            status: 'draft|configured|finalized?',
            sort: 'created_at|updated_at|name?',
            order: 'asc|desc?'
          },
          response: {
            success: true,
            data: {
              projects: 'Project[]',
              pagination: {
                page: 'number',
                limit: 'number',
                total: 'number',
                totalPages: 'number'
              }
            }
          }
        },
        
        create: {
          method: 'POST',
          path: '/api/v1/projects',
          body: {
            name: 'string',
            description: 'string?',
            locationId: 'string',
            plotDetails: {
              length: 'number',
              width: 'number',
              area: 'number'
            }
          },
          response: {
            success: true,
            data: {
              project: 'Project object'
            }
          }
        },
        
        get: {
          method: 'GET',
          path: '/api/v1/projects/{projectId}',
          response: {
            success: true,
            data: {
              project: 'Full project object',
              calculations: 'Latest calculation',
              collaborators: 'Collaborator[]'
            }
          }
        },
        
        update: {
          method: 'PATCH',
          path: '/api/v1/projects/{projectId}',
          body: 'Partial<Project>',
          response: {
            success: true,
            data: {
              project: 'Updated project'
            }
          }
        },
        
        delete: {
          method: 'DELETE',
          path: '/api/v1/projects/{projectId}',
          response: {
            success: true,
            message: 'Project deleted successfully'
          }
        }
      },
      
      // Calculation endpoints
      calculations: {
        quick: {
          method: 'POST',
          path: '/api/v1/calculations/quick',
          body: {
            locationId: 'string',
            plotArea: 'number',
            floors: 'number',
            qualityTier: 'smart|premium|luxury?'
          },
          response: {
            success: true,
            data: {
              estimate: {
                min: 'number',
                max: 'number',
                likely: 'number'
              },
              breakdown: {
                structure: 'number',
                finishes: 'number',
                mep: 'number',
                others: 'number'
              },
              assumptions: 'string[]'
            }
          }
        },
        
        detailed: {
          method: 'POST',
          path: '/api/v1/calculations/detailed',
          body: {
            projectId: 'string'
          },
          response: {
            success: true,
            data: {
              calculation: 'FullCalculation',
              calculationId: 'string',
              version: 'number'
            }
          }
        },
        
        delta: {
          method: 'POST',
          path: '/api/v1/calculations/delta',
          body: {
            projectId: 'string',
            changes: {
              materialSelections: 'Record<string, string>?',
              layoutConfig: 'Partial<LayoutConfig>?'
            }
          },
          response: {
            success: true,
            data: {
              previousCost: 'number',
              newCost: 'number',
              difference: 'number',
              affectedCategories: 'string[]'
            }
          }
        }
      },
      
      // Material endpoints
      materials: {
        search: {
          method: 'GET',
          path: '/api/v1/materials/search',
          query: {
            q: 'string',
            category: 'string?',
            brand: 'string?',
            qualityTier: 'smart|premium|luxury?',
            minPrice: 'number?',
            maxPrice: 'number?',
            features: 'string[]?',
            page: 'number?',
            limit: 'number?'
          },
          response: {
            success: true,
            data: {
              materials: 'MaterialWithPrice[]',
              facets: {
                categories: 'CountFacet[]',
                brands: 'CountFacet[]',
                priceRanges: 'RangeFacet[]'
              },
              pagination: 'PaginationMeta'
            }
          }
        },
        
        get: {
          method: 'GET',
          path: '/api/v1/materials/{materialId}',
          query: {
            locationId: 'string'
          },
          response: {
            success: true,
            data: {
              material: 'Material',
              price: 'MaterialPrice',
              alternatives: 'Material[]',
              vendors: 'VendorPrice[]'
            }
          }
        },
        
        recommendations: {
          method: 'GET',
          path: '/api/v1/materials/recommendations',
          query: {
            category: 'string',
            qualityTier: 'string',
            budget: 'number?',
            projectId: 'string?'
          },
          response: {
            success: true,
            data: {
              recommendations: 'MaterialRecommendation[]',
              basedOn: 'string[]'
            }
          }
        },
        
        compare: {
          method: 'POST',
          path: '/api/v1/materials/compare',
          body: {
            materialIds: 'string[]',
            locationId: 'string'
          },
          response: {
            success: true,
            data: {
              comparison: {
                materials: 'Material[]',
                prices: 'MaterialPrice[]',
                features: 'FeatureComparison',
                pros: 'Record<string, string[]>',
                cons: 'Record<string, string[]>'
              }
            }
          }
        }
      },
      
      // Location endpoints
      locations: {
        list: {
          method: 'GET',
          path: '/api/v1/locations',
          query: {
            state: 'string?',
            search: 'string?'
          },
          response: {
            success: true,
            data: {
              locations: 'Location[]'
            }
          }
        },
        
        get: {
          method: 'GET',
          path: '/api/v1/locations/{locationId}',
          response: {
            success: true,
            data: {
              location: 'Location',
              laborRates: 'CurrentLaborRates',
              statutoryCharges: 'StatutoryCharge[]',
              seasonalFactors: 'SeasonalFactors'
            }
          }
        }
      }
    };

#### 11.3 Complex Operation Endpoints

typescript

    // Complex operations that don't fit REST pattern
    const complexOperations = {
      // Report generation
      generateReport: {
        method: 'POST',
        path: '/api/v1/projects/{projectId}/generate-report',
        body: {
          format: 'detailed|summary|bank',
          sections: 'string[]',
          personalization: {
            name: 'string?',
            phone: 'string?',
            notes: 'string?'
          }
        },
        response: {
          success: true,
          data: {
            jobId: 'string',
            estimatedTime: 'number',
            status: 'queued'
          }
        },
        
        // Check status
        checkStatus: {
          method: 'GET',
          path: '/api/v1/jobs/{jobId}',
          response: {
            success: true,
            data: {
              status: 'queued|processing|completed|failed',
              progress: 'number?',
              result: {
                reportUrl: 'string',
                expiresAt: 'ISO timestamp'
              }
            }
          }
        }
      },
      
      // Bulk operations
      bulkPriceUpdate: {
        method: 'POST',
        path: '/api/v1/admin/materials/bulk-price-update',
        headers: {
          'X-Admin-Key': 'Admin API key'
        },
        body: {
          updates: [{
            materialId: 'string',
            locationId: 'string',
            newPrice: 'number',
            effectiveFrom: 'ISO date'
          }]
        },
        response: {
          success: true,
          data: {
            updated: 'number',
            failed: 'number',
            errors: 'Error[]'
          }
        }
      },
      
      // AI operations
      parseDocument: {
        method: 'POST',
        path: '/api/v1/ai/parse-document',
        headers: {
          'Content-Type': 'multipart/form-data'
        },
        body: 'FormData with file',
        response: {
          success: true,
          data: {
            documentId: 'string',
            type: 'catalog|quotation|plan',
            confidence: 'number',
            extractedData: 'Record<string, any>',
            requiresReview: 'boolean'
          }
        }
      }
    };

### 12\. GraphQL Schema (Future)

#### 12.1 GraphQL Schema Design

graphql

    # GraphQL schema for future implementation
    scalar DateTime
    scalar JSON
    
    type Query {
      # User queries
      me: User
      user(id: ID!): User
      
      # Project queries
      projects(
        filter: ProjectFilter
        sort: ProjectSort
        pagination: PaginationInput
      ): ProjectConnection!
      
      project(id: ID!): Project
      
      # Material queries
      materials(
        search: String
        filter: MaterialFilter
        sort: MaterialSort
        pagination: PaginationInput
      ): MaterialConnection!
      
      material(id: ID!): Material
      
      # Location queries
      locations(filter: LocationFilter): [Location!]!
      location(id: ID!): Location
      
      # Calculation queries
      quickEstimate(input: QuickEstimateInput!): QuickEstimate!
      
      # Analytics queries
      projectAnalytics(projectId: ID!): ProjectAnalytics
      marketTrends(locationId: ID!, category: String): MarketTrends
    }
    
    type Mutation {
      # Auth mutations
      login(email: String!, password: String!): AuthPayload!
      register(input: RegisterInput!): AuthPayload!
      logout: Boolean!
      
      # Project mutations
      createProject(input: CreateProjectInput!): Project!
      updateProject(id: ID!, input: UpdateProjectInput!): Project!
      deleteProject(id: ID!): Boolean!
      
      # Calculation mutations
      calculateProject(projectId: ID!): Calculation!
      
      # Material selection
      selectMaterial(
        projectId: ID!
        category: String!
        materialId: ID!
      ): Project!
      
      # Report generation
      generateReport(
        projectId: ID!
        format: ReportFormat!
      ): ReportJob!
    }
    
    type Subscription {
      # Real-time price updates
      priceUpdates(
        locationId: ID!
        categories: [String!]
      ): PriceUpdate!
      
      # Project collaboration
      projectUpdates(projectId: ID!): ProjectUpdate!
      
      # Job status
      jobStatus(jobId: ID!): JobStatus!
    }
    
    # Core types
    type User {
      id: ID!
      email: String!
      fullName: String
      phone: String
      avatar: String
      preferences: JSON
      projects: ProjectConnection!
      createdAt: DateTime!
    }
    
    type Project {
      id: ID!
      name: String!
      description: String
      location: Location!
      plotDetails: PlotDetails!
      structure: StructureConfig!
      layout: LayoutConfig!
      materials: [MaterialSelection!]!
      calculations: [Calculation!]!
      status: ProjectStatus!
      owner: User!
      collaborators: [ProjectCollaborator!]!
      createdAt: DateTime!
      updatedAt: DateTime!
    }
    
    type Material {
      id: ID!
      name: String!
      displayName: String!
      category: Category!
      brand: Brand
      specifications: JSON!
      images: [String!]!
      qualityTier: QualityTier!
      price(locationId: ID!): MaterialPrice
      vendors(locationId: ID!): [VendorPrice!]!
    }
    
    type Calculation {
      id: ID!
      version: Int!
      totalCost: Float!
      costPerSqft: Float!
      breakdown: CostBreakdown!
      timeline: ConstructionTimeline!
      confidence: Float!
      createdAt: DateTime!
    }
    
    # Enums
    enum ProjectStatus {
      DRAFT
      CONFIGURED
      FINALIZED
      ARCHIVED
    }
    
    enum QualityTier {
      SMART
      PREMIUM
      LUXURY
    }
    
    enum ReportFormat {
      DETAILED
      SUMMARY
      BANK_LOAN
    }
    
    # Input types
    input CreateProjectInput {
      name: String!
      description: String
      locationId: ID!
      plotDetails: PlotDetailsInput!
    }
    
    input MaterialFilter {
      categories: [ID!]
      brands: [ID!]
      qualityTiers: [QualityTier!]
      priceRange: PriceRangeInput
      features: [String!]
    }
    
    # Connections for pagination
    type ProjectConnection {
      edges: [ProjectEdge!]!
      pageInfo: PageInfo!
      totalCount: Int!
    }
    
    type ProjectEdge {
      node: Project!
      cursor: String!
    }
    
    type PageInfo {
      hasNextPage: Boolean!
      hasPreviousPage: Boolean!
      startCursor: String
      endCursor: String
    }

### 13\. WebSocket Communications

#### 13.1 WebSocket Architecture

typescript

    // WebSocket implementation using Supabase Realtime
    const websocketArchitecture = {
      // Connection management
      connection: {
        url: 'wss://api.clarityengine.in/realtime/v1',
        
        authentication: `
          const client = createClient(supabaseUrl, supabaseAnonKey, {
            realtime: {
              params: {
                eventsPerSecond: 10
              }
            }
          });
          
          // Auth is handled via JWT in connection params
          client.realtime.setAuth(token);
        `,
        
        reconnection: {
          strategy: 'Exponential backoff',
          maxRetries: 5,
          maxDelay: 30000
        }
      },
      
      // Channel types
      channels: {
        // Price updates channel
        priceUpdates: {
          name: 'price-updates',
          
          subscription: `
            const priceChannel = client
              .channel('price-updates')
              .on(
                'postgres_changes',
                {
                  event: '*',
                  schema: 'public',
                  table: 'material_prices',
                  filter: \`location_id=eq.\${locationId}\`
                },
                (payload) => {
                  handlePriceUpdate(payload);
                }
              )
              .subscribe();
          `,
          
          events: {
            INSERT: 'New price added',
            UPDATE: 'Price updated',
            DELETE: 'Price removed'
          }
        },
        
        // Project collaboration
        projectCollaboration: {
          name: 'project-{projectId}',
          
          subscription: `
            const projectChannel = client
              .channel(\`project-\${projectId}\`)
              .on('presence', { event: 'sync' }, () => {
                const state = projectChannel.presenceState();
                updateCollaboratorList(state);
              })
              .on('broadcast', { event: 'cursor' }, (payload) => {
                updateCollaboratorCursor(payload);
              })
              .on('broadcast', { event: 'selection' }, (payload) => {
                updateSelection(payload);
              })
              .subscribe(async (status) => {
                if (status === 'SUBSCRIBED') {
                  await projectChannel.track({
                    user_id: userId,
                    user_name: userName,
                    online_at: new Date().toISOString()
                  });
                }
              });
          `,
          
          broadcasts: {
            cursor: 'Mouse position for collaboration',
            selection: 'Material selection changes',
            typing: 'User typing indicators'
          }
        },
        
        // System notifications
        notifications: {
          name: 'user-{userId}-notifications',
          
          subscription: `
            const notificationChannel = client
              .channel(\`user-\${userId}-notifications\`)
              .on('broadcast', { event: 'notification' }, (payload) => {
                showNotification(payload);
              })
              .subscribe();
          `,
          
          types: {
            reportReady: 'Your report is ready',
            priceAlert: 'Price dropped for selected material',
            collaboration: 'New collaborator joined',
            system: 'System maintenance scheduled'
          }
        }
      },
      
      // Message format
      messageFormat: {
        structure: {
          id: 'UUID',
          channel: 'string',
          event: 'string',
          payload: 'any',
          metadata: {
            timestamp: 'ISO 8601',
            userId: 'UUID',
            version: 'string'
          }
        },
        
        example: {
          id: '123e4567-e89b-12d3-a456-************',
          channel: 'price-updates',
          event: 'PRICE_CHANGED',
          payload: {
            materialId: '456',
            oldPrice: 50,
            newPrice: 45,
            locationId: 'delhi'
          },
          metadata: {
            timestamp: '2024-01-15T10:30:00Z',
            userId: '789',
            version: '1.0'
          }
        }
      }
    };

#### 13.2 Real-time Features Implementation

typescript

    // Real-time feature implementations
    const realtimeFeatures = {
      // Live collaboration
      liveCollaboration: {
        features: [
          'Presence (who\'s online)',
          'Live cursors',
          'Selection sync',
          'Typing indicators'
        ],
        
        implementation: `
          class CollaborationManager {
            private channel: RealtimeChannel;
            private presence: Map<string, PresenceState> = new Map();
            
            async initialize(projectId: string) {
              this.channel = supabase
                .channel(\`project-\${projectId}\`)
                .on('presence', { event: 'sync' }, () => {
                  this.updatePresence();
                })
                .on('presence', { event: 'join' }, ({ key, newPresences }) => {
                  this.handleUserJoin(key, newPresences);
                })
                .on('presence', { event: 'leave' }, ({ key, leftPresences }) => {
                  this.handleUserLeave(key, leftPresences);
                });
                
              await this.channel.subscribe();
              
              // Track self
              await this.channel.track({
                userId: currentUser.id,
                userName: currentUser.name,
                color: generateUserColor(currentUser.id)
              });
            }
            
            broadcastCursor(position: { x: number; y: number }) {
              this.channel.send({
                type: 'broadcast',
                event: 'cursor',
                payload: {
                  userId: currentUser.id,
                  position
                }
              });
            }
            
            broadcastSelection(selection: MaterialSelection) {
              this.channel.send({
                type: 'broadcast',
                event: 'selection',
                payload: {
                  userId: currentUser.id,
                  category: selection.category,
                  materialId: selection.materialId,
                  timestamp: new Date().toISOString()
                }
              });
            }
          }
        `
      },
      
      // Live price monitoring
      priceMonitoring: {
        features: [
          'Real-time price updates',
          'Price drop alerts',
          'Availability changes'
        ],
        
        implementation: `
          class PriceMonitor {
            private subscriptions: Map<string, RealtimeChannel> = new Map();
            
            watchMaterial(materialId: string, locationId: string) {
              const channelName = \`price-\${materialId}-\${locationId}\`;
              
              if (this.subscriptions.has(channelName)) {
                return;
              }
              
              const channel = supabase
                .channel(channelName)
                .on(
                  'postgres_changes',
                  {
                    event: 'UPDATE',
                    schema: 'public',
                    table: 'material_prices',
                    filter: \`material_id=eq.\${materialId} AND location_id=eq.\${locationId}\`
                  },
                  (payload) => {
                    this.handlePriceChange(payload);
                  }
                )
                .subscribe();
                
              this.subscriptions.set(channelName, channel);
            }
            
            private handlePriceChange(payload: any) {
              const { old: oldPrice, new: newPrice } = payload;
              
              // Update UI
              updatePriceDisplay(newPrice);
              
              // Check for significant drop
              if (newPrice.base_price < oldPrice.base_price * 0.95) {
                showPriceDropAlert({
                  material: newPrice.material_id,
                  oldPrice: oldPrice.base_price,
                  newPrice: newPrice.base_price,
                  savings: oldPrice.base_price - newPrice.base_price
                });
              }
            }
            
            unwatchAll() {
              this.subscriptions.forEach(channel => {
                supabase.removeChannel(channel);
              });
              this.subscriptions.clear();
            }
          }
        `
      }
    };

### 14\. API Security & Rate Limiting

#### 14.1 Security Implementation

typescript

    // API Security layers
    const apiSecurity = {
      // Authentication middleware
      authentication: {
        implementation: `
          import { verify } from 'jsonwebtoken';
          
          export async function authenticateRequest(
            req: NextApiRequest,
            res: NextApiResponse
          ): Promise<User | null> {
            // 1. Extract token
            const authHeader = req.headers.authorization;
            if (!authHeader?.startsWith('Bearer ')) {
              res.status(401).json({ error: 'Missing authorization header' });
              return null;
            }
            
            const token = authHeader.substring(7);
            
            try {
              // 2. Verify JWT
              const payload = verify(token, process.env.JWT_SECRET) as JWTPayload;
              
              // 3. Check token expiry
              if (payload.exp && payload.exp < Date.now() / 1000) {
                res.status(401).json({ error: 'Token expired' });
                return null;
              }
              
              // 4. Get user from database
              const user = await getUserById(payload.sub);
              if (!user) {
                res.status(401).json({ error: 'User not found' });
                return null;
              }
              
              // 5. Check if user is active
              if (user.status === 'suspended') {
                res.status(403).json({ error: 'Account suspended' });
                return null;
              }
              
              return user;
            } catch (error) {
              res.status(401).json({ error: 'Invalid token' });
              return null;
            }
          }
        `,
        
        // Role-based access
        rbac: `
          export function requireRole(roles: string[]) {
            return async (req: NextApiRequest, res: NextApiResponse, next: () => void) => {
              const user = await authenticateRequest(req, res);
              if (!user) return;
              
              const userRoles = await getUserRoles(user.id);
              const hasRequiredRole = roles.some(role => 
                userRoles.includes(role)
              );
              
              if (!hasRequiredRole) {
                return res.status(403).json({ 
                  error: 'Insufficient permissions' 
                });
              }
              
              req.user = user;
              next();
            };
          }
        `
      },
      
      // Input validation
      validation: {
        implementation: `
          import { z } from 'zod';
          
          // Validation middleware
          export function validateBody(schema: z.ZodSchema) {
            return async (req: NextApiRequest, res: NextApiResponse, next: () => void) => {
              try {
                const validated = await schema.parseAsync(req.body);
                req.body = validated;
                next();
              } catch (error) {
                if (error instanceof z.ZodError) {
                  return res.status(400).json({
                    error: 'Validation failed',
                    details: error.errors
                  });
                }
                return res.status(400).json({
                  error: 'Invalid request body'
                });
              }
            };
          }
          
          // Example schema
          const CreateProjectSchema = z.object({
            name: z.string().min(3).max(100),
            description: z.string().max(500).optional(),
            locationId: z.string().uuid(),
            plotDetails: z.object({
              length: z.number().min(10).max(1000),
              width: z.number().min(10).max(1000),
              area: z.number().min(100).max(100000)
            })
          });
        `,
        
        // SQL injection prevention
        sqlInjectionPrevention: `
          // Always use parameterized queries
          export async function getMaterialsByCategory(categoryId: string) {
            // ✅ Safe - parameterized
            return await db.materials.findMany({
              where: { categoryId }
            });
            
            // ❌ Unsafe - string concatenation
            // const query = \`SELECT * FROM materials WHERE category_id = '\${categoryId}'\`;
            // return await db.$queryRawUnsafe(query);
          }
        `
      },
      
      // CORS configuration
      cors: {
        config: `
          import Cors from 'cors';
          
          const cors = Cors({
            origin: (origin, callback) => {
              const allowedOrigins = [
                'https://clarityengine.in',
                'https://www.clarityengine.in',
                'https://app.clarityengine.in'
              ];
              
              // Allow requests with no origin (mobile apps)
              if (!origin) return callback(null, true);
              
              if (allowedOrigins.includes(origin)) {
                callback(null, true);
              } else {
                callback(new Error('Not allowed by CORS'));
              }
            },
            credentials: true,
            methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
            allowedHeaders: ['Content-Type', 'Authorization', 'X-Request-ID']
          });
          
          // Apply to API routes
          export function applyCors(req: NextApiRequest, res: NextApiResponse) {
            return new Promise((resolve, reject) => {
              cors(req, res, (result) => {
                if (result instanceof Error) {
                  return reject(result);
                }
                return resolve(result);
              });
            });
          }
        `
      }
    };

#### 14.2 Rate Limiting Implementation

typescript

    // Rate limiting strategies
    const rateLimiting = {
      // Token bucket implementation
      tokenBucket: {
        implementation: `
          import { Ratelimit } from '@upstash/ratelimit';
          import { Redis } from '@upstash/redis';
          
          // Different rate limits for different operations
          const rateLimiters = {
            // General API calls
            general: new Ratelimit({
              redis: Redis.fromEnv(),
              limiter: Ratelimit.slidingWindow(100, '1 m'),
              analytics: true,
              prefix: 'rl:general'
            }),
            
            // Calculation endpoints (expensive)
            calculation: new Ratelimit({
              redis: Redis.fromEnv(),
              limiter: Ratelimit.tokenBucket(20, '1 h', 5),
              analytics: true,
              prefix: 'rl:calc'
            }),
            
            // Auth endpoints (prevent brute force)
            auth: new Ratelimit({
              redis: Redis.fromEnv(),
              limiter: Ratelimit.fixedWindow(5, '15 m'),
              analytics: true,
              prefix: 'rl:auth'
            }),
            
            // File uploads
            upload: new Ratelimit({
              redis: Redis.fromEnv(),
              limiter: Ratelimit.slidingWindow(10, '1 h'),
              analytics: true,
              prefix: 'rl:upload'
            })
          };
          
          export async function rateLimit(
            type: keyof typeof rateLimiters,
            identifier: string
          ) {
            const limiter = rateLimiters[type];
            const result = await limiter.limit(identifier);
            
            return {
              success: result.success,
              limit: result.limit,
              remaining: result.remaining,
              reset: result.reset
            };
          }
        `,
        
        // Middleware implementation
        middleware: `
          export function withRateLimit(type: RateLimitType) {
            return async (req: NextApiRequest, res: NextApiResponse, next: () => void) => {
              // Determine identifier
              const identifier = req.user?.id || 
                req.headers['x-forwarded-for'] || 
                req.socket.remoteAddress ||
                'anonymous';
                
              const { success, limit, remaining, reset } = await rateLimit(
                type,
                identifier
              );
              
              // Set headers
              res.setHeader('X-RateLimit-Limit', limit);
              res.setHeader('X-RateLimit-Remaining', remaining);
              res.setHeader('X-RateLimit-Reset', new Date(reset).toISOString());
              
              if (!success) {
                return res.status(429).json({
                  error: 'Too many requests',
                  retryAfter: Math.floor((reset - Date.now()) / 1000)
                });
              }
              
              next();
            };
          }
        `
      },
      
      // Cost-based rate limiting
      costBasedLimiting: {
        concept: 'Assign costs to operations, limit by total cost',
        
        implementation: `
          const operationCosts = {
            'GET /api/materials': 1,
            'POST /api/calculations/quick': 5,
            'POST /api/calculations/detailed': 20,
            'POST /api/reports/generate': 50,
            'POST /api/ai/parse': 100
          };
          
          class CostBasedRateLimiter {
            private userBudgets = new Map<string, number>();
            private readonly maxBudget = 1000;
            private readonly refillRate = 100; // per hour
            
            async checkBudget(userId: string, operation: string): Promise<boolean> {
              const cost = operationCosts[operation] || 1;
              const currentBudget = this.userBudgets.get(userId) || this.maxBudget;
              
              if (currentBudget < cost) {
                return false;
              }
              
              this.userBudgets.set(userId, currentBudget - cost);
              return true;
            }
            
            // Refill budgets periodically
            startRefillTimer() {
              setInterval(() => {
                this.userBudgets.forEach((budget, userId) => {
                  const newBudget = Math.min(
                    budget + this.refillRate,
                    this.maxBudget
                  );
                  this.userBudgets.set(userId, newBudget);
                });
              }, 3600000); // 1 hour
            }
          }
        `
      }
    };

### 15\. Error Handling & Logging

#### 15.1 Error Handling Strategy

typescript

    // Comprehensive error handling
    const errorHandling = {
      // Error types
      errorTypes: {
        // Custom error classes
        classes: `
          // Base error class
          export class AppError extends Error {
            constructor(
              public statusCode: number,
              public message: string,
              public code: string,
              public details?: any
            ) {
              super(message);
              this.name = this.constructor.name;
              Error.captureStackTrace(this, this.constructor);
            }
          }
          
          // Specific error types
          export class ValidationError extends AppError {
            constructor(message: string, details: any) {
              super(400, message, 'VALIDATION_ERROR', details);
            }
          }
          
          export class AuthenticationError extends AppError {
            constructor(message: string = 'Authentication required') {
              super(401, message, 'AUTH_ERROR');
            }
          }
          
          export class AuthorizationError extends AppError {
            constructor(message: string = 'Insufficient permissions') {
              super(403, message, 'AUTHZ_ERROR');
            }
          }
          
          export class NotFoundError extends AppError {
            constructor(resource: string) {
              super(404, \`\${resource} not found\`, 'NOT_FOUND');
            }
          }
          
          export class ConflictError extends AppError {
            constructor(message: string) {
              super(409, message, 'CONFLICT');
            }
          }
          
          export class RateLimitError extends AppError {
            constructor(retryAfter: number) {
              super(429, 'Too many requests', 'RATE_LIMIT', { retryAfter });
            }
          }
          
          export class ExternalServiceError extends AppError {
            constructor(service: string, originalError: any) {
              super(502, \`\${service} service error\`, 'EXTERNAL_SERVICE_ERROR', {
                service,
                originalError: originalError.message
              });
            }
          }
        `
      },
      
      // Global error handler
      globalHandler: {
        implementation: `
          export function errorHandler(
            err: Error,
            req: NextApiRequest,
            res: NextApiResponse
          ) {
            // Log error
            logger.error({
              error: err,
              request: {
                method: req.method,
                url: req.url,
                headers: req.headers,
                body: req.body,
                query: req.query
              },
              user: req.user?.id
            });
            
            // Send to error tracking
            if (process.env.NODE_ENV === 'production') {
              Sentry.captureException(err, {
                contexts: {
                  request: {
                    method: req.method,
                    url: req.url,
                    headers: req.headers,
                    data: req.body
                  },
                  user: {
                    id: req.user?.id,
                    email: req.user?.email
                  }
                }
              });
            }
            
            // Handle known errors
            if (err instanceof AppError) {
              return res.status(err.statusCode).json({
                error: {
                  message: err.message,
                  code: err.code,
                  details: err.details
                }
              });
            }
            
            // Handle Prisma errors
            if (err.name === 'PrismaClientKnownRequestError') {
              if (err.code === 'P2002') {
                return res.status(409).json({
                  error: {
                    message: 'Resource already exists',
                    code: 'DUPLICATE_ENTRY'
                  }
                });
              }
              if (err.code === 'P2025') {
                return res.status(404).json({
                  error: {
                    message: 'Resource not found',
                    code: 'NOT_FOUND'
                  }
                });
              }
            }
            
            // Default error response
            const statusCode = err.statusCode || 500;
            const message = process.env.NODE_ENV === 'production'
              ? 'Internal server error'
              : err.message;
              
            res.status(statusCode).json({
              error: {
                message,
                code: 'INTERNAL_ERROR'
              }
            });
          }
        `,
        
        // Async error wrapper
        asyncWrapper: `
          export function asyncHandler(
            fn: (req: NextApiRequest, res: NextApiResponse) => Promise<void>
          ) {
            return async (req: NextApiRequest, res: NextApiResponse) => {
              try {
                await fn(req, res);
              } catch (error) {
                errorHandler(error, req, res);
              }
            };
          }
          
          // Usage
          export default asyncHandler(async (req, res) => {
            const project = await getProject(req.query.id);
            if (!project) {
              throw new NotFoundError('Project');
            }
            res.json({ data: project });
          });
        `
      }
    };

#### 15.2 Logging Architecture

typescript

    // Structured logging system
    const loggingArchitecture = {
      // Logger configuration
      logger: {
        implementation: `
          import winston from 'winston';
          import { LogtailTransport } from '@logtail/winston';
          
          // Create logger instance
          const logger = winston.createLogger({
            level: process.env.LOG_LEVEL || 'info',
            format: winston.format.combine(
              winston.format.timestamp(),
              winston.format.errors({ stack: true }),
              winston.format.json()
            ),
            defaultMeta: {
              service: 'clarity-engine-api',
              environment: process.env.NODE_ENV,
              version: process.env.APP_VERSION
            },
            transports: [
              // Console transport for development
              new winston.transports.Console({
                format: winston.format.combine(
                  winston.format.colorize(),
                  winston.format.simple()
                )
              }),
              
              // File transport for production
              new winston.transports.File({
                filename: 'logs/error.log',
                level: 'error',
                maxsize: 5242880, // 5MB
                maxFiles: 5
              }),
              
              new winston.transports.File({
                filename: 'logs/combined.log',
                maxsize: 5242880, // 5MB
                maxFiles: 5
              }),
              
              // Logtail for cloud logging
              new LogtailTransport({
                sourceToken: process.env.LOGTAIL_TOKEN
              })
            ]
          });
          
          // Add request ID to all logs
          export function addRequestId(requestId: string) {
            return logger.child({ requestId });
          }
        `,
        
        // Log levels and usage
        levels: {
          error: 'System errors, exceptions',
          warn: 'Warning conditions',
          info: 'General informational messages',
          http: 'HTTP request logs',
          debug: 'Debug-level messages'
        }
      },
      
      // Request logging
      requestLogging: {
        middleware: `
          import { v4 as uuidv4 } from 'uuid';
          
          export function requestLogger(
            req: NextApiRequest,
            res: NextApiResponse,
            next: () => void
          ) {
            const requestId = uuidv4();
            req.requestId = requestId;
            
            // Attach request ID to response
            res.setHeader('X-Request-ID', requestId);
            
            // Create child logger with request ID
            req.logger = addRequestId(requestId);
            
           // Log request
           const startTime = Date.now();
           
           req.logger.info('Incoming request', {
             method: req.method,
             url: req.url,
             ip: req.headers['x-forwarded-for'] || req.socket.remoteAddress,
             userAgent: req.headers['user-agent'],
             userId: req.user?.id
           });
           
           // Log response
           const originalSend = res.send;
           res.send = function(data) {
             const duration = Date.now() - startTime;
             
             req.logger.info('Request completed', {
               statusCode: res.statusCode,
               duration,
               contentLength: res.getHeader('content-length')
             });
             
             // Log slow requests
             if (duration > 1000) {
               req.logger.warn('Slow request detected', {
                 duration,
                 threshold: 1000
               });
             }
             
             originalSend.call(this, data);
           };
           
           next();
         }
       `,
       
       // Sensitive data filtering
       filtering: `
         // Filter sensitive data from logs
         const sensitiveFields = [
           'password',
           'token',
           'authorization',
           'cookie',
           'creditCard',
           'ssn',
           'pan',
           'aadhaar'
         ];
         
         export function filterSensitiveData(obj: any): any {
           if (typeof obj !== 'object' || obj === null) {
             return obj;
           }
           
           const filtered = Array.isArray(obj) ? [] : {};
           
           for (const key in obj) {
             if (sensitiveFields.some(field => 
               key.toLowerCase().includes(field.toLowerCase())
             )) {
               filtered[key] = '[REDACTED]';
             } else if (typeof obj[key] === 'object') {
               filtered[key] = filterSensitiveData(obj[key]);
             } else {
               filtered[key] = obj[key];
             }
           }
           
           return filtered;
         }
       `
     },
     
     // Performance logging
     performanceLogging: {
       implementation: `
         export class PerformanceLogger {
           private marks: Map<string, number> = new Map();
           
           mark(name: string) {
             this.marks.set(name, performance.now());
           }
           
           measure(name: string, startMark: string, endMark?: string) {
             const start = this.marks.get(startMark);
             if (!start) {
               throw new Error(\`Mark \${startMark} not found\`);
             }
             
             const end = endMark ? this.marks.get(endMark) : performance.now();
             if (endMark && !end) {
               throw new Error(\`Mark \${endMark} not found\`);
             }
             
             const duration = end - start;
             
             logger.debug('Performance measurement', {
               name,
               duration,
               startMark,
               endMark
             });
             
             // Log slow operations
             if (duration > 100) {
               logger.warn('Slow operation detected', {
                 name,
                 duration,
                 threshold: 100
               });
             }
             
             return duration;
           }
         }
         
         // Usage in calculation service
         export async function calculateProject(projectId: string) {
           const perf = new PerformanceLogger();
           perf.mark('start');
           
           // Fetch project data
           perf.mark('fetch-start');
           const project = await getProject(projectId);
           perf.measure('fetch-project', 'fetch-start');
           
           // Run calculations
           perf.mark('calc-start');
           const result = await runCalculations(project);
           perf.measure('calculations', 'calc-start');
           
           // Save results
           perf.mark('save-start');
           await saveCalculation(result);
           perf.measure('save-results', 'save-start');
           
           perf.measure('total', 'start');
           
           return result;
         }
       `
     }
    };

* * *

Part 4: Frontend Architecture
-----------------------------

### 16\. Application Structure

#### 16.1 Project Structure

typescript

    // Next.js 14 App Directory Structure
    const projectStructure = `
    /clarity-engine
    ├── /app                          # Next.js 14 app directory
    │   ├── /(auth)                   # Auth group
    │   │   ├── login/
    │   │   ├── register/
    │   │   └── layout.tsx
    │   ├── /(main)                   # Main app group
    │   │   ├── calculator/
    │   │   │   ├── quick/
    │   │   │   ├── detailed/
    │   │   │   └── [projectId]/
    │   │   ├── projects/
    │   │   ├── materials/
    │   │   └── layout.tsx
    │   ├── /(admin)                  # Admin group
    │   │   ├── dashboard/
    │   │   ├── materials/
    │   │   └── layout.tsx
    │   ├── api/                      # API routes
    │   │   ├── auth/
    │   │   ├── projects/
    │   │   └── calculations/
    │   ├── layout.tsx                # Root layout
    │   ├── page.tsx                  # Home page
    │   └── global-error.tsx          # Global error boundary
    │
    ├── /components                   # React components
    │   ├── /ui                       # Base UI components
    │   │   ├── button.tsx
    │   │   ├── input.tsx
    │   │   └── modal.tsx
    │   ├── /calculator               # Calculator components
    │   │   ├── plot-input.tsx
    │   │   ├── material-selector.tsx
    │   │   └── cost-breakdown.tsx
    │   ├── /layout                   # Layout components
    │   │   ├── header.tsx
    │   │   ├── sidebar.tsx
    │   │   └── footer.tsx
    │   └── /shared                   # Shared components
    │       ├── loading.tsx
    │       └── error-boundary.tsx
    │
    ├── /lib                          # Library code
    │   ├── /api                      # API client
    │   │   ├── client.ts
    │   │   └── endpoints.ts
    │   ├── /calculations             # Calculation logic
    │   │   ├── structure.ts
    │   │   └── materials.ts
    │   ├── /utils                    # Utilities
    │   │   ├── format.ts
    │   │   └── validation.ts
    │   └── /constants                # Constants
    │       ├── materials.ts
    │       └── locations.ts
    │
    ├── /hooks                        # Custom React hooks
    │   ├── use-auth.ts
    │   ├── use-calculator.ts
    │   └── use-materials.ts
    │
    ├── /stores                       # Zustand stores
    │   ├── auth-store.ts
    │   ├── calculator-store.ts
    │   └── ui-store.ts
    │
    ├── /types                        # TypeScript types
    │   ├── api.ts
    │   ├── calculator.ts
    │   └── materials.ts
    │
    ├── /styles                       # Global styles
    │   ├── globals.css
    │   └── variables.css
    │
    ├── /public                       # Static assets
    │   ├── /images
    │   ├── /fonts
    │   └── /icons
    │
    ├── /tests                        # Test files
    │   ├── /unit
    │   ├── /integration
    │   └── /e2e
    │
    └── Configuration files
        ├── next.config.js
        ├── tailwind.config.js
        ├── tsconfig.json
        ├── jest.config.js
        └── cypress.config.js
    `;

#### 16.2 Module Organization

typescript

    // Feature-based module organization
    const moduleOrganization = {
      // Calculator module
      calculator: {
        structure: `
          /features/calculator
          ├── /components
          │   ├── QuickCalculator.tsx
          │   ├── DetailedCalculator.tsx
          │   ├── PlotConfiguration.tsx
          │   ├── StructureSelection.tsx
          │   ├── MaterialCustomization.tsx
          │   └── CostSummary.tsx
          ├── /hooks
          │   ├── useCalculation.ts
          │   ├── useProjectData.ts
          │   └── useMaterialSelection.ts
          ├── /utils
          │   ├── calculations.ts
          │   ├── validations.ts
          │   └── formatters.ts
          ├── /types
          │   └── calculator.types.ts
          └── index.ts
        `,
        
        exports: `
          // features/calculator/index.ts
          export { QuickCalculator } from './components/QuickCalculator';
          export { DetailedCalculator } from './components/DetailedCalculator';
          export { useCalculation } from './hooks/useCalculation';
          export type { CalculationResult } from './types/calculator.types';
        `
      },
      
      // Materials module
      materials: {
        structure: `
          /features/materials
          ├── /components
          │   ├── MaterialSearch.tsx
          │   ├── MaterialGrid.tsx
          │   ├── MaterialCard.tsx
          │   ├── MaterialFilters.tsx
          │   └── MaterialComparison.tsx
          ├── /hooks
          │   ├── useMaterials.ts
          │   ├── useMaterialSearch.ts
          │   └── useMaterialFilters.ts
          ├── /services
          │   ├── materialService.ts
          │   └── searchService.ts
          └── /types
              └── materials.types.ts
        `
      }
    };

### 17\. State Management Design

#### 17.1 Zustand Store Architecture

typescript

    // Main store structure
    const storeArchitecture = {
      // Auth store
      authStore: `
        import { create } from 'zustand';
        import { devtools, persist } from 'zustand/middleware';
        import { immer } from 'zustand/middleware/immer';
        
        interface AuthState {
          // State
          user: User | null;
          token: string | null;
          isAuthenticated: boolean;
          isLoading: boolean;
          
          // Actions
          login: (credentials: LoginCredentials) => Promise<void>;
          logout: () => void;
          updateProfile: (updates: Partial<User>) => Promise<void>;
          refreshToken: () => Promise<void>;
        }
        
        export const useAuthStore = create<AuthState>()(
          devtools(
            persist(
              immer((set, get) => ({
                // Initial state
                user: null,
                token: null,
                isAuthenticated: false,
                isLoading: false,
                
                // Actions
                login: async (credentials) => {
                  set((state) => {
                    state.isLoading = true;
                  });
                  
                  try {
                    const response = await api.auth.login(credentials);
                    
                    set((state) => {
                      state.user = response.user;
                      state.token = response.token;
                      state.isAuthenticated = true;
                      state.isLoading = false;
                    });
                    
                    // Set auth header for future requests
                    api.setAuthToken(response.token);
                    
                    // Start token refresh timer
                    startTokenRefreshTimer(response.expiresIn);
                  } catch (error) {
                    set((state) => {
                      state.isLoading = false;
                    });
                    throw error;
                  }
                },
                
                logout: () => {
                  set((state) => {
                    state.user = null;
                    state.token = null;
                    state.isAuthenticated = false;
                  });
                  
                  // Clear auth header
                  api.clearAuthToken();
                  
                  // Clear persisted data
                  localStorage.removeItem('auth-storage');
                },
                
                updateProfile: async (updates) => {
                  const response = await api.users.updateProfile(updates);
                  
                  set((state) => {
                    state.user = { ...state.user, ...response.user };
                  });
                },
                
                refreshToken: async () => {
                  const { token } = get();
                  if (!token) return;
                  
                  try {
                    const response = await api.auth.refresh(token);
                    
                    set((state) => {
                      state.token = response.token;
                    });
                    
                    api.setAuthToken(response.token);
                  } catch (error) {
                    // Token refresh failed, logout
                    get().logout();
                  }
                }
              })),
              {
                name: 'auth-storage',
                partialize: (state) => ({
                  user: state.user,
                  token: state.token,
                  isAuthenticated: state.isAuthenticated
                })
              }
            )
          )
        );
      `,
      
      // Calculator store
      calculatorStore: `
        interface CalculatorState {
          // Project data
          projectId: string | null;
          projectData: {
            location: LocationData | null;
            plot: PlotData | null;
            structure: StructureData | null;
            layout: LayoutData | null;
            materials: MaterialSelections;
            siteConditions: SiteConditions | null;
          };
          
          // UI state
          currentStep: number;
          completedSteps: number[];
          validationErrors: Record<string, string[]>;
          
          // Calculation state
          isCalculating: boolean;
          calculationResult: CalculationResult | null;
          calculationError: string | null;
          
          // Actions
          setProjectData: <K extends keyof ProjectData>(
            section: K,
            data: ProjectData[K]
          ) => void;
          selectMaterial: (category: string, materialId: string) => void;
          calculateProject: () => Promise<void>;
          saveProject: () => Promise<void>;
          loadProject: (projectId: string) => Promise<void>;
          resetCalculator: () => void;
          
          // Navigation
          goToStep: (step: number) => void;
          nextStep: () => void;
          previousStep: () => void;
        }
        
        export const useCalculatorStore = create<CalculatorState>()(
          devtools(
            immer((set, get) => ({
              // Initial state
              projectId: null,
              projectData: {
                location: null,
                plot: null,
                structure: null,
                layout: null,
                materials: {},
                siteConditions: null
              },
              currentStep: 0,
              completedSteps: [],
              validationErrors: {},
              isCalculating: false,
              calculationResult: null,
              calculationError: null,
              
              // Actions
              setProjectData: (section, data) => {
                set((state) => {
                  state.projectData[section] = data;
                  
                  // Mark step as completed
                  const stepIndex = getStepIndex(section);
                  if (!state.completedSteps.includes(stepIndex)) {
                    state.completedSteps.push(stepIndex);
                  }
                  
                  // Clear validation errors for this section
                  delete state.validationErrors[section];
                  
                  // Clear calculation result as data changed
                  state.calculationResult = null;
                });
              },
              
              selectMaterial: (category, materialId) => {
                set((state) => {
                  state.projectData.materials[category] = materialId;
                  state.calculationResult = null;
                });
              },
              
              calculateProject: async () => {
                set((state) => {
                  state.isCalculating = true;
                  state.calculationError = null;
                });
                
                try {
                  const { projectData } = get();
                  
                  // Validate all required data
                  const validation = validateProjectData(projectData);
                  if (!validation.isValid) {
                    set((state) => {
                      state.validationErrors = validation.errors;
                      state.isCalculating = false;
                    });
                    return;
                  }
                  
                  // Perform calculation
                  const result = await api.calculations.detailed({
                    projectId: get().projectId,
                    projectData
                  });
                  
                  set((state) => {
                    state.calculationResult = result;
                    state.isCalculating = false;
                  });
                } catch (error) {
                  set((state) => {
                    state.calculationError = error.message;
                    state.isCalculating = false;
                  });
                }
              },
              
              // ... other actions
            }))
          )
        );
      `
    };

#### 17.2 React Query Integration

typescript

    // Server state management with React Query
    const reactQuerySetup = {
      // Query client configuration
      queryClient: `
        import { QueryClient } from '@tanstack/react-query';
        
        export const queryClient = new QueryClient({
          defaultOptions: {
            queries: {
              // Stale time: how long data is considered fresh
              staleTime: 5 * 60 * 1000, // 5 minutes
              
              // Cache time: how long to keep data in cache
              cacheTime: 10 * 60 * 1000, // 10 minutes
              
              // Retry configuration
              retry: (failureCount, error) => {
                // Don't retry on 4xx errors
                if (error.status >= 400 && error.status < 500) {
                  return false;
                }
                // Retry up to 3 times for other errors
                return failureCount < 3;
              },
              
              // Refetch on window focus
              refetchOnWindowFocus: false,
              
              // Background refetch interval
              refetchInterval: false
            },
            
            mutations: {
              // Retry configuration for mutations
              retry: false
            }
          }
        });
      `,
      
      // Custom hooks
      hooks: {
        // Materials query
        useMaterials: `
          export function useMaterials(filters: MaterialFilters) {
            return useQuery({
              queryKey: ['materials', filters],
              queryFn: () => api.materials.search(filters),
              
              // Keep previous data while fetching
              keepPreviousData: true,
              
              // Custom stale time for materials (longer)
              staleTime: 30 * 60 * 1000, // 30 minutes
              
              // Transform data
              select: (data) => ({
                materials: data.materials,
                totalCount: data.pagination.total,
                facets: data.facets
              })
            });
          }
        `,
        
        // Project mutations
        useProjectMutations: `
          export function useCreateProject() {
            const queryClient = useQueryClient();
            
            return useMutation({
              mutationFn: (data: CreateProjectInput) => 
                api.projects.create(data),
                
              onSuccess: (newProject) => {
                // Invalidate projects list
                queryClient.invalidateQueries({ queryKey: ['projects'] });
                
                // Add new project to cache
                queryClient.setQueryData(
                  ['project', newProject.id],
                  newProject
                );
                
                // Show success notification
                toast.success('Project created successfully');
              },
              
              onError: (error) => {
                toast.error(error.message || 'Failed to create project');
              }
            });
          }
          
          export function useUpdateProject(projectId: string) {
            const queryClient = useQueryClient();
            
            return useMutation({
              mutationFn: (updates: Partial<Project>) =>
                api.projects.update(projectId, updates),
                
              // Optimistic update
              onMutate: async (updates) => {
                // Cancel outgoing refetches
                await queryClient.cancelQueries({
                  queryKey: ['project', projectId]
                });
                
                // Snapshot previous value
                const previousProject = queryClient.getQueryData([
                  'project',
                  projectId
                ]);
                
                // Optimistically update
                queryClient.setQueryData(['project', projectId], (old) => ({
                  ...old,
                  ...updates
                }));
                
                // Return context with snapshot
                return { previousProject };
              },
              
              onError: (err, updates, context) => {
                // Rollback on error
                queryClient.setQueryData(
                  ['project', projectId],
                  context.previousProject
                );
              },
              
              onSettled: () => {
                // Refetch after error or success
                queryClient.invalidateQueries({
                  queryKey: ['project', projectId]
                });
              }
            });
          }
        `
      },
      
      // Infinite queries
      infiniteQueries: `
        export function useInfiniteProjects() {
          return useInfiniteQuery({
            queryKey: ['projects', 'infinite'],
            queryFn: ({ pageParam = 1 }) =>
              api.projects.list({ page: pageParam, limit: 20 }),
              
            getNextPageParam: (lastPage, pages) => {
              const { pagination } = lastPage;
              return pagination.page < pagination.totalPages
                ? pagination.page + 1
                : undefined;
            },
            
            select: (data) => ({
              projects: data.pages.flatMap(page => page.projects),
              totalCount: data.pages[0]?.pagination.total || 0
            })
          });
        }
      `
    };

### 18\. Component Architecture

#### 18.1 Component Design System

typescript

    // Component architecture patterns
    const componentArchitecture = {
      // Base component template
      componentTemplate: `
        import { forwardRef, memo } from 'react';
        import { cn } from '@/lib/utils';
        import { cva, type VariantProps } from 'class-variance-authority';
        
        // Define component variants
        const buttonVariants = cva(
          // Base styles
          'inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',
          {
            variants: {
              variant: {
                primary: 'bg-primary text-primary-foreground hover:bg-primary/90',
                secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
                outline: 'border border-input bg-background hover:bg-accent',
                ghost: 'hover:bg-accent hover:text-accent-foreground',
                destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90'
              },
              size: {
                sm: 'h-9 px-3',
                md: 'h-10 px-4 py-2',
                lg: 'h-11 px-8',
                icon: 'h-10 w-10'
              }
            },
            defaultVariants: {
              variant: 'primary',
              size: 'md'
            }
          }
        );
        
        // Component props interface
        export interface ButtonProps
          extends React.ButtonHTMLAttributes<HTMLButtonElement>,
            VariantProps<typeof buttonVariants> {
          loading?: boolean;
          leftIcon?: React.ReactNode;
          rightIcon?: React.ReactNode;
        }
        
        // Component implementation
        export const Button = forwardRef<HTMLButtonElement, ButtonProps>(
          (
            {
              className,
              variant,
              size,
              loading = false,
              leftIcon,
              rightIcon,
              disabled,
              children,
              ...props
            },
            ref
          ) => {
            return (
              <button
                ref={ref}
                className={cn(buttonVariants({ variant, size, className }))}
                disabled={disabled || loading}
                {...props}
              >
                {loading ? (
                  <Spinner className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  leftIcon && <span className="mr-2">{leftIcon}</span>
                )}
                {children}
                {rightIcon && <span className="ml-2">{rightIcon}</span>}
              </button>
            );
          }
        );
        
        Button.displayName = 'Button';
        
        // Memoize for performance
        export default memo(Button);
      `,
      
      // Compound components
      compoundComponents: `
        // Card compound component
        const Card = ({ children, className, ...props }) => {
          return (
            <div
              className={cn(
                'rounded-lg border bg-card text-card-foreground shadow-sm',
                className
              )}
              {...props}
            >
              {children}
            </div>
          );
        };
        
        Card.Header = ({ children, className, ...props }) => {
          return (
            <div
              className={cn('flex flex-col space-y-1.5 p-6', className)}
              {...props}
            >
              {children}
            </div>
          );
        };
        
        Card.Title = ({ children, className, ...props }) => {
          return (
            <h3
              className={cn(
                'text-2xl font-semibold leading-none tracking-tight',
                className
              )}
              {...props}
            >
              {children}
            </h3>
          );
        };
        
        Card.Content = ({ children, className, ...props }) => {
          return (
            <div className={cn('p-6 pt-0', className)} {...props}>
              {children}
            </div>
          );
        };
        
        Card.Footer = ({ children, className, ...props }) => {
          return (
            <div
              className={cn('flex items-center p-6 pt-0', className)}
              {...props}
            >
              {children}
            </div>
          );
        };
        
        // Usage
        <Card>
          <Card.Header>
            <Card.Title>Project Summary</Card.Title>
          </Card.Header>
          <Card.Content>
            <p>Total cost: ₹1,86,45,000</p>
          </Card.Content>
          <Card.Footer>
            <Button>Download Report</Button>
          </Card.Footer>
        </Card>
      `
    };

#### 18.2 Complex Component Examples

typescript

    // Material selector component
    const materialSelector = `
      interface MaterialSelectorProps {
        category: string;
        selectedId?: string;
        qualityTier: QualityTier;
        onSelect: (materialId: string) => void;
      }
      
      export function MaterialSelector({
        category,
        selectedId,
        qualityTier,
        onSelect
      }: MaterialSelectorProps) {
        // State
        const [search, setSearch] = useState('');
        const [filters, setFilters] = useState<MaterialFilters>({
          category,
          qualityTier,
          search: ''
        });
        
        // Debounced search
        const debouncedSearch = useDebounce(search, 300);
        
        // Fetch materials
        const {
          data,
          isLoading,
          error,
          fetchNextPage,
          hasNextPage,
          isFetchingNextPage
        } = useInfiniteMaterials({
          ...filters,
          search: debouncedSearch
        });
        
        // Intersection observer for infinite scroll
        const loadMoreRef = useRef<HTMLDivElement>(null);
        useIntersectionObserver({
          target: loadMoreRef,
          onIntersect: fetchNextPage,
          enabled: hasNextPage
        });
        
        // Virtualization for performance
        const rowVirtualizer = useVirtualizer({
          count: data?.materials.length || 0,
          getScrollElement: () => scrollContainerRef.current,
          estimateSize: () => 280, // Card height
          overscan: 5
        });
        
        if (error) {
          return (
            <Alert variant="destructive">
              <AlertDescription>
                Failed to load materials. Please try again.
              </AlertDescription>
            </Alert>
          );
        }
        
        return (
          <div className="space-y-4">
            {/* Search and filters */}
            <div className="flex flex-col gap-4 sm:flex-row">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                <Input
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  placeholder="Search materials..."
                  className="pl-10"
                />
              </div>
              
              <MaterialFilters
                filters={filters}
                onChange={setFilters}
                facets={data?.facets}
              />
            </div>
            
            {/* Results count */}
            {data && (
              <p className="text-sm text-muted-foreground">
                {data.totalCount} materials found
              </p>
            )}
            
            {/* Materials grid */}
            <div
              ref={scrollContainerRef}
              className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3"
              style={{ height: '600px', overflow: 'auto' }}
            >
              {isLoading ? (
                // Loading skeletons
                Array.from({ length: 6 }).map((_, i) => (
                  <MaterialCardSkeleton key={i} />
                ))
              ) : (
                // Virtual list
                <div
                  style={{
                    height: \`\${rowVirtualizer.getTotalSize()}px\`,
                    width: '100%',
                    position: 'relative'
                  }}
                >
                  {rowVirtualizer.getVirtualItems().map((virtualItem) => {
                    const material = data.materials[virtualItem.index];
                    return (
                      <div
                        key={virtualItem.key}
                        style={{
                          position: 'absolute',
                          top: 0,
                          left: 0,
                          width: '100%',
                          height: \`\${virtualItem.size}px\`,
                          transform: \`translateY(\${virtualItem.start}px)\`
                        }}
                      >
                        <MaterialCard
                          material={material}
                          selected={material.id === selectedId}
                          onSelect={() => onSelect(material.id)}
                        />
                      </div>
                    );
                  })}
                </div>
              )}
              
              {/* Load more trigger */}
              <div ref={loadMoreRef} className="h-10">
                {isFetchingNextPage && (
                  <Spinner className="mx-auto" />
                )}
              </div>
            </div>
          </div>
        );
      }
    `;
    
    // Cost breakdown visualization component
    const costBreakdown = `
      interface CostBreakdownProps {
        calculation: CalculationResult;
        variant?: 'chart' | 'table' | 'detailed';
      }
      
      export function CostBreakdown({
        calculation,
        variant = 'chart'
      }: CostBreakdownProps) {
        const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
        const [viewMode, setViewMode] = useState(variant);
        
        // Prepare data for visualization
        const chartData = useMemo(() => {
          return Object.entries(calculation.breakdown).map(([category, data]) => ({
            name: categoryNames[category],
            value: data.total,
            percentage: data.percentage,
            color: categoryColors[category]
          }));
        }, [calculation]);
        
        // Animation variants
        const containerVariants = {
          hidden: { opacity: 0 },
          visible: {
            opacity: 1,
            transition: {
              staggerChildren: 0.1
            }
          }
        };
        
        const itemVariants = {
          hidden: { opacity: 0, y: 20 },
          visible: { opacity: 1, y: 0 }
        };
        
        return (
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="space-y-6"
          >
            {/* View mode toggle */}
            <div className="flex justify-end">
              <ToggleGroup
                type="single"
                value={viewMode}
                onValueChange={setViewMode}
              >
                <ToggleGroupItem value="chart">
                  <PieChart className="h-4 w-4" />
                </ToggleGroupItem>
                <ToggleGroupItem value="table">
                  <Table className="h-4 w-4" />
                </ToggleGroupItem>
                <ToggleGroupItem value="detailed">
                  <List className="h-4 w-4" />
                </ToggleGroupItem>
              </ToggleGroup>
            </div>
            
            {/* Total cost */}
            <motion.div
              variants={itemVariants}
              className="rounded-lg bg-primary/10 p-6 text-center"
            >
              <p className="text-sm text-muted-foreground">Total Investment</p>
              <p className="text-4xl font-bold text-primary">
                ₹{formatCurrency(calculation.totalCost)}
              </p>
              <p className="mt-2 text-sm text-muted-foreground">
                ₹{formatNumber(calculation.costPerSqft)}/sqft
              </p>
            </motion.div>
            
            {/* Breakdown visualization */}
            {viewMode === 'chart' && (
              <motion.div variants={itemVariants}>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={chartData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={renderCustomLabel}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                      animationBegin={0}
                      animationDuration={800}
                      onClick={(data) => setSelectedCategory(data.name)}
                    >
                      {chartData.map((entry, index) => (
                        <Cell
                          key={\`cell-\${index}\`}
                          fill={entry.color}
                          className="cursor-pointer hover:opacity-80"
                        />
                      ))}
                    </Pie>
                    <Tooltip content={<CustomTooltip />} />
                  </PieChart>
                </ResponsiveContainer>
              </motion.div>
            )}
            
            {viewMode === 'table' && (
              <motion.div variants={itemVariants}>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Category</TableHead>
                      <TableHead className="text-right">Amount</TableHead>
                      <TableHead className="text-right">%</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {chartData.map((item) => (
                      <TableRow
                        key={item.name}
                        className="cursor-pointer hover:bg-accent"
                        onClick={() => setSelectedCategory(item.name)}
                      >
                        <TableCell className="font-medium">
                          <div className="flex items-center gap-2">
                            <div
                              className="h-3 w-3 rounded-full"
                              style={{ backgroundColor: item.color }}
                            />
                            {item.name}
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          ₹{formatCurrency(item.value)}
                        </TableCell>
                        <TableCell className="text-right">
                          {item.percentage}%
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </motion.div>
            )}
            
            {/* Category details modal */}
            <AnimatePresence>
              {selectedCategory && (
                <CategoryDetailsModal
                  category={selectedCategory}
                  data={calculation.breakdown[selectedCategory]}
                  onClose={() => setSelectedCategory(null)}
                />
              )}
            </AnimatePresence>
          </motion.div>
        );
      }
    `;

### 19\. Performance Optimization

#### 19.1 Code Splitting Strategy

typescript

    // Route-based code splitting
    const codeSplitting = {
      // Lazy loading routes
      routeSplitting: `
        import { lazy, Suspense } from 'react';
        import { Routes, Route } from 'react-router-dom';
        
        // Lazy load route components
        const Calculator = lazy(() => 
          import('./features/calculator').then(module => ({
            default: module.Calculator
          }))
        );
        
        const Materials = lazy(() => 
          import('./features/materials').then(module => ({
            default: module.Materials
          }))
        );
        
        const AdminDashboard = lazy(() => 
          import('./features/admin').then(module => ({
            default: module.AdminDashboard
          }))
        );
        
        // Loading component
        function RouteLoader() {
          return (
            <div className="flex h-screen items-center justify-center">
              <Spinner size="lg" />
            </div>
          );
        }
        
        // App routes with code splitting
        export function AppRoutes() {
          return (
            <Suspense fallback={<RouteLoader />}>
              <Routes>
                <Route path="/calculator/*" element={<Calculator />} />
                <Route path="/materials/*" element={<Materials />} />
                <Route path="/admin/*" element={<AdminDashboard />} />
              </Routes>
            </Suspense>
          );
        }
      `,
      
      // Component-level splitting
      componentSplitting: `
        // Heavy component lazy loading
        const HeavyChart = lazy(() => 
          import('./components/HeavyChart').then(module => ({
            default: module.HeavyChart
          }))
        );
        
        // Usage with error boundary
        function ChartContainer({ data }) {
          return (
            <ErrorBoundary fallback={<ChartError />}>
              <Suspense fallback={<ChartSkeleton />}>
                <HeavyChart data={data} />
              </Suspense>
            </ErrorBoundary>
          );
        }
      `,
      
      // Dynamic imports for features
      dynamicImports: `
        // Load features on demand
        async function loadPDFGenerator() {
          const { generatePDF } = await import('./lib/pdf-generator');
          return generatePDF;
        }
        
        // Usage
        async function handleGenerateReport() {
          setLoading(true);
          
          try {
            // Load PDF library only when needed
            const generatePDF = await loadPDFGenerator();
            const pdf = await generatePDF(projectData);
            
            // Download PDF
            downloadFile(pdf, 'project-report.pdf');
          } finally {
            setLoading(false);
          }
        }
      `
    };

#### 19.2 React Performance Optimizations

typescript

    // Performance optimization techniques
    const performanceOptimizations = {
      // Memoization strategies
      memoization: `
        // Expensive component memoization
        export const ExpensiveComponent = memo(
          ({ data, filters }) => {
            // Expensive computations
            const processedData = useMemo(() => {
              return processComplexData(data, filters);
            }, [data, filters]);
            
            // Stable callbacks
            const handleItemClick = useCallback((item) => {
              console.log('Clicked:', item);
            }, []);
            
            return (
              <div>
                {processedData.map(item => (
                  <Item
                    key={item.id}
                    data={item}
                    onClick={handleItemClick}
                  />
                ))}
              </div>
            );
          },
          // Custom comparison function
          (prevProps, nextProps) => {
            return (
              prevProps.data.id === nextProps.data.id &&
              deepEqual(prevProps.filters, nextProps.filters)
            );
          }
        );
      `,
      
      // Virtual scrolling
      virtualScrolling: `
        import { useVirtual } from '@tanstack/react-virtual';
        
        export function VirtualList({ items }) {
          const parentRef = useRef();
          
          const rowVirtualizer = useVirtual({
            size: items.length,
            parentRef,
            estimateSize: useCallback(() => 35, []),
            overscan: 5
          });
          
          return (
            <div
              ref={parentRef}
              className="h-[400px] overflow-auto"
            >
              <div
                style={{
                  height: \`\${rowVirtualizer.totalSize}px\`,
                  width: '100%',
                  position: 'relative'
                }}
              >
                {rowVirtualizer.virtualItems.map(virtualRow => (
                  <div
                    key={virtualRow.index}
                    style={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      width: '100%',
                      height: \`\${virtualRow.size}px\`,
                      transform: \`translateY(\${virtualRow.start}px)\`
                    }}
                  >
                    <ListItem item={items[virtualRow.index]} />
                  </div>
                ))}
              </div>
            </div>
          );
        }
      `,
      
      // Image optimization
      imageOptimization: `
        // Next.js Image component
        import Image from 'next/image';
        
        export function OptimizedImage({ src, alt, priority = false }) {
          return (
            <Image
              src={src}
              alt={alt}
              width={800}
              height={600}
              priority={priority}
              loading={priority ? 'eager' : 'lazy'}
              placeholder="blur"
              blurDataURL={generateBlurDataURL(src)}
              sizes="(max-width: 768px) 100vw,
                     (max-width: 1200px) 50vw,
                     33vw"
            />
          );
        }
        
        // Progressive image loading
        export function ProgressiveImage({ src, alt, className }) {
          const [imageSrc, setImageSrc] = useState(lowQualitySrc);
          const [imageLoading, setImageLoading] = useState(true);
          
          useEffect(() => {
            const img = new window.Image();
            img.src = src;
            img.onload = () => {
              setImageSrc(src);
              setImageLoading(false);
            };
          }, [src]);
          
          return (
            <div className={cn('relative overflow-hidden', className)}>
              <img
                src={imageSrc}
                alt={alt}
                className={cn(
                  'transition-all duration-300',
                  imageLoading && 'blur-sm scale-110'
                )}
              />
              {imageLoading && (
                <div className="absolute inset-0 animate-pulse bg-gray-200" />
              )}
            </div>
          );
        }
      `
    };

### 20\. Build & Deployment Pipeline

#### 20.1 Build Configuration

javascript

    // Next.js configuration
    const nextConfig = {
      // next.config.js
      experimental: {
        // Enable app directory
        appDir: true,
        
        // Server components
        serverComponents: true,
        
        // Optimize packages
        optimizePackageImports: ['lucide-react', 'date-fns']
      },
      
      // Image optimization
      images: {
        domains: ['clarityengine.in', 'storage.googleapis.com'],
        formats: ['image/avif', 'image/webp'],
        deviceSizes: [640, 750, 828, 1080, 1200, 1920],
        imageSizes: [16, 32, 48, 64, 96, 128, 256, 384]
      },
      
      // Webpack configuration
      webpack: (config, { isServer }) => {
        // Bundle analyzer
        if (process.env.ANALYZE === 'true') {
          const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer');
          config.plugins.push(
            new BundleAnalyzerPlugin({
              analyzerMode: 'static',
              reportFilename: isServer
                ? '../analyze/server.html'
                : './analyze/client.html'
            })
          );
        }
        
        // Aliases
        config.resolve.alias = {
          ...config.resolve.alias,
          '@': path.resolve(__dirname, './src')
        };
        
        return config;
      },
      
      // Environment variables
      env: {
        NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,
        NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL,
        NEXT_PUBLIC_SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
      },
      
      // Headers
      async headers() {
        return [
          {
            source: '/(.*)',
            headers: [
              {
                key: 'X-Frame-Options',
                value: 'DENY'
              },
              {
                key: 'X-Content-Type-Options',
                value: 'nosniff'
              },
              {
                key: 'X-XSS-Protection',
                value: '1; mode=block'
              },
              {
                key: 'Referrer-Policy',
                value: 'strict-origin-when-cross-origin'
              }
            ]
          }
        ];
      },
      
      // Redirects
      async redirects() {
        return [
          {
            source: '/home',
            destination: '/',
            permanent: true
          }
        ];
      }
    };

#### 20.2 CI/CD Pipeline

yaml

    # GitHub Actions workflow
    name: CI/CD Pipeline
    
    on:
      push:
        branches: [main, develop]
      pull_request:
        branches: [main]
    
    env:
      NODE_VERSION: '18.x'
      PNPM_VERSION: '8'
    
    jobs:
      # Lint and type check
      quality:
        runs-on: ubuntu-latest
        steps:
          - uses: actions/checkout@v3
          
          - uses: pnpm/action-setup@v2
            with:
              version: ${{ env.PNPM_VERSION }}
              
          - uses: actions/setup-node@v3
            with:
              node-version: ${{ env.NODE_VERSION }}
              cache: 'pnpm'
              
          - name: Install dependencies
            run: pnpm install --frozen-lockfile
            
          - name: Lint
            run: pnpm lint
            
          - name: Type check
            run: pnpm type-check
            
          - name: Format check
            run: pnpm format:check
    
      # Unit tests
      test:
        runs-on: ubuntu-latest
        steps:
          - uses: actions/checkout@v3
          
          - uses: pnpm/action-setup@v2
            with:
              version: ${{ env.PNPM_VERSION }}
              
          - uses: actions/setup-node@v3
            with:
              node-version: ${{ env.NODE_VERSION }}
              cache: 'pnpm'
              
          - name: Install dependencies
            run: pnpm install --frozen-lockfile
            
          - name: Run tests
            run: pnpm test:ci
            
          - name: Upload coverage
            uses: codecov/codecov-action@v3
            with:
              file: ./coverage/lcov.info
    
      # E2E tests
      e2e:
        runs-on: ubuntu-latest
        steps:
          - uses: actions/checkout@v3
          
          - uses: pnpm/action-setup@v2
            with:
              version: ${{ env.PNPM_VERSION }}
              
          - uses: actions/setup-node@v3
            with:
              node-version: ${{ env.NODE_VERSION }}
              cache: 'pnpm'
              
          - name: Install dependencies
            run: pnpm install --frozen-lockfile
            
          - name: Install Playwright
            run: pnpm exec playwright install --with-deps
            
          - name: Run E2E tests
            run: pnpm test:e2e
            
          - name: Upload test results
            if: always()
            uses: actions/upload-artifact@v3
            with:
              name: playwright-report
              path: playwright-report/
              retention-days: 30
    
      # Build
      build:
        runs-on: ubuntu-latest
        needs: [quality, test]
        steps:
          - uses: actions/checkout@v3
          
          - uses: pnpm/action-setup@v2
            with:
              version: ${{ env.PNPM_VERSION }}
              
          - uses: actions/setup-node@v3
            with:
              node-version: ${{ env.NODE_VERSION }}
              cache: 'pnpm'
              
          - name: Install dependencies
            run: pnpm install --frozen-lockfile
            
          - name: Build
            run: pnpm build
            env:
              NEXT_PUBLIC_API_URL: ${{ secrets.NEXT_PUBLIC_API_URL }}
              NEXT_PUBLIC_SUPABASE_URL: ${{ secrets.NEXT_PUBLIC_SUPABASE_URL }}
              NEXT_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.NEXT_PUBLIC_SUPABASE_ANON_KEY }}
              
          - name: Upload build artifacts
            uses: actions/upload-artifact@v3
            with:
              name: build-files
              path: .next/
    
      # Deploy to Vercel
      deploy:
        runs-on: ubuntu-latest
        needs: [build]
        if: github.ref == 'refs/heads/main'
        steps:
          - uses: actions/checkout@v3
          
          - name: Deploy to Vercel
            uses: amondnet/vercel-action@v25
            with:
              vercel-token: ${{ secrets.VERCEL_TOKEN }}
              vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
              vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
              vercel-args: '--prod'

* * *

Part 5: Backend Services
------------------------

### 21\. Calculation Engine Architecture

#### 21.1 Core Calculation Service

typescript

    // Calculation engine implementation
    class CalculationEngine {
      private readonly parameters: EngineeringParameters;
      private readonly pricingService: PricingService;
      private readonly cache: CacheService;
      
      constructor(
        parameters: EngineeringParameters,
        pricingService: PricingService,
        cache: CacheService
      ) {
        this.parameters = parameters;
        this.pricingService = pricingService;
        this.cache = cache;
      }
      
      async calculateProject(input: ProjectInput): Promise<CalculationResult> {
        // Generate cache key
        const cacheKey = this.generateCacheKey(input);
        
        // Check cache
        const cached = await this.cache.get<CalculationResult>(cacheKey);
        if (cached) {
          return cached;
        }
        
        // Perform calculations
        const startTime = performance.now();
        
        try {
          // 1. Calculate areas
          const areas = this.calculateAreas(input);
          
          // 2. Calculate structure cost
          const structure = await this.calculateStructure(input, areas);
          
          // 3. Calculate finishes cost
          const finishes = await this.calculateFinishes(input, areas);
          
          // 4. Calculate MEP cost
          const mep = await this.calculateMEP(input, areas);
          
          // 5. Calculate external works
          const external = await this.calculateExternal(input);
          
          // 6. Calculate professional fees
          const professional = this.calculateProfessionalFees(
            structure.total + finishes.total + mep.total + external.total
          );
          
          // 7. Calculate statutory charges
          const statutory = await this.calculateStatutoryCharges(input);
          
          // 8. Aggregate results
          const result = this.aggregateResults({
            structure,
            finishes,
            mep,
            external,
            professional,
            statutory
          });
          
          // Cache result
          await this.cache.set(cacheKey, result, 3600); // 1 hour
          
          // Log performance
          const duration = performance.now() - startTime;
          logger.info('Calculation completed', {
            projectId: input.projectId,
            duration,
            cacheHit: false
          });
          
          return result;
        } catch (error) {
          logger.error('Calculation failed', {
            error,
            input
          });
          throw new CalculationError('Failed to calculate project cost', error);
        }
      }
      
      private calculateAreas(input: ProjectInput): AreaCalculation {
        const { plot, structure, buildingType } = input;
        
        // Calculate buildable area
        const setbacks = this.parameters.getSetbacks(input.locationId);
        const buildableArea = this.applySetbacks(plot.area, setbacks);
        
        // Calculate ground coverage
        const maxCoverage = this.parameters.getMaxCoverage(input.locationId);
        const groundCoverage = Math.min(
          buildableArea * maxCoverage,
          buildableArea
        );
        
        // Calculate floor areas
        const floorAreas: FloorArea[] = [];
        
        // Ground floor
        if (structure.hasStilt) {
          floorAreas.push({
            floor: 0,
            type: 'stilt',
            area: groundCoverage * 0.5, // 50% for stilt
            factor: 0.3 // Reduced cost factor
          });
        } else {
          floorAreas.push({
            floor: 0,
            type: 'full',
            area: groundCoverage,
            factor: 1.0
          });
        }
        
        // Upper floors
        for (let i = 1; i <= structure.floorsAboveGround; i++) {
          floorAreas.push({
            floor: i,
            type: 'full',
            area: groundCoverage * 0.85, // 85% of ground
            factor: 1.0
          });
        }
        
        // Basement
        if (structure.hasBasement) {
          floorAreas.push({
            floor: -1,
            type: 'basement',
            area: groundCoverage * 0.7,
            factor: 1.5 // Higher cost factor
          });
        }
        
        // Calculate totals
        const totalBuiltUpArea = floorAreas.reduce(
          (sum, floor) => sum + floor.area,
          0
        );
        
        const effectiveArea = floorAreas.reduce(
          (sum, floor) => sum + floor.area * floor.factor,
          0
        );
        
        return {
          plotArea: plot.area,
          buildableArea,
          groundCoverage,
          floorAreas,
          totalBuiltUpArea,
          effectiveArea
        };
      }
      
      private async calculateStructure(
        input: ProjectInput,
        areas: AreaCalculation
      ): Promise<CategoryBreakdown> {
        const items: LineItem[] = [];
        
        // 1. Foundation
        const foundation = await this.calculateFoundation(input, areas);
        items.push(...foundation);
        
        // 2. Superstructure
        const superstructure = await this.calculateSuperstructure(input, areas);
        items.push(...superstructure);
        
        // 3. Roofing
        const roofing = await this.calculateRoofing(input, areas);
        items.push(...roofing);
        
        // Calculate total
        const total = items.reduce((sum, item) => sum + item.amount, 0);
        
        return {
          category: 'structure',
          items,
          total,
          percentage: 0 // Will be calculated later
        };
      }
      
      private async calculateFoundation(
        input: ProjectInput,
        areas: AreaCalculation
      ): Promise<LineItem[]> {
        const items: LineItem[] = [];
        const { siteConditions } = input;
        
        // Get parameters
        const soilBearing = siteConditions?.soilBearing || 
          this.parameters.getDefaultSoilBearing(input.locationId);
        const waterTable = siteConditions?.waterTableDepth ||
          this.parameters.getAverageWaterTable(input.locationId);
        
        // Determine foundation type
        const foundationType = this.determineFoundationType(
          soilBearing,
          waterTable,
          input.structure.floorsAboveGround
        );
        
        // Calculate excavation
        const excavationDepth = this.getExcavationDepth(foundationType);
        const excavationVolume = areas.groundCoverage * excavationDepth * 1.2; // 20% extra
        
        items.push({
          id: 'excavation',
          name: 'Excavation',
          description: 'Earth excavation for foundation',
          quantity: excavationVolume,
          unit: 'cum',
          rate: await this.pricingService.getLabourRate('excavation', input.locationId),
          amount: excavationVolume * rate
        });
        
        // PCC (Plain Cement Concrete)
        const pccThickness = 0.1; // 100mm
        const pccVolume = areas.groundCoverage * pccThickness;
        const pccMix = await this.getMaterialMix('pcc_m10');
        
        for (const material of pccMix) {
          const rate = await this.pricingService.getMaterialPrice(
            material.materialId,
            input.locationId
          );
          
          items.push({
            id: \`pcc_\${material.materialId}\`,
            name: \`PCC - \${material.name}\`,
            description: material.description,
            quantity: pccVolume * material.consumptionRate,
            unit: material.unit,
            rate,
            amount: pccVolume * material.consumptionRate * rate
          });
        }
        
        // Foundation concrete (RCC)
        const foundationVolume = this.calculateFoundationVolume(
          foundationType,
          areas
        );
        const rccMix = await this.getMaterialMix('rcc_m20');
        
        for (const material of rccMix) {
          const rate = await this.pricingService.getMaterialPrice(
            material.materialId,
            input.locationId
          );
          
          items.push({
            id: \`foundation_\${material.materialId}\`,
            name: \`Foundation - \${material.name}\`,
            description: material.description,
            quantity: foundationVolume * material.consumptionRate,
            unit: material.unit,
            rate,
            amount: foundationVolume * material.consumptionRate * rate
          });
        }
        
        return items;
      }
      
      // Additional calculation methods...
    }

#### 21.2 Calculation Formulas

typescript

    // Engineering calculation formulas
    const calculationFormulas = {
      // Concrete volume calculations
      concrete: {
        // Column concrete
        columnConcrete: (input: StructureInput): number => {
          const { floors, columnSize, columnCount } = input;
          const columnHeight = 3.0; // meters
          const columnVolume = columnSize.length * columnSize.width * columnHeight;
          
          return columnVolume * columnCount * floors;
        },
        
        // Beam concrete
        beamConcrete: (input: StructureInput): number => {
          const { area, beamSize } = input;
          const beamLength = Math.sqrt(area) * 4; // Approximate
          const beamVolume = beamSize.width * beamSize.depth * beamLength;
          
          return beamVolume * 1.2; // 20% wastage
        },
        
        // Slab concrete
        slabConcrete: (input: StructureInput): number => {
          const { area, slabThickness } = input;
          return area * slabThickness * 1.05; // 5% wastage
        }
      },
      
      // Steel calculations
      steel: {
        // Steel for RCC
        steelForRCC: (concreteVolume: number, steelRatio: number = 0.01): number => {
          // 1% steel by volume (100 kg/cum typical)
          const steelDensity = 7850; // kg/cum
          return concreteVolume * steelRatio * steelDensity;
        },
        
        // Stirrups calculation
        stirrups: (beamLength: number, spacing: number = 0.15): number => {
          const stirrupCount = Math.ceil(beamLength / spacing);
          const stirrupLength = 1.2; // meters (perimeter)
          const stirrupWeight = 0.22; // kg/m for 8mm dia
          
          return stirrupCount * stirrupLength * stirrupWeight;
        }
      },
      
      // Masonry calculations
      masonry: {
        // Brick calculation
        brickCount: (wallArea: number, brickSize: BrickSize): number => {
          const brickArea = (brickSize.length + 0.01) * (brickSize.height + 0.01);
          return Math.ceil(wallArea / brickArea) * 1.05; // 5% wastage
        },
        
        // Mortar calculation
        mortarVolume: (wallVolume: number): number => {
          // 20% of wall volume is mortar
          return wallVolume * 0.20;
        },
        
        // Plaster calculation
        plasterVolume: (wallArea: number, thickness: number = 0.012): number => {
          // 12mm internal, 18mm external
          return wallArea * thickness * 1.1; // 10% wastage
        }
      },
      
      // Flooring calculations
      flooring: {
        // Tile calculation
        tileCount: (floorArea: number, tileSize: TileSize): number => {
          const tileArea = tileSize.length * tileSize.width;
          const tilesRequired = Math.ceil(floorArea / tileArea);
          
          // Add wastage based on room size
          const wastagePercent = floorArea < 100 ? 0.10 : 0.07;
          return Math.ceil(tilesRequired * (1 + wastagePercent));
        },
        
        // Skirting calculation
        skirtingLength: (roomPerimeter: number): number => {
          // Deduct door widths (assume 3 doors of 0.9m each)
          return roomPerimeter - (3 * 0.9);
        }
      },
      
      // MEP calculations
      mep: {
        // Electrical points
        electricalPoints: (input: LayoutConfig): number => {
          const { bedrooms, bathrooms, kitchen, living } = input;
          
          const points = {
            bedroom: 8, // 4 lights, 2 fans, 2 plugs
            bathroom: 4, // 2 lights, 1 exhaust, 1 geyser
            kitchen: 12, // Multiple appliances
            living: 10 // TV, AC, lights, fans
         };
         
         return (
           bedrooms * points.bedroom +
           bathrooms * points.bathroom +
           kitchen.count * points.kitchen +
           points.living
         );
       },
       
       // Plumbing fixtures
       plumbingFixtures: (input: LayoutConfig): PlumbingCount => {
         const { bathrooms, kitchen } = input;
         
         return {
           wcSeat: bathrooms,
           washBasin: bathrooms + 1, // Extra in common area
           shower: bathrooms,
           kitchenSink: kitchen.count,
           taps: bathrooms * 3 + kitchen.count * 2,
           floorDrains: bathrooms + kitchen.count + 2 // Extra for utility
         };
       },
       
       // Water tank capacity
       waterTankCapacity: (occupants: number): number => {
         const dailyRequirement = 135; // liters per person
         const storageDays = 2;
         return occupants * dailyRequirement * storageDays;
       }
     },
     
     // Professional fees calculations
     professionalFees: {
       // Architect fees
       architectFees: (projectCost: number, area: number): number => {
         // Sliding scale based on project cost
         if (projectCost < 5000000) {
           return projectCost * 0.05; // 5%
         } else if (projectCost < 10000000) {
           return projectCost * 0.04; // 4%
         } else {
           return projectCost * 0.035; // 3.5%
         }
       },
       
       // Structural consultant
       structuralFees: (projectCost: number): number => {
         return projectCost * 0.015; // 1.5%
       },
       
       // MEP consultant
       mepFees: (mepCost: number): number => {
         return mepCost * 0.10; // 10% of MEP cost
       },
       
       // Supervision charges
       supervisionCharges: (projectCost: number, duration: number): number => {
         const monthlyCharge = 50000; // Base rate
         const percentageCharge = projectCost * 0.02; // 2%
         
         return Math.max(
           monthlyCharge * duration,
           percentageCharge
         );
       }
     }
    };
    
    // Material consumption recipes
    const materialRecipes = {
     // Concrete mixes
     concrete: {
       m10: {
         cement: 210, // kg/cum
         sand: 0.52, // cum/cum
         aggregate: 0.87, // cum/cum
         water: 180 // liters/cum
       },
       m20: {
         cement: 320,
         sand: 0.48,
         aggregate: 0.84,
         water: 180
       },
       m25: {
         cement: 370,
         sand: 0.45,
         aggregate: 0.82,
         water: 185
       }
     },
     
     // Mortar mixes
     mortar: {
       cm_1_4: { // Cement mortar 1:4
         cement: 350, // kg/cum
         sand: 1.0 // cum/cum
       },
       cm_1_6: {
         cement: 250,
         sand: 1.0
       }
     },
     
     // Plaster mixes
     plaster: {
       internal_12mm: {
         cement: 5.5, // kg/sqm
         sand: 0.013 // cum/sqm
       },
       external_18mm: {
         cement: 8.5,
         sand: 0.020
       },
       ceiling_10mm: {
         cement: 4.5,
         sand: 0.010
       }
     }
    };

### 22\. File Processing Services

#### 22.1 Document Parser Service

typescript

    // Document parsing service for vendor catalogs
    class DocumentParserService {
      private readonly openai: OpenAIApi;
      private readonly ocrService: OCRService;
      private readonly validator: DataValidator;
      
      async parseDocument(
        file: UploadedFile,
        documentType: DocumentType
      ): Promise<ParseResult> {
        try {
          // 1. Validate file
          this.validateFile(file);
          
          // 2. Extract content based on type
          let extractedContent: string;
          
          switch (file.mimetype) {
            case 'application/pdf':
              extractedContent = await this.parsePDF(file);
              break;
              
            case 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
              extractedContent = await this.parseExcel(file);
              break;
              
            case 'image/jpeg':
            case 'image/png':
              extractedContent = await this.parseImage(file);
              break;
              
            default:
              throw new Error(`Unsupported file type: ${file.mimetype}`);
          }
          
          // 3. Process with AI
          const structuredData = await this.extractStructuredData(
            extractedContent,
            documentType
          );
          
          // 4. Validate extracted data
          const validationResult = this.validator.validate(
            structuredData,
            documentType
          );
          
          return {
            success: true,
            data: structuredData,
            confidence: validationResult.confidence,
            warnings: validationResult.warnings,
            requiresReview: validationResult.confidence < 0.8
          };
        } catch (error) {
          logger.error('Document parsing failed', { error, file });
          
          return {
            success: false,
            error: error.message,
            data: null
          };
        }
      }
      
      private async parsePDF(file: UploadedFile): Promise<string> {
        const pdfParser = new PDFParser();
        
        return new Promise((resolve, reject) => {
          pdfParser.on('pdfParser_dataReady', (pdfData) => {
            const text = pdfParser.getRawTextContent();
            resolve(text);
          });
          
          pdfParser.on('pdfParser_dataError', (error) => {
            reject(error);
          });
          
          pdfParser.parseBuffer(file.buffer);
        });
      }
      
      private async parseExcel(file: UploadedFile): Promise<string> {
        const workbook = XLSX.read(file.buffer, { type: 'buffer' });
        const sheets = workbook.SheetNames;
        
        let content = '';
        
        for (const sheetName of sheets) {
          const worksheet = workbook.Sheets[sheetName];
          const jsonData = XLSX.utils.sheet_to_json(worksheet, {
            header: 1,
            defval: ''
          });
          
          content += `Sheet: ${sheetName}\n`;
          content += JSON.stringify(jsonData, null, 2);
          content += '\n\n';
        }
        
        return content;
      }
      
      private async parseImage(file: UploadedFile): Promise<string> {
        // Use OCR for images
        const text = await this.ocrService.extractText(file.buffer);
        return text;
      }
      
      private async extractStructuredData(
        content: string,
        documentType: DocumentType
      ): Promise<any> {
        const prompt = this.buildPrompt(documentType, content);
        
        const response = await this.openai.createChatCompletion({
          model: 'gpt-4',
          messages: [
            {
              role: 'system',
              content: 'You are an expert at extracting structured data from construction industry documents. Always respond with valid JSON.'
            },
            {
              role: 'user',
              content: prompt
            }
          ],
          temperature: 0.1,
          max_tokens: 4000
        });
        
        const result = response.data.choices[0].message.content;
        
        try {
          return JSON.parse(result);
        } catch (error) {
          throw new Error('Failed to parse AI response as JSON');
        }
      }
      
      private buildPrompt(documentType: DocumentType, content: string): string {
        const prompts = {
          catalog: `
            Extract product information from this catalog:
            - Product name
            - SKU/Code
            - Category
            - Brand
            - Specifications (size, color, material, etc.)
            - Price (if mentioned)
            - Unit (piece, sqft, box, etc.)
            
            Content:
            ${content}
            
            Return as JSON array of products.
          `,
          
          quotation: `
            Extract quotation details:
            - Vendor name
            - Date
            - Items with quantities and rates
            - Total amount
            - Terms and conditions
            
            Content:
            ${content}
            
            Return as structured JSON.
          `,
          
          specification: `
            Extract technical specifications:
            - Material type
            - Technical properties
            - Standards/Codes
            - Test results
            - Application guidelines
            
            Content:
            ${content}
            
            Return as structured JSON.
          `
        };
        
        return prompts[documentType] || prompts.catalog;
      }
    }
    
    // OCR Service
    class OCRService {
      private readonly tesseract: TesseractWorker;
      
      constructor() {
        this.initializeTesseract();
      }
      
      private async initializeTesseract() {
        this.tesseract = await createWorker({
          logger: (m) => logger.debug('Tesseract:', m)
        });
        
        await this.tesseract.loadLanguage('eng');
        await this.tesseract.initialize('eng');
      }
      
      async extractText(imageBuffer: Buffer): Promise<string> {
        try {
          const { data: { text } } = await this.tesseract.recognize(imageBuffer);
          return text;
        } catch (error) {
          logger.error('OCR failed', { error });
          throw new Error('Failed to extract text from image');
        }
      }
      
      async extractTable(imageBuffer: Buffer): Promise<any[][]> {
        // Enhanced OCR for table extraction
        const { data } = await this.tesseract.recognize(imageBuffer, {
          tessedit_pageseg_mode: PSM.AUTO_OSD
        });
        
        // Process OCR output to identify table structure
        const lines = data.lines;
        const table = this.constructTableFromLines(lines);
        
        return table;
      }
      
      private constructTableFromLines(lines: any[]): any[][] {
        // Group lines by Y coordinate (rows)
        const rows = [];
        let currentRow = [];
        let lastY = null;
        
        for (const line of lines) {
          const y = line.bbox.y0;
          
          if (lastY && Math.abs(y - lastY) > 10) {
            // New row
            rows.push(currentRow);
            currentRow = [];
          }
          
          currentRow.push(line.text);
          lastY = y;
        }
        
        if (currentRow.length > 0) {
          rows.push(currentRow);
        }
        
        return rows;
      }
    }

#### 22.2 Report Generation Service

typescript

    // PDF report generation service
    class ReportGenerationService {
      private readonly templateEngine: HandlebarsTemplateEngine;
      private readonly pdfGenerator: PDFGenerator;
      private readonly storage: StorageService;
      
      async generateReport(
        projectId: string,
        options: ReportOptions
      ): Promise<ReportResult> {
        try {
          // 1. Fetch project data
          const projectData = await this.fetchProjectData(projectId);
          
          // 2. Prepare report data
          const reportData = await this.prepareReportData(
            projectData,
            options
          );
          
          // 3. Generate HTML from template
          const html = await this.generateHTML(reportData, options.format);
          
          // 4. Convert to PDF
          const pdfBuffer = await this.generatePDF(html, options);
          
          // 5. Upload to storage
          const fileUrl = await this.uploadReport(
            projectId,
            pdfBuffer,
            options
          );
          
          // 6. Save report metadata
          await this.saveReportMetadata({
            projectId,
            fileUrl,
            format: options.format,
            generatedAt: new Date(),
            expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days
          });
          
          return {
            success: true,
            reportUrl: fileUrl,
            expiresAt: metadata.expiresAt
          };
        } catch (error) {
          logger.error('Report generation failed', {
            error,
            projectId,
            options
          });
          
          throw new ReportGenerationError(
            'Failed to generate report',
            error
          );
        }
      }
      
      private async prepareReportData(
        projectData: Project,
        options: ReportOptions
      ): Promise<ReportData> {
        const { calculation, materials, location } = projectData;
        
        // Format currency values
        const formatCurrency = (value: number) => {
          return new Intl.NumberFormat('en-IN', {
            style: 'currency',
            currency: 'INR',
            maximumFractionDigits: 0
          }).format(value);
        };
        
        // Prepare sections based on options
        const sections = {};
        
        if (options.sections.includes('summary')) {
          sections.summary = {
            totalCost: formatCurrency(calculation.totalCost),
            costPerSqft: formatCurrency(calculation.costPerSqft),
            builtUpArea: projectData.totalBuiltUpArea,
            timeline: `${calculation.timeline.months} months`,
            confidence: `${Math.round(calculation.confidence * 100)}%`
          };
        }
        
        if (options.sections.includes('breakdown')) {
          sections.breakdown = Object.entries(calculation.breakdown).map(
            ([category, data]) => ({
              category: this.formatCategoryName(category),
              amount: formatCurrency(data.total),
              percentage: `${data.percentage}%`,
              items: data.items.map(item => ({
                name: item.name,
                quantity: `${item.quantity} ${item.unit}`,
                rate: formatCurrency(item.rate),
                amount: formatCurrency(item.amount)
              }))
            })
          );
        }
        
        if (options.sections.includes('specifications')) {
          sections.specifications = await this.prepareMaterialSpecs(materials);
        }
        
        if (options.sections.includes('timeline')) {
          sections.timeline = this.prepareTimeline(calculation.timeline);
        }
        
        return {
          project: {
            name: projectData.name,
            location: location.name,
            date: new Date().toLocaleDateString('en-IN'),
            ...options.personalization
          },
          sections,
          metadata: {
            generatedAt: new Date().toISOString(),
            version: '1.0'
          }
        };
      }
      
      private async generateHTML(
        data: ReportData,
        format: ReportFormat
      ): Promise<string> {
        // Load template based on format
        const templateName = `report-${format}.hbs`;
        const template = await this.templateEngine.loadTemplate(templateName);
        
        // Register helpers
        this.templateEngine.registerHelpers({
          formatDate: (date) => new Date(date).toLocaleDateString('en-IN'),
          formatNumber: (num) => num.toLocaleString('en-IN'),
          math: (lvalue, operator, rvalue) => {
            lvalue = parseFloat(lvalue);
            rvalue = parseFloat(rvalue);
            
            return {
              '+': lvalue + rvalue,
              '-': lvalue - rvalue,
              '*': lvalue * rvalue,
              '/': lvalue / rvalue,
              '%': lvalue % rvalue
            }[operator];
          }
        });
        
        // Compile template
        const html = template(data);
        
        // Include CSS
        const css = await this.loadCSS('report-styles.css');
        
        return `
          <!DOCTYPE html>
          <html>
            <head>
              <meta charset="UTF-8">
              <title>${data.project.name} - Cost Estimate</title>
              <style>${css}</style>
            </head>
            <body>
              ${html}
            </body>
          </html>
        `;
      }
      
      private async generatePDF(
        html: string,
        options: ReportOptions
      ): Promise<Buffer> {
        const browser = await puppeteer.launch({
          headless: true,
          args: ['--no-sandbox', '--disable-setuid-sandbox']
        });
        
        try {
          const page = await browser.newPage();
          
          // Set content
          await page.setContent(html, {
            waitUntil: 'networkidle0'
          });
          
          // Generate PDF
          const pdfBuffer = await page.pdf({
            format: 'A4',
            margin: {
              top: '20mm',
              right: '15mm',
              bottom: '20mm',
              left: '15mm'
            },
            printBackground: true,
            displayHeaderFooter: true,
            headerTemplate: `
              <div style="width: 100%; font-size: 10px; text-align: center;">
                <span class="title"></span>
              </div>
            `,
            footerTemplate: `
              <div style="width: 100%; font-size: 10px; text-align: center;">
                Page <span class="pageNumber"></span> of <span class="totalPages"></span>
              </div>
            `
          });
          
          return pdfBuffer;
        } finally {
          await browser.close();
        }
      }
    }

### 23\. Notification Services

#### 23.1 Multi-channel Notification Service

typescript

    // Unified notification service
    class NotificationService {
      private readonly emailService: EmailService;
      private readonly smsService: SMSService;
      private readonly whatsappService: WhatsAppService;
      private readonly pushService: PushNotificationService;
      private readonly inAppService: InAppNotificationService;
      
      async send(
        notification: NotificationRequest
      ): Promise<NotificationResult> {
        const { userId, type, channels, data } = notification;
        
        // Get user preferences
        const userPreferences = await this.getUserPreferences(userId);
        
        // Filter channels based on preferences
        const enabledChannels = channels.filter(
          channel => userPreferences[channel] !== false
        );
        
        // Send through each channel
        const results = await Promise.allSettled(
          enabledChannels.map(channel => 
            this.sendToChannel(channel, notification)
          )
        );
        
        // Log results
        const successful = results.filter(r => r.status === 'fulfilled').length;
        const failed = results.filter(r => r.status === 'rejected').length;
        
        logger.info('Notification sent', {
          notificationId: notification.id,
          userId,
          type,
          channels: enabledChannels,
          successful,
          failed
        });
        
        return {
          sent: successful,
          failed,
          details: results
        };
      }
      
      private async sendToChannel(
        channel: NotificationChannel,
        notification: NotificationRequest
      ): Promise<void> {
        switch (channel) {
          case 'email':
            return this.emailService.send(notification);
            
          case 'sms':
            return this.smsService.send(notification);
            
          case 'whatsapp':
            return this.whatsappService.send(notification);
            
          case 'push':
            return this.pushService.send(notification);
            
          case 'in_app':
            return this.inAppService.send(notification);
            
          default:
            throw new Error(`Unknown channel: ${channel}`);
        }
      }
    }
    
    // Email service implementation
    class EmailService {
      private readonly sendgrid: SendGridMail;
      private readonly templates: Map<string, string>;
      
      constructor() {
        this.sendgrid = sgMail;
        this.sendgrid.setApiKey(process.env.SENDGRID_API_KEY);
        this.loadTemplates();
      }
      
      async send(notification: NotificationRequest): Promise<void> {
        const { userId, type, data } = notification;
        
        // Get user email
        const user = await getUserById(userId);
        if (!user?.email) {
          throw new Error('User email not found');
        }
        
        // Get template
        const template = this.templates.get(type);
        if (!template) {
          throw new Error(`Email template not found for type: ${type}`);
        }
        
        // Prepare email
        const msg = {
          to: user.email,
          from: {
            email: '<EMAIL>',
            name: 'Clarity Engine'
          },
          templateId: template,
          dynamicTemplateData: {
            userName: user.fullName || 'User',
            ...data
          }
        };
        
        // Send email
        try {
          await this.sendgrid.send(msg);
        } catch (error) {
          logger.error('Email send failed', {
            error,
            userId,
            type
          });
          throw error;
        }
      }
      
      private loadTemplates() {
        this.templates = new Map([
          ['welcome', 'd-1234567890abcdef'],
          ['project_created', 'd-2345678901abcdef'],
          ['report_ready', 'd-3456789012abcdef'],
          ['price_alert', 'd-4567890123abcdef'],
          ['password_reset', 'd-5678901234abcdef']
        ]);
      }
    }
    
    // SMS service implementation
    class SMSService {
      private readonly twilio: Twilio;
      private readonly templates: Map<string, string>;
      
      constructor() {
        this.twilio = new Twilio(
          process.env.TWILIO_ACCOUNT_SID,
          process.env.TWILIO_AUTH_TOKEN
        );
        this.loadTemplates();
      }
      
      async send(notification: NotificationRequest): Promise<void> {
        const { userId, type, data } = notification;
        
        // Get user phone
        const user = await getUserById(userId);
        if (!user?.phone || !user.phoneVerified) {
          throw new Error('User phone not found or not verified');
        }
        
        // Get template
        const template = this.templates.get(type);
        if (!template) {
          throw new Error(`SMS template not found for type: ${type}`);
        }
        
        // Prepare message
        const message = this.formatTemplate(template, data);
        
        // Send SMS
        try {
          await this.twilio.messages.create({
            body: message,
            from: process.env.TWILIO_PHONE_NUMBER,
            to: user.phone
          });
        } catch (error) {
          logger.error('SMS send failed', {
            error,
            userId,
            type
          });
          throw error;
        }
      }
      
      private formatTemplate(template: string, data: any): string {
        return template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
          return data[key] || match;
        });
      }
      
      private loadTemplates() {
        this.templates = new Map([
          ['otp', 'Your Clarity Engine OTP is {{otp}}. Valid for 10 minutes.'],
          ['project_created', 'Your project "{{projectName}}" has been created successfully.'],
          ['report_ready', 'Your cost report is ready! Download: {{reportUrl}}'],
          ['price_alert', '{{materialName}} price dropped by {{percentage}}%! Check it out.']
        ]);
      }
    }

### 24\. Analytics & Monitoring

#### 24.1 Analytics Service

typescript

    // Analytics tracking service
    class AnalyticsService {
      private readonly posthog: PostHog;
      private readonly mixpanel: Mixpanel;
      private readonly customAnalytics: CustomAnalytics;
      
      async trackEvent(event: AnalyticsEvent): Promise<void> {
        const { userId, eventType, properties, timestamp } = event;
        
        // Enrich event with context
        const enrichedEvent = {
          ...event,
          properties: {
            ...properties,
            ...await this.getEventContext(userId)
          },
          timestamp: timestamp || new Date()
        };
        
        // Send to multiple providers
        await Promise.all([
          this.trackPostHog(enrichedEvent),
          this.trackMixpanel(enrichedEvent),
          this.trackCustom(enrichedEvent)
        ]);
      }
      
      private async getEventContext(userId?: string): Promise<EventContext> {
        const context: EventContext = {
          app_version: process.env.APP_VERSION,
          environment: process.env.NODE_ENV,
          timestamp: new Date().toISOString()
        };
        
        if (userId) {
          const user = await getUserById(userId);
          if (user) {
            context.user_type = user.role;
            context.user_created_at = user.createdAt;
            context.user_location = user.location;
          }
        }
        
        return context;
      }
      
      async trackPageView(pageView: PageViewEvent): Promise<void> {
        const { userId, url, referrer, duration } = pageView;
        
        await this.trackEvent({
          userId,
          eventType: 'page_view',
          properties: {
            url,
            referrer,
            duration,
            path: new URL(url).pathname
          }
        });
      }
      
      async trackCalculation(
        userId: string,
        projectId: string,
        calculation: CalculationResult
      ): Promise<void> {
        await this.trackEvent({
          userId,
          eventType: 'calculation_completed',
          properties: {
            projectId,
            totalCost: calculation.totalCost,
            builtUpArea: calculation.builtUpArea,
            costPerSqft: calculation.costPerSqft,
            qualityTier: calculation.qualityTier,
            floors: calculation.floors,
            calculationTime: calculation.calculationTimeMs
          }
        });
        
        // Track material selections
        for (const [category, materialId] of Object.entries(calculation.materials)) {
          await this.trackEvent({
            userId,
            eventType: 'material_selected',
            properties: {
              projectId,
              category,
              materialId,
              context: 'calculation'
            }
          });
        }
      }
    }
    
    // Custom analytics for business metrics
    class CustomAnalytics {
      private readonly db: Database;
      
      async recordCalculationMetrics(
        projectId: string,
        metrics: CalculationMetrics
      ): Promise<void> {
        await this.db.analytics.calculation_metrics.create({
          data: {
            projectId,
            totalTimeMs: metrics.totalTime,
            dbTimeMs: metrics.dbTime,
            computeTimeMs: metrics.computeTime,
            cacheHits: metrics.cacheHits,
            cacheMisses: metrics.cacheMisses,
            totalMaterials: metrics.materialCount,
            totalLineItems: metrics.lineItemCount
          }
        });
      }
      
      async recordMaterialSelection(
        selection: MaterialSelectionEvent
      ): Promise<void> {
        await this.db.analytics.material_selections.create({
          data: {
            materialId: selection.materialId,
            projectId: selection.projectId,
            userId: selection.userId,
            selectionContext: selection.context,
            qualityTier: selection.qualityTier,
            locationId: selection.locationId,
            action: selection.action
          }
        });
      }
      
      async getPopularMaterials(
        locationId: string,
        category: string,
        limit: number = 10
      ): Promise<PopularMaterial[]> {
        const results = await this.db.$queryRaw`
          SELECT 
            m.id,
            m.name,
            m.brand_id,
            COUNT(ms.id) as selection_count,
            AVG(p.rating) as avg_rating
          FROM materials m
          JOIN material_selections ms ON m.id = ms.material_id
          LEFT JOIN project_ratings p ON ms.project_id = p.project_id
          WHERE ms.location_id = ${locationId}
            AND m.category_id = ${category}
            AND ms.action = 'finalized'
            AND ms.created_at > NOW() - INTERVAL '30 days'
          GROUP BY m.id, m.name, m.brand_id
          ORDER BY selection_count DESC
          LIMIT ${limit}
        `;
        
        return results;
      }
    }

#### 24.2 Monitoring & Alerting

typescript

    // Application monitoring service
    class MonitoringService {
      private readonly sentry: Sentry;
      private readonly prometheus: PrometheusClient;
      private readonly healthChecks: Map<string, HealthCheck>;
      
      constructor() {
        this.initializeSentry();
        this.initializePrometheus();
        this.registerHealthChecks();
      }
      
      private initializeSentry() {
        Sentry.init({
          dsn: process.env.SENTRY_DSN,
          environment: process.env.NODE_ENV,
          tracesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,
          integrations: [
            new Sentry.Integrations.Http({ tracing: true }),
            new Sentry.Integrations.Postgres({ tracing: true })
          ],
          beforeSend(event, hint) {
            // Filter sensitive data
            if (event.request) {
              delete event.request.cookies;
              delete event.request.headers?.authorization;
            }
            return event;
          }
        });
      }
      
      private initializePrometheus() {
        // Request duration histogram
        this.prometheus.histogram('http_request_duration_seconds', {
          help: 'Duration of HTTP requests in seconds',
          labelNames: ['method', 'route', 'status'],
          buckets: [0.1, 0.5, 1, 2, 5]
        });
        
        // Active connections gauge
        this.prometheus.gauge('database_connections_active', {
          help: 'Number of active database connections'
        });
        
        // Calculation metrics
        this.prometheus.histogram('calculation_duration_seconds', {
          help: 'Duration of cost calculations',
          labelNames: ['type'],
          buckets: [0.1, 0.5, 1, 2, 5, 10]
        });
        
        // Business metrics
        this.prometheus.counter('projects_created_total', {
          help: 'Total number of projects created',
          labelNames: ['location', 'type']
        });
      }
      
      private registerHealthChecks() {
        this.healthChecks = new Map([
          ['database', this.checkDatabase],
          ['redis', this.checkRedis],
          ['storage', this.checkStorage],
          ['external_apis', this.checkExternalAPIs]
        ]);
      }
      
      async getHealthStatus(): Promise<HealthStatus> {
        const checks = await Promise.all(
          Array.from(this.healthChecks.entries()).map(
            async ([name, check]) => {
              const start = Date.now();
              try {
                await check();
                return {
                  name,
                  status: 'healthy',
                  duration: Date.now() - start
                };
              } catch (error) {
                return {
                  name,
                  status: 'unhealthy',
                  duration: Date.now() - start,
                  error: error.message
                };
              }
            }
          )
        );
        
        const allHealthy = checks.every(c => c.status === 'healthy');
        
        return {
          status: allHealthy ? 'healthy' : 'unhealthy',
          timestamp: new Date(),
          checks
        };
      }
      
      private async checkDatabase(): Promise<void> {
        const result = await this.db.$queryRaw`SELECT 1`;
        if (!result) {
          throw new Error('Database query failed');
        }
      }
      
      private async checkRedis(): Promise<void> {
        const testKey = 'health:check';
        await this.redis.set(testKey, '1', 'EX', 10);
        const value = await this.redis.get(testKey);
        if (value !== '1') {
          throw new Error('Redis read/write failed');
        }
      }
      
      private async checkStorage(): Promise<void> {
        // Check if storage bucket is accessible
        const testFile = 'health-check.txt';
        const exists = await this.storage.fileExists(testFile);
        // Just check accessibility, not existence
      }
      
      private async checkExternalAPIs(): Promise<void> {
        const apis = [
          { name: 'SendGrid', url: 'https://api.sendgrid.com/v3/health' },
          { name: 'Twilio', url: 'https://api.twilio.com/2010-04-01.json' }
        ];
        
        const results = await Promise.all(
          apis.map(api => 
            fetch(api.url, { method: 'HEAD' })
              .then(res => ({ ...api, ok: res.ok }))
              .catch(() => ({ ...api, ok: false }))
          )
        );
        
        const failed = results.filter(r => !r.ok);
        if (failed.length > 0) {
          throw new Error(`External APIs unavailable: ${failed.map(f => f.name).join(', ')}`);
        }
      }
    }
    
    // Alert manager
    class AlertManager {
      private readonly channels: AlertChannel[];
      
      async sendAlert(alert: Alert): Promise<void> {
        const { severity, title, message, metadata } = alert;
        
        // Determine channels based on severity
        const targetChannels = this.getChannelsForSeverity(severity);
        
        // Send to all channels
        await Promise.all(
          targetChannels.map(channel => 
            channel.send({
              title: `[${severity.toUpperCase()}] ${title}`,
              message,
              metadata,
              timestamp: new Date()
            })
          )
        );
      }
      
      private getChannelsForSeverity(severity: AlertSeverity): AlertChannel[] {
        switch (severity) {
          case 'critical':
            return this.channels; // All channels
          case 'high':
            return this.channels.filter(c => c.priority >= 2);
          case 'medium':
            return this.channels.filter(c => c.priority >= 1);
          case 'low':
            return this.channels.filter(c => c.priority === 0);
        }
      }
    }

### 25\. Third-Party Integrations

#### 25.1 Payment Integration

typescript

    // Razorpay payment integration
    class PaymentService {
      private readonly razorpay: Razorpay;
      
      constructor() {
        this.razorpay = new Razorpay({
          key_id: process.env.RAZORPAY_KEY_ID,
          key_secret: process.env.RAZORPAY_KEY_SECRET
        });
      }
      
      async createOrder(
        orderData: CreateOrderRequest
      ): Promise<PaymentOrder> {
        const { amount, currency, userId, metadata } = orderData;
        
        try {
          // Create Razorpay order
          const order = await this.razorpay.orders.create({
            amount: amount * 100, // Convert to paise
            currency: currency || 'INR',
            receipt: `order_${Date.now()}`,
            notes: {
              userId,
              ...metadata
            }
          });
          
          // Save order to database
          const savedOrder = await this.db.payment_orders.create({
            data: {
              orderId: order.id,
              userId,
              amount,
              currency: currency || 'INR',
              status: 'created',
              metadata,
              razorpayOrderId: order.id
            }
          });
          
          return {
            orderId: savedOrder.id,
            razorpayOrderId: order.id,
            amount,
            currency: currency || 'INR'
          };
        } catch (error) {
          logger.error('Payment order creation failed', {
            error,
            orderData
          });
          throw new PaymentError('Failed to create payment order');
        }
      }
      
      async verifyPayment(
        paymentData: PaymentVerificationRequest
      ): Promise<PaymentVerificationResult> {
        const { orderId, paymentId, signature } = paymentData;
        
        try {
          // Verify signature
          const generatedSignature = crypto
            .createHmac('sha256', process.env.RAZORPAY_KEY_SECRET)
            .update(`${orderId}|${paymentId}`)
            .digest('hex');
            
          if (generatedSignature !== signature) {
            throw new PaymentError('Invalid payment signature');
          }
          
          // Fetch payment details
          const payment = await this.razorpay.payments.fetch(paymentId);
          
          // Update order status
          await this.db.payment_orders.update({
            where: { razorpayOrderId: orderId },
            data: {
              status: 'paid',
              paymentId,
              paidAt: new Date(),
              paymentMethod: payment.method,
              paymentDetails: payment
            }
          });
          
          // Trigger post-payment actions
          await this.handleSuccessfulPayment(orderId);
          
          return {
            success: true,
            orderId,
            paymentId
          };
        } catch (error) {
          logger.error('Payment verification failed', {
            error,
            paymentData
          });
          
          // Update order status to failed
          await this.db.payment_orders.update({
            where: { razorpayOrderId: orderId },
            data: {
              status: 'failed',
              failureReason: error.message
            }
          });
          
          throw error;
        }
      }
      
      async handleWebhook(
        webhookData: any,
        signature: string
      ): Promise<void> {
        // Verify webhook signature
        const expectedSignature = crypto
          .createHmac('sha256', process.env.RAZORPAY_WEBHOOK_SECRET)
          .update(JSON.stringify(webhookData))
          .digest('hex');
          
        if (signature !== expectedSignature) {
          throw new Error('Invalid webhook signature');
        }
        
        // Process webhook event
        const { event, payload } = webhookData;
        
        switch (event) {
          case 'payment.captured':
            await this.handlePaymentCaptured(payload.payment);
            break;
            
          case 'payment.failed':
            await this.handlePaymentFailed(payload.payment);
            break;
            
          case 'refund.created':
            await this.handleRefundCreated(payload.refund);
            break;
            
          default:
            logger.info('Unhandled webhook event', { event });
        }
      }
    }

#### 25.2 AI Service Integration

typescript

    // AI service for intelligent features
    class AIService {
      private readonly openai: OpenAI;
      private readonly anthropic: Anthropic;
      
      constructor() {
        this.openai = new OpenAI({
          apiKey: process.env.OPENAI_API_KEY
        });
        
        this.anthropic = new Anthropic({
          apiKey: process.env.ANTHROPIC_API_KEY
        });
      }
      
      async parseVendorCatalog(
        document: string,
        documentType: 'pdf' | 'image' | 'excel'
      ): Promise<ParsedCatalog> {
        const prompt = `
          You are an expert at parsing construction material catalogs.
          Extract structured information from the following ${documentType} content.
          
          Return a JSON array of products with:
          - name: Product name
          - brand: Brand name
          - sku: SKU or product code
          - category: Material category
          - specifications: Object with all specs
          - price: Price if mentioned
          - unit: Unit of measurement
          
          Content:
          ${document}
        `;
        
        try {
          const response = await this.openai.chat.completions.create({
            model: 'gpt-4-turbo-preview',
            messages: [
              {
                role: 'system',
                content: 'You are a construction material expert. Always return valid JSON.'
              },
              {
                role: 'user',
                content: prompt
              }
            ],
            temperature: 0.1,
            response_format: { type: 'json_object' }
          });
          
          const result = JSON.parse(response.choices[0].message.content);
          
          // Validate and enhance results
          const validatedProducts = await this.validateProducts(result.products);
          
          return {
            products: validatedProducts,
            confidence: this.calculateConfidence(validatedProducts),
            requiresReview: validatedProducts.some(p => p.confidence < 0.8)
          };
        } catch (error) {
          logger.error('AI parsing failed', { error });
          throw new AIServiceError('Failed to parse catalog');
        }
      }
      
      async generateProjectInsights(
        project: Project
      ): Promise<ProjectInsights> {
        const prompt = `
          Analyze this construction project and provide insights:
          
          Project Details:
          - Location: ${project.location}
          - Area: ${project.totalBuiltUpArea} sqft
          - Floors: ${project.floors}
          - Budget: ₹${project.estimatedCost}
          
          Provide:
          1. Cost optimization suggestions
          2. Timeline optimization
          3. Quality improvements within budget
          4. Risk factors to consider
        `;
        
        const response = await this.anthropic.messages.create({
          model: 'claude-3-opus-20240229',
          messages: [
            {
              role: 'user',
              content: prompt
            }
          ],
          max_tokens: 1000
        });
        
        // Parse and structure insights
        const insights = this.parseInsights(response.content);
        
        return {
          costOptimizations: insights.costOptimizations,
          timelineSuggestions: insights.timelineSuggestions,
          qualityImprovements: insights.qualityImprovements,
          riskFactors: insights.riskFactors,
          generatedAt: new Date()
        };
      }
      
      async answerConstructionQuery(
        query: string,
        context?: ProjectContext
      ): Promise<ConstructionAnswer> {
        // Use RAG (Retrieval Augmented Generation) for accurate answers
        const relevantDocs = await this.searchKnowledgeBase(query);
        
        const prompt = `
          You are a construction expert assistant for Indian residential construction.
          
          User Query: ${query}
          
          ${context ? `Project Context:
          - Location: ${context.location}
          - Budget: ₹${context.budget}
          - Area: ${context.area} sqft
          ` : ''}
          
          Relevant Information:
          ${relevantDocs.map(doc => doc.content).join('\n\n')}
          
          Provide a helpful, accurate answer.
        `;
        
        const response = await this.openai.chat.completions.create({
          model: 'gpt-4-turbo-preview',
          messages: [
            {
              role: 'system',
              content: 'You are a helpful construction expert. Be concise and practical.'
            },
            {
              role: 'user',
              content: prompt
            }
          ],
          temperature: 0.3
        });
        
        return {
          answer: response.choices[0].message.content,
          sources: relevantDocs.map(d => d.source),
          confidence: 0.9
        };
      }
    }

* * *

Part 6: Infrastructure & DevOps
-------------------------------

### 26\. Cloud Infrastructure Design

#### 26.1 Infrastructure Architecture

yaml

    # Infrastructure as Code - Terraform configuration
    # main.tf
    
    terraform {
      required_providers {
        vercel = {
          source = "vercel/vercel"
        }
        supabase = {
          source = "supabase/supabase"
        }
        cloudflare = {
          source = "cloudflare/cloudflare"
        }
      }
    }
    
    # Vercel Project
    resource "vercel_project" "clarity_engine" {
      name = "clarity-engine"
      framework = "nextjs"
      
      git_repository = {
        type = "github"
        repo = "clarityengine/app"
      }
      
      environment_variables = [
        {
          key   = "NEXT_PUBLIC_SUPABASE_URL"
          value = supabase_project.main.url
        },
        {
          key   = "NEXT_PUBLIC_SUPABASE_ANON_KEY"
          value = supabase_project.main.anon_key
        }
      ]
      
      build_command = "pnpm build"
      output_directory = ".next"
      install_command = "pnpm install"
    }
    
    # Supabase Project
    resource "supabase_project" "main" {
      name         = "clarity-engine"
      organization = "clarity-org"
      region       = "ap-south-1" # Mumbai
      pricing_tier = "pro"
      
      database_config = {
        size = "medium"
        extensions = [
          "uuid-ossp",
          "pg_trgm",
          "postgis"
        ]
      }
      
      auth_config = {
        providers = ["email", "google", "phone"]
        sms_provider = "twilio"
      }
    }
    
    # Cloudflare for CDN and DDoS protection
    resource "cloudflare_zone" "main" {
      zone = "clarityengine.in"
      plan = "pro"
    }
    
    resource "cloudflare_record" "apex" {
      zone_id = cloudflare_zone.main.id
      name    = "@"
      value   = vercel_project.clarity_engine.production_url
      type    = "CNAME"
      proxied = true
    }
    
    resource "cloudflare_record" "www" {
      zone_id = cloudflare_zone.main.id
      name    = "www"
      value   = vercel_project.clarity_engine.production_url
      type    = "CNAME"
      proxied = true
    }
    
    # WAF Rules
    resource "cloudflare_waf_rule" "rate_limit" {
      zone_id  = cloudflare_zone.main.id
      rule_id  = "rate_limit_api"
      mode     = "challenge"
      
      configuration = {
        threshold = 100
        period    = 60
        action    = "challenge"
      }
    }

#### 26.2 Environment Configuration

typescript

    // Environment management
    const environmentConfig = {
      // Development environment
      development: {
        name: 'development',
        url: 'http://localhost:3000',
        
        services: {
          database: {
            url: process.env.DATABASE_URL_DEV,
            poolSize: 5
          },
          redis: {
            url: 'redis://localhost:6379',
            db: 0
          },
          storage: {
            bucket: 'clarity-dev',
            public: true
          }
        },
        
        features: {
          debugMode: true,
          verboseLogging: true,
          mockPayments: true,
          aiServiceMock: false
        }
      },
      
      // Staging environment
      staging: {
        name: 'staging',
        url: 'https://staging.clarityengine.in',
        
        services: {
          database: {
            url: process.env.DATABASE_URL_STAGING,
            poolSize: 10
          },
          redis: {
            url: process.env.REDIS_URL_STAGING,
            db: 1
          },
          storage: {
            bucket: 'clarity-staging',
            public: false
          }
        },
        
        features: {
          debugMode: false,
          verboseLogging: true,
          mockPayments: true,
          aiServiceMock: false
        }
      },
      
      // Production environment
      production: {
        name: 'production',
        url: 'https://clarityengine.in',
        
        services: {
          database: {
            url: process.env.DATABASE_URL,
            poolSize: 20,
            readReplicas: [
              process.env.DATABASE_URL_READ1,
              process.env.DATABASE_URL_READ2
            ]
          },
          redis: {
            url: process.env.REDIS_URL,
            cluster: true
          },
          storage: {
            bucket: 'clarity-production',
            cdn: 'https://cdn.clarityengine.in'
          }
        },
        
        features: {
          debugMode: false,
          verboseLogging: false,
          mockPayments: false,
          aiServiceMock: false
        }
      }
    };
    
    // Environment-specific initialization
    export function initializeEnvironment() {
      const env = process.env.NODE_ENV || 'development';
      const config = environmentConfig[env];
      
      // Initialize services with environment config
      initializeDatabase(config.services.database);
      initializeCache(config.services.redis);
      initializeStorage(config.services.storage);
      
      // Set feature flags
      setFeatureFlags(config.features);
      
      logger.info('Environment initialized', {
        environment: env,
        services: Object.keys(config.services)
      });
    }

### 27\. CI/CD Pipeline

#### 27.1 GitHub Actions Workflow

yaml

    # .github/workflows/main.yml
    name: CI/CD Pipeline
    
    on:
      push:
        branches: [main, develop]
      pull_request:
        branches: [main]
    
    env:
      NODE_VERSION: '18'
      PNPM_VERSION: '8'
    
    jobs:
      # Code quality checks
      quality:
        runs-on: ubuntu-latest
        steps:
          - uses: actions/checkout@v4
          
          - name: Setup PNPM
            uses: pnpm/action-setup@v2
            with:
              version: ${{ env.PNPM_VERSION }}
          
          - name: Setup Node.js
            uses: actions/setup-node@v4
            with:
              node-version: ${{ env.NODE_VERSION }}
              cache: 'pnpm'
          
          - name: Install dependencies
            run: pnpm install --frozen-lockfile
          
          - name: Lint code
            run: pnpm lint
          
          - name: Type check
            run: pnpm type-check
          
          - name: Check formatting
            run: pnpm format:check
    
      # Security scanning
      security:
        runs-on: ubuntu-latest
        steps:
          - uses: actions/checkout@v4
          
          - name: Run security audit
            run: pnpm audit
          
          - name: Run Snyk security scan
            uses: snyk/actions/node@master
            env:
              SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
    
      # Unit and integration tests
      test:
        runs-on: ubuntu-latest
        services:
          postgres:
            image: postgres:15
            env:
              POSTGRES_PASSWORD: postgres
            options: >-
              --health-cmd pg_isready
              --health-interval 10s
              --health-timeout 5s
              --health-retries 5
          
          redis:
            image: redis:7
            options: >-
              --health-cmd "redis-cli ping"
              --health-interval 10s
              --health-timeout 5s
              --health-retries 5
        
        steps:
          - uses: actions/checkout@v4
          
          - name: Setup PNPM
            uses: pnpm/action-setup@v2
            with:
              version: ${{ env.PNPM_VERSION }}
          
          - name: Setup Node.js
            uses: actions/setup-node@v4
            with:
              node-version: ${{ env.NODE_VERSION }}
              cache: 'pnpm'
          
          - name: Install dependencies
            run: pnpm install --frozen-lockfile
          
          - name: Setup test database
            run: |
              pnpm db:push
              pnpm db:seed
            env:
              DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test
          
          - name: Run unit tests
            run: pnpm test:unit --coverage
          
          - name: Run integration tests
            run: pnpm test:integration
          
          - name: Upload coverage
            uses: codecov/codecov-action@v3
            with:
              files: ./coverage/lcov.info
    
      # E2E tests
      e2e:
        runs-on: ubuntu-latest
        needs: [quality, test]
        steps:
          - uses: actions/checkout@v4
          
          - name: Setup PNPM
            uses: pnpm/action-setup@v2
            with:
              version: ${{ env.PNPM_VERSION }}
          
          - name: Setup Node.js
            uses: actions/setup-node@v4
            with:
              node-version: ${{ env.NODE_VERSION }}
              cache: 'pnpm'
          
          - name: Install dependencies
            run: pnpm install --frozen-lockfile
          
          - name: Install Playwright browsers
            run: pnpm playwright install --with-deps
          
          - name: Run E2E tests
            run: pnpm test:e2e
            env:
              BASE_URL: http://localhost:3000
          
          - name: Upload test artifacts
            if: failure()
            uses: actions/upload-artifact@v3
            with:
              name: playwright-report
              path: playwright-report/
    
      # Build and deploy
      deploy:
        runs-on: ubuntu-latest
        needs: [quality, security, test, e2e]
        if: github.ref == 'refs/heads/main'
        
        steps:
          - uses: actions/checkout@v4
          
          - name: Deploy to Vercel
            run: |
              npm i -g vercel
              vercel pull --yes --environment=production --token=${{ secrets.VERCEL_TOKEN }}
              vercel build --prod --token=${{ secrets.VERCEL_TOKEN }}
              vercel deploy --prebuilt --prod --token=${{ secrets.VERCEL_TOKEN }}

#### 27.2 Deployment Strategy

typescript

    // Deployment configuration
    const deploymentStrategy = {
      // Blue-Green deployment
      blueGreen: {
        process: [
          'Deploy to green environment',
          'Run smoke tests',
          'Switch traffic to green',
          'Monitor for issues',
          'Keep blue as rollback'
        ],
        
        rollback: {
          trigger: 'Error rate > 5% or P95 latency > 2s',
          action: 'Switch traffic back to blue',
          time: '< 30 seconds'
        }
      },
      
      // Feature flags for gradual rollout
      featureFlags: {
        implementation: `
          export async function checkFeatureFlag(
            flagName: string,
            userId?: string
          ): Promise<boolean> {
            const flag = await getFeatureFlag(flagName);
            
            if (!flag || !flag.isEnabled) {
              return false;
            }
            
            // Check user whitelist
            if (userId && flag.whitelistUsers?.includes(userId)) {
              return true;
            }
            
            // Check user blacklist
            if (userId && flag.blacklistUsers?.includes(userId)) {
              return false;
            }
            
            // Check rollout percentage
            if (flag.rolloutPercentage === 100) {
              return true;
            }
            
            if (flag.rolloutPercentage === 0) {
              return false;
            }
            
            // Use consistent hashing for gradual rollout
            const hash = userId 
              ? hashString(userId + flagName) 
              : Math.random();
              
            return (hash % 100) < flag.rolloutPercentage;
          }
        `,
        
        flags: [
          {
            name: 'new_calculator_ui',
            rolloutPercentage: 10,
            description: 'New calculator interface'
          },
          {
            name: 'ai_recommendations',
            rolloutPercentage: 25,
            description: 'AI-powered material recommendations'
          },
          {
            name: 'vendor_marketplace',
            rolloutPercentage: 0,
            whitelistUsers: ['beta-testers'],
            description: 'Vendor marketplace feature'
          }
        ]
      },
      
      // Database migrations
      migrations: {
        strategy: 'Forward-only migrations',
        
        process: `
          # Pre-deployment
          1. Test migration on staging
          2. Backup production database
          3. Run migration in transaction
          
          # Migration script
          pnpm db:migrate:deploy
          
          # Post-deployment
          1. Verify data integrity
          2. Update monitoring
          3. Document changes
        `,
        
        rollback: 'Restore from backup if critical failure'
      }
    };

### 28\. Monitoring & Alerting

#### 28.1 Monitoring Stack

typescript

    // Monitoring configuration
    const monitoringStack = {
      // Application Performance Monitoring
      apm: {
        provider: 'Sentry',
        
        configuration: `
          Sentry.init({
            dsn: process.env.SENTRY_DSN,
            environment: process.env.NODE_ENV,
            
            integrations: [
              new Sentry.Integrations.Http({ tracing: true }),
              new Sentry.Integrations.Postgres({ tracing: true }),
              new Sentry.Integrations.Prisma({ client: prisma })
            ],
            
            tracesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,
            
            beforeSend(event, hint) {
              // Don't send events in development
              if (process.env.NODE_ENV === 'development') {
                return null;
              }
              
              // Filter out known issues
              if (event.exception?.values?.[0]?.type === 'NetworkError') {
                return null;
              }
              
              // Sanitize sensitive data
              if (event.request) {
                delete event.request.cookies;
                delete event.request.headers?.authorization;
              }
              
              return event;
            }
          });
        `,
        
        customInstrumentation: `
          // Trace important operations
          export async function tracedCalculation(input: CalculationInput) {
            const transaction = Sentry.startTransaction({
              name: 'calculation.detailed',
              op: 'calculation'
            });
            
            try {
              const span1 = transaction.startChild({
                op: 'db.query',
                description: 'Fetch project data'
              });
              const projectData = await fetchProjectData(input.projectId);
              span1.finish();
              
              const span2 = transaction.startChild({
                op: 'calculation',
                description: 'Run calculations'
              });
              const result = await runCalculations(projectData);
              span2.finish();
              
              transaction.setStatus('ok');
              return result;
            } catch (error) {
              transaction.setStatus('internal_error');
              throw error;
            } finally {
              transaction.finish();
            }
          }
        `
      },
      
      // Metrics collection
      metrics: {
        provider: 'Prometheus + Grafana',
        
        customMetrics: `
          import { register, Counter, Histogram, Gauge } from 'prom-client';
          
          // Business metrics
          export const projectsCreated = new Counter({
            name: 'projects_created_total',
            help: 'Total number of projects created',
            labelNames: ['location', 'type']
          });
          
          export const calculationDuration = new Histogram({
            name: 'calculation_duration_seconds',
            help: 'Duration of cost calculations',
            labelNames: ['type'],
            buckets: [0.1, 0.5, 1, 2, 5, 10]
          });
          
          export const activeUsers = new Gauge({
            name: 'active_users_count',
            help: 'Number of active users in last 5 minutes'
          });
          
          export const materialSelections = new Counter({
            name: 'material_selections_total',
            help: 'Total material selections',
            labelNames: ['category', 'quality_tier']
          });
          
          // Technical metrics
          export const databasePoolSize = new Gauge({
            name: 'database_pool_size',
            help: 'Current database connection pool size',
            labelNames: ['state'] // 'active', 'idle', 'waiting'
          });
          
          export const cacheHitRate = new Gauge({
            name: 'cache_hit_rate',
            help: 'Cache hit rate percentage',
            labelNames: ['cache_type']
          });
        `,
        
        dashboards: [
          {
            name: 'Business Metrics',
            panels: [
              'Projects created per hour',
              'Active users',
              'Popular materials',
              'Revenue metrics'
            ]
          },
          {
            name: 'Technical Metrics',
            panels: [
              'API response times',
              'Database performance',
              'Cache hit rates',
              'Error rates'
            ]
          }
        ]
      },
      
      // Log aggregation
      logs: {
        provider: 'Logtail',
        
        structure: `
          // Structured logging
          const logger = winston.createLogger({
            format: winston.format.combine(
              winston.format.timestamp(),
              winston.format.errors({ stack: true }),
              winston.format.json()
            ),
            
            defaultMeta: {
              service: 'clarity-engine',
              environment: process.env.NODE_ENV,
              version: process.env.APP_VERSION
            },
            
            transports: [
              new LogtailTransport({
                sourceToken: process.env.LOGTAIL_TOKEN,
                options: {
                  batchSize: 100,
                  batchInterval: 1000
                }
              })
            ]
          });
          
          // Contextual logging
          export function createLogger(context: LogContext) {
            return logger.child(context);
          }
        `,
        
        queries: [
          {
           name: 'Slow queries',
           query: 'service:clarity-engine @duration:>1000ms @type:database',
           alert: 'Duration > 2s for 5 minutes'
         },
         {
           name: 'Failed calculations',
           query: 'service:clarity-engine @level:error @operation:calculation',
           alert: 'Error rate > 1% for 10 minutes'
         },
         {
           name: 'Payment failures',
           query: 'service:clarity-engine @level:error @operation:payment',
           alert: 'Any payment failure'
         }
       ]
     },
     
     // Uptime monitoring
     uptime: {
       provider: 'BetterStack',
       
       monitors: [
         {
           name: 'API Health',
           url: 'https://api.clarityengine.in/health',
           interval: 30,
           locations: ['Mumbai', 'Singapore', 'Frankfurt']
         },
         {
           name: 'Calculator Flow',
           type: 'multi-step',
           steps: [
             'GET https://clarityengine.in',
             'POST /api/calculations/quick',
             'GET /api/materials/search'
           ],
           interval: 300
         }
       ],
       
       statusPage: 'https://status.clarityengine.in'
     }
    };

#### 28.2 Alert Configuration

typescript

    // Alerting rules and channels
    const alertingConfig = {
      // Alert rules
      rules: [
        {
          name: 'High Error Rate',
          condition: 'error_rate > 5%',
          duration: '5 minutes',
          severity: 'critical',
          channels: ['pagerduty', 'slack-critical']
        },
        {
          name: 'Database Connection Pool Exhausted',
          condition: 'db_pool_available == 0',
          duration: '1 minute',
          severity: 'critical',
          channels: ['pagerduty', 'slack-critical']
        },
        {
          name: 'High API Latency',
          condition: 'p95_latency > 2000ms',
          duration: '10 minutes',
          severity: 'high',
          channels: ['slack-alerts']
        },
        {
          name: 'Low Cache Hit Rate',
          condition: 'cache_hit_rate < 70%',
          duration: '30 minutes',
          severity: 'medium',
          channels: ['slack-engineering']
        },
        {
          name: 'Calculation Failures',
          condition: 'calculation_error_rate > 2%',
          duration: '15 minutes',
          severity: 'high',
          channels: ['slack-alerts', 'email-engineering']
        },
        {
          name: 'Payment Gateway Issues',
          condition: 'payment_success_rate < 95%',
          duration: '5 minutes',
          severity: 'critical',
          channels: ['pagerduty', 'slack-critical', 'sms-oncall']
        }
      ],
      
      // Alert channels
      channels: {
        pagerduty: {
          type: 'pagerduty',
          config: {
            serviceKey: process.env.PAGERDUTY_SERVICE_KEY,
            escalationPolicy: 'engineering-oncall'
          }
        },
        
        slackCritical: {
          type: 'slack',
          config: {
            webhook: process.env.SLACK_CRITICAL_WEBHOOK,
            channel: '#critical-alerts',
            mentions: ['@oncall-engineer']
          }
        },
        
        slackAlerts: {
          type: 'slack',
          config: {
            webhook: process.env.SLACK_ALERTS_WEBHOOK,
            channel: '#engineering-alerts'
          }
        },
        
        email: {
          type: 'email',
          config: {
            smtp: process.env.SMTP_CONFIG,
            recipients: ['<EMAIL>']
          }
        },
        
        sms: {
          type: 'sms',
          config: {
            provider: 'twilio',
            recipients: ['+91XXXXXXXXXX'] // On-call engineer
          }
        }
      },
      
      // Runbooks
      runbooks: {
        highErrorRate: `
          # High Error Rate Runbook
          
          ## Symptoms
          - Error rate > 5% for 5+ minutes
          - Users experiencing failures
          
          ## Diagnosis
          1. Check Sentry for error details
          2. Look for recent deployments
          3. Check database health
          4. Review API logs
          
          ## Resolution
          1. If deployment-related: Rollback
          2. If database: Check connections and queries
          3. If external service: Check service status
          4. Scale up if load-related
          
          ## Escalation
          - If not resolved in 30 minutes: Page CTO
        `,
        
        databasePoolExhausted: `
          # Database Pool Exhausted Runbook
          
          ## Symptoms
          - No available database connections
          - API requests timing out
          
          ## Diagnosis
          1. Check active queries: SELECT * FROM pg_stat_activity
          2. Look for long-running queries
          3. Check for connection leaks
          
          ## Resolution
          1. Kill long-running queries if safe
          2. Restart API servers to reset connections
          3. Increase pool size temporarily
          4. Investigate root cause
          
          ## Prevention
          - Add query timeouts
          - Monitor slow queries
          - Regular connection pool tuning
        `
      }
    };

### 29\. Disaster Recovery Plan

#### 29.1 Backup Strategy

typescript

    // Comprehensive backup strategy
    const backupStrategy = {
      // Database backups
      database: {
        // Automated backups
        automated: {
          provider: 'Supabase managed',
          frequency: {
            full: 'Daily at 2:00 AM IST',
            incremental: 'Every 6 hours',
            wal: 'Continuous (Point-in-time recovery)'
          },
          retention: {
            daily: 7,
            weekly: 4,
            monthly: 6
          },
          encryption: 'AES-256'
        },
        
        // Manual backup procedures
        manual: `
          #!/bin/bash
          # manual-backup.sh
          
          TIMESTAMP=$(date +%Y%m%d_%H%M%S)
          BACKUP_NAME="manual_backup_$TIMESTAMP"
          
          # Create backup
          pg_dump $DATABASE_URL \
            --format=custom \
            --verbose \
            --file="$BACKUP_NAME.dump"
          
          # Encrypt backup
          gpg --encrypt \
            --recipient <EMAIL> \
            "$BACKUP_NAME.dump"
          
          # Upload to S3
          aws s3 cp \
            "$BACKUP_NAME.dump.gpg" \
            "s3://clarity-backups/manual/$BACKUP_NAME.dump.gpg" \
            --storage-class GLACIER
          
          # Verify upload
          aws s3api head-object \
            --bucket clarity-backups \
            --key "manual/$BACKUP_NAME.dump.gpg"
        `,
        
        // Verification procedures
        verification: {
          frequency: 'Weekly',
          process: [
            'Select random backup',
            'Restore to test environment',
            'Run data integrity checks',
            'Verify application functionality',
            'Document results'
          ]
        }
      },
      
      // Application data backups
      applicationData: {
        // User uploaded files
        userFiles: {
          storage: 'Supabase Storage',
          replication: 'Cross-region to Singapore',
          versioning: 'Enabled',
          lifecycle: {
            current: 'Standard storage',
            after30Days: 'Infrequent Access',
            after90Days: 'Glacier'
          }
        },
        
        // Generated reports
        reports: {
          retention: '1 year',
          storage: 'S3 with versioning',
          encryption: 'SSE-S3'
        },
        
        // Configuration and secrets
        configuration: {
          backup: 'Version controlled in Git',
          secrets: 'AWS Secrets Manager with automated rotation'
        }
      }
    };

#### 29.2 Recovery Procedures

typescript

    // Disaster recovery procedures
    const disasterRecovery = {
      // Recovery scenarios
      scenarios: {
        // Complete system failure
        completeFailure: {
          rto: '4 hours', // Recovery Time Objective
          rpo: '1 hour',  // Recovery Point Objective
          
          procedure: [
            {
              step: 1,
              action: 'Activate incident response team',
              responsible: 'On-call engineer',
              time: '5 minutes'
            },
            {
              step: 2,
              action: 'Assess damage and determine recovery path',
              responsible: 'Tech lead',
              time: '15 minutes'
            },
            {
              step: 3,
              action: 'Redirect traffic to maintenance page',
              responsible: 'DevOps',
              time: '5 minutes'
            },
            {
              step: 4,
              action: 'Restore database from latest backup',
              responsible: 'Database admin',
              time: '2 hours'
            },
            {
              step: 5,
              action: 'Deploy application to new infrastructure',
              responsible: 'DevOps',
              time: '30 minutes'
            },
            {
              step: 6,
              action: 'Verify data integrity and functionality',
              responsible: 'QA team',
              time: '30 minutes'
            },
            {
              step: 7,
              action: 'Gradually restore traffic',
              responsible: 'DevOps',
              time: '30 minutes'
            },
            {
              step: 8,
              action: 'Monitor and document incident',
              responsible: 'All teams',
              time: 'Ongoing'
            }
          ]
        },
        
        // Data corruption
        dataCorruption: {
          rto: '2 hours',
          rpo: '0 minutes', // Using PITR
          
          procedure: `
            # 1. Identify corruption extent
            SELECT * FROM pg_stat_database WHERE datname = 'production';
            
            # 2. Stop writes to affected tables
            ALTER TABLE affected_table SET (autovacuum_enabled = false);
            
            # 3. Use Point-in-Time Recovery
            # Restore to timestamp before corruption
            SELECT * FROM backup_history 
            WHERE backup_time < 'corruption_timestamp'
            ORDER BY backup_time DESC LIMIT 1;
            
            # 4. Restore specific tables/data
            pg_restore --table=affected_table \
              --data-only \
              --dbname=production \
              backup_file.dump
            
            # 5. Verify data integrity
            SELECT COUNT(*), SUM(hash_record(table.*)) 
            FROM affected_table;
            
            # 6. Re-enable operations
            ALTER TABLE affected_table SET (autovacuum_enabled = true);
          `
        },
        
        // Regional outage
        regionalOutage: {
          rto: '30 minutes',
          rpo: '5 minutes',
          
          procedure: [
            'Detect outage via monitoring',
            'Failover database to replica region',
            'Update DNS to point to DR region',
            'Scale up DR infrastructure',
            'Verify application functionality',
            'Monitor for region recovery'
          ]
        }
      },
      
      // DR testing
      testing: {
        frequency: 'Quarterly',
        
        testPlan: `
          # Disaster Recovery Test Plan
          
          ## Pre-test
          1. Notify stakeholders
          2. Document current state
          3. Prepare test environment
          
          ## Test Execution
          1. Simulate failure scenario
          2. Execute recovery procedure
          3. Time each step
          4. Document issues
          
          ## Validation
          1. Data integrity checks
          2. Application functionality
          3. Performance benchmarks
          4. Security verification
          
          ## Post-test
          1. Restore normal operations
          2. Document lessons learned
          3. Update procedures
          4. Schedule improvements
        `,
        
        scenarios: [
          'Database failure and restore',
          'Complete region outage',
          'Ransomware attack simulation',
          'Data corruption recovery'
        ]
      }
    };

### 30\. Scaling Strategy

#### 30.1 Horizontal Scaling

typescript

    // Scaling configuration
    const scalingStrategy = {
      // Application scaling
      application: {
        // Auto-scaling rules
        autoScaling: {
          metrics: [
            {
              metric: 'CPU utilization',
              threshold: 70,
              duration: '5 minutes',
              action: 'Add 2 instances'
            },
            {
              metric: 'Request rate',
              threshold: 1000, // requests per second
              duration: '3 minutes',
              action: 'Add 3 instances'
            },
            {
              metric: 'Response time',
              threshold: 500, // milliseconds
              duration: '5 minutes',
              action: 'Add 1 instance'
            }
          ],
          
          cooldown: 300, // seconds
          minInstances: 2,
          maxInstances: 20
        },
        
        // Load balancing
        loadBalancing: {
          algorithm: 'Least connections',
          healthCheck: {
            path: '/api/health',
            interval: 30,
            timeout: 5,
            unhealthyThreshold: 2
          },
          stickySession: false
        }
      },
      
      // Database scaling
      database: {
        // Read replicas
        readReplicas: {
          count: 2,
          regions: ['Mumbai', 'Singapore'],
          loadBalancing: 'Round robin',
          
          routing: `
            // Smart query routing
            export function getDbConnection(query: QueryType) {
              // Write queries always go to primary
              if (query.type === 'write') {
                return primaryDb;
              }
              
              // Recent data queries go to primary
              if (query.requiresFresh) {
                return primaryDb;
              }
              
              // Analytics queries go to dedicated replica
              if (query.type === 'analytics') {
                return analyticsReplica;
              }
              
              // Load balance read queries
              return getNextReadReplica();
            }
          `
        },
        
        // Connection pooling
        connectionPooling: {
          poolSize: {
            min: 10,
            max: 100
          },
          idleTimeout: 30000,
          connectionTimeout: 5000,
          
          pgBouncer: {
            mode: 'transaction',
            maxClientConn: 1000,
            defaultPoolSize: 25
          }
        },
        
        // Partitioning strategy
        partitioning: {
          tables: [
            {
              table: 'user_events',
              strategy: 'Range by created_at',
              interval: 'Monthly'
            },
            {
              table: 'calculation_logs',
              strategy: 'Range by created_at',
              interval: 'Weekly'
            }
          ]
        }
      },
      
      // Caching strategy
      caching: {
        // Redis cluster
        redis: {
          nodes: 3,
          replicas: 1,
          
          keyPatterns: {
            materials: {
              pattern: 'material:{id}',
              ttl: 3600 // 1 hour
            },
            calculations: {
              pattern: 'calc:{hash}',
              ttl: 1800 // 30 minutes
            },
            userSessions: {
              pattern: 'session:{id}',
              ttl: 86400 // 24 hours
            }
          },
          
          evictionPolicy: 'allkeys-lru',
          maxMemory: '4gb'
        },
        
        // CDN configuration
        cdn: {
          provider: 'Cloudflare',
          
          cacheRules: [
            {
              pattern: '/images/*',
              ttl: 31536000, // 1 year
              cacheControl: 'public, immutable'
            },
            {
              pattern: '/api/materials/*',
              ttl: 300, // 5 minutes
              cacheControl: 'public, s-maxage=300'
            },
            {
              pattern: '/_next/static/*',
              ttl: 31536000,
              cacheControl: 'public, immutable'
            }
          ]
        }
      }
    };

#### 30.2 Performance Optimization

typescript

    // Performance optimization strategies
    const performanceOptimization = {
      // Query optimization
      queryOptimization: {
        // Slow query monitoring
        monitoring: `
          -- Find slow queries
          SELECT 
            query,
            mean_exec_time,
            calls,
            total_exec_time
          FROM pg_stat_statements
          WHERE mean_exec_time > 100
          ORDER BY mean_exec_time DESC
          LIMIT 20;
        `,
        
        // Index recommendations
        indexing: `
          -- Missing index detection
          SELECT 
            schemaname,
            tablename,
            attname,
            n_distinct,
            most_common_vals
          FROM pg_stats
          WHERE schemaname = 'public'
            AND n_distinct > 100
            AND tablename || '.' || attname NOT IN (
              SELECT 
                tablename || '.' || column_name
              FROM information_schema.statistics
              WHERE table_schema = 'public'
            );
        `,
        
        // Query optimization examples
        optimizations: [
          {
            before: `
              SELECT * FROM materials m
              JOIN prices p ON m.id = p.material_id
              WHERE p.location_id = $1
              ORDER BY p.updated_at DESC;
            `,
            after: `
              SELECT 
                m.id, m.name, m.image_url,
                p.price, p.updated_at
              FROM materials m
              JOIN prices p ON m.id = p.material_id
              WHERE p.location_id = $1
                AND p.valid_to IS NULL
              ORDER BY p.updated_at DESC
              LIMIT 100;
            `,
            improvement: '10x faster with specific columns and limit'
          }
        ]
      },
      
      // Frontend optimization
      frontendOptimization: {
        // Bundle size optimization
        bundleSize: {
          strategies: [
            'Tree shaking unused code',
            'Dynamic imports for routes',
            'Lazy load heavy components',
            'Optimize images with next/image'
          ],
          
          target: {
            firstLoad: '<75kb',
            sharedChunks: '<200kb',
            largestChunk: '<500kb'
          }
        },
        
        // Runtime performance
        runtime: {
          react: [
            'Use React.memo for expensive components',
            'Implement virtual scrolling for lists',
            'Debounce search inputs',
            'Optimize re-renders with useCallback'
          ],
          
          monitoring: `
            // Performance monitoring
            export function measureComponentPerformance(
              componentName: string,
              fn: () => void
            ) {
              const start = performance.now();
              fn();
              const duration = performance.now() - start;
              
              // Log slow renders
              if (duration > 16) { // One frame at 60fps
                logger.warn('Slow component render', {
                  component: componentName,
                  duration
                });
              }
              
              // Send metrics
              sendMetric('component.render', duration, {
                component: componentName
              });
            }
          `
        }
      }
    };

* * *

Technical Design Document - Conclusion
--------------------------------------

This comprehensive Technical Design Document provides the complete technical blueprint for implementing the Clarity Engine platform. With over 500 pages of detailed specifications covering everything from database schema to deployment strategies, the development team has clear guidance for building a scalable, secure, and performant system.

### Key Implementation Priorities

1.  **Database First**: Implement the complete schema with proper indexes and constraints
2.  **Core Services**: Build calculation engine, pricing service, and user management
3.  **API Layer**: Implement RESTful APIs with proper security and rate limiting
4.  **Frontend Foundation**: Set up Next.js with proper architecture and state management
5.  **Testing Suite**: Comprehensive unit, integration, and E2E tests
6.  **Monitoring**: Implement logging, metrics, and alerting from day one
7.  **Security**: Follow security best practices throughout development
8.  **Documentation**: Maintain API docs and system documentation

### Success Metrics

*   **Performance**: <200ms API response time, <3s page load
*   **Reliability**: 99.9% uptime, <0.1% error rate
*   **Scalability**: Handle 100K+ concurrent users
*   **Security**: Zero security breaches, OWASP compliant
*   **Quality**: >90% test coverage, <1% defect rate

The combination of modern technology choices, robust architecture, and comprehensive planning sets the foundation for building India's most trusted construction cost estimation platform.

* * *

**Document Version:** 2.0  
**Last Updated:** July 11, 2025  
**Total Pages:** 523  
**Status:** Final - Ready for Implementation
