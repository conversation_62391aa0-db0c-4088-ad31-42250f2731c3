/**
 * Animation utilities for Framer Motion
 * Provides standardized animations and accessibility support
 */

import { Variants, MotionProps } from 'framer-motion';

// Check if user prefers reduced motion
export const prefersReducedMotion = 
  typeof window !== 'undefined' && 
  window.matchMedia('(prefers-reduced-motion: reduce)').matches;

// Standard animation durations
export const DURATIONS = {
  fast: 0.15,
  normal: 0.3,
  slow: 0.6,
  extraSlow: 1.0,
} as const;

// Standard spring configs
export const SPRING_CONFIGS = {
  gentle: { type: 'spring' as const, damping: 25, stiffness: 400 },
  bouncy: { type: 'spring' as const, damping: 20, stiffness: 300 },
  snappy: { type: 'spring' as const, damping: 30, stiffness: 500 },
} as const;

// Common animation variants
export const fadeInUp: Variants = {
  initial: { 
    opacity: 0, 
    y: prefersReducedMotion ? 0 : 20 
  },
  animate: { 
    opacity: 1, 
    y: 0,
    transition: prefersReducedMotion 
      ? { duration: 0 } 
      : { duration: DURATIONS.normal, ...SPRING_CONFIGS.gentle }
  },
  exit: { 
    opacity: 0, 
    y: prefersReducedMotion ? 0 : -10,
    transition: { duration: prefersReducedMotion ? 0 : DURATIONS.fast }
  }
};

export const fadeIn: Variants = {
  initial: { opacity: 0 },
  animate: { 
    opacity: 1,
    transition: { duration: prefersReducedMotion ? 0 : DURATIONS.normal }
  },
  exit: { 
    opacity: 0,
    transition: { duration: prefersReducedMotion ? 0 : DURATIONS.fast }
  }
};

export const slideInLeft: Variants = {
  initial: { 
    opacity: 0, 
    x: prefersReducedMotion ? 0 : -30 
  },
  animate: { 
    opacity: 1, 
    x: 0,
    transition: prefersReducedMotion 
      ? { duration: 0 } 
      : { duration: DURATIONS.normal, ...SPRING_CONFIGS.gentle }
  },
  exit: { 
    opacity: 0, 
    x: prefersReducedMotion ? 0 : 30,
    transition: { duration: prefersReducedMotion ? 0 : DURATIONS.fast }
  }
};

export const slideInRight: Variants = {
  initial: { 
    opacity: 0, 
    x: prefersReducedMotion ? 0 : 30 
  },
  animate: { 
    opacity: 1, 
    x: 0,
    transition: prefersReducedMotion 
      ? { duration: 0 } 
      : { duration: DURATIONS.normal, ...SPRING_CONFIGS.gentle }
  },
  exit: { 
    opacity: 0, 
    x: prefersReducedMotion ? 0 : -30,
    transition: { duration: prefersReducedMotion ? 0 : DURATIONS.fast }
  }
};

export const scaleIn: Variants = {
  initial: { 
    opacity: 0, 
    scale: prefersReducedMotion ? 1 : 0.8 
  },
  animate: { 
    opacity: 1, 
    scale: 1,
    transition: prefersReducedMotion 
      ? { duration: 0 } 
      : { duration: DURATIONS.normal, ...SPRING_CONFIGS.bouncy }
  },
  exit: { 
    opacity: 0, 
    scale: prefersReducedMotion ? 1 : 0.9,
    transition: { duration: prefersReducedMotion ? 0 : DURATIONS.fast }
  }
};

// Number count-up animation
export const countUpVariants: Variants = {
  initial: { opacity: 0, scale: 0.5 },
  animate: { 
    opacity: 1, 
    scale: 1,
    transition: prefersReducedMotion 
      ? { duration: 0 } 
      : { duration: DURATIONS.slow, ...SPRING_CONFIGS.bouncy }
  }
};

// Stagger children animation
export const staggerContainer: Variants = {
  initial: {},
  animate: {
    transition: {
      staggerChildren: prefersReducedMotion ? 0 : 0.1,
      delayChildren: prefersReducedMotion ? 0 : 0.2
    }
  }
};

export const staggerItem: Variants = {
  initial: { 
    opacity: 0, 
    y: prefersReducedMotion ? 0 : 20 
  },
  animate: { 
    opacity: 1, 
    y: 0,
    transition: prefersReducedMotion 
      ? { duration: 0 } 
      : { duration: DURATIONS.normal }
  }
};

// Progress bar animation
export const progressBar: Variants = {
  initial: { width: 0 },
  animate: (percentage: number) => ({
    width: `${percentage}%`,
    transition: prefersReducedMotion 
      ? { duration: 0 } 
      : { duration: DURATIONS.extraSlow, ease: 'easeOut', delay: 0.5 }
  })
};

// Button hover effects
export const buttonHover = {
  scale: prefersReducedMotion ? 1 : 1.02,
  transition: { duration: DURATIONS.fast }
};

export const buttonTap = {
  scale: prefersReducedMotion ? 1 : 0.98,
  transition: { duration: DURATIONS.fast }
};

// Card hover effects
export const cardHover = {
  y: prefersReducedMotion ? 0 : -4,
  boxShadow: prefersReducedMotion 
    ? undefined 
    : '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
  transition: { duration: DURATIONS.normal }
};

// Loading spinner
export const spinnerRotate = {
  animate: {
    rotate: 360,
    transition: {
      duration: 1,
      repeat: Infinity,
      ease: 'linear'
    }
  }
};

// Form focus animations
export const inputFocus = {
  scale: prefersReducedMotion ? 1 : 1.01,
  transition: { duration: DURATIONS.fast }
};

// Page transition variants
export const pageTransition: Variants = {
  initial: { 
    opacity: 0, 
    y: prefersReducedMotion ? 0 : 20 
  },
  animate: { 
    opacity: 1, 
    y: 0,
    transition: prefersReducedMotion 
      ? { duration: 0 } 
      : { duration: DURATIONS.normal, ...SPRING_CONFIGS.gentle }
  },
  exit: { 
    opacity: 0, 
    y: prefersReducedMotion ? 0 : -20,
    transition: { duration: prefersReducedMotion ? 0 : DURATIONS.fast }
  }
};

// Utility function to create custom stagger animation
export const createStaggerAnimation = (staggerDelay = 0.1, childDelay = 0) => ({
  container: {
    initial: {},
    animate: {
      transition: {
        staggerChildren: prefersReducedMotion ? 0 : staggerDelay,
        delayChildren: prefersReducedMotion ? 0 : childDelay
      }
    }
  },
  item: {
    initial: { 
      opacity: 0, 
      y: prefersReducedMotion ? 0 : 20 
    },
    animate: { 
      opacity: 1, 
      y: 0,
      transition: prefersReducedMotion 
        ? { duration: 0 } 
        : { duration: DURATIONS.normal }
    }
  }
});

// Advanced staggered animations for cost cards
export const staggeredCards: Variants = {
  initial: {},
  animate: {
    transition: {
      staggerChildren: prefersReducedMotion ? 0 : 0.15,
      delayChildren: prefersReducedMotion ? 0 : 0.1
    }
  }
};

export const cardStaggerItem: Variants = {
  initial: { 
    opacity: 0, 
    y: prefersReducedMotion ? 0 : 30,
    scale: prefersReducedMotion ? 1 : 0.95 
  },
  animate: { 
    opacity: 1, 
    y: 0,
    scale: 1,
    transition: prefersReducedMotion 
      ? { duration: 0 } 
      : { 
          duration: DURATIONS.slow,
          ease: 'easeOut',
          ...SPRING_CONFIGS.gentle
        }
  }
};

// Enhanced count-up with spring bounce effect
export const enhancedCountUp: Variants = {
  initial: { 
    opacity: 0, 
    scale: prefersReducedMotion ? 1 : 0.3,
    y: prefersReducedMotion ? 0 : 20
  },
  animate: { 
    opacity: 1, 
    scale: 1,
    y: 0,
    transition: prefersReducedMotion 
      ? { duration: 0 } 
      : { 
          duration: DURATIONS.extraSlow,
          ease: 'easeOut',
          ...SPRING_CONFIGS.bouncy
        }
  }
};

// Shimmer loading animation
export const shimmerLoading = {
  initial: { backgroundPosition: '-200px 0' },
  animate: {
    backgroundPosition: '200px 0',
    transition: {
      duration: 1.5,
      repeat: Infinity,
      ease: 'linear'
    }
  }
};

// Number ticker effect for currency values
export const numberTicker: Variants = {
  initial: { y: 20, opacity: 0 },
  animate: (i: number) => ({
    y: 0,
    opacity: 1,
    transition: {
      delay: i * 0.1,
      duration: 0.5,
      ease: 'easeOut'
    }
  })
};

// Percentage progress animation
export const percentageProgress: Variants = {
  initial: { 
    width: '0%',
    opacity: 0 
  },
  animate: (percentage: number) => ({
    width: `${percentage}%`,
    opacity: 1,
    transition: prefersReducedMotion 
      ? { duration: 0 } 
      : { 
          duration: DURATIONS.extraSlow,
          ease: 'easeOut',
          delay: 0.3
        }
  })
};

// Micro-interactions for buttons
export const buttonPulse = {
  animate: {
    scale: [1, 1.05, 1],
    transition: {
      duration: 0.6,
      repeat: Infinity,
      ease: 'easeInOut'
    }
  }
};

export const buttonRipple = {
  initial: { scale: 0, opacity: 0.5 },
  animate: { 
    scale: 1, 
    opacity: 0,
    transition: { duration: 0.5 }
  }
};

// Enhanced input focus with glow effect
export const inputFocusGlow = {
  initial: { 
    scale: 1, 
    boxShadow: '0 0 0 0px rgba(59, 130, 246, 0)' 
  },
  focus: { 
    scale: prefersReducedMotion ? 1 : 1.02,
    boxShadow: prefersReducedMotion 
      ? '0 0 0 0px rgba(59, 130, 246, 0)' 
      : '0 0 0 3px rgba(59, 130, 246, 0.1)',
    transition: { duration: DURATIONS.fast }
  }
};

// Loading spinner with bounce
export const loadingSpinner = {
  animate: {
    rotate: [0, 360],
    scale: [1, 1.1, 1],
    transition: {
      rotate: {
        duration: 1,
        repeat: Infinity,
        ease: 'linear'
      },
      scale: {
        duration: 0.5,
        repeat: Infinity,
        ease: 'easeInOut'
      }
    }
  }
};

// Notification slide-in animations
export const notificationSlide: Variants = {
  initial: { 
    x: prefersReducedMotion ? 0 : 300, 
    opacity: 0,
    scale: prefersReducedMotion ? 1 : 0.8
  },
  animate: { 
    x: 0, 
    opacity: 1,
    scale: 1,
    transition: prefersReducedMotion 
      ? { duration: 0 } 
      : { 
          duration: DURATIONS.normal,
          ...SPRING_CONFIGS.snappy
        }
  },
  exit: { 
    x: prefersReducedMotion ? 0 : 300, 
    opacity: 0,
    scale: prefersReducedMotion ? 1 : 0.8,
    transition: { duration: prefersReducedMotion ? 0 : DURATIONS.fast }
  }
};

// Success checkmark animation
export const successCheckmark = {
  initial: { 
    scale: 0, 
    opacity: 0,
    rotate: -180
  },
  animate: { 
    scale: 1, 
    opacity: 1,
    rotate: 0,
    transition: {
      duration: 0.5,
      type: 'spring',
      damping: 20,
      stiffness: 300
    }
  }
};

// Error shake animation
export const errorShake = {
  animate: {
    x: [0, -5, 5, -5, 5, 0],
    transition: {
      duration: 0.5,
      ease: 'easeInOut'
    }
  }
};

// Get motion props with accessibility
export const getMotionProps = (
  variants: Variants, 
  custom?: any, 
  delay = 0
): MotionProps => ({
  variants,
  initial: 'initial',
  animate: 'animate',
  exit: 'exit',
  custom,
  transition: prefersReducedMotion 
    ? { duration: 0 } 
    : { delay }
});

// Enhanced stagger utility with custom timings
export const createAdvancedStagger = (
  itemCount: number,
  baseDelay = 0.1,
  staggerDelay = 0.05
) => ({
  container: {
    initial: {},
    animate: {
      transition: {
        staggerChildren: prefersReducedMotion ? 0 : staggerDelay,
        delayChildren: prefersReducedMotion ? 0 : baseDelay
      }
    }
  },
  item: {
    initial: { 
      opacity: 0, 
      y: prefersReducedMotion ? 0 : 20,
      scale: prefersReducedMotion ? 1 : 0.8
    },
    animate: { 
      opacity: 1, 
      y: 0,
      scale: 1,
      transition: prefersReducedMotion 
        ? { duration: 0 } 
        : { 
            duration: DURATIONS.normal,
            ease: 'easeOut'
          }
    }
  }
});

// Utility for creating hover states
export const createHoverAnimation = (
  scale = 1.05,
  rotate = 0,
  boxShadow?: string
) => ({
  whileHover: prefersReducedMotion 
    ? {} 
    : {
        scale,
        rotate,
        boxShadow: boxShadow || '0 10px 20px rgba(0, 0, 0, 0.1)',
        transition: { duration: DURATIONS.fast }
      },
  whileTap: prefersReducedMotion 
    ? {} 
    : {
        scale: scale * 0.95,
        transition: { duration: DURATIONS.fast }
      }
});