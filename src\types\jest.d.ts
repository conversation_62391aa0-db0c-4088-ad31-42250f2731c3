import '@testing-library/jest-dom';

declare global {
  namespace jest {
    interface Matchers<R> {
      toBeInTheDocument(): R;
      toHaveClass(className: string): R;
      toHaveAttribute(attr: string, value?: any): R;
      toHaveStyle(style: string): R;
      toHaveValue(value: string | string[] | number): R;
      toBeDisabled(): R;
      toBeEnabled(): R;
      toBeVisible(): R;
      toBeChecked(): R;
      toHaveFocus(): R;
      toHaveAccessibleName(name?: string | RegExp): R;
      toHaveNoViolations(): R;
    }
  }
}