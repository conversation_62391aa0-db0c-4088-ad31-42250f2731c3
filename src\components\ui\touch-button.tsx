"use client";

import React, { useState, useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';
import hapticFeedback from '@/lib/mobile/haptic-feedback';

interface TouchButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  enableHaptic?: boolean;
  hapticPattern?: string;
  touchOptimized?: boolean;
  loading?: boolean;
  rippleEffect?: boolean;
  pressEffect?: boolean;
  longPress?: boolean;
  longPressDelay?: number;
  onLongPress?: () => void;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
}

const TouchButton = React.forwardRef<HTMLButtonElement, TouchButtonProps>(
  ({
    className,
    variant = 'primary',
    size = 'md',
    enableHaptic = true,
    hapticPattern = 'button',
    touchOptimized = true,
    loading = false,
    rippleEffect = true,
    pressEffect = true,
    longPress = false,
    longPressDelay = 500,
    onLongPress,
    leftIcon,
    rightIcon,
    children,
    onClick,
    onTouchStart,
    onTouchEnd,
    onMouseDown,
    onMouseUp,
    disabled,
    ...props
  }, ref) => {
    const [isPressed, setIsPressed] = useState(false);
    const [ripples, setRipples] = useState<Array<{ id: string; x: number; y: number }>>([]);
    const buttonRef = useRef<HTMLButtonElement>(null);
    const longPressTimer = useRef<NodeJS.Timeout | null>(null);
    const longPressTriggered = useRef(false);

    useEffect(() => {
      return () => {
        if (longPressTimer.current) {
          clearTimeout(longPressTimer.current);
        }
      };
    }, []);

    const handleTouchStart = (event: React.TouchEvent<HTMLButtonElement>) => {
      if (disabled || loading) return;
      
      setIsPressed(true);
      
      if (enableHaptic) {
        hapticFeedback.trigger(hapticPattern);
      }
      
      if (rippleEffect) {
        createRipple(event);
      }
      
      if (longPress && onLongPress) {
        longPressTriggered.current = false;
        longPressTimer.current = setTimeout(() => {
          longPressTriggered.current = true;
          if (enableHaptic) {
            hapticFeedback.longPress();
          }
          onLongPress();
        }, longPressDelay);
      }
      
      onTouchStart?.(event);
    };

    const handleTouchEnd = (event: React.TouchEvent<HTMLButtonElement>) => {
      setIsPressed(false);
      
      if (longPressTimer.current) {
        clearTimeout(longPressTimer.current);
        longPressTimer.current = null;
      }
      
      onTouchEnd?.(event);
    };

    const handleMouseDown = (event: React.MouseEvent<HTMLButtonElement>) => {
      if (disabled || loading) return;
      
      setIsPressed(true);
      
      if (enableHaptic) {
        hapticFeedback.trigger(hapticPattern);
      }
      
      if (rippleEffect) {
        createRipple(event);
      }
      
      onMouseDown?.(event);
    };

    const handleMouseUp = (event: React.MouseEvent<HTMLButtonElement>) => {
      setIsPressed(false);
      onMouseUp?.(event);
    };

    const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
      if (disabled || loading) return;
      
      // Don't trigger click if long press was triggered
      if (longPressTriggered.current) {
        longPressTriggered.current = false;
        return;
      }
      
      if (enableHaptic) {
        hapticFeedback.trigger(hapticPattern);
      }
      
      onClick?.(event);
    };

    const createRipple = (event: React.TouchEvent | React.MouseEvent) => {
      const button = buttonRef.current || (ref as React.RefObject<HTMLButtonElement>)?.current;
      if (!button) return;

      const rect = button.getBoundingClientRect();
      const isTouch = 'touches' in event;
      const clientX = isTouch ? event.touches[0]?.clientX : event.clientX;
      const clientY = isTouch ? event.touches[0]?.clientY : event.clientY;
      
      const x = clientX - rect.left;
      const y = clientY - rect.top;
      
      const ripple = {
        id: `ripple-${Date.now()}-${Math.random()}`,
        x,
        y
      };
      
      setRipples(prev => [...prev, ripple]);
      
      // Remove ripple after animation
      setTimeout(() => {
        setRipples(prev => prev.filter(r => r.id !== ripple.id));
      }, 600);
    };

    const baseClasses = cn(
      // Base styles
      'relative inline-flex items-center justify-center font-medium',
      'transition-all duration-200 ease-in-out',
      'focus:outline-none focus:ring-2 focus:ring-offset-2',
      'disabled:opacity-50 disabled:cursor-not-allowed',
      'overflow-hidden',
      
      // Touch optimizations
      touchOptimized && [
        'min-h-[44px]', // iOS minimum touch target
        'min-w-[44px]',
        'touch-manipulation', // Optimize for touch
        'select-none',
        'user-select-none',
        '-webkit-user-select-none',
        '-webkit-touch-callout-none',
      ],
      
      // Press effect
      pressEffect && isPressed && !disabled && [
        'scale-95',
        'shadow-inner'
      ],
      
      // Loading state
      loading && 'cursor-wait',
      
      // Disabled state
      disabled && 'pointer-events-none'
    );

    const variantClasses = {
      primary: cn(
        'bg-blue-600 text-white border border-blue-600',
        'hover:bg-blue-700 hover:border-blue-700',
        'focus:ring-blue-500',
        'active:bg-blue-800'
      ),
      secondary: cn(
        'bg-gray-600 text-white border border-gray-600',
        'hover:bg-gray-700 hover:border-gray-700',
        'focus:ring-gray-500',
        'active:bg-gray-800'
      ),
      outline: cn(
        'bg-transparent text-blue-600 border border-blue-600',
        'hover:bg-blue-50 hover:text-blue-700',
        'focus:ring-blue-500',
        'active:bg-blue-100'
      ),
      ghost: cn(
        'bg-transparent text-gray-700 border border-transparent',
        'hover:bg-gray-100 hover:text-gray-900',
        'focus:ring-gray-500',
        'active:bg-gray-200'
      ),
      destructive: cn(
        'bg-red-600 text-white border border-red-600',
        'hover:bg-red-700 hover:border-red-700',
        'focus:ring-red-500',
        'active:bg-red-800'
      )
    };

    const sizeClasses = {
      sm: 'px-3 py-2 text-sm rounded-md gap-1.5',
      md: 'px-4 py-2.5 text-base rounded-lg gap-2',
      lg: 'px-6 py-3 text-lg rounded-lg gap-2.5',
      xl: 'px-8 py-4 text-xl rounded-xl gap-3'
    };

    return (
      <button
        ref={ref || buttonRef}
        className={cn(
          baseClasses,
          variantClasses[variant],
          sizeClasses[size],
          className
        )}
        onClick={handleClick}
        onTouchStart={handleTouchStart}
        onTouchEnd={handleTouchEnd}
        onMouseDown={handleMouseDown}
        onMouseUp={handleMouseUp}
        disabled={disabled || loading}
        {...props}
      >
        {/* Ripple effects */}
        {rippleEffect && ripples.map(ripple => (
          <span
            key={ripple.id}
            className="absolute pointer-events-none"
            style={{
              left: ripple.x,
              top: ripple.y,
              transform: 'translate(-50%, -50%)'
            }}
          >
            <span 
              className={cn(
                'block w-0 h-0 rounded-full',
                'bg-white/30 animate-ping',
                'transition-all duration-600 ease-out'
              )}
              style={{
                animation: 'ripple 0.6s ease-out'
              }}
            />
          </span>
        ))}
        
        {/* Content */}
        <span className="relative z-10 flex items-center justify-center gap-inherit">
          {loading ? (
            <>
              <svg 
                className="animate-spin h-4 w-4" 
                viewBox="0 0 24 24"
                fill="none"
              >
                <circle 
                  cx="12" 
                  cy="12" 
                  r="10" 
                  stroke="currentColor" 
                  strokeWidth="4" 
                  className="opacity-25"
                />
                <path 
                  fill="currentColor" 
                  d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  className="opacity-75"
                />
              </svg>
              <span>Loading...</span>
            </>
          ) : (
            <>
              {leftIcon && (
                <span className="flex-shrink-0">
                  {leftIcon}
                </span>
              )}
              
              {children && (
                <span className="flex-1">
                  {children}
                </span>
              )}
              
              {rightIcon && (
                <span className="flex-shrink-0">
                  {rightIcon}
                </span>
              )}
            </>
          )}
        </span>
        
        {/* Long press indicator */}
        {longPress && isPressed && (
          <div 
            className="absolute inset-0 border-2 border-white/50 rounded-inherit"
            style={{
              animation: `longPressIndicator ${longPressDelay}ms ease-out`
            }}
          />
        )}
        
        {/* Focus ring for accessibility */}
        <div 
          className={cn(
            'absolute inset-0 rounded-inherit',
            'ring-2 ring-transparent ring-offset-2',
            'focus-visible:ring-current',
            'pointer-events-none'
          )}
          aria-hidden="true"
        />
      </button>
    );
  }
);

TouchButton.displayName = 'TouchButton';

// Add custom CSS animations
if (typeof document !== 'undefined') {
  const style = document.createElement('style');
  style.textContent = `
    @keyframes ripple {
      0% {
        width: 0;
        height: 0;
        opacity: 1;
      }
      100% {
        width: 100px;
        height: 100px;
        opacity: 0;
      }
    }
    
    @keyframes longPressIndicator {
      0% {
        clip-path: polygon(0 0, 0 0, 0 100%, 0% 100%);
      }
      100% {
        clip-path: polygon(0 0, 100% 0, 100% 100%, 0% 100%);
      }
    }
  `;
  
  if (!document.head.querySelector('style[data-touch-button]')) {
    style.setAttribute('data-touch-button', 'true');
    document.head.appendChild(style);
  }
}

export { TouchButton };
export type { TouchButtonProps };