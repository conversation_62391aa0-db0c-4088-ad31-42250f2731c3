/**
 * Memory Optimizer
 * Utilities for memory management and optimization
 */

import { useEffect, useRef, useCallback } from 'react';

// Memory monitoring interface
interface MemorySnapshot {
  timestamp: number;
  usedJSHeapSize: number;
  totalJSHeapSize: number;
  jsHeapSizeLimit: number;
  componentId?: string;
}

// Memory leak detection
interface MemoryLeakDetector {
  componentId: string;
  samples: MemorySnapshot[];
  threshold: number;
  isLeaking: boolean;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

// Memory optimization strategies
export enum OptimizationStrategy {
  LAZY_LOADING = 'lazy_loading',
  MEMOIZATION = 'memoization',
  VIRTUALIZATION = 'virtualization',
  DEBOUNCING = 'debouncing',
  GARBAGE_COLLECTION = 'garbage_collection',
  OBJECT_POOLING = 'object_pooling',
  WEAK_REFERENCES = 'weak_references',
}

// Memory budget configuration
interface MemoryBudget {
  maxHeapSize: number;
  maxComponentMemory: number;
  maxGrowthRate: number; // bytes per second
  alertThreshold: number;
  criticalThreshold: number;
}

class MemoryOptimizer {
  private memorySnapshots: MemorySnapshot[] = [];
  private componentDetectors: Map<string, MemoryLeakDetector> = new Map();
  private budget: MemoryBudget;
  private monitoring: boolean = false;
  private monitoringInterval: NodeJS.Timeout | null = null;
  private weakRefs: WeakSet<object> = new WeakSet();
  private objectPools: Map<string, object[]> = new Map();

  constructor(budget?: Partial<MemoryBudget>) {
    this.budget = {
      maxHeapSize: 256 * 1024 * 1024, // 256MB
      maxComponentMemory: 50 * 1024 * 1024, // 50MB per component
      maxGrowthRate: 1024 * 1024, // 1MB per second
      alertThreshold: 200 * 1024 * 1024, // 200MB
      criticalThreshold: 400 * 1024 * 1024, // 400MB
      ...budget,
    };
  }

  // Start memory monitoring
  startMonitoring(interval: number = 1000): void {
    if (this.monitoring) return;

    this.monitoring = true;
    this.monitoringInterval = setInterval(() => {
      this.takeSnapshot();
      this.analyzeMemoryUsage();
      this.detectMemoryLeaks();
      this.enforceMemoryBudget();
    }, interval);
  }

  // Stop memory monitoring
  stopMonitoring(): void {
    if (!this.monitoring) return;

    this.monitoring = false;
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
  }

  // Take memory snapshot
  private takeSnapshot(componentId?: string): MemorySnapshot | null {
    if (typeof window === 'undefined' || !(window as any).performance?.memory) {
      return null;
    }

    const memoryInfo = (window as any).performance.memory;
    const snapshot: MemorySnapshot = {
      timestamp: Date.now(),
      usedJSHeapSize: memoryInfo.usedJSHeapSize,
      totalJSHeapSize: memoryInfo.totalJSHeapSize,
      jsHeapSizeLimit: memoryInfo.jsHeapSizeLimit,
      componentId,
    };

    this.memorySnapshots.push(snapshot);

    // Keep only last 100 snapshots
    if (this.memorySnapshots.length > 100) {
      this.memorySnapshots.shift();
    }

    return snapshot;
  }

  // Analyze memory usage patterns
  private analyzeMemoryUsage(): void {
    if (this.memorySnapshots.length < 2) return;

    const recent = this.memorySnapshots.slice(-10);
    const growthRate = this.calculateGrowthRate(recent);

    if (growthRate > this.budget.maxGrowthRate) {
      console.warn(`High memory growth rate detected: ${this.formatBytes(growthRate)}/second`);
      this.triggerOptimization(OptimizationStrategy.GARBAGE_COLLECTION);
    }
  }

  // Detect memory leaks
  private detectMemoryLeaks(): void {
    this.componentDetectors.forEach((detector, componentId) => {
      if (detector.samples.length < 5) return;

      const growthRate = this.calculateGrowthRate(detector.samples);
      const currentMemory = detector.samples[detector.samples.length - 1].usedJSHeapSize;

      detector.isLeaking = growthRate > detector.threshold;
      
      if (detector.isLeaking) {
        if (growthRate > 10 * 1024 * 1024) detector.severity = 'critical';
        else if (growthRate > 5 * 1024 * 1024) detector.severity = 'high';
        else if (growthRate > 2 * 1024 * 1024) detector.severity = 'medium';
        else detector.severity = 'low';

        console.warn(`Memory leak detected in ${componentId}:`, {
          severity: detector.severity,
          growthRate: this.formatBytes(growthRate),
          currentMemory: this.formatBytes(currentMemory),
        });
      }
    });
  }

  // Enforce memory budget
  private enforceMemoryBudget(): void {
    if (this.memorySnapshots.length === 0) return;

    const current = this.memorySnapshots[this.memorySnapshots.length - 1];
    
    if (current.usedJSHeapSize > this.budget.criticalThreshold) {
      console.error('Critical memory usage detected!');
      this.triggerEmergencyCleanup();
    } else if (current.usedJSHeapSize > this.budget.alertThreshold) {
      console.warn('High memory usage detected');
      this.triggerOptimization(OptimizationStrategy.GARBAGE_COLLECTION);
    }
  }

  // Calculate memory growth rate
  private calculateGrowthRate(samples: MemorySnapshot[]): number {
    if (samples.length < 2) return 0;

    const start = samples[0];
    const end = samples[samples.length - 1];
    const timeDiff = (end.timestamp - start.timestamp) / 1000; // Convert to seconds
    const memoryDiff = end.usedJSHeapSize - start.usedJSHeapSize;

    return timeDiff > 0 ? memoryDiff / timeDiff : 0;
  }

  // Format bytes for display
  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // Trigger optimization strategy
  private triggerOptimization(strategy: OptimizationStrategy): void {
    switch (strategy) {
      case OptimizationStrategy.GARBAGE_COLLECTION:
        this.forceGarbageCollection();
        break;
      case OptimizationStrategy.OBJECT_POOLING:
        this.cleanupObjectPools();
        break;
      case OptimizationStrategy.WEAK_REFERENCES:
        this.cleanupWeakReferences();
        break;
      default:
        console.log(`Optimization strategy ${strategy} not implemented`);
    }
  }

  // Force garbage collection (development only)
  private forceGarbageCollection(): void {
    if (typeof window !== 'undefined' && (window as any).gc) {
      (window as any).gc();
      console.log('Garbage collection triggered');
    }
  }

  // Emergency cleanup
  private triggerEmergencyCleanup(): void {
    console.log('Triggering emergency memory cleanup...');
    
    // Clear large data structures
    this.objectPools.clear();
    this.memorySnapshots = this.memorySnapshots.slice(-10);
    
    // Force garbage collection
    this.forceGarbageCollection();
    
    // Clear component detectors
    this.componentDetectors.clear();
  }

  // Register component for monitoring
  public registerComponent(componentId: string, threshold: number = 1024 * 1024): void {
    if (!this.componentDetectors.has(componentId)) {
      this.componentDetectors.set(componentId, {
        componentId,
        samples: [],
        threshold,
        isLeaking: false,
        severity: 'low',
      });
    }
  }

  // Unregister component
  public unregisterComponent(componentId: string): void {
    this.componentDetectors.delete(componentId);
  }

  // Record component memory usage
  public recordComponentMemory(componentId: string): void {
    const detector = this.componentDetectors.get(componentId);
    if (!detector) return;

    const snapshot = this.takeSnapshot(componentId);
    if (snapshot) {
      detector.samples.push(snapshot);
      
      // Keep only last 10 samples per component
      if (detector.samples.length > 10) {
        detector.samples.shift();
      }
    }
  }

  // Object pooling utilities
  public getFromPool<T>(poolName: string, factory: () => T): T {
    const pool = this.objectPools.get(poolName) || [];
    const obj = pool.pop() as T;
    
    if (obj) {
      return obj;
    }
    
    return factory();
  }

  public returnToPool(poolName: string, obj: object): void {
    const pool = this.objectPools.get(poolName) || [];
    pool.push(obj);
    this.objectPools.set(poolName, pool);
    
    // Limit pool size
    if (pool.length > 100) {
      pool.shift();
    }
  }

  // Weak reference management
  public addWeakRef(obj: object): void {
    this.weakRefs.add(obj);
  }

  public hasWeakRef(obj: object): boolean {
    return this.weakRefs.has(obj);
  }

  private cleanupObjectPools(): void {
    this.objectPools.forEach((pool, poolName) => {
      if (pool.length > 10) {
        this.objectPools.set(poolName, pool.slice(-10));
      }
    });
  }

  private cleanupWeakReferences(): void {
    // WeakSet automatically cleans up garbage collected objects
    console.log('Weak references cleanup triggered');
  }

  // Get current memory status
  public getMemoryStatus(): {
    current: number;
    budget: number;
    usage: number;
    status: 'healthy' | 'warning' | 'critical';
    recommendations: string[];
  } {
    const current = this.memorySnapshots.length > 0 
      ? this.memorySnapshots[this.memorySnapshots.length - 1].usedJSHeapSize
      : 0;
    
    const usage = (current / this.budget.maxHeapSize) * 100;
    
    let status: 'healthy' | 'warning' | 'critical';
    const recommendations: string[] = [];
    
    if (current > this.budget.criticalThreshold) {
      status = 'critical';
      recommendations.push('Immediate action required: Clear large data structures');
      recommendations.push('Consider implementing virtualization for large lists');
      recommendations.push('Review component lifecycle for memory leaks');
    } else if (current > this.budget.alertThreshold) {
      status = 'warning';
      recommendations.push('Monitor memory usage closely');
      recommendations.push('Consider implementing lazy loading');
      recommendations.push('Review large object creation patterns');
    } else {
      status = 'healthy';
    }
    
    return {
      current,
      budget: this.budget.maxHeapSize,
      usage,
      status,
      recommendations,
    };
  }

  // Get memory leak report
  public getMemoryLeakReport(): {
    leakingComponents: string[];
    totalLeaks: number;
    severityDistribution: Record<string, number>;
  } {
    const leakingComponents: string[] = [];
    const severityDistribution: Record<string, number> = {
      low: 0,
      medium: 0,
      high: 0,
      critical: 0,
    };
    
    this.componentDetectors.forEach((detector, componentId) => {
      if (detector.isLeaking) {
        leakingComponents.push(componentId);
        severityDistribution[detector.severity]++;
      }
    });
    
    return {
      leakingComponents,
      totalLeaks: leakingComponents.length,
      severityDistribution,
    };
  }
}

// Create singleton instance
export const memoryOptimizer = new MemoryOptimizer();

// React hooks for memory optimization
export const useMemoryOptimization = (componentId: string, threshold?: number) => {
  const mountedRef = useRef(false);

  useEffect(() => {
    if (!mountedRef.current) {
      memoryOptimizer.registerComponent(componentId, threshold);
      mountedRef.current = true;
    }

    return () => {
      memoryOptimizer.unregisterComponent(componentId);
    };
  }, [componentId, threshold]);

  const recordMemory = useCallback(() => {
    memoryOptimizer.recordComponentMemory(componentId);
  }, [componentId]);

  return { recordMemory };
};

// Hook for object pooling
export const useObjectPool = <T>(poolName: string, factory: () => T) => {
  const getObject = useCallback(() => {
    return memoryOptimizer.getFromPool(poolName, factory);
  }, [poolName, factory]);

  const returnObject = useCallback((obj: T) => {
    memoryOptimizer.returnToPool(poolName, obj as object);
  }, [poolName]);

  return { getObject, returnObject };
};

// Hook for memory monitoring
export const useMemoryMonitor = (interval: number = 5000) => {
  const [memoryStatus, setMemoryStatus] = React.useState(() => 
    memoryOptimizer.getMemoryStatus()
  );

  useEffect(() => {
    const updateStatus = () => {
      setMemoryStatus(memoryOptimizer.getMemoryStatus());
    };

    const intervalId = setInterval(updateStatus, interval);
    updateStatus(); // Initial update

    return () => clearInterval(intervalId);
  }, [interval]);

  return memoryStatus;
};

// Memory optimization directive
export const optimizeMemory = (strategy: OptimizationStrategy) => {
  return (target: any, propertyKey: string, descriptor: PropertyDescriptor) => {
    const originalMethod = descriptor.value;

    descriptor.value = function (...args: any[]) {
      const start = Date.now();
      const result = originalMethod.apply(this, args);
      const duration = Date.now() - start;

      if (duration > 100) { // If method takes more than 100ms
        console.warn(`Slow method detected: ${propertyKey} took ${duration}ms`);
        // Apply optimization strategy
      }

      return result;
    };

    return descriptor;
  };
};

export default memoryOptimizer;