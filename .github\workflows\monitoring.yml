name: 🔍 Comprehensive Monitoring & Alerts

on:
  schedule:
    # Run every 5 minutes for critical monitoring
    - cron: '*/5 * * * *'
    # Run comprehensive monitoring every hour
    - cron: '0 * * * *'
    # Run daily health report at 8 AM UTC
    - cron: '0 8 * * *'
  workflow_dispatch:
    inputs:
      monitoring_type:
        description: 'Type of monitoring to run'
        required: true
        default: 'full'
        type: choice
        options:
          - uptime
          - performance
          - security
          - full
  push:
    branches: [main, master]

env:
  PRODUCTION_URL: https://nirmaan-ai.vercel.app
  STAGING_URL: https://staging-nirmaan-ai.vercel.app
  API_TIMEOUT: 30
  PERFORMANCE_THRESHOLD_MS: 3000

jobs:
  # Uptime Monitoring
  uptime-check:
    name: 🟢 Uptime Monitoring
    runs-on: ubuntu-latest
    if: github.event.inputs.monitoring_type == 'uptime' || github.event.inputs.monitoring_type == 'full' || github.event.inputs.monitoring_type == ''
    timeout-minutes: 10
    
    outputs:
      production-status: ${{ steps.health-check.outputs.production-status }}
      staging-status: ${{ steps.health-check.outputs.staging-status }}
      response-time: ${{ steps.performance-check.outputs.response-time }}
      uptime-score: ${{ steps.uptime-score.outputs.score }}
    
    steps:
      - name: 📦 Checkout code
        uses: actions/checkout@v4
        
      - name: 🔍 Comprehensive health check
        id: health-check
        run: |
          echo "🔍 Starting comprehensive health check..."
          
          # Production health check
          PROD_RESPONSE=$(curl -s -w "%{http_code}" -o /dev/null --max-time ${{ env.API_TIMEOUT }} ${{ env.PRODUCTION_URL }}/api/health)
          echo "production-response=$PROD_RESPONSE" >> $GITHUB_OUTPUT
          
          if [ "$PROD_RESPONSE" = "200" ]; then
            echo "✅ Production health check passed"
            echo "production-status=healthy" >> $GITHUB_OUTPUT
          else
            echo "❌ Production health check failed with status: $PROD_RESPONSE"
            echo "production-status=unhealthy" >> $GITHUB_OUTPUT
          fi
          
          # Staging health check (if accessible)
          STAGING_RESPONSE=$(curl -s -w "%{http_code}" -o /dev/null --max-time ${{ env.API_TIMEOUT }} ${{ env.STAGING_URL }}/api/health || echo "000")
          echo "staging-response=$STAGING_RESPONSE" >> $GITHUB_OUTPUT
          
          if [ "$STAGING_RESPONSE" = "200" ]; then
            echo "✅ Staging health check passed"
            echo "staging-status=healthy" >> $GITHUB_OUTPUT
          else
            echo "⚠️ Staging health check failed with status: $STAGING_RESPONSE"
            echo "staging-status=unhealthy" >> $GITHUB_OUTPUT
          fi
          
      - name: 🔍 Critical API endpoints check
        run: |
          echo "🔍 Testing critical API endpoints..."
          
          ENDPOINTS=(
            "${{ env.PRODUCTION_URL }}/api/health"
            "${{ env.PRODUCTION_URL }}/api/monitoring"
          )
          
          FAILED_ENDPOINTS=0
          
          for endpoint in "${ENDPOINTS[@]}"; do
            STATUS=$(curl -s -w "%{http_code}" -o /dev/null --max-time ${{ env.API_TIMEOUT }} "$endpoint")
            if [ "$STATUS" = "200" ]; then
              echo "✅ $endpoint: $STATUS"
            else
              echo "❌ $endpoint: $STATUS"
              FAILED_ENDPOINTS=$((FAILED_ENDPOINTS + 1))
            fi
          done
          
          echo "Failed endpoints: $FAILED_ENDPOINTS"
          
          if [ $FAILED_ENDPOINTS -gt 0 ]; then
            echo "⚠️ Some critical endpoints are failing"
          fi
          
      - name: 📊 Performance metrics collection
        id: performance-check
        run: |
          echo "📊 Collecting performance metrics..."
          
          # Response time measurement
          START_TIME=$(date +%s%3N)
          curl -s --max-time ${{ env.API_TIMEOUT }} ${{ env.PRODUCTION_URL }} > /dev/null
          END_TIME=$(date +%s%3N)
          RESPONSE_TIME=$((END_TIME - START_TIME))
          
          echo "response-time=$RESPONSE_TIME" >> $GITHUB_OUTPUT
          echo "Response time: ${RESPONSE_TIME}ms"
          
          # DNS resolution time
          DNS_TIME=$(dig +stats ${{ env.PRODUCTION_URL }} | grep "Query time" | awk '{print $4}')
          echo "DNS resolution time: ${DNS_TIME}ms"
          
          # SSL certificate check
          SSL_EXPIRY=$(echo | openssl s_client -servername nirmaan-ai.vercel.app -connect nirmaan-ai.vercel.app:443 2>/dev/null | openssl x509 -noout -enddate | cut -d= -f2)
          echo "SSL certificate expires: $SSL_EXPIRY"
          
          # Performance alerts
          if [ $RESPONSE_TIME -gt ${{ env.PERFORMANCE_THRESHOLD_MS }} ]; then
            echo "⚠️ High response time detected: ${RESPONSE_TIME}ms (threshold: ${{ env.PERFORMANCE_THRESHOLD_MS }}ms)"
          fi
          
      - name: 🌍 Multi-region availability check
        run: |
          echo "🌍 Testing availability from different regions..."
          
          # Simulate different geographic locations by testing various CDN endpoints
          REGIONS=("us" "eu" "asia")
          
          for region in "${REGIONS[@]}"; do
            echo "Testing from $region region..."
            # In a real scenario, you'd use different test endpoints or proxies
            STATUS=$(curl -s -w "%{http_code}" -o /dev/null --max-time 10 ${{ env.PRODUCTION_URL }})
            echo "$region region response: $STATUS"
          done
          
      - name: 📊 Calculate uptime score
        id: uptime-score
        run: |
          PROD_STATUS="${{ steps.health-check.outputs.production-status }}"
          STAGING_STATUS="${{ steps.health-check.outputs.staging-status }}"
          RESPONSE_TIME="${{ steps.performance-check.outputs.response-time }}"
          
          # Calculate uptime score (0-100)
          SCORE=0
          
          # Production health (50 points)
          [ "$PROD_STATUS" = "healthy" ] && SCORE=$((SCORE + 50))
          
          # Staging health (20 points)
          [ "$STAGING_STATUS" = "healthy" ] && SCORE=$((SCORE + 20))
          
          # Performance (30 points)
          if [ $RESPONSE_TIME -le 1000 ]; then
            SCORE=$((SCORE + 30))
          elif [ $RESPONSE_TIME -le 2000 ]; then
            SCORE=$((SCORE + 20))
          elif [ $RESPONSE_TIME -le 3000 ]; then
            SCORE=$((SCORE + 10))
          fi
          
          echo "score=$SCORE" >> $GITHUB_OUTPUT
          echo "Uptime Score: $SCORE/100"
          
      - name: 📢 Critical alert
        if: steps.health-check.outputs.production-status == 'unhealthy'
        run: |
          echo "🚨 CRITICAL ALERT: Production application is DOWN!"
          echo "Time: $(date)"
          echo "Production Status: ${{ steps.health-check.outputs.production-status }}"
          echo "Response Time: ${{ steps.performance-check.outputs.response-time }}ms"
          exit 1

  # Performance Monitoring
  performance-monitoring:
    name: ⚡ Performance Monitoring
    runs-on: ubuntu-latest
    timeout-minutes: 10
    
    steps:
      - name: 📦 Checkout code
        uses: actions/checkout@v4
        
      - name: 🔧 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: 📥 Install dependencies
        run: npm ci --prefer-offline --no-audit --progress=false
        
      - name: ⚡ Run Lighthouse audit
        run: |
          npm install -g @lhci/cli@0.12.x
          lhci autorun --config=lighthouserc.monitoring.js
        env:
          LHCI_GITHUB_APP_TOKEN: ${{ secrets.LHCI_GITHUB_APP_TOKEN }}
          
      - name: 📊 Performance metrics collection
        run: |
          # Collect Core Web Vitals
          node -e "
            const { performance } = require('perf_hooks');
            const fetch = require('node-fetch');
            
            async function measurePerformance() {
              const start = performance.now();
              await fetch('${{ env.PRODUCTION_URL }}');
              const end = performance.now();
              
              console.log('TTFB:', end - start, 'ms');
              
              // Alert thresholds
              if (end - start > 2500) {
                console.error('⚠️ High TTFB detected');
                process.exit(1);
              }
            }
            
            measurePerformance();
          "
          
      - name: 📈 Bundle size monitoring
        run: |
          # Check bundle size
          BUNDLE_SIZE=$(du -sh .next/static/chunks/*.js | awk '{sum += $1} END {print sum}')
          echo "Bundle size: $BUNDLE_SIZE"
          
          # Alert if bundle size > 1MB
          if [ "$BUNDLE_SIZE" -gt 1048576 ]; then
            echo "⚠️ Large bundle size detected: $BUNDLE_SIZE bytes"
          fi

  # Error Rate Monitoring
  error-monitoring:
    name: 🚨 Error Rate Monitoring
    runs-on: ubuntu-latest
    timeout-minutes: 5
    
    steps:
      - name: 📦 Checkout code
        uses: actions/checkout@v4
        
      - name: 🔍 Check error rates
        run: |
          # Simulate error rate checking
          ERROR_RATE=$(curl -s ${{ env.PRODUCTION_URL }}/api/monitoring | jq -r '.errorRate // 0')
          echo "Current error rate: $ERROR_RATE%"
          
          # Alert if error rate > 5%
          if (( $(echo "$ERROR_RATE > 5" | bc -l) )); then
            echo "🚨 High error rate detected: $ERROR_RATE%"
            exit 1
          fi
          
      - name: 🔍 Database health check
        run: |
          # Check database connectivity
          DB_STATUS=$(curl -s ${{ env.PRODUCTION_URL }}/api/health | jq -r '.database // "unknown"')
          echo "Database status: $DB_STATUS"
          
          if [ "$DB_STATUS" != "healthy" ]; then
            echo "🚨 Database health issue detected"
            exit 1
          fi

  # Security Monitoring
  security-monitoring:
    name: 🔒 Security Monitoring
    runs-on: ubuntu-latest
    timeout-minutes: 10
    
    steps:
      - name: 📦 Checkout code
        uses: actions/checkout@v4
        
      - name: 🔒 SSL certificate check
        run: |
          # Check SSL certificate validity
          CERT_EXPIRY=$(echo | openssl s_client -servername nirmaan-ai.vercel.app -connect nirmaan-ai.vercel.app:443 2>/dev/null | openssl x509 -noout -enddate | cut -d= -f2)
          echo "SSL Certificate expires: $CERT_EXPIRY"
          
          # Check if certificate expires in next 30 days
          EXPIRY_TIMESTAMP=$(date -d "$CERT_EXPIRY" +%s)
          CURRENT_TIMESTAMP=$(date +%s)
          DAYS_UNTIL_EXPIRY=$(( (EXPIRY_TIMESTAMP - CURRENT_TIMESTAMP) / 86400 ))
          
          if [ $DAYS_UNTIL_EXPIRY -lt 30 ]; then
            echo "⚠️ SSL certificate expires in $DAYS_UNTIL_EXPIRY days"
          fi
          
      - name: 🔍 Security headers check
        run: |
          # Check security headers
          HEADERS=$(curl -s -I ${{ env.PRODUCTION_URL }})
          
          echo "Checking security headers..."
          
          if echo "$HEADERS" | grep -q "Content-Security-Policy"; then
            echo "✅ CSP header present"
          else
            echo "⚠️ CSP header missing"
          fi
          
          if echo "$HEADERS" | grep -q "X-Frame-Options"; then
            echo "✅ X-Frame-Options header present"
          else
            echo "⚠️ X-Frame-Options header missing"
          fi
          
          if echo "$HEADERS" | grep -q "X-Content-Type-Options"; then
            echo "✅ X-Content-Type-Options header present"
          else
            echo "⚠️ X-Content-Type-Options header missing"
          fi

  # Daily Health Report
  daily-health-report:
    name: 📈 Daily Health Report
    runs-on: ubuntu-latest
    if: github.event.schedule == '0 8 * * *' || github.event.inputs.monitoring_type == 'full'
    needs: [uptime-check, performance-monitoring, error-monitoring, security-monitoring]
    timeout-minutes: 10
    
    steps:
      - name: 📊 Generate daily health report
        run: |
          echo "# 📈 Daily Health Report - $(date +%Y-%m-%d)" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "## 🎯 System Health Overview" >> $GITHUB_STEP_SUMMARY
          echo "| Component | Status | Score |" >> $GITHUB_STEP_SUMMARY
          echo "|-----------|--------|-------|" >> $GITHUB_STEP_SUMMARY
          echo "| Production Uptime | ${{ needs.uptime-check.outputs.production-status == 'healthy' && '✅ Healthy' || '❌ Issues' }} | ${{ needs.uptime-check.outputs.uptime-score || 'N/A' }}/100 |" >> $GITHUB_STEP_SUMMARY
          echo "| Performance | ${{ needs.performance-monitoring.result == 'success' && '✅ Good' || '⚠️ Issues' }} | ${{ needs.uptime-check.outputs.response-time || 'N/A' }}ms |" >> $GITHUB_STEP_SUMMARY
          echo "| Error Rate | ${{ needs.error-monitoring.result == 'success' && '✅ Low' || '⚠️ High' }} | - |" >> $GITHUB_STEP_SUMMARY
          echo "| Security | ${{ needs.security-monitoring.result == 'success' && '✅ Secure' || '⚠️ Issues' }} | - |" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "## 📊 Performance Metrics" >> $GITHUB_STEP_SUMMARY
          echo "- **Response Time**: ${{ needs.uptime-check.outputs.response-time || 'N/A' }}ms" >> $GITHUB_STEP_SUMMARY
          echo "- **Uptime Score**: ${{ needs.uptime-check.outputs.uptime-score || 'N/A' }}/100" >> $GITHUB_STEP_SUMMARY
          echo "- **Production Status**: ${{ needs.uptime-check.outputs.production-status || 'unknown' }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Staging Status**: ${{ needs.uptime-check.outputs.staging-status || 'unknown' }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "## 🔗 Quick Links" >> $GITHUB_STEP_SUMMARY
          echo "- 🌐 [Production Site](https://nirmaan-ai.vercel.app)" >> $GITHUB_STEP_SUMMARY
          echo "- 🧪 [Staging Site](https://staging-nirmaan-ai.vercel.app)" >> $GITHUB_STEP_SUMMARY
          echo "- 📊 [Monitoring Dashboard](https://github.com/${{ github.repository }}/actions/workflows/monitoring.yml)" >> $GITHUB_STEP_SUMMARY
          echo "- 🔍 [Performance Logs](https://github.com/${{ github.repository }}/actions/workflows/performance.yml)" >> $GITHUB_STEP_SUMMARY

  # Enhanced Notification System
  alert-notifications:
    name: 📢 Enhanced Alert Notifications
    runs-on: ubuntu-latest
    needs: [uptime-check, performance-monitoring, error-monitoring, security-monitoring]
    if: always()
    
    steps:
      - name: 📊 Determine alert severity
        id: alert-severity
        run: |
          UPTIME_STATUS="${{ needs.uptime-check.outputs.production-status }}"
          UPTIME_SCORE="${{ needs.uptime-check.outputs.uptime-score || 0 }}"
          RESPONSE_TIME="${{ needs.uptime-check.outputs.response-time || 0 }}"
          
          FAILED_CHECKS=0
          [ "${{ needs.uptime-check.result }}" == "failure" ] && FAILED_CHECKS=$((FAILED_CHECKS + 1))
          [ "${{ needs.performance-monitoring.result }}" == "failure" ] && FAILED_CHECKS=$((FAILED_CHECKS + 1))
          [ "${{ needs.error-monitoring.result }}" == "failure" ] && FAILED_CHECKS=$((FAILED_CHECKS + 1))
          [ "${{ needs.security-monitoring.result }}" == "failure" ] && FAILED_CHECKS=$((FAILED_CHECKS + 1))
          
          # Determine severity
          if [ "$UPTIME_STATUS" == "unhealthy" ] || [ $FAILED_CHECKS -ge 3 ]; then
            SEVERITY="critical"
          elif [ $FAILED_CHECKS -ge 2 ] || [ $UPTIME_SCORE -lt 70 ] || [ $RESPONSE_TIME -gt 5000 ]; then
            SEVERITY="high"
          elif [ $FAILED_CHECKS -ge 1 ] || [ $UPTIME_SCORE -lt 85 ] || [ $RESPONSE_TIME -gt 3000 ]; then
            SEVERITY="medium"
          else
            SEVERITY="low"
          fi
          
          echo "severity=$SEVERITY" >> $GITHUB_OUTPUT
          echo "failed-checks=$FAILED_CHECKS" >> $GITHUB_OUTPUT
          echo "Alert Severity: $SEVERITY"
          echo "Failed Checks: $FAILED_CHECKS"
          
      - name: 🚨 Critical alert (Slack)
        if: steps.alert-severity.outputs.severity == 'critical'
        uses: 8398a7/action-slack@v3
        with:
          status: failure
          channel: '#critical-alerts'
          text: |
            🚨 **CRITICAL PRODUCTION ALERT** 🚨
            
            **Service**: Nirmaan AI Construction Calculator
            **Severity**: CRITICAL
            **Time**: $(date)
            **Failed Checks**: ${{ steps.alert-severity.outputs.failed-checks }}/4
            
            **Status Details:**
            • Production: ${{ needs.uptime-check.outputs.production-status }}
            • Response Time: ${{ needs.uptime-check.outputs.response-time }}ms
            • Uptime Score: ${{ needs.uptime-check.outputs.uptime-score }}/100
            
            **Failed Components:**
            • Uptime: ${{ needs.uptime-check.result }}
            • Performance: ${{ needs.performance-monitoring.result }}
            • Error Monitoring: ${{ needs.error-monitoring.result }}
            • Security: ${{ needs.security-monitoring.result }}
            
            🔗 [View Details](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})
            
            @channel **IMMEDIATE ACTION REQUIRED**
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
        continue-on-error: true
        
      - name: ⚠️ High severity alert (Slack)
        if: steps.alert-severity.outputs.severity == 'high'
        uses: 8398a7/action-slack@v3
        with:
          status: failure
          channel: '#alerts'
          text: |
            ⚠️ **HIGH PRIORITY ALERT** ⚠️
            
            **Service**: Nirmaan AI Construction Calculator
            **Severity**: HIGH
            **Time**: $(date)
            **Failed Checks**: ${{ steps.alert-severity.outputs.failed-checks }}/4
            
            **Status**: ${{ needs.uptime-check.outputs.production-status }}
            **Response Time**: ${{ needs.uptime-check.outputs.response-time }}ms
            **Uptime Score**: ${{ needs.uptime-check.outputs.uptime-score }}/100
            
            Please investigate soon.
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
        continue-on-error: true
        
      - name: 📧 Critical email alert
        if: steps.alert-severity.outputs.severity == 'critical'
        uses: dawidd6/action-send-mail@v3
        with:
          server_address: smtp.gmail.com
          server_port: 465
          username: ${{ secrets.EMAIL_USERNAME }}
          password: ${{ secrets.EMAIL_PASSWORD }}
          subject: "🚨 CRITICAL ALERT - Nirmaan AI Production Down"
          to: ${{ secrets.ALERT_EMAIL }}
          from: "<EMAIL>"
          body: |
            CRITICAL PRODUCTION ALERT
            ========================
            
            Service: Nirmaan AI Construction Calculator
            Severity: CRITICAL
            Time: $(date)
            
            SYSTEM STATUS:
            - Production Health: ${{ needs.uptime-check.outputs.production-status }}
            - Staging Health: ${{ needs.uptime-check.outputs.staging-status }}
            - Response Time: ${{ needs.uptime-check.outputs.response-time }}ms
            - Uptime Score: ${{ needs.uptime-check.outputs.uptime-score }}/100
            - Failed Checks: ${{ steps.alert-severity.outputs.failed-checks }}/4
            
            FAILED COMPONENTS:
            - Uptime Check: ${{ needs.uptime-check.result }}
            - Performance: ${{ needs.performance-monitoring.result }}
            - Error Monitoring: ${{ needs.error-monitoring.result }}
            - Security: ${{ needs.security-monitoring.result }}
            
            IMMEDIATE ACTION REQUIRED
            
            Dashboard: https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}
            Production: https://nirmaan-ai.vercel.app
            
            This is an automated alert from GitHub Actions.
        continue-on-error: true
        
      - name: 📢 Status update (low/medium severity)
        if: steps.alert-severity.outputs.severity == 'low' || steps.alert-severity.outputs.severity == 'medium'
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ steps.alert-severity.outputs.severity == 'low' && 'success' || 'warning' }}
          channel: '#monitoring'
          text: |
            📊 **Monitoring Update**
            
            **Service**: Nirmaan AI Construction Calculator
            **Status**: ${{ steps.alert-severity.outputs.severity == 'low' && 'All Systems Operational' || 'Minor Issues Detected' }}
            **Time**: $(date)
            
            **Metrics**:
            • Response Time: ${{ needs.uptime-check.outputs.response-time }}ms
            • Uptime Score: ${{ needs.uptime-check.outputs.uptime-score }}/100
            • Production: ${{ needs.uptime-check.outputs.production-status }}
            
            ${{ steps.alert-severity.outputs.severity == 'medium' && '⚠️ *Some minor issues detected - monitoring closely*' || '✅ *All systems operating normally*' }}
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
        continue-on-error: true
        
      - name: 🔔 Discord alert (critical only)
        if: steps.alert-severity.outputs.severity == 'critical'
        uses: Ilshidur/action-discord@master
        env:
          DISCORD_WEBHOOK: ${{ secrets.DISCORD_WEBHOOK }}
        with:
          args: |
            🚨 **CRITICAL PRODUCTION ALERT** 🚨
            
            **Service:** Nirmaan AI Construction Calculator
            **Status:** CRITICAL FAILURE
            **Time:** $(date)
            **Failed Systems:** ${{ steps.alert-severity.outputs.failed-checks }}/4
            
            **Production Health:** ${{ needs.uptime-check.outputs.production-status }}
            **Response Time:** ${{ needs.uptime-check.outputs.response-time }}ms
            **Uptime Score:** ${{ needs.uptime-check.outputs.uptime-score }}/100
            
            **🔗 [View Dashboard](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})**
            
            @everyone **IMMEDIATE RESPONSE REQUIRED**
        continue-on-error: true

  # Status Page Update
  status-update:
    name: 📊 Status Page Update
    runs-on: ubuntu-latest
    needs: [uptime-check, performance-monitoring, error-monitoring, security-monitoring]
    if: always()
    
    steps:
      - name: 📊 Update status page
        run: |
          # Update status based on monitoring results
          if [ "${{ needs.uptime-check.result }}" = "success" ] && 
             [ "${{ needs.performance-monitoring.result }}" = "success" ] && 
             [ "${{ needs.error-monitoring.result }}" = "success" ] && 
             [ "${{ needs.security-monitoring.result }}" = "success" ]; then
            STATUS="operational"
            MESSAGE="All systems operational"
          else
            STATUS="degraded"
            MESSAGE="Some systems experiencing issues"
          fi
          
          echo "Status: $STATUS"
          echo "Message: $MESSAGE"
          
          # Update external status page (if available)
          # curl -X POST "https://api.statuspage.io/v1/pages/PAGE_ID/incidents" \
          #   -H "Authorization: OAuth TOKEN" \
          #   -H "Content-Type: application/json" \
          #   -d "{\"incident\": {\"name\": \"$MESSAGE\", \"status\": \"$STATUS\"}}"