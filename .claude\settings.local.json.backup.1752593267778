{"permissions": {"allow": ["Bash(claude mcp add:*)", "<PERSON><PERSON>(claude mcp:*)", "Bash(rm:*)", "Bash(npm install)", "Bash(grep:*)", "Bash(find:*)", "Bash(node:*)", "Bash(npm run build:*)", "<PERSON><PERSON>(sed:*)", "Bash(npm run lint)", "Bash(npx eslint:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(true)", "<PERSON><PERSON>(env)", "Bash(ls:*)", "Bash(npm install:*)", "Bash(npm run dev:*)", "Bash(rg:*)", "mcp__ide__getDiagnostics", "mcp__sequential-thinking__sequentialthinking", "mcp__filesystem__directory_tree", "mcp__filesystem__read_file", "<PERSON><PERSON>(timeout 5s npm run dev)", "Bash(npx tsc:*)", "mcp__filesystem__list_allowed_directories", "mcp__filesystem__list_directory", "Bash(cd:*)", "<PERSON><PERSON>(timeout 15s npm run dev)", "<PERSON><PERSON>(curl:*)", "Bash(npm run preview)", "Bash(cd:*)", "<PERSON><PERSON>(touch:*)", "mcp__memory__read_graph", "mcp__memory__create_entities", "Bash(gh auth:*)", "mcp__filesystem__read_multiple_files", "Bash(timeout 30 npm run dev)", "Bash(git add:*)", "Bash(git config:*)", "Bash(wc:*)", "mcp__filesystem__list_directory_with_sizes", "mcp__filesystem__search_files", "mcp__filesystem__get_file_info", "<PERSON><PERSON>(mkdir:*)", "Bash(npm run lint --silent)", "Bash(cd:*)", "<PERSON><PERSON>(mv:*)", "Bash(npx tsx:*)", "Bash(cp:*)", "Bash(npm run lint:*)", "mcp__filesystem__write_file", "mcp__filesystem__create_directory", "Bash(find:*)", "<PERSON><PERSON>(timeout 10 npm run dev)", "<PERSON><PERSON>(cat:*)", "Bash(ss:*)", "Bash(kill:*)", "Bash(cd \"/mnt/d/real estate/Nirmaan_AI/Construction_calculator_bolt/project-bolt-sb1-bbgbqmjq (2)/<PERSON>_code_project\")", "Bash(grep -n \"addSpecification\\|updateSpecification\\|removeSpecification\\|getCategoryImageGuidelines\" src/components/admin/ComponentForm.tsx)", "Bash(cd \"/mnt/d/real estate/Nirmaan_AI/Construction_calculator_bolt/project-bolt-sb1-bbgbqmjq (2)/<PERSON>_code_project\")", "<PERSON><PERSON>(chmod:*)", "mcp__github__get_file_contents", "Bash(bash:*)", "mcp__github__search_repositories", "WebFetch(domain:example.com)", "Bash(npm search:*)", "Bash(npm view:*)", "Bash(npx @modelcontextprotocol/server-postgres:*)", "Bash(npx:*)", "mcp__puppeteer__puppeteer_navigate", "mcp__everything__echo", "mcp__google-maps__maps_geocode", "mcp__youtube__searchVideos", "WebFetch(domain:bprnearwznzrbcpnzphq.supabase.co)", "mcp__zerodha__get_profile", "mcp__playwright__browser_snapshot", "mcp__notion__API-get-self", "<PERSON><PERSON>(sudo apt-get:*)", "Bash(sudo apt-get install:*)", "Bash(PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD=false npx playwright install --with-deps chromium)", "Bash(claude --list-tools)", "mcp__playwright__browser_navigate", "<PERSON><PERSON>(sudo npx playwright:*)", "mcp__puppeteer__puppeteer_screenshot", "mcp__puppeteer__puppeteer_evaluate", "mcp__playwright__browser_take_screenshot", "Bash(fuser:*)", "mcp__puppeteer__puppeteer_click", "mcp__playwright__browser_click", "mcp__playwright__browser_select_option", "mcp__playwright__browser_console_messages", "mcp__playwright__browser_install", "Bash(npm run preview:*)", "mcp__playwright__browser_wait_for", "mcp__playwright__browser_close", "mcp__playwright__browser_press_key", "Bash(gh repo create:*)", "Bash(git push:*)", "mcp__brave-search__brave_web_search", "mcp__google-maps__maps_geocode", "mcp__selenium__start_browser", "mcp__selenium__close_session", "mcp__youtube__searchVideos", "mcp__youtube__getVideoCategories", "<PERSON><PERSON>(env)", "mcp__filesystem__search_files", "mcp__filesystem__list_directory", "Bash(find:*)", "mcp__memory__search_nodes", "WebFetch(domain:supabase.com)", "Bash(SUPABASE_ACCESS_TOKEN=\"********************************************\" npx -y @supabase/mcp-server-supabase@latest --read-only --project-ref=fsppzopxkxdnyqleuhpl --help)", "WebFetch(domain:docs.anthropic.com)", "Bash(git commit:*)", "Bash(git branch:*)", "mcp__playwright__browser_type", "mcp__playwright__browser_resize", "mcp__playwright__browser_hover"], "deny": []}, "enabledMcpjsonServers": ["filesystem", "github", "youtube", "brave-search", "google-maps", "memory", "context7", "sequential-thinking", "puppeteer", "selenium", "playwright", "notion", "google-drive", "zerodha", "supabase", "postgres"]}