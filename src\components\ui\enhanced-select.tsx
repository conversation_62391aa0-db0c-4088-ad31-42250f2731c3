/**
 * Enhanced Select Component for MVP Design System
 * Professional select with search, multi-select, and accessibility
 */

import * as React from "react";
import * as SelectPrimitive from "@radix-ui/react-select";
import { motion, type Variants } from "framer-motion";
import { Check, ChevronDown, Search, X } from "lucide-react";
import { cn } from "@/lib/utils";

type SelectVariant = "default" | "success" | "error" | "warning";
type SelectSize = "sm" | "md" | "lg";

interface SelectOption {
  value: string;
  label: string;
  disabled?: boolean;
  description?: string;
  icon?: React.ReactNode;
}

interface SelectProps {
  variant?: SelectVariant;
  size?: SelectSize;
  label?: string;
  placeholder?: string;
  helperText?: string;
  errorMessage?: string;
  options: SelectOption[];
  value?: string;
  defaultValue?: string;
  onValueChange?: (value: string) => void;
  disabled?: boolean;
  loading?: boolean;
  searchable?: boolean;
  clearable?: boolean;
  fullWidth?: boolean;
  required?: boolean;
}

// Animation variants
const contentVariants: Variants = {
  hidden: {
    opacity: 0,
    y: -10,
    scale: 0.95,
  },
  visible: {
    opacity: 1,
    y: 0,
    scale: 1,
    transition: {
      duration: 0.2,
      ease: "easeOut",
    },
  },
};

const itemVariants: Variants = {
  hidden: { opacity: 0, x: -10 },
  visible: { opacity: 1, x: 0 },
};

// Base styles
const triggerBaseClasses = cn(
  "flex h-10 w-full items-center justify-between rounded-lg border px-3 py-2",
  "text-sm ring-offset-background transition-all duration-200",
  "focus:outline-none focus:ring-4 focus:ring-primary-500/20",
  "disabled:cursor-not-allowed disabled:opacity-50",
  "data-[placeholder]:text-secondary-400"
);

// Variant styles
const triggerVariantClasses: Record<SelectVariant, string> = {
  default: cn(
    "border-secondary-200 bg-white text-secondary-900",
    "hover:border-secondary-300 focus:border-primary-500"
  ),
  success: cn(
    "border-green-300 bg-green-50 text-green-900",
    "hover:border-green-400 focus:border-green-500 focus:ring-green-500/20"
  ),
  error: cn(
    "border-red-300 bg-red-50 text-red-900",
    "hover:border-red-400 focus:border-red-500 focus:ring-red-500/20"
  ),
  warning: cn(
    "border-yellow-300 bg-yellow-50 text-yellow-900",
    "hover:border-yellow-400 focus:border-yellow-500 focus:ring-yellow-500/20"
  ),
};

// Size styles
const triggerSizeClasses: Record<SelectSize, string> = {
  sm: "h-8 px-2 text-sm",
  md: "h-10 px-3 text-base",
  lg: "h-12 px-4 text-lg",
};

export const EnhancedSelect = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Root>,
  SelectProps
>(
  (
    {
      variant = "default",
      size = "md",
      label,
      placeholder = "Select an option...",
      helperText,
      errorMessage,
      options,
      value,
      defaultValue,
      onValueChange,
      disabled = false,
      loading = false,
      searchable = false,
      clearable = false,
      fullWidth = true,
      required = false,
    },
    ref
  ) => {
    const [searchQuery, setSearchQuery] = React.useState("");
    const [open, setOpen] = React.useState(false);
    const inputId = React.useId();
    const helperId = React.useId();
    const errorId = React.useId();

    // Filter options based on search query
    const filteredOptions = React.useMemo(() => {
      if (!searchable || !searchQuery.trim()) {
        return options;
      }
      return options.filter(
        (option) =>
          option.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
          option.description?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }, [options, searchQuery, searchable]);

    // Clear search when closing
    React.useEffect(() => {
      if (!open) {
        setSearchQuery("");
      }
    }, [open]);

    // Determine effective variant
    const effectiveVariant = errorMessage ? "error" : variant;

    // Find selected option
    const selectedOption = options.find((option) => option.value === value);

    // Clear selection
    const handleClear = (e: React.MouseEvent) => {
      e.stopPropagation();
      onValueChange?.("");
    };

    return (
      <div className={cn("space-y-2", fullWidth && "w-full")}>
        {/* Label */}
        {label && (
          <label
            htmlFor={inputId}
            className={cn(
              "block text-sm font-medium",
              effectiveVariant === "error" ? "text-red-700" : "text-secondary-700"
            )}
          >
            {label}
            {required && <span className="text-red-500 ml-1">*</span>}
          </label>
        )}

        {/* Select */}
        <SelectPrimitive.Root
          value={value}
          defaultValue={defaultValue}
          onValueChange={onValueChange}
          disabled={disabled || loading}
          open={open}
          onOpenChange={setOpen}
        >
          <SelectPrimitive.Trigger
            ref={ref}
            id={inputId}
            className={cn(
              triggerBaseClasses,
              triggerVariantClasses[effectiveVariant],
              triggerSizeClasses[size],
              fullWidth && "w-full"
            )}
            aria-describedby={cn(
              helperText && helperId,
              errorMessage && errorId
            )}
          >
            <div className="flex items-center gap-2 flex-1 min-w-0">
              {selectedOption?.icon && (
                <span className="flex-shrink-0 text-secondary-500">
                  {selectedOption.icon}
                </span>
              )}
              <SelectPrimitive.Value
                placeholder={placeholder}
                className="truncate"
              />
            </div>

            <div className="flex items-center gap-1">
              {/* Clear Button */}
              {clearable && selectedOption && !disabled && !loading && (
                <button
                  type="button"
                  onClick={handleClear}
                  className="p-1 hover:bg-secondary-100 rounded transition-colors"
                  aria-label="Clear selection"
                >
                  <X className="h-3 w-3" />
                </button>
              )}

              {/* Loading Spinner */}
              {loading ? (
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{
                    duration: 1,
                    repeat: Infinity,
                    ease: "linear",
                  }}
                  className="h-4 w-4 text-secondary-400"
                >
                  ⟳
                </motion.div>
              ) : (
                <SelectPrimitive.Icon asChild>
                  <ChevronDown className="h-4 w-4 text-secondary-400" />
                </SelectPrimitive.Icon>
              )}
            </div>
          </SelectPrimitive.Trigger>

          <SelectPrimitive.Portal>
            <SelectPrimitive.Content
              className={cn(
                "relative z-50 min-w-[8rem] overflow-hidden rounded-lg border",
                "bg-white shadow-lg data-[state=open]:animate-in",
                "data-[state=closed]:animate-out data-[state=closed]:fade-out-0",
                "data-[state=open]:fade-in-0 data-[side=bottom]:slide-in-from-top-2",
                "data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2",
                "data-[side=top]:slide-in-from-bottom-2"
              )}
              position="popper"
              sideOffset={4}
            >
              <motion.div
                variants={contentVariants}
                initial="hidden"
                animate="visible"
              >
                {/* Search Input */}
                {searchable && (
                  <div className="p-2 border-b border-secondary-200">
                    <div className="relative">
                      <Search className="absolute left-2 top-1/2 -translate-y-1/2 h-4 w-4 text-secondary-400" />
                      <input
                        type="text"
                        placeholder="Search options..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="w-full pl-8 pr-2 py-1 text-sm border-0 focus:outline-none focus:ring-0"
                      />
                    </div>
                  </div>
                )}

                <SelectPrimitive.Viewport className="p-1 max-h-60 overflow-y-auto">
                  {filteredOptions.length === 0 ? (
                    <div className="py-6 text-center text-sm text-secondary-500">
                      {searchQuery ? "No options found." : "No options available."}
                    </div>
                  ) : (
                    filteredOptions.map((option, index) => (
                      <SelectPrimitive.Item
                        key={option.value}
                        className={cn(
                          "relative flex cursor-default select-none items-center rounded-md",
                          "px-2 py-2 text-sm outline-none transition-colors",
                          "focus:bg-primary-100 focus:text-primary-900",
                          "data-[disabled]:pointer-events-none data-[disabled]:opacity-50"
                        )}
                        value={option.value}
                        disabled={option.disabled}
                      >
                        <motion.div
                          className="flex items-center gap-2 flex-1 min-w-0"
                          variants={itemVariants}
                          initial="hidden"
                          animate="visible"
                          transition={{ delay: index * 0.05 }}
                        >
                          {option.icon && (
                            <span className="flex-shrink-0 text-secondary-500">
                              {option.icon}
                            </span>
                          )}
                          <div className="flex-1 min-w-0">
                            <SelectPrimitive.ItemText className="truncate">
                              {option.label}
                            </SelectPrimitive.ItemText>
                            {option.description && (
                              <div className="text-xs text-secondary-500 truncate">
                                {option.description}
                              </div>
                            )}
                          </div>
                          <SelectPrimitive.ItemIndicator className="flex h-3.5 w-3.5 items-center justify-center">
                            <Check className="h-4 w-4" />
                          </SelectPrimitive.ItemIndicator>
                        </motion.div>
                      </SelectPrimitive.Item>
                    ))
                  )}
                </SelectPrimitive.Viewport>
              </motion.div>
            </SelectPrimitive.Content>
          </SelectPrimitive.Portal>
        </SelectPrimitive.Root>

        {/* Helper Text */}
        {helperText && !errorMessage && (
          <p id={helperId} className="text-sm text-secondary-500">
            {helperText}
          </p>
        )}

        {/* Error Message */}
        {errorMessage && (
          <motion.p
            id={errorId}
            className="text-sm text-red-600"
            initial={{ opacity: 0, y: -5 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.15 }}
          >
            {errorMessage}
          </motion.p>
        )}
      </div>
    );
  }
);

EnhancedSelect.displayName = "EnhancedSelect";

// Export types
export type { SelectProps, SelectOption, SelectVariant, SelectSize };