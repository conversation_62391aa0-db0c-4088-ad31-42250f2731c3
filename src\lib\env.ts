/**
 * Environment Variables Validation and Configuration
 * Ensures all required environment variables are present and valid
 */

import { z } from 'zod';

// Environment schema validation
const envSchema = z.object({
  // App Configuration
  NODE_ENV: z.enum(['development', 'staging', 'production']).default('development'),
  NEXT_PUBLIC_APP_URL: z.string().url().optional(),
  
  // Supabase Configuration
  NEXT_PUBLIC_SUPABASE_URL: z.string().url().optional(),
  NEXT_PUBLIC_SUPABASE_ANON_KEY: z.string().optional(),
  SUPABASE_SERVICE_ROLE_KEY: z.string().optional(),
  
  // Analytics
  NEXT_PUBLIC_GA_MEASUREMENT_ID: z.string().optional(),
  NEXT_PUBLIC_POSTHOG_KEY: z.string().optional(),
  NEXT_PUBLIC_POSTHOG_HOST: z.string().url().optional(),
  
  // Error Tracking
  SENTRY_DSN: z.string().url().optional(),
  
  // Rate Limiting
  RATE_LIMIT_MAX: z.string().regex(/^\d+$/).transform(Number).optional().default(100),
  RATE_LIMIT_WINDOW: z.string().regex(/^\d+$/).transform(Number).optional().default(60000),
  
  // Feature Flags
  NEXT_PUBLIC_ENABLE_ANALYTICS: z.string().optional().transform(val => val === 'true').default(false),
  NEXT_PUBLIC_ENABLE_ERROR_TRACKING: z.string().optional().transform(val => val === 'true').default(false),
  NEXT_PUBLIC_ENABLE_PERFORMANCE_MONITORING: z.string().optional().transform(val => val === 'true').default(true),
  NEXT_PUBLIC_ENABLE_WEB_VITALS: z.string().optional().transform(val => val === 'true').default(true),
  
  // Development
  ANALYZE: z.string().optional().transform(val => val === 'true').default(false),
});

type EnvConfig = z.infer<typeof envSchema>;

let envConfig: EnvConfig;

try {
  envConfig = envSchema.parse({
    NODE_ENV: process.env.NODE_ENV,
    NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL,
    NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL,
    NEXT_PUBLIC_SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
    SUPABASE_SERVICE_ROLE_KEY: process.env.SUPABASE_SERVICE_ROLE_KEY,
    NEXT_PUBLIC_GA_MEASUREMENT_ID: process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID,
    NEXT_PUBLIC_POSTHOG_KEY: process.env.NEXT_PUBLIC_POSTHOG_KEY,
    NEXT_PUBLIC_POSTHOG_HOST: process.env.NEXT_PUBLIC_POSTHOG_HOST,
    SENTRY_DSN: process.env.SENTRY_DSN,
    RATE_LIMIT_MAX: process.env.RATE_LIMIT_MAX,
    RATE_LIMIT_WINDOW: process.env.RATE_LIMIT_WINDOW,
    NEXT_PUBLIC_ENABLE_ANALYTICS: process.env.NEXT_PUBLIC_ENABLE_ANALYTICS,
    NEXT_PUBLIC_ENABLE_ERROR_TRACKING: process.env.NEXT_PUBLIC_ENABLE_ERROR_TRACKING,
    NEXT_PUBLIC_ENABLE_PERFORMANCE_MONITORING: process.env.NEXT_PUBLIC_ENABLE_PERFORMANCE_MONITORING,
    NEXT_PUBLIC_ENABLE_WEB_VITALS: process.env.NEXT_PUBLIC_ENABLE_WEB_VITALS,
    ANALYZE: process.env.ANALYZE,
  });
} catch (error) {
  console.error('❌ Invalid environment variables:', error);
  throw new Error('Environment validation failed. Please check your .env.local file.');
}

// Export validated environment configuration
export const env = envConfig;

// Helper functions for environment checks
export const isDevelopment = env.NODE_ENV === 'development';
export const isProduction = env.NODE_ENV === 'production';
export const isStaging = env.NODE_ENV === 'staging';

// Feature flag helpers
export const isAnalyticsEnabled = env.NEXT_PUBLIC_ENABLE_ANALYTICS;
export const isErrorTrackingEnabled = env.NEXT_PUBLIC_ENABLE_ERROR_TRACKING;
export const isPerformanceMonitoringEnabled = env.NEXT_PUBLIC_ENABLE_PERFORMANCE_MONITORING;
export const isWebVitalsEnabled = env.NEXT_PUBLIC_ENABLE_WEB_VITALS;

// Configuration objects
export const analyticsConfig = {
  enabled: isAnalyticsEnabled,
  gaId: env.NEXT_PUBLIC_GA_MEASUREMENT_ID,
  posthogKey: env.NEXT_PUBLIC_POSTHOG_KEY,
  posthogHost: env.NEXT_PUBLIC_POSTHOG_HOST,
};

export const rateLimitConfig = {
  max: env.RATE_LIMIT_MAX,
  window: env.RATE_LIMIT_WINDOW,
};

export const appConfig = {
  name: 'Clarity Engine',
  version: process.env.npm_package_version || '0.1.0',
  url: env.NEXT_PUBLIC_APP_URL || (isProduction ? 'https://clarityengine.app' : 'http://localhost:3000'),
  environment: env.NODE_ENV,
};

// Log environment status (only in development)
if (isDevelopment) {
  console.log('🚀 Environment Configuration Loaded:');
  console.log(`   Environment: ${env.NODE_ENV}`);
  console.log(`   Analytics: ${isAnalyticsEnabled ? '✅' : '❌'}`);
  console.log(`   Error Tracking: ${isErrorTrackingEnabled ? '✅' : '❌'}`);
  console.log(`   Performance Monitoring: ${isPerformanceMonitoringEnabled ? '✅' : '❌'}`);
  console.log(`   Rate Limit: ${env.RATE_LIMIT_MAX} requests/${env.RATE_LIMIT_WINDOW}ms`);
}