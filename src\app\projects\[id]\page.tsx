'use client';

import { motion } from 'framer-motion';
import { 
  ArrowLeftIcon, 
  CalculatorIcon, 
  LoaderIcon, 
  EditIcon,
  TrashIcon,
  ShareIcon,
  FileTextIcon,
  MapPinIcon,
  BuildingIcon,
  CalendarIcon,
  ClockIcon
} from 'lucide-react';
import Link from 'next/link';
import { useParams, useRouter } from 'next/navigation';
import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';

import { Layout } from '@/components/layout/Layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { CostBreakdownCard } from '@/components/calculator/CostBreakdownCard';
import { MaterialsList } from '@/components/calculator/MaterialsList';
import { useDeleteProject } from '@/hooks/useProjects';
import { fadeInUp, staggerContainer, staggerItem } from '@/lib/animations';
import { exportCalculationToPDF } from '@/lib/pdf';

export default function ProjectDetailPage() {
  const params = useParams();
  const router = useRouter();
  const projectId = params.id as string;
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);
  const [activeTab, setActiveTab] = useState<'overview' | 'breakdown' | 'materials'>('overview');
  const deleteProject = useDeleteProject();

  // Fetch project details
  const { data, isLoading, error } = useQuery({
    queryKey: ['project', projectId],
    queryFn: async () => {
      const response = await fetch(`/api/projects/${projectId}`, {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to fetch project');
      }

      return response.json();
    },
    enabled: !!projectId,
  });

  const project = data?.project;

  const handleDelete = async () => {
    if (project && window.confirm(`Are you sure you want to delete "${project.name}"? This action cannot be undone.`)) {
      deleteProject.mutate(projectId, {
        onSuccess: () => {
          router.push('/projects');
        },
      });
    }
  };

  const handleLoadInCalculator = () => {
    if (project?.calculation_data?.formData) {
      // Store form data in localStorage for the calculator to load
      localStorage.setItem('loadedCalculation', JSON.stringify(project.calculation_data.formData));
      router.push('/calculator?load=true');
    }
  };

  const handleDownloadPDF = async () => {
    if (!project) return;

    setIsGeneratingPDF(true);
    try {
      await exportCalculationToPDF(
        project.calculation_data.formData,
        project.calculation_data.results
      );
    } catch (error) {
      console.error('Error generating PDF:', error);
    } finally {
      setIsGeneratingPDF(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getQualityTierColor = (tier: string) => {
    switch (tier) {
      case 'smart':
        return 'bg-green-100 text-green-800';
      case 'premium':
        return 'bg-blue-100 text-blue-800';
      case 'luxury':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-[400px]">
          <LoaderIcon className="h-8 w-8 animate-spin text-blue-600" />
        </div>
      </Layout>
    );
  }

  if (error || !project) {
    return (
      <Layout>
        <div className="container mx-auto px-4 py-12">
          <motion.div {...fadeInUp} className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              Project Not Found
            </h1>
            <p className="text-gray-600 mb-6">
              The project you're looking for doesn't exist or you don't have permission to view it.
            </p>
            <Link href="/projects">
              <Button>
                <ArrowLeftIcon className="h-4 w-4 mr-2" />
                Back to Projects
              </Button>
            </Link>
          </motion.div>
        </div>
      </Layout>
    );
  }

  const calculationData = project.calculation_data;
  const formData = calculationData?.formData || {};
  const results = calculationData?.results || {};

  return (
    <Layout>
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <motion.div {...fadeInUp} className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <Link href="/projects">
              <Button variant="outline" size="sm">
                <ArrowLeftIcon className="h-4 w-4 mr-2" />
                Back to Projects
              </Button>
            </Link>
          </div>

          <div className="flex items-start justify-between">
            <div className="flex-1">
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                {project.name}
              </h1>
              
              <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600">
                <div className="flex items-center gap-1">
                  <MapPinIcon className="h-4 w-4" />
                  {project.location}
                </div>
                <div className="flex items-center gap-1">
                  <BuildingIcon className="h-4 w-4" />
                  {project.area_sqft.toLocaleString()} sq ft
                </div>
                <div className="flex items-center gap-1">
                  <CalendarIcon className="h-4 w-4" />
                  Created {formatDate(project.created_at)}
                </div>
                <span
                  className={`px-2 py-1 rounded-full text-xs font-medium capitalize ${getQualityTierColor(
                    project.quality_tier
                  )}`}
                >
                  {project.quality_tier}
                </span>
              </div>
            </div>

            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleLoadInCalculator}
              >
                <CalculatorIcon className="h-4 w-4 mr-2" />
                Load in Calculator
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={handleDownloadPDF}
                disabled={isGeneratingPDF}
              >
                {isGeneratingPDF ? (
                  <LoaderIcon className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <FileTextIcon className="h-4 w-4 mr-2" />
                )}
                Download PDF
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={handleDelete}
                disabled={deleteProject.isPending}
                className="text-red-600 hover:text-red-700 hover:bg-red-50"
              >
                {deleteProject.isPending ? (
                  <LoaderIcon className="h-4 w-4 animate-spin" />
                ) : (
                  <TrashIcon className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>
        </motion.div>

        {/* Cost Summary */}
        <motion.div {...fadeInUp} className="mb-8">
          <Card className="bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-200">
            <CardContent className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <p className="text-sm text-gray-600 mb-1">Total Cost</p>
                  <p className="text-3xl font-bold text-blue-900">
                    ₹{results.totalCost?.toLocaleString() || 0}
                  </p>
                </div>
                <div className="text-center">
                  <p className="text-sm text-gray-600 mb-1">Cost per Sq Ft</p>
                  <p className="text-2xl font-semibold text-blue-700">
                    ₹{results.costPerSqft?.toLocaleString() || 0}
                  </p>
                </div>
                <div className="text-center">
                  <p className="text-sm text-gray-600 mb-1">Timeline</p>
                  <p className="text-2xl font-semibold text-blue-700">
                    {results.timeline?.totalDuration || 0} months
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Navigation Tabs */}
        <motion.div {...fadeInUp} className="mb-8">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {[
                { key: 'overview', label: 'Overview' },
                { key: 'breakdown', label: 'Cost Breakdown' },
                { key: 'materials', label: 'Materials' },
              ].map((tab) => (
                <button
                  key={tab.key}
                  onClick={() => setActiveTab(tab.key as any)}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.key
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>
        </motion.div>

        {/* Tab Content */}
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          {activeTab === 'overview' && (
            <div className="grid gap-6 md:grid-cols-2">
              {/* Project Details */}
              <Card>
                <CardHeader>
                  <CardTitle>Project Details</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <p className="text-sm font-medium text-gray-500">Built-up Area</p>
                    <p className="text-lg">{formData.builtUpArea?.toLocaleString() || 0} sq ft</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Floors</p>
                    <p className="text-lg">{(formData.floors || 0) + 1} ({formData.floors === 0 ? 'Ground' : `G+${formData.floors}`})</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Quality Tier</p>
                    <p className="text-lg capitalize">{project.quality_tier}</p>
                  </div>
                  {formData.hasParking && (
                    <div>
                      <p className="text-sm font-medium text-gray-500">Parking</p>
                      <p className="text-lg capitalize">{formData.parkingType || 'Yes'}</p>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Construction Timeline */}
              {results.timeline?.phases && (
                <Card>
                  <CardHeader>
                    <CardTitle>Construction Timeline</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {results.timeline.phases.map((phase: any, index: number) => (
                        <div key={index} className="flex justify-between items-center py-2 border-b border-gray-100 last:border-0">
                          <span className="font-medium">{phase.name}</span>
                          <span className="text-sm text-gray-600">{phase.duration} months</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          )}

          {activeTab === 'breakdown' && results.costBreakdown && (
            <div className="grid gap-6">
              {Object.entries(results.costBreakdown).map(([category, data]: [string, any]) => (
                <CostBreakdownCard
                  key={category}
                  title={data.name || category}
                  amount={data.cost || 0}
                  percentage={data.percentage || 0}
                  subcategories={data.subcategories || {}}
                  description={data.description}
                />
              ))}
            </div>
          )}

          {activeTab === 'materials' && results.materials && (
            <MaterialsList materials={results.materials} />
          )}
        </motion.div>
      </div>
    </Layout>
  );
}