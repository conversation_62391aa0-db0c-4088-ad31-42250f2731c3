'use client';

import React, { useState } from 'react';
import { 
  EnhancedButton,
  EnhancedInput, 
  EnhancedCard,
  EnhancedSelect,
  EnhancedProgress,
  EnhancedModal,
  ConfirmModal
} from '@/components/ui/enhanced-index';

// Demo page to test all enhanced components
export default function DemoEnhancedPage() {
  const [modalOpen, setModalOpen] = useState(false);
  const [confirmOpen, setConfirmOpen] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [selectValue, setSelectValue] = useState('');
  const [progress, setProgress] = useState(75);

  const selectOptions = [
    { value: 'option1', label: 'Option 1' },
    { value: 'option2', label: 'Option 2' },
    { value: 'option3', label: 'Option 3' },
  ];

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 space-y-8">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Enhanced Components Demo
          </h1>
          <p className="text-gray-600">
            Testing all 6 enhanced MVP components with interactive examples.
          </p>
        </div>

        {/* Enhanced Cards Demo */}
        <EnhancedCard 
          variant="elevated" 
          header={<h2 className="text-xl font-semibold">Enhanced Buttons</h2>}
        >
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            <EnhancedButton variant="primary">Primary</EnhancedButton>
            <EnhancedButton variant="secondary">Secondary</EnhancedButton>
            <EnhancedButton variant="outline">Outline</EnhancedButton>
            <EnhancedButton variant="ghost">Ghost</EnhancedButton>
            <EnhancedButton variant="destructive">Destructive</EnhancedButton>
            <EnhancedButton loading>Loading</EnhancedButton>
          </div>
        </EnhancedCard>

        {/* Enhanced Inputs Demo */}
        <EnhancedCard 
          variant="outlined"
          header={<h2 className="text-xl font-semibold">Enhanced Inputs</h2>}
        >
          <div className="space-y-4">
            <EnhancedInput
              label="Text Input"
              placeholder="Enter some text..."
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
            />
            <EnhancedInput
              label="Password Input"
              type="password"
              showPasswordToggle
              placeholder="Enter password..."
            />
            <EnhancedInput
              label="Error State"
              errorMessage="This field is required"
              placeholder="Input with error..."
            />
          </div>
        </EnhancedCard>

        {/* Enhanced Select Demo */}
        <EnhancedCard
          variant="glass"
          header={<h2 className="text-xl font-semibold">Enhanced Select</h2>}
        >
          <div className="space-y-4">
            <EnhancedSelect
              label="Basic Select"
              options={selectOptions}
              value={selectValue}
              onValueChange={setSelectValue}
              placeholder="Choose an option..."
            />
            <EnhancedSelect
              label="Searchable Select"
              options={selectOptions}
              searchable
              clearable
              placeholder="Search and select..."
            />
          </div>
        </EnhancedCard>

        {/* Enhanced Progress Demo */}
        <EnhancedCard
          variant="gradient"
          header={<h2 className="text-xl font-semibold">Enhanced Progress</h2>}
        >
          <div className="space-y-6">
            <EnhancedProgress
              label="Progress Bar"
              value={progress}
              showPercentage
              animated
            />
            <EnhancedProgress
              variant="success"
              value={85}
              showValue
              label="Success Progress"
              striped
            />
            <EnhancedProgress
              variant="gradient"
              indeterminate
              label="Loading Progress"
            />
            <div className="flex gap-4">
              <EnhancedButton 
                size="sm"
                onClick={() => setProgress(Math.max(0, progress - 10))}
              >
                Decrease
              </EnhancedButton>
              <EnhancedButton 
                size="sm"
                onClick={() => setProgress(Math.min(100, progress + 10))}
              >
                Increase
              </EnhancedButton>
            </div>
          </div>
        </EnhancedCard>

        {/* Enhanced Modal Demo */}
        <EnhancedCard
          header={<h2 className="text-xl font-semibold">Enhanced Modals</h2>}
        >
          <div className="flex gap-4">
            <EnhancedButton onClick={() => setModalOpen(true)}>
              Open Modal
            </EnhancedButton>
            <EnhancedButton 
              variant="destructive"
              onClick={() => setConfirmOpen(true)}
            >
              Open Confirm Dialog
            </EnhancedButton>
          </div>
        </EnhancedCard>

        {/* Component Status */}
        <EnhancedCard
          variant="success"
          header={<h2 className="text-xl font-semibold">Component Status</h2>}
        >
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
            <div className="flex items-center gap-2">
              <span className="w-2 h-2 bg-green-500 rounded-full"></span>
              EnhancedButton ✓
            </div>
            <div className="flex items-center gap-2">
              <span className="w-2 h-2 bg-green-500 rounded-full"></span>
              EnhancedInput ✓
            </div>
            <div className="flex items-center gap-2">
              <span className="w-2 h-2 bg-green-500 rounded-full"></span>
              EnhancedCard ✓
            </div>
            <div className="flex items-center gap-2">
              <span className="w-2 h-2 bg-green-500 rounded-full"></span>
              EnhancedSelect ✓
            </div>
            <div className="flex items-center gap-2">
              <span className="w-2 h-2 bg-green-500 rounded-full"></span>
              EnhancedProgress ✓
            </div>
            <div className="flex items-center gap-2">
              <span className="w-2 h-2 bg-green-500 rounded-full"></span>
              EnhancedModal ✓
            </div>
          </div>
        </EnhancedCard>
      </div>

      {/* Modals */}
      <EnhancedModal
        open={modalOpen}
        onOpenChange={setModalOpen}
        title="Enhanced Modal Demo"
        description="This is a demo of the enhanced modal component with animations and accessibility."
        footer={
          <div className="flex gap-3 justify-end">
            <EnhancedButton 
              variant="outline" 
              onClick={() => setModalOpen(false)}
            >
              Cancel
            </EnhancedButton>
            <EnhancedButton onClick={() => setModalOpen(false)}>
              Save Changes
            </EnhancedButton>
          </div>
        }
      >
        <div className="space-y-4">
          <p>This modal demonstrates the enhanced modal component with:</p>
          <ul className="list-disc list-inside space-y-1 text-sm text-gray-600">
            <li>Smooth animations</li>
            <li>Accessibility features</li>
            <li>Customizable variants</li>
            <li>Flexible content and footer</li>
          </ul>
        </div>
      </EnhancedModal>

      <ConfirmModal
        open={confirmOpen}
        onOpenChange={setConfirmOpen}
        title="Confirm Action"
        description="Are you sure you want to perform this action? This cannot be undone."
        variant="destructive"
        onConfirm={() => {
          console.log('Action confirmed!');
          setConfirmOpen(false);
        }}
      />
    </div>
  );
}