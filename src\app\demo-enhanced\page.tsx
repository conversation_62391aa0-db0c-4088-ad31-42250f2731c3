'use client';

import React, { useState } from 'react';
import { 
  EnhancedButton,
  EnhancedCard,
} from '@/components/ui/enhanced-index';
import { FormWizard } from '@/components/calculator';
import type { CalculationResult } from '@/components/calculator/types/wizard';

// Demo page to test the Form Wizard
export default function DemoEnhancedPage() {
  const [result, setResult] = useState<CalculationResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [showWizard, setShowWizard] = useState(true);

  const handleResult = (calculationResult: CalculationResult) => {
    setResult(calculationResult);
    setShowWizard(false);
    setError(null);
  };

  const handleError = (errorMessage: string) => {
    setError(errorMessage);
    setResult(null);
  };

  const resetDemo = () => {
    setResult(null);
    setError(null);
    setShowWizard(true);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4 space-y-8">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Enhanced Form Wizard Demo
          </h1>
          <p className="text-gray-600">
            Experience the TurboTax-like multi-step construction cost calculator wizard.
          </p>
          {!showWizard && (
            <EnhancedButton 
              onClick={resetDemo}
              variant="outline"
              size="sm"
              className="mt-4"
            >
              Start New Calculation
            </EnhancedButton>
          )}
        </div>

        {/* Form Wizard Demo */}
        {showWizard && (
          <FormWizard
            onResult={handleResult}
            onError={handleError}
          />
        )}

        {/* Results Display */}
        {result && (
          <EnhancedCard 
            variant="elevated" 
            size="lg"
            header={
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold text-green-700">Calculation Complete!</h2>
                <EnhancedButton 
                  onClick={resetDemo}
                  variant="outline"
                  size="sm"
                >
                  New Calculation
                </EnhancedButton>
              </div>
            }
          >
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <h3 className="font-semibold text-gray-900">Project Summary</h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Built-up Area:</span>
                      <span className="font-medium">{result.builtUpArea.toLocaleString()} sq ft</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Quality:</span>
                      <span className="font-medium capitalize">{result.quality}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Location:</span>
                      <span className="font-medium capitalize">{result.location}</span>
                    </div>
                  </div>
                </div>
                
                <div className="space-y-3">
                  <h3 className="font-semibold text-gray-900">Cost Summary</h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Total Cost:</span>
                      <span className="font-bold text-lg text-green-600">
                        ₹{(result.totalCost / 100000).toFixed(1)}L
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Cost per sq ft:</span>
                      <span className="font-medium">₹{result.costPerSqft.toLocaleString()}</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-3">
                <h3 className="font-semibold text-gray-900">Cost Breakdown</h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center p-3 bg-blue-50 rounded-lg">
                    <div className="text-sm text-gray-600">Structure</div>
                    <div className="font-bold text-blue-600">
                      ₹{(result.breakdown.structure / 100000).toFixed(1)}L
                    </div>
                  </div>
                  <div className="text-center p-3 bg-green-50 rounded-lg">
                    <div className="text-sm text-gray-600">Finishing</div>
                    <div className="font-bold text-green-600">
                      ₹{(result.breakdown.finishing / 100000).toFixed(1)}L
                    </div>
                  </div>
                  <div className="text-center p-3 bg-purple-50 rounded-lg">
                    <div className="text-sm text-gray-600">MEP Work</div>
                    <div className="font-bold text-purple-600">
                      ₹{(result.breakdown.mep / 100000).toFixed(1)}L
                    </div>
                  </div>
                  <div className="text-center p-3 bg-orange-50 rounded-lg">
                    <div className="text-sm text-gray-600">Other</div>
                    <div className="font-bold text-orange-600">
                      ₹{(result.breakdown.other / 100000).toFixed(1)}L
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </EnhancedCard>
        )}

        {/* Error Display */}
        {error && (
          <EnhancedCard 
            variant="outlined" 
            header={<h2 className="text-xl font-semibold text-red-700">Calculation Error</h2>}
          >
            <div className="space-y-4">
              <p className="text-red-600">{error}</p>
              <EnhancedButton 
                onClick={resetDemo}
                variant="primary"
              >
                Try Again
              </EnhancedButton>
            </div>
          </EnhancedCard>
        )}
      </div>
    </div>
  );
}