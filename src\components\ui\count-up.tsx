'use client';

import { motion, useSpring, useTransform, useInView } from 'framer-motion';
import { useEffect, useRef } from 'react';
import { prefersReducedMotion, enhancedCountUp, numberTicker } from '@/lib/animations';

interface CountUpProps {
  value: number;
  duration?: number;
  delay?: number;
  className?: string;
  prefix?: string;
  suffix?: string;
  decimals?: number;
  separator?: string;
  formatter?: (value: number) => string;
}

export function CountUp({
  value,
  duration = 2,
  delay = 0,
  className,
  prefix = '',
  suffix = '',
  decimals = 0,
  separator = ',',
  formatter
}: CountUpProps) {
  const ref = useRef<HTMLSpanElement>(null);
  const isInView = useInView(ref, { once: true, amount: 0.3 });
  
  const spring = useSpring(0, {
    damping: 25,
    stiffness: 100,
    duration: prefersReducedMotion ? 0 : duration * 1000
  });
  
  const display = useTransform(spring, (current) => {
    if (formatter) {
      return formatter(current);
    }
    
    const formattedValue = current.toFixed(decimals);
    
    if (separator && current >= 1000) {
      return formattedValue.replace(/\B(?=(\d{3})+(?!\d))/g, separator);
    }
    
    return formattedValue;
  });
  
  useEffect(() => {
    if (isInView) {
      const timer = setTimeout(() => {
        spring.set(value);
      }, delay * 1000);
      
      return () => clearTimeout(timer);
    }
  }, [isInView, value, delay, spring]);
  
  return (
    <motion.span
      ref={ref}
      className={className}
      variants={enhancedCountUp}
      initial="initial"
      animate={isInView ? "animate" : "initial"}
      transition={{ 
        duration: prefersReducedMotion ? 0 : 0.5, 
        delay: prefersReducedMotion ? 0 : delay,
      }}
    >
      {prefix}
      <motion.span
        variants={numberTicker}
        initial="initial"
        animate={isInView ? "animate" : "initial"}
        custom={0}
      >
        {display}
      </motion.span>
      {suffix}
    </motion.span>
  );
}