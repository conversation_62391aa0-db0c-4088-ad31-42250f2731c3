/**
 * Enhanced <PERSON><PERSON> Component for MVP Design System
 * Professional button with variants, sizes, and accessibility
 */

import * as React from "react";
import { motion, type Variants } from "framer-motion";
import { Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";

// Button variant types
type ButtonVariant = "primary" | "secondary" | "outline" | "ghost" | "destructive";
type ButtonSize = "sm" | "md" | "lg" | "xl";

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: ButtonVariant;
  size?: ButtonSize;
  loading?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  fullWidth?: boolean;
  asChild?: boolean;
}

// Animation variants for button states
const buttonVariants: Variants = {
  idle: {
    scale: 1,
    boxShadow: "0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)",
  },
  hover: {
    scale: 1.02,
    boxShadow: "0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)",
    transition: { duration: 0.15 },
  },
  tap: {
    scale: 0.98,
    transition: { duration: 0.1 },
  },
};

// Loading spinner animation
const spinnerVariants: Variants = {
  animate: {
    rotate: 360,
    transition: {
      duration: 1,
      repeat: Infinity,
      ease: "linear",
    },
  },
};

// Base button classes
const baseClasses = cn(
  "inline-flex items-center justify-center gap-2 rounded-lg font-medium",
  "transition-all duration-200 ease-out",
  "focus:outline-none focus:ring-4 focus:ring-primary-500/20",
  "disabled:opacity-50 disabled:cursor-not-allowed disabled:pointer-events-none",
  "relative overflow-hidden"
);

// Variant styles
const variantClasses: Record<ButtonVariant, string> = {
  primary: cn(
    "bg-gradient-to-r from-primary-500 to-primary-600",
    "text-white border border-primary-600",
    "hover:from-primary-600 hover:to-primary-700",
    "shadow-lg shadow-primary-500/25"
  ),
  secondary: cn(
    "bg-secondary-100 text-secondary-700 border border-secondary-200",
    "hover:bg-secondary-200 hover:border-secondary-300"
  ),
  outline: cn(
    "bg-transparent border-2 border-primary-300 text-primary-700",
    "hover:bg-primary-50 hover:border-primary-400"
  ),
  ghost: cn(
    "bg-transparent text-secondary-700",
    "hover:bg-secondary-100"
  ),
  destructive: cn(
    "bg-red-500 text-white border border-red-600",
    "hover:bg-red-600 hover:border-red-700",
    "shadow-lg shadow-red-500/25"
  ),
};

// Size styles  
const sizeClasses: Record<ButtonSize, string> = {
  sm: "px-3 py-1.5 text-sm h-8",
  md: "px-4 py-2 text-base h-10",
  lg: "px-6 py-3 text-lg h-12",
  xl: "px-8 py-4 text-xl h-14",
};

export const EnhancedButton = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      className,
      variant = "primary",
      size = "md",
      loading = false,
      leftIcon,
      rightIcon,
      fullWidth = false,
      children,
      disabled,
      onClick,
      type = "button",
      ...props
    },
    ref
  ) => {
    const isDisabled = disabled || loading;

    const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
      if (!isDisabled && onClick) {
        onClick(event);
      }
    };

    return (
      <motion.button
        ref={ref}
        type={type}
        className={cn(
          baseClasses,
          variantClasses[variant],
          sizeClasses[size],
          fullWidth && "w-full",
          className
        )}
        disabled={isDisabled}
        onClick={handleClick}
        variants={buttonVariants}
        initial="idle"
        whileHover={!isDisabled ? "hover" : undefined}
        whileTap={!isDisabled ? "tap" : undefined}
        aria-busy={loading}
        aria-disabled={isDisabled}
        {...props}
      >
        {/* Loading Spinner */}
        {loading && (
          <motion.div
            variants={spinnerVariants}
            animate="animate"
            className="mr-2"
          >
            <Loader2 className="h-4 w-4" />
          </motion.div>
        )}

        {/* Left Icon */}
        {!loading && leftIcon && (
          <span className="flex-shrink-0" aria-hidden="true">
            {leftIcon}
          </span>
        )}

        {/* Button Content */}
        <span className={cn("truncate", loading && "opacity-75")}>
          {children}
        </span>

        {/* Right Icon */}
        {!loading && rightIcon && (
          <span className="flex-shrink-0" aria-hidden="true">
            {rightIcon}
          </span>
        )}

        {/* Shine Effect for Primary Buttons */}
        {variant === "primary" && !isDisabled && (
          <motion.div
            className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
            initial={{ x: "-100%" }}
            whileHover={{
              x: "100%",
              transition: { duration: 0.6, ease: "easeInOut" },
            }}
          />
        )}
      </motion.button>
    );
  }
);

EnhancedButton.displayName = "EnhancedButton";

// Export types for external use
export type { ButtonProps, ButtonVariant, ButtonSize };