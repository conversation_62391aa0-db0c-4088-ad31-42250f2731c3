/**
 * Smart Defaults Engine
 * AI-powered defaults and suggestions based on user input and regional preferences
 */

import { CalculatorFormData } from '@/components/calculator/types/wizard';

export interface RegionalPreferences {
  city: string;
  preferredRoomSizes: {
    bedrooms: number;
    bathrooms: number;
    balconies: number;
  };
  popularFeatures: string[];
  budgetMultiplier: number;
  constructionStyle: 'compact' | 'spacious' | 'traditional';
  qualityPreference: 'smart' | 'premium' | 'luxury';
}

export interface SmartDefault {
  field: keyof CalculatorFormData;
  value: string | boolean;
  confidence: number; // 0-1 scale
  reason: string;
  userVisible: boolean; // Whether to show suggestion to user
}

export interface DefaultsContext {
  plotSize?: number;
  location?: string;
  buildingType?: string;
  floors?: number;
  budget?: number; // Estimated or user-provided
  familySize?: number; // Inferred from room count
  userType?: 'first-time' | 'experienced' | 'investor';
}

// Regional preference data based on Indian market research
export const REGIONAL_PREFERENCES: Record<string, RegionalPreferences> = {
  mumbai: {
    city: 'Mumbai',
    preferredRoomSizes: { bedrooms: 2, bathrooms: 2, balconies: 1 },
    popularFeatures: ['homeAutomation', 'securitySystem', 'internetCabling'],
    budgetMultiplier: 1.2,
    constructionStyle: 'compact',
    qualityPreference: 'premium',
  },
  delhi: {
    city: 'Delhi NCR',
    preferredRoomSizes: { bedrooms: 3, bathrooms: 3, balconies: 2 },
    popularFeatures: ['generator', 'rainwaterHarvesting', 'securitySystem'],
    budgetMultiplier: 1.05,
    constructionStyle: 'spacious',
    qualityPreference: 'premium',
  },
  bangalore: {
    city: 'Bangalore',
    preferredRoomSizes: { bedrooms: 3, bathrooms: 2, balconies: 2 },
    popularFeatures: ['solarPanels', 'rainwaterHarvesting', 'internetCabling'],
    budgetMultiplier: 1.0,
    constructionStyle: 'spacious',
    qualityPreference: 'smart',
  },
  chennai: {
    city: 'Chennai',
    preferredRoomSizes: { bedrooms: 3, bathrooms: 2, balconies: 1 },
    popularFeatures: ['rainwaterHarvesting', 'solarPanels', 'generator'],
    budgetMultiplier: 0.95,
    constructionStyle: 'traditional',
    qualityPreference: 'smart',
  },
  hyderabad: {
    city: 'Hyderabad',
    preferredRoomSizes: { bedrooms: 3, bathrooms: 2, balconies: 2 },
    popularFeatures: ['solarPanels', 'rainwaterHarvesting', 'securitySystem'],
    budgetMultiplier: 0.9,
    constructionStyle: 'spacious',
    qualityPreference: 'smart',
  },
  pune: {
    city: 'Pune',
    preferredRoomSizes: { bedrooms: 3, bathrooms: 2, balconies: 2 },
    popularFeatures: ['solarPanels', 'internetCabling', 'securitySystem'],
    budgetMultiplier: 1.0,
    constructionStyle: 'spacious',
    qualityPreference: 'smart',
  },
  kolkata: {
    city: 'Kolkata',
    preferredRoomSizes: { bedrooms: 3, bathrooms: 2, balconies: 1 },
    popularFeatures: ['generator', 'securitySystem', 'internetCabling'],
    budgetMultiplier: 0.85,
    constructionStyle: 'traditional',
    qualityPreference: 'smart',
  },
  ahmedabad: {
    city: 'Ahmedabad',
    preferredRoomSizes: { bedrooms: 3, bathrooms: 2, balconies: 2 },
    popularFeatures: ['solarPanels', 'rainwaterHarvesting', 'generator'],
    budgetMultiplier: 0.8,
    constructionStyle: 'traditional',
    qualityPreference: 'smart',
  },
  jaipur: {
    city: 'Jaipur',
    preferredRoomSizes: { bedrooms: 3, bathrooms: 2, balconies: 2 },
    popularFeatures: ['rainwaterHarvesting', 'solarPanels', 'generator'],
    budgetMultiplier: 0.75,
    constructionStyle: 'traditional',
    qualityPreference: 'smart',
  },
  lucknow: {
    city: 'Lucknow',
    preferredRoomSizes: { bedrooms: 3, bathrooms: 2, balconies: 1 },
    popularFeatures: ['generator', 'securitySystem', 'rainwaterHarvesting'],
    budgetMultiplier: 0.7,
    constructionStyle: 'traditional',
    qualityPreference: 'smart',
  },
};

// Popular combinations based on market research
const POPULAR_COMBINATIONS = [
  { bedrooms: 2, bathrooms: 2, kitchens: 1, livingRooms: 1, confidence: 0.9 },
  { bedrooms: 3, bathrooms: 2, kitchens: 1, livingRooms: 1, confidence: 0.95 },
  { bedrooms: 3, bathrooms: 3, kitchens: 1, livingRooms: 1, confidence: 0.8 },
  { bedrooms: 4, bathrooms: 3, kitchens: 1, livingRooms: 2, confidence: 0.7 },
];

export class SmartDefaultsEngine {
  private context: DefaultsContext;
  private regionalPrefs: RegionalPreferences | null = null;

  constructor(context: DefaultsContext = {}) {
    this.context = context;
    if (context.location) {
      this.regionalPrefs = REGIONAL_PREFERENCES[context.location] || null;
    }
  }

  /**
   * Update context with new information
   */
  updateContext(newContext: Partial<DefaultsContext>): void {
    this.context = { ...this.context, ...newContext };
    if (newContext.location) {
      this.regionalPrefs = REGIONAL_PREFERENCES[newContext.location] || null;
    }
  }

  /**
   * Generate smart defaults for a given step
   */
  generateDefaults(
    currentData: Partial<CalculatorFormData>,
    step: 'basic' | 'rooms' | 'quality' | 'features'
  ): SmartDefault[] {
    const defaults: SmartDefault[] = [];

    switch (step) {
      case 'basic':
        defaults.push(...this.generateBasicDefaults(currentData));
        break;
      case 'rooms':
        defaults.push(...this.generateRoomDefaults(currentData));
        break;
      case 'quality':
        defaults.push(...this.generateQualityDefaults(currentData));
        break;
      case 'features':
        defaults.push(...this.generateFeatureDefaults(currentData));
        break;
    }

    return defaults.filter(d => d.confidence > 0.6); // Only high-confidence suggestions
  }

  /**
   * Generate defaults for basic info step
   */
  private generateBasicDefaults(data: Partial<CalculatorFormData>): SmartDefault[] {
    const defaults: SmartDefault[] = [];

    // Auto-suggest floors based on plot size
    if (data.plotSize && !data.floors) {
      const plotSize = parseFloat(data.plotSize);
      let suggestedFloors = '1';
      let confidence = 0.7;

      if (plotSize < 1000) {
        suggestedFloors = '2';
        confidence = 0.8;
      } else if (plotSize > 3000) {
        suggestedFloors = '1';
        confidence = 0.9;
      }

      defaults.push({
        field: 'floors',
        value: suggestedFloors,
        confidence,
        reason: `Based on plot size of ${plotSize} sq ft, ${suggestedFloors} floor${parseInt(suggestedFloors) > 1 ? 's' : ''} is typical`,
        userVisible: true,
      });
    }

    // Auto-calculate built-up area
    if (data.plotSize && data.floors && !data.builtUpArea) {
      const plotSize = parseFloat(data.plotSize);
      const floors = parseInt(data.floors);
      const buildableRatio = plotSize < 1000 ? 0.7 : plotSize < 2000 ? 0.65 : 0.6;
      const suggestedArea = Math.round(plotSize * buildableRatio * floors);

      defaults.push({
        field: 'builtUpArea',
        value: suggestedArea.toString(),
        confidence: 0.85,
        reason: `Based on ${buildableRatio * 100}% buildable area for ${plotSize} sq ft plot`,
        userVisible: true,
      });
    }

    return defaults;
  }

  /**
   * Generate defaults for room configuration step
   */
  private generateRoomDefaults(data: Partial<CalculatorFormData>): SmartDefault[] {
    const defaults: SmartDefault[] = [];

    // Use regional preferences if available
    if (this.regionalPrefs && !data.bedrooms) {
      defaults.push({
        field: 'bedrooms',
        value: this.regionalPrefs.preferredRoomSizes.bedrooms.toString(),
        confidence: 0.8,
        reason: `Popular choice in ${this.regionalPrefs.city}`,
        userVisible: true,
      });
    }

    // Suggest popular combinations
    const bestMatch = this.findBestRoomCombination(data);
    if (bestMatch) {
      if (!data.bathrooms) {
        defaults.push({
          field: 'bathrooms',
          value: bestMatch.bathrooms.toString(),
          confidence: bestMatch.confidence,
          reason: `Popular combination with ${bestMatch.bedrooms} bedrooms`,
          userVisible: true,
        });
      }

      if (!data.livingRooms) {
        defaults.push({
          field: 'livingRooms',
          value: bestMatch.livingRooms.toString(),
          confidence: bestMatch.confidence,
          reason: `Standard for ${bestMatch.bedrooms}BR configuration`,
          userVisible: false,
        });
      }
    }

    // Smart balcony suggestions based on building type and location
    if (!data.balconies && data.buildingType && data.floors) {
      let suggestedBalconies = '1';
      let confidence = 0.7;

      if (data.buildingType === 'apartment') {
        suggestedBalconies = '1';
        confidence = 0.9;
      } else if (this.regionalPrefs) {
        suggestedBalconies = this.regionalPrefs.preferredRoomSizes.balconies.toString();
        confidence = 0.8;
      }

      defaults.push({
        field: 'balconies',
        value: suggestedBalconies,
        confidence,
        reason: `Typical for ${data.buildingType} in ${this.context.location}`,
        userVisible: true,
      });
    }

    // Parking suggestions
    if (!data.parkingSpaces && data.bedrooms) {
      const bedrooms = parseInt(data.bedrooms);
      const suggestedSpaces = bedrooms >= 3 ? '2' : '1';
      
      defaults.push({
        field: 'parkingSpaces',
        value: suggestedSpaces,
        confidence: 0.8,
        reason: `${suggestedSpaces} space${parseInt(suggestedSpaces) > 1 ? 's' : ''} recommended for ${bedrooms}BR home`,
        userVisible: true,
      });
    }

    return defaults;
  }

  /**
   * Generate defaults for quality selection step
   */
  private generateQualityDefaults(data: Partial<CalculatorFormData>): SmartDefault[] {
    const defaults: SmartDefault[] = [];

    // Quality tier based on location and estimated budget
    if (!data.quality && this.regionalPrefs) {
      defaults.push({
        field: 'quality',
        value: this.regionalPrefs.qualityPreference,
        confidence: 0.8,
        reason: `Popular choice in ${this.regionalPrefs.city}`,
        userVisible: true,
      });
    }

    // Budget-based quality suggestions
    if (data.builtUpArea && !data.quality) {
      const area = parseFloat(data.builtUpArea);
      const estimatedBudget = area * 2000; // Rough estimate

      if (estimatedBudget > 5000000) { // 50L+
        defaults.push({
          field: 'quality',
          value: 'luxury',
          confidence: 0.7,
          reason: 'Luxury finishes recommended for large projects',
          userVisible: true,
        });
      } else if (estimatedBudget > 2500000) { // 25L+
        defaults.push({
          field: 'quality',
          value: 'premium',
          confidence: 0.8,
          reason: 'Premium quality offers best value for this budget',
          userVisible: true,
        });
      }
    }

    // Material suggestions based on quality and region
    if (data.quality) {
      const qualityDefaults = this.getMaterialDefaultsForQuality(data.quality);
      defaults.push(...qualityDefaults);
    }

    return defaults;
  }

  /**
   * Generate defaults for advanced features step
   */
  private generateFeatureDefaults(data: Partial<CalculatorFormData>): SmartDefault[] {
    const defaults: SmartDefault[] = [];

    // Regional feature preferences
    if (this.regionalPrefs) {
      for (const feature of this.regionalPrefs.popularFeatures) {
        if (feature in data && data[feature as keyof CalculatorFormData] === undefined) {
          defaults.push({
            field: feature as keyof CalculatorFormData,
            value: true,
            confidence: 0.7,
            reason: `Popular feature in ${this.regionalPrefs.city}`,
            userVisible: true,
          });
        }
      }
    }

    // Budget-based feature suggestions
    if (data.builtUpArea && data.quality) {
      const area = parseFloat(data.builtUpArea);
      const qualityMultiplier = data.quality === 'luxury' ? 3500 : data.quality === 'premium' ? 2500 : 1800;
      const estimatedBudget = area * qualityMultiplier;

      if (estimatedBudget > 7500000 && !('swimmingPool' in data)) { // 75L+
        defaults.push({
          field: 'swimmingPool',
          value: false, // Suggest consideration, not auto-enable
          confidence: 0.6,
          reason: 'Swimming pool option available for luxury projects',
          userVisible: true,
        });
      }

      if (estimatedBudget > 3000000 && !('solarPanels' in data)) { // 30L+
        defaults.push({
          field: 'solarPanels',
          value: true,
          confidence: 0.8,
          reason: 'Solar panels provide long-term savings',
          userVisible: true,
        });
      }
    }

    // Floors-based elevator suggestion
    if (data.floors && parseInt(data.floors) >= 3 && !('elevator' in data)) {
      defaults.push({
        field: 'elevator',
        value: true,
        confidence: 0.9,
        reason: 'Elevator recommended for 3+ floor buildings',
        userVisible: true,
      });
    }

    return defaults;
  }

  /**
   * Find best room combination match
   */
  private findBestRoomCombination(data: Partial<CalculatorFormData>) {
    if (!data.bedrooms) return null;

    const bedrooms = parseInt(data.bedrooms);
    return POPULAR_COMBINATIONS.find(combo => combo.bedrooms === bedrooms);
  }

  /**
   * Get material defaults for quality tier
   */
  private getMaterialDefaultsForQuality(quality: string): SmartDefault[] {
    const defaults: SmartDefault[] = [];

    const qualityMaterials = {
      smart: {
        flooringType: 'ceramic',
        wallFinish: 'paint',
        ceilingType: 'false',
        kitchenType: 'modular',
        bathroomFixtures: 'standard',
        electricalFittings: 'standard',
      },
      premium: {
        flooringType: 'vitrified',
        wallFinish: 'texture',
        ceilingType: 'gypsum',
        kitchenType: 'premium-modular',
        bathroomFixtures: 'premium',
        electricalFittings: 'premium',
      },
      luxury: {
        flooringType: 'marble',
        wallFinish: 'stone',
        ceilingType: 'designer',
        kitchenType: 'luxury',
        bathroomFixtures: 'luxury',
        electricalFittings: 'automation',
      },
    };

    const materials = qualityMaterials[quality as keyof typeof qualityMaterials];
    if (materials) {
      Object.entries(materials).forEach(([field, value]) => {
        defaults.push({
          field: field as keyof CalculatorFormData,
          value,
          confidence: 0.85,
          reason: `Standard for ${quality} quality tier`,
          userVisible: false,
        });
      });
    }

    return defaults;
  }

  /**
   * Get budget estimate based on current selections
   */
  getBudgetEstimate(data: Partial<CalculatorFormData>): number | null {
    if (!data.builtUpArea) return null;

    const area = parseFloat(data.builtUpArea);
    const quality = data.quality || 'smart';
    const location = data.location || 'delhi';

    const baseCosts = {
      smart: 1800,
      premium: 2500,
      luxury: 3500,
    };

    const locationMultiplier = REGIONAL_PREFERENCES[location]?.budgetMultiplier || 1.0;
    const baseCost = baseCosts[quality as keyof typeof baseCosts] * locationMultiplier;

    return area * baseCost;
  }

  /**
   * Get user experience level based on selections
   */
  getUserExperienceLevel(data: Partial<CalculatorFormData>): 'first-time' | 'experienced' | 'investor' {
    // Simple heuristics based on choices
    const hasAdvancedFeatures = !!(
      data.homeAutomation ||
      data.swimmingPool ||
      data.elevator ||
      data.solarPanels
    );

    const isLuxuryQuality = data.quality === 'luxury';
    const hasMultipleFloors = data.floors && parseInt(data.floors) > 2;

    if (hasAdvancedFeatures && isLuxuryQuality) return 'investor';
    if (hasMultipleFloors || data.quality === 'premium') return 'experienced';
    return 'first-time';
  }
}

// Export singleton instance for global use
export const smartDefaults = new SmartDefaultsEngine();

// Helper function for quick default generation
export function generateQuickDefaults(
  data: Partial<CalculatorFormData>,
  context: DefaultsContext = {}
): SmartDefault[] {
  const engine = new SmartDefaultsEngine(context);
  
  // Determine current step based on available data
  let step: 'basic' | 'rooms' | 'quality' | 'features' = 'basic';
  
  if (data.plotSize && data.location) {
    step = 'rooms';
  }
  if (data.bedrooms && data.bathrooms) {
    step = 'quality';
  }
  if (data.quality) {
    step = 'features';
  }

  return engine.generateDefaults(data, step);
}