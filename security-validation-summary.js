#!/usr/bin/env node

/**
 * Security Validation Summary
 * Quick validation of security implementations
 */

const fs = require('fs');
const path = require('path');

class SecurityValidationSummary {
  constructor() {
    this.results = [];
    this.basePath = process.cwd();
  }

  async validateSecurityImplementation() {
    console.log('🔒 Security Implementation Validation');
    console.log('='.repeat(50));

    // 1. Validate security files exist
    this.validateSecurityFiles();
    
    // 2. Validate middleware implementation
    this.validateMiddleware();
    
    // 3. Validate security headers
    this.validateSecurityHeaders();
    
    // 4. Validate rate limiter
    this.validateRateLimiter();
    
    // 5. Validate vulnerability scanner
    this.validateVulnerabilityScanner();
    
    // 6. Validate input sanitizer
    this.validateInputSanitizer();
    
    // 7. Validate CORS configuration
    this.validateCORSConfig();
    
    // 8. Validate security monitoring
    this.validateSecurityMonitoring();
    
    this.generateValidationReport();
  }

  validateSecurityFiles() {
    console.log('\n📁 Validating Security Files...');
    
    const requiredFiles = [
      'middleware.ts',
      'src/lib/security/headers.ts',
      'src/lib/security/advanced-rate-limiter.ts',
      'src/lib/security/vulnerability-scanner.ts',
      'src/lib/security/input-sanitizer.ts',
      'src/lib/security/cors-config.ts',
      'src/lib/security/security-monitor.ts',
      'src/lib/security/security-tester.ts',
      'src/lib/security/incident-response.ts'
    ];
    
    for (const file of requiredFiles) {
      const filePath = path.join(this.basePath, file);
      const exists = fs.existsSync(filePath);
      
      this.addResult('Files', file, exists, 
        exists ? '✅ File exists' : '❌ File missing');
    }
  }

  validateMiddleware() {
    console.log('\n🛡️ Validating Middleware Implementation...');
    
    try {
      const middlewarePath = path.join(this.basePath, 'middleware.ts');
      const middlewareContent = fs.readFileSync(middlewarePath, 'utf8');
      
      const checks = [
        { name: 'Production Security Middleware', pattern: /ProductionSecurityMiddleware/ },
        { name: 'Rate Limiting Integration', pattern: /advancedRateLimiter/ },
        { name: 'Vulnerability Scanning', pattern: /vulnerabilityScanner/ },
        { name: 'Security Headers', pattern: /securityHeaders/ },
        { name: 'Geographic Blocking', pattern: /blockedCountries/ },
        { name: 'Bot Detection', pattern: /suspiciousUserAgents/ },
        { name: 'Path Traversal Protection', pattern: /detectPathTraversal/ },
        { name: 'Critical Path Protection', pattern: /criticalPaths/ },
        { name: 'Request Size Validation', pattern: /validateRequestSize/ },
        { name: 'Content Type Validation', pattern: /validateContentType/ }
      ];
      
      for (const check of checks) {
        const implemented = check.pattern.test(middlewareContent);
        this.addResult('Middleware', check.name, implemented,
          implemented ? '✅ Implemented' : '❌ Missing');
      }
      
    } catch (error) {
      this.addResult('Middleware', 'Validation', false, `❌ Error: ${error.message}`);
    }
  }

  validateSecurityHeaders() {
    console.log('\n📋 Validating Security Headers...');
    
    try {
      const headersPath = path.join(this.basePath, 'src/lib/security/headers.ts');
      const headersContent = fs.readFileSync(headersPath, 'utf8');
      
      const headerChecks = [
        { name: 'Content Security Policy', pattern: /Content-Security-Policy/ },
        { name: 'X-Content-Type-Options', pattern: /X-Content-Type-Options/ },
        { name: 'X-Frame-Options', pattern: /X-Frame-Options/ },
        { name: 'X-XSS-Protection', pattern: /X-XSS-Protection/ },
        { name: 'Strict-Transport-Security', pattern: /Strict-Transport-Security/ },
        { name: 'Referrer-Policy', pattern: /Referrer-Policy/ },
        { name: 'Permissions-Policy', pattern: /Permissions-Policy/ },
        { name: 'Cross-Origin Policies', pattern: /Cross-Origin/ },
        { name: 'Nonce Generation', pattern: /generateNonce/ },
        { name: 'CSP Builder', pattern: /buildCSP/ }
      ];
      
      for (const check of headerChecks) {
        const implemented = check.pattern.test(headersContent);
        this.addResult('Headers', check.name, implemented,
          implemented ? '✅ Configured' : '❌ Missing');
      }
      
    } catch (error) {
      this.addResult('Headers', 'Validation', false, `❌ Error: ${error.message}`);
    }
  }

  validateRateLimiter() {
    console.log('\n⚡ Validating Rate Limiter...');
    
    try {
      const rateLimiterPath = path.join(this.basePath, 'src/lib/security/advanced-rate-limiter.ts');
      const rateLimiterContent = fs.readFileSync(rateLimiterPath, 'utf8');
      
      const rateLimiterChecks = [
        { name: 'Multi-tier Rate Limiting', pattern: /RateLimitTier/ },
        { name: 'VIP Tier Implementation', pattern: /vip.*tier/i },
        { name: 'Anonymous Tier Implementation', pattern: /anonymous.*tier/i },
        { name: 'Bot Detection Tier', pattern: /bot.*tier/i },
        { name: 'Burst Protection', pattern: /burstLimit/ },
        { name: 'Progressive Blocking', pattern: /suspiciousActivity/ },
        { name: 'IP Blocking', pattern: /blockedIPs/ },
        { name: 'Client Statistics', pattern: /ClientStats/ },
        { name: 'Cleanup Task', pattern: /cleanupInterval/ },
        { name: 'Rate Limit Result', pattern: /RateLimitResult/ }
      ];
      
      for (const check of rateLimiterChecks) {
        const implemented = check.pattern.test(rateLimiterContent);
        this.addResult('Rate Limiter', check.name, implemented,
          implemented ? '✅ Implemented' : '❌ Missing');
      }
      
    } catch (error) {
      this.addResult('Rate Limiter', 'Validation', false, `❌ Error: ${error.message}`);
    }
  }

  validateVulnerabilityScanner() {
    console.log('\n🔍 Validating Vulnerability Scanner...');
    
    try {
      const scannerPath = path.join(this.basePath, 'src/lib/security/vulnerability-scanner.ts');
      const scannerContent = fs.readFileSync(scannerPath, 'utf8');
      
      const scannerChecks = [
        { name: 'SQL Injection Detection', pattern: /SQL.*Injection/i },
        { name: 'XSS Detection', pattern: /XSS.*Protection/i },
        { name: 'Path Traversal Detection', pattern: /Path.*Traversal/i },
        { name: 'Command Injection Detection', pattern: /Command.*Injection/i },
        { name: 'Information Disclosure', pattern: /Information.*Disclosure/i },
        { name: 'Authentication Bypass', pattern: /Authentication.*Bypass/i },
        { name: 'DoS Detection', pattern: /DoS.*Resource/i },
        { name: 'CSRF Detection', pattern: /CSRF/i },
        { name: 'Vulnerability Rules', pattern: /VulnerabilityRule/ },
        { name: 'Risk Scoring', pattern: /riskScore/ },
        { name: 'OWASP Mapping', pattern: /owasp/ },
        { name: 'CWE Mapping', pattern: /cwe/ }
      ];
      
      for (const check of scannerChecks) {
        const implemented = check.pattern.test(scannerContent);
        this.addResult('Vulnerability Scanner', check.name, implemented,
          implemented ? '✅ Implemented' : '❌ Missing');
      }
      
    } catch (error) {
      this.addResult('Vulnerability Scanner', 'Validation', false, `❌ Error: ${error.message}`);
    }
  }

  validateInputSanitizer() {
    console.log('\n🧹 Validating Input Sanitizer...');
    
    try {
      // Check if input sanitizer exists
      const sanitizerPath = path.join(this.basePath, 'src/lib/security/input-sanitizer.ts');
      const sanitizerExists = fs.existsSync(sanitizerPath);
      
      this.addResult('Input Sanitizer', 'File Exists', sanitizerExists,
        sanitizerExists ? '✅ File exists' : '❌ File missing');
      
      if (sanitizerExists) {
        const sanitizerContent = fs.readFileSync(sanitizerPath, 'utf8');
        
        const sanitizerChecks = [
          { name: 'HTML Sanitization', pattern: /DOMPurify|sanitizeHTML/i },
          { name: 'SQL Injection Prevention', pattern: /sanitizeSQL|parameterized/i },
          { name: 'XSS Prevention', pattern: /XSS|sanitizeXSS/i },
          { name: 'File Upload Security', pattern: /file.*upload.*security/i },
          { name: 'Email Validation', pattern: /email.*validation/i },
          { name: 'URL Validation', pattern: /url.*validation/i },
          { name: 'Phone Validation', pattern: /phone.*validation/i }
        ];
        
        for (const check of sanitizerChecks) {
          const implemented = check.pattern.test(sanitizerContent);
          this.addResult('Input Sanitizer', check.name, implemented,
            implemented ? '✅ Implemented' : '❌ Missing');
        }
      }
      
    } catch (error) {
      this.addResult('Input Sanitizer', 'Validation', false, `❌ Error: ${error.message}`);
    }
  }

  validateCORSConfig() {
    console.log('\n🌐 Validating CORS Configuration...');
    
    try {
      const corsPath = path.join(this.basePath, 'src/lib/security/cors-config.ts');
      const corsExists = fs.existsSync(corsPath);
      
      this.addResult('CORS', 'File Exists', corsExists,
        corsExists ? '✅ File exists' : '❌ File missing');
      
      if (corsExists) {
        const corsContent = fs.readFileSync(corsPath, 'utf8');
        
        const corsChecks = [
          { name: 'Origin Validation', pattern: /origin.*validation/i },
          { name: 'Credentials Control', pattern: /credentials/i },
          { name: 'Methods Control', pattern: /methods/i },
          { name: 'Headers Control', pattern: /headers/i },
          { name: 'Geographic Control', pattern: /geographic|country/i }
        ];
        
        for (const check of corsChecks) {
          const implemented = check.pattern.test(corsContent);
          this.addResult('CORS', check.name, implemented,
            implemented ? '✅ Configured' : '❌ Missing');
        }
      }
      
    } catch (error) {
      this.addResult('CORS', 'Validation', false, `❌ Error: ${error.message}`);
    }
  }

  validateSecurityMonitoring() {
    console.log('\n📊 Validating Security Monitoring...');
    
    try {
      const monitorPath = path.join(this.basePath, 'src/lib/security/security-monitor.ts');
      const monitorExists = fs.existsSync(monitorPath);
      
      this.addResult('Monitoring', 'File Exists', monitorExists,
        monitorExists ? '✅ File exists' : '❌ File missing');
      
      if (monitorExists) {
        const monitorContent = fs.readFileSync(monitorPath, 'utf8');
        
        const monitorChecks = [
          { name: 'Real-time Monitoring', pattern: /real.*time/i },
          { name: 'Incident Tracking', pattern: /incident/i },
          { name: 'Alert System', pattern: /alert|notification/i },
          { name: 'Security Events', pattern: /security.*event/i },
          { name: 'Threat Intelligence', pattern: /threat.*intelligence/i },
          { name: 'Audit Trail', pattern: /audit.*trail/i }
        ];
        
        for (const check of monitorChecks) {
          const implemented = check.pattern.test(monitorContent);
          this.addResult('Monitoring', check.name, implemented,
            implemented ? '✅ Implemented' : '❌ Missing');
        }
      }
      
    } catch (error) {
      this.addResult('Monitoring', 'Validation', false, `❌ Error: ${error.message}`);
    }
  }

  addResult(category, test, passed, details) {
    this.results.push({ category, test, passed, details });
    console.log(`  ${details}`);
  }

  generateValidationReport() {
    console.log('\n' + '='.repeat(50));
    console.log('📊 SECURITY VALIDATION REPORT');
    console.log('='.repeat(50));
    
    const total = this.results.length;
    const passed = this.results.filter(r => r.passed).length;
    const failed = total - passed;
    const score = Math.round((passed / total) * 100);
    
    console.log(`\n📈 Summary: ${passed}/${total} validations passed (${score}%)`);
    
    // Group by category
    const categories = {};
    this.results.forEach(r => {
      if (!categories[r.category]) {
        categories[r.category] = { passed: 0, total: 0, failed: [] };
      }
      categories[r.category].total++;
      if (r.passed) {
        categories[r.category].passed++;
      } else {
        categories[r.category].failed.push(r);
      }
    });
    
    console.log('\n📋 Category Breakdown:');
    Object.entries(categories).forEach(([category, stats]) => {
      const categoryScore = Math.round((stats.passed / stats.total) * 100);
      const icon = categoryScore === 100 ? '🟢' : categoryScore >= 80 ? '🟡' : '🔴';
      console.log(`  ${icon} ${category}: ${stats.passed}/${stats.total} (${categoryScore}%)`);
    });
    
    if (failed > 0) {
      console.log('\n❌ Failed Validations:');
      this.results.filter(r => !r.passed).forEach(r => {
        console.log(`   ${r.category}: ${r.test} - ${r.details}`);
      });
    }
    
    // Security Assessment
    console.log('\n🛡️ Security Assessment:');
    if (score >= 95) {
      console.log('   🟢 EXCELLENT: Production-ready security implementation');
    } else if (score >= 85) {
      console.log('   🟡 GOOD: Minor security improvements needed');
    } else if (score >= 70) {
      console.log('   🟠 FAIR: Moderate security concerns');
    } else {
      console.log('   🔴 POOR: Significant security issues');
    }
    
    // Key Security Features Status
    console.log('\n🔑 Key Security Features:');
    const keyFeatures = [
      'Production Security Middleware',
      'Multi-tier Rate Limiting', 
      'Vulnerability Detection',
      'Security Headers',
      'Input Sanitization',
      'CORS Configuration',
      'Security Monitoring'
    ];
    
    keyFeatures.forEach(feature => {
      const featureResult = this.results.find(r => 
        r.test.includes(feature) || r.test.includes(feature.split(' ')[0])
      );
      const status = featureResult ? (featureResult.passed ? '✅' : '❌') : '❓';
      console.log(`   ${status} ${feature}`);
    });
    
    // Overall Security Posture
    console.log('\n🎯 Overall Security Posture:');
    console.log(`   Security Score: ${score}/100`);
    console.log(`   Production Ready: ${score >= 90 ? '✅ YES' : '❌ NO'}`);
    console.log(`   Enterprise Grade: ${score >= 95 ? '✅ YES' : '❌ NO'}`);
    
    console.log('\n='.repeat(50));
    
    // Save validation results
    try {
      const validationData = {
        timestamp: new Date().toISOString(),
        score,
        total,
        passed,
        failed,
        categories,
        results: this.results
      };
      
      fs.writeFileSync('security-validation-results.json', 
        JSON.stringify(validationData, null, 2));
      console.log('📄 Validation results saved to: security-validation-results.json');
    } catch (error) {
      console.log('❌ Failed to save validation results');
    }
  }
}

// Run validation
const validator = new SecurityValidationSummary();
validator.validateSecurityImplementation().catch(console.error);