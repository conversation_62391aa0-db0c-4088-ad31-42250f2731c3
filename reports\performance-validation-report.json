{"timestamp": "2025-07-16T02:27:32.394Z", "duration": 9020, "overallScore": 88, "testSummary": {"total": 53, "passed": 48, "failed": 5, "passRate": 91}, "results": {"bundleOptimization": {"buildExists": true, "totalSize": 2861379, "chunkCount": 9, "chunks": [{"name": "common-5ff4460c6cdf9b66.js", "size": 59117}, {"name": "forms-c307d0cd6fde9ee8.js", "size": 208035}, {"name": "main-3e265475a24f4b4c.js", "size": 145}, {"name": "main-app-db801482452c3a61.js", "size": 516}, {"name": "polyfills-42372ed130431b0a.js", "size": 112594}, {"name": "react-7d0c28e3aaf2d47f.js", "size": 179285}, {"name": "ui-9dd5a19f99b3046f.js", "size": 131623}, {"name": "vendors-75106c60543745e6.js", "size": 2166633}, {"name": "webpack-6f539e3ad72d9227.js", "size": 3431}], "dynamicImports": 55}, "webVitals": {"coreVitals": {"LCP (Largest Contentful Paint)": true, "FID (First Input Delay)": true, "CLS (Cumulative Layout Shift)": true, "FCP (First Contentful Paint)": true, "TTFB (Time to First Byte)": true, "INP (Interaction to Next Paint)": true}, "implemented": true}, "memoryOptimization": {"memoUsage": 12, "callbackUsage": 141, "memoHookUsage": 16, "cleanupUsage": 1, "profilerExists": true}, "loadingStates": {"skeletonUsage": 256, "loadingUsage": 204, "suspenseUsage": 33, "progressiveUsage": 67}, "mobileOptimization": {"responsiveUsage": 364, "touchUsage": 424, "mobileLibExists": true, "imageOptUsage": 72}, "cachingStrategies": {"nextConfigExists": true, "swExists": true, "middlewareExists": true}, "apiPerformance": {"routeCount": 18, "errorHandlingCount": 0, "rateLimitingCount": 4, "validationCount": 10, "apiCaching": true}, "imageOptimization": {"nextImageUsage": 1, "lazyImageUsage": 16, "progressiveUsage": true}, "pwaFeatures": {"manifestExists": true, "swExists": true, "offlineExists": true}, "overallScore": 88, "recommendations": [{"category": "Memory Management", "priority": "High", "issue": "Insufficient effect cleanup detected", "solution": "Implement proper cleanup in useEffect hooks to prevent memory leaks"}]}, "recommendations": [{"category": "Memory Management", "priority": "High", "issue": "Insufficient effect cleanup detected", "solution": "Implement proper cleanup in useEffect hooks to prevent memory leaks"}], "environment": {"nodeVersion": "v24.3.0", "platform": "linux", "architecture": "x64"}}