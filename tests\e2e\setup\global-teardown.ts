import { FullConfig } from '@playwright/test';
import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

async function globalTeardown(config: FullConfig) {
  console.log('🧹 Starting global test teardown...');
  
  const startTime = Date.now();
  
  try {
    // 1. Generate test reports
    await generateTestReports();
    
    // 2. Cleanup test database
    await cleanupTestDatabase();
    
    // 3. Archive test artifacts
    await archiveTestArtifacts();
    
    // 4. Send test notifications
    await sendTestNotifications();
    
    // 5. Cleanup temporary files
    await cleanupTemporaryFiles();
    
    console.log(`✅ Global teardown completed in ${Date.now() - startTime}ms`);
    
  } catch (error) {
    console.error('❌ Global teardown failed:', error);
    // Don't throw error to avoid failing the entire test suite
  }
}

async function generateTestReports() {
  console.log('📊 Generating test reports...');
  
  try {
    // Generate HTML report
    execSync('npx playwright show-report --reporter=html', { stdio: 'inherit' });
    
    // Generate test summary
    await generateTestSummary();
    
    console.log('✅ Test reports generated');
  } catch (error) {
    console.error('❌ Failed to generate test reports:', error);
  }
}

async function generateTestSummary() {
  const resultsPath = path.join(process.cwd(), 'tests/e2e/reports/results.json');
  
  if (!fs.existsSync(resultsPath)) {
    return;
  }
  
  const results = JSON.parse(fs.readFileSync(resultsPath, 'utf-8'));
  
  const summary = {
    timestamp: new Date().toISOString(),
    total: results.suites?.reduce((acc: number, suite: any) => acc + suite.tests.length, 0) || 0,
    passed: results.suites?.reduce((acc: number, suite: any) => 
      acc + suite.tests.filter((test: any) => test.status === 'passed').length, 0) || 0,
    failed: results.suites?.reduce((acc: number, suite: any) => 
      acc + suite.tests.filter((test: any) => test.status === 'failed').length, 0) || 0,
    skipped: results.suites?.reduce((acc: number, suite: any) => 
      acc + suite.tests.filter((test: any) => test.status === 'skipped').length, 0) || 0,
    duration: results.duration || 0,
    browsers: results.config?.projects?.map((project: any) => project.name) || [],
  };
  
  const summaryPath = path.join(process.cwd(), 'tests/e2e/reports/summary.json');
  fs.writeFileSync(summaryPath, JSON.stringify(summary, null, 2));
  
  // Generate human-readable summary
  const readableSummary = `
# Test Execution Summary

**Timestamp:** ${summary.timestamp}
**Total Tests:** ${summary.total}
**Passed:** ${summary.passed}
**Failed:** ${summary.failed}
**Skipped:** ${summary.skipped}
**Duration:** ${Math.round(summary.duration / 1000)}s
**Browsers:** ${summary.browsers.join(', ')}

## Success Rate: ${Math.round((summary.passed / summary.total) * 100)}%
`;
  
  const readablePath = path.join(process.cwd(), 'tests/e2e/reports/summary.md');
  fs.writeFileSync(readablePath, readableSummary);
}

async function cleanupTestDatabase() {
  console.log('🗄️  Cleaning up test database...');
  
  try {
    // Drop test database
    execSync('npm run db:drop:test', { stdio: 'inherit' });
    
    console.log('✅ Test database cleanup completed');
  } catch (error) {
    console.log('ℹ️  Database cleanup skipped (not configured)');
  }
}

async function archiveTestArtifacts() {
  console.log('📦 Archiving test artifacts...');
  
  const artifactDirs = [
    'tests/e2e/screenshots',
    'tests/e2e/videos',
    'tests/e2e/traces',
  ];
  
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const archiveDir = path.join(process.cwd(), 'tests/e2e/archives', timestamp);
  
  if (!fs.existsSync(archiveDir)) {
    fs.mkdirSync(archiveDir, { recursive: true });
  }
  
  for (const dir of artifactDirs) {
    const fullPath = path.join(process.cwd(), dir);
    if (fs.existsSync(fullPath)) {
      const files = fs.readdirSync(fullPath);
      if (files.length > 0) {
        const targetDir = path.join(archiveDir, path.basename(dir));
        fs.mkdirSync(targetDir, { recursive: true });
        
        for (const file of files) {
          const srcPath = path.join(fullPath, file);
          const destPath = path.join(targetDir, file);
          fs.copyFileSync(srcPath, destPath);
        }
      }
    }
  }
  
  console.log(`✅ Test artifacts archived to: ${archiveDir}`);
}

async function sendTestNotifications() {
  console.log('📧 Sending test notifications...');
  
  try {
    const summaryPath = path.join(process.cwd(), 'tests/e2e/reports/summary.json');
    
    if (!fs.existsSync(summaryPath)) {
      return;
    }
    
    const summary = JSON.parse(fs.readFileSync(summaryPath, 'utf-8'));
    
    // Send to Slack if configured
    if (process.env.SLACK_WEBHOOK_URL) {
      await sendSlackNotification(summary);
    }
    
    // Send to Discord if configured
    if (process.env.DISCORD_WEBHOOK_URL) {
      await sendDiscordNotification(summary);
    }
    
    // Send to email if configured
    if (process.env.EMAIL_NOTIFICATION_ENABLED) {
      await sendEmailNotification(summary);
    }
    
    console.log('✅ Test notifications sent');
  } catch (error) {
    console.error('❌ Failed to send test notifications:', error);
  }
}

async function sendSlackNotification(summary: any) {
  const webhook = process.env.SLACK_WEBHOOK_URL;
  if (!webhook) return;
  
  const status = summary.failed > 0 ? 'Failed' : 'Passed';
  const color = summary.failed > 0 ? 'danger' : 'good';
  
  const payload = {
    text: `E2E Test Results: ${status}`,
    attachments: [
      {
        color,
        fields: [
          { title: 'Total Tests', value: summary.total, short: true },
          { title: 'Passed', value: summary.passed, short: true },
          { title: 'Failed', value: summary.failed, short: true },
          { title: 'Duration', value: `${Math.round(summary.duration / 1000)}s`, short: true },
        ],
      },
    ],
  };
  
  await fetch(webhook, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(payload),
  });
}

async function sendDiscordNotification(summary: any) {
  const webhook = process.env.DISCORD_WEBHOOK_URL;
  if (!webhook) return;
  
  const status = summary.failed > 0 ? 'Failed' : 'Passed';
  const color = summary.failed > 0 ? 0xff0000 : 0x00ff00;
  
  const payload = {
    embeds: [
      {
        title: `E2E Test Results: ${status}`,
        color,
        fields: [
          { name: 'Total Tests', value: summary.total.toString(), inline: true },
          { name: 'Passed', value: summary.passed.toString(), inline: true },
          { name: 'Failed', value: summary.failed.toString(), inline: true },
          { name: 'Duration', value: `${Math.round(summary.duration / 1000)}s`, inline: true },
        ],
        timestamp: new Date().toISOString(),
      },
    ],
  };
  
  await fetch(webhook, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(payload),
  });
}

async function sendEmailNotification(summary: any) {
  // Email notification implementation would go here
  console.log('📧 Email notification would be sent here');
}

async function cleanupTemporaryFiles() {
  console.log('🧹 Cleaning up temporary files...');
  
  const tempDirs = [
    'tests/e2e/auth',
    'tests/e2e/data',
    '.playwright',
  ];
  
  for (const dir of tempDirs) {
    const fullPath = path.join(process.cwd(), dir);
    if (fs.existsSync(fullPath)) {
      // Only remove if it's a temporary directory
      if (dir.includes('temp') || dir.includes('.playwright')) {
        fs.rmSync(fullPath, { recursive: true, force: true });
      }
    }
  }
  
  console.log('✅ Temporary files cleaned up');
}

export default globalTeardown;