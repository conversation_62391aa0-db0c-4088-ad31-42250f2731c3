'use client';

import { motion } from 'framer-motion';
import { BookmarkIcon, LoaderIcon, UserIcon } from 'lucide-react';
import { useState } from 'react';

import { useAuth } from '@/contexts/AuthContext';
import { useSaveProject } from '@/hooks/useProjects';
import { Button } from '@/components/ui/button';
import { AuthModal } from '@/components/auth/AuthModal';

interface SaveCalculationButtonProps {
  formData: any;
  results: any;
  disabled?: boolean;
}

export function SaveCalculationButton({ formData, results, disabled }: SaveCalculationButtonProps) {
  const { user } = useAuth();
  const saveProject = useSaveProject();
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [authMode, setAuthMode] = useState<'signin' | 'signup'>('signin');
  const [showSaveDialog, setShowSaveDialog] = useState(false);
  const [projectName, setProjectName] = useState('');

  const handleSaveClick = () => {
    if (!user) {
      setAuthMode('signin');
      setShowAuthModal(true);
      return;
    }

    // Generate default project name
    const defaultName = `${formData.qualityTier} ${formData.floors + 1} Floor House - ${formData.location}`;
    setProjectName(defaultName);
    setShowSaveDialog(true);
  };

  const handleSaveProject = () => {
    if (!projectName.trim()) return;

    const saveData = {
      name: projectName.trim(),
      location: formData.location,
      area_sqft: formData.builtUpArea,
      floors: formData.floors,
      quality_tier: formData.qualityTier,
      calculation_data: {
        formData,
        results,
        calculatedAt: new Date().toISOString(),
        version: '1.0',
      },
    };

    saveProject.mutate(saveData, {
      onSuccess: () => {
        setShowSaveDialog(false);
        setProjectName('');
      },
    });
  };

  return (
    <>
      <Button
        onClick={handleSaveClick}
        disabled={disabled || saveProject.isPending}
        className="flex items-center gap-2"
        variant="outline"
      >
        {saveProject.isPending ? (
          <LoaderIcon className="h-4 w-4 animate-spin" />
        ) : user ? (
          <BookmarkIcon className="h-4 w-4" />
        ) : (
          <UserIcon className="h-4 w-4" />
        )}
        {user ? 'Save Project' : 'Sign In to Save'}
      </Button>

      {/* Auth Modal for guest users */}
      <AuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
        mode={authMode}
        onModeChange={setAuthMode}
        onSuccess={() => {
          setShowAuthModal(false);
          // Auto-trigger save after successful sign-in
          setTimeout(() => {
            const defaultName = `${formData.qualityTier} ${formData.floors + 1} Floor House - ${formData.location}`;
            setProjectName(defaultName);
            setShowSaveDialog(true);
          }, 500);
        }}
      />

      {/* Save Dialog */}
      {showSaveDialog && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 p-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            className="relative w-full max-w-md rounded-xl bg-white p-6 shadow-2xl"
          >
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Save Your Calculation
            </h3>

            <div className="space-y-4">
              <div>
                <label htmlFor="project-name" className="block text-sm font-medium text-gray-700 mb-1">
                  Project Name
                </label>
                <input
                  type="text"
                  id="project-name"
                  value={projectName}
                  onChange={(e) => setProjectName(e.target.value)}
                  className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                  placeholder="Enter project name..."
                  autoFocus
                />
              </div>

              <div className="bg-gray-50 rounded-lg p-3 text-sm text-gray-600">
                <p><strong>Location:</strong> {formData.location}</p>
                <p><strong>Area:</strong> {formData.builtUpArea.toLocaleString()} sq ft</p>
                <p><strong>Quality:</strong> {formData.qualityTier}</p>
                <p><strong>Total Cost:</strong> ₹{results.totalCost.toLocaleString()}</p>
              </div>

              <div className="flex gap-3">
                <Button
                  onClick={() => setShowSaveDialog(false)}
                  variant="outline"
                  className="flex-1"
                  disabled={saveProject.isPending}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleSaveProject}
                  disabled={!projectName.trim() || saveProject.isPending}
                  className="flex-1"
                >
                  {saveProject.isPending ? (
                    <LoaderIcon className="h-4 w-4 animate-spin mr-2" />
                  ) : (
                    <BookmarkIcon className="h-4 w-4 mr-2" />
                  )}
                  Save Project
                </Button>
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </>
  );
}