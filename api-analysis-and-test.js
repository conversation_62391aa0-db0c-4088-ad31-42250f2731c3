#!/usr/bin/env node

/**
 * Comprehensive API Analysis and Testing Framework
 * Nirmaan AI Construction Calculator
 * 
 * This script analyzes the API implementation and conducts comprehensive
 * testing through code analysis, mock testing, and validation checks
 */

const fs = require('fs');
const path = require('path');

class APIAnalysisFramework {
    constructor() {
        this.results = {
            endpoints: [],
            securityAnalysis: {},
            performanceAnalysis: {},
            validationAnalysis: {},
            rateLimit: {},
            errorHandling: {},
            recommendations: [],
            scores: {
                security: 0,
                performance: 0,
                validation: 0,
                errorHandling: 0,
                documentation: 0,
                overall: 0
            }
        };
        
        this.apiBasePath = '/mnt/d/real estate/Nirmaan_AI_cons_calc_Claude/src/app/api';
        this.findings = [];
    }

    // Analyze API endpoint structure and implementation
    async analyzeAPIStructure() {
        console.log('🔍 Analyzing API Structure...');
        
        const apiEndpoints = [
            {
                path: '/api/health',
                methods: ['GET'],
                file: 'health/route.ts',
                description: 'Health check endpoint',
                requiresAuth: false
            },
            {
                path: '/api/calculate',
                methods: ['GET', 'POST', 'OPTIONS'],
                file: 'calculate/route.ts',
                description: 'Construction cost calculation',
                requiresAuth: false
            },
            {
                path: '/api/monitoring',
                methods: ['GET'],
                file: 'monitoring/route.ts',
                description: 'System monitoring metrics',
                requiresAuth: false
            },
            {
                path: '/api/performance/metrics',
                methods: ['GET', 'POST'],
                file: 'performance/metrics/route.ts',
                description: 'Performance metrics collection',
                requiresAuth: false
            },
            {
                path: '/api/analytics/web-vitals',
                methods: ['GET', 'POST'],
                file: 'analytics/web-vitals/route.ts',
                description: 'Web vitals analytics',
                requiresAuth: false
            },
            {
                path: '/api/projects',
                methods: ['GET', 'DELETE'],
                file: 'projects/route.ts',
                description: 'Project management',
                requiresAuth: true
            },
            {
                path: '/api/projects/save',
                methods: ['POST'],
                file: 'projects/save/route.ts',
                description: 'Save project data',
                requiresAuth: true
            },
            {
                path: '/api/robots',
                methods: ['GET'],
                file: 'robots/route.ts',
                description: 'Robots.txt endpoint',
                requiresAuth: false
            },
            {
                path: '/api/sitemap',
                methods: ['GET'],
                file: 'sitemap/route.ts',
                description: 'XML sitemap',
                requiresAuth: false
            }
        ];

        for (const endpoint of apiEndpoints) {
            const filePath = path.join(this.apiBasePath, endpoint.file);
            if (fs.existsSync(filePath)) {
                const content = fs.readFileSync(filePath, 'utf8');
                const analysis = this.analyzeEndpointFile(content, endpoint);
                endpoint.analysis = analysis;
                endpoint.exists = true;
            } else {
                endpoint.exists = false;
                this.findings.push({
                    type: 'error',
                    severity: 'high',
                    endpoint: endpoint.path,
                    message: `Endpoint file not found: ${endpoint.file}`
                });
            }
        }

        this.results.endpoints = apiEndpoints;
        return apiEndpoints;
    }

    // Analyze individual endpoint file
    analyzeEndpointFile(content, endpoint) {
        const analysis = {
            hasErrorHandling: false,
            hasValidation: false,
            hasRateLimit: false,
            hasAuthentication: false,
            hasCORS: false,
            hasTypeScript: false,
            hasLogging: false,
            hasPerformanceMonitoring: false,
            hasDocumentation: false,
            httpMethods: [],
            securityIssues: [],
            performanceIssues: [],
            validationIssues: []
        };

        // Check for TypeScript usage
        analysis.hasTypeScript = content.includes('NextRequest') && content.includes('NextResponse');

        // Check for HTTP methods
        if (content.includes('export async function GET')) analysis.httpMethods.push('GET');
        if (content.includes('export async function POST')) analysis.httpMethods.push('POST');
        if (content.includes('export async function PUT')) analysis.httpMethods.push('PUT');
        if (content.includes('export async function DELETE')) analysis.httpMethods.push('DELETE');
        if (content.includes('export async function OPTIONS')) analysis.httpMethods.push('OPTIONS');

        // Check for error handling
        analysis.hasErrorHandling = content.includes('try {') && content.includes('catch') && 
                                   content.includes('NextResponse.json') && content.includes('status: 500');

        // Check for input validation
        analysis.hasValidation = content.includes('validate') || content.includes('sanitize') ||
                               content.includes('requiredFields') || content.includes('parseFloat') ||
                               content.includes('parseInt');

        // Check for rate limiting
        analysis.hasRateLimit = content.includes('rate') && (content.includes('limit') || content.includes('429'));

        // Check for authentication
        analysis.hasAuthentication = content.includes('auth') || content.includes('Authorization') ||
                                   content.includes('token') || content.includes('jwt');

        // Check for CORS
        analysis.hasCORS = content.includes('Access-Control-Allow') || content.includes('CORS');

        // Check for logging
        analysis.hasLogging = content.includes('console.log') || content.includes('console.error') ||
                            content.includes('console.info') || content.includes('console.warn');

        // Check for performance monitoring
        analysis.hasPerformanceMonitoring = content.includes('performance.now()') || 
                                          content.includes('Date.now()') ||
                                          content.includes('metrics') ||
                                          content.includes('timing');

        // Check for documentation
        analysis.hasDocumentation = content.includes('/**') || content.includes('description:') ||
                                  content.includes('@param') || content.includes('example');

        // Security analysis
        this.analyzeSecurityIssues(content, analysis, endpoint);

        // Performance analysis
        this.analyzePerformanceIssues(content, analysis, endpoint);

        // Validation analysis
        this.analyzeValidationIssues(content, analysis, endpoint);

        return analysis;
    }

    // Analyze security issues
    analyzeSecurityIssues(content, analysis, endpoint) {
        // Check for SQL injection vulnerabilities
        if (content.includes('query') && !content.includes('sanitize') && !content.includes('escape')) {
            analysis.securityIssues.push('Potential SQL injection vulnerability');
        }

        // Check for XSS protection
        if (content.includes('innerHTML') || content.includes('eval(')) {
            analysis.securityIssues.push('Potential XSS vulnerability');
        }

        // Check for proper authentication
        if (endpoint.requiresAuth && !analysis.hasAuthentication) {
            analysis.securityIssues.push('Missing authentication for protected endpoint');
        }

        // Check for rate limiting on public endpoints
        if (!endpoint.requiresAuth && endpoint.path.includes('calculate') && !analysis.hasRateLimit) {
            analysis.securityIssues.push('Missing rate limiting on calculation endpoint');
        }

        // Check for input validation
        if (!analysis.hasValidation && endpoint.methods.includes('POST')) {
            analysis.securityIssues.push('Missing input validation for POST endpoint');
        }

        // Check for CORS configuration
        if (!analysis.hasCORS && !endpoint.requiresAuth) {
            analysis.securityIssues.push('Missing CORS configuration');
        }
    }

    // Analyze performance issues
    analyzePerformanceIssues(content, analysis, endpoint) {
        // Check for caching headers
        if (!content.includes('Cache-Control') && endpoint.methods.includes('GET')) {
            analysis.performanceIssues.push('Missing caching headers for GET endpoint');
        }

        // Check for compression
        if (!content.includes('gzip') && !content.includes('compress')) {
            analysis.performanceIssues.push('No explicit compression handling');
        }

        // Check for pagination on list endpoints
        if (endpoint.path.includes('projects') && !content.includes('limit') && !content.includes('offset')) {
            analysis.performanceIssues.push('Missing pagination for list endpoint');
        }

        // Check for performance monitoring
        if (!analysis.hasPerformanceMonitoring) {
            analysis.performanceIssues.push('Missing performance monitoring');
        }

        // Check for async/await proper usage
        if (content.includes('async') && !content.includes('await')) {
            analysis.performanceIssues.push('Async function without await usage');
        }
    }

    // Analyze validation issues
    analyzeValidationIssues(content, analysis, endpoint) {
        // Check for type checking
        if (!content.includes('typeof') && !content.includes('instanceof') && endpoint.methods.includes('POST')) {
            analysis.validationIssues.push('Missing type checking for input data');
        }

        // Check for range validation
        if (endpoint.path.includes('calculate') && !content.includes('range') && !content.includes('min') && !content.includes('max')) {
            analysis.validationIssues.push('Missing range validation for numeric inputs');
        }

        // Check for sanitization
        if (!content.includes('sanitize') && !content.includes('trim') && endpoint.methods.includes('POST')) {
            analysis.validationIssues.push('Missing input sanitization');
        }

        // Check for required field validation
        if (!content.includes('required') && endpoint.methods.includes('POST')) {
            analysis.validationIssues.push('Missing required field validation');
        }
    }

    // Simulate API testing scenarios
    async simulateAPITests() {
        console.log('🧪 Simulating API Test Scenarios...');
        
        const testScenarios = [
            {
                name: 'Basic Calculation Test',
                endpoint: '/api/calculate',
                method: 'POST',
                input: {
                    builtUpArea: 1000,
                    floors: 1,
                    qualityTier: 'smart',
                    location: 'bangalore'
                },
                expectedStatus: 200,
                expectedFields: ['totalCost', 'costPerSqft', 'breakdown']
            },
            {
                name: 'Invalid Input Test',
                endpoint: '/api/calculate',
                method: 'POST',
                input: {
                    builtUpArea: 'invalid',
                    qualityTier: 'invalid'
                },
                expectedStatus: 400,
                expectedFields: ['error']
            },
            {
                name: 'Missing Required Fields',
                endpoint: '/api/calculate',
                method: 'POST',
                input: {
                    builtUpArea: 1000
                },
                expectedStatus: 400,
                expectedFields: ['error']
            },
            {
                name: 'Rate Limit Test',
                endpoint: '/api/calculate',
                method: 'POST',
                input: {
                    builtUpArea: 1000,
                    floors: 1,
                    qualityTier: 'smart',
                    location: 'bangalore'
                },
                expectedStatus: 429,
                simulateRateLimit: true
            },
            {
                name: 'Health Check Test',
                endpoint: '/api/health',
                method: 'GET',
                expectedStatus: 200,
                expectedFields: ['status', 'timestamp', 'checks']
            },
            {
                name: 'Large Payload Test',
                endpoint: '/api/calculate',
                method: 'POST',
                input: {
                    builtUpArea: 5000,
                    floors: 5,
                    qualityTier: 'luxury',
                    location: 'mumbai',
                    specialFeatures: Array(100).fill().map((_, i) => ({
                        name: `Feature ${i}`,
                        cost: 10000 * i,
                        description: 'Test feature'
                    }))
                },
                expectedStatus: 200
            }
        ];

        const testResults = [];

        for (const scenario of testScenarios) {
            const result = this.simulateTestScenario(scenario);
            testResults.push(result);
        }

        return testResults;
    }

    // Simulate individual test scenario
    simulateTestScenario(scenario) {
        const result = {
            name: scenario.name,
            endpoint: scenario.endpoint,
            method: scenario.method,
            passed: false,
            issues: [],
            recommendations: []
        };

        // Find endpoint analysis
        const endpointAnalysis = this.results.endpoints.find(ep => ep.path === scenario.endpoint);
        
        if (!endpointAnalysis) {
            result.issues.push('Endpoint not found in API structure');
            return result;
        }

        if (!endpointAnalysis.exists) {
            result.issues.push('Endpoint implementation file not found');
            return result;
        }

        // Check if method is supported
        if (!endpointAnalysis.analysis.httpMethods.includes(scenario.method)) {
            result.issues.push(`HTTP method ${scenario.method} not implemented`);
            return result;
        }

        // Validate based on scenario type
        if (scenario.name.includes('Invalid Input') || scenario.name.includes('Missing Required')) {
            if (!endpointAnalysis.analysis.hasValidation) {
                result.issues.push('Endpoint lacks proper input validation');
            } else {
                result.passed = true;
            }
        } else if (scenario.name.includes('Rate Limit')) {
            if (!endpointAnalysis.analysis.hasRateLimit) {
                result.issues.push('Endpoint lacks rate limiting');
            } else {
                result.passed = true;
            }
        } else if (scenario.name.includes('Large Payload')) {
            if (!endpointAnalysis.analysis.hasValidation) {
                result.issues.push('No payload size validation detected');
            } else {
                result.passed = true;
            }
        } else {
            // Basic functionality test
            if (endpointAnalysis.analysis.hasErrorHandling && 
                (scenario.method === 'GET' || endpointAnalysis.analysis.hasValidation)) {
                result.passed = true;
            } else {
                result.issues.push('Endpoint lacks proper error handling or validation');
            }
        }

        // Add recommendations based on issues found
        if (result.issues.length > 0) {
            result.recommendations.push('Implement comprehensive input validation');
            result.recommendations.push('Add proper error handling with appropriate status codes');
            result.recommendations.push('Implement rate limiting for public endpoints');
        }

        return result;
    }

    // Analyze security configuration
    analyzeSecurityConfiguration() {
        console.log('🔒 Analyzing Security Configuration...');
        
        const security = {
            rateLimit: {
                implemented: false,
                endpoints: [],
                configuration: {}
            },
            authentication: {
                implemented: false,
                endpoints: [],
                method: 'unknown'
            },
            validation: {
                implemented: false,
                endpoints: [],
                issues: []
            },
            cors: {
                implemented: false,
                configuration: {}
            },
            headers: {
                security: [],
                missing: []
            }
        };

        // Analyze each endpoint for security features
        this.results.endpoints.forEach(endpoint => {
            if (endpoint.analysis) {
                if (endpoint.analysis.hasRateLimit) {
                    security.rateLimit.implemented = true;
                    security.rateLimit.endpoints.push(endpoint.path);
                }

                if (endpoint.analysis.hasAuthentication) {
                    security.authentication.implemented = true;
                    security.authentication.endpoints.push(endpoint.path);
                }

                if (endpoint.analysis.hasValidation) {
                    security.validation.implemented = true;
                    security.validation.endpoints.push(endpoint.path);
                }

                if (endpoint.analysis.hasCORS) {
                    security.cors.implemented = true;
                }

                // Collect security issues
                security.validation.issues.push(...endpoint.analysis.securityIssues);
            }
        });

        // Security score calculation
        let securityScore = 0;
        if (security.rateLimit.implemented) securityScore += 20;
        if (security.authentication.implemented) securityScore += 20;
        if (security.validation.implemented) securityScore += 25;
        if (security.cors.implemented) securityScore += 15;
        if (security.validation.issues.length === 0) securityScore += 20;

        this.results.securityAnalysis = security;
        this.results.scores.security = securityScore;

        return security;
    }

    // Analyze performance characteristics
    analyzePerformanceCharacteristics() {
        console.log('⚡ Analyzing Performance Characteristics...');
        
        const performance = {
            caching: {
                implemented: false,
                endpoints: []
            },
            monitoring: {
                implemented: false,
                endpoints: []
            },
            optimization: {
                issues: [],
                recommendations: []
            },
            responseTime: {
                estimated: 'unknown',
                factors: []
            }
        };

        // Analyze performance aspects
        this.results.endpoints.forEach(endpoint => {
            if (endpoint.analysis) {
                // Check for performance monitoring
                if (endpoint.analysis.hasPerformanceMonitoring) {
                    performance.monitoring.implemented = true;
                    performance.monitoring.endpoints.push(endpoint.path);
                }

                // Collect performance issues
                performance.optimization.issues.push(...endpoint.analysis.performanceIssues);
            }
        });

        // Performance recommendations
        if (!performance.monitoring.implemented) {
            performance.optimization.recommendations.push('Implement comprehensive performance monitoring');
        }
        if (!performance.caching.implemented) {
            performance.optimization.recommendations.push('Add appropriate caching strategies');
        }
        performance.optimization.recommendations.push('Implement request/response compression');
        performance.optimization.recommendations.push('Add performance metrics collection');

        // Performance score
        let performanceScore = 0;
        if (performance.monitoring.implemented) performanceScore += 30;
        if (performance.caching.implemented) performanceScore += 25;
        if (performance.optimization.issues.length < 5) performanceScore += 25;
        performanceScore += Math.max(0, 20 - performance.optimization.issues.length);

        this.results.performanceAnalysis = performance;
        this.results.scores.performance = performanceScore;

        return performance;
    }

    // Generate comprehensive recommendations
    generateRecommendations() {
        console.log('💡 Generating Recommendations...');
        
        const recommendations = [];

        // Security recommendations
        if (this.results.scores.security < 80) {
            recommendations.push({
                category: 'Security',
                priority: 'High',
                items: [
                    'Implement rate limiting on all public endpoints',
                    'Add comprehensive input validation and sanitization',
                    'Configure proper CORS policies',
                    'Implement security headers (HSTS, CSP, etc.)',
                    'Add authentication for protected endpoints'
                ]
            });
        }

        // Performance recommendations
        if (this.results.scores.performance < 70) {
            recommendations.push({
                category: 'Performance',
                priority: 'Medium',
                items: [
                    'Implement response caching strategies',
                    'Add performance monitoring and metrics',
                    'Optimize database queries and connections',
                    'Implement request/response compression',
                    'Add pagination for list endpoints'
                ]
            });
        }

        // Validation recommendations
        recommendations.push({
            category: 'Validation',
            priority: 'High',
            items: [
                'Implement schema-based validation (Zod, Joi)',
                'Add comprehensive type checking',
                'Validate numeric ranges and constraints',
                'Sanitize all user inputs',
                'Implement business logic validation'
            ]
        });

        // Error handling recommendations
        recommendations.push({
            category: 'Error Handling',
            priority: 'Medium',
            items: [
                'Standardize error response format',
                'Add detailed error logging',
                'Implement graceful error recovery',
                'Add user-friendly error messages',
                'Implement error tracking and alerting'
            ]
        });

        // Documentation recommendations
        recommendations.push({
            category: 'Documentation',
            priority: 'Low',
            items: [
                'Add comprehensive API documentation',
                'Include request/response examples',
                'Document error codes and responses',
                'Add authentication requirements',
                'Include rate limiting information'
            ]
        });

        // Testing recommendations
        recommendations.push({
            category: 'Testing',
            priority: 'High',
            items: [
                'Implement comprehensive unit tests',
                'Add integration testing suite',
                'Include load testing scenarios',
                'Add security testing (OWASP)',
                'Implement automated testing in CI/CD'
            ]
        });

        this.results.recommendations = recommendations;
        return recommendations;
    }

    // Calculate overall scores
    calculateOverallScore() {
        const weights = {
            security: 0.25,
            performance: 0.20,
            validation: 0.20,
            errorHandling: 0.15,
            documentation: 0.20
        };

        // Calculate individual scores based on analysis
        this.results.scores.validation = this.calculateValidationScore();
        this.results.scores.errorHandling = this.calculateErrorHandlingScore();
        this.results.scores.documentation = this.calculateDocumentationScore();

        // Calculate weighted overall score
        this.results.scores.overall = Math.round(
            this.results.scores.security * weights.security +
            this.results.scores.performance * weights.performance +
            this.results.scores.validation * weights.validation +
            this.results.scores.errorHandling * weights.errorHandling +
            this.results.scores.documentation * weights.documentation
        );
    }

    calculateValidationScore() {
        let score = 0;
        let totalEndpoints = 0;
        let validatedEndpoints = 0;

        this.results.endpoints.forEach(endpoint => {
            if (endpoint.methods.includes('POST') || endpoint.methods.includes('PUT')) {
                totalEndpoints++;
                if (endpoint.analysis && endpoint.analysis.hasValidation) {
                    validatedEndpoints++;
                }
            }
        });

        if (totalEndpoints > 0) {
            score = Math.round((validatedEndpoints / totalEndpoints) * 100);
        }

        return score;
    }

    calculateErrorHandlingScore() {
        let score = 0;
        let totalEndpoints = this.results.endpoints.length;
        let handledEndpoints = 0;

        this.results.endpoints.forEach(endpoint => {
            if (endpoint.analysis && endpoint.analysis.hasErrorHandling) {
                handledEndpoints++;
            }
        });

        if (totalEndpoints > 0) {
            score = Math.round((handledEndpoints / totalEndpoints) * 100);
        }

        return score;
    }

    calculateDocumentationScore() {
        let score = 0;
        let totalEndpoints = this.results.endpoints.length;
        let documentedEndpoints = 0;

        this.results.endpoints.forEach(endpoint => {
            if (endpoint.analysis && endpoint.analysis.hasDocumentation) {
                documentedEndpoints++;
            }
        });

        if (totalEndpoints > 0) {
            score = Math.round((documentedEndpoints / totalEndpoints) * 60); // Max 60 for inline docs
        }

        // Add score for external documentation
        if (fs.existsSync('/mnt/d/real estate/Nirmaan_AI_cons_calc_Claude/API_DOCUMENTATION.md')) {
            score += 40;
        }

        return Math.min(score, 100);
    }

    // Generate comprehensive report
    generateComprehensiveReport() {
        const report = {
            summary: {
                totalEndpoints: this.results.endpoints.length,
                implementedEndpoints: this.results.endpoints.filter(ep => ep.exists).length,
                securityScore: this.results.scores.security,
                performanceScore: this.results.scores.performance,
                validationScore: this.results.scores.validation,
                errorHandlingScore: this.results.scores.errorHandling,
                documentationScore: this.results.scores.documentation,
                overallScore: this.results.scores.overall,
                grade: this.calculateGrade(this.results.scores.overall)
            },
            endpoints: this.results.endpoints.map(ep => ({
                path: ep.path,
                methods: ep.methods,
                description: ep.description,
                requiresAuth: ep.requiresAuth,
                exists: ep.exists,
                implementation: ep.analysis ? {
                    hasErrorHandling: ep.analysis.hasErrorHandling,
                    hasValidation: ep.analysis.hasValidation,
                    hasRateLimit: ep.analysis.hasRateLimit,
                    hasCORS: ep.analysis.hasCORS,
                    hasPerformanceMonitoring: ep.analysis.hasPerformanceMonitoring,
                    securityIssues: ep.analysis.securityIssues.length,
                    performanceIssues: ep.analysis.performanceIssues.length,
                    validationIssues: ep.analysis.validationIssues.length
                } : null
            })),
            security: this.results.securityAnalysis,
            performance: this.results.performanceAnalysis,
            recommendations: this.results.recommendations,
            testResults: this.results.testResults || [],
            findings: this.findings,
            timestamp: new Date().toISOString()
        };

        return report;
    }

    calculateGrade(score) {
        if (score >= 90) return 'A';
        if (score >= 80) return 'B';
        if (score >= 70) return 'C';
        if (score >= 60) return 'D';
        return 'F';
    }

    // Print comprehensive summary
    printSummary(report) {
        console.log('\n' + '='.repeat(80));
        console.log('🏆 COMPREHENSIVE API INTEGRATION TESTING REPORT');
        console.log('='.repeat(80));
        
        console.log(`\n📊 OVERALL ASSESSMENT:`);
        console.log(`Overall Score: ${report.summary.overallScore}/100 (Grade: ${report.summary.grade})`);
        console.log(`Total Endpoints: ${report.summary.totalEndpoints}`);
        console.log(`Implemented: ${report.summary.implementedEndpoints}/${report.summary.totalEndpoints}`);
        
        console.log(`\n📈 DETAILED SCORES:`);
        console.log(`🔒 Security: ${report.summary.securityScore}/100`);
        console.log(`⚡ Performance: ${report.summary.performanceScore}/100`);
        console.log(`✅ Validation: ${report.summary.validationScore}/100`);
        console.log(`🛠️  Error Handling: ${report.summary.errorHandlingScore}/100`);
        console.log(`📚 Documentation: ${report.summary.documentationScore}/100`);
        
        console.log(`\n🎯 ENDPOINT ANALYSIS:`);
        report.endpoints.forEach(endpoint => {
            const status = endpoint.exists ? '✅' : '❌';
            const auth = endpoint.requiresAuth ? '🔐' : '🌐';
            console.log(`${status} ${auth} ${endpoint.path} [${endpoint.methods.join(', ')}]`);
            
            if (endpoint.implementation) {
                const impl = endpoint.implementation;
                const features = [];
                if (impl.hasValidation) features.push('Validation');
                if (impl.hasErrorHandling) features.push('Error Handling');
                if (impl.hasRateLimit) features.push('Rate Limit');
                if (impl.hasPerformanceMonitoring) features.push('Monitoring');
                
                console.log(`    Features: ${features.join(', ') || 'None'}`);
                
                if (impl.securityIssues > 0 || impl.performanceIssues > 0 || impl.validationIssues > 0) {
                    console.log(`    Issues: Security(${impl.securityIssues}) Performance(${impl.performanceIssues}) Validation(${impl.validationIssues})`);
                }
            }
        });
        
        console.log(`\n💡 TOP RECOMMENDATIONS:`);
        const topRecommendations = report.recommendations
            .filter(rec => rec.priority === 'High')
            .slice(0, 3);
            
        topRecommendations.forEach((rec, index) => {
            console.log(`${index + 1}. ${rec.category}: ${rec.items[0]}`);
        });
        
        console.log(`\n🚨 CRITICAL FINDINGS:`);
        const criticalFindings = this.findings.filter(f => f.severity === 'high');
        if (criticalFindings.length > 0) {
            criticalFindings.forEach((finding, index) => {
                console.log(`${index + 1}. ${finding.endpoint || 'Global'}: ${finding.message}`);
            });
        } else {
            console.log('No critical issues found.');
        }
        
        console.log('\n' + '='.repeat(80));
    }

    // Save report to file
    saveReport(report) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `comprehensive-api-integration-test-report-${timestamp}.json`;
        const filepath = path.join(process.cwd(), filename);
        
        fs.writeFileSync(filepath, JSON.stringify(report, null, 2));
        console.log(`\n📄 Detailed report saved to: ${filepath}`);
        
        return filepath;
    }

    // Main execution method
    async runComprehensiveAnalysis() {
        console.log('🚀 Starting Comprehensive API Integration Analysis...');
        console.log('=' * 80);
        
        try {
            // 1. Analyze API structure
            await this.analyzeAPIStructure();
            
            // 2. Simulate API tests
            this.results.testResults = await this.simulateAPITests();
            
            // 3. Analyze security
            this.analyzeSecurityConfiguration();
            
            // 4. Analyze performance
            this.analyzePerformanceCharacteristics();
            
            // 5. Generate recommendations
            this.generateRecommendations();
            
            // 6. Calculate scores
            this.calculateOverallScore();
            
            // 7. Generate comprehensive report
            const report = this.generateComprehensiveReport();
            
            // 8. Print summary
            this.printSummary(report);
            
            // 9. Save detailed report
            this.saveReport(report);
            
            return report;
            
        } catch (error) {
            console.error('❌ Analysis failed:', error);
            throw error;
        }
    }
}

// Run the analysis if this file is executed directly
if (require.main === module) {
    const analyzer = new APIAnalysisFramework();
    analyzer.runComprehensiveAnalysis()
        .then(report => {
            const grade = report.summary.grade;
            const exitCode = grade === 'F' ? 1 : 0;
            process.exit(exitCode);
        })
        .catch(error => {
            console.error('❌ Comprehensive analysis failed:', error);
            process.exit(1);
        });
}

module.exports = APIAnalysisFramework;