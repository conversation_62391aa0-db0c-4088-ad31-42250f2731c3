/**
 * MEP (Mechanical, Electrical, Plumbing) Cost Calculations
 * Based on Indian electrical and plumbing standards
 */

import type { CalculationInput, CategoryCost, SubCategory } from '../types';
import {
  COST_BREAKDOWN_PERCENTAGES,
  QUALITY_TIER_SPECS,
} from '../constants';

export function calculateMEPCost(input: CalculationInput): CategoryCost {
  const { builtUpArea, floors, qualityTier, hasBasement = false } = input;

  // MEP rates per sqft by quality tier (Electrical + Plumbing + HVAC)
  const mepBaseRates = {
    smart: 360, // ₹360/sqft for Smart Choice MEP
    premium: 500, // ₹500/sqft for Premium Selection MEP
    luxury: 700, // ₹700/sqft for Luxury Collection MEP
  };

  // Calculate total area including all floors and basement
  let totalArea = builtUpArea * (floors + 1); // +1 for ground floor
  if (hasBasement) {
    totalArea += builtUpArea * 0.7; // Basement MEP is 70% of normal area
  }

  // Get base MEP rate for quality tier
  const mepCostPerSqft = mepBaseRates[qualityTier];

  // Total MEP cost
  const totalMEPCost = totalArea * mepCostPerSqft;

  // Calculate sub-categories for MEP work
  const subCategories: SubCategory[] = [
    {
      name: 'Electrical Work',
      amount: Math.round(totalMEPCost * 0.5),
      percentage: 50,
      description: getElectricalDescription(qualityTier),
    },
    {
      name: 'Plumbing',
      amount: Math.round(totalMEPCost * 0.35),
      percentage: 35,
      description: getPlumbingDescription(qualityTier),
    },
    {
      name: 'HVAC & Ventilation',
      amount: Math.round(totalMEPCost * 0.15),
      percentage: 15,
      description: 'Exhaust fans, ventilation, and basic HVAC provisions',
    },
  ];

  return {
    amount: Math.round(totalMEPCost),
    percentage: COST_BREAKDOWN_PERCENTAGES.mep * 100,
    subCategories,
  };
}

function getElectricalDescription(
  qualityTier: 'smart' | 'premium' | 'luxury'
): string {
  const specs = QUALITY_TIER_SPECS[qualityTier];
  return `${specs.electrical} switches, copper wiring, distribution board, earthing`;
}

function getPlumbingDescription(
  qualityTier: 'smart' | 'premium' | 'luxury'
): string {
  const pipeSpecs = {
    smart: 'CPVC pipes, basic fittings',
    premium: 'CPVC/PPR pipes, quality fittings',
    luxury: 'PPR pipes, premium fittings',
  };
  return pipeSpecs[qualityTier];
}

/**
 * Calculate electrical work cost based on points and quality
 */
export function calculateElectricalCost(
  area: number,
  qualityTier: 'smart' | 'premium' | 'luxury',
  floors: number
): {
  points: number;
  cost: number;
  breakdown: {
    wiring: number;
    switches: number;
    distribution: number;
    earthing: number;
  };
} {
  // Electrical points per sqft (industry standard)
  const pointsPerSqft = 1.2;
  const totalArea = area * (floors + 1);
  const totalPoints = Math.round(totalArea * pointsPerSqft);

  // Cost per electrical point by quality tier
  const costPerPoint = {
    smart: 350, // Basic switches, copper wire
    premium: 550, // Modular switches, quality wire
    luxury: 850, // Premium switches, heavy duty wire
  };

  const baseCost = totalPoints * costPerPoint[qualityTier];

  // Distribution board cost (varies by floors and points)
  const distributionCost = Math.max(8000, totalPoints * 15) + floors * 2000;

  // Earthing cost (one-time for the building)
  const earthingCost = 5000 + floors * 1500;

  const breakdown = {
    wiring: Math.round(baseCost * 0.6), // 60% for wiring and points
    switches: Math.round(baseCost * 0.25), // 25% for switches and sockets
    distribution: distributionCost,
    earthing: earthingCost,
  };

  const totalCost = Object.values(breakdown).reduce(
    (sum, cost) => sum + cost,
    0
  );

  return {
    points: totalPoints,
    cost: totalCost,
    breakdown,
  };
}

/**
 * Calculate plumbing cost including water supply and drainage
 */
export function calculatePlumbingCost(
  area: number,
  qualityTier: 'smart' | 'premium' | 'luxury',
  floors: number,
  bathrooms: number = 2
): {
  cost: number;
  breakdown: {
    waterSupply: number;
    drainage: number;
    fixtures: number;
    overhead: number;
  };
} {
  const totalArea = area * (floors + 1);

  // Base plumbing cost per sqft
  const baseCostPerSqft = {
    smart: 65, // CPVC pipes, basic fittings
    premium: 95, // PPR pipes, better fittings
    luxury: 135, // Premium pipes and fittings
  };

  const baseCost = totalArea * baseCostPerSqft[qualityTier];

  // Additional costs
  const overheadTankCost = {
    smart: 15000, // Basic plastic tank with pump
    premium: 25000, // Better tank with good pump
    luxury: 40000, // Premium tank with high-end pump
  };

  // Bathroom fixtures cost per bathroom
  const fixturesCostPerBathroom = {
    smart: 8000, // Basic sanitary ware
    premium: 15000, // Branded sanitary ware
    luxury: 25000, // Premium branded fixtures
  };

  const breakdown = {
    waterSupply: Math.round(baseCost * 0.45), // 45% for water supply lines
    drainage: Math.round(baseCost * 0.35), // 35% for drainage
    fixtures: bathrooms * fixturesCostPerBathroom[qualityTier],
    overhead: overheadTankCost[qualityTier],
  };

  const totalCost = Object.values(breakdown).reduce(
    (sum, cost) => sum + cost,
    0
  );

  return {
    cost: totalCost,
    breakdown,
  };
}

/**
 * Calculate HVAC cost for different quality tiers
 */
export function calculateHVACCost(
  area: number,
  qualityTier: 'smart' | 'premium' | 'luxury',
  rooms: number = 4
): number {
  const hvacOptions = {
    smart: {
      costPerRoom: 2500, // Exhaust fans only
      description: 'Exhaust fans in kitchen and bathrooms',
    },
    premium: {
      costPerRoom: 5000, // Exhaust + basic AC provisions
      description: 'Exhaust fans + AC points and provisions',
    },
    luxury: {
      costPerRoom: 8500, // Full HVAC provisions
      description: 'Complete HVAC provisions with ducting',
    },
  };

  return rooms * hvacOptions[qualityTier].costPerRoom;
}
