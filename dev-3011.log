
> clarity-engine@1.0.0 dev
> next dev

   [1m[38;2;173;127;168m▲ Next.js 15.3.5[39m[22m
   - Local:        http://localhost:3011
   - Network:      http://**************:3011
   - Environments: .env.local

 [32m[1m✓[22m[39m Starting...
 [32m[1m✓[22m[39m Ready in 40.6s
 [37m[1m○[22m[39m Compiling /calculator ...
 [32m[1m✓[22m[39m Compiled /calculator in 17.5s (2411 modules)
🚀 Environment Configuration Loaded:
   Environment: development
   Analytics: ❌
   Error Tracking: ❌
   Performance Monitoring: ✅
   Rate Limit: 100 requests/60000ms
🚀 Environment Configuration Loaded:
   Environment: development
   Analytics: ❌
   Error Tracking: ❌
   Performance Monitoring: ✅
   Rate Limit: 100 requests/60000ms
 GET /calculator [32m200[39m in 20730ms
 [37m[1m○[22m[39m Compiling /api/calculate ...
 [32m[1m✓[22m[39m Compiled /api/calculate in 4.2s (2439 modules)
Calculation metrics: {
  event: [32m'calculation_completed'[39m,
  requestId: [32m'calc_1752671335842_9auvsl9y8z9'[39m,
  timestamp: [32m'2025-07-16T13:08:55.844Z'[39m,
  input: {
    builtUpArea: [33m1000[39m,
    floors: [33m1[39m,
    qualityTier: [32m'smart'[39m,
    location: [32m'delhi'[39m,
    hasSpecialFeatures: [33mfalse[39m
  },
  result: { totalCost: [33m4029570[39m, costPerSqft: [33m2015[39m, totalBuiltUpArea: [33m2000[39m },
  performance: {
    validationTime: [33m0.1054279999807477[39m,
    calculationTime: [33m0.7363210000039544[39m,
    totalTime: [33m1.6369469999917783[39m,
    inputSize: [33m79[39m,
    outputSize: [33m5840[39m
  },
  client: { ip: [32m'::1'[39m, userAgent: [32m'browser'[39m }
}
 POST /api/calculate [32m200[39m in 5040ms
🚀 Environment Configuration Loaded:
   Environment: development
   Analytics: ❌
   Error Tracking: ❌
   Performance Monitoring: ✅
   Rate Limit: 100 requests/60000ms
🚀 Environment Configuration Loaded:
   Environment: development
   Analytics: ❌
   Error Tracking: ❌
   Performance Monitoring: ✅
   Rate Limit: 100 requests/60000ms
 GET /calculator [32m200[39m in 452ms
 GET /calculator [32m200[39m in 201ms
 GET /calculator [32m200[39m in 128ms
 GET /calculator [32m200[39m in 157ms
 GET /calculator [32m200[39m in 179ms
Calculation metrics: {
  event: [32m'calculation_completed'[39m,
  requestId: [32m'calc_1752671706399_f6fvsog0j8v'[39m,
  timestamp: [32m'2025-07-16T13:15:06.401Z'[39m,
  input: {
    builtUpArea: [33m1000[39m,
    floors: [33m1[39m,
    qualityTier: [32m'smart'[39m,
    location: [32m'delhi'[39m,
    hasSpecialFeatures: [33mfalse[39m
  },
  result: { totalCost: [33m4029570[39m, costPerSqft: [33m2015[39m, totalBuiltUpArea: [33m2000[39m },
  performance: {
    validationTime: [33m0.14216000004671514[39m,
    calculationTime: [33m0.8994630000088364[39m,
    totalTime: [33m1.9150590000208467[39m,
    inputSize: [33m79[39m,
    outputSize: [33m5839[39m
  },
  client: { ip: [32m'::1'[39m, userAgent: [32m'browser'[39m }
}
 POST /api/calculate [32m200[39m in 114ms
 GET /calculator [32m200[39m in 150ms
 GET /calculator [32m200[39m in 170ms
 GET /calculator [32m200[39m in 151ms
 [31m[1m⨯[22m[39m [Error: ENOENT: no such file or directory, open '/mnt/d/real estate/Nirmaan_AI_cons_calc_Claude/.next/server/app-paths-manifest.json'] {
  errno: [33m-2[39m,
  code: [32m'ENOENT'[39m,
  syscall: [32m'open'[39m,
  path: [32m'/mnt/d/real estate/Nirmaan_AI_cons_calc_Claude/.next/server/app-paths-manifest.json'[39m,
  page: [32m'/calculator'[39m
}
 [37m[1m○[22m[39m Compiling /_error ...
 [32m[1m✓[22m[39m Compiled /_error in 2.5s (2732 modules)
 GET /calculator [31m500[39m in 31691ms
[?25h
