import Link from 'next/link';
import { Gith<PERSON>, Twitter, Linkedin, Mail } from 'lucide-react';

export function Footer() {
  const currentYear = new Date().getFullYear();

  const quickLinks = [
    { href: '/about', label: 'About' },
    { href: '/contact', label: 'Contact' },
    { href: '/privacy', label: 'Privacy Policy' },
    { href: '/terms', label: 'Terms of Service' },
  ];

  const socialLinks = [
    { href: '#', label: 'GitHub', icon: Github },
    { href: '#', label: 'Twitter', icon: Twitter },
    { href: '#', label: 'LinkedIn', icon: Linkedin },
    { href: '#', label: 'Email', icon: Mail },
  ];

  return (
    <footer className='border-t bg-background'>
      <div className='container py-8 md:py-12'>
        <div className='grid grid-cols-1 md:grid-cols-3 gap-8'>
          {/* Company Info */}
          <div className='space-y-4'>
            <div className='flex items-center space-x-2'>
              <div className='h-8 w-8 rounded bg-primary flex items-center justify-center'>
                <span className='text-primary-foreground font-bold text-lg'>
                  C
                </span>
              </div>
              <span className='font-bold text-xl'>Clarity Engine</span>
            </div>
            <p className='text-sm text-muted-foreground max-w-xs'>
              India's most trusted construction intelligence platform,
              empowering families to build their dream homes with complete
              financial clarity.
            </p>
          </div>

          {/* Quick Links */}
          <div className='space-y-4'>
            <h3 className='text-sm font-semibold'>Quick Links</h3>
            <ul className='space-y-2'>
              {quickLinks.map(link => (
                <li key={link.href}>
                  <Link
                    href={link.href}
                    className='text-sm text-muted-foreground hover:text-primary transition-colors'
                  >
                    {link.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Social Links */}
          <div className='space-y-4'>
            <h3 className='text-sm font-semibold'>Connect With Us</h3>
            <div className='flex space-x-4'>
              {socialLinks.map(social => (
                <Link
                  key={social.label}
                  href={social.href}
                  className='text-muted-foreground hover:text-primary transition-colors'
                  aria-label={social.label}
                >
                  <social.icon className='h-5 w-5' />
                </Link>
              ))}
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className='mt-8 pt-8 border-t flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0'>
          <p className='text-sm text-muted-foreground'>
            © {currentYear} Clarity Engine. All rights reserved.
          </p>
          <p className='text-sm text-muted-foreground'>
            Building India's Construction Future
          </p>
        </div>
      </div>
    </footer>
  );
}
