import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

// Import the function we're testing
function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

describe('Utility Functions', () => {
  describe('cn (className merger)', () => {
    it('should merge simple class names', () => {
      const result = cn('px-4', 'py-2', 'bg-blue-500');
      expect(result).toBe('px-4 py-2 bg-blue-500');
    });

    it('should handle conditional classes', () => {
      const isActive = true;
      const isDisabled = false;
      
      const result = cn(
        'base-class',
        isActive && 'active-class',
        isDisabled && 'disabled-class'
      );
      
      expect(result).toBe('base-class active-class');
    });

    it('should merge conflicting Tailwind classes', () => {
      const result = cn('px-4', 'px-6');
      expect(result).toBe('px-6');
    });

    it('should handle complex Tailwind merges', () => {
      const result = cn(
        'bg-red-500 text-white',
        'bg-blue-500', // Should override bg-red-500
        'text-black'   // Should override text-white
      );
      
      expect(result).toBe('text-white bg-blue-500 text-black');
    });

    it('should handle arrays of classes', () => {
      const baseClasses = ['flex', 'items-center'];
      const conditionalClasses = ['justify-between', 'space-x-4'];
      
      const result = cn(baseClasses, conditionalClasses);
      expect(result).toBe('flex items-center justify-between space-x-4');
    });

    it('should handle objects with boolean values', () => {
      const result = cn({
        'active': true,
        'disabled': false,
        'loading': true,
      });
      
      expect(result).toBe('active loading');
    });

    it('should handle mixed input types', () => {
      const result = cn(
        'base',
        ['array', 'classes'],
        { conditional: true, ignored: false },
        'additional'
      );
      
      expect(result).toBe('base array classes conditional additional');
    });

    it('should handle empty inputs', () => {
      expect(cn()).toBe('');
      expect(cn('')).toBe('');
      expect(cn(null)).toBe('');
      expect(cn(undefined)).toBe('');
    });

    it('should handle responsive classes', () => {
      const result = cn(
        'text-sm',
        'md:text-base',
        'lg:text-lg',
        'md:text-xl' // Should override md:text-base
      );
      
      expect(result).toBe('text-sm md:text-base lg:text-lg md:text-xl');
    });

    it('should handle hover and focus states', () => {
      const result = cn(
        'bg-gray-100',
        'hover:bg-gray-200',
        'focus:bg-gray-300',
        'bg-white' // Should override bg-gray-100
      );
      
      expect(result).toBe('bg-gray-100 hover:bg-gray-200 focus:bg-gray-300 bg-white');
    });

    it('should handle complex component styling', () => {
      const variant = 'primary' as 'primary' | 'secondary';
      const size = 'lg' as 'sm' | 'md' | 'lg';
      const disabled = false;
      
      const buttonStyles = cn(
        'inline-flex items-center justify-center rounded-md font-medium transition-colors',
        {
          // Variants
          'bg-blue-600 text-white hover:bg-blue-700': variant === 'primary',
          'bg-gray-200 text-gray-900 hover:bg-gray-300': variant === 'secondary',
          
          // Sizes
          'h-10 px-4 py-2 text-sm': size === 'sm',
          'h-11 px-8 py-2': size === 'md',
          'h-12 px-8 py-3 text-lg': size === 'lg',
          
          // States
          'opacity-50 cursor-not-allowed': disabled,
        }
      );
      
      expect(buttonStyles).toContain('bg-blue-600');
      expect(buttonStyles).toContain('h-12');
      expect(buttonStyles).not.toContain('opacity-50');
    });
  });

  describe('formatCurrency', () => {
    const formatCurrency = (amount: number): string => {
      return new Intl.NumberFormat('en-IN', {
        style: 'currency',
        currency: 'INR',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
      }).format(amount);
    };

    it('should format Indian currency correctly', () => {
      expect(formatCurrency(1000)).toBe('₹1,000');
      expect(formatCurrency(100000)).toBe('₹1,00,000');
      expect(formatCurrency(1000000)).toBe('₹10,00,000');
    });

    it('should handle decimal values', () => {
      expect(formatCurrency(1234.56)).toBe('₹1,235'); // Rounded
      expect(formatCurrency(999.99)).toBe('₹1,000');
    });

    it('should handle large numbers', () => {
      expect(formatCurrency(50000000)).toBe('₹5,00,00,000');
      expect(formatCurrency(100000000)).toBe('₹10,00,00,000');
    });

    it('should handle zero and negative values', () => {
      expect(formatCurrency(0)).toBe('₹0');
      expect(formatCurrency(-1000)).toBe('-₹1,000');
    });
  });

  describe('formatArea', () => {
    const formatArea = (area: number, unit: string = 'sq ft'): string => {
      return `${area.toLocaleString('en-IN')} ${unit}`;
    };

    it('should format area with default unit', () => {
      expect(formatArea(1000)).toBe('1,000 sq ft');
      expect(formatArea(2500)).toBe('2,500 sq ft');
    });

    it('should format area with custom unit', () => {
      expect(formatArea(100, 'sq m')).toBe('100 sq m');
      expect(formatArea(1234, 'acres')).toBe('1,234 acres');
    });

    it('should handle large areas', () => {
      expect(formatArea(50000)).toBe('50,000 sq ft');
    });
  });

  describe('calculateCostPerSqFt', () => {
    const calculateCostPerSqFt = (totalCost: number, area: number): number => {
      return Math.round(totalCost / area);
    };

    it('should calculate cost per square foot correctly', () => {
      expect(calculateCostPerSqFt(1000000, 500)).toBe(2000);
      expect(calculateCostPerSqFt(2500000, 1000)).toBe(2500);
    });

    it('should round to nearest integer', () => {
      expect(calculateCostPerSqFt(1234567, 500)).toBe(2469); // 2469.134 rounded
      expect(calculateCostPerSqFt(999999, 400)).toBe(2500); // 2499.9975 rounded
    });

    it('should handle decimal areas', () => {
      expect(calculateCostPerSqFt(1000000, 400.5)).toBe(2497); // 2496.88 rounded
    });
  });

  describe('slugify', () => {
    const slugify = (text: string): string => {
      return text
        .toLowerCase()
        .trim()
        .replace(/[^\w\s-]/g, '')
        .replace(/[\s_-]+/g, '-')
        .replace(/^-+|-+$/g, '');
    };

    it('should convert text to URL-friendly slug', () => {
      expect(slugify('Hello World')).toBe('hello-world');
      expect(slugify('Smart Choice Premium')).toBe('smart-choice-premium');
    });

    it('should handle special characters', () => {
      expect(slugify('Test & Example!')).toBe('test-example');
      expect(slugify('Price: $1,000')).toBe('price-1000');
    });

    it('should handle multiple spaces and hyphens', () => {
      expect(slugify('  multiple   spaces  ')).toBe('multiple-spaces');
      expect(slugify('already-has-hyphens')).toBe('already-has-hyphens');
    });

    it('should handle empty and edge cases', () => {
      expect(slugify('')).toBe('');
      expect(slugify('   ')).toBe('');
      expect(slugify('---')).toBe('');
    });
  });

  describe('debounce', () => {
    const debounce = <T extends (...args: any[]) => any>(
      func: T,
      wait: number
    ): ((...args: Parameters<T>) => void) => {
      let timeout: NodeJS.Timeout;
      
      return (...args: Parameters<T>) => {
        clearTimeout(timeout);
        timeout = setTimeout(() => func(...args), wait);
      };
    };

    beforeEach(() => {
      vi.useFakeTimers();
    });

    afterEach(() => {
      vi.useRealTimers();
    });

    it('should delay function execution', () => {
      const mockFn = vi.fn();
      const debouncedFn = debounce(mockFn, 300);
      
      debouncedFn('test');
      expect(mockFn).not.toHaveBeenCalled();
      
      vi.advanceTimersByTime(300);
      expect(mockFn).toHaveBeenCalledWith('test');
    });

    it('should cancel previous calls', () => {
      const mockFn = vi.fn();
      const debouncedFn = debounce(mockFn, 300);
      
      debouncedFn('first');
      debouncedFn('second');
      debouncedFn('third');
      
      vi.advanceTimersByTime(300);
      
      expect(mockFn).toHaveBeenCalledTimes(1);
      expect(mockFn).toHaveBeenCalledWith('third');
    });

    it('should handle multiple arguments', () => {
      const mockFn = vi.fn();
      const debouncedFn = debounce(mockFn, 100);
      
      debouncedFn('arg1', 'arg2', 'arg3');
      
      vi.advanceTimersByTime(100);
      
      expect(mockFn).toHaveBeenCalledWith('arg1', 'arg2', 'arg3');
    });
  });

  describe('generateId', () => {
    const generateId = (prefix: string = ''): string => {
      const timestamp = Date.now().toString(36);
      const random = Math.random().toString(36).substr(2, 5);
      return prefix ? `${prefix}-${timestamp}-${random}` : `${timestamp}-${random}`;
    };

    it('should generate unique IDs', () => {
      const id1 = generateId();
      const id2 = generateId();
      
      expect(id1).not.toBe(id2);
      expect(typeof id1).toBe('string');
      expect(id1.length).toBeGreaterThan(5);
    });

    it('should include prefix when provided', () => {
      const id = generateId('test');
      expect(id).toMatch(/^test-/);
    });

    it('should generate IDs without prefix', () => {
      const id = generateId();
      expect(id).toMatch(/^[a-z0-9]+-[a-z0-9]+$/);
    });
  });

  describe('validateEmail', () => {
    const validateEmail = (email: string): boolean => {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return emailRegex.test(email);
    };

    it('should validate correct email addresses', () => {
      expect(validateEmail('<EMAIL>')).toBe(true);
      expect(validateEmail('<EMAIL>')).toBe(true);
      expect(validateEmail('<EMAIL>')).toBe(true);
    });

    it('should reject invalid email addresses', () => {
      expect(validateEmail('invalid-email')).toBe(false);
      expect(validateEmail('test@')).toBe(false);
      expect(validateEmail('@domain.com')).toBe(false);
      expect(validateEmail('<EMAIL>')).toBe(false);
    });

    it('should handle edge cases', () => {
      expect(validateEmail('')).toBe(false);
      expect(validateEmail(' ')).toBe(false);
      expect(validateEmail('test @example.com')).toBe(false);
    });
  });

  describe('deepClone', () => {
    const deepClone = <T>(obj: T): T => {
      if (obj === null || typeof obj !== 'object') return obj;
      if (obj instanceof Date) return new Date(obj.getTime()) as unknown as T;
      if (obj instanceof Array) return obj.map(item => deepClone(item)) as unknown as T;
      if (typeof obj === 'object') {
        const clonedObj = {} as T;
        for (const key in obj) {
          if (obj.hasOwnProperty(key)) {
            clonedObj[key] = deepClone(obj[key]);
          }
        }
        return clonedObj;
      }
      return obj;
    };

    it('should clone primitive values', () => {
      expect(deepClone(42)).toBe(42);
      expect(deepClone('string')).toBe('string');
      expect(deepClone(true)).toBe(true);
      expect(deepClone(null)).toBe(null);
    });

    it('should clone arrays', () => {
      const original = [1, 2, 3, [4, 5]];
      const cloned = deepClone(original);
      
      expect(cloned).toEqual(original);
      expect(cloned).not.toBe(original);
      expect(cloned[3]).not.toBe(original[3]);
    });

    it('should clone objects', () => {
      const original = {
        name: 'test',
        nested: {
          value: 42,
          array: [1, 2, 3],
        },
      };
      
      const cloned = deepClone(original);
      
      expect(cloned).toEqual(original);
      expect(cloned).not.toBe(original);
      expect(cloned.nested).not.toBe(original.nested);
      expect(cloned.nested.array).not.toBe(original.nested.array);
    });

    it('should clone dates', () => {
      const original = new Date('2023-01-01');
      const cloned = deepClone(original);
      
      expect(cloned).toEqual(original);
      expect(cloned).not.toBe(original);
    });
  });
});