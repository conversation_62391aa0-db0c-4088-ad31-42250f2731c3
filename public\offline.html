<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - Nirmaan AI Calculator</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            text-align: center;
            max-width: 500px;
            width: 100%;
        }
        
        .icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 30px;
            opacity: 0.8;
        }
        
        h1 {
            font-size: 2.5rem;
            margin-bottom: 20px;
            font-weight: 700;
        }
        
        p {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 30px;
            opacity: 0.9;
        }
        
        .features {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            backdrop-filter: blur(10px);
        }
        
        .features h2 {
            font-size: 1.5rem;
            margin-bottom: 20px;
        }
        
        .feature-list {
            list-style: none;
            text-align: left;
        }
        
        .feature-list li {
            padding: 10px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
        }
        
        .feature-list li:last-child {
            border-bottom: none;
        }
        
        .feature-list li::before {
            content: '✓';
            color: #4ade80;
            font-weight: bold;
            margin-right: 15px;
            font-size: 1.2rem;
        }
        
        .retry-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            margin: 10px;
        }
        
        .retry-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
        }
        
        .retry-btn:active {
            transform: translateY(0);
        }
        
        .status {
            display: inline-flex;
            align-items: center;
            gap: 10px;
            font-size: 0.9rem;
            opacity: 0.8;
            margin-top: 20px;
        }
        
        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #ef4444;
            animation: pulse 2s infinite;
        }
        
        .status-dot.online {
            background: #4ade80;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        @media (max-width: 640px) {
            h1 {
                font-size: 2rem;
            }
            
            .features {
                padding: 20px;
            }
            
            .retry-btn {
                width: 100%;
                margin: 10px 0;
            }
        }
        
        .loading {
            display: none;
            margin: 20px 0;
        }
        
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Building/Construction Icon -->
        <div class="icon">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M19 21V9l-7-5-7 5v12h5v-7h4v7h5z"/>
                <path d="M9 21v-6h6v6"/>
            </svg>
        </div>
        
        <h1>You're Offline</h1>
        <p>Don't worry! Nirmaan AI Calculator works offline too. You can still access saved calculations and use basic features.</p>
        
        <div class="features">
            <h2>Available Offline Features</h2>
            <ul class="feature-list">
                <li>View saved calculation history</li>
                <li>Access material cost database</li>
                <li>Use construction cost calculator</li>
                <li>Browse quality tier information</li>
                <li>Access user guides and help</li>
            </ul>
        </div>
        
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p style="margin-top: 15px;">Checking connection...</p>
        </div>
        
        <button class="retry-btn" onclick="retryConnection()">Try Again</button>
        <button class="retry-btn" onclick="goToCalculator()">Go to Calculator</button>
        
        <div class="status">
            <div class="status-dot" id="statusDot"></div>
            <span id="statusText">No internet connection</span>
        </div>
    </div>
    
    <script>
        // Check connection status
        function updateConnectionStatus() {
            const statusDot = document.getElementById('statusDot');
            const statusText = document.getElementById('statusText');
            
            if (navigator.onLine) {
                statusDot.classList.add('online');
                statusText.textContent = 'Connected - You can now access all features';
            } else {
                statusDot.classList.remove('online');
                statusText.textContent = 'No internet connection';
            }
        }
        
        // Retry connection
        function retryConnection() {
            const loading = document.getElementById('loading');
            loading.style.display = 'block';
            
            // Simulate checking connection
            setTimeout(() => {
                loading.style.display = 'none';
                
                if (navigator.onLine) {
                    // Redirect to home page
                    window.location.href = '/';
                } else {
                    // Show offline message
                    alert('Still offline. Try again when you have an internet connection.');
                }
            }, 2000);
        }
        
        // Go to calculator (offline mode)
        function goToCalculator() {
            window.location.href = '/calculator';
        }
        
        // Listen for online/offline events
        window.addEventListener('online', () => {
            updateConnectionStatus();
            setTimeout(() => {
                window.location.href = '/';
            }, 1000);
        });
        
        window.addEventListener('offline', updateConnectionStatus);
        
        // Initial status check
        updateConnectionStatus();
        
        // Periodically check connection
        setInterval(() => {
            updateConnectionStatus();
        }, 5000);
        
        // Service Worker communication
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.addEventListener('message', (event) => {
                if (event.data.type === 'CONNECTIVITY_CHANGE') {
                    updateConnectionStatus();
                }
            });
        }
        
        // Add touch feedback for mobile
        document.querySelectorAll('.retry-btn').forEach(btn => {
            btn.addEventListener('touchstart', function() {
                this.style.transform = 'scale(0.95)';
            });
            
            btn.addEventListener('touchend', function() {
                this.style.transform = 'scale(1)';
            });
        });
        
        // Haptic feedback (if available)
        function triggerHaptic() {
            if ('vibrate' in navigator) {
                navigator.vibrate(10);
            }
        }
        
        document.querySelectorAll('.retry-btn').forEach(btn => {
            btn.addEventListener('click', triggerHaptic);
        });
    </script>
</body>
</html>