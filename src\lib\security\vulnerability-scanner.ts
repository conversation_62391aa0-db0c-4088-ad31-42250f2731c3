/**
 * Vulnerability Scanner and Protection System
 * Real-time vulnerability detection and automated protection
 */

interface VulnerabilityRule {
  id: string;
  name: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  category: 'injection' | 'xss' | 'auth' | 'dos' | 'disclosure' | 'csrf' | 'rce' | 'traversal';
  pattern: RegExp | ((input: any) => boolean);
  mitigation: string;
  cwe?: string; // Common Weakness Enumeration
  owasp?: string; // OWASP Top 10 reference
}

interface VulnerabilityResult {
  vulnerable: boolean;
  findings: VulnerabilityFinding[];
  riskScore: number;
  recommendations: string[];
}

interface VulnerabilityFinding {
  ruleId: string;
  ruleName: string;
  severity: string;
  description: string;
  location: string;
  evidence: string;
  mitigation: string;
  cwe?: string;
  owasp?: string;
}

interface SecurityMetrics {
  totalScans: number;
  vulnerabilitiesFound: number;
  criticalIssues: number;
  highIssues: number;
  mediumIssues: number;
  lowIssues: number;
  lastScan: Date;
  threatLevel: 'low' | 'medium' | 'high' | 'critical';
}

export class VulnerabilityScanner {
  private rules: VulnerabilityRule[];
  private metrics: SecurityMetrics;
  private scanHistory: VulnerabilityResult[] = [];
  private readonly maxHistorySize = 1000;

  constructor() {
    this.rules = this.initializeRules();
    this.metrics = {
      totalScans: 0,
      vulnerabilitiesFound: 0,
      criticalIssues: 0,
      highIssues: 0,
      mediumIssues: 0,
      lowIssues: 0,
      lastScan: new Date(),
      threatLevel: 'low',
    };
  }

  private initializeRules(): VulnerabilityRule[] {
    return [
      // SQL Injection Detection
      {
        id: 'sqli-001',
        name: 'SQL Injection - Union Based',
        description: 'Detects UNION-based SQL injection attempts',
        severity: 'critical',
        category: 'injection',
        pattern: /(\bunion\b.+\bselect\b)|(\bselect\b.+\bunion\b)/i,
        mitigation: 'Use parameterized queries and input validation',
        cwe: 'CWE-89',
        owasp: 'A03:2021 - Injection',
      },
      {
        id: 'sqli-002',
        name: 'SQL Injection - Error Based',
        description: 'Detects error-based SQL injection attempts',
        severity: 'critical',
        category: 'injection',
        pattern: /(extractvalue|updatexml|floor|rand|group by|having|concat|char|ascii|hex|unhex|conv|substring|mid|left|right|reverse|length|char_length|ord|bin)/i,
        mitigation: 'Implement proper error handling and input sanitization',
        cwe: 'CWE-89',
        owasp: 'A03:2021 - Injection',
      },
      {
        id: 'sqli-003',
        name: 'SQL Injection - Time Based',
        description: 'Detects time-based blind SQL injection attempts',
        severity: 'high',
        category: 'injection',
        pattern: /(sleep|benchmark|pg_sleep|waitfor delay|dbms_pipe\.receive_message)/i,
        mitigation: 'Use parameterized queries and timeout controls',
        cwe: 'CWE-89',
        owasp: 'A03:2021 - Injection',
      },
      {
        id: 'sqli-004',
        name: 'SQL Injection - Boolean Based',
        description: 'Detects boolean-based blind SQL injection attempts',
        severity: 'high',
        category: 'injection',
        pattern: /(and|or).+(1=1|1=0|true|false).+(--|\#|\/\*)/i,
        mitigation: 'Implement input validation and WAF rules',
        cwe: 'CWE-89',
        owasp: 'A03:2021 - Injection',
      },

      // Cross-Site Scripting (XSS) Detection
      {
        id: 'xss-001',
        name: 'XSS - Script Tag Injection',
        description: 'Detects script tag injection attempts',
        severity: 'high',
        category: 'xss',
        pattern: /<script[^>]*>.*?<\/script>/i,
        mitigation: 'Implement Content Security Policy and output encoding',
        cwe: 'CWE-79',
        owasp: 'A03:2021 - Injection',
      },
      {
        id: 'xss-002',
        name: 'XSS - Event Handler Injection',
        description: 'Detects JavaScript event handler injection',
        severity: 'high',
        category: 'xss',
        pattern: /on(load|click|mouseover|error|focus|blur|change|submit|keydown|keyup|keypress)\s*=\s*['"]/i,
        mitigation: 'Sanitize user input and use CSP',
        cwe: 'CWE-79',
        owasp: 'A03:2021 - Injection',
      },
      {
        id: 'xss-003',
        name: 'XSS - JavaScript Protocol',
        description: 'Detects javascript: protocol usage',
        severity: 'medium',
        category: 'xss',
        pattern: /javascript\s*:/i,
        mitigation: 'Filter dangerous protocols from user input',
        cwe: 'CWE-79',
        owasp: 'A03:2021 - Injection',
      },
      {
        id: 'xss-004',
        name: 'XSS - Expression Injection',
        description: 'Detects CSS expression injection',
        severity: 'medium',
        category: 'xss',
        pattern: /expression\s*\(/i,
        mitigation: 'Sanitize CSS input and use strict CSP',
        cwe: 'CWE-79',
        owasp: 'A03:2021 - Injection',
      },

      // Path Traversal Detection
      {
        id: 'traversal-001',
        name: 'Path Traversal - Directory Climbing',
        description: 'Detects directory traversal attempts',
        severity: 'high',
        category: 'traversal',
        pattern: /(\.\.[\/\\]){2,}|(%2e%2e[\/\\]){2,}|(\.%2e[\/\\]){2,}/i,
        mitigation: 'Validate and sanitize file paths',
        cwe: 'CWE-22',
        owasp: 'A01:2021 - Broken Access Control',
      },
      {
        id: 'traversal-002',
        name: 'Path Traversal - Null Byte',
        description: 'Detects null byte injection for path traversal',
        severity: 'high',
        category: 'traversal',
        pattern: /%00|\\x00|\\0/i,
        mitigation: 'Filter null bytes from user input',
        cwe: 'CWE-22',
        owasp: 'A01:2021 - Broken Access Control',
      },

      // Remote Code Execution Detection
      {
        id: 'rce-001',
        name: 'RCE - Command Injection',
        description: 'Detects command injection attempts',
        severity: 'critical',
        category: 'rce',
        pattern: /(;|\||&|`|\$\(|eval|exec|system|shell_exec|passthru|cmd|powershell|bash|sh)/i,
        mitigation: 'Never execute user input as system commands',
        cwe: 'CWE-78',
        owasp: 'A03:2021 - Injection',
      },
      {
        id: 'rce-002',
        name: 'RCE - Server-Side Template Injection',
        description: 'Detects template injection attempts',
        severity: 'critical',
        category: 'rce',
        pattern: /(\{\{.*\}\}|\{%.*%\}|<%.*%>|\$\{.*\})/i,
        mitigation: 'Sanitize template input and use sandboxed templates',
        cwe: 'CWE-94',
        owasp: 'A03:2021 - Injection',
      },

      // Information Disclosure Detection
      {
        id: 'disclosure-001',
        name: 'Information Disclosure - File Inclusion',
        description: 'Detects file inclusion attempts',
        severity: 'high',
        category: 'disclosure',
        pattern: /(\/etc\/passwd|\/etc\/shadow|\.\.\/|\.\.\\|php:\/\/|file:\/\/|data:\/\/)/i,
        mitigation: 'Restrict file access and validate file paths',
        cwe: 'CWE-98',
        owasp: 'A05:2021 - Security Misconfiguration',
      },

      // Authentication Bypass Detection
      {
        id: 'auth-001',
        name: 'Authentication Bypass - Always True',
        description: 'Detects authentication bypass attempts',
        severity: 'critical',
        category: 'auth',
        pattern: /(1=1|true|always|admin|root).*(or|and).*(1=1|true)/i,
        mitigation: 'Implement proper authentication mechanisms',
        cwe: 'CWE-287',
        owasp: 'A07:2021 - Identification and Authentication Failures',
      },

      // DoS Attack Detection
      {
        id: 'dos-001',
        name: 'DoS - Resource Exhaustion',
        description: 'Detects potential DoS attacks',
        severity: 'medium',
        category: 'dos',
        pattern: (input: any) => {
          if (typeof input === 'string') {
            return input.length > 10000 || input.includes('A'.repeat(1000));
          }
          return false;
        },
        mitigation: 'Implement input length limits and rate limiting',
        cwe: 'CWE-400',
        owasp: 'A06:2021 - Vulnerable and Outdated Components',
      },

      // CSRF Detection
      {
        id: 'csrf-001',
        name: 'CSRF - Missing Token',
        description: 'Detects potential CSRF attacks',
        severity: 'medium',
        category: 'csrf',
        pattern: (input: any) => {
          // This would be implemented in the actual request analysis
          return false;
        },
        mitigation: 'Implement CSRF tokens for state-changing operations',
        cwe: 'CWE-352',
        owasp: 'A01:2021 - Broken Access Control',
      },
    ];
  }

  /**
   * Scan input for vulnerabilities
   */
  scanInput(input: any, context: string = 'general'): VulnerabilityResult {
    const findings: VulnerabilityFinding[] = [];
    let riskScore = 0;

    // Convert input to scannable format
    const scanData = this.prepareScanData(input);

    // Run all vulnerability rules
    for (const rule of this.rules) {
      const isVulnerable = this.checkRule(rule, scanData, context);
      
      if (isVulnerable) {
        findings.push({
          ruleId: rule.id,
          ruleName: rule.name,
          severity: rule.severity,
          description: rule.description,
          location: context,
          evidence: this.extractEvidence(scanData, rule),
          mitigation: rule.mitigation,
          cwe: rule.cwe,
          owasp: rule.owasp,
        });

        // Calculate risk score
        riskScore += this.getSeverityScore(rule.severity);
      }
    }

    // Update metrics
    this.updateMetrics(findings);

    const result: VulnerabilityResult = {
      vulnerable: findings.length > 0,
      findings,
      riskScore,
      recommendations: this.generateRecommendations(findings),
    };

    // Store in history
    this.addToHistory(result);

    return result;
  }

  /**
   * Scan HTTP request for vulnerabilities
   */
  scanRequest(req: any): VulnerabilityResult {
    const allFindings: VulnerabilityFinding[] = [];
    let totalRiskScore = 0;

    // Scan URL parameters
    if (req.nextUrl?.searchParams) {
      req.nextUrl.searchParams.forEach((value: string, key: string) => {
        const result = this.scanInput(value, `query_param:${key}`);
        allFindings.push(...result.findings);
        totalRiskScore += result.riskScore;
      });
    }

    // Scan headers
    const suspiciousHeaders = ['user-agent', 'referer', 'x-forwarded-for'];
    suspiciousHeaders.forEach(headerName => {
      const headerValue = req.headers.get(headerName);
      if (headerValue) {
        const result = this.scanInput(headerValue, `header:${headerName}`);
        allFindings.push(...result.findings);
        totalRiskScore += result.riskScore;
      }
    });

    // Scan request body (if present)
    if (req.body) {
      const result = this.scanInput(req.body, 'request_body');
      allFindings.push(...result.findings);
      totalRiskScore += result.riskScore;
    }

    // Scan cookies
    const cookies = req.headers.get('cookie');
    if (cookies) {
      const result = this.scanInput(cookies, 'cookies');
      allFindings.push(...result.findings);
      totalRiskScore += result.riskScore;
    }

    const finalResult: VulnerabilityResult = {
      vulnerable: allFindings.length > 0,
      findings: allFindings,
      riskScore: totalRiskScore,
      recommendations: this.generateRecommendations(allFindings),
    };

    this.addToHistory(finalResult);
    return finalResult;
  }

  /**
   * Prepare data for scanning
   */
  private prepareScanData(input: any): string {
    if (typeof input === 'string') {
      return input;
    } else if (typeof input === 'object') {
      return JSON.stringify(input);
    } else {
      return String(input);
    }
  }

  /**
   * Check if input matches a vulnerability rule
   */
  private checkRule(rule: VulnerabilityRule, data: string, context: string): boolean {
    if (typeof rule.pattern === 'function') {
      return rule.pattern(data);
    } else {
      return rule.pattern.test(data);
    }
  }

  /**
   * Extract evidence of vulnerability
   */
  private extractEvidence(data: string, rule: VulnerabilityRule): string {
    if (typeof rule.pattern === 'function') {
      return data.substring(0, 100) + '...';
    } else {
      const match = data.match(rule.pattern);
      return match ? match[0] : 'Pattern detected';
    }
  }

  /**
   * Get numeric score for severity level
   */
  private getSeverityScore(severity: string): number {
    const scores = {
      low: 1,
      medium: 5,
      high: 10,
      critical: 20,
    };
    return scores[severity as keyof typeof scores] || 0;
  }

  /**
   * Update security metrics
   */
  private updateMetrics(findings: VulnerabilityFinding[]): void {
    this.metrics.totalScans++;
    this.metrics.lastScan = new Date();
    
    if (findings.length > 0) {
      this.metrics.vulnerabilitiesFound += findings.length;
      
      findings.forEach(finding => {
        switch (finding.severity) {
          case 'critical':
            this.metrics.criticalIssues++;
            break;
          case 'high':
            this.metrics.highIssues++;
            break;
          case 'medium':
            this.metrics.mediumIssues++;
            break;
          case 'low':
            this.metrics.lowIssues++;
            break;
        }
      });
      
      // Update threat level
      this.updateThreatLevel();
    }
  }

  /**
   * Update overall threat level
   */
  private updateThreatLevel(): void {
    if (this.metrics.criticalIssues > 0) {
      this.metrics.threatLevel = 'critical';
    } else if (this.metrics.highIssues > 2) {
      this.metrics.threatLevel = 'high';
    } else if (this.metrics.mediumIssues > 5) {
      this.metrics.threatLevel = 'medium';
    } else {
      this.metrics.threatLevel = 'low';
    }
  }

  /**
   * Generate recommendations based on findings
   */
  private generateRecommendations(findings: VulnerabilityFinding[]): string[] {
    const recommendations = new Set<string>();
    
    findings.forEach(finding => {
      recommendations.add(finding.mitigation);
      
      // Add category-specific recommendations
      switch (finding.severity) {
        case 'critical':
          recommendations.add('Immediately block this request and investigate the source');
          recommendations.add('Review and strengthen input validation');
          break;
        case 'high':
          recommendations.add('Implement additional security controls');
          recommendations.add('Monitor for similar attack patterns');
          break;
      }
    });
    
    return Array.from(recommendations);
  }

  /**
   * Add result to scan history
   */
  private addToHistory(result: VulnerabilityResult): void {
    this.scanHistory.push(result);
    
    // Maintain history size limit
    if (this.scanHistory.length > this.maxHistorySize) {
      this.scanHistory.shift();
    }
  }

  /**
   * Get security metrics
   */
  getMetrics(): SecurityMetrics {
    return { ...this.metrics };
  }

  /**
   * Get recent vulnerability findings
   */
  getRecentFindings(limit: number = 10): VulnerabilityFinding[] {
    return this.scanHistory
      .filter(result => result.vulnerable)
      .flatMap(result => result.findings)
      .slice(-limit);
  }

  /**
   * Get threat analysis
   */
  getThreatAnalysis(): {
    currentThreatLevel: string;
    recentAttacks: number;
    topVulnerabilities: string[];
    riskTrend: 'increasing' | 'stable' | 'decreasing';
  } {
    const recentResults = this.scanHistory.slice(-50);
    const recentAttacks = recentResults.filter(r => r.vulnerable).length;
    
    // Calculate vulnerability frequency
    const vulnCounts: Record<string, number> = {};
    recentResults.forEach(result => {
      result.findings.forEach(finding => {
        vulnCounts[finding.ruleName] = (vulnCounts[finding.ruleName] || 0) + 1;
      });
    });
    
    const topVulnerabilities = Object.entries(vulnCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([name]) => name);
    
    // Calculate risk trend (simplified)
    const firstHalf = recentResults.slice(0, 25);
    const secondHalf = recentResults.slice(25);
    const firstHalfRisk = firstHalf.reduce((sum, r) => sum + r.riskScore, 0);
    const secondHalfRisk = secondHalf.reduce((sum, r) => sum + r.riskScore, 0);
    
    let riskTrend: 'increasing' | 'stable' | 'decreasing' = 'stable';
    if (secondHalfRisk > firstHalfRisk * 1.2) {
      riskTrend = 'increasing';
    } else if (secondHalfRisk < firstHalfRisk * 0.8) {
      riskTrend = 'decreasing';
    }
    
    return {
      currentThreatLevel: this.metrics.threatLevel,
      recentAttacks,
      topVulnerabilities,
      riskTrend,
    };
  }

  /**
   * Add custom vulnerability rule
   */
  addCustomRule(rule: VulnerabilityRule): void {
    this.rules.push(rule);
  }

  /**
   * Remove vulnerability rule
   */
  removeRule(ruleId: string): void {
    this.rules = this.rules.filter(rule => rule.id !== ruleId);
  }
}

// Export singleton instance
export const vulnerabilityScanner = new VulnerabilityScanner();

// Export types
export type { VulnerabilityRule, VulnerabilityResult, VulnerabilityFinding, SecurityMetrics };