import { NextRequest, NextResponse } from 'next/server';

interface MonitoringData {
  timestamp: string;
  status: 'healthy' | 'degraded' | 'unhealthy';
  responseTime: number;
  uptime: number;
  errorRate: number;
  memoryUsage: number;
}

export async function GET(request: NextRequest) {
  // Verify this is a legitimate cron request
  const authHeader = request.headers.get('authorization');
  if (authHeader !== `Bearer ${process.env.CRON_SECRET}`) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const healthCheckStart = Date.now();
    
    // Make internal health check request
    const healthResponse = await fetch(
      `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/health`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );

    const healthData = await healthResponse.json();
    const responseTime = Date.now() - healthCheckStart;

    // Create monitoring data
    const monitoringData: MonitoringData = {
      timestamp: new Date().toISOString(),
      status: healthData.status,
      responseTime,
      uptime: healthData.uptime || 0,
      errorRate: healthData.performance?.errorRate || 0,
      memoryUsage: healthData.checks?.memory?.percentage || 0,
    };

    // Log monitoring data (in production, send to monitoring service)
    console.log('Health Check Monitoring:', JSON.stringify(monitoringData, null, 2));

    // Alert if unhealthy
    if (healthData.status === 'unhealthy' || responseTime > 5000) {
      await sendAlert(monitoringData);
    }

    return NextResponse.json({
      success: true,
      data: monitoringData,
      message: 'Health check completed',
    });

  } catch (error) {
    console.error('Cron health check failed:', error);
    
    const alertData: MonitoringData = {
      timestamp: new Date().toISOString(),
      status: 'unhealthy',
      responseTime: 0,
      uptime: 0,
      errorRate: 100,
      memoryUsage: 0,
    };

    await sendAlert(alertData);

    return NextResponse.json(
      { 
        success: false, 
        error: 'Health check failed',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

async function sendAlert(data: MonitoringData) {
  try {
    // In production, integrate with actual alerting services
    const alertMessage = `
🚨 CLARITY ENGINE ALERT 🚨

Status: ${data.status.toUpperCase()}
Timestamp: ${data.timestamp}
Response Time: ${data.responseTime}ms
Memory Usage: ${data.memoryUsage}%
Error Rate: ${data.errorRate}%
Uptime: ${data.uptime}s

${data.status === 'unhealthy' ? 'IMMEDIATE ACTION REQUIRED!' : 'Performance degraded, monitor closely.'}
    `.trim();

    console.error('ALERT:', alertMessage);

    // Send to monitoring services (placeholder)
    if (process.env.SLACK_WEBHOOK_URL) {
      await fetch(process.env.SLACK_WEBHOOK_URL, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          text: alertMessage,
          channel: '#alerts',
          username: 'Clarity Engine Monitor',
          icon_emoji: ':warning:',
        }),
      });
    }

    // Send email alert if configured
    if (process.env.ALERT_EMAIL) {
      // Email sending logic would go here
      console.log('Email alert would be sent to:', process.env.ALERT_EMAIL);
    }

  } catch (error) {
    console.error('Failed to send alert:', error);
  }
}