'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ArrowLeft,
  Share2,
  TrendingUp,
  Clock,
  MapPin,
  Building,
  Calculator,
  FileText,
  Loader2,
  ChevronUp,
  ChevronDown,
} from 'lucide-react';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { AnimatedButton } from '@/components/ui/animated-button';
import { CountUp } from '@/components/ui/count-up';
import { CostBreakdownCard } from './CostBreakdownCard';
import { MaterialsList } from './MaterialsList';
import { SaveCalculationButton } from './SaveCalculationButton';
import { exportCalculationToPDF } from '@/lib/pdf';
import { PDFExportButton, CompactPDFExportButton } from '@/components/ui/pdf-export-button';

import { CalculationResult } from '@/core/calculator/types';
import { CalculatorFormData, formatCurrency, formatNumber } from '@/lib/validation/calculator';
import { QUALITY_TIER_SPECS } from '@/core/calculator/constants';
import { MobileSheet } from '@/components/ui/mobile-sheet';
import { 
  fadeInUp, 
  staggerContainer, 
  staggerItem, 
  pageTransition,
  createStaggerAnimation 
} from '@/lib/animations';
import { 
  isMobileViewport, 
  mobileClasses, 
  hapticFeedback, 
  shareContent 
} from '@/lib/mobile';
import { cn } from '@/lib/utils';

interface ResultsDisplayProps {
  result: CalculationResult;
  formData: Partial<CalculatorFormData>;
  onBack: () => void;
}

export function ResultsDisplay({ result, formData, onBack }: ResultsDisplayProps) {
  const [activeTab, setActiveTab] = useState<'overview' | 'breakdown' | 'materials'>('overview');
  const [isMobile, setIsMobile] = useState(false);
  const [showMobileSheet, setShowMobileSheet] = useState(false);
  const [mobileSheetContent, setMobileSheetContent] = useState<'breakdown' | 'materials' | null>(null);

  const qualitySpec = formData.qualityTier ? QUALITY_TIER_SPECS[formData.qualityTier] : null;

  // Check if mobile on component mount
  useEffect(() => {
    setIsMobile(isMobileViewport());
    const handleResize = () => setIsMobile(isMobileViewport());
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Handle mobile sheet opening
  const openMobileSheet = (content: 'breakdown' | 'materials') => {
    setMobileSheetContent(content);
    setShowMobileSheet(true);
    hapticFeedback.light();
  };

  const closeMobileSheet = () => {
    setShowMobileSheet(false);
    setMobileSheetContent(null);
    hapticFeedback.light();
  };

  // Handle native share
  const handleShare = async () => {
    const shareData = {
      title: 'Construction Cost Estimate',
      text: `Construction cost for ${formData.builtUpArea} sq ft: ${formatCurrency(result.totalCost)}`,
      url: window.location.href,
    };

    const shareResult = await shareContent(shareData);
    if (shareResult.success) {
      hapticFeedback.success();
    } else {
      hapticFeedback.error();
    }
  };

  // Convert formData to CalculationInput format for PDF export
  const calculationInput = {
    builtUpArea: formData.builtUpArea || 0,
    floors: formData.floors || 0,
    qualityTier: formData.qualityTier || 'smart',
    location: formData.location || 'bangalore',
    plotArea: formData.plotArea,
    hasBasement: formData.hasBasement,
    parkingType: formData.parkingType,
  };

  const pdfExportOptions = {
    includeTimeline: true,
    includeMaterials: true,
    includeDetailedBreakdown: true,
  };

  // Handle PDF export success
  const handlePDFSuccess = (filename: string) => {
    console.log(`PDF exported successfully: ${filename}`);
    // You could add a toast notification here
  };

  // Handle PDF export error
  const handlePDFError = (error: Error) => {
    console.error('PDF generation failed:', error);
    // You could add a toast notification here
  };
  
  // Create stagger animation for overview cards
  const overviewStagger = createStaggerAnimation(0.15, 0.3);

  // Mobile view renders a different layout
  if (isMobile) {
    return (
      <>
        <motion.div 
          className="space-y-6"
          variants={pageTransition}
          initial="initial"
          animate="animate"
          exit="exit"
        >
          {/* Mobile Header */}
          <motion.div 
            className="flex items-center justify-between"
            variants={fadeInUp}
            initial="initial"
            animate="animate"
          >
            <AnimatedButton
              variant="outline"
              onClick={onBack}
              className={mobileClasses.touchTarget}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </AnimatedButton>

            <div className="flex space-x-2">
              <SaveCalculationButton
                formData={formData}
                results={result}
                disabled={isGeneratingPDF}
              />
              
              <AnimatedButton
                variant="outline"
                size="sm"
                onClick={handleShare}
                className={mobileClasses.touchTarget}
              >
                <Share2 className="h-4 w-4" />
              </AnimatedButton>
              
              <CompactPDFExportButton
                input={calculationInput}
                result={result}
                options={pdfExportOptions}
                onSuccess={handlePDFSuccess}
                onError={handlePDFError}
                className={mobileClasses.touchTarget}
              />
            </div>
          </motion.div>

          {/* Mobile Hero Card */}
          <motion.div
            variants={fadeInUp}
            initial="initial"
            animate="animate"
          >
            <Card className="bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-200">
              <CardContent className="p-6">
                <div className="text-center space-y-4">
                  <div>
                    <p className="text-sm text-gray-600 mb-2">Total Construction Cost</p>
                    <CountUp
                      value={result.totalCost}
                      prefix="₹"
                      className="text-3xl sm:text-4xl font-bold text-blue-600"
                      duration={2000}
                    />
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4 pt-4 border-t border-blue-200">
                    <div className="text-center">
                      <p className="text-xs text-gray-500">Cost per sq ft</p>
                      <p className="text-lg font-semibold text-gray-900">
                        ₹{formatNumber(Math.round(result.totalCost / (formData.builtUpArea || 1)))}
                      </p>
                    </div>
                    <div className="text-center">
                      <p className="text-xs text-gray-500">Timeline</p>
                      <p className="text-lg font-semibold text-gray-900">
                        8-12 months
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Mobile Action Cards */}
          <motion.div 
            className="grid grid-cols-1 gap-4"
            variants={staggerContainer}
            initial="initial"
            animate="animate"
          >
            <motion.button
              variants={staggerItem}
              onClick={() => openMobileSheet('breakdown')}
              className={cn(
                'text-left p-4 bg-white border rounded-lg shadow-sm hover:shadow-md transition-shadow',
                mobileClasses.touchTarget
              )}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <TrendingUp className="h-5 w-5 text-green-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">Cost Breakdown</h3>
                    <p className="text-sm text-gray-600">Detailed category costs</p>
                  </div>
                </div>
                <ChevronUp className="h-5 w-5 text-gray-400" />
              </div>
            </motion.button>

            <motion.button
              variants={staggerItem}
              onClick={() => openMobileSheet('materials')}
              className={cn(
                'text-left p-4 bg-white border rounded-lg shadow-sm hover:shadow-md transition-shadow',
                mobileClasses.touchTarget
              )}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-purple-100 rounded-lg">
                    <Building className="h-5 w-5 text-purple-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">Materials List</h3>
                    <p className="text-sm text-gray-600">Quantities & specifications</p>
                  </div>
                </div>
                <ChevronUp className="h-5 w-5 text-gray-400" />
              </div>
            </motion.button>
          </motion.div>

          {/* Project Summary */}
          <motion.div
            variants={fadeInUp}
            initial="initial"
            animate="animate"
          >
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Project Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-gray-600">Built-up Area</p>
                    <p className="font-semibold">{formatNumber(formData.builtUpArea || 0)} sq ft</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Quality Tier</p>
                    <p className="font-semibold">{qualitySpec?.name || 'Smart Choice'}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Location</p>
                    <p className="font-semibold capitalize">{formData.location || 'Bangalore'}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Floors</p>
                    <p className="font-semibold">{formData.floors || 1} floors</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </motion.div>

        {/* Mobile Bottom Sheets */}
        <MobileSheet
          isOpen={showMobileSheet}
          onClose={closeMobileSheet}
          title={mobileSheetContent === 'breakdown' ? 'Cost Breakdown' : 'Materials List'}
        >
          {mobileSheetContent === 'breakdown' && (
            <div className="space-y-4">
              {result.breakdown && Object.entries(result.breakdown).map(([category, data]) => (
                <CostBreakdownCard
                  key={category}
                  title={category.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                  amount={data.total}
                  percentage={(data.total / result.totalCost) * 100}
                  subcategories={data.subcategories}
                  totalCost={result.totalCost}
                />
              ))}
            </div>
          )}
          
          {mobileSheetContent === 'materials' && result.materials && (
            <MaterialsList materials={result.materials} />
          )}
        </MobileSheet>
      </>
    );
  }

  // Desktop layout
  return (
    <motion.div 
      className="space-y-8"
      variants={pageTransition}
      initial="initial"
      animate="animate"
      exit="exit"
    >
      {/* Header with back button */}
      <motion.div 
        className="flex items-center justify-between"
        variants={fadeInUp}
        initial="initial"
        animate="animate"
      >
        <AnimatedButton
          variant="outline"
          onClick={onBack}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back to Calculator
        </AnimatedButton>

        <div className="flex gap-2">
          <AnimatedButton variant="outline" size="sm">
            <Share2 className="h-4 w-4 mr-2" />
            Share
          </AnimatedButton>
          <PDFExportButton
            input={calculationInput}
            result={result}
            options={pdfExportOptions}
            onSuccess={handlePDFSuccess}
            onError={handlePDFError}
            showProgress
            showEstimatedTime
            enableRetry
          />
        </div>
      </motion.div>

      {/* Hero cost display */}
      <motion.div
        variants={fadeInUp}
        initial="initial"
        animate="animate"
        transition={{ delay: 0.1 }}
      >
        <Card className="bg-gradient-to-br from-blue-500 to-blue-700 text-white">
          <CardContent className="p-8">
            <motion.div 
              className="text-center space-y-4"
              variants={staggerContainer}
              initial="initial"
              animate="animate"
            >
              <motion.div 
                className="flex items-center justify-center gap-2 text-blue-100"
                variants={staggerItem}
              >
                <Calculator className="h-5 w-5" />
                <span className="text-sm font-medium">Total Construction Cost</span>
              </motion.div>

              <motion.div 
                className="text-5xl font-bold"
                variants={staggerItem}
              >
                <CountUp 
                  value={result.totalCost} 
                  prefix="₹"
                  separator=","
                  duration={2.5}
                  delay={0.5}
                />
              </motion.div>

              <motion.div 
                className="text-xl text-blue-100"
                variants={staggerItem}
              >
                <CountUp 
                  value={result.costPerSqft} 
                  prefix="₹"
                  suffix=" per sq ft"
                  separator=","
                  duration={2}
                  delay={0.8}
                />
              </motion.div>

              <motion.div 
                className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-6 border-t border-blue-400"
                variants={staggerContainer}
                initial="initial"
                animate="animate"
              >
                <motion.div className="text-center" variants={staggerItem}>
                  <div className="flex items-center justify-center gap-2 text-blue-200 mb-1">
                    <Building className="h-4 w-4" />
                    <span className="text-sm">Built-up Area</span>
                  </div>
                  <div className="text-lg font-semibold">
                    <CountUp 
                      value={result.summary.totalBuiltUpArea} 
                      suffix=" sq ft"
                      separator=","
                      duration={1.5}
                      delay={1.2}
                    />
                  </div>
                </motion.div>

                <motion.div className="text-center" variants={staggerItem}>
                  <div className="flex items-center justify-center gap-2 text-blue-200 mb-1">
                    <Clock className="h-4 w-4" />
                    <span className="text-sm">Duration</span>
                  </div>
                  <div className="text-lg font-semibold">
                    <CountUp 
                      value={result.summary.constructionDuration} 
                      suffix=" months"
                      duration={1.5}
                      delay={1.3}
                    />
                  </div>
                </motion.div>

                <motion.div className="text-center" variants={staggerItem}>
                  <div className="flex items-center justify-center gap-2 text-blue-200 mb-1">
                    <TrendingUp className="h-4 w-4" />
                    <span className="text-sm">Accuracy</span>
                  </div>
                  <motion.div 
                    className="text-lg font-semibold"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 1.4, duration: 0.5 }}
                  >
                    {result.summary.estimateAccuracy}
                  </motion.div>
                </motion.div>
              </motion.div>
            </motion.div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Project details summary */}
      <motion.div
        variants={fadeInUp}
        initial="initial"
        animate="animate"
        transition={{ delay: 0.2 }}
      >
        <Card>
          <CardHeader>
            <CardTitle>Project Summary</CardTitle>
          </CardHeader>
          <CardContent>
            <motion.div 
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"
              variants={staggerContainer}
              initial="initial"
              animate="animate"
            >
              <motion.div className="space-y-1" variants={staggerItem}>
                <p className="text-sm text-gray-500">Quality Tier</p>
                <p className="font-semibold">{qualitySpec?.name}</p>
                <p className="text-xs text-gray-600">{qualitySpec?.description}</p>
              </motion.div>

              <motion.div className="space-y-1" variants={staggerItem}>
                <p className="text-sm text-gray-500">Location</p>
                <p className="font-semibold flex items-center gap-1">
                  <MapPin className="h-4 w-4" />
                  {formData.location}
                </p>
              </motion.div>

              <motion.div className="space-y-1" variants={staggerItem}>
                <p className="text-sm text-gray-500">Floors</p>
                <p className="font-semibold">
                  {formData.floors === 1 ? 'Ground' : `Ground + ${(formData.floors || 1) - 1}`}
                </p>
              </motion.div>

              <motion.div className="space-y-1" variants={staggerItem}>
                <p className="text-sm text-gray-500">Carpet Area</p>
                <p className="font-semibold">
                  {formatNumber(result.summary.carpetArea)} sq ft
                </p>
              </motion.div>
            </motion.div>

            {/* Additional features */}
            {(formData.hasBasement || formData.hasStilt || formData.parkingType !== 'none') && (
              <motion.div 
                className="mt-4 pt-4 border-t"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4, duration: 0.3 }}
              >
                <p className="text-sm text-gray-500 mb-2">Additional Features</p>
                <motion.div 
                  className="flex flex-wrap gap-2"
                  variants={staggerContainer}
                  initial="initial"
                  animate="animate"
                >
                  {formData.hasBasement && (
                    <motion.span 
                      className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded"
                      variants={staggerItem}
                    >
                      Basement
                    </motion.span>
                  )}
                  {formData.hasStilt && (
                    <motion.span 
                      className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded"
                      variants={staggerItem}
                    >
                      Stilt Parking
                    </motion.span>
                  )}
                  {formData.parkingType !== 'none' && (
                    <motion.span 
                      className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded"
                      variants={staggerItem}
                    >
                      {formData.parkingType === 'covered' ? 'Covered' : 'Open'} Parking
                    </motion.span>
                  )}
                </motion.div>
              </motion.div>
            )}
          </CardContent>
        </Card>
      </motion.div>

      {/* Tab navigation */}
      <motion.div 
        className="flex space-x-1 bg-gray-100 p-1 rounded-lg"
        variants={fadeInUp}
        initial="initial"
        animate="animate"
        transition={{ delay: 0.3 }}
      >
        {[
          { id: 'overview', label: 'Cost Overview' },
          { id: 'breakdown', label: 'Detailed Breakdown' },
          { id: 'materials', label: 'Materials List' },
        ].map((tab, index) => (
          <motion.button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as typeof activeTab)}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
              activeTab === tab.id
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 + index * 0.1 }}
          >
            {tab.label}
          </motion.button>
        ))}
      </motion.div>

      {/* Tab content */}
      <AnimatePresence mode="wait">
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.3 }}
        >
          {activeTab === 'overview' && (
            <motion.div 
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4"
              variants={overviewStagger.container}
              initial="initial"
              animate="animate"
            >
              {Object.entries(result.breakdown).filter(([key]) => key !== 'total').map(([category, data], index) => (
                <motion.div
                  key={category}
                  variants={overviewStagger.item}
                  whileHover={{
                    y: -4,
                    boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
                    transition: { duration: 0.2 }
                  }}
                >
                  <Card className="transition-all duration-200">
                    <CardContent className="p-4 text-center">
                      <motion.div 
                        className="text-2xl font-bold text-gray-900"
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ delay: 0.5 + index * 0.1, type: 'spring', stiffness: 300 }}
                      >
                        {data.percentage}%
                      </motion.div>
                      <div className="text-sm font-medium text-gray-600 capitalize mb-2">
                        {category === 'mep' ? 'MEP Systems' : category}
                      </div>
                      <div className="text-lg font-semibold text-gray-900">
                        <CountUp 
                          value={data.amount}
                          prefix="₹"
                          separator=","
                          duration={1.5}
                          delay={0.7 + index * 0.1}
                        />
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </motion.div>
          )}

          {activeTab === 'breakdown' && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
            >
              <CostBreakdownCard breakdown={result.breakdown} />
            </motion.div>
          )}

          {activeTab === 'materials' && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
            >
              <MaterialsList materials={result.materials} />
            </motion.div>
          )}
        </motion.div>
      </AnimatePresence>

      {/* Disclaimer */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5, duration: 0.3 }}
      >
        <Card className="bg-amber-50 border-amber-200">
          <CardContent className="p-4">
            <p className="text-sm text-amber-800">
              <strong>Important:</strong> These estimates are based on current market rates and standard specifications.
              Actual costs may vary based on specific requirements, material choices, site conditions, and market fluctuations.
              For detailed project planning and accurate quotes, please consult with our professional team.
            </p>
          </CardContent>
        </Card>
      </motion.div>
    </motion.div>
  );
}

// Default export for backward compatibility
export default ResultsDisplay;