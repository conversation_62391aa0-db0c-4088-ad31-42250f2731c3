/**
 * Typography Components for Enhanced MVP Design System
 * Comprehensive typography system with Inter + Poppins fonts
 */

import React from 'react';
import { cn } from '@/lib/utils';

// Typography variant types
export type TypographyVariant = 
  | 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6'
  | 'body-large' | 'body' | 'body-small'
  | 'caption' | 'overline' | 'lead' | 'muted';

export type TypographyElement = 
  | 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6'
  | 'p' | 'span' | 'div' | 'blockquote' | 'code' | 'pre';

export interface TypographyProps extends React.HTMLAttributes<HTMLElement> {
  variant?: TypographyVariant;
  element?: TypographyElement;
  children: React.ReactNode;
  className?: string;
  gradient?: boolean;
  weight?: 'light' | 'normal' | 'medium' | 'semibold' | 'bold';
  color?: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'muted';
}

// Typography variant mapping to CSS classes
const variantStyles: Record<TypographyVariant, string> = {
  h1: 'heading-1',
  h2: 'heading-2', 
  h3: 'heading-3',
  h4: 'heading-4',
  h5: 'heading-5',
  h6: 'heading-6',
  'body-large': 'body-large',
  'body': 'body-normal',
  'body-small': 'body-small',
  'caption': 'caption',
  'overline': 'caption uppercase tracking-widest',
  'lead': 'text-xl text-muted-foreground',
  'muted': 'body-small'
};

// Default element mapping for semantic HTML
const defaultElements: Record<TypographyVariant, TypographyElement> = {
  h1: 'h1',
  h2: 'h2',
  h3: 'h3', 
  h4: 'h4',
  h5: 'h5',
  h6: 'h6',
  'body-large': 'p',
  'body': 'p',
  'body-small': 'p',
  'caption': 'span',
  'overline': 'span',
  'lead': 'p',
  'muted': 'p'
};

// Color variant mapping
const colorStyles: Record<string, string> = {
  default: 'text-foreground',
  primary: 'text-primary',
  secondary: 'text-secondary',
  success: 'text-success',
  warning: 'text-warning',
  error: 'text-error',
  muted: 'text-muted'
};

// Font weight mapping
const weightStyles: Record<string, string> = {
  light: 'font-light',
  normal: 'font-normal',
  medium: 'font-medium',
  semibold: 'font-semibold',
  bold: 'font-bold'
};

/**
 * Universal Typography Component
 * Provides consistent typography across the application
 */
export const Typography = React.forwardRef<HTMLElement, TypographyProps>(({
  variant = 'body',
  element,
  children,
  className,
  gradient = false,
  weight,
  color = 'default',
  ...props
}, ref) => {
  const Component = (element || defaultElements[variant]) as keyof JSX.IntrinsicElements;
  
  const variantClass = variantStyles[variant];
  const colorClass = gradient ? '' : colorStyles[color];
  const weightClass = weight ? weightStyles[weight] : '';
  const gradientClass = gradient ? 'text-gradient-primary' : '';
  
  return React.createElement(
    Component,
    {
      ref,
      className: cn(
        variantClass,
        colorClass,
        weightClass,
        gradientClass,
        className
      ),
      ...props
    },
    children
  );
});

Typography.displayName = 'Typography';

/**
 * Heading Components (h1-h6)
 */
export const H1 = React.forwardRef<HTMLHeadingElement, Omit<TypographyProps, 'variant'>>(
  ({ children, className, ...props }, ref) => (
    <Typography ref={ref} variant="h1" className={className} {...props}>
      {children}
    </Typography>
  )
);
H1.displayName = 'H1';

export const H2 = React.forwardRef<HTMLHeadingElement, Omit<TypographyProps, 'variant'>>(
  ({ children, className, ...props }, ref) => (
    <Typography ref={ref} variant="h2" className={className} {...props}>
      {children}
    </Typography>
  )
);
H2.displayName = 'H2';

export const H3 = React.forwardRef<HTMLHeadingElement, Omit<TypographyProps, 'variant'>>(
  ({ children, className, ...props }, ref) => (
    <Typography ref={ref} variant="h3" className={className} {...props}>
      {children}
    </Typography>
  )
);
H3.displayName = 'H3';

export const H4 = React.forwardRef<HTMLHeadingElement, Omit<TypographyProps, 'variant'>>(
  ({ children, className, ...props }, ref) => (
    <Typography ref={ref} variant="h4" className={className} {...props}>
      {children}
    </Typography>
  )
);
H4.displayName = 'H4';

export const H5 = React.forwardRef<HTMLHeadingElement, Omit<TypographyProps, 'variant'>>(
  ({ children, className, ...props }, ref) => (
    <Typography ref={ref} variant="h5" className={className} {...props}>
      {children}
    </Typography>
  )
);
H5.displayName = 'H5';

export const H6 = React.forwardRef<HTMLHeadingElement, Omit<TypographyProps, 'variant'>>(
  ({ children, className, ...props }, ref) => (
    <Typography ref={ref} variant="h6" className={className} {...props}>
      {children}
    </Typography>
  )
);
H6.displayName = 'H6';

/**
 * Body Text Components
 */
export const BodyLarge = React.forwardRef<HTMLParagraphElement, Omit<TypographyProps, 'variant'>>(
  ({ children, className, ...props }, ref) => (
    <Typography ref={ref} variant="body-large" className={className} {...props}>
      {children}
    </Typography>
  )
);
BodyLarge.displayName = 'BodyLarge';

export const Body = React.forwardRef<HTMLParagraphElement, Omit<TypographyProps, 'variant'>>(
  ({ children, className, ...props }, ref) => (
    <Typography ref={ref} variant="body" className={className} {...props}>
      {children}
    </Typography>
  )
);
Body.displayName = 'Body';

export const BodySmall = React.forwardRef<HTMLParagraphElement, Omit<TypographyProps, 'variant'>>(
  ({ children, className, ...props }, ref) => (
    <Typography ref={ref} variant="body-small" className={className} {...props}>
      {children}
    </Typography>
  )
);
BodySmall.displayName = 'BodySmall';

/**
 * Specialty Text Components
 */
export const Lead = React.forwardRef<HTMLParagraphElement, Omit<TypographyProps, 'variant'>>(
  ({ children, className, ...props }, ref) => (
    <Typography ref={ref} variant="lead" className={className} {...props}>
      {children}
    </Typography>
  )
);
Lead.displayName = 'Lead';

export const Caption = React.forwardRef<HTMLSpanElement, Omit<TypographyProps, 'variant'>>(
  ({ children, className, ...props }, ref) => (
    <Typography ref={ref} variant="caption" element="span" className={className} {...props}>
      {children}
    </Typography>
  )
);
Caption.displayName = 'Caption';

export const Overline = React.forwardRef<HTMLSpanElement, Omit<TypographyProps, 'variant'>>(
  ({ children, className, ...props }, ref) => (
    <Typography ref={ref} variant="overline" element="span" className={className} {...props}>
      {children}
    </Typography>
  )
);
Overline.displayName = 'Overline';

export const Muted = React.forwardRef<HTMLParagraphElement, Omit<TypographyProps, 'variant'>>(
  ({ children, className, ...props }, ref) => (
    <Typography ref={ref} variant="muted" className={className} {...props}>
      {children}
    </Typography>
  )
);
Muted.displayName = 'Muted';

/**
 * Code Components
 */
export interface CodeProps extends React.HTMLAttributes<HTMLElement> {
  children: React.ReactNode;
  className?: string;
  inline?: boolean;
}

export const Code = React.forwardRef<HTMLElement, CodeProps>(
  ({ children, className, inline = true, ...props }, ref) => {
    if (inline) {
      return (
        <code
          ref={ref as React.Ref<HTMLElement>}
          className={cn(
            'relative rounded bg-muted px-1.5 py-0.5 font-mono text-sm font-medium',
            className
          )}
          {...props}
        >
          {children}
        </code>
      );
    }

    return (
      <pre
        ref={ref as React.Ref<HTMLPreElement>}
        className={cn(
          'overflow-x-auto rounded-lg bg-muted p-4 font-mono text-sm',
          className
        )}
        {...props}
      >
        <code>{children}</code>
      </pre>
    );
  }
);
Code.displayName = 'Code';

/**
 * Blockquote Component
 */
export interface BlockquoteProps extends React.BlockquoteHTMLAttributes<HTMLQuoteElement> {
  children: React.ReactNode;
  className?: string;
  cite?: string;
}

export const Blockquote = React.forwardRef<HTMLQuoteElement, BlockquoteProps>(
  ({ children, className, cite, ...props }, ref) => (
    <blockquote
      ref={ref}
      className={cn(
        'mt-6 border-l-4 border-primary pl-6 italic text-muted-foreground',
        className
      )}
      {...props}
    >
      {children}
      {cite && (
        <footer className="mt-2 text-sm">
          <cite className="not-italic">— {cite}</cite>
        </footer>
      )}
    </blockquote>
  )
);
Blockquote.displayName = 'Blockquote';

/**
 * Text with Gradient Effect
 */
export interface GradientTextProps extends React.HTMLAttributes<HTMLSpanElement> {
  children: React.ReactNode;
  className?: string;
  gradient?: 'primary' | 'accent' | 'success' | 'warning';
}

export const GradientText = React.forwardRef<HTMLSpanElement, GradientTextProps>(
  ({ children, className, gradient = 'primary', ...props }, ref) => {
    const gradientClass = gradient === 'primary' 
      ? 'text-gradient-primary' 
      : `text-gradient-${gradient}`;
      
    return (
      <span
        ref={ref}
        className={cn(gradientClass, className)}
        {...props}
      >
        {children}
      </span>
    );
  }
);
GradientText.displayName = 'GradientText';

/**
 * Typography Showcase Component
 * For demonstrating all typography variants
 */
export const TypographyShowcase: React.FC = () => {
  return (
    <div className="space-y-8 p-8">
      <div className="space-y-4">
        <H2>Typography Scale</H2>
        <div className="space-y-4">
          <H1>Heading 1 - Display Large</H1>
          <H2>Heading 2 - Display Medium</H2>
          <H3>Heading 3 - Display Small</H3>
          <H4>Heading 4 - Headline Large</H4>
          <H5>Heading 5 - Headline Medium</H5>
          <H6>Heading 6 - Headline Small</H6>
        </div>
      </div>

      <div className="space-y-4">
        <H2>Body Text</H2>
        <div className="space-y-4">
          <Lead>
            This is lead text used for important introductory content that needs to stand out from regular body text.
          </Lead>
          <BodyLarge>
            This is large body text used for emphasized content that needs more prominence than regular body text.
          </BodyLarge>
          <Body>
            This is regular body text used for most content. It provides excellent readability and comfortable reading experience across all devices.
          </Body>
          <BodySmall>
            This is small body text used for secondary information, captions, and less important content.
          </BodySmall>
          <Caption>This is caption text for labels, metadata, and auxiliary information.</Caption>
          <Overline>This is overline text for categories and section labels</Overline>
        </div>
      </div>

      <div className="space-y-4">
        <H2>Color Variants</H2>
        <div className="space-y-2">
          <Body color="default">Default text color</Body>
          <Body color="primary">Primary color text</Body>
          <Body color="secondary">Secondary color text</Body>
          <Body color="success">Success color text</Body>
          <Body color="warning">Warning color text</Body>
          <Body color="error">Error color text</Body>
          <Body color="muted">Muted color text</Body>
        </div>
      </div>

      <div className="space-y-4">
        <H2>Font Weights</H2>
        <div className="space-y-2">
          <Body weight="light">Light weight text</Body>
          <Body weight="normal">Normal weight text</Body>
          <Body weight="medium">Medium weight text</Body>
          <Body weight="semibold">Semibold weight text</Body>
          <Body weight="bold">Bold weight text</Body>
        </div>
      </div>

      <div className="space-y-4">
        <H2>Special Effects</H2>
        <div className="space-y-4">
          <H1>
            <GradientText>Gradient Text Effect</GradientText>
          </H1>
          <Body>
            Regular text with <Code>inline code</Code> formatting.
          </Body>
          <Code inline={false}>
{`// Block code example
const greeting = "Hello, World!";
console.log(greeting);`}
          </Code>
          <Blockquote cite="Typography Expert">
            Great typography is invisible to the reader, yet essential for readability and user experience.
          </Blockquote>
        </div>
      </div>
    </div>
  );
};

// Export all components
export {
  Typography as default,
  type TypographyProps,
  type TypographyVariant,
  type TypographyElement
};