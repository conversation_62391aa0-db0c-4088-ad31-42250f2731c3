'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { 
  webVitalsMonitor, 
  performanceBudget, 
  WebVitalsData, 
  PerformanceReport, 
  PerformanceAlert 
} from '@/lib/performance/web-vitals';

export interface UsePerformanceOptions {
  trackComponent?: boolean;
  componentName?: string;
  enableAlerts?: boolean;
  enableRealTimeMonitoring?: boolean;
  budgetThresholds?: {
    LCP?: number;
    FID?: number;
    CLS?: number;
    FCP?: number;
    TTFB?: number;
    INP?: number;
  };
}

export interface PerformanceMetrics {
  lcp?: WebVitalsData;
  fid?: WebVitalsData;
  cls?: WebVitalsData;
  fcp?: WebVitalsData;
  ttfb?: WebVitalsData;
  inp?: WebVitalsData;
}

export interface PerformanceState {
  metrics: PerformanceMetrics;
  report: PerformanceReport | null;
  alerts: PerformanceAlert[];
  isMonitoring: boolean;
  performanceScore: number;
  budgetStatus: {
    passed: boolean;
    violations: any[];
  };
}

export interface PerformanceActions {
  startMonitoring: () => void;
  stopMonitoring: () => void;
  clearAlerts: () => void;
  refreshMetrics: () => void;
  setUserId: (userId: string) => void;
  trackRender: (componentName: string) => () => void;
  trackAsyncOperation: <T>(operation: () => Promise<T>, name: string) => Promise<T>;
  trackUserInteraction: (action: string, details?: any) => void;
  measureCustomMetric: (name: string, value: number) => void;
}

export const usePerformance = (options: UsePerformanceOptions = {}): PerformanceState & PerformanceActions => {
  const {
    trackComponent = false,
    componentName = 'Unknown',
    enableAlerts = true,
    enableRealTimeMonitoring = true,
    budgetThresholds = {}
  } = options;

  const [state, setState] = useState<PerformanceState>({
    metrics: {},
    report: null,
    alerts: [],
    isMonitoring: false,
    performanceScore: 0,
    budgetStatus: { passed: true, violations: [] }
  });

  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const renderStartTime = useRef<number | null>(null);

  // Update budget thresholds
  useEffect(() => {
    Object.entries(budgetThresholds).forEach(([metric, threshold]) => {
      if (threshold) {
        performanceBudget.setBudget(metric, threshold);
      }
    });
  }, [budgetThresholds]);

  // Initialize monitoring
  useEffect(() => {
    if (enableRealTimeMonitoring) {
      webVitalsMonitor.startRealTimeMonitoring();
    }

    // Set up report callback
    webVitalsMonitor.onReport((report) => {
      const metrics = webVitalsMonitor.getMetrics();
      const metricsObject: PerformanceMetrics = {
        lcp: metrics.get('LCP'),
        fid: metrics.get('FID'),
        cls: metrics.get('CLS'),
        fcp: metrics.get('FCP'),
        ttfb: metrics.get('TTFB'),
        inp: metrics.get('INP')
      };

      const budgetStatus = performanceBudget.checkBudget(metrics);
      const performanceScore = webVitalsMonitor.getPerformanceScore();

      setState(prev => ({
        ...prev,
        metrics: metricsObject,
        report,
        performanceScore,
        budgetStatus,
        isMonitoring: true
      }));
    });

    // Set up alert callback
    if (enableAlerts) {
      webVitalsMonitor.onAlert((alert) => {
        setState(prev => ({
          ...prev,
          alerts: [...prev.alerts, alert]
        }));
      });
    }

    // Start periodic updates
    intervalRef.current = setInterval(() => {
      const metrics = webVitalsMonitor.getMetrics();
      const metricsObject: PerformanceMetrics = {
        lcp: metrics.get('LCP'),
        fid: metrics.get('FID'),
        cls: metrics.get('CLS'),
        fcp: metrics.get('FCP'),
        ttfb: metrics.get('TTFB'),
        inp: metrics.get('INP')
      };

      const budgetStatus = performanceBudget.checkBudget(metrics);
      const performanceScore = webVitalsMonitor.getPerformanceScore();

      setState(prev => ({
        ...prev,
        metrics: metricsObject,
        performanceScore,
        budgetStatus
      }));
    }, 5000);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [enableAlerts, enableRealTimeMonitoring]);

  // Track component render time
  useEffect(() => {
    if (trackComponent) {
      renderStartTime.current = performance.now();
      
      return () => {
        if (renderStartTime.current) {
          const renderTime = performance.now() - renderStartTime.current;
          measureCustomMetric(`${componentName}-render-time`, renderTime);
        }
      };
    }
  }, [trackComponent, componentName]);

  // Actions
  const startMonitoring = useCallback(() => {
    setState(prev => ({ ...prev, isMonitoring: true }));
  }, []);

  const stopMonitoring = useCallback(() => {
    setState(prev => ({ ...prev, isMonitoring: false }));
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
  }, []);

  const clearAlerts = useCallback(() => {
    setState(prev => ({ ...prev, alerts: [] }));
    webVitalsMonitor.clearAlerts();
  }, []);

  const refreshMetrics = useCallback(() => {
    const metrics = webVitalsMonitor.getMetrics();
    const metricsObject: PerformanceMetrics = {
      lcp: metrics.get('LCP'),
      fid: metrics.get('FID'),
      cls: metrics.get('CLS'),
      fcp: metrics.get('FCP'),
      ttfb: metrics.get('TTFB'),
      inp: metrics.get('INP')
    };

    const report = webVitalsMonitor.generateReport();
    const budgetStatus = performanceBudget.checkBudget(metrics);
    const performanceScore = webVitalsMonitor.getPerformanceScore();

    setState(prev => ({
      ...prev,
      metrics: metricsObject,
      report,
      performanceScore,
      budgetStatus
    }));
  }, []);

  const setUserId = useCallback((userId: string) => {
    webVitalsMonitor.setUserId(userId);
  }, []);

  const trackRender = useCallback((componentName: string) => {
    const startTime = performance.now();
    
    return () => {
      const endTime = performance.now();
      const renderTime = endTime - startTime;
      measureCustomMetric(`${componentName}-render`, renderTime);
    };
  }, []);

  const trackAsyncOperation = useCallback(async <T>(
    operation: () => Promise<T>,
    name: string
  ): Promise<T> => {
    const startTime = performance.now();
    
    try {
      const result = await operation();
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      measureCustomMetric(`${name}-duration`, duration);
      
      // Track success
      if (typeof window !== 'undefined' && window.gtag) {
        window.gtag('event', 'async_operation_success', {
          event_category: 'Performance',
          event_label: name,
          value: Math.round(duration)
        });
      }
      
      return result;
    } catch (error) {
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      measureCustomMetric(`${name}-error-duration`, duration);
      
      // Track error
      if (typeof window !== 'undefined' && window.gtag) {
        window.gtag('event', 'async_operation_error', {
          event_category: 'Performance',
          event_label: name,
          value: Math.round(duration)
        });
      }
      
      throw error;
    }
  }, []);

  const trackUserInteraction = useCallback((action: string, details?: any) => {
    const timestamp = performance.now();
    
    // Track interaction timing
    requestIdleCallback(() => {
      const processingTime = performance.now() - timestamp;
      measureCustomMetric(`${action}-interaction-time`, processingTime);
    });
    
    // Send to analytics
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'user_interaction', {
        event_category: 'Performance',
        event_label: action,
        custom_parameter_1: JSON.stringify(details)
      });
    }
  }, []);

  const measureCustomMetric = useCallback((name: string, value: number) => {
    // Send to performance API
    if (typeof window !== 'undefined' && window.fetch) {
      fetch('/api/performance/custom-metrics', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name,
          value,
          timestamp: Date.now(),
          url: window.location.href,
          route: window.location.pathname,
          userAgent: navigator.userAgent
        })
      }).catch(error => {
        console.warn('Failed to send custom metric:', error);
      });
    }
    
    // Create performance mark
    if (typeof window !== 'undefined' && window.performance && window.performance.mark) {
      try {
        window.performance.mark(`custom-${name}`);
        window.performance.measure(`custom-${name}-measure`, `custom-${name}`);
      } catch (error) {
        console.warn('Failed to create performance mark:', error);
      }
    }
  }, []);

  return {
    ...state,
    startMonitoring,
    stopMonitoring,
    clearAlerts,
    refreshMetrics,
    setUserId,
    trackRender,
    trackAsyncOperation,
    trackUserInteraction,
    measureCustomMetric
  };
};

// Hook for component-specific performance tracking
export const useComponentPerformance = (componentName: string) => {
  const [renderMetrics, setRenderMetrics] = useState({
    renderCount: 0,
    totalRenderTime: 0,
    averageRenderTime: 0,
    slowRenders: 0
  });

  const renderStartTime = useRef<number | null>(null);

  const startRender = useCallback(() => {
    renderStartTime.current = performance.now();
  }, []);

  const endRender = useCallback(() => {
    if (renderStartTime.current) {
      const renderTime = performance.now() - renderStartTime.current;
      
      setRenderMetrics(prev => {
        const newRenderCount = prev.renderCount + 1;
        const newTotalTime = prev.totalRenderTime + renderTime;
        const newAverageTime = newTotalTime / newRenderCount;
        const newSlowRenders = prev.slowRenders + (renderTime > 16 ? 1 : 0);
        
        return {
          renderCount: newRenderCount,
          totalRenderTime: newTotalTime,
          averageRenderTime: newAverageTime,
          slowRenders: newSlowRenders
        };
      });
      
      // Log slow renders
      if (renderTime > 16) {
        console.warn(`[Performance] Slow render detected in ${componentName}: ${renderTime.toFixed(2)}ms`);
      }
      
      // Send to monitoring
      if (typeof window !== 'undefined' && window.fetch) {
        fetch('/api/performance/component-metrics', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            component: componentName,
            renderTime,
            timestamp: Date.now(),
            url: window.location.href
          })
        }).catch(() => {
          // Silently fail
        });
      }
      
      renderStartTime.current = null;
    }
  }, [componentName]);

  useEffect(() => {
    startRender();
    return endRender;
  });

  return {
    ...renderMetrics,
    startRender,
    endRender
  };
};

// Hook for tracking specific metrics
export const useMetricTracker = () => {
  const trackTime = useCallback((name: string, startTime: number) => {
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    if (typeof window !== 'undefined' && window.fetch) {
      fetch('/api/performance/metrics', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          metric: {
            name,
            value: duration,
            timestamp: Date.now(),
            url: window.location.href,
            userAgent: navigator.userAgent
          }
        })
      }).catch(() => {
        // Silently fail
      });
    }
    
    return duration;
  }, []);

  const trackEvent = useCallback((name: string, value: number, metadata?: any) => {
    if (typeof window !== 'undefined' && window.fetch) {
      fetch('/api/performance/events', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          event: {
            name,
            value,
            metadata,
            timestamp: Date.now(),
            url: window.location.href,
            userAgent: navigator.userAgent
          }
        })
      }).catch(() => {
        // Silently fail
      });
    }
  }, []);

  return {
    trackTime,
    trackEvent
  };
};

export default usePerformance;