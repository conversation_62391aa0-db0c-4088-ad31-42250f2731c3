/**
 * Mobile-Optimized Loading Components
 * Provides touch-friendly loading states with mobile-specific optimizations
 */

'use client';

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { motion, AnimatePresence, useReducedMotion, PanInfo } from 'framer-motion';
import { cn } from '@/lib/utils';
import { fadeIn, slideInLeft } from '@/lib/animations';
import { Skeleton } from './skeleton';
import { LoadingSpinner } from './loading-states';

// Pull-to-refresh component
interface PullToRefreshProps {
  onRefresh: () => Promise<void>;
  children: React.ReactNode;
  threshold?: number;
  resistance?: number;
  refreshingThreshold?: number;
  className?: string;
  disabled?: boolean;
  showIndicator?: boolean;
}

export function PullToRefresh({
  onRefresh,
  children,
  threshold = 60,
  resistance = 2.5,
  refreshingThreshold = 80,
  className,
  disabled = false,
  showIndicator = true
}: PullToRefreshProps) {
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [pullDistance, setPullDistance] = useState(0);
  const [canPull, setCanPull] = useState(false);
  const shouldReduceMotion = useReducedMotion();
  const containerRef = useRef<HTMLDivElement>(null);

  const handlePanStart = useCallback(() => {
    if (disabled || isRefreshing) return false;
    
    const container = containerRef.current;
    if (!container) return false;
    
    // Only allow pull-to-refresh at the top of the page
    const isAtTop = container.scrollTop === 0;
    setCanPull(isAtTop);
    return isAtTop;
  }, [disabled, isRefreshing]);

  const handlePan = useCallback((event: any, info: PanInfo) => {
    if (!canPull || disabled || isRefreshing) return;
    
    const dragY = Math.max(0, info.offset.y);
    const resistedDistance = dragY / resistance;
    setPullDistance(Math.min(resistedDistance, threshold * 1.5));
  }, [canPull, disabled, isRefreshing, resistance, threshold]);

  const handlePanEnd = useCallback(async (event: any, info: PanInfo) => {
    if (!canPull || disabled || isRefreshing) {
      setPullDistance(0);
      return;
    }

    const dragY = Math.max(0, info.offset.y);
    const resistedDistance = dragY / resistance;

    if (resistedDistance >= refreshingThreshold) {
      setIsRefreshing(true);
      setPullDistance(threshold);
      
      try {
        await onRefresh();
      } finally {
        setIsRefreshing(false);
        setPullDistance(0);
      }
    } else {
      setPullDistance(0);
    }
    
    setCanPull(false);
  }, [canPull, disabled, isRefreshing, resistance, refreshingThreshold, threshold, onRefresh]);

  const pullProgress = Math.min(pullDistance / threshold, 1);
  const shouldShowIndicator = showIndicator && (pullDistance > 0 || isRefreshing);

  return (
    <div className={cn('relative overflow-hidden', className)}>
      {/* Pull indicator */}
      <AnimatePresence>
        {shouldShowIndicator && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            className="absolute top-0 left-1/2 transform -translate-x-1/2 z-10"
            style={{
              transform: `translateX(-50%) translateY(${Math.max(0, pullDistance - 30)}px)`
            }}
          >
            <div className="bg-white rounded-full shadow-lg p-3 border">
              {isRefreshing ? (
                <LoadingSpinner size="sm" />
              ) : (
                <motion.svg
                  className="w-5 h-5 text-gray-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  animate={{
                    rotate: pullProgress * 180
                  }}
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 14l-7 7m0 0l-7-7m7 7V3"
                  />
                </motion.svg>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Content container */}
      <motion.div
        ref={containerRef}
        className="h-full overflow-auto"
        style={{
          transform: shouldReduceMotion ? undefined : `translateY(${pullDistance}px)`
        }}
        onPanStart={handlePanStart}
        onPan={handlePan}
        onPanEnd={handlePanEnd}
        drag={!disabled && !isRefreshing ? "y" : false}
        dragConstraints={{ top: 0, bottom: 0 }}
        dragElastic={0.2}
      >
        {children}
      </motion.div>
    </div>
  );
}

// Mobile skeleton list
interface MobileSkeletonListProps {
  itemCount?: number;
  showAvatar?: boolean;
  showImage?: boolean;
  itemHeight?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function MobileSkeletonList({
  itemCount = 5,
  showAvatar = true,
  showImage = false,
  itemHeight = 'md',
  className
}: MobileSkeletonListProps) {
  const heights = {
    sm: 'h-16',
    md: 'h-20',
    lg: 'h-24'
  };

  return (
    <div className={cn('space-y-3', className)}>
      {Array.from({ length: itemCount }, (_, i) => (
        <motion.div
          key={i}
          variants={fadeIn}
          initial="initial"
          animate="animate"
          transition={{ delay: i * 0.1 }}
          className={cn(
            'flex items-center space-x-3 p-3 bg-white rounded-lg border',
            heights[itemHeight]
          )}
        >
          {showAvatar && (
            <Skeleton variant="avatar" className="w-10 h-10 flex-shrink-0" />
          )}
          
          <div className="flex-1 space-y-2">
            <Skeleton variant="text" className="h-4 w-3/4" />
            <Skeleton variant="text" className="h-3 w-1/2" />
          </div>
          
          {showImage && (
            <Skeleton variant="image" className="w-12 h-12 flex-shrink-0" />
          )}
        </motion.div>
      ))}
    </div>
  );
}

// Mobile card skeleton
interface MobileSkeletonCardProps {
  showImage?: boolean;
  showActions?: boolean;
  className?: string;
  compact?: boolean;
}

export function MobileSkeletonCard({
  showImage = true,
  showActions = false,
  className,
  compact = false
}: MobileSkeletonCardProps) {
  return (
    <motion.div
      variants={fadeIn}
      initial="initial"
      animate="animate"
      className={cn(
        'bg-white rounded-lg border overflow-hidden',
        compact ? 'p-3' : 'p-4',
        className
      )}
    >
      {showImage && (
        <Skeleton 
          variant="image" 
          className={cn(
            'w-full mb-3',
            compact ? 'h-32' : 'h-40'
          )} 
        />
      )}
      
      <div className="space-y-3">
        <div className="space-y-2">
          <Skeleton variant="text" className="h-5 w-3/4" />
          <Skeleton variant="text" className="h-4 w-full" />
          <Skeleton variant="text" className="h-4 w-2/3" />
        </div>
        
        <div className="flex items-center justify-between">
          <Skeleton variant="text" className="h-4 w-20" />
          <Skeleton variant="text" className="h-6 w-16" />
        </div>
        
        {showActions && (
          <div className="flex space-x-2 pt-2">
            <Skeleton variant="button" className="h-8 flex-1" />
            <Skeleton variant="button" className="h-8 w-16" />
          </div>
        )}
      </div>
    </motion.div>
  );
}

// Mobile loading overlay
interface MobileLoadingOverlayProps {
  show: boolean;
  message?: string;
  progress?: number;
  onCancel?: () => void;
  backdrop?: boolean;
  className?: string;
}

export function MobileLoadingOverlay({
  show,
  message,
  progress,
  onCancel,
  backdrop = true,
  className
}: MobileLoadingOverlayProps) {
  if (!show) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className={cn(
          'fixed inset-0 z-50 flex items-center justify-center',
          backdrop && 'bg-black/20 backdrop-blur-sm',
          className
        )}
      >
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          className="bg-white rounded-lg p-6 mx-4 max-w-sm w-full shadow-xl"
        >
          <div className="text-center space-y-4">
            <LoadingSpinner size="lg" />
            
            {message && (
              <p className="text-sm text-gray-600">{message}</p>
            )}
            
            {typeof progress === 'number' && (
              <div className="space-y-2">
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <motion.div
                    className="bg-blue-500 h-2 rounded-full"
                    initial={{ width: 0 }}
                    animate={{ width: `${progress}%` }}
                    transition={{ duration: 0.3 }}
                  />
                </div>
                <p className="text-xs text-gray-500">{Math.round(progress)}%</p>
              </div>
            )}
            
            {onCancel && (
              <button
                onClick={onCancel}
                className="w-full mt-4 px-4 py-2 text-sm text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                Cancel
              </button>
            )}
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
}

// Touch-friendly loading button
interface MobileLoadingButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  isLoading?: boolean;
  loadingText?: string;
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  fullWidth?: boolean;
  children: React.ReactNode;
}

export function MobileLoadingButton({
  isLoading = false,
  loadingText,
  variant = 'primary',
  size = 'md',
  fullWidth = false,
  children,
  className,
  disabled,
  ...props
}: MobileLoadingButtonProps) {
  const baseClasses = 'inline-flex items-center justify-center rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none';
  
  const variantClasses = {
    primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500',
    secondary: 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500',
    outline: 'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-blue-500'
  };
  
  const sizeClasses = {
    sm: 'px-3 py-2 text-sm h-9',
    md: 'px-4 py-3 text-base h-11',
    lg: 'px-6 py-4 text-lg h-14'
  };

  return (
    <motion.button
      className={cn(
        baseClasses,
        variantClasses[variant],
        sizeClasses[size],
        fullWidth && 'w-full',
        className
      )}
      disabled={disabled || isLoading}
      whileTap={{ scale: 0.98 }}
      transition={{ duration: 0.1 }}
      {...props}
    >
      <AnimatePresence mode="wait">
        {isLoading ? (
          <motion.div
            key="loading"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="flex items-center space-x-2"
          >
            <LoadingSpinner size="sm" />
            {loadingText && <span>{loadingText}</span>}
          </motion.div>
        ) : (
          <motion.span
            key="content"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            {children}
          </motion.span>
        )}
      </AnimatePresence>
    </motion.button>
  );
}

// Swipe to reveal loading action
interface SwipeActionProps {
  onSwipe: () => Promise<void>;
  children: React.ReactNode;
  actionText?: string;
  actionIcon?: React.ReactNode;
  threshold?: number;
  className?: string;
  disabled?: boolean;
}

export function SwipeAction({
  onSwipe,
  children,
  actionText = 'Release to continue',
  actionIcon,
  threshold = 100,
  className,
  disabled = false
}: SwipeActionProps) {
  const [swipeDistance, setSwipeDistance] = useState(0);
  const [isExecuting, setIsExecuting] = useState(false);
  const [isDragging, setIsDragging] = useState(false);

  const handlePan = useCallback((event: any, info: PanInfo) => {
    if (disabled || isExecuting) return;
    
    const distance = Math.max(0, info.offset.x);
    setSwipeDistance(Math.min(distance, threshold * 1.5));
  }, [disabled, isExecuting, threshold]);

  const handlePanStart = useCallback(() => {
    if (!disabled && !isExecuting) {
      setIsDragging(true);
    }
  }, [disabled, isExecuting]);

  const handlePanEnd = useCallback(async (event: any, info: PanInfo) => {
    setIsDragging(false);
    
    if (disabled || isExecuting) {
      setSwipeDistance(0);
      return;
    }

    const distance = Math.max(0, info.offset.x);
    
    if (distance >= threshold) {
      setIsExecuting(true);
      try {
        await onSwipe();
      } finally {
        setIsExecuting(false);
        setSwipeDistance(0);
      }
    } else {
      setSwipeDistance(0);
    }
  }, [disabled, isExecuting, threshold, onSwipe]);

  const progress = Math.min(swipeDistance / threshold, 1);
  const shouldShowAction = swipeDistance > threshold * 0.3;

  return (
    <div className={cn('relative overflow-hidden rounded-lg', className)}>
      {/* Background action */}
      <AnimatePresence>
        {shouldShowAction && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-green-500 flex items-center justify-end pr-4 text-white"
          >
            <div className="flex items-center space-x-2">
              {actionIcon || (
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              )}
              <span className="text-sm font-medium">{actionText}</span>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Content */}
      <motion.div
        className="relative bg-white border rounded-lg"
        style={{
          transform: `translateX(${swipeDistance}px)`
        }}
        drag={!disabled && !isExecuting ? 'x' : false}
        dragConstraints={{ left: 0, right: 0 }}
        dragElastic={0.2}
        onPanStart={handlePanStart}
        onPan={handlePan}
        onPanEnd={handlePanEnd}
      >
        <div className="p-4">
          {isExecuting ? (
            <div className="flex items-center space-x-3">
              <LoadingSpinner size="sm" />
              <span className="text-sm text-gray-600">Processing...</span>
            </div>
          ) : (
            children
          )}
        </div>
        
        {/* Progress indicator */}
        {isDragging && (
          <div className="absolute bottom-0 left-0 h-1 bg-green-500 transition-all duration-100"
               style={{ width: `${progress * 100}%` }} />
        )}
      </motion.div>
    </div>
  );
}

// Mobile-specific loading states hook
export function useMobileLoading() {
  const [isLoading, setIsLoading] = useState(false);
  const [loadingMessage, setLoadingMessage] = useState('');
  const [progress, setProgress] = useState(0);

  const startLoading = useCallback((message?: string) => {
    setIsLoading(true);
    setLoadingMessage(message || '');
    setProgress(0);
  }, []);

  const updateProgress = useCallback((value: number, message?: string) => {
    setProgress(Math.max(0, Math.min(100, value)));
    if (message) setLoadingMessage(message);
  }, []);

  const stopLoading = useCallback(() => {
    setIsLoading(false);
    setLoadingMessage('');
    setProgress(0);
  }, []);

  return {
    isLoading,
    loadingMessage,
    progress,
    startLoading,
    updateProgress,
    stopLoading
  };
}