'use client';

import { ReactNode } from 'react';
import { motion, AnimatePresence, PanInfo } from 'framer-motion';
import { X } from 'lucide-react';
import { cn } from '@/lib/utils';
import { hapticFeedback, mobileClasses } from '@/lib/mobile';

interface MobileSheetProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: ReactNode;
  className?: string;
  showHandle?: boolean;
  dragToClose?: boolean;
}

const bottomSheetVariants = {
  hidden: {
    y: '100%',
    opacity: 0,
  },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      type: 'spring' as const,
      damping: 30,
      stiffness: 400,
    },
  },
  exit: {
    y: '100%',
    opacity: 0,
    transition: {
      duration: 0.2,
    },
  },
};

export function MobileSheet({
  isOpen,
  onClose,
  title,
  children,
  className,
  showHandle = true,
  dragToClose = true,
}: MobileSheetProps) {
  const handleDragEnd = (event: MouseEvent | TouchEvent | PointerEvent, info: PanInfo) => {
    if (dragToClose && (info.velocity.y > 500 || info.offset.y > 200)) {
      hapticFeedback.light();
      onClose();
    }
  };

  return (
    <>
      {/* Backdrop */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            className="fixed inset-0 bg-black/50 z-40 md:hidden"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
          />
        )}
      </AnimatePresence>

      {/* Sheet */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            className={cn(
              'fixed inset-x-0 bottom-0 z-50 bg-white shadow-xl md:hidden',
              'max-h-[90vh] overflow-hidden',
              mobileClasses.mobileCard,
              mobileClasses.safeAreaPadding,
              className
            )}
            variants={bottomSheetVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            drag={dragToClose ? 'y' : false}
            dragConstraints={{ top: 0 }}
            dragElastic={0.1}
            onDragEnd={handleDragEnd}
          >
            {/* Handle bar */}
            {showHandle && (
              <div className="flex justify-center py-3">
                <div className="w-12 h-1 bg-gray-300 rounded-full" />
              </div>
            )}

            {/* Header */}
            {title && (
              <div className="flex items-center justify-between px-4 py-3 border-b border-gray-200">
                <h2 className="text-lg font-semibold text-gray-900">{title}</h2>
                <button
                  onClick={() => {
                    hapticFeedback.light();
                    onClose();
                  }}
                  className={cn(
                    'rounded-full p-2 hover:bg-gray-100 transition-colors',
                    mobileClasses.touchTarget
                  )}
                >
                  <X className="h-5 w-5 text-gray-500" />
                </button>
              </div>
            )}

            {/* Content */}
            <div className={cn(
              'overflow-y-auto',
              mobileClasses.mobilePadding
            )}>
              {children}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
}

interface MobileActionSheetProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  description?: string;
  actions: Array<{
    label: string;
    onClick: () => void;
    variant?: 'default' | 'destructive';
    icon?: ReactNode;
  }>;
}

export function MobileActionSheet({
  isOpen,
  onClose,
  title,
  description,
  actions,
}: MobileActionSheetProps) {
  return (
    <MobileSheet isOpen={isOpen} onClose={onClose} showHandle={false}>
      {/* Header */}
      {(title || description) && (
        <div className="text-center px-4 py-6 border-b border-gray-200">
          {title && (
            <h3 className="text-lg font-semibold text-gray-900 mb-1">{title}</h3>
          )}
          {description && (
            <p className="text-sm text-gray-600">{description}</p>
          )}
        </div>
      )}

      {/* Actions */}
      <div className="py-2">
        {actions.map((action, index) => (
          <button
            key={index}
            onClick={() => {
              hapticFeedback.light();
              action.onClick();
              onClose();
            }}
            className={cn(
              'w-full flex items-center gap-3 px-4 py-4 text-left hover:bg-gray-50 transition-colors',
              mobileClasses.touchTarget,
              action.variant === 'destructive' && 'text-red-600'
            )}
          >
            {action.icon && (
              <span className={cn(
                'flex-shrink-0',
                action.variant === 'destructive' ? 'text-red-600' : 'text-gray-500'
              )}>
                {action.icon}
              </span>
            )}
            <span className="font-medium">{action.label}</span>
          </button>
        ))}
      </div>

      {/* Cancel button */}
      <div className="border-t border-gray-200 px-4 py-3">
        <button
          onClick={() => {
            hapticFeedback.light();
            onClose();
          }}
          className={cn(
            'w-full py-3 text-center font-medium text-gray-600 hover:bg-gray-50 rounded-lg transition-colors',
            mobileClasses.touchTarget
          )}
        >
          Cancel
        </button>
      </div>
    </MobileSheet>
  );
}