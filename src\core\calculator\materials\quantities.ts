/**
 * Material Quantities Calculation
 * Based on IS codes and Indian construction standards
 */

import type { CalculationInput } from '../types';
import { MATERIAL_CONSUMPTION_RATES, WASTAGE_FACTORS } from '../constants';

export interface MaterialQuantityResult {
  material: string;
  quantity: number;
  unit: string;
  rate: number;
  amount: number;
  wastage: number;
  totalQuantity: number;
  totalAmount: number;
}

/**
 * Calculate material quantities based on construction area and quality tier
 */
export function calculateMaterialQuantities(
  input: CalculationInput
): MaterialQuantityResult[] {
  const { builtUpArea, floors, qualityTier } = input;

  // Calculate total construction area
  const totalArea = builtUpArea * (floors + 1); // +1 for ground floor

  // Current market rates (per unit) - updated for 2024
  const materialRates = {
    cement: 420, // per 50kg bag
    steel: 68, // per kg
    bricks: 8, // per piece
    sand: 45, // per cft
    aggregate: 55, // per cft
  };

  const results: MaterialQuantityResult[] = [];

  MATERIAL_CONSUMPTION_RATES.forEach(materialData => {
    const { material, unit, qualityVariation } = materialData;

    // Get consumption rate for the quality tier
    const consumptionRate =
      qualityVariation?.[qualityTier] || materialData.consumptionPerSqft;

    // Calculate base quantity
    const baseQuantity = totalArea * consumptionRate;

    // Get wastage factor
    const wastage =
      WASTAGE_FACTORS[material as keyof typeof WASTAGE_FACTORS] ||
      WASTAGE_FACTORS.default;

    // Calculate wastage quantity
    const wastageQuantity = baseQuantity * wastage;

    // Total quantity including wastage
    const totalQuantity = baseQuantity + wastageQuantity;

    // Get material rate
    const rate = materialRates[material as keyof typeof materialRates];

    // Calculate costs
    const baseAmount = baseQuantity * rate;
    const totalAmount = totalQuantity * rate;

    results.push({
      material: material.charAt(0).toUpperCase() + material.slice(1),
      quantity: Math.round(baseQuantity * 100) / 100,
      unit,
      rate,
      amount: Math.round(baseAmount),
      wastage: Math.round(wastage * 100), // percentage
      totalQuantity: Math.round(totalQuantity * 100) / 100,
      totalAmount: Math.round(totalAmount),
    });
  });

  return results;
}

/**
 * Calculate cement consumption based on IS 456 standards
 */
export function calculateCementQuantity(
  totalArea: number,
  qualityTier: 'smart' | 'premium' | 'luxury'
): MaterialQuantityResult {
  // Cement consumption per sqft (bags of 50kg each)
  const consumptionRates = {
    smart: 0.36, // M20 grade concrete
    premium: 0.38, // M25 grade concrete
    luxury: 0.42, // M30+ grade concrete
  };

  const consumptionRate = consumptionRates[qualityTier];
  const baseQuantity = totalArea * consumptionRate;
  const wastage = WASTAGE_FACTORS.cement;
  const wastageQuantity = baseQuantity * wastage;
  const totalQuantity = baseQuantity + wastageQuantity;

  const rate = 420; // per 50kg bag
  const baseAmount = baseQuantity * rate;
  const totalAmount = totalQuantity * rate;

  return {
    material: 'Cement',
    quantity: Math.round(baseQuantity * 100) / 100,
    unit: 'bags (50kg)',
    rate,
    amount: Math.round(baseAmount),
    wastage: Math.round(wastage * 100),
    totalQuantity: Math.round(totalQuantity * 100) / 100,
    totalAmount: Math.round(totalAmount),
  };
}

/**
 * Calculate steel reinforcement based on IS 456 and IS 13920
 */
export function calculateSteelQuantity(
  totalArea: number,
  qualityTier: 'smart' | 'premium' | 'luxury'
): MaterialQuantityResult {
  // Steel consumption per sqft (kg)
  const consumptionRates = {
    smart: 3.8, // Fe415 grade
    premium: 4.0, // Fe500 grade
    luxury: 4.5, // Fe500D/Fe550 grade
  };

  const consumptionRate = consumptionRates[qualityTier];
  const baseQuantity = totalArea * consumptionRate;
  const wastage = WASTAGE_FACTORS.steel;
  const wastageQuantity = baseQuantity * wastage;
  const totalQuantity = baseQuantity + wastageQuantity;

  const rate = 68; // per kg
  const baseAmount = baseQuantity * rate;
  const totalAmount = totalQuantity * rate;

  return {
    material: 'Steel',
    quantity: Math.round(baseQuantity),
    unit: 'kg',
    rate,
    amount: Math.round(baseAmount),
    wastage: Math.round(wastage * 100),
    totalQuantity: Math.round(totalQuantity),
    totalAmount: Math.round(totalAmount),
  };
}

/**
 * Calculate brick quantity based on IS 1905
 */
export function calculateBrickQuantity(
  totalArea: number,
  qualityTier: 'smart' | 'premium' | 'luxury'
): MaterialQuantityResult {
  // Brick consumption per sqft (pieces for 9" wall)
  const consumptionRates = {
    smart: 8, // Standard red clay bricks
    premium: 7, // Fly ash bricks/AAC blocks
    luxury: 6, // Premium AAC blocks
  };

  const consumptionRate = consumptionRates[qualityTier];
  const baseQuantity = totalArea * consumptionRate;
  const wastage = WASTAGE_FACTORS.bricks;
  const wastageQuantity = baseQuantity * wastage;
  const totalQuantity = baseQuantity + wastageQuantity;

  const rate = 8; // per piece
  const baseAmount = baseQuantity * rate;
  const totalAmount = totalQuantity * rate;

  return {
    material: 'Bricks',
    quantity: Math.round(baseQuantity),
    unit: 'pieces',
    rate,
    amount: Math.round(baseAmount),
    wastage: Math.round(wastage * 100),
    totalQuantity: Math.round(totalQuantity),
    totalAmount: Math.round(totalAmount),
  };
}

/**
 * Calculate sand quantity based on IS 383
 */
export function calculateSandQuantity(
  totalArea: number,
  qualityTier: 'smart' | 'premium' | 'luxury'
): MaterialQuantityResult {
  // Sand consumption per sqft (cubic feet)
  const consumptionRates = {
    smart: 0.85, // Standard river sand
    premium: 0.816, // M-sand (manufactured sand)
    luxury: 0.78, // Premium M-sand
  };

  const consumptionRate = consumptionRates[qualityTier];
  const baseQuantity = totalArea * consumptionRate;
  const wastage = WASTAGE_FACTORS.sand;
  const wastageQuantity = baseQuantity * wastage;
  const totalQuantity = baseQuantity + wastageQuantity;

  const rate = 45; // per cft
  const baseAmount = baseQuantity * rate;
  const totalAmount = totalQuantity * rate;

  return {
    material: 'Sand',
    quantity: Math.round(baseQuantity * 100) / 100,
    unit: 'cft',
    rate,
    amount: Math.round(baseAmount),
    wastage: Math.round(wastage * 100),
    totalQuantity: Math.round(totalQuantity * 100) / 100,
    totalAmount: Math.round(totalAmount),
  };
}

/**
 * Calculate aggregate quantity based on IS 383
 */
export function calculateAggregateQuantity(
  totalArea: number,
  qualityTier: 'smart' | 'premium' | 'luxury'
): MaterialQuantityResult {
  // Aggregate consumption per sqft (cubic feet)
  const consumptionRates = {
    smart: 0.65, // 20mm and 10mm aggregate
    premium: 0.608, // Graded aggregate
    luxury: 0.58, // Premium graded aggregate
  };

  const consumptionRate = consumptionRates[qualityTier];
  const baseQuantity = totalArea * consumptionRate;
  const wastage = WASTAGE_FACTORS.aggregate;
  const wastageQuantity = baseQuantity * wastage;
  const totalQuantity = baseQuantity + wastageQuantity;

  const rate = 55; // per cft
  const baseAmount = baseQuantity * rate;
  const totalAmount = totalQuantity * rate;

  return {
    material: 'Aggregate',
    quantity: Math.round(baseQuantity * 100) / 100,
    unit: 'cft',
    rate,
    amount: Math.round(baseAmount),
    wastage: Math.round(wastage * 100),
    totalQuantity: Math.round(totalQuantity * 100) / 100,
    totalAmount: Math.round(totalAmount),
  };
}

/**
 * Get material summary for a construction project
 */
export function getMaterialSummary(input: CalculationInput) {
  const materialQuantities = calculateMaterialQuantities(input);

  const totalMaterialCost = materialQuantities.reduce(
    (sum, material) => sum + material.totalAmount,
    0
  );

  return {
    materials: materialQuantities,
    totalCost: Math.round(totalMaterialCost),
    costPerSqft: Math.round(
      totalMaterialCost / (input.builtUpArea * (input.floors + 1))
    ),
  };
}
