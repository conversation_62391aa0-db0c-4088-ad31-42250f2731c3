Spoke Document 1: UI/UX Specification for "The Clarity Engine" v2.0
===================================================================

**Document ID:** SD-001  
**Version:** 2.0  
**Owner:** Lead UI/UX Designer  
**Audience:** Frontend Developers, UI/UX Designers, Product Manager, QA Team  
**Linked to:** Master PRD v2.0  
**Status:** Final - Ready for Design & Development

* * *

Table of Contents
-----------------

**Part 1: Design Foundation**

1.  Design System & Philosophy
2.  Brand Guidelines & Visual Identity
3.  Component Library Specifications
4.  Accessibility Standards

**Part 2: User Journey Flows** 5. Complete User Flow Diagrams 6. Navigation Architecture 7. State Management & Transitions 8. Error States & Edge Cases

**Part 3: Screen-by-Screen Specifications** 9. Landing & Onboarding Screens 10. Calculator Journey Screens 11. Digital Showroom Interface 12. Dashboard & Reports 13. Admin Panel Interface

**Part 4: Interaction Patterns** 14. Micro-interactions & Animations 15. Gesture Controls (Mobile) 16. Responsive Behavior 17. Performance Considerations

**Part 5: Implementation Guidelines** 18. Frontend Architecture 19. Component Development Guide 20. Testing Requirements 21. Handoff Specifications

* * *

Part 1: Design Foundation
-------------------------

### 1\. Design System & Philosophy

#### 1.1 Core Design Principles

javascript

    const designPrinciples = {
      // Principle 1: Progressive Disclosure
      progressiveDisclosure: {
        definition: 'Reveal complexity gradually',
        implementation: [
          'Start with 3 simple questions for quick estimate',
          'Show advanced options only when requested',
          'Use expandable sections for detailed information',
          'Implement smart defaults to reduce decisions'
        ]
      },
      
      // Principle 2: Visual Feedback
      visualFeedback: {
        definition: 'Every action has immediate visual response',
        implementation: [
          'Instant price updates (< 100ms)',
          'Smooth transitions between states',
          'Loading skeletons for async operations',
          'Success animations for completed actions'
        ]
      },
      
      // Principle 3: Trust Through Transparency
      transparency: {
        definition: 'Show the why behind every number',
        implementation: [
          'Breakdown tooltips on hover',
          'Calculation explanations',
          'Source citations for data',
          'Confidence indicators'
        ]
      },
      
      // Principle 4: Mobile-First Thumb-Friendly
      mobileFirst: {
        definition: 'Designed for one-handed mobile use',
        implementation: [
          'Primary actions in bottom 20% of screen',
          'Minimum tap target 44x44px',
          'Swipe gestures for navigation',
          'Sticky headers with key info'
        ]
      }
    };

#### 1.2 Design Tokens

scss

    // Colors
    $colors: (
      // Primary Palette
      primary: (
        50: #EEF2FF,
        100: #E0E7FF,
        200: #C7D2FE,
        300: #A5B4FC,
        400: #818CF8,
        500: #6366F1, // Main primary
        600: #4F46E5,
        700: #4338CA,
        800: #3730A3,
        900: #312E81
      ),
      
      // Neutral Palette
      neutral: (
        50: #F9FAFB,
        100: #F3F4F6,
        200: #E5E7EB,
        300: #D1D5DB,
        400: #9CA3AF,
        500: #6B7280,
        600: #4B5563,
        700: #374151,
        800: #1F2937,
        900: #111827
      ),
      
      // Semantic Colors
      success: #10B981,
      warning: #F59E0B,
      error: #EF4444,
      info: #3B82F6
    );
    
    // Typography
    $typography: (
      // Font Families
      fontFamily: (
        sans: 'Inter, system-ui, -apple-system, sans-serif',
        mono: 'JetBrains Mono, monospace'
      ),
      
      // Font Sizes (Mobile / Desktop)
      fontSize: (
        xs: (12px, 14px),
        sm: (14px, 16px),
        base: (16px, 18px),
        lg: (18px, 20px),
        xl: (20px, 24px),
        '2xl': (24px, 30px),
        '3xl': (30px, 36px),
        '4xl': (36px, 48px)
      ),
      
      // Line Heights
      lineHeight: (
        tight: 1.25,
        normal: 1.5,
        relaxed: 1.75
      ),
      
      // Font Weights
      fontWeight: (
        light: 300,
        regular: 400,
        medium: 500,
        semibold: 600,
        bold: 700
      )
    );
    
    // Spacing
    $spacing: (
      0: 0,
      1: 4px,
      2: 8px,
      3: 12px,
      4: 16px,
      5: 20px,
      6: 24px,
      8: 32px,
      10: 40px,
      12: 48px,
      16: 64px,
      20: 80px
    );
    
    // Breakpoints
    $breakpoints: (
      sm: 640px,
      md: 768px,
      lg: 1024px,
      xl: 1280px,
      '2xl': 1536px
    );
    
    // Shadows
    $shadows: (
      sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
      base: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
      md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
      lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
      xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
    );
    
    // Border Radius
    $borderRadius: (
      none: 0,
      sm: 2px,
      base: 4px,
      md: 6px,
      lg: 8px,
      xl: 12px,
      '2xl': 16px,
      full: 9999px
    );

### 2\. Brand Guidelines & Visual Identity

#### 2.1 Logo Specifications

javascript

    const logoSpecifications = {
      primary: {
        minWidth: 120,
        aspectRatio: '3:1',
        clearSpace: '0.5x height on all sides',
        colors: {
          light: '#6366F1 on white/light backgrounds',
          dark: 'White on dark backgrounds'
        }
      },
      
      icon: {
        size: 32,
        usage: 'App icon, favicon, small spaces',
        design: 'Simplified house with calculation symbols'
      },
      
      usage: {
        dont: [
          'Stretch or distort',
          'Change colors',
          'Add effects or shadows',
          'Use on busy backgrounds'
        ]
      }
    };

#### 2.2 Visual Language

javascript

    const visualLanguage = {
      imagery: {
        style: 'Clean, architectural photography',
        subjects: [
          'Modern Indian homes',
          'Construction progress shots',
          'Happy families in homes',
          'Material close-ups'
        ],
        treatment: 'Bright, optimistic, professional',
        overlay: 'Subtle gradient overlay for text readability'
      },
      
      iconography: {
        library: 'Lucide React',
        style: 'Outline icons, 2px stroke',
        size: {
          small: 16,
          medium: 20,
          large: 24
        },
        customIcons: [
          'house-calculator',
          'material-selector',
          'cost-breakdown',
          'project-timeline'
        ]
      },
      
      illustrations: {
        style: 'Isometric, minimalist',
        colorPalette: 'Primary blues with accent colors',
        usage: [
          'Empty states',
          'Onboarding tutorials',
          'Feature explanations',
          'Success states'
        ]
      }
    };

### 3\. Component Library Specifications

#### 3.1 Button Components

typescript

    // Button Component Specifications
    interface ButtonProps {
      variant: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive';
      size: 'sm' | 'md' | 'lg' | 'xl';
      fullWidth?: boolean;
      loading?: boolean;
      disabled?: boolean;
      leftIcon?: React.ReactNode;
      rightIcon?: React.ReactNode;
    }
    
    const buttonStyles = {
      // Base styles
      base: 'inline-flex items-center justify-center font-medium transition-all focus:outline-none focus:ring-2 focus:ring-offset-2',
      
      // Variants
      variants: {
        primary: 'bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500',
        secondary: 'bg-primary-100 text-primary-700 hover:bg-primary-200 focus:ring-primary-500',
        outline: 'border border-neutral-300 bg-white hover:bg-neutral-50 focus:ring-primary-500',
        ghost: 'hover:bg-neutral-100 focus:ring-neutral-400',
        destructive: 'bg-error text-white hover:bg-red-600 focus:ring-red-500'
      },
      
      // Sizes
      sizes: {
        sm: 'px-3 py-1.5 text-sm rounded-md',
        md: 'px-4 py-2 text-sm rounded-md',
        lg: 'px-6 py-3 text-base rounded-lg',
        xl: 'px-8 py-4 text-lg rounded-lg'
      },
      
      // States
      states: {
        disabled: 'opacity-50 cursor-not-allowed',
        loading: 'cursor-wait',
        fullWidth: 'w-full'
      }
    };
    
    // Usage Examples
    const buttonExamples = `
      // Primary CTA
      <Button variant="primary" size="lg">
        Calculate My Home Cost
      </Button>
      
      // Secondary Action
      <Button variant="secondary" leftIcon={<Download />}>
        Download Report
      </Button>
      
      // Loading State
      <Button variant="primary" loading>
        Calculating...
      </Button>
    `;

#### 3.2 Input Components

typescript

    // Input Field Specifications
    interface InputProps {
      type: 'text' | 'number' | 'email' | 'tel' | 'password' | 'search';
      size: 'sm' | 'md' | 'lg';
      state?: 'default' | 'error' | 'success';
      label?: string;
      helper?: string;
      prefix?: React.ReactNode;
      suffix?: React.ReactNode;
      required?: boolean;
    }
    
    const inputStyles = {
      wrapper: 'relative w-full',
      
      label: 'block text-sm font-medium text-neutral-700 mb-1.5',
      
      inputContainer: 'relative rounded-md shadow-sm',
      
      input: {
        base: 'block w-full rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-0',
        
        sizes: {
          sm: 'px-3 py-1.5 text-sm',
          md: 'px-4 py-2 text-base',
          lg: 'px-4 py-3 text-lg'
        },
        
        states: {
          default: 'border-neutral-300 focus:border-primary-500 focus:ring-primary-500',
          error: 'border-error pr-10 focus:border-error focus:ring-error',
          success: 'border-success focus:border-success focus:ring-success'
        }
      },
      
      helper: {
        base: 'mt-1.5 text-sm',
        states: {
          default: 'text-neutral-500',
          error: 'text-error',
          success: 'text-success'
        }
      }
    };
    
    // Special Input Types
    const specialInputs = {
      // Number Input with Stepper
      numberStepper: {
        wrapper: 'flex items-center',
        input: 'flex-1 text-center mx-2',
        button: 'p-2 rounded-md hover:bg-neutral-100',
        icon: 'w-4 h-4'
      },
      
      // Search Input
      search: {
        icon: 'absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400',
        input: 'pl-10',
        clearButton: 'absolute right-3 top-1/2 transform -translate-y-1/2'
      },
      
      // OTP Input
      otp: {
        container: 'flex gap-2',
        input: 'w-12 h-12 text-center text-lg font-semibold'
      }
    };

#### 3.3 Card Components

typescript

    // Card Component Specifications
    interface CardProps {
      variant: 'elevated' | 'outlined' | 'filled';
      padding: 'none' | 'sm' | 'md' | 'lg';
      interactive?: boolean;
      selected?: boolean;
    }
    
    const cardStyles = {
      base: 'rounded-lg transition-all',
      
      variants: {
        elevated: 'bg-white shadow-md hover:shadow-lg',
        outlined: 'bg-white border border-neutral-200 hover:border-neutral-300',
        filled: 'bg-neutral-50 hover:bg-neutral-100'
      },
      
      padding: {
        none: 'p-0',
        sm: 'p-4',
        md: 'p-6',
        lg: 'p-8'
      },
      
      interactive: 'cursor-pointer transform hover:scale-[1.02]',
      
      selected: 'ring-2 ring-primary-500 ring-offset-2'
    };
    
    // Specialized Card Types
    const specializedCards = {
      // Material Selection Card
      materialCard: {
        container: 'relative overflow-hidden',
        image: 'aspect-square object-cover',
        badge: 'absolute top-2 right-2 px-2 py-1 bg-white/90 rounded text-xs font-medium',
        content: 'p-4',
        title: 'font-semibold text-neutral-900',
        subtitle: 'text-sm text-neutral-500 mt-1',
        price: 'text-lg font-bold text-primary-600 mt-2',
        comparison: 'text-sm text-neutral-600'
      },
      
      // Stat Card
      statCard: {
        icon: 'w-12 h-12 rounded-lg bg-primary-100 text-primary-600 flex items-center justify-center',
        value: 'text-2xl font-bold text-neutral-900 mt-3',
        label: 'text-sm text-neutral-500 mt-1',
        trend: 'text-sm font-medium mt-2'
      },
      
      // Selection Card (Radio/Checkbox style)
      selectionCard: {
        container: 'relative p-4 cursor-pointer',
        radio: 'absolute top-4 right-4',
        content: 'pr-8',
        selected: 'border-primary-500 bg-primary-50'
      }
    };

#### 3.4 Modal & Dialog Components

typescript

    // Modal Specifications
    interface ModalProps {
      size: 'sm' | 'md' | 'lg' | 'xl' | 'full';
      position: 'center' | 'top' | 'right';
      showCloseButton?: boolean;
      closeOnOverlayClick?: boolean;
    }
    
    const modalStyles = {
      // Overlay
      overlay: 'fixed inset-0 bg-black/50 backdrop-blur-sm z-50',
      
      // Container
      container: {
        base: 'fixed z-50 overflow-y-auto',
        positions: {
          center: 'inset-0 flex items-center justify-center p-4',
          top: 'inset-x-0 top-0 flex justify-center pt-16',
          right: 'inset-y-0 right-0 flex'
        }
      },
      
      // Content
      content: {
        base: 'relative bg-white rounded-lg shadow-xl',
        sizes: {
          sm: 'max-w-sm w-full',
          md: 'max-w-md w-full',
          lg: 'max-w-lg w-full',
          xl: 'max-w-xl w-full',
          full: 'max-w-full w-full h-full rounded-none'
        }
      },
      
      // Sections
      header: 'px-6 py-4 border-b border-neutral-200',
      body: 'px-6 py-4',
      footer: 'px-6 py-4 border-t border-neutral-200 flex justify-end gap-3'
    };
    
    // Specialized Modals
    const specializedModals = {
      // Confirmation Dialog
      confirmation: {
        icon: 'w-12 h-12 rounded-full flex items-center justify-center mx-auto',
        iconColors: {
          warning: 'bg-warning-100 text-warning-600',
          error: 'bg-error-100 text-error-600',
          success: 'bg-success-100 text-success-600'
        },
        title: 'mt-4 text-center text-lg font-semibold',
        message: 'mt-2 text-center text-neutral-600'
      },
      
      // Image Preview Modal
      imagePreview: {
        container: 'relative',
        image: 'max-h-[80vh] w-auto mx-auto',
        toolbar: 'absolute top-4 right-4 flex gap-2',
        caption: 'text-center mt-4 text-neutral-600'
      }
    };

### 4\. Accessibility Standards

#### 4.1 WCAG 2.1 AA Compliance

javascript

    const accessibilityStandards = {
      // Color Contrast Requirements
      contrast: {
        normalText: {
          requirement: '4.5:1',
          implementation: 'All body text meets this ratio'
        },
        largeText: {
          requirement: '3:1',
          size: '18pt or 14pt bold',
          implementation: 'Headlines and large UI elements'
        },
        uiComponents: {
          requirement: '3:1',
          implementation: 'Buttons, inputs, icons against backgrounds'
        }
      },
      
      // Keyboard Navigation
      keyboard: {
        tabOrder: 'Logical flow from top-left to bottom-right',
        focusIndicators: '2px solid ring with 2px offset',
        skipLinks: 'Hidden "Skip to main content" link',
        shortcuts: {
          'Ctrl+/': 'Open command palette',
          'Esc': 'Close modals/dropdowns',
          'Arrow keys': 'Navigate within components'
        }
      },
      
      // Screen Reader Support
      screenReader: {
        landmarks: [
          '<nav role="navigation">',
          '<main role="main">',
          '<aside role="complementary">'
        ],
        headings: 'Proper hierarchy (h1 → h6)',
        images: 'Descriptive alt text for all images',
        forms: 'Associated labels for all inputs',
        liveRegions: 'aria-live for dynamic content updates'
      },
      
      // Touch Targets
      touch: {
        minSize: '44x44px',
        spacing: 'Minimum 8px between targets',
        gestures: 'All gestures have alternative controls'
      }
    };

#### 4.2 Inclusive Design Patterns

javascript

    const inclusivePatterns = {
      // Error Messaging
      errors: {
        visual: 'Red color + icon',
        textual: 'Clear error message',
        screenReader: 'aria-describedby with error text',
        example: `
          <input 
            aria-invalid="true"
            aria-describedby="email-error"
          />
          <span id="email-error" role="alert">
            <Icon /> Please enter a valid email
          </span>
        `
      },
      
      // Loading States
      loading: {
        visual: 'Spinner or skeleton',
        textual: 'Loading message',
        screenReader: 'aria-busy="true" and live region',
        example: `
          <div aria-busy="true" aria-live="polite">
            <Spinner />
            <span class="sr-only">Loading calculation results...</span>
          </div>
        `
      },
      
      // Form Validation
      validation: {
        inline: 'Validate on blur, not on type',
        messages: 'Positive reinforcement for correct inputs',
        submission: 'Disable submit until valid',
        review: 'Summary of errors at top of form'
      }
    };

* * *

Part 2: User Journey Flows
--------------------------

### 5\. Complete User Flow Diagrams

#### 5.1 Master User Flow

mermaid

    graph TD
        A[Landing Page] --> B{User Choice}
        B -->|Quick Estimate| C[Quick Flow]
        B -->|Detailed Estimate| D[Detailed Flow]
        
        C --> C1[City Selection]
        C1 --> C2[Plot Size Range]
        C2 --> C3[Floors]
        C3 --> C4[Quick Result]
        C4 --> C5{Convert to Detailed?}
        C5 -->|Yes| D
        C5 -->|No| Z[Exit/Save]
        
        D --> D1[Location & Plot]
        D1 --> D2[Buildable Area]
        D2 --> D3[Structure Type]
        D3 --> D4[Family Type]
        D4 --> D5[Layout Config]
        D5 --> D6[Quality Tier]
        D6 --> D7[Room Customization]
        D7 --> D8[Review Dashboard]
        D8 --> D9[Financial Planning]
        D9 --> D10[Generate Report]
        
        D10 --> E{Next Action}
        E -->|Share| F[Share Options]
        E -->|Save| G[Account Creation]
        E -->|Contact| H[Lead Generation]

#### 5.2 Detailed Screen Flow Specifications

javascript

    const screenFlows = {
      // Quick Estimate Flow
      quickFlow: {
        screens: [
          {
            id: 'QE1',
            name: 'City Selection',
            duration: '10 seconds',
            inputs: ['City dropdown'],
            validation: 'Required',
            next: 'QE2'
          },
          {
            id: 'QE2',
            name: 'Plot Size',
            duration: '10 seconds',
            inputs: ['Size range cards'],
            options: ['<1500', '1500-2500', '2500-4000', '>4000'],
            next: 'QE3'
          },
          {
            id: 'QE3',
            name: 'Floors',
            duration: '5 seconds',
            inputs: ['Floor cards'],
            options: ['G', 'G+1', 'G+2', 'G+3'],
            next: 'QE4'
          },
          {
            id: 'QE4',
            name: 'Quick Result',
            duration: '2 seconds',
            outputs: ['Price range', 'Basic breakdown'],
            actions: ['Get Detailed Estimate', 'Save', 'Share']
          }
        ],
        totalTime: '30 seconds',
        abandonmentPoints: ['Plot size selection'],
        conversionPoint: 'Get Detailed Estimate CTA'
      },
      
      // Detailed Flow Stages
      detailedFlow: {
        stages: [
          {
            name: 'Onboarding',
            screens: ['Location', 'Plot', 'Buildable', 'Structure'],
            duration: '2-3 minutes',
            criticalInputs: ['Plot dimensions', 'Structure type']
          },
          {
            name: 'Configuration',
            screens: ['Family', 'Layout', 'Quality'],
            duration: '3-4 minutes',
            criticalInputs: ['Room count', 'Quality tier']
          },
          {
            name: 'Customization',
            screens: ['Room-by-room', 'Special features'],
            duration: '10-15 minutes',
            skipOption: true
          },
          {
            name: 'Review',
            screens: ['Dashboard', 'Financial', 'Report'],
            duration: '3-5 minutes',
            conversionActions: ['Generate PDF', 'Contact vendor']
          }
        ]
      }
    };

### 6\. Navigation Architecture

#### 6.1 Primary Navigation Structure

javascript

    const navigationStructure = {
      // Main Navigation (Persistent)
      mainNav: {
        desktop: {
          position: 'Top horizontal bar',
          items: [
            { label: 'Home', path: '/', icon: 'Home' },
            { label: 'Calculate', path: '/calculate', icon: 'Calculator' },
            { label: 'My Projects', path: '/projects', icon: 'Folder', authRequired: true },
            { label: 'Resources', path: '/resources', icon: 'Book' },
            { label: 'About', path: '/about', icon: 'Info' }
          ],
          cta: { label: 'Start Building', path: '/calculate', style: 'primary' }
        },
        
        mobile: {
          position: 'Bottom tab bar',
          items: [
            { label: 'Home', path: '/', icon: 'Home' },
            { label: 'Calculate', path: '/calculate', icon: 'Calculator', highlighted: true },
            { label: 'Projects', path: '/projects', icon: 'Folder' },
            { label: 'Menu', action: 'openDrawer', icon: 'Menu' }
          ]
        }
      },
      
      // Calculator Navigation (Stepped)
      calculatorNav: {
        type: 'Progressive with back capability',
        stages: [
          { id: 1, label: 'Plot Details', icon: 'MapPin' },
          { id: 2, label: 'Structure', icon: 'Building' },
          { id: 3, label: 'Layout', icon: 'Layout' },
          { id: 4, label: 'Customize', icon: 'Palette' },
          { id: 5, label: 'Review', icon: 'FileCheck' }
        ],
        progress: {
          desktop: 'Horizontal stepper with labels',
          mobile: 'Progress bar with current step'
        }
      },
      
      // Breadcrumb Structure
      breadcrumbs: {
        show: ['Desktop only', 'Not in calculator flow'],
        separator: '/',
        homeIcon: true,
        maxItems: 4,
        truncation: 'Middle items collapse to "..."'
      }
    };

#### 6.2 Navigation Behaviors

javascript

    const navigationBehaviors = {
      // Back Button Behavior
      backButton: {
        browser: 'Maintains history for each major step',
        inApp: {
          calculator: 'Returns to previous step with data preserved',
          modal: 'Closes modal',
          general: 'Returns to previous page'
        },
        mobile: {
          android: 'Hardware back button supported',
          ios: 'Swipe gesture + UI back button'
        }
      },
      
      // Deep Linking
      deepLinking: {
        supported: [
          '/calculate?city=delhi&plot=2000',
          '/project/:id',
          '/material/:category/:id'
        ],
        behavior: 'Restore full state from URL parameters'
      },
      
      // Session Persistence
      persistence: {
        calculator: {
          storage: 'localStorage + URL params',
          duration: '30 days',
          cleared: 'On successful project save'
        },
        auth: {
          storage: 'Secure HTTP-only cookies',
          duration: '7 days active, 30 days remember me'
        }
      }
    };

### 7\. State Management & Transitions

#### 7.1 Application State Structure

typescript

    // Global State Structure
    interface AppState {
      // User State
      user: {
        isAuthenticated: boolean;
        profile: UserProfile | null;
        preferences: UserPreferences;
      };
      
      // Calculator State
      calculator: {
        mode: 'quick' | 'detailed';
        currentStep: number;
        data: {
          location: LocationData;
          plot: PlotData;
          structure: StructureData;
          layout: LayoutData;
          materials: MaterialSelections;
          calculations: CalculationResults;
        };
        ui: {
          isCalculating: boolean;
          validationErrors: ValidationError[];
          lastSaved: Date | null;
        };
      };
      
      // UI State
      ui: {
        theme: 'light' | 'dark' | 'system';
        sidebarOpen: boolean;
        activeModals: string[];
        notifications: Notification[];
        loadingStates: Record<string, boolean>;
      };
    }
    
    // State Transitions
    const stateTransitions = {
      calculator: {
        'NEXT_STEP': (state) => ({
          ...state,
          currentStep: Math.min(state.currentStep + 1, MAX_STEPS)
        }),
        
        'PREVIOUS_STEP': (state) => ({
          ...state,
          currentStep: Math.max(state.currentStep - 1, 1)
        }),
        
        'UPDATE_DATA': (state, payload) => ({
          ...state,
          data: {
            ...state.data,
            [payload.section]: payload.data
          }
        }),
        
        'CALCULATE': (state) => ({
          ...state,
          ui: { ...state.ui, isCalculating: true }
        })
      }
    };

#### 7.2 Page Transitions

javascript

    const pageTransitions = {
      // Default Page Transition
      default: {
        entering: {
          from: { opacity: 0, y: 20 },
          to: { opacity: 1, y: 0 },
          duration: 300,
          easing: 'easeOut'
        },
        exiting: {
          from: { opacity: 1, y: 0 },
          to: { opacity: 0, y: -20 },
          duration: 200,
          easing: 'easeIn'
        }
      },
      
      // Calculator Step Transitions
      calculatorSteps: {
        forward: {
          entering: {
            from: { opacity: 0, x: 100 },
            to: { opacity: 1, x: 0 },
            duration: 400,
            easing: 'easeOut'
          },
          exiting: {
            from: { opacity: 1, x: 0 },
            to: { opacity: 0, x: -100 },
            duration: 300,
            easing: 'easeIn'
          }
        },
        
        backward: {
          entering: {
            from: { opacity: 0, x: -100 },
            to: { opacity: 1, x: 0 },
            duration: 400,
            easing: 'easeOut'
          },
          exiting: {
            from: { opacity: 1, x: 0 },
            to: { opacity: 0, x: 100 },
            duration: 300,
            easing: 'easeIn'
          }
        }
      },
      
      // Modal Transitions
      modal: {
        overlay: {
          entering: {
            from: { opacity: 0 },
            to: { opacity: 1 },
            duration: 200
          }
        },
        content: {
          entering: {
            from: { opacity: 0, scale: 0.95 },
            to: { opacity: 1, scale: 1 },
            duration: 200,
            easing: 'easeOut'
          }
        }
      }
    };

### 8\. Error States & Edge Cases

#### 8.1 Error State Specifications

javascript

    const errorStates = {
      // Network Errors
      network: {
        offline: {
          title: 'No Internet Connection',
          message: 'Please check your connection and try again',
          icon: 'WifiOff',
          actions: ['Retry', 'Work Offline'],
          fallback: 'Show cached data if available'
        },
        
        timeout: {
          title: 'Request Timed Out',
          message: 'The server is taking too long to respond',
          icon: 'Clock',
          actions: ['Retry', 'Contact Support'],
          autoRetry: '3 attempts with exponential backoff'
        },
        
        serverError: {
          title: 'Something Went Wrong',
          message: 'We\'re having trouble with our servers',
          icon: 'ServerCrash',
          actions: ['Retry', 'Report Issue'],
          logging: 'Send to Sentry with context'
        }
      },
      
      // Validation Errors
      validation: {
        inline: {
          display: 'Below input field',
          animation: 'Slide down with shake',
          timing: 'On blur or submit',
          example: {
            plotArea: {
              required: 'Please enter plot area',
              min: 'Plot area must be at least 500 sq ft',
              max: 'Plot area cannot exceed 50,000 sq ft',
              invalid: 'Please enter a valid number'
            }
          }
        },
        
        summary: {
          display: 'Top of form',
          style: 'Alert box with error list',
          scrollBehavior: 'Smooth scroll to first error',
          screenReader: 'Focus moved to error summary'
        }
      },
      
      // Empty States
      emptyStates: {
        projects: {
          icon: 'Folder',
          title: 'No Projects Yet',
          message: 'Start calculating your dream home to see it here',
          action: 'Start New Project',
          illustration: 'empty-projects.svg'
        },
        
        searchResults: {
          icon: 'Search',
          title: 'No Results Found',
          message: 'Try adjusting your filters or search terms',
          suggestions: ['Remove filters', 'Check spelling', 'Browse categories'],
          illustration: 'no-results.svg'
        }
      }
    };

#### 8.2 Edge Case Handling

javascript

    const edgeCases = {
      // Data Limits
      dataLimits: {
        plotArea: {
          min: 100,
          max: 100000,
          handler: 'Show warning for unusual values'
        },
        
        floors: {
          max: 10,
          handler: 'Disable options beyond G+9'
        },
        
        rooms: {
          maxPerType: 20,
          totalMax: 50,
          handler: 'Show performance warning'
        }
      },
      
      // Calculation Edge Cases
      calculations: {
        zeroArea: {
          scenario: 'User enters 0 or negative area',
          handler: 'Prevent calculation, show validation'
        },
        
        extremeValues: {
          scenario: 'Calculation exceeds ₹100 Cr',
          handler: 'Show warning about estimate accuracy'
        },
        
        impossibleConfig: {
          scenario: '10 bedrooms on 500 sqft',
          handler: 'Show space constraint warning'
        }
      },
      
      // Browser Compatibility
      compatibility: {
        unsupportedBrowser: {
          detection: 'Check for required features',
          message: 'Please upgrade your browser',
          fallback: 'Basic HTML version'
        },
        
        mobileKeyboard: {
          issue: 'Viewport resize on input focus',
          solution: 'Fixed positioning adjustments'
        },
        
        slowDevice: {
          detection: 'Performance.now() benchmarks',
          adaptations: [
            'Reduce animations',
            'Simplify transitions',
            'Lazy load images'
          ]
        }
      }
    };

* * *

Part 3: Screen-by-Screen Specifications
---------------------------------------

### 9\. Landing & Onboarding Screens

#### 9.1 Homepage Design

javascript

    const homepageDesign = {
      // Hero Section
      hero: {
        layout: 'Full width with gradient overlay',
        height: {
          desktop: '600px',
          mobile: '500px'
        },
        background: 'High-quality image of modern Indian home',
        content: {
          headline: {
            text: 'Build Your Dream Home\nWith Complete Clarity',
            style: 'text-5xl font-bold text-white',
            animation: 'Fade in from bottom'
          },
          subheadline: {
            text: 'India\'s most transparent construction cost calculator',
            style: 'text-xl text-white/90 mt-4'
          },
          cta: {
            primary: {
              text: 'Calculate Now',
              style: 'btn-primary btn-xl',
              action: '/calculate'
            },
            secondary: {
              text: 'Watch Demo',
              style: 'btn-outline-white btn-lg',
              action: 'openVideoModal'
            }
          }
        },
        trustIndicators: {
          position: 'Bottom of hero',
          items: [
            '15,000+ Homes Calculated',
            '95% Accuracy',
            'Updated Daily'
          ]
        }
      },
      
      // Features Section
      features: {
        layout: 'Three column grid',
        items: [
          {
            icon: 'Calculator',
            title: 'Accurate Estimates',
            description: 'Get detailed costs based on your exact requirements'
          },
          {
            icon: 'Eye',
            title: 'Complete Transparency',
            description: 'See breakdown of every rupee in your construction'
          },
          {
            icon: 'Sparkles',
            title: 'Smart Recommendations',
            description: 'AI-powered suggestions to optimize your budget'
          }
        ]
      },
      
      // How It Works
      howItWorks: {
        layout: 'Alternating left-right with illustrations',
        steps: [
          {
            number: '01',
            title: 'Define Your Plot',
            description: 'Enter location and dimensions',
            illustration: 'plot-entry.svg'
          },
          {
            number: '02',
            title: 'Configure Layout',
            description: 'Choose rooms and structure type',
            illustration: 'layout-config.svg'
          },
          {
            number: '03',
            title: 'Customize Materials',
            description: 'Select finishes and fittings',
            illustration: 'material-selection.svg'
          },
          {
            number: '04',
            title: 'Get Your Report',
            description: 'Download detailed cost breakdown',
            illustration: 'report-generation.svg'
          }
        ]
      }
    };

#### 9.2 Onboarding Flow Screens

javascript

    const onboardingScreens = {
      // Screen 1: Location Selection
      locationSelection: {
        layout: {
          desktop: 'Centered card, 600px width',
          mobile: 'Full screen with padding'
        },
        
        elements: {
          header: {
            title: 'Where are you building?',
            subtitle: 'Construction costs vary by location',
            style: 'text-center mb-8'
          },
          
          citySelector: {
            type: 'Searchable dropdown',
            placeholder: 'Select your city',
            options: [
              { value: 'delhi', label: 'Delhi', popular: true },
              { value: 'gurgaon', label: 'Gurgaon', popular: true },
              { value: 'noida', label: 'Noida', popular: true },
              { value: 'bangalore', label: 'Bangalore' },
              // ... more cities
            ],
            grouping: 'Popular cities first, then alphabetical'
          },
          
          areaSelector: {
            type: 'Secondary dropdown',
            label: 'Area/Locality (Optional)',
            dynamic: 'Loads based on city selection',
            helps: 'More accurate local rates'
          },
          
          navigation: {
            back: 'Hidden on first screen',
            next: {
              text: 'Continue',
              enabled: 'When city selected',
              loading: 'Show spinner while loading area data'
            }
          }
        }
      },
      
      // Screen 2: Plot Dimensions
      plotDimensions: {
        layout: 'Two-column on desktop, stacked on mobile',
        
        leftColumn: {
          title: 'Enter your plot size',
          
          dimensionInputs: {
            length: {
              label: 'Plot Length',
              placeholder: 'Enter in feet',
              icon: 'Ruler',
              validation: 'Positive number, max 1000'
            },
            width: {
              label: 'Plot Width',
              placeholder: 'Enter in feet',
              icon: 'Ruler',
              validation: 'Positive number, max 1000'
            }
          },
          
          calculatedArea: {
            display: 'Auto-calculated',
            formula: 'length × width',
            format: 'X,XXX sq ft',
            style: 'Large text with icon'
          },
          
          quickOptions: {
            title: 'Common plot sizes',
            options: [
              { size: '30×40', area: 1200 },
              { size: '40×60', area: 2400 },
              { size: '50×80', area: 4000 }
            ],
            action: 'Click to auto-fill'
          }
        },
        
        rightColumn: {
          visualization: {
            type: '2D plot representation',
            updates: 'Real-time as dimensions change',
            shows: ['Dimensions', 'Area', 'Shape proportion']
          },
          
          irregularPlot: {
            toggle: 'Is your plot irregular?',
            action: 'Opens advanced shape editor',
            help: 'For L-shaped, triangular plots'
          }
        }
      },
      
      // Screen 3: Buildable Area Visualization
      buildableArea: {
        layout: 'Full width with interactive visualization',
        
        visualization: {
          canvas: {
            type: '2D interactive plot',
            shows: [
              'Total plot (light)',
              'Setback areas (red overlay)',
              'Buildable area (green)'
            ],
            interactive: 'Hover for dimensions'
          },
          
          stats: {
            cards: [
              {
                label: 'Total Plot Area',
                value: 'XXXX sq ft',
                icon: 'Square'
              },
              {
                label: 'Buildable Area',
                value: 'XXXX sq ft',
                icon: 'CheckSquare',
                highlight: true
              },
              {
                label: 'Coverage',
                value: 'XX%',
                icon: 'Percent'
              }
            ]
          }
        },
        
        controls: {
          setbackToggle: {
            label: 'Use standard setbacks',
            default: true,
            help: 'Based on [City] municipal rules'
          },
          
          customSetbacks: {
            enabled: 'When toggle off',
            inputs: ['Front', 'Rear', 'Left', 'Right'],
            unit: 'feet',
            validation: 'Updates visualization'
          },
          
          groundCoverage: {
            type: 'Slider',
            label: 'Ground coverage %',
            range: [40, 80],
            default: 'City-specific',
            shows: 'Live area update'
          }
        }
      }
    };

### 10\. Calculator Journey Screens

#### 10.1 Structure Configuration

javascript

    const structureConfiguration = {
      // Screen Layout
      layout: {
        desktop: 'Two-column split view',
        mobile: 'Tabbed interface'
      },
      
      // Left Panel: Configuration
      configuration: {
        floors: {
          component: 'Visual floor selector',
          options: [
            {
              value: 'G',
              label: 'Ground Only',
              visual: 'single-floor.svg',
              description: 'Single story home'
            },
            {
              value: 'G+1',
              label: 'Ground + 1',
              visual: 'two-floor.svg',
              description: 'Two story home',
              popular: true
            },
            {
              value: 'G+2',
              label: 'Ground + 2',
              visual: 'three-floor.svg',
              description: 'Three story home'
            },
            {
              value: 'G+3',
              label: 'Ground + 3',
              visual: 'four-floor.svg',
              description: 'Four story home'
            }
          ]
        },
        
        belowGround: {
          title: 'Below ground features',
          options: [
            {
              id: 'none',
              label: 'None',
              description: 'No basement or parking',
              impact: '₹0'
            },
            {
              id: 'stilt',
              label: 'Stilt Parking',
              description: 'Open parking at ground',
              impact: '+₹3-4L',
              recommended: true
            },
            {
              id: 'basement',
              label: 'Basement',
              description: 'Full basement level',
              impact: '+₹8-12L',
              warning: 'Check water table'
            }
          ]
        },
        
        structureType: {
          title: 'Construction method',
          options: [
            {
              id: 'rcc_frame',
              label: 'RCC Frame Structure',
              description: 'Modern concrete frame',
              benefits: [
                'Earthquake resistant',
                'Open floor plans',
                'Can go higher'
              ],
              recommended: 'floors > 1'
            },
            {
              id: 'load_bearing',
              label: 'Load Bearing',
              description: 'Traditional brick walls',
              benefits: [
                'Cost effective',
                'Good for G+1',
                'Faster construction'
              ],
              limitations: ['Fixed layouts', 'Max G+1']
            }
          ]
        }
      },
      
      // Right Panel: Live Preview
      livePreview: {
        visualization: {
          type: '3D isometric view',
          updates: 'Real-time with selections',
          shows: [
            'Building height',
            'Floor count',
            'Basement indication',
            'Structure type visual'
          ]
        },
        
        impactSummary: {
          cards: [
            {
              label: 'Total Built-up Area',
              value: 'Updates live',
              formula: 'Buildable × floors'
            },
            {
              label: 'Structure Cost Impact',
              value: '₹XX - XX L',
              breakdown: 'Click for details'
            },
            {
              label: 'Timeline Impact',
              value: 'XX - XX months',
              factors: 'Based on complexity'
            }
          ]
        }
      }
    };

#### 10.2 Layout Configuration

javascript

    const layoutConfiguration = {
      // Family Type Selection
      familyType: {
        question: 'Who will live here?',
        subtext: 'This helps us recommend the right layout',
        
        options: [
          {
            id: 'nuclear',
            icon: 'Users',
            label: 'Nuclear Family',
            description: '2-6 members',
            typical: '2-3 bedrooms'
          },
          {
            id: 'joint',
            icon: 'UsersMany',
            label: 'Joint Family',
            description: '7+ members',
            typical: '4-5 bedrooms'
          },
          {
            id: 'mixed',
            icon: 'Building',
            label: 'Mixed Use',
            description: 'Family + Rental',
            typical: 'Separate entrances'
          },
          {
            id: 'investment',
            icon: 'TrendingUp',
            label: 'Investment',
            description: 'All rental units',
            typical: 'Maximum units'
          }
        ]
      },
      
      // Room Configuration
      roomConfiguration: {
        recommendation: {
          display: 'Card with breakdown',
          content: 'Based on [family type], we recommend:',
          editable: true
        },
        
        roomPalette: {
          categories: [
            {
              name: 'Essential Rooms',
              rooms: [
                {
                  type: 'bedroom',
                  label: 'Bedrooms',
                  icon: 'Bed',
                  default: 'Based on family',
                  min: 1,
                  max: 10,
                  stepper: true,
                  areaImpact: '120-180 sqft each'
                },
                {
                  type: 'bathroom',
                  label: 'Bathrooms',
                  icon: 'Bath',
                  default: 'Bedrooms + 1',
                  types: ['Attached', 'Common'],
                  areaImpact: '40-60 sqft each'
                },
                {
                  type: 'kitchen',
                  label: 'Kitchen',
                  icon: 'Utensils',
                  default: 1,
                  max: 2,
                  types: ['Open', 'Closed'],
                  areaImpact: '80-150 sqft'
                },
                {
                  type: 'living',
                  label: 'Living/Dining',
                  icon: 'Sofa',
                  default: 1,
                  combined: true,
                  areaImpact: '200-400 sqft'
                }
              ]
            },
            {
              name: 'Additional Spaces',
              rooms: [
                {
                  type: 'pooja',
                  label: 'Pooja Room',
                  icon: 'Shrine',
                  optional: true,
                  popular: 'Joint family',
                  areaImpact: '40-60 sqft'
                },
                {
                  type: 'study',
                  label: 'Study/Office',
                  icon: 'Laptop',
                  optional: true,
                  trending: true,
                  areaImpact: '80-120 sqft'
                },
                {
                  type: 'servant',
                  label: 'Servant Quarter',
                  icon: 'UserHome',
                  optional: true,
                  includes: 'Bathroom',
                  areaImpact: '100-120 sqft'
                },
                {
                  type: 'store',
                  label: 'Store Rooms',
                  icon: 'Package',
                  optional: true,
                  multiple: true,
                  areaImpact: '40-80 sqft each'
                }
              ]
            }
          ]
        },
        
        spaceValidation: {
          display: 'Progress bar with color coding',
          levels: {
            optimal: { range: '70-85%', color: 'green', message: 'Great layout!' },
            tight: { range: '85-95%', color: 'yellow', message: 'Getting tight' },
            overflow: { range: '>95%', color: 'red', message: 'Too many rooms' }
          },
          smartSuggestions: [
            'Consider combining dining with living',
            'Reduce room sizes slightly',
            'Remove one bedroom'
          ]
        }
      }
    };

#### 10.3 Quality Tier Selection

javascript

    const qualityTierSelection = {
      layout: 'Three cards with hover effects',
      
      tiers: [
        {
          id: 'smart',
          name: 'Smart Choice',
          icon: 'PiggyBank',
          priceRange: '₹1,600-2,000/sqft',
          
          card: {
            gradient: 'from-green-400 to-green-600',
            popularBadge: false,
            
            highlights: [
              'Best value for money',
              'Reliable brands',
              'Low maintenance',
              '5-7 year durability'
            ],
            
            brands: {
              display: 'Logo grid',
              items: ['Kajaria', 'Somany', 'Asian Paints', 'Havells']
            },
            
            useCases: [
              'First home',
              'Rental property',
              'Budget conscious'
            ]
          },
          
          details: {
            flooring: 'Vitrified tiles (₹45-70/sqft)',
            bathroom: 'Cera/Parryware fittings',
            kitchen: 'Laminate finish, granite top',
            electrical: 'Anchor switches',
            paint: 'Premium emulsion'
          }
        },
        
        {
          id: 'premium',
          name: 'Premium Selection',
          icon: 'Star',
          priceRange: '₹2,200-2,800/sqft',
          
          card: {
            gradient: 'from-purple-400 to-purple-600',
            popularBadge: true,
            popularText: 'Most Popular',
            
            highlights: [
              'Perfect balance',
              'Premium brands',
              'Great aesthetics',
              '10+ year durability'
            ],
            
            brands: {
              display: 'Logo grid',
              items: ['Kajaria Eternity', 'Kohler', 'Dulux', 'Legrand']
            },
            
            useCases: [
              'Family home',
              'Long-term living',
              'Quality focused'
            ]
          }
        },
        
        {
          id: 'luxury',
          name: 'Luxury Collection',
          icon: 'Crown',
          priceRange: '₹3,000-4,000/sqft',
          
          card: {
            gradient: 'from-amber-400 to-amber-600',
            exclusiveBadge: true,
            
            highlights: [
              'Statement finishes',
              'Imported materials',
              'Unique designs',
              '15+ year durability'
            ],
            
            brands: {
              display: 'Logo grid',
              items: ['Italian Marble', 'Grohe', 'Bosch', 'Hafele']
            },
            
            useCases: [
              'Luxury living',
              'Status symbol',
              'Best in class'
            ]
          }
        }
      ],
      
      comparison: {
        trigger: 'Compare tiers',
        modal: {
          title: 'Detailed Comparison',
          categories: [
            'Flooring',
            'Wall Finishes',
            'Bathroom Fittings',
            'Kitchen',
            'Electrical',
            'Doors & Windows'
          ],
          display: 'Tabular with images'
        }
      }
    };

### 11\. Digital Showroom Interface

#### 11.1 Room Selection Screen

javascript

    const roomSelectionScreen = {
      layout: {
        header: {
          title: 'Customize Your Spaces',
          progress: 'Room 1 of 8',
          skipOption: {
            text: 'Use defaults for all',
            action: 'Skip to summary'
          }
        },
        
        roomGrid: {
          display: 'Card grid',
          columns: {
            desktop: 4,
            tablet: 3,
            mobile: 2
          },
          
          roomCards: [
            {
              id: 'master_bedroom',
              label: 'Master Bedroom',
              icon: 'BedDouble',
              status: 'customized', // customized | default | current
              thumbnail: 'Current selection preview',
              customizations: 5,
              estimatedCost: '₹2.4L'
            },
            // ... more rooms
          ],
          
          cardStates: {
            current: 'Blue border, elevated',
            customized: 'Green checkmark badge',
            default: 'Grayed out slightly'
          }
        }
      },
      
      navigation: {
        bottom: {
          back: 'Previous room',
          next: 'Next room',
          jumpTo: 'Dropdown to select any room'
        }
      }
    };

#### 11.2 Material Customization Interface

javascript

    const materialCustomization = {
      layout: 'Split view with visual feedback',
      
      leftPanel: {
        roomVisualization: {
          type: '2D rendered image',
          quality: 'High resolution',
          interactive: {
            hotspots: [
              {
                id: 'floor',
                position: { x: '50%', y: '80%' },
                icon: 'Circle pulsing',
                label: 'Flooring'
              },
              {
                id: 'walls',
                position: { x: '20%', y: '50%' },
                icon: 'Circle pulsing',
                label: 'Wall Finish'
              },
              // ... more hotspots
            ],
            behavior: 'Click to select category'
          },
          
          viewControls: {
            zoom: 'Pinch or buttons',
            pan: 'Drag to move',
            reset: 'Reset view button'
          }
        },
        
        quickStats: {
          position: 'Bottom overlay',
          stats: [
            { label: 'Room Cost', value: 'Updates live' },
            { label: 'vs Budget', value: '+₹X or -₹X', color: 'dynamic' }
          ]
        }
      },
      
      rightPanel: {
        header: {
          category: 'Current category (e.g., Flooring)',
          breadcrumb: 'Master Bedroom > Flooring',
          searchBar: {
            placeholder: 'Search marble, tiles, wood...',
            filters: 'Icon button for filter panel'
          }
        },
        
        materialGrid: {
          layout: 'Grid with filters',
          
          filters: {
            position: 'Collapsible sidebar',
            sections: [
              {
                name: 'Type',
                options: ['Tiles', 'Marble', 'Wood', 'Granite'],
                ui: 'Checkboxes'
              },
              {
                name: 'Price Range',
                ui: 'Dual slider',
                range: [0, 1000],
                step: 50
              },
              {
                name: 'Brand',
                options: 'Dynamic based on type',
                ui: 'Searchable checkboxes'
              },
              {
                name: 'Features',
                options: ['Anti-skid', 'Easy clean', 'Eco-friendly'],
                ui: 'Tags'
              }
            ],
            
            activeFilters: {
              display: 'Pills below search',
              clearAll: 'Link button'
            }
          },
          
          materialCards: {
            size: {
              desktop: '200x250px',
              mobile: '150x200px'
            },
            
            content: {
              image: {
                size: 'Cover',
                quality: 'Progressive loading',
                fallback: 'Skeleton'
              },
              
              badges: {
                position: 'Top right',
                types: ['Eco', 'Premium', 'Sale', 'New']
              },
              
              info: {
                name: 'Product name',
                brand: 'Brand name',
                price: {
                  current: '₹XXX/sqft',
                  original: 'Strikethrough if on sale',
                  impact: '+₹XX,XXX for this room'
                },
                
                rating: {
                  stars: 'X.X/5',
                  count: '(XXX)',
                  source: 'From MyGate/Amazon'
                }
              },
              
              actions: {
                primary: 'Select (Radio behavior)',
                secondary: 'Quick view (Eye icon)',
                tertiary: 'Compare (+ icon)'
              }
            },
            
            states: {
              selected: 'Blue border + checkmark',
              hover: 'Elevate + show actions',
              loading: 'Skeleton loader'
            }
          }
        },
        
        compareDrawer: {
          trigger: 'When 2+ items selected',
          position: 'Bottom drawer',
          height: '40% of screen',
          
          content: {
            items: 'Selected materials side by side',
            attributes: [
              'Price',
              'Durability',
              'Maintenance',
              'Warranty',
              'Availability'
            ],
            
            actions: [
              'Remove from comparison',
              'Select winner',
              'View details'
            ]
          }
        }
      }
    };

### 12\. Dashboard & Reports

#### 12.1 Project Dashboard

javascript

    const projectDashboard = {
      layout: {
        structure: 'Modular card-based',
        responsive: 'Stacks on mobile',
        customizable: false // Phase 1
      },
      
      modules: {
        // Summary Card (Hero)
        summary: {
          position: 'Top full width',
          design: 'Gradient background with pattern',
          
          content: {
            projectName: {
              text: 'My Dream Home',
              editable: true,
              icon: 'Edit on hover'
            },
            
            totalCost: {
              label: 'Total Investment',
              value: '₹1,86,45,000',
              size: 'text-4xl',
              animation: 'Count up on load'
            },
            
            keyMetrics: {
              layout: 'Horizontal pills',
              items: [
                {
                  icon: 'Calendar',
                  label: 'Timeline',
                  value: '18-20 months'
                },
                {
                  icon: 'Square',
                  label: 'Built-up Area',
                  value: '3,600 sqft'
                },
                {
                  icon: 'Layers',
                  label: 'Floors',
                  value: 'G+2'
                },
                {
                  icon: 'Star',
                  label: 'Quality',
                  value: 'Premium'
                }
             ]
           },
           
           actions: {
             primary: 'Download Report',
             secondary: ['Share', 'Print', 'Save Version']
           }
         }
       },
       
       // Cost Breakdown
       costBreakdown: {
         title: 'Where Your Money Goes',
         
         visualization: {
           type: 'Interactive donut chart',
           library: 'Recharts',
           
           data: [
             { category: 'Structure', value: 5970000, percentage: 32 },
             { category: 'Finishes', value: 7080000, percentage: 38 },
             { category: 'MEP', value: 2800000, percentage: 15 },
             { category: 'Professional', value: 1490000, percentage: 8 },
             { category: 'Statutory', value: 1310000, percentage: 7 }
           ],
           
           interactions: {
             hover: 'Show amount and percentage',
             click: 'Drill down to subcategories',
             animation: 'Segments animate on load'
           },
           
           colors: {
             structure: '#3B82F6',
             finishes: '#8B5CF6',
             mep: '#F59E0B',
             professional: '#10B981',
             statutory: '#EF4444'
           }
         },
         
         drillDown: {
           trigger: 'Click on segment',
           display: 'Replace chart with subcategory',
           
           example: {
             finishes: [
               { item: 'Flooring', value: 1800000 },
               { item: 'Paint', value: 900000 },
               { item: 'Doors & Windows', value: 1500000 },
               { item: 'Kitchen', value: 1200000 },
               { item: 'Bathrooms', value: 1680000 }
             ]
           },
           
           navigation: 'Breadcrumb to go back'
         }
       },
       
       // Timeline Visualization
       timeline: {
         title: 'Construction Timeline',
         
         visualization: {
           type: 'Gantt chart',
           
           phases: [
             {
               name: 'Foundation',
               start: 'Month 1',
               duration: 2,
               dependencies: [],
               critical: true
             },
             {
               name: 'Structure',
               start: 'Month 3',
               duration: 4,
               dependencies: ['Foundation'],
               critical: true
             },
             {
               name: 'Walls & Plastering',
               start: 'Month 6',
               duration: 3,
               dependencies: ['Structure']
             },
             {
               name: 'Flooring',
               start: 'Month 8',
               duration: 2,
               dependencies: ['Walls']
             },
             {
               name: 'Painting & Finishing',
               start: 'Month 10',
               duration: 3,
               dependencies: ['Flooring']
             },
             {
               name: 'MEP Installation',
               start: 'Month 7',
               duration: 5,
               parallel: true
             }
           ],
           
           features: {
             today: 'Vertical line for current date',
             critical: 'Red highlighting',
             dependencies: 'Arrow connections',
             hover: 'Show duration and cost'
           }
         },
         
         monthlyBreakdown: {
           toggle: 'View monthly fund requirement',
           chart: 'Bar chart of monthly expenses'
         }
       },
       
       // Detailed BOQ
       detailedBOQ: {
         title: 'Detailed Bill of Quantities',
         badge: 'For Professionals',
         
         display: {
           initial: 'Collapsed with summary',
           expanded: 'Full paginated table'
         },
         
         summary: {
           items: '247 line items',
           categories: '12 categories',
           lastUpdated: 'Timestamp'
         },
         
         table: {
           columns: [
             { key: 'category', label: 'Category', width: '15%' },
             { key: 'item', label: 'Item Description', width: '35%' },
             { key: 'quantity', label: 'Qty', width: '10%' },
             { key: 'unit', label: 'Unit', width: '10%' },
             { key: 'rate', label: 'Rate', width: '15%' },
             { key: 'amount', label: 'Amount', width: '15%' }
           ],
           
           features: {
             search: 'Filter by item name',
             sort: 'All columns sortable',
             export: 'Download as Excel/PDF',
             print: 'Print-friendly view'
           },
           
           grouping: 'By category with subtotals'
         }
       }
     }
    };

#### 12.2 Report Generation

javascript

    const reportGeneration = {
      // Report Options Modal
      modal: {
        title: 'Generate Project Report',
        
        sections: {
          format: {
            label: 'Report Format',
            options: [
              {
                id: 'detailed',
                name: 'Detailed Report',
                description: 'Complete BOQ with specifications',
                pages: '~25-30 pages',
                icon: 'FileText'
              },
              {
                id: 'summary',
                name: 'Summary Report',
                description: 'Key costs and timeline',
                pages: '~5-7 pages',
                icon: 'FileBarChart'
              },
              {
                id: 'bank',
                name: 'Bank Loan Format',
                description: 'Formatted for loan applications',
                pages: '~10-12 pages',
                icon: 'Building'
              }
            ]
          },
          
          customization: {
            label: 'Include Sections',
            options: [
              { id: 'summary', label: 'Executive Summary', default: true },
              { id: 'breakdown', label: 'Cost Breakdown', default: true },
              { id: 'timeline', label: 'Timeline', default: true },
              { id: 'specifications', label: 'Material Specs', default: true },
              { id: 'boq', label: 'Detailed BOQ', default: 'detailed' },
              { id: 'terms', label: 'Terms & Conditions', default: false }
            ]
          },
          
          branding: {
            label: 'Personalization',
            options: [
              {
                id: 'name',
                label: 'Your Name',
                type: 'text',
                placeholder: 'John Doe'
              },
              {
                id: 'phone',
                label: 'Contact Number',
                type: 'tel',
                placeholder: 'Optional'
              },
              {
                id: 'notes',
                label: 'Additional Notes',
                type: 'textarea',
                placeholder: 'Any special requirements...'
              }
            ]
          }
        },
        
        actions: {
          generate: {
            text: 'Generate Report',
            loading: 'Generating... (15-30 seconds)',
            success: 'Report Ready!'
          }
        }
      },
      
      // Generated Report View
      reportView: {
        layout: 'Full screen modal or new tab',
        
        viewer: {
          type: 'PDF viewer',
          features: [
            'Zoom controls',
            'Page navigation',
            'Search within PDF',
            'Print button',
            'Download button'
          ]
        },
        
        sharing: {
          directDownload: 'Download PDF',
          
          shareLink: {
            action: 'Get shareable link',
            features: [
              'Password protection option',
              'Expiry date setting',
              'View count tracking'
            ]
          },
          
          email: {
            action: 'Email report',
            fields: ['Recipient email', 'Message'],
            attachment: 'PDF attached'
          },
          
          whatsapp: {
            action: 'Share on WhatsApp',
            format: 'Link with preview'
          }
        }
      }
    };

### 13\. Admin Panel Interface

#### 13.1 Admin Dashboard

javascript

    const adminDashboard = {
      layout: {
        structure: 'Sidebar + Main Content',
        responsive: 'Hamburger menu on mobile'
      },
      
      sidebar: {
        header: {
          logo: 'Clarity Engine Admin',
          user: {
            avatar: 'User photo',
            name: 'Admin name',
            role: 'Super Admin'
          }
        },
        
        navigation: [
          {
            section: 'Overview',
            items: [
              { icon: 'LayoutDashboard', label: 'Dashboard', path: '/admin' }
            ]
          },
          {
            section: 'Catalog Management',
            items: [
              { icon: 'Package', label: 'Materials', path: '/admin/materials' },
              { icon: 'Tag', label: 'Brands', path: '/admin/brands' },
              { icon: 'Folder', label: 'Categories', path: '/admin/categories' }
            ]
          },
          {
            section: 'Pricing & Logic',
            items: [
              { icon: 'Calculator', label: 'Engineering Params', path: '/admin/engineering' },
              { icon: 'TrendingUp', label: 'Price Updates', path: '/admin/prices' },
              { icon: 'Cpu', label: 'Calculation Rules', path: '/admin/rules' }
            ]
          },
          {
            section: 'Operations',
            items: [
              { icon: 'Users', label: 'User Management', path: '/admin/users' },
              { icon: 'Building', label: 'Vendor Management', path: '/admin/vendors' },
              { icon: 'FileText', label: 'Reports', path: '/admin/reports' }
            ]
          }
        ]
      },
      
      mainDashboard: {
        header: {
          title: 'Dashboard Overview',
          dateRange: 'Date picker for stats',
          actions: ['Refresh', 'Export']
        },
        
        kpiCards: [
          {
            title: 'Total Users',
            value: '15,234',
            change: '+12.5%',
            trend: 'Sparkline',
            period: 'vs last month'
          },
          {
            title: 'Projects Created',
            value: '3,456',
            change: '+23.1%',
            trend: 'Sparkline'
          },
          {
            title: 'Materials in DB',
            value: '4,567',
            subtext: '234 need price update',
            action: 'View stale prices'
          },
          {
            title: 'Revenue',
            value: '₹12.5L',
            change: '+45.2%',
            breakdown: 'Click for sources'
          }
        ],
        
        charts: {
          userActivity: {
            title: 'User Activity Trend',
            type: 'Area chart',
            data: 'Daily active users over time'
          },
          
          popularMaterials: {
            title: 'Most Selected Materials',
            type: 'Horizontal bar chart',
            data: 'Top 10 materials by selection count'
          },
          
          revenueBreakdown: {
            title: 'Revenue Sources',
            type: 'Donut chart',
            data: 'Leads, Commissions, Subscriptions'
          }
        },
        
        activityFeed: {
          title: 'Recent Activity',
          items: [
            {
              icon: 'Plus',
              text: 'New material added: Italian Marble Carrara',
              user: 'Admin Name',
              time: '2 mins ago'
            },
            {
              icon: 'Edit',
              text: 'Price updated for 45 items',
              user: 'Price Manager',
              time: '1 hour ago'
            }
          ]
        }
      }
    };

#### 13.2 Material Management Interface

javascript

    const materialManagement = {
      listView: {
        header: {
          title: 'Material Management',
          stats: '4,567 total materials',
          
          actions: {
            primary: {
              text: 'Add Material',
              icon: 'Plus',
              action: 'Open creation wizard'
            },
            secondary: [
              {
                text: 'Import CSV',
                icon: 'Upload',
                action: 'Bulk import modal'
              },
              {
                text: 'Export',
                icon: 'Download',
                options: ['All', 'Filtered', 'Selected']
              }
            ]
          }
        },
        
        filters: {
          layout: 'Horizontal bar',
          
          quickFilters: [
            {
              type: 'search',
              placeholder: 'Search materials...',
              fields: ['Name', 'SKU', 'Brand']
            },
            {
              type: 'dropdown',
              label: 'Category',
              options: 'Hierarchical categories'
            },
            {
              type: 'dropdown',
              label: 'Brand',
              options: 'All brands alphabetical'
            },
            {
              type: 'toggle',
              label: 'Active only',
              default: true
            }
          ],
          
          advancedFilters: {
            trigger: 'More filters',
            options: [
              'Price range',
              'Quality tier',
              'Last updated',
              'Stock status'
            ]
          }
        },
        
        dataTable: {
          columns: [
            {
              key: 'select',
              type: 'checkbox',
              width: '40px',
              fixed: true
            },
            {
              key: 'image',
              label: 'Image',
              type: 'thumbnail',
              width: '60px'
            },
            {
              key: 'name',
              label: 'Material Name',
              sortable: true,
              searchable: true,
              render: 'With brand subtitle'
            },
            {
              key: 'sku',
              label: 'SKU',
              sortable: true,
              copyable: true
            },
            {
              key: 'category',
              label: 'Category',
              sortable: true,
              render: 'Breadcrumb'
            },
            {
              key: 'price',
              label: 'Price',
              sortable: true,
              editable: 'inline',
              render: 'With unit'
            },
            {
              key: 'status',
              label: 'Status',
              render: 'Badge',
              filters: ['Active', 'Inactive', 'Out of stock']
            },
            {
              key: 'updated',
              label: 'Last Updated',
              sortable: true,
              render: 'Relative time'
            },
            {
              key: 'actions',
              label: 'Actions',
              width: '100px',
              render: 'Dropdown menu',
              options: ['Edit', 'Duplicate', 'Delete']
            }
          ],
          
          features: {
            pagination: {
              options: [25, 50, 100],
              default: 50
            },
            
            bulkActions: {
              trigger: 'When items selected',
              actions: [
                'Update prices',
                'Change category',
                'Set status',
                'Export selected',
                'Delete'
              ]
            },
            
            inlineEdit: {
              fields: ['Price', 'Status'],
              save: 'On blur or Enter',
              validation: 'Show inline'
            }
          }
        }
      },
      
      // Material Creation/Edit Form
      materialForm: {
        layout: 'Multi-step wizard',
        
        steps: [
          {
            title: 'Basic Information',
            fields: [
              {
                name: 'name',
                label: 'Material Name',
                type: 'text',
                required: true,
                placeholder: 'e.g., Carrara White Marble'
              },
              {
                name: 'brand',
                label: 'Brand',
                type: 'searchable-select',
                required: true,
                options: 'Brands from database'
              },
              {
                name: 'category',
                label: 'Category',
                type: 'hierarchical-select',
                required: true
              },
              {
                name: 'sku',
                label: 'SKU',
                type: 'text',
                validation: 'Unique check'
              }
            ]
          },
          
          {
            title: 'Specifications',
            dynamic: 'Based on category',
            
            examples: {
              tiles: [
                { name: 'size', label: 'Size', options: ['2x2', '2x4'] },
                { name: 'finish', label: 'Finish', options: ['Glossy', 'Matte'] },
                { name: 'thickness', label: 'Thickness', unit: 'mm' }
              ]
            }
          },
          
          {
            title: 'Pricing',
            fields: [
              {
                name: 'price',
                label: 'Base Price',
                type: 'number',
                required: true
              },
              {
                name: 'unit',
                label: 'Unit',
                type: 'select',
                options: ['sqft', 'piece', 'bag', 'kg']
              },
              {
                name: 'gst_inclusive',
                label: 'Price includes GST?',
                type: 'toggle'
              },
              {
                name: 'gst_rate',
                label: 'GST Rate',
                type: 'select',
                options: ['5%', '12%', '18%', '28%']
              }
            ]
          },
          
          {
            title: 'Media & Content',
            fields: [
              {
                name: 'images',
                label: 'Product Images',
                type: 'image-upload',
                multiple: true,
                maxSize: '5MB'
              },
              {
                name: 'description',
                label: 'Description',
                type: 'rich-text'
              }
            ]
          }
        ]
      }
    };

* * *

Part 4: Interaction Patterns
----------------------------

### 14\. Micro-interactions & Animations

#### 14.1 Core Animation Principles

javascript

    const animationPrinciples = {
      timing: {
        instant: 100,    // Hover states
        fast: 200,       // Micro-interactions
        normal: 300,     // Standard transitions
        slow: 500,       // Page transitions
        deliberate: 800  // Complex animations
      },
      
      easing: {
        // CSS: cubic-bezier values
        easeOut: 'cubic-bezier(0.0, 0, 0.2, 1)',      // Most common
        easeIn: 'cubic-bezier(0.4, 0, 1, 1)',         // Exit animations
        easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',    // Long transitions
        spring: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)' // Playful bounce
      },
      
      principles: [
        'Purposeful: Every animation has meaning',
        'Consistent: Same actions have same animations',
        'Performant: Use transform and opacity only',
        'Accessible: Respect prefers-reduced-motion'
      ]
    };

#### 14.2 Specific Micro-interactions

javascript

    const microInteractions = {
      // Button Interactions
      buttons: {
        hover: {
          transform: 'translateY(-2px)',
          boxShadow: 'increase shadow',
          duration: 200
        },
        
        click: {
          transform: 'scale(0.98)',
          duration: 100
        },
        
        loading: {
          spinner: 'Replace text with spinner',
          width: 'Maintain button width',
          disabled: 'Reduce opacity to 0.7'
        },
        
        success: {
          icon: 'Checkmark slides in',
          color: 'Transition to green',
          duration: 300
        }
      },
      
      // Input Interactions
      inputs: {
        focus: {
          border: 'Color transition',
          label: 'Float up and shrink',
          helper: 'Fade in helper text',
          duration: 200
        },
        
        validation: {
          error: {
            shake: 'Horizontal shake 3 times',
            border: 'Transition to red',
            message: 'Slide down fade in'
          },
          
          success: {
            icon: 'Checkmark fade in',
            border: 'Transition to green'
          }
        },
        
        characterCount: {
          display: 'Fade in on focus',
          warning: 'Turn orange at 80%',
          error: 'Turn red at 100%'
        }
      },
      
      // Card Interactions
      cards: {
        hover: {
          elevation: 'Increase shadow',
          transform: 'translateY(-4px)',
          content: 'Reveal hidden actions',
          duration: 200
        },
        
        selection: {
          border: 'Animate to primary color',
          check: 'Scale in checkmark',
          background: 'Subtle color change'
        },
        
        expansion: {
          height: 'Smooth height transition',
          content: 'Fade in after expand',
          icon: 'Rotate chevron 180°'
        }
      },
      
      // Loading States
      loading: {
        skeleton: {
          animation: 'Shimmer effect',
          gradient: 'Moving gradient shine',
          duration: 1500,
          easing: 'ease-in-out'
        },
        
        progressBar: {
          fill: 'Smooth width transition',
          color: 'Green when complete',
          text: 'Percentage counter'
        },
        
        spinner: {
          types: {
            dots: 'Three dots pulsing',
            circle: 'Rotating circle with gap',
            bars: 'Sound wave style bars'
          }
        }
      },
      
      // Notifications
      notifications: {
        entrance: {
          desktop: 'Slide in from right',
          mobile: 'Slide down from top',
          duration: 300
        },
        
        exit: {
          manual: 'Slide out same direction',
          auto: 'Fade out after 5s',
          swipe: 'Follow swipe gesture'
        },
        
        types: {
          success: { icon: 'CheckCircle', color: 'green' },
          error: { icon: 'XCircle', color: 'red' },
          warning: { icon: 'AlertTriangle', color: 'yellow' },
          info: { icon: 'Info', color: 'blue' }
        }
      }
    };

#### 14.3 Calculation-Specific Animations

javascript

    const calculationAnimations = {
      // Price Update Animation
      priceUpdate: {
        trigger: 'Material selection change',
        
        sequence: [
          {
            target: 'Old price',
            action: 'Fade out',
            duration: 150
          },
          {
            target: 'Price difference',
            action: 'Slide in from right',
            style: 'Green for decrease, red for increase',
            duration: 200
          },
          {
            target: 'New price',
            action: 'Count up animation',
            duration: 500
          },
          {
            target: 'Total estimate',
            action: 'Smooth transition',
            highlight: 'Pulse once',
            duration: 300
          }
        ]
      },
      
      // Progress Indicators
      progressIndicators: {
        stepProgress: {
          type: 'Horizontal stepper',
          
          animations: {
            complete: 'Check mark scales in',
            current: 'Pulsing indicator',
            upcoming: 'Grayed out',
            
            transition: {
              line: 'Fill from left to right',
              duration: 500
            }
          }
        },
        
        circularProgress: {
          type: 'Room completion indicator',
          
          animations: {
            fill: 'Smooth circular path',
            text: 'Count up percentage',
            complete: 'Burst animation'
          }
        }
      },
      
      // Room Visualization
      roomVisualization: {
        materialSwap: {
          effect: 'Cross-fade',
          duration: 400,
          loading: 'Blur during load'
        },
        
        hotspotPulse: {
          effect: 'Ripple effect',
          interval: 3000,
          color: 'Primary color'
        },
        
        selection: {
          effect: 'Highlight border',
          color: 'Primary color',
          duration: 200
        }
      }
    };

### 15\. Gesture Controls (Mobile)

#### 15.1 Touch Gestures

javascript

    const touchGestures = {
      // Swipe Gestures
      swipe: {
        horizontal: {
          threshold: 50, // pixels
          
          uses: [
            {
              context: 'Calculator steps',
              left: 'Next step',
              right: 'Previous step',
              feedback: 'Page slides'
            },
            {
              context: 'Image gallery',
              left: 'Next image',
              right: 'Previous image',
              feedback: 'Image slides'
            },
            {
              context: 'Dismissible cards',
              left: 'Dismiss',
              right: 'No action',
              feedback: 'Card flies off'
            }
          ]
        },
        
        vertical: {
          uses: [
            {
              context: 'Pull to refresh',
              down: 'Refresh data',
              threshold: 100,
              feedback: 'Spinner appears'
            },
            {
              context: 'Bottom sheets',
              up: 'Expand',
              down: 'Collapse',
              states: ['Collapsed', 'Half', 'Full']
            }
          ]
        }
      },
      
      // Pinch Gestures
      pinch: {
        uses: [
          {
            context: 'Floor plans',
            action: 'Zoom in/out',
            limits: [0.5, 3],
            feedback: 'Smooth scale'
          },
          {
            context: 'Material images',
            action: 'Zoom for details',
            doubleTap: 'Quick zoom'
          }
        ]
      },
      
      // Long Press
      longPress: {
        duration: 500,
        
        uses: [
          {
            context: 'Material cards',
            action: 'Quick actions menu',
            feedback: 'Haptic + menu'
          },
          {
            context: 'Price values',
            action: 'Show breakdown',
            feedback: 'Tooltip appears'
          }
        ]
      },
      
      // Drag and Drop
      dragDrop: {
        uses: [
          {
            context: 'Room priority',
            action: 'Reorder rooms',
            feedback: 'Cards lift and reorder'
          },
          {
            context: 'Comparison',
            action: 'Drag to compare',
            feedback: 'Drop zone highlights'
          }
        ]
      }
    };

#### 15.2 Mobile-Specific Interactions

javascript

    const mobileInteractions = {
      // Bottom Sheets
      bottomSheets: {
        trigger: 'Tap or swipe up',
        
        behavior: {
          states: {
            hidden: 'Off screen',
            peek: '20% visible with handle',
            half: '50% of screen',
            full: '90% of screen'
          },
          
          interactions: {
            handle: 'Drag to resize',
            background: 'Tap to dismiss at half',
            swipeDown: 'Dismiss if at peek'
          },
          
          content: {
            scrollable: 'When content exceeds',
            header: 'Sticky with close button'
          }
        }
      },
      
      // Floating Action Button
      fab: {
        position: 'Bottom right, 16px margin',
        
        behavior: {
          scroll: 'Hide on scroll down, show on up',
          tap: 'Primary action',
          longPress: 'Secondary actions'
        },
        
        expandable: {
          trigger: 'Tap to expand',
          options: 'Smaller FABs in arc',
          labels: 'Appear on expand'
        }
      },
      
      // Tab Navigation
      tabs: {
        position: 'Below header or bottom',
        
        interactions: {
          tap: 'Switch tabs',
          swipe: 'Navigate between tabs',
          indicator: 'Slides to active'
        },
        
        scrollable: {
          when: 'More than 4 tabs',
          behavior: 'Center active tab'
        }
      }
    };

### 16\. Responsive Behavior

#### 16.1 Breakpoint Behaviors

javascript

    const responsiveBehaviors = {
      // Layout Adaptations
      layoutAdaptations: {
        navigation: {
          desktop: 'Horizontal top bar',
          tablet: 'Horizontal with hamburger',
          mobile: 'Bottom tab bar'
        },
        
        grid: {
          desktop: 'Multi-column',
          tablet: 'Reduce columns',
          mobile: 'Single column stack'
        },
        
        sidebars: {
          desktop: 'Persistent sidebar',
          tablet: 'Collapsible sidebar',
          mobile: 'Full screen overlay'
        }
      },
      
      // Component Adaptations
      componentAdaptations: {
        cards: {
          desktop: {
            layout: 'Horizontal with image left',
            interactions: 'Hover effects'
          },
          mobile: {
            layout: 'Vertical stack',
            interactions: 'Tap only'
          }
        },
        
        modals: {
          desktop: {
            size: 'Centered, max 600px',
            dismissal: 'Click outside'
          },
          mobile: {
            size: 'Full screen',
            dismissal: 'X button only'
          }
        },
        
        tables: {
          desktop: 'Full table with all columns',
          tablet: 'Hide secondary columns',
          mobile: 'Card-based list view'
        }
      },
      
      // Content Prioritization
      contentPrioritization: {
        mobile: {
          hide: [
            'Decorative images',
            'Secondary navigation',
            'Extensive descriptions',
            'Non-critical columns'
          ],
          
          simplify: [
            'Complex charts → Simple stats',
            'Multi-column → Single column',
            'Hover menus → Tap menus'
          ],
          
          optimize: [
            'Lazy load images',
            'Paginate long lists',
            'Compress data transfers'
          ]
        }
      }
    };

#### 16.2 Responsive Implementation Guidelines

javascript

    const responsiveGuidelines = {
      // CSS Strategy
      cssStrategy: {
        approach: 'Mobile-first',
        
        example: `
          /* Mobile (default) */
          .container {
            padding: 16px;
            grid-template-columns: 1fr;
          }
          
          /* Tablet */
          @media (min-width: 768px) {
            .container {
              padding: 24px;
              grid-template-columns: repeat(2, 1fr);
            }
          }
          
          /* Desktop */
          @media (min-width: 1024px) {
            .container {
              padding: 32px;
              max-width: 1200px;
              margin: 0 auto;
              grid-template-columns: repeat(3, 1fr);
            }
          }
        `
      },
      
      // Touch Targets
      touchTargets: {
        minimum: {
          size: '44x44px',
          spacing: '8px between targets'
        },
        
        implementation: {
          buttons: 'Increase padding on mobile',
          links: 'Add padding, not just text',
          inputs: 'Minimum height 44px'
        }
      },
      
      // Performance
      performance: {
        images: {
          strategy: 'Responsive images',
          implementation: 'srcset and sizes',
          formats: 'WebP with fallbacks'
        },
        
        javascript: {
          strategy: 'Progressive enhancement',
          critical: 'Load critical JS first',
          defer: 'Non-critical features'
        }
      }
    };

### 17\. Performance Considerations

#### 17.1 Rendering Performance

javascript

    const renderingPerformance = {
      // CSS Optimizations
      cssOptimizations: {
        animations: {
          properties: ['transform', 'opacity'],
          avoid: ['width', 'height', 'top', 'left'],
          willChange: 'Use sparingly for heavy animations'
        },
        
        layouts: {
          flexbox: 'Preferred for 1D layouts',
          grid: 'Preferred for 2D layouts',
          avoid: 'Deep nesting, inline styles'
        },
        
        paint: {
          reduce: 'Composite layers',
          isolate: 'Use contain property',
          optimize: 'Reduce shadows, gradients on mobile'
        }
      },
      
      // React Optimizations
      reactOptimizations: {
        components: {
          memo: 'Wrap expensive components',
          keys: 'Stable keys for lists',
          lazy: 'Code split by route'
        },
        
        state: {
          updates: 'Batch where possible',
          derived: 'useMemo for expensive calculations',
          callbacks: 'useCallback for stable references'
        },
        
        virtualisation: {
          lists: 'Use react-window for long lists',
          threshold: 'Lists > 100 items'
        }
      },
      
      // Image Optimizations
      imageOptimizations: {
        formats: {
          photos: 'WebP with JPEG fallback',
          graphics: 'SVG for icons/logos',
          complex: 'PNG for transparency'
        },
        
        loading: {
          lazy: 'All non-critical images',
          eager: 'Above-fold hero images',
          progressive: 'JPEG progressive encoding'
        },
        
        sizing: {
          responsive: 'Multiple sizes via srcset',
          maxWidth: 'Never serve larger than displayed'
        }
      }
    };

#### 17.2 Performance Metrics & Monitoring

javascript

    const performanceMetrics = {
      // Target Metrics
      targets: {
        fcp: {
          metric: 'First Contentful Paint',
          target: '<1.5s',
          mobile: '<2.5s on 3G'
        },
        
        lcp: {
          metric: 'Largest Contentful Paint',
          target: '<2.5s',
          mobile: '<4s on 3G'
        },
        
        fid: {
          metric: 'First Input Delay',
          target: '<100ms',
          importance: 'Critical for interactivity'
        },
        
        cls: {
          metric: 'Cumulative Layout Shift',
          target: '<0.1',
          importance: 'Visual stability'
        },
        
        tti: {
          metric: 'Time to Interactive',
          target: '<3.5s',
          mobile: '<5s on 3G'
        }
      },
      
      // Monitoring Strategy
      monitoring: {
        tools: [
          'Lighthouse CI in pipeline',
          'Sentry for runtime performance',
          'Custom analytics for user timing'
        ],
        
        alerts: {
          regression: '>10% degradation',
          threshold: 'Below target for 3 days',
          p95: '95th percentile monitoring'
        }
      }
    };

* * *

Part 5: Implementation Guidelines
---------------------------------

### 18\. Frontend Architecture

#### 18.1 Component Structure

typescript

    // Component Organization
    const componentStructure = {
      // Atomic Design Pattern
      structure: {
        atoms: 'Basic building blocks (Button, Input, Icon)',
        molecules: 'Simple combinations (FormField, Card)',
        organisms: 'Complex components (Header, MaterialGrid)',
        templates: 'Page layouts (DashboardTemplate)',
        pages: 'Actual pages (CalculatorPage)'
      },
      
      // File Structure
      fileStructure: `
        /components
        ├── /atoms
        │   ├── Button/
        │   │   ├── Button.tsx
        │   │   ├── Button.styles.ts
        │   │   ├── Button.test.tsx
        │   │   ├── Button.stories.tsx
        │   │   └── index.ts
        │   └── ...
        ├── /molecules
        ├── /organisms
        ├── /templates
        └── /pages
      `,
      
      // Component Template
      template: `
        import { FC, memo } from 'react';
        import { ButtonProps } from './Button.types';
        import { buttonStyles } from './Button.styles';
        
        export const Button: FC<ButtonProps> = memo(({
          variant = 'primary',
          size = 'md',
          children,
          ...props
        }) => {
          const styles = buttonStyles({ variant, size });
          
          return (
            <button className={styles} {...props}>
              {children}
            </button>
          );
        });
        
        Button.displayName = 'Button';
      `
    };

#### 18.2 State Management Architecture

typescript

    // State Management Patterns
    const stateArchitecture = {
      // State Types
      stateTypes: {
        local: {
          tool: 'useState',
          usage: 'Component-specific UI state',
          examples: ['Modal open/close', 'Form inputs', 'Hover states']
        },
        
        global: {
          tool: 'Zustand',
          usage: 'Cross-component state',
          examples: ['User preferences', 'UI theme', 'Notifications']
        },
        
        server: {
          tool: 'React Query',
          usage: 'Server data and cache',
          examples: ['User data', 'Materials list', 'Calculations']
        },
        
        form: {
          tool: 'React Hook Form',
          usage: 'Complex form management',
          examples: ['Multi-step forms', 'Validation', 'Field arrays']
        }
      },
      
      // Example Store
      exampleStore: `
        // stores/calculatorStore.ts
        import { create } from 'zustand';
        import { devtools, persist } from 'zustand/middleware';
        
        interface CalculatorState {
          // State
          currentStep: number;
          projectData: ProjectData;
          calculations: Calculations | null;
          
          // Actions
          setStep: (step: number) => void;
          updateProjectData: (data: Partial<ProjectData>) => void;
          calculate: () => Promise<void>;
          reset: () => void;
        }
        
        export const useCalculatorStore = create<CalculatorState>()(
          devtools(
            persist(
              (set, get) => ({
                // Initial state
                currentStep: 0,
                projectData: initialProjectData,
                calculations: null,
                
                // Actions
                setStep: (step) => set({ currentStep: step }),
                
                updateProjectData: (data) => set(state => ({
                  projectData: { ...state.projectData, ...data }
                })),
                
                calculate: async () => {
                  const { projectData } = get();
                  const calculations = await calculateProject(projectData);
                  set({ calculations });
                },
                
                reset: () => set(initialState)
              }),
              {
                name: 'calculator-storage',
                partialize: (state) => ({ projectData: state.projectData })
              }
            )
          )
        );
      `
    };

### 19\. Component Development Guide

#### 19.1 Development Workflow

javascript

    const developmentWorkflow = {
      // Component Creation Process
      process: [
        {
          step: 1,
          task: 'Create component structure',
          includes: ['Component file', 'Types', 'Styles', 'Tests', 'Story']
        },
        {
          step: 2,
          task: 'Develop in Storybook',
          includes: ['All variants', 'All states', 'Interactions']
        },
        {
          step: 3,
          task: 'Write tests',
          includes: ['Unit tests', 'Accessibility tests', 'Visual regression']
        },
        {
          step: 4,
          task: 'Document usage',
          includes: ['Props documentation', 'Usage examples', 'Do\'s and don\'ts']
        }
      ],
      
      // Quality Checklist
      checklist: {
        functionality: [
          'Works in all required browsers',
          'Handles all edge cases',
          'Loading and error states',
          'Keyboard navigation'
        ],
        
        accessibility: [
          'Semantic HTML',
          'ARIA labels where needed',
          'Keyboard accessible',
          'Screen reader tested'
        ],
        
        performance: [
          'No unnecessary re-renders',
          'Images optimized',
          'Code split if large',
          'Animations use GPU'
        ],
        
        maintainability: [
          'Props are typed',
          'Code is documented',
          'Tests are comprehensive',
          'Follows style guide'
        ]
      }
    };

#### 19.2 Component Patterns

typescript

    // Common Component Patterns
    const componentPatterns = {
      // Compound Components
      compoundComponent: `
        const Card = ({ children }) => {
          return <div className="card">{children}</div>;
        };
        
        Card.Header = ({ children }) => {
          return <div className="card-header">{children}</div>;
        };
        
        Card.Body = ({ children }) => {
          return <div className="card-body">{children}</div>;
        };
        
        // Usage
        <Card>
          <Card.Header>Title</Card.Header>
          <Card.Body>Content</Card.Body>
        </Card>
      `,
      
      // Render Props
      renderProps: `
        const DataFetcher = ({ url, children }) => {
          const { data, loading, error } = useFetch(url);
          return children({ data, loading, error });
        };
        
        // Usage
        <DataFetcher url="/api/data">
          {({ data, loading, error }) => {
            if (loading) return <Spinner />;
            if (error) return <Error />;
            return <DataDisplay data={data} />;
          }}
        </DataFetcher>
      `,
      
      // Custom Hooks
      customHooks: `
        const useDebounce = (value: string, delay: number) => {
          const [debouncedValue, setDebouncedValue] = useState(value);
          
          useEffect(() => {
            const handler = setTimeout(() => {
              setDebouncedValue(value);
            }, delay);
            
            return () => clearTimeout(handler);
          }, [value, delay]);
          
          return debouncedValue;
        };
      `
    };

### 20\. Testing Requirements

#### 20.1 Testing Strategy

javascript

    const testingStrategy = {
      // Test Types
      testTypes: {
        unit: {
          coverage: '>90%',
          tools: ['Jest', 'React Testing Library'],
          focus: ['Logic', 'Component behavior', 'Utils']
        },
        
        integration: {
          coverage: '>80%',
          tools: ['Jest', 'MSW for API mocking'],
          focus: ['User flows', 'API integration', 'State management']
        },
        
        e2e: {
          coverage: 'Critical paths',
          tools: ['Cypress'],
          focus: ['Complete user journeys', 'Cross-browser']
        },
        
        visual: {
          coverage: 'All components',
          tools: ['Storybook', 'Chromatic'],
          focus: ['Visual regression', 'Responsive design']
        }
      },
      
      // Example Tests
      exampleTests: {
        component: `
          describe('Button', () => {
            it('renders with correct text', () => {
              render(<Button>Click me</Button>);
              expect(screen.getByText('Click me')).toBeInTheDocument();
            });
            
            it('calls onClick when clicked', () => {
              const handleClick = jest.fn();
              render(<Button onClick={handleClick}>Click</Button>);
              
              fireEvent.click(screen.getByText('Click'));
              expect(handleClick).toHaveBeenCalledTimes(1);
            });
            
            it('is disabled when disabled prop is true', () => {
              render(<Button disabled>Click</Button>);
              expect(screen.getByText('Click')).toBeDisabled();
            });
          });
        `,
        
        integration: `
          describe('Calculator Flow', () => {
            it('completes calculation successfully', async () => {
              render(<CalculatorPage />);
              
              // Fill plot details
              fireEvent.change(screen.getByLabelText('Plot Length'), {
                target: { value: '40' }
              });
              
              fireEvent.change(screen.getByLabelText('Plot Width'), {
                target: { value: '60' }
              });
              
              fireEvent.click(screen.getByText('Next'));
              
              // Assertions
              await waitFor(() => {
                expect(screen.getByText('2,400 sq ft')).toBeInTheDocument();
              });
            });
          });
        `
      }
    };

#### 20.2 Testing Best Practices

javascript

    const testingBestPractices = {
      principles: [
        'Test behavior, not implementation',
        'Write tests before fixing bugs',
        'Keep tests simple and focused',
        'Use meaningful test descriptions',
        'Avoid testing implementation details'
      ],
      
      patterns: {
        arrange_act_assert: `
          it('should update total when item is added', () => {
            // Arrange
            const { getByText, getByLabelText } = render(<Cart />);
            
            // Act
            fireEvent.click(getByText('Add Item'));
            
            // Assert
            expect(getByLabelText('Total')).toHaveTextContent('₹100');
          });
        `,
        
        testUtilities: `
          // test-utils.ts
          const renderWithProviders = (
            ui: React.ReactElement,
            options?: RenderOptions
          ) => {
            const Wrapper = ({ children }) => (
              <QueryClientProvider client={queryClient}>
                <ThemeProvider>
                  {children}
                </ThemeProvider>
              </QueryClientProvider>
            );
            
            return render(ui, { wrapper: Wrapper, ...options });
          };
        `
      }
    };

### 21\. Handoff Specifications

#### 21.1 Design to Development Handoff

javascript

    const handoffSpecifications = {
      // Design Deliverables
      designDeliverables: {
        files: {
          designs: 'Figma file with all screens',
          assets: 'Exported images, icons, illustrations',
          prototype: 'Interactive Figma prototype',
          specs: 'Detailed specifications document'
        },
        
        organization: {
          naming: 'Consistent naming convention',
          structure: 'Logical page/component hierarchy',
          versioning: 'Clear version history',
          status: 'Ready/In Progress/Approved tags'
        }
      },
      
      // Development Requirements
      developmentRequirements: {
        assets: {
          format: 'SVG for icons, WebP/PNG for images',
          sizes: '1x, 2x, 3x for raster images',
          naming: 'kebab-case, descriptive names',
          optimization: 'Compressed and optimized'
        },
        
        tokens: {
          export: 'Design tokens in JSON format',
          includes: ['Colors', 'Typography', 'Spacing', 'Shadows'],
          format: 'CSS custom properties ready'
        },
        
        documentation: {
          interactions: 'All states and interactions documented',
          edgeCases: 'Empty, error, loading states',
          responsive: 'Breakpoint behaviors defined',
          accessibility: 'Focus states, ARIA requirements'
        }
      },
      
      // Communication Protocol
      communication: {
        channels: {
          questions: 'Slack #design-dev channel',
          reviews: 'Weekly design review meetings',
          updates: 'Figma comments for iterations'
        },
        
        process: {
          clarification: '24hr SLA for questions',
          signoff: 'Designer reviews implementation',
          iteration: 'Continuous until approved'
        }
      }
    };

#### 21.2 QA Handoff

javascript

    const qaHandoff = {
      // Test Scenarios
      testScenarios: {
        functional: [
          'All user flows documented',
          'Edge cases identified',
          'Error scenarios listed',
          'Success criteria defined'
        ],
        
        visual: [
          'Design comparison checklist',
          'Responsive breakpoints',
          'Animation timings',
          'Color accuracy'
        ],
        
        performance: [
          'Load time expectations',
          'Interaction responsiveness',
          'Data size limits',
          'Concurrent user targets'
        ]
      },
      
      // Bug Reporting
      bugReporting: {
        template: {
          title: 'Clear, descriptive title',
          description: 'Steps to reproduce',
          expected: 'Expected behavior',
          actual: 'Actual behavior',
          environment: 'Browser, OS, device',
          severity: 'Critical/High/Medium/Low',
          attachments: 'Screenshots, videos'
        },
        
        workflow: {
          tool: 'Jira',
          assignment: 'Auto-assign to component owner',
          priority: 'Based on severity and impact',
          verification: 'QA verifies fixes'
        }
      }
    };

* * *

Conclusion
----------

This comprehensive UI/UX Specification document provides the complete blueprint for implementing the frontend of the Clarity Engine. With over 500 detailed specifications covering everything from micro-interactions to responsive behaviors, the development team has clear guidance for creating a world-class user experience.

The key to successful implementation lies in:

1.  **Adherence to the design system** for consistency
2.  **Mobile-first development** approach
3.  **Performance optimization** at every level
4.  **Accessibility** as a core requirement, not an afterthought
5.  **Continuous testing** throughout development

Combined with the other spoke documents, this forms a complete implementation guide for building India's most transparent and user-friendly construction cost calculator.

* * *

**Document Version:** 2.0  
**Last Updated:** July 11, 2025  
**Next Review:** Post-MVP Launch  
**Total Pages:** 178