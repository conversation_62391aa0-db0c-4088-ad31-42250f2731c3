Spoke Document 4: Design System & UI Component Library v2.0
===========================================================

**Document ID:** SD-004  
**Version:** 2.0  
**Owner:** Design Lead + Frontend Lead  
**Audience:** UI/UX Designers, Frontend Developers, QA Engineers  
**Linked to:** Master PRD v2.0, Technical Design Document v2.0  
**Status:** Final - Ready for Implementation

* * *

Table of Contents
-----------------

**Part 1: Design Foundations**

1.  Design Principles & Philosophy
2.  Brand Identity & Visual Language
3.  Color System
4.  Typography System
5.  Spacing & Layout Grid
6.  Icons & Illustrations

**Part 2: Core Components** 7. Buttons & Actions 8. Form Elements 9. Navigation Components 10. Cards & Containers 11. Data Display Components 12. Feedback & Messaging

**Part 3: Complex Components** 13. Calculator Interface Components 14. Material Selection Components 15. Visualization Components 16. Project Dashboard Components 17. Report Generation Components

**Part 4: Patterns & Flows** 18. User Flow Patterns 19. Responsive Design Patterns 20. Animation & Interaction Patterns 21. Accessibility Patterns

**Part 5: Implementation Guidelines** 22. Component Architecture 23. Theming System 24. Performance Guidelines 25. Testing & Documentation

* * *

Part 1: Design Foundations
--------------------------

### 1\. Design Principles & Philosophy

#### 1.1 Core Design Principles

typescript

    // Design principles that guide all decisions
    const designPrinciples = {
      // Clarity Above All
      clarity: {
        definition: "Every element should have a clear purpose and be immediately understandable",
        applications: [
          "Use plain language, avoid jargon",
          "Progressive disclosure of complexity",
          "Visual hierarchy guides attention",
          "Consistent patterns reduce cognitive load"
        ],
        examples: {
          good: "₹18.5 Lakhs total cost",
          bad: "Total project expenditure: INR 1,850,000.00"
        }
      },
      
      // Trust Through Transparency
      transparency: {
        definition: "Users should understand how calculations work and what factors influence costs",
        applications: [
          "Show calculation breakdowns",
          "Explain assumptions clearly",
          "Provide confidence indicators",
          "Enable easy verification"
        ],
        implementation: {
          costBreakdown: "Always visible and explorable",
          assumptions: "Listed with ability to modify",
          dataSource: "Clear attribution for rates"
        }
      },
      
      // Empower Decision Making
      empowerment: {
        definition: "Give users the tools and information to make confident decisions",
        applications: [
          "Comparison tools for materials/options",
          "What-if scenarios",
          "Cost optimization suggestions",
          "Export and share capabilities"
        ]
      },
      
      // Accessible to All
      accessibility: {
        definition: "Usable by everyone regardless of technical expertise or abilities",
        applications: [
          "Mobile-first responsive design",
          "Support for regional languages",
          "WCAG AA compliance minimum",
          "Offline capability for basic features"
        ]
      },
      
      // Delightful Experience
      delight: {
        definition: "Professional yet approachable, serious yet enjoyable",
        applications: [
          "Smooth animations guide attention",
          "Celebratory moments for milestones",
          "Helpful tips and insights",
          "Personal touches where appropriate"
        ]
      }
    };

#### 1.2 Design Philosophy

typescript

    // Overarching design philosophy
    const designPhilosophy = {
      // Visual Design Philosophy
      visual: {
        style: "Clean Professional Warmth",
        
        characteristics: [
          "Generous whitespace for breathing room",
          "Soft shadows for depth without heaviness",
          "Rounded corners for approachability",
          "Selective use of color for emphasis"
        ],
        
        inspiration: [
          "Modern Indian architecture aesthetics",
          "Financial app clarity (Zerodha, Paytm)",
          "Global best practices (Stripe, Linear)"
        ],
        
        avoid: [
          "Cluttered layouts",
          "Harsh contrasts",
          "Decorative elements without purpose",
          "Trendy effects that date quickly"
        ]
      },
      
      // Interaction Philosophy
      interaction: {
        principle: "Predictable and Responsive",
        
        guidelines: [
          "Instant feedback for all actions",
          "Smooth transitions between states",
          "Forgiveness for mistakes (undo/redo)",
          "Keyboard navigation support"
        ],
        
        microInteractions: {
          hover: "Subtle elevation and color shift",
          click: "Immediate visual feedback",
          loading: "Skeleton screens, not spinners",
          success: "Brief celebration, then continue"
        }
      },
      
      // Content Philosophy  
      content: {
        voice: "Knowledgeable Friend",
        
        characteristics: [
          "Conversational but professional",
          "Encouraging and supportive",
          "Clear without being condescending",
          "Culturally aware and inclusive"
        ],
        
        examples: {
          error: "Let's fix this together",
          success: "Great choice! Here's what's next",
          help: "Many users find this helpful",
          waiting: "Calculating your dream home costs..."
        }
      }
    };

### 2\. Brand Identity & Visual Language

#### 2.1 Brand Elements

typescript

    // Core brand identity elements
    const brandIdentity = {
      // Logo System
      logo: {
        primary: {
          symbol: "Interconnected house outline with calculator grid",
          wordmark: "Clarity Engine",
          tagline: "Building Cost Confidence",
          
          usage: {
            minSize: "24px height",
            clearSpace: "Equal to 'C' height on all sides",
            backgrounds: ["White", "Light gray", "Dark with reversed colors"]
          }
        },
        
        variations: {
          horizontal: "Symbol + Wordmark side by side",
          stacked: "Symbol above Wordmark",
          symbol: "Symbol only for app icons",
          wordmark: "Text only for constrained spaces"
        },
        
        colors: {
          primary: "#006BB3", // Trust Blue
          secondary: "#00A86B", // Growth Green
          mono: ["#000000", "#FFFFFF"]
        }
      },
      
      // Visual Metaphors
      visualMetaphors: {
        construction: {
          elements: ["Grid patterns", "Blueprint lines", "Building blocks"],
          usage: "Background patterns, loading states"
        },
        
        calculation: {
          elements: ["Plus signs", "Equal signs", "Grid dots"],
          usage: "Icons, empty states, dividers"
        },
        
        growth: {
          elements: ["Upward arrows", "Plant motifs", "Progress bars"],
          usage: "Success states, progress indicators"
        }
      },
      
      // Illustration Style
      illustrationStyle: {
        style: "Simplified Isometric",
        
        characteristics: [
          "45-degree isometric angle",
          "Flat colors with subtle gradients",
          "Geometric simplification",
          "Consistent stroke weights"
        ],
        
        colorPalette: {
          primary: ["#006BB3", "#0084E3", "#3399FF"],
          secondary: ["#00A86B", "#00C781", "#33D6A0"],
          neutral: ["#F5F7FA", "#E5E9F0", "#C4CDD5"]
        },
        
        subjects: [
          "House construction stages",
          "Material categories",
          "Professional characters",
          "Success celebrations"
        ]
      }
    };

#### 2.2 Visual Language Patterns

typescript

    // Consistent visual language patterns
    const visualLanguage = {
      // Card Patterns
      cardPatterns: {
        elevation: {
          resting: "box-shadow: 0 1px 3px rgba(0,0,0,0.08)",
          hover: "box-shadow: 0 4px 12px rgba(0,0,0,0.12)",
          active: "box-shadow: 0 2px 6px rgba(0,0,0,0.10)"
        },
        
        borderRadius: {
          small: "8px",
          default: "12px", 
          large: "16px",
          circular: "50%"
        },
        
        padding: {
          compact: "16px",
          default: "24px",
          spacious: "32px"
        }
      },
      
      // Semantic Patterns
      semanticPatterns: {
        hierarchy: {
          primary: "Filled backgrounds, strong colors",
          secondary: "Outlined borders, medium colors",
          tertiary: "Text only, subtle colors"
        },
        
        states: {
          interactive: "Cursor pointer, hover effects",
          disabled: "Opacity 0.5, cursor not-allowed",
          loading: "Pulse animation, skeleton screens",
          selected: "Border accent, background tint"
        },
        
        feedback: {
          success: "Green checkmark, subtle animation",
          error: "Red exclamation, shake animation",
          warning: "Orange triangle, pulse animation",
          info: "Blue circle, fade in"
        }
      },
      
      // Layout Patterns
      layoutPatterns: {
        pageStructure: {
          header: "Fixed top, 64px height, white background",
          sidebar: "256px width, collapsible on mobile",
          content: "Max-width 1200px, centered",
          footer: "Auto height, gray background"
        },
        
        sections: {
          hero: "Full width, gradient background",
          features: "3-column grid, responsive",
          pricing: "Card-based comparison",
          testimonials: "Carousel or grid"
        },
        
        spacing: {
          sections: "80px vertical padding",
          components: "24px gap",
          elements: "8px-16px spacing"
        }
      }
    };

### 3\. Color System

#### 3.1 Color Palette

typescript

    // Comprehensive color system
    const colorSystem = {
      // Brand Colors
      brand: {
        primary: {
          50: "#E6F2FF",
          100: "#BAE0FF",
          200: "#7AC2FF",
          300: "#3AA4FF",
          400: "#0084E3",
          500: "#006BB3", // Main brand color
          600: "#005A96",
          700: "#004878",
          800: "#00365A",
          900: "#00243D"
        },
        
        secondary: {
          50: "#E6F9F2",
          100: "#B3EDDB",
          200: "#80E1C3",
          300: "#4DD5AC",
          400: "#1AC995",
          500: "#00A86B", // Main secondary
          600: "#008F5A",
          700: "#007549",
          800: "#005C38",
          900: "#004227"
        }
      },
      
      // Semantic Colors
      semantic: {
        success: {
          light: "#D4F4E2",
          main: "#00C781",
          dark: "#008552",
          contrast: "#FFFFFF"
        },
        
        error: {
          light: "#FFEBEE",
          main: "#DC3545",
          dark: "#B71C1C",
          contrast: "#FFFFFF"
        },
        
        warning: {
          light: "#FFF8E1",
          main: "#FFA000",
          dark: "#F57C00",
          contrast: "#000000"
        },
        
        info: {
          light: "#E3F2FD",
          main: "#2196F3",
          dark: "#1565C0",
          contrast: "#FFFFFF"
        }
      },
      
      // Neutral Colors
      neutral: {
        0: "#FFFFFF",
        50: "#FAFBFC",
        100: "#F5F7FA",
        200: "#E5E9F0",
        300: "#C4CDD5",
        400: "#9FA8B3",
        500: "#6B7785",
        600: "#4A5568",
        700: "#2D3748",
        800: "#1A202C",
        900: "#0D1117"
      },
      
      // Functional Colors
      functional: {
        background: {
          primary: "#FFFFFF",
          secondary: "#FAFBFC",
          tertiary: "#F5F7FA",
          inverse: "#1A202C"
        },
        
        text: {
          primary: "#1A202C",
          secondary: "#4A5568",
          tertiary: "#6B7785",
          disabled: "#9FA8B3",
          inverse: "#FFFFFF"
        },
        
        border: {
          light: "#E5E9F0",
          medium: "#C4CDD5",
          dark: "#9FA8B3",
          focus: "#006BB3"
        },
        
        overlay: {
          light: "rgba(255, 255, 255, 0.8)",
          medium: "rgba(0, 0, 0, 0.5)",
          dark: "rgba(0, 0, 0, 0.8)"
        }
      },
      
      // Special Purpose Colors
      special: {
        qualityTiers: {
          smart: "#FFA000",    // Orange - Affordable
          premium: "#2196F3",  // Blue - Balanced
          luxury: "#9C27B0"    // Purple - High-end
        },
        
        materials: {
          structure: "#795548",   // Brown
          finishing: "#FF6B6B",   // Coral
          electrical: "#4ECDC4",  // Teal
          plumbing: "#45B7D1",    // Sky Blue
          exterior: "#95E1D3"     // Mint
        },
        
        visualization: {
          chart1: "#006BB3",
          chart2: "#00A86B",
          chart3: "#FFA000",
          chart4: "#DC3545",
          chart5: "#9C27B0",
          chart6: "#00BCD4"
        }
      }
    };
    
    // Color usage guidelines
    const colorUsageGuidelines = {
      // Contrast Requirements
      contrast: {
        text: {
          normal: "4.5:1 minimum",
          large: "3:1 minimum (18px+ or 14px+ bold)",
          decorative: "No requirement"
        },
        
        interactive: {
          normal: "3:1 minimum against background",
          hover: "Darken by 10%",
          active: "Darken by 20%"
        }
      },
      
      // Color Application Rules
      application: {
        primary: "CTA buttons, active states, links",
        secondary: "Supporting actions, accents",
        semantic: "Status messages, alerts only",
        neutral: "Text, borders, backgrounds",
        
        doNot: [
          "Don't use more than 3 colors prominently per screen",
          "Don't use color as the only differentiator",
          "Don't use pure black (#000000) for text",
          "Don't use semantic colors decoratively"
        ]
      }
    };

### 4\. Typography System

#### 4.1 Font Families & Hierarchy

typescript

    // Typography system definition
    const typographySystem = {
      // Font Families
      fonts: {
        primary: {
          family: "'Inter', -apple-system, BlinkMacSystemFont, sans-serif",
          weights: {
            regular: 400,
            medium: 500,
            semibold: 600,
            bold: 700
          },
          usage: "All UI text, body copy"
        },
        
        secondary: {
          family: "'SF Mono', 'Monaco', 'Inconsolata', monospace",
          weights: {
            regular: 400,
            medium: 500
          },
          usage: "Numbers, codes, calculations"
        },
        
        display: {
          family: "'Inter Display', 'Inter', sans-serif",
          weights: {
            medium: 500,
            semibold: 600,
            bold: 700
          },
          usage: "Large headings, marketing pages"
        }
      },
      
      // Type Scale
      scale: {
        hero: {
          fontSize: "56px",
          lineHeight: "64px",
          letterSpacing: "-0.02em",
          fontWeight: 700,
          usage: "Marketing hero sections only"
        },
        
        h1: {
          fontSize: "40px",
          lineHeight: "48px",
          letterSpacing: "-0.01em",
          fontWeight: 700,
          usage: "Page titles"
        },
        
        h2: {
          fontSize: "32px",
          lineHeight: "40px",
          letterSpacing: "-0.01em",
          fontWeight: 600,
          usage: "Section headers"
        },
        
        h3: {
          fontSize: "24px",
          lineHeight: "32px",
          letterSpacing: "-0.005em",
          fontWeight: 600,
          usage: "Subsection headers"
        },
        
        h4: {
          fontSize: "20px",
          lineHeight: "28px",
          letterSpacing: "-0.005em",
          fontWeight: 600,
          usage: "Card titles"
        },
        
        h5: {
          fontSize: "16px",
          lineHeight: "24px",
          letterSpacing: "0",
          fontWeight: 600,
          usage: "Small headers"
        },
        
        body1: {
          fontSize: "16px",
          lineHeight: "24px",
          letterSpacing: "0",
          fontWeight: 400,
          usage: "Default body text"
        },
        
        body2: {
          fontSize: "14px",
          lineHeight: "20px",
          letterSpacing: "0",
          fontWeight: 400,
          usage: "Secondary body text"
        },
        
        caption: {
          fontSize: "12px",
          lineHeight: "16px",
          letterSpacing: "0.01em",
          fontWeight: 400,
          usage: "Help text, labels"
        },
        
        overline: {
          fontSize: "12px",
          lineHeight: "16px",
          letterSpacing: "0.08em",
          fontWeight: 600,
          textTransform: "uppercase",
          usage: "Category labels"
        },
        
        button: {
          fontSize: "14px",
          lineHeight: "20px",
          letterSpacing: "0.02em",
          fontWeight: 500,
          usage: "Button text"
        },
        
        // Special number display
        displayNumber: {
          large: {
            fontSize: "48px",
            lineHeight: "56px",
            fontWeight: 600,
            fontFamily: "secondary"
          },
          
          medium: {
            fontSize: "32px",
            lineHeight: "40px",
            fontWeight: 500,
            fontFamily: "secondary"
          },
          
          small: {
            fontSize: "20px",
            lineHeight: "28px",
            fontWeight: 500,
            fontFamily: "secondary"
          }
        }
      },
      
      // Responsive Typography
      responsive: {
        breakpoints: {
          mobile: { max: 640 },
          tablet: { min: 641, max: 1024 },
          desktop: { min: 1025 }
        },
        
        scaling: {
          hero: { mobile: "40px", tablet: "48px", desktop: "56px" },
          h1: { mobile: "32px", tablet: "36px", desktop: "40px" },
          h2: { mobile: "24px", tablet: "28px", desktop: "32px" },
          h3: { mobile: "20px", tablet: "22px", desktop: "24px" },
          h4: { mobile: "18px", tablet: "19px", desktop: "20px" },
          body: { mobile: "15px", tablet: "15px", desktop: "16px" }
        }
      }
    };

#### 4.2 Text Styling & Usage

typescript

    // Text styling patterns
    const textStyling = {
      // Text Colors by Purpose
      textColors: {
        heading: colorSystem.neutral[900],
        body: colorSystem.neutral[800],
        secondary: colorSystem.neutral[600],
        tertiary: colorSystem.neutral[500],
        disabled: colorSystem.neutral[400],
        placeholder: colorSystem.neutral[400],
        link: colorSystem.brand.primary[500],
        linkHover: colorSystem.brand.primary[600],
        error: colorSystem.semantic.error.main,
        success: colorSystem.semantic.success.dark
      },
      
      // Text Utilities
      utilities: {
        truncate: {
          css: `
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          `,
          usage: "Long titles, file names"
        },
        
        clamp: {
          css: `
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
          `,
          usage: "Multi-line descriptions"
        },
        
        balance: {
          css: "text-wrap: balance",
          usage: "Headlines for better line breaks"
        }
      },
      
      // Number Formatting
      numberFormatting: {
        currency: {
          indian: {
            format: "₹XX,XX,XXX",
            examples: ["₹1,50,000", "₹45.5 Lakhs", "₹2.3 Cr"],
            rules: [
              "Use Lakhs/Cr for large numbers",
              "Always show ₹ symbol",
              "Round to meaningful precision"
            ]
          }
        },
        
        percentage: {
          format: "XX%",
          precision: "0-1 decimal places",
          examples: ["15%", "12.5%", "0.5%"]
        },
        
        quantity: {
          format: "Comma separated",
          examples: ["1,234 sqft", "2,500 bags", "15 units"]
        }
      }
    };

### 5\. Spacing & Layout Grid

#### 5.1 Spacing System

typescript

    // Consistent spacing system
    const spacingSystem = {
      // Base unit
      base: 8, // pixels
      
      // Spacing scale
      scale: {
        0: "0px",
        1: "8px",
        2: "16px",
        3: "24px",
        4: "32px",
        5: "40px",
        6: "48px",
        7: "56px",
        8: "64px",
        9: "72px",
        10: "80px",
        11: "88px",
        12: "96px",
        16: "128px",
        20: "160px",
        24: "192px"
      },
      
      // Component-specific spacing
      componentSpacing: {
        button: {
          paddingX: "space-3", // 24px
          paddingY: "space-2", // 16px
          gap: "space-2"       // 16px between icon and text
        },
        
        card: {
          padding: "space-3",  // 24px
          gap: "space-2"       // 16px between elements
        },
        
        form: {
          labelGap: "space-1", // 8px
          fieldGap: "space-3", // 24px
          sectionGap: "space-5" // 40px
        },
        
        list: {
          itemGap: "space-2",  // 16px
          sectionGap: "space-4" // 32px
        }
      },
      
      // Responsive spacing
      responsiveSpacing: {
        section: {
          mobile: "space-6",  // 48px
          tablet: "space-8",  // 64px
          desktop: "space-10" // 80px
        },
        
        container: {
          mobile: "space-2",  // 16px
          tablet: "space-3",  // 24px
          desktop: "space-4"  // 32px
        }
      }
    };

#### 5.2 Layout Grid System

typescript

    // Grid system configuration
    const gridSystem = {
      // Container widths
      containers: {
        xs: "100%",
        sm: "640px",
        md: "768px",
        lg: "1024px",
        xl: "1280px",
        xxl: "1536px",
        
        default: "1200px", // Most common
        wide: "1440px",    // Dashboard views
        narrow: "720px"    // Content pages
      },
      
      // Grid configuration
      grid: {
        columns: 12,
        gutters: {
          mobile: "16px",
          tablet: "24px", 
          desktop: "32px"
        },
        
        // Flex grid utilities
        flexGrid: {
          container: `
            display: flex;
            flex-wrap: wrap;
            margin: calc(var(--gutter) * -0.5);
          `,
          
          item: `
            padding: calc(var(--gutter) * 0.5);
            flex: 0 0 auto;
          `,
          
          columns: {
            1: "8.333%",
            2: "16.667%",
            3: "25%",
            4: "33.333%",
            5: "41.667%",
            6: "50%",
            7: "58.333%",
            8: "66.667%",
            9: "75%",
            10: "83.333%",
            11: "91.667%",
            12: "100%"
          }
        }
      },
      
      // Common layouts
      layouts: {
        dashboard: {
          sidebar: "256px fixed",
          content: "remaining space",
          header: "64px fixed",
          
          responsive: {
            mobile: "Full screen, bottom nav",
            tablet: "Collapsible sidebar",
            desktop: "Fixed sidebar"
          }
        },
        
        calculator: {
          input: "40% or 480px max",
          output: "60% or fill",
          
          responsive: {
            mobile: "Stacked, full width",
            tablet: "Side by side if space",
            desktop: "Side by side optimal"
          }
        },
        
        marketing: {
          hero: "Full width",
          content: "1200px max, centered",
          features: "3 column grid",
          
          responsive: {
            mobile: "Single column",
            tablet: "2 columns where applicable",
            desktop: "Full layout"
          }
        }
      }
    };

### 6\. Icons & Illustrations

#### 6.1 Icon System

typescript

    // Icon system specification
    const iconSystem = {
      // Icon library
      library: {
        primary: "Lucide React",
        fallback: "Custom SVG icons",
        
        characteristics: [
          "24x24px base size",
          "2px stroke width",
          "Rounded line caps",
          "Consistent visual weight"
        ]
      },
      
      // Icon sizes
      sizes: {
        xs: "16px",
        sm: "20px",
        md: "24px", // Default
        lg: "32px",
        xl: "40px"
      },
      
      // Icon categories
      categories: {
        navigation: [
          "menu", "arrow-left", "arrow-right", "chevron-down",
          "home", "settings", "log-out", "user"
        ],
        
        action: [
          "plus", "edit", "trash", "save", "download",
          "share", "copy", "refresh", "search"
        ],
        
        status: [
          "check-circle", "x-circle", "alert-circle", "info",
          "alert-triangle", "loader", "clock", "bell"
        ],
        
        construction: [
          "home", "building", "hammer", "wrench",
          "ruler", "hard-hat", "truck", "package"
        ],
        
        finance: [
          "indian-rupee", "calculator", "trending-up",
          "pie-chart", "bar-chart", "receipt", "wallet"
        ]
      },
      
      // Custom icons needed
      customIcons: {
        qualityTiers: {
          smart: "Light bulb with rupee",
          premium: "Star with checkmark",
          luxury: "Crown with sparkle"
        },
        
        materials: {
          cement: "Bag with C",
          steel: "I-beam cross section",
          brick: "Stacked bricks",
          paint: "Paint roller"
        },
        
        features: {
          instant: "Lightning bolt",
          accurate: "Target center",
          trusted: "Shield checkmark"
        }
      },
      
      // Icon usage rules
      usage: {
        color: {
          default: "currentColor inheritance",
          semantic: "Match semantic color for status",
          branded: "Use brand colors sparingly"
        },
        
        placement: {
          beforeText: "8px gap",
          afterText: "8px gap",
          standalone: "With tooltip on hover"
        },
        
        animation: {
          loading: "Smooth rotation",
          success: "Scale and fade",
          hover: "Subtle scale 1.1"
        }
      }
    };

#### 6.2 Illustration System

typescript

    // Illustration guidelines
    const illustrationSystem = {
      // Illustration styles
      styles: {
        primary: {
          name: "Isometric Construction",
          angle: "45 degrees",
          
          characteristics: [
            "Simplified geometric shapes",
            "Flat colors with subtle gradients",
            "Consistent 2px strokes",
            "Modular components"
          ],
          
          colorPalette: {
            primary: ["#006BB3", "#0084E3", "#3399FF"],
            secondary: ["#00A86B", "#00C781", "#33D6A0"],
            neutral: ["#F5F7FA", "#E5E9F0", "#C4CDD5"],
            accent: ["#FFA000", "#FFB833", "#FFD166"]
          }
        },
        
        secondary: {
          name: "Line Art Diagrams",
          
          characteristics: [
            "Single color line drawings",
            "Technical accuracy",
            "Clean and minimal",
            "Educational focus"
          ],
          
          usage: [
            "Construction process steps",
            "Technical explanations",
            "Help documentation"
          ]
        }
      },
      
      // Illustration library
      library: {
        emptyStates: {
          noProjects: {
            image: "isometric_house_blueprint",
            title: "Start your first project",
            description: "Create a cost estimate for your dream home"
          },
          
          noMaterials: {
            image: "isometric_material_stack",
            title: "No materials selected yet",
            description: "Browse our catalog to add materials"
          },
          
          calculating: {
            image: "isometric_calculator_working",
            title: "Crunching the numbers",
            description: "Calculating accurate costs for your project"
          }
        },
        
        success: {
          projectCreated: {
            image: "isometric_house_complete",
            animation: "fade_in_scale"
          },
          
          reportGenerated: {
            image: "isometric_document_check",
            animation: "slide_up_fade"
          }
        },
        
        errors: {
          404: {
            image: "isometric_house_search",
            title: "Page not found",
            description: "Let's get you back home"
          },
          
          500: {
            image: "isometric_house_maintenance",
            title: "Under maintenance",
            description: "We'll be back shortly"
          }
        }
      },
      
      // Animation guidelines
      animations: {
        microAnimations: {
          hover: "Subtle float effect",
          loading: "Gentle pulse",
          entrance: "Fade and scale"
        },
        
        complexAnimations: {
          construction: [
            "Foundation appears",
            "Walls rise up",
            "Roof slides in",
            "Details fade in"
          ],
          
          calculation: [
            "Numbers fly in",
            "Calculator buttons press",
            "Result celebrates"
          ]
        }
      }
    };

* * *

Part 2: Core Components
-----------------------

### 7\. Buttons & Actions

#### 7.1 Button Component

typescript

    // Button component specification
    interface ButtonProps {
      variant: 'primary' | 'secondary' | 'tertiary' | 'ghost' | 'danger';
      size: 'small' | 'medium' | 'large';
      state: 'default' | 'hover' | 'active' | 'disabled' | 'loading';
      icon?: IconName;
      iconPosition?: 'left' | 'right';
      fullWidth?: boolean;
    }
    
    const buttonComponent = {
      // Base styles
      base: {
        display: "inline-flex",
        alignItems: "center",
        justifyContent: "center",
        borderRadius: "8px",
        fontWeight: 500,
        transition: "all 0.2s ease",
        cursor: "pointer",
        border: "none",
        outline: "none",
        textDecoration: "none",
        whiteSpace: "nowrap",
        userSelect: "none"
      },
      
      // Variants
      variants: {
        primary: {
          background: colorSystem.brand.primary[500],
          color: colorSystem.neutral[0],
          border: "none",
          
          hover: {
            background: colorSystem.brand.primary[600],
            transform: "translateY(-1px)",
            boxShadow: "0 4px 12px rgba(0, 107, 179, 0.2)"
          },
          
          active: {
            background: colorSystem.brand.primary[700],
            transform: "translateY(0)"
          },
          
          disabled: {
            background: colorSystem.neutral[300],
            cursor: "not-allowed",
            transform: "none"
          }
        },
        
        secondary: {
          background: "transparent",
          color: colorSystem.brand.primary[500],
          border: `2px solid ${colorSystem.brand.primary[500]}`,
          
          hover: {
            background: colorSystem.brand.primary[50],
            borderColor: colorSystem.brand.primary[600],
            color: colorSystem.brand.primary[600]
          },
          
          active: {
            background: colorSystem.brand.primary[100],
            borderColor: colorSystem.brand.primary[700],
            color: colorSystem.brand.primary[700]
          }
        },
        
        tertiary: {
          background: colorSystem.neutral[100],
          color: colorSystem.neutral[700],
          border: "none",
          
          hover: {
            background: colorSystem.neutral[200],
            color: colorSystem.neutral[800]
          }
        },
        
        ghost: {
          background: "transparent",
          color: colorSystem.neutral[600],
          border: "none",
          
          hover: {
            background: colorSystem.neutral[100],
            color: colorSystem.neutral[800]
          }
        },
        
        danger: {
          background: colorSystem.semantic.error.main,
          color: colorSystem.neutral[0],
          
          hover: {
            background: colorSystem.semantic.error.dark
          }
        }
      },
      
      // Sizes
      sizes: {
        small: {
          height: "32px",
          paddingX: "16px",
          fontSize: "14px",
          iconSize: "16px",
          gap: "6px"
        },
        
        medium: {
          height: "40px",
          paddingX: "24px",
          fontSize: "16px",
          iconSize: "20px",
          gap: "8px"
        },
        
        large: {
          height: "48px",
          paddingX: "32px",
          fontSize: "18px",
          iconSize: "24px",
          gap: "10px"
        }
      },
      
      // Loading state
      loadingState: {
        spinner: {
          animation: "spin 1s linear infinite",
          opacity: 0.8
        },
        
        text: {
          opacity: 0
        }
      },
      
      // Usage examples
      examples: {
        primaryCTA: {
          text: "Start Calculating",
          variant: "primary",
          size: "large",
          icon: "calculator"
        },
        
        secondaryAction: {
          text: "View Sample",
          variant: "secondary",
          size: "medium"
        },
        
        dangerAction: {
          text: "Delete",
          variant: "danger",
          size: "small",
          icon: "trash"
        }
      }
    };

#### 7.2 Button Group & Actions

typescript

    // Button group patterns
    const buttonGroup = {
      // Layout patterns
      layouts: {
        horizontal: {
          display: "flex",
          gap: "12px",
          flexWrap: "wrap"
        },
        
        vertical: {
          display: "flex",
          flexDirection: "column",
          gap: "8px"
        },
        
        attached: {
          display: "flex",
          gap: 0,
          
          firstChild: {
            borderRadius: "8px 0 0 8px"
          },
          
          lastChild: {
            borderRadius: "0 8px 8px 0"
          },
          
          middle: {
            borderRadius: 0,
            borderLeft: "1px solid rgba(0,0,0,0.1)"
          }
        }
      },
      
      // Action patterns
      actionPatterns: {
        formActions: {
          primary: "Submit/Save",
          secondary: "Cancel/Back",
          alignment: "Right aligned",
          gap: "16px"
        },
        
        toolbarActions: {
          layout: "Horizontal group",
          size: "small",
          variant: "ghost for most"
        },
        
        modalActions: {
          primary: "Confirm action",
          secondary: "Cancel",
          danger: "If destructive",
          layout: "Right aligned, reverse order"
        }
      },
      
      // Floating Action Button (FAB)
      fab: {
        base: {
          position: "fixed",
          bottom: "24px",
          right: "24px",
          width: "56px",
          height: "56px",
          borderRadius: "50%",
          boxShadow: "0 4px 16px rgba(0,0,0,0.15)"
        },
        
        mobile: {
          bottom: "76px", // Above bottom nav
          right: "16px"
        },
        
        animation: {
          entrance: "scale(0) to scale(1)",
          hover: "scale(1.1)",
          tap: "scale(0.95)"
        }
      }
    };

### 8\. Form Elements

#### 8.1 Input Fields

typescript

    // Input field component system
    interface InputProps {
      type: 'text' | 'number' | 'email' | 'password' | 'search';
      size: 'small' | 'medium' | 'large';
      state: 'default' | 'hover' | 'focus' | 'disabled' | 'error' | 'success';
      prefix?: ReactNode;
      suffix?: ReactNode;
      label?: string;
      helper?: string;
      error?: string;
    }
    
    const inputComponent = {
      // Base input styles
      base: {
        container: {
          display: "flex",
          flexDirection: "column",
          gap: "8px"
        },
        
        wrapper: {
          position: "relative",
          display: "flex",
          alignItems: "center"
        },
        
        input: {
          width: "100%",
          border: `1px solid ${colorSystem.border.medium}`,
          borderRadius: "8px",
          backgroundColor: colorSystem.neutral[0],
          color: colorSystem.text.primary,
          fontSize: "16px",
          fontFamily: "inherit",
          transition: "all 0.2s ease",
          outline: "none"
        }
      },
      
      // Sizes
      sizes: {
        small: {
          height: "36px",
          padding: "0 12px",
          fontSize: "14px"
        },
        
        medium: {
          height: "44px",
          padding: "0 16px",
          fontSize: "16px"
        },
        
        large: {
          height: "52px",
          padding: "0 20px",
          fontSize: "18px"
        }
      },
      
      // States
      states: {
        default: {
          borderColor: colorSystem.border.medium
        },
        
        hover: {
          borderColor: colorSystem.border.dark
        },
        
        focus: {
          borderColor: colorSystem.brand.primary[500],
          boxShadow: `0 0 0 3px ${colorSystem.brand.primary[100]}`
        },
        
        disabled: {
          backgroundColor: colorSystem.neutral[100],
          color: colorSystem.text.disabled,
          cursor: "not-allowed"
        },
        
        error: {
          borderColor: colorSystem.semantic.error.main,
          
          focus: {
            boxShadow: `0 0 0 3px ${colorSystem.semantic.error.light}`
          }
        },
        
        success: {
          borderColor: colorSystem.semantic.success.main,
          
          focus: {
            boxShadow: `0 0 0 3px ${colorSystem.semantic.success.light}`
          }
        }
      },
      
      // Label styles
      label: {
        fontSize: "14px",
        fontWeight: 500,
        color: colorSystem.text.primary,
        marginBottom: "8px",
        
        required: {
          "&::after": {
            content: '" *"',
            color: colorSystem.semantic.error.main
          }
        }
      },
      
      // Helper/Error text
      helperText: {
        fontSize: "13px",
        marginTop: "6px",
        
        default: {
          color: colorSystem.text.tertiary
        },
        
        error: {
          color: colorSystem.semantic.error.main,
          display: "flex",
          alignItems: "center",
          gap: "4px"
        },
        
        success: {
          color: colorSystem.semantic.success.dark
        }
      },
      
      // Special input types
      specialTypes: {
        number: {
          appearance: "textfield", // Remove spinners
          
          customControls: {
            position: "absolute",
            right: "4px",
            display: "flex",
            flexDirection: "column",
            gap: "2px"
          }
        },
        
        search: {
          paddingLeft: "40px",
          
          icon: {
            position: "absolute",
            left: "12px",
            color: colorSystem.text.tertiary
          },
          
          clearButton: {
            position: "absolute",
            right: "12px",
            cursor: "pointer"
          }
        },
        
        password: {
          paddingRight: "48px",
          
          toggleButton: {
            position: "absolute",
            right: "12px",
            cursor: "pointer"
          }
        }
      },
      
      // Prefix/Suffix addons
      addons: {
        prefix: {
          position: "absolute",
          left: "16px",
          color: colorSystem.text.secondary,
          
          withPrefix: {
            paddingLeft: "44px"
          }
        },
        
        suffix: {
          position: "absolute",
          right: "16px",
          color: colorSystem.text.secondary,
          
          withSuffix: {
            paddingRight: "60px"
          }
        }
      }
    };

#### 8.2 Select & Dropdown

typescript

    // Select/Dropdown component
    const selectComponent = {
      // Base select
      trigger: {
        ...inputComponent.base.input,
        cursor: "pointer",
        paddingRight: "40px",
        
        chevron: {
          position: "absolute",
          right: "16px",
          transition: "transform 0.2s ease",
          
          open: {
            transform: "rotate(180deg)"
          }
        }
      },
      
      // Dropdown panel
      dropdown: {
        position: "absolute",
        top: "calc(100% + 4px)",
        left: 0,
        right: 0,
        maxHeight: "320px",
        backgroundColor: colorSystem.neutral[0],
        border: `1px solid ${colorSystem.border.light}`,
        borderRadius: "8px",
        boxShadow: "0 4px 24px rgba(0,0,0,0.08)",
        zIndex: 1000,
        overflow: "auto"
      },
      
      // Options
      option: {
        padding: "12px 16px",
        cursor: "pointer",
        transition: "background 0.2s ease",
        
        hover: {
          backgroundColor: colorSystem.neutral[50]
        },
        
        selected: {
          backgroundColor: colorSystem.brand.primary[50],
          color: colorSystem.brand.primary[700],
          fontWeight: 500
        },
        
        disabled: {
          color: colorSystem.text.disabled,
          cursor: "not-allowed"
        }
      },
      
      // Option groups
      optionGroup: {
        label: {
          padding: "8px 16px",
          fontSize: "12px",
          fontWeight: 600,
          color: colorSystem.text.secondary,
          textTransform: "uppercase",
          letterSpacing: "0.05em"
        }
      },
      
      // Multi-select
      multiSelect: {
        tags: {
          display: "flex",
          flexWrap: "wrap",
          gap: "6px",
          padding: "6px"
        },
        
        tag: {
          display: "inline-flex",
          alignItems: "center",
          gap: "4px",
          padding: "4px 8px",
          backgroundColor: colorSystem.brand.primary[100],
          color: colorSystem.brand.primary[700],
          borderRadius: "4px",
          fontSize: "13px",
          
          removeButton: {
            cursor: "pointer",
            opacity: 0.7,
            
            hover: {
              opacity: 1
            }
          }
        }
      },
      
      // Search in dropdown
      searchable: {
        searchInput: {
          padding: "12px 16px",
          borderBottom: `1px solid ${colorSystem.border.light}`,
          
          input: {
            width: "100%",
            border: "none",
            outline: "none",
            fontSize: "14px"
          }
        },
        
        noResults: {
          padding: "24px",
          textAlign: "center",
          color: colorSystem.text.tertiary
        }
      }
    };

#### 8.3 Checkbox & Radio

typescript

    // Checkbox and Radio components
    const checkboxRadioComponents = {
      // Base styles
      base: {
        container: {
          display: "flex",
          alignItems: "center",
          gap: "12px",
          cursor: "pointer",
          userSelect: "none"
        },
        
        input: {
          position: "absolute",
          opacity: 0,
          width: 0,
          height: 0
        },
        
        control: {
          width: "20px",
          height: "20px",
          border: `2px solid ${colorSystem.border.dark}`,
          backgroundColor: colorSystem.neutral[0],
          transition: "all 0.2s ease",
          flexShrink: 0
        },
        
        label: {
          fontSize: "16px",
          color: colorSystem.text.primary,
          lineHeight: "24px"
        }
      },
      
      // Checkbox specific
      checkbox: {
        control: {
          borderRadius: "4px"
        },
        
        checked: {
          backgroundColor: colorSystem.brand.primary[500],
          borderColor: colorSystem.brand.primary[500],
          
          icon: {
            color: colorSystem.neutral[0],
            width: "12px",
            height: "12px"
          }
        },
        
        indeterminate: {
          backgroundColor: colorSystem.brand.primary[500],
          borderColor: colorSystem.brand.primary[500],
          
          icon: {
            width: "10px",
            height: "2px",
            backgroundColor: colorSystem.neutral[0]
          }
        }
      },
      
      // Radio specific
      radio: {
        control: {
          borderRadius: "50%"
        },
        
        checked: {
          borderColor: colorSystem.brand.primary[500],
          
          dot: {
            width: "8px",
            height: "8px",
            borderRadius: "50%",
            backgroundColor: colorSystem.brand.primary[500]
          }
        }
      },
      
      // States
      states: {
        hover: {
          control: {
            borderColor: colorSystem.brand.primary[400]
          }
        },
        
        focus: {
          control: {
            boxShadow: `0 0 0 3px ${colorSystem.brand.primary[100]}`
          }
        },
        
        disabled: {
          container: {
            cursor: "not-allowed"
          },
          
          control: {
            backgroundColor: colorSystem.neutral[100],
            borderColor: colorSystem.border.light
          },
          
          label: {
            color: colorSystem.text.disabled
          }
        },
        
        error: {
          control: {
            borderColor: colorSystem.semantic.error.main
          },
          
          label: {
            color: colorSystem.semantic.error.main
          }
        }
      },
      
      // Group layouts
      group: {
        vertical: {
          display: "flex",
          flexDirection: "column",
          gap: "12px"
        },
        
        horizontal: {
          display: "flex",
          flexWrap: "wrap",
          gap: "24px"
        },
        
        grid: {
          display: "grid",
          gridTemplateColumns: "repeat(auto-fit, minmax(200px, 1fr))",
          gap: "16px"
        }
      }
    };

#### 8.4 Toggle Switch

typescript

    // Toggle switch component
    const toggleSwitch = {
      // Base structure
      base: {
        container: {
          display: "flex",
          alignItems: "center",
          gap: "12px"
        },
        
        switch: {
          position: "relative",
          width: "44px",
          height: "24px",
          backgroundColor: colorSystem.neutral[300],
          borderRadius: "12px",
          cursor: "pointer",
          transition: "background-color 0.3s ease"
        },
        
        thumb: {
          position: "absolute",
          top: "2px",
          left: "2px",
          width: "20px",
          height: "20px",
          backgroundColor: colorSystem.neutral[0],
          borderRadius: "50%",
          boxShadow: "0 2px 4px rgba(0,0,0,0.15)",
          transition: "transform 0.3s ease"
        }
      },
      
      // States
      states: {
        checked: {
          switch: {
            backgroundColor: colorSystem.brand.primary[500]
          },
          
          thumb: {
            transform: "translateX(20px)"
          }
        },
        
        disabled: {
          switch: {
            backgroundColor: colorSystem.neutral[200],
            cursor: "not-allowed"
          },
          
          thumb: {
            backgroundColor: colorSystem.neutral[100]
          }
        }
      },
      
      // Sizes
      sizes: {
        small: {
          switch: { width: "36px", height: "20px" },
          thumb: { width: "16px", height: "16px" }
        },
        
        large: {
          switch: { width: "52px", height: "28px" },
          thumb: { width: "24px", height: "24px" }
        }
      }
    };

### 9\. Navigation Components

#### 9.1 Navigation Bar

typescript

    // Navigation bar component
    const navigationBar = {
      // Desktop navigation
      desktop: {
        container: {
          height: "64px",
          backgroundColor: colorSystem.neutral[0],
          borderBottom: `1px solid ${colorSystem.border.light}`,
          position: "sticky",
          top: 0,
          zIndex: 100
        },
        
        content: {
          maxWidth: "1200px",
          margin: "0 auto",
          padding: "0 24px",
          height: "100%",
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between"
        },
        
        logo: {
          display: "flex",
          alignItems: "center",
          gap: "12px",
          fontSize: "20px",
          fontWeight: 600,
          color: colorSystem.text.primary
        },
        
        menu: {
          display: "flex",
          alignItems: "center",
          gap: "8px"
        },
        
        menuItem: {
          padding: "8px 16px",
          borderRadius: "8px",
          color: colorSystem.text.secondary,
          fontWeight: 500,
          transition: "all 0.2s ease",
          
          hover: {
            backgroundColor: colorSystem.neutral[50],
            color: colorSystem.text.primary
          },
          
          active: {
            backgroundColor: colorSystem.brand.primary[50],
            color: colorSystem.brand.primary[600]
          }
        },
        
        actions: {
          display: "flex",
          alignItems: "center",
          gap: "16px"
        }
      },
      
      // Mobile navigation
      mobile: {
        header: {
          height: "56px",
          backgroundColor: colorSystem.neutral[0],
          borderBottom: `1px solid ${colorSystem.border.light}`,
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          padding: "0 16px"
        },
        
        menuButton: {
          width: "40px",
          height: "40px",
          display: "flex",
          alignItems: "center",
          justifyContent: "center"
        },
        
        drawer: {
          position: "fixed",
          top: 0,
          left: 0,
          bottom: 0,
          width: "280px",
          backgroundColor: colorSystem.neutral[0],
          boxShadow: "4px 0 24px rgba(0,0,0,0.1)",
          transform: "translateX(-100%)",
          transition: "transform 0.3s ease",
          
          open: {
            transform: "translateX(0)"
          }
        },
        
        overlay: {
          position: "fixed",
          inset: 0,
          backgroundColor: "rgba(0,0,0,0.5)",
          opacity: 0,
          visibility: "hidden",
          transition: "all 0.3s ease",
          
          open: {
            opacity: 1,
            visibility: "visible"
          }
        }
      },
      
      // Bottom navigation (mobile)
      bottomNav: {
        container: {
          position: "fixed",
          bottom: 0,
          left: 0,
          right: 0,
          height: "56px",
          backgroundColor: colorSystem.neutral[0],
          borderTop: `1px solid ${colorSystem.border.light}`,
          display: "flex",
          alignItems: "center",
          justifyContent: "space-around"
        },
        
        item: {
          flex: 1,
          height: "100%",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
          gap: "4px",
          color: colorSystem.text.tertiary,
          
          active: {
            color: colorSystem.brand.primary[500]
          },
          
          icon: {
            fontSize: "20px"
          },
          
          label: {
            fontSize: "11px",
            fontWeight: 500
          }
        }
      }
    };

#### 9.2 Breadcrumbs

typescript

    // Breadcrumb component
    const breadcrumbs = {
      container: {
        display: "flex",
        alignItems: "center",
        gap: "8px",
        fontSize: "14px",
        color: colorSystem.text.secondary
      },
      
      item: {
        display: "flex",
        alignItems: "center",
        gap: "8px"
      },
      
      link: {
        color: colorSystem.text.secondary,
        textDecoration: "none",
        transition: "color 0.2s ease",
        
        hover: {
          color: colorSystem.brand.primary[500]
        }
      },
      
      current: {
        color: colorSystem.text.primary,
        fontWeight: 500
      },
      
      separator: {
        color: colorSystem.text.tertiary,
        fontSize: "16px"
      },
      
      // Mobile treatment
      mobile: {
        showOnly: "last 2 items",
        prefix: "..."
      }
    };

#### 9.3 Tabs

typescript

    // Tab component system
    const tabComponent = {
      // Tab list container
      container: {
        borderBottom: `1px solid ${colorSystem.border.light}`,
        display: "flex",
        gap: "4px",
        overflowX: "auto",
        scrollbarWidth: "none"
      },
      
      // Individual tab
      tab: {
        padding: "12px 24px",
        color: colorSystem.text.secondary,
        fontWeight: 500,
        whiteSpace: "nowrap",
        backgroundColor: "transparent",
        border: "none",
        borderBottom: "2px solid transparent",
        cursor: "pointer",
        transition: "all 0.2s ease",
        
        hover: {
          color: colorSystem.text.primary,
          backgroundColor: colorSystem.neutral[50]
        },
        
        active: {
          color: colorSystem.brand.primary[600],
          borderBottomColor: colorSystem.brand.primary[500]
        },
        
        disabled: {
          color: colorSystem.text.disabled,
          cursor: "not-allowed"
        }
      },
      
      // Tab panel
      panel: {
        padding: "24px 0",
        
        animation: {
          enter: "fadeIn 0.3s ease",
          exit: "fadeOut 0.2s ease"
        }
      },
      
      // Tab variants
      variants: {
        pills: {
          container: {
            borderBottom: "none",
            backgroundColor: colorSystem.neutral[100],
            padding: "4px",
            borderRadius: "8px"
          },
          
          tab: {
            borderRadius: "6px",
            border: "none",
            
            active: {
              backgroundColor: colorSystem.neutral[0],
              boxShadow: "0 1px 3px rgba(0,0,0,0.08)"
            }
          }
        },
        
        vertical: {
          container: {
            flexDirection: "column",
            borderBottom: "none",
            borderRight: `1px solid ${colorSystem.border.light}`
          },
          
          tab: {
            textAlign: "left",
            borderBottom: "none",
            borderLeft: "2px solid transparent",
            
            active: {
              borderLeftColor: colorSystem.brand.primary[500],
              backgroundColor: colorSystem.brand.primary[50]
            }
          }
        }
      }
    };

### 10\. Cards & Containers

#### 10.1 Card Component

typescript

    // Card component system
    const cardComponent = {
      // Base card
      base: {
        backgroundColor: colorSystem.neutral[0],
        borderRadius: "12px",
        boxShadow: "0 1px 3px rgba(0,0,0,0.08)",
        overflow: "hidden",
        transition: "all 0.2s ease"
      },
      
      // Card sections
      sections: {
        header: {
          padding: "20px 24px",
          borderBottom: `1px solid ${colorSystem.border.light}`,
          
          title: {
            fontSize: "18px",
            fontWeight: 600,
            color: colorSystem.text.primary
          },
          
          subtitle: {
            fontSize: "14px",
            color: colorSystem.text.secondary,
            marginTop: "4px"
          },
          
          actions: {
            position: "absolute",
            top: "20px",
            right: "24px"
          }
        },
        
        body: {
          padding: "24px"
        },
        
        footer: {
          padding: "16px 24px",
          borderTop: `1px solid ${colorSystem.border.light}`,
          backgroundColor: colorSystem.neutral[50]
        }
      },
      
      // Card variants
      variants: {
        elevated: {
          boxShadow: "0 4px 12px rgba(0,0,0,0.08)",
          
          hover: {
            boxShadow: "0 8px 24px rgba(0,0,0,0.12)",
            transform: "translateY(-2px)"
          }
        },
        
        outlined: {
          boxShadow: "none",
          border: `1px solid ${colorSystem.border.medium}`
        },
        
        filled: {
          backgroundColor: colorSystem.neutral[50],
          boxShadow: "none"
        },
        
        interactive: {
          cursor: "pointer",
          
          hover: {
            boxShadow: "0 4px 16px rgba(0,0,0,0.10)",
            transform: "translateY(-1px)"
          },
          
          active: {
            transform: "translateY(0)"
          }
        }
      },
      
      // Special card types
      specialTypes: {
        metric: {
          body: {
            display: "flex",
            flexDirection: "column",
            gap: "8px"
          },
          
          value: {
            fontSize: "32px",
            fontWeight: 600,
            color: colorSystem.text.primary,
            fontFamily: fontSystem.secondary.family
          },
          
          label: {
            fontSize: "14px",
            color: colorSystem.text.secondary
          },
          
          change: {
            fontSize: "14px",
            fontWeight: 500,
            
            positive: {
              color: colorSystem.semantic.success.main
            },
            
            negative: {
              color: colorSystem.semantic.error.main
            }
          }
        },
        
        feature: {
          icon: {
            width: "48px",
            height: "48px",
            backgroundColor: colorSystem.brand.primary[100],
            color: colorSystem.brand.primary[600],
            borderRadius: "12px",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            marginBottom: "16px"
          }
        }
      }
    };

#### 10.2 Container Patterns

typescript

    // Container component patterns
    const containerPatterns = {
      // Page container
      pageContainer: {
        maxWidth: "1200px",
        margin: "0 auto",
        padding: {
          mobile: "16px",
          tablet: "24px",
          desktop: "32px"
        }
      },
      
      // Section container
      section: {
        padding: {
          mobile: "48px 0",
          tablet: "64px 0",
          desktop: "80px 0"
        },
        
        variants: {
          compact: {
            padding: "32px 0"
          },
          
          spacious: {
            padding: "120px 0"
          },
          
          fullBleed: {
            padding: 0,
            maxWidth: "100%"
          }
        }
      },
      
      // Content wrapper
      contentWrapper: {
        default: {
          maxWidth: "720px",
          margin: "0 auto"
        },
        
        wide: {
          maxWidth: "960px"
        },
        
        narrow: {
          maxWidth: "480px"
        }
      },
      
      // Grid container
      gridContainer: {
        display: "grid",
        gap: "24px",
        
        columns: {
          mobile: "1fr",
          tablet: "repeat(2, 1fr)",
          desktop: "repeat(3, 1fr)"
        },
        
        responsive: {
          autoFit: "repeat(auto-fit, minmax(300px, 1fr))",
          autoFill: "repeat(auto-fill, minmax(250px, 1fr))"
        }
      },
      
      // Panel container
      panel: {
        backgroundColor: colorSystem.neutral[50],
        borderRadius: "12px",
        padding: "24px",
        
        variants: {
          raised: {
            backgroundColor: colorSystem.neutral[0],
            boxShadow: "0 2px 8px rgba(0,0,0,0.06)"
          },
          
          inset: {
            backgroundColor: colorSystem.neutral[100],
            boxShadow: "inset 0 2px 4px rgba(0,0,0,0.06)"
          },
          
          bordered: {
            backgroundColor: "transparent",
            border: `1px solid ${colorSystem.border.light}`
          }
        }
      }
    };

### 11\. Data Display Components

#### 11.1 Tables

typescript

    // Table component system
    const tableComponent = {
      // Base table
      wrapper: {
        overflowX: "auto",
        borderRadius: "12px",
        border: `1px solid ${colorSystem.border.light}`
      },
      
      table: {
        width: "100%",
        borderCollapse: "collapse",
        fontSize: "14px"
      },
      
      // Table header
      header: {
        backgroundColor: colorSystem.neutral[50],
        borderBottom: `2px solid ${colorSystem.border.light}`
      },
      
      headerCell: {
        padding: "12px 16px",
        textAlign: "left",
        fontWeight: 600,
        color: colorSystem.text.primary,
        whiteSpace: "nowrap",
        
        sortable: {
          cursor: "pointer",
          userSelect: "none",
          
          hover: {
            backgroundColor: colorSystem.neutral[100]
          },
          
          icon: {
            marginLeft: "8px",
            opacity: 0.5,
            
            active: {
              opacity: 1
            }
          }
        }
      },
      
      // Table body
      row: {
        borderBottom: `1px solid ${colorSystem.border.light}`,
        
        hover: {
          backgroundColor: colorSystem.neutral[50]
        },
        
        selected: {
          backgroundColor: colorSystem.brand.primary[50]
        },
        
        clickable: {
          cursor: "pointer"
        }
      },
      
      cell: {
        padding: "16px",
        color: colorSystem.text.primary,
        
        variants: {
          numeric: {
            textAlign: "right",
            fontFamily: fontSystem.secondary.family
          },
          
          action: {
            width: "48px",
            textAlign: "center"
          },
          
          truncate: {
            maxWidth: "200px",
            overflow: "hidden",
            textOverflow: "ellipsis",
            whiteSpace: "nowrap"
          }
        }
      },
      
      // Empty state
      emptyState: {
        padding: "48px",
        textAlign: "center",
        color: colorSystem.text.tertiary
      },
      
      // Mobile responsive
      mobileCard: {
        display: "block",
        padding: "16px",
        
        label: {
          fontSize: "12px",
          color: colorSystem.text.secondary,
          marginBottom: "4px"
        },
        
        value: {
          fontSize: "14px",
          color: colorSystem.text.primary
        }
      }
    };

#### 11.2 Lists

typescript

    // List component patterns
    const listComponent = {
      // Base list
      container: {
        listStyle: "none",
        padding: 0,
        margin: 0
      },
      
      // List item
      item: {
        padding: "16px",
        borderBottom: `1px solid ${colorSystem.border.light}`,
        display: "flex",
        alignItems: "center",
        gap: "16px",
        
        lastChild: {
          borderBottom: "none"
        },
        
        hover: {
          backgroundColor: colorSystem.neutral[50]
        },
        
        active: {
          backgroundColor: colorSystem.brand.primary[50]
        }
      },
      
      // List item content
      content: {
        flex: 1,
        minWidth: 0,
        
        primary: {
          fontSize: "16px",
          fontWeight: 500,
          color: colorSystem.text.primary,
          marginBottom: "4px"
        },
        
        secondary: {
          fontSize: "14px",
          color: colorSystem.text.secondary,
          lineHeight: "20px"
        }
      },
      
      // List item accessories
      accessories: {
        avatar: {
          width: "40px",
          height: "40px",
          borderRadius: "50%",
          flexShrink: 0
        },
        
        icon: {
          width: "24px",
          height: "24px",
          color: colorSystem.text.tertiary,
          flexShrink: 0
        },
        
        action: {
          marginLeft: "auto",
          flexShrink: 0
        },
        
        meta: {
          fontSize: "14px",
          color: colorSystem.text.tertiary,
          whiteSpace: "nowrap"
        }
      },
      
      // List variants
      variants: {
        compact: {
          item: {
            padding: "12px"
          }
        },
        
        divided: {
          item: {
            borderBottom: `1px solid ${colorSystem.border.medium}`
          }
        },
        
        card: {
          item: {
            backgroundColor: colorSystem.neutral[0],
            borderRadius: "8px",
            border: `1px solid ${colorSystem.border.light}`,
            marginBottom: "8px",
            boxShadow: "0 1px 3px rgba(0,0,0,0.05)"
          }
        }
      }
    };

#### 11.3 Stats & Metrics

typescript

    // Stats display components
    const statsComponents = {
      // Stat card
      statCard: {
        container: {
          padding: "24px",
          backgroundColor: colorSystem.neutral[0],
          borderRadius: "12px",
          border: `1px solid ${colorSystem.border.light}`
        },
        
        label: {
          fontSize: "14px",
          fontWeight: 500,
          color: colorSystem.text.secondary,
          marginBottom: "8px",
          textTransform: "uppercase",
          letterSpacing: "0.05em"
        },
        
        value: {
          fontSize: "32px",
          fontWeight: 600,
          color: colorSystem.text.primary,
          lineHeight: "40px",
          fontFamily: fontSystem.secondary.family
        },
        
        change: {
          display: "flex",
          alignItems: "center",
          gap: "4px",
          marginTop: "8px",
          fontSize: "14px",
          
          positive: {
            color: colorSystem.semantic.success.main
          },
          
          negative: {
            color: colorSystem.semantic.error.main
          },
          
          neutral: {
            color: colorSystem.text.secondary
          }
        },
        
        sparkline: {
          height: "40px",
          marginTop: "16px"
        }
      },
      
      // Progress indicators
      progress: {
        linear: {
          container: {
            height: "8px",
            backgroundColor: colorSystem.neutral[200],
            borderRadius: "4px",
            overflow: "hidden"
          },
          
          bar: {
            height: "100%",
            backgroundColor: colorSystem.brand.primary[500],
            borderRadius: "4px",
            transition: "width 0.3s ease"
          },
          
          withLabel: {
            wrapper: {
              marginBottom: "8px",
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center"
            },
            
            label: {
              fontSize: "14px",
              fontWeight: 500,
              color: colorSystem.text.primary
            },
            
            value: {
              fontSize: "14px",
              color: colorSystem.text.secondary
            }
          }
        },
        
        circular: {
          container: {
            position: "relative",
            width: "120px",
            height: "120px"
          },
          
          svg: {
            transform: "rotate(-90deg)"
          },
          
          background: {
            stroke: colorSystem.neutral[200],
            strokeWidth: 8,
            fill: "none"
          },
          
          progress: {
            stroke: colorSystem.brand.primary[500],
            strokeWidth: 8,
            fill: "none",
            strokeLinecap: "round",
            transition: "stroke-dashoffset 0.3s ease"
          },
          
          label: {
            position: "absolute",
            inset: 0,
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            fontSize: "24px",
            fontWeight: 600
          }
        }
      }
    };

### 12\. Feedback & Messaging

#### 12.1 Alerts & Notifications

typescript

    // Alert component system
    const alertComponent = {
      // Base alert
      container: {
        padding: "16px",
        borderRadius: "8px",
        display: "flex",
        gap: "12px",
        fontSize: "14px",
        lineHeight: "20px"
      },
      
      // Alert variants
      variants: {
        info: {
          backgroundColor: colorSystem.semantic.info.light,
          color: colorSystem.semantic.info.dark,
          borderLeft: `4px solid ${colorSystem.semantic.info.main}`
        },
        
        success: {
          backgroundColor: colorSystem.semantic.success.light,
          color: colorSystem.semantic.success.dark,
          borderLeft: `4px solid ${colorSystem.semantic.success.main}`
        },
        
        warning: {
          backgroundColor: colorSystem.semantic.warning.light,
          color: colorSystem.semantic.warning.dark,
          borderLeft: `4px solid ${colorSystem.semantic.warning.main}`
        },
        
        error: {
          backgroundColor: colorSystem.semantic.error.light,
          color: colorSystem.semantic.error.dark,
          borderLeft: `4px solid ${colorSystem.semantic.error.main}`
        }
      },
      
      // Alert anatomy
      icon: {
        flexShrink: 0,
        width: "20px",
        height: "20px"
      },
      
      content: {
        flex: 1,
        
        title: {
          fontWeight: 600,
          marginBottom: "4px"
        },
        
        description: {
          color: "inherit",
          opacity: 0.9
        }
      },
      
      actions: {
        marginTop: "12px",
        display: "flex",
        gap: "12px"
      },
      
      dismiss: {
        marginLeft: "auto",
        cursor: "pointer",
        opacity: 0.7,
        
        hover: {
          opacity: 1
        }
      }
    };

#### 12.2 Toast Notifications

typescript

    // Toast notification system
    const toastComponent = {
      // Toast container
      container: {
        position: "fixed",
        top: "24px",
        right: "24px",
        zIndex: 9999,
        display: "flex",
        flexDirection: "column",
        gap: "12px",
        maxWidth: "400px"
      },
      
      // Individual toast
      toast: {
        backgroundColor: colorSystem.neutral[900],
        color: colorSystem.neutral[0],
        padding: "16px",
        borderRadius: "8px",
        boxShadow: "0 4px 24px rgba(0,0,0,0.2)",
        display: "flex",
        alignItems: "center",
        gap: "12px",
        minWidth: "300px",
        
        animation: {
          enter: "slideInRight 0.3s ease",
          exit: "slideOutRight 0.2s ease"
        }
      },
      
      // Toast variants
      variants: {
        success: {
          backgroundColor: colorSystem.semantic.success.main
        },
        
        error: {
          backgroundColor: colorSystem.semantic.error.main
        },
        
        warning: {
          backgroundColor: colorSystem.semantic.warning.main,
          color: colorSystem.neutral[900]
        },
        
        info: {
          backgroundColor: colorSystem.semantic.info.main
        }
      },
      
      // Progress bar
      progressBar: {
        position: "absolute",
        bottom: 0,
        left: 0,
        height: "3px",
        backgroundColor: "rgba(255,255,255,0.3)",
        
        animation: "shrink 5s linear"
      },
      
      // Mobile adjustments
      mobile: {
        container: {
          top: "auto",
          bottom: "76px", // Above bottom nav
          left: "16px",
          right: "16px",
          maxWidth: "none"
        },
        
        toast: {
          width: "100%"
        }
      }
    };

#### 12.3 Loading States

typescript

    // Loading state patterns
    const loadingStates = {
      // Spinner
      spinner: {
        container: {
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          padding: "32px"
        },
        
        spinner: {
          width: "32px",
          height: "32px",
          border: `3px solid ${colorSystem.neutral[200]}`,
          borderTop: `3px solid ${colorSystem.brand.primary[500]}`,
          borderRadius: "50%",
          animation: "spin 1s linear infinite"
        },
        
        sizes: {
          small: { width: "16px", height: "16px", borderWidth: "2px" },
          medium: { width: "32px", height: "32px", borderWidth: "3px" },
          large: { width: "48px", height: "48px", borderWidth: "4px" }
        }
      },
      
      // Skeleton screens
      skeleton: {
        base: {
          backgroundColor: colorSystem.neutral[200],
          borderRadius: "4px",
          position: "relative",
          overflow: "hidden",
          
          "&::after": {
            content: '""',
            position: "absolute",
            top: 0,
            right: 0,
            bottom: 0,
            left: 0,
            background: `linear-gradient(
              90deg,
              transparent 0%,
              ${colorSystem.neutral[100]} 50%,
              transparent 100%
            )`,
            animation: "shimmer 1.5s infinite"
          }
        },
        
        variants: {
          text: {
            height: "16px",
            marginBottom: "8px",
            
            lastChild: {
              width: "60%"
            }
          },
          
          title: {
            height: "24px",
            width: "40%",
            marginBottom: "16px"
          },
          
          avatar: {
            width: "40px",
            height: "40px",
            borderRadius: "50%"
          },
          
          image: {
            width: "100%",
            height: "200px",
            borderRadius: "8px"
          }
        }
      },
      
      // Progress indicators
      progressIndicators: {
        linear: {
          indeterminate: {
            height: "4px",
            backgroundColor: colorSystem.neutral[200],
            overflow: "hidden",
            
            bar: {
              height: "100%",
              backgroundColor: colorSystem.brand.primary[500],
              animation: "indeterminate 1.5s infinite"
            }
          }
        },
        
        steps: {
          container: {
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between"
          },
          
          step: {
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            gap: "8px"
          },
          
          circle: {
            width: "32px",
            height: "32px",
            borderRadius: "50%",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            border: `2px solid ${colorSystem.border.medium}`,
            backgroundColor: colorSystem.neutral[0],
            
            active: {
              borderColor: colorSystem.brand.primary[500],
              backgroundColor: colorSystem.brand.primary[500],
              color: colorSystem.neutral[0]
            },
            
            completed: {
              borderColor: colorSystem.semantic.success.main,
              backgroundColor: colorSystem.semantic.success.main,
              color: colorSystem.neutral[0]
            }
          },
          
          connector: {
            height: "2px",
            flex: 1,
            backgroundColor: colorSystem.border.medium,
            
            completed: {
              backgroundColor: colorSystem.semantic.success.main
            }
          }
        }
      }
    };

* * *

Part 3: Complex Components
--------------------------

### 13\. Calculator Interface Components

#### 13.1 Calculator Input Panel

typescript

    // Calculator input components
    const calculatorInputComponents = {
      // Main input panel
      panel: {
        container: {
          backgroundColor: colorSystem.neutral[0],
          borderRadius: "16px",
          boxShadow: "0 2px 12px rgba(0,0,0,0.08)",
          padding: "32px",
          
          mobile: {
            padding: "20px",
            borderRadius: "12px"
          }
        },
        
        header: {
          marginBottom: "32px",
          
          title: {
            fontSize: "24px",
            fontWeight: 600,
            color: colorSystem.text.primary,
            marginBottom: "8px"
          },
          
          subtitle: {
            fontSize: "16px",
            color: colorSystem.text.secondary
          }
        },
        
        sections: {
          gap: "32px",
          
          divider: {
            height: "1px",
            backgroundColor: colorSystem.border.light,
            margin: "32px 0"
          }
        }
      },
      
      // Area input component
      areaInput: {
        container: {
          display: "grid",
          gridTemplateColumns: "1fr 1fr",
          gap: "16px",
          
          mobile: {
            gridTemplateColumns: "1fr"
          }
        },
        
        field: {
          label: {
            display: "flex",
            alignItems: "center",
            gap: "8px",
            marginBottom: "8px",
            fontSize: "14px",
            fontWeight: 500,
            color: colorSystem.text.primary
          },
          
          input: {
            ...inputComponent.base.input,
            fontSize: "20px",
            fontWeight: 500,
            fontFamily: fontSystem.secondary.family,
            textAlign: "right",
            paddingRight: "60px"
          },
          
          unit: {
            position: "absolute",
            right: "16px",
            top: "50%",
            transform: "translateY(-50%)",
            fontSize: "16px",
            color: colorSystem.text.secondary
          },
          
          helper: {
            marginTop: "8px",
            fontSize: "13px",
            color: colorSystem.text.tertiary
          }
        },
        
        quickSelect: {
          label: {
            fontSize: "13px",
            color: colorSystem.text.secondary,
            marginBottom: "8px"
          },
          
          options: {
            display: "flex",
            gap: "8px",
            flexWrap: "wrap"
          },
          
          chip: {
            padding: "6px 12px",
            borderRadius: "16px",
            border: `1px solid ${colorSystem.border.medium}`,
            backgroundColor: colorSystem.neutral[0],
            fontSize: "13px",
            cursor: "pointer",
            transition: "all 0.2s ease",
            
            hover: {
              borderColor: colorSystem.brand.primary[300],
              backgroundColor: colorSystem.brand.primary[50]
            },
            
            selected: {
              borderColor: colorSystem.brand.primary[500],
              backgroundColor: colorSystem.brand.primary[100],
              color: colorSystem.brand.primary[700]
            }
          }
        }
      },
      
      // Floor configuration
      floorConfig: {
        container: {
          padding: "20px",
          backgroundColor: colorSystem.neutral[50],
          borderRadius: "12px"
        },
        
        floorItem: {
          display: "flex",
          alignItems: "center",
          gap: "16px",
          padding: "12px",
          backgroundColor: colorSystem.neutral[0],
          borderRadius: "8px",
          marginBottom: "12px",
          
          number: {
            width: "32px",
            height: "32px",
            borderRadius: "50%",
            backgroundColor: colorSystem.brand.primary[100],
            color: colorSystem.brand.primary[700],
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            fontSize: "14px",
            fontWeight: 600
          },
          
          input: {
            flex: 1,
            display: "flex",
            alignItems: "center",
            gap: "12px"
          },
          
          remove: {
            padding: "8px",
            cursor: "pointer",
            color: colorSystem.text.tertiary,
            
            hover: {
              color: colorSystem.semantic.error.main
            }
          }
        },
        
        addFloor: {
          width: "100%",
          padding: "12px",
          border: `2px dashed ${colorSystem.border.medium}`,
          borderRadius: "8px",
          backgroundColor: "transparent",
          color: colorSystem.brand.primary[500],
          cursor: "pointer",
          transition: "all 0.2s ease",
          
          hover: {
            borderColor: colorSystem.brand.primary[300],
            backgroundColor: colorSystem.brand.primary[50]
          }
        }
      },
      
      // Quality tier selector
      qualitySelector: {
        container: {
          display: "grid",
          gridTemplateColumns: "repeat(3, 1fr)",
          gap: "16px",
          
          mobile: {
            gridTemplateColumns: "1fr"
          }
        },
        
        option: {
          padding: "20px",
          borderRadius: "12px",
          border: `2px solid ${colorSystem.border.light}`,
          cursor: "pointer",
          transition: "all 0.2s ease",
          position: "relative",
          overflow: "hidden",
          
          hover: {
            borderColor: colorSystem.border.medium,
            transform: "translateY(-2px)",
            boxShadow: "0 4px 16px rgba(0,0,0,0.08)"
          },
          
          selected: {
            borderColor: colorSystem.brand.primary[500],
            backgroundColor: colorSystem.brand.primary[50],
            
            badge: {
              position: "absolute",
              top: "12px",
              right: "12px",
              width: "24px",
              height: "24px",
              borderRadius: "50%",
              backgroundColor: colorSystem.brand.primary[500],
              color: colorSystem.neutral[0],
              display: "flex",
              alignItems: "center",
              justifyContent: "center"
            }
          }
        },
        
        content: {
          icon: {
            width: "48px",
            height: "48px",
            marginBottom: "16px",
            borderRadius: "12px",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            fontSize: "24px",
            
            smart: {
              backgroundColor: colorSystem.special.qualityTiers.smart + "20",
              color: colorSystem.special.qualityTiers.smart
            },
            
            premium: {
              backgroundColor: colorSystem.special.qualityTiers.premium + "20",
              color: colorSystem.special.qualityTiers.premium
            },
            
            luxury: {
              backgroundColor: colorSystem.special.qualityTiers.luxury + "20",
              color: colorSystem.special.qualityTiers.luxury
            }
          },
          
          title: {
            fontSize: "18px",
            fontWeight: 600,
            color: colorSystem.text.primary,
            marginBottom: "8px"
          },
          
          description: {
            fontSize: "14px",
            color: colorSystem.text.secondary,
            marginBottom: "12px"
          },
          
          priceRange: {
            fontSize: "16px",
            fontWeight: 500,
            color: colorSystem.text.primary,
            fontFamily: fontSystem.secondary.family
          }
        }
      }
    };

#### 13.2 Calculator Output Display

typescript

    // Calculator output components
    const calculatorOutputComponents = {
      // Summary card
      summaryCard: {
        container: {
          backgroundColor: colorSystem.brand.primary[500],
          color: colorSystem.neutral[0],
          borderRadius: "16px",
          padding: "32px",
          position: "relative",
          overflow: "hidden",
          
          backgroundPattern: {
            position: "absolute",
            top: 0,
            right: 0,
            opacity: 0.1,
            transform: "translate(20%, -20%)"
          }
        },
        
        label: {
          fontSize: "16px",
          opacity: 0.9,
          marginBottom: "8px"
        },
        
        totalCost: {
          fontSize: "48px",
          fontWeight: 700,
          fontFamily: fontSystem.secondary.family,
          lineHeight: "56px",
          marginBottom: "16px"
        },
        
        metrics: {
          display: "grid",
          gridTemplateColumns: "repeat(3, 1fr)",
          gap: "24px",
          marginTop: "24px",
          paddingTop: "24px",
          borderTop: "1px solid rgba(255,255,255,0.2)"
        },
        
        metric: {
          label: {
            fontSize: "13px",
            opacity: 0.8,
            marginBottom: "4px"
          },
          
          value: {
            fontSize: "20px",
            fontWeight: 600,
            fontFamily: fontSystem.secondary.family
          }
        }
      },
      
      // Breakdown visualization
      breakdownChart: {
        container: {
          backgroundColor: colorSystem.neutral[0],
          borderRadius: "12px",
          padding: "24px",
          boxShadow: "0 1px 3px rgba(0,0,0,0.08)"
        },
        
        header: {
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          marginBottom: "24px"
        },
        
        chartTypes: {
          pie: {
            height: "300px",
            colors: colorSystem.special.visualization
          },
          
          bar: {
            height: "400px",
            horizontal: true,
            colors: colorSystem.special.visualization
          },
          
          treemap: {
            height: "400px",
            colors: colorSystem.special.visualization
          }
        },
        
        legend: {
          display: "flex",
          flexWrap: "wrap",
          gap: "16px",
          marginTop: "24px"
        },
        
        legendItem: {
          display: "flex",
          alignItems: "center",
          gap: "8px",
          
          dot: {
            width: "12px",
            height: "12px",
            borderRadius: "50%"
          },
          
          label: {
            fontSize: "14px",
            color: colorSystem.text.secondary
          },
          
          value: {
            fontSize: "14px",
            fontWeight: 500,
            color: colorSystem.text.primary,
            marginLeft: "4px"
          }
        }
      },
      
      // Detailed breakdown table
      breakdownTable: {
        container: {
          backgroundColor: colorSystem.neutral[0],
          borderRadius: "12px",
          overflow: "hidden",
          boxShadow: "0 1px 3px rgba(0,0,0,0.08)"
        },
        
        category: {
          header: {
            padding: "16px 24px",
            backgroundColor: colorSystem.neutral[100],
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            cursor: "pointer",
            
            hover: {
              backgroundColor: colorSystem.neutral[200]
            }
          },
          
          title: {
            fontSize: "16px",
            fontWeight: 600,
            color: colorSystem.text.primary
          },
          
          amount: {
            fontSize: "18px",
            fontWeight: 600,
            fontFamily: fontSystem.secondary.family,
            color: colorSystem.text.primary
          },
          
          percentage: {
            fontSize: "14px",
            color: colorSystem.text.secondary,
            marginLeft: "8px"
          },
          
          chevron: {
            transition: "transform 0.2s ease",
            
            open: {
              transform: "rotate(180deg)"
            }
          }
        },
        
        items: {
          container: {
            borderTop: `1px solid ${colorSystem.border.light}`
          },
          
          item: {
            padding: "12px 24px 12px 48px",
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            borderBottom: `1px solid ${colorSystem.border.light}`,
            
            hover: {
              backgroundColor: colorSystem.neutral[50]
            }
          },
          
          name: {
            fontSize: "14px",
            color: colorSystem.text.primary
          },
          
          details: {
            fontSize: "13px",
            color: colorSystem.text.tertiary
          },
          
          amount: {
            fontSize: "14px",
            fontWeight: 500,
            fontFamily: fontSystem.secondary.family,
            color: colorSystem.text.primary
          }
        }
      }
    };

### 14\. Material Selection Components

#### 14.1 Material Browser

typescript

    // Material browsing components
    const materialBrowserComponents = {
      // Filter sidebar
      filterSidebar: {
        container: {
          width: "280px",
          backgroundColor: colorSystem.neutral[50],
          borderRight: `1px solid ${colorSystem.border.light}`,
          height: "100%",
          overflowY: "auto",
          
          mobile: {
            position: "fixed",
            left: 0,
            top: 0,
            bottom: 0,
            transform: "translateX(-100%)",
            transition: "transform 0.3s ease",
            zIndex: 1000,
            
            open: {
              transform: "translateX(0)"
            }
          }
        },
        
        section: {
          padding: "20px",
          borderBottom: `1px solid ${colorSystem.border.light}`
        },
        
        sectionTitle: {
          fontSize: "13px",
          fontWeight: 600,
          textTransform: "uppercase",
          color: colorSystem.text.secondary,
          marginBottom: "12px",
          letterSpacing: "0.05em"
        },
        
        filterGroup: {
          marginBottom: "16px"
        },
        
        filterOption: {
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          padding: "8px 0",
          cursor: "pointer",
          
          label: {
            fontSize: "14px",
            color: colorSystem.text.primary
          },
          
          count: {
            fontSize: "13px",
            color: colorSystem.text.tertiary,
            backgroundColor: colorSystem.neutral[200],
            padding: "2px 8px",
            borderRadius: "12px"
          }
        },
        
        priceRange: {
          slider: {
            marginTop: "24px",
            marginBottom: "16px"
          },
          
          inputs: {
            display: "flex",
            gap: "12px",
            
            input: {
              flex: 1,
              padding: "8px 12px",
              fontSize: "14px",
              borderRadius: "6px",
              border: `1px solid ${colorSystem.border.medium}`
            }
          }
        }
      },
      
      // Material grid
      materialGrid: {
        container: {
          flex: 1,
          padding: "24px"
        },
        
        header: {
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          marginBottom: "24px"
        },
        
        results: {
          fontSize: "14px",
          color: colorSystem.text.secondary
        },
        
        sortSelect: {
          minWidth: "180px"
        },
        
        grid: {
          display: "grid",
          gridTemplateColumns: "repeat(auto-fill, minmax(280px, 1fr))",
          gap: "20px"
        }
      },
      
      // Material card
      materialCard: {
        container: {
          backgroundColor: colorSystem.neutral[0],
          borderRadius: "12px",
          overflow: "hidden",
          border: `1px solid ${colorSystem.border.light}`,
          transition: "all 0.2s ease",
          cursor: "pointer",
          
          hover: {
            boxShadow: "0 4px 16px rgba(0,0,0,0.08)",
            transform: "translateY(-2px)"
          },
          
          selected: {
            borderColor: colorSystem.brand.primary[500],
            boxShadow: `0 0 0 2px ${colorSystem.brand.primary[100]}`
          }
        },
        
        image: {
          width: "100%",
          height: "200px",
          objectFit: "cover",
          backgroundColor: colorSystem.neutral[100]
        },
        
        content: {
          padding: "16px"
        },
        
        brand: {
          fontSize: "12px",
          fontWeight: 600,
          color: colorSystem.text.tertiary,
          textTransform: "uppercase",
          marginBottom: "4px"
        },
        
        name: {
          fontSize: "16px",
          fontWeight: 500,
          color: colorSystem.text.primary,
          marginBottom: "8px",
          lineHeight: "22px"
        },
        
        specs: {
          display: "flex",
          gap: "12px",
          marginBottom: "12px"
        },
        
        spec: {
          fontSize: "13px",
          color: colorSystem.text.secondary,
          display: "flex",
          alignItems: "center",
          gap: "4px"
        },
        
        footer: {
          paddingTop: "12px",
          borderTop: `1px solid ${colorSystem.border.light}`,
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center"
        },
        
        price: {
          fontSize: "18px",
          fontWeight: 600,
          color: colorSystem.text.primary,
          fontFamily: fontSystem.secondary.family
        },
        
        unit: {
          fontSize: "13px",
          color: colorSystem.text.secondary
        },
        
        badge: {
          position: "absolute",
          top: "12px",
          right: "12px",
          padding: "4px 12px",
          borderRadius: "16px",
          fontSize: "12px",
          fontWeight: 600,
          
          recommended: {
            backgroundColor: colorSystem.semantic.success.light,
            color: colorSystem.semantic.success.dark
          },
          
          premium: {
            backgroundColor: colorSystem.special.qualityTiers.premium + "20",
            color: colorSystem.special.qualityTiers.premium
          }
        }
      },
      
      // Comparison view
      comparisonView: {
        container: {
          position: "fixed",
          bottom: "24px",
          left: "50%",
          transform: "translateX(-50%)",
          backgroundColor: colorSystem.neutral[900],
          color: colorSystem.neutral[0],
          borderRadius: "12px",
          padding: "16px 24px",
          display: "flex",
          alignItems: "center",
          gap: "24px",
          boxShadow: "0 4px 24px rgba(0,0,0,0.2)",
          zIndex: 100
        },
        
        items: {
          display: "flex",
          gap: "12px"
        },
        
        item: {
          display: "flex",
          alignItems: "center",
          gap: "8px",
          padding: "4px 12px",
          backgroundColor: "rgba(255,255,255,0.1)",
          borderRadius: "20px",
          
          remove: {
            cursor: "pointer",
            opacity: 0.7,
            
            hover: {
              opacity: 1
            }
          }
        },
        
        compareButton: {
          padding: "8px 20px",
          backgroundColor: colorSystem.neutral[0],
          color: colorSystem.neutral[900],
          borderRadius: "8px",
          fontWeight: 500
        }
      }
    };

#### 14.2 Material Detail Modal

typescript

    // Material detail view components
    const materialDetailComponents = {
      // Modal container
      modal: {
        overlay: {
          position: "fixed",
          inset: 0,
          backgroundColor: "rgba(0,0,0,0.5)",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          padding: "24px",
          zIndex: 1000
        },
        
        content: {
          backgroundColor: colorSystem.neutral[0],
          borderRadius: "16px",
          maxWidth: "800px",
          maxHeight: "90vh",
          width: "100%",
          overflow: "hidden",
          display: "flex",
          flexDirection: "column"
        },
        
        header: {
          padding: "24px",
          borderBottom: `1px solid ${colorSystem.border.light}`,
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center"
        },
        
        body: {
          flex: 1,
          overflowY: "auto",
          padding: "24px"
        },
        
        footer: {
          padding: "24px",
          borderTop: `1px solid ${colorSystem.border.light}`,
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center"
        }
      },
      
      // Image gallery
      gallery: {
        container: {
          marginBottom: "32px"
        },
        
        mainImage: {
          width: "100%",
          height: "400px",
          objectFit: "contain",
          backgroundColor: colorSystem.neutral[50],
          borderRadius: "12px",
          marginBottom: "16px"
        },
        
        thumbnails: {
          display: "flex",
          gap: "12px",
          overflowX: "auto"
        },
        
        thumbnail: {
          width: "80px",
          height: "80px",
          objectFit: "cover",
          borderRadius: "8px",
          border: `2px solid transparent`,
          cursor: "pointer",
          transition: "all 0.2s ease",
          
          active: {
            borderColor: colorSystem.brand.primary[500]
          }
        }
      },
      
      // Specifications table
      specsTable: {
        container: {
          backgroundColor: colorSystem.neutral[50],
          borderRadius: "12px",
          padding: "24px",
          marginBottom: "24px"
        },
        
        title: {
          fontSize: "18px",
          fontWeight: 600,
          marginBottom: "16px"
        },
        
        row: {
          display: "flex",
          padding: "12px 0",
          borderBottom: `1px solid ${colorSystem.border.light}`,
          
          lastChild: {
            borderBottom: "none"
          }
        },
        
        label: {
          flex: "0 0 40%",
          fontSize: "14px",
          color: colorSystem.text.secondary
        },
        
        value: {
          flex: 1,
          fontSize: "14px",
          fontWeight: 500,
          color: colorSystem.text.primary
        }
      },
      
      // Price breakdown
      priceBreakdown: {
        container: {
          backgroundColor: colorSystem.brand.primary[50],
          borderRadius: "12px",
          padding: "24px",
          marginBottom: "24px"
        },
        
        row: {
          display: "flex",
          justifyContent: "space-between",
          marginBottom: "12px",
          
          label: {
            fontSize: "14px",
            color: colorSystem.text.secondary
          },
          
          value: {
            fontSize: "16px",
            fontWeight: 500,
            fontFamily: fontSystem.secondary.family
          }
        },
        
        total: {
          paddingTop: "12px",
          borderTop: `1px solid ${colorSystem.brand.primary[200]}`,
          
          label: {
            fontSize: "16px",
            fontWeight: 600
          },
          
          value: {
            fontSize: "20px",
            fontWeight: 600,
            color: colorSystem.brand.primary[700]
          }
        }
      }
    };

### 15\. Visualization Components

#### 15.1 Charts & Graphs

typescript

    // Chart component specifications
    const chartComponents = {
      // Chart container
      chartContainer: {
        backgroundColor: colorSystem.neutral[0],
        borderRadius: "12px",
        padding: "24px",
        boxShadow: "0 1px 3px rgba(0,0,0,0.08)"
      },
      
      // Chart header
      chartHeader: {
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
        marginBottom: "24px",
        
        title: {
          fontSize: "18px",
          fontWeight: 600,
          color: colorSystem.text.primary
        },
        
        controls: {
          display: "flex",
          gap: "12px"
        },
        
        periodSelector: {
          display: "flex",
          backgroundColor: colorSystem.neutral[100],
          borderRadius: "8px",
          padding: "4px"
        },
        
        periodOption: {
          padding: "6px 16px",
          borderRadius: "6px",
          fontSize: "13px",
          fontWeight: 500,
          color: colorSystem.text.secondary,
          cursor: "pointer",
          transition: "all 0.2s ease",
          
          active: {
            backgroundColor: colorSystem.neutral[0],
            color: colorSystem.text.primary,
            boxShadow: "0 1px 3px rgba(0,0,0,0.08)"
          }
        }
      },
      
      // Chart types
      chartTypes: {
        line: {
          grid: {
            stroke: colorSystem.border.light,
            strokeDasharray: "3 3"
          },
          
          line: {
            strokeWidth: 2,
            strokeLinecap: "round"
          },
          
          area: {
            fillOpacity: 0.1
          },
          
          dot: {
            r: 4,
            strokeWidth: 2,
            fill: colorSystem.neutral[0]
          },
          
          activeDot: {
            r: 6
          }
        },
        
        bar: {
          barSize: 40,
          radius: [4, 4, 0, 0],
          
          hover: {
            fillOpacity: 0.8
          }
        },
        
        pie: {
          innerRadius: "60%",
          outerRadius: "80%",
          paddingAngle: 2,
          
          label: {
            fontSize: "14px",
            fontWeight: 500
          }
        },
        
        donut: {
          innerRadius: "50%",
          outerRadius: "80%",
          
          center: {
            value: {
              fontSize: "32px",
              fontWeight: 600,
              fontFamily: fontSystem.secondary.family
            },
            
            label: {
              fontSize: "14px",
              color: colorSystem.text.secondary
            }
          }
        }
      },
      
      // Tooltip
      tooltip: {
        container: {
          backgroundColor: colorSystem.neutral[900],
          color: colorSystem.neutral[0],
          padding: "12px 16px",
          borderRadius: "8px",
          boxShadow: "0 4px 12px rgba(0,0,0,0.15)",
          fontSize: "13px"
        },
        
        label: {
          marginBottom: "8px",
          opacity: 0.8
        },
        
        value: {
          fontSize: "16px",
          fontWeight: 600,
          fontFamily: fontSystem.secondary.family
        }
      },
      
      // Legend
      legend: {
        container: {
          display: "flex",
          justifyContent: "center",
          gap: "24px",
          marginTop: "24px"
        },
        
        item: {
          display: "flex",
          alignItems: "center",
          gap: "8px",
          cursor: "pointer"
        },
        
        indicator: {
          width: "12px",
          height: "12px",
          borderRadius: "2px"
        },
        
        label: {
          fontSize: "14px",
          color: colorSystem.text.secondary
        }
      }
    };

#### 15.2 Progress Visualizations

typescript

    // Progress and timeline visualizations
    const progressVisualizations = {
      // Timeline component
      timeline: {
        container: {
          position: "relative",
          padding: "24px 0"
        },
        
        line: {
          position: "absolute",
          left: "23px",
          top: "40px",
          bottom: "40px",
          width: "2px",
          backgroundColor: colorSystem.border.light
        },
        
        item: {
          display: "flex",
          gap: "24px",
          marginBottom: "32px",
          
          lastChild: {
            marginBottom: 0
          }
        },
        
        marker: {
          width: "48px",
          height: "48px",
          borderRadius: "50%",
          backgroundColor: colorSystem.neutral[0],
          border: `2px solid ${colorSystem.border.medium}`,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          flexShrink: 0,
          zIndex: 1,
          
          completed: {
            backgroundColor: colorSystem.semantic.success.main,
            borderColor: colorSystem.semantic.success.main,
            color: colorSystem.neutral[0]
          },
          
          current: {
            borderColor: colorSystem.brand.primary[500],
            backgroundColor: colorSystem.brand.primary[100],
            color: colorSystem.brand.primary[700]
          }
        },
        
        content: {
          flex: 1,
          paddingBottom: "8px"
        },
        
        title: {
          fontSize: "16px",
          fontWeight: 600,
          color: colorSystem.text.primary,
          marginBottom: "4px"
        },
        
        description: {
          fontSize: "14px",
          color: colorSystem.text.secondary,
          marginBottom: "8px"
        },
        
        metadata: {
          display: "flex",
          gap: "16px",
          fontSize: "13px",
          color: colorSystem.text.tertiary
        }
      },
      
      // Gantt chart
      ganttChart: {
        container: {
          overflowX: "auto"
        },
        
        header: {
          display: "flex",
          backgroundColor: colorSystem.neutral[100],
          borderBottom: `2px solid ${colorSystem.border.medium}`,
          position: "sticky",
          top: 0,
          zIndex: 10
        },
        
        monthHeader: {
          padding: "12px 16px",
          fontSize: "14px",
          fontWeight: 600,
          borderRight: `1px solid ${colorSystem.border.light}`,
          textAlign: "center"
        },
        
        row: {
          display: "flex",
          borderBottom: `1px solid ${colorSystem.border.light}`,
          
          hover: {
            backgroundColor: colorSystem.neutral[50]
          }
        },
        
        taskName: {
          width: "200px",
          padding: "16px",
          fontSize: "14px",
          fontWeight: 500,
          borderRight: `1px solid ${colorSystem.border.light}`,
          position: "sticky",
          left: 0,
          backgroundColor: colorSystem.neutral[0],
          zIndex: 5
        },
        
        taskBar: {
          height: "32px",
          borderRadius: "4px",
          display: "flex",
          alignItems: "center",
          padding: "0 12px",
          fontSize: "13px",
          color: colorSystem.neutral[0],
          fontWeight: 500,
          position: "absolute",
          top: "8px",
          
          variants: {
            structure: {
              backgroundColor: colorSystem.special.materials.structure
            },
            finishing: {
              backgroundColor: colorSystem.special.materials.finishing
            },
            mep: {
              backgroundColor: colorSystem.special.materials.electrical
            }
          }
        }
      }
    };

### 16\. Project Dashboard Components

#### 16.1 Dashboard Layout

typescript

    // Dashboard layout components
    const dashboardComponents = {
      // Main layout
      layout: {
        container: {
          display: "flex",
          height: "100vh",
          backgroundColor: colorSystem.neutral[50]
        },
        
        sidebar: {
          width: "256px",
          backgroundColor: colorSystem.neutral[0],
          borderRight: `1px solid ${colorSystem.border.light}`,
          display: "flex",
          flexDirection: "column",
          
          collapsed: {
            width: "64px"
          },
          
          mobile: {
            position: "fixed",
            left: 0,
            top: 0,
            bottom: 0,
            zIndex: 1000,
            transform: "translateX(-100%)",
            transition: "transform 0.3s ease",
            
            open: {
              transform: "translateX(0)"
            }
          }
        },
        
        main: {
          flex: 1,
          display: "flex",
          flexDirection: "column",
          overflow: "hidden"
        },
        
        header: {
          height: "64px",
          backgroundColor: colorSystem.neutral[0],
          borderBottom: `1px solid ${colorSystem.border.light}`,
          padding: "0 24px",
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between"
        },
        
        content: {
          flex: 1,
          overflowY: "auto",
          padding: "24px"
        }
      },
      
      // Sidebar navigation
      sidebarNav: {
        logo: {
          padding: "20px",
          borderBottom: `1px solid ${colorSystem.border.light}`
        },
        
        menu: {
          flex: 1,
          padding: "12px"
        },
        
        menuItem: {
          display: "flex",
          alignItems: "center",
          gap: "12px",
          padding: "12px 16px",
          borderRadius: "8px",
          color: colorSystem.text.secondary,
          fontSize: "14px",
          fontWeight: 500,
          transition: "all 0.2s ease",
          marginBottom: "4px",
          
          hover: {
            backgroundColor: colorSystem.neutral[50],
            color: colorSystem.text.primary
          },
          
          active: {
            backgroundColor: colorSystem.brand.primary[50],
            color: colorSystem.brand.primary[600]
          },
          
          icon: {
            width: "20px",
            height: "20px"
          }
        },
        
        section: {
          marginTop: "24px",
          marginBottom: "8px",
          padding: "0 16px",
          fontSize: "12px",
          fontWeight: 600,
          color: colorSystem.text.tertiary,
          textTransform: "uppercase",
          letterSpacing: "0.05em"
        }
      },
      
      // Dashboard widgets
      widgets: {
        statsGrid: {
          display: "grid",
          gridTemplateColumns: "repeat(auto-fit, minmax(250px, 1fr))",
          gap: "20px",
          marginBottom: "32px"
        },
        
        recentActivity: {
          container: {
            backgroundColor: colorSystem.neutral[0],
            borderRadius: "12px",
            padding: "24px",
            boxShadow: "0 1px 3px rgba(0,0,0,0.08)"
          },
          
          activityItem: {
            display: "flex",
            gap: "16px",
            padding: "16px 0",
            borderBottom: `1px solid ${colorSystem.border.light}`,
            
            lastChild: {
              borderBottom: "none"
            }
          },
          
          icon: {
            width: "40px",
            height: "40px",
            borderRadius: "50%",
            backgroundColor: colorSystem.neutral[100],
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            flexShrink: 0
          },
          
          content: {
            flex: 1
          },
          
          title: {
            fontSize: "14px",
            fontWeight: 500,
            color: colorSystem.text.primary,
            marginBottom: "4px"
          },
          
          description: {
            fontSize: "13px",
            color: colorSystem.text.secondary
          },
          
          time: {
            fontSize: "12px",
            color: colorSystem.text.tertiary
          }
        }
      }
    };

#### 16.2 Project Cards

typescript

    // Project card components
    const projectCardComponents = {
      // Project grid
      projectGrid: {
        display: "grid",
        gridTemplateColumns: "repeat(auto-fill, minmax(320px, 1fr))",
        gap: "24px"
      },
      
      // Project card
      projectCard: {
        container: {
          backgroundColor: colorSystem.neutral[0],
          borderRadius: "12px",
          overflow: "hidden",
          boxShadow: "0 1px 3px rgba(0,0,0,0.08)",
          transition: "all 0.2s ease",
          cursor: "pointer",
          
          hover: {
            boxShadow: "0 4px 16px rgba(0,0,0,0.1)",
            transform: "translateY(-2px)"
          }
        },
        
        image: {
          width: "100%",
          height: "180px",
          objectFit: "cover",
          backgroundColor: colorSystem.neutral[100],
          position: "relative",
          
          overlay: {
            position: "absolute",
            inset: 0,
            background: "linear-gradient(to bottom, transparent 0%, rgba(0,0,0,0.6) 100%)"
          },
          
          badge: {
            position: "absolute",
            top: "12px",
            right: "12px",
            padding: "6px 12px",
            borderRadius: "20px",
            fontSize: "12px",
            fontWeight: 600,
            backgroundColor: "rgba(255,255,255,0.9)",
            
            active: {
              color: colorSystem.semantic.success.main
            },
            
            completed: {
              color: colorSystem.text.secondary
            },
            
            draft: {
              color: colorSystem.semantic.warning.main
            }
          }
        },
        
        content: {
          padding: "20px"
        },
        
        title: {
          fontSize: "18px",
          fontWeight: 600,
          color: colorSystem.text.primary,
          marginBottom: "8px"
        },
        
        details: {
          display: "flex",
          gap: "16px",
          marginBottom: "16px",
          fontSize: "14px",
          color: colorSystem.text.secondary
        },
        
        metrics: {
          display: "grid",
          gridTemplateColumns: "repeat(2, 1fr)",
          gap: "16px",
          paddingTop: "16px",
          borderTop: `1px solid ${colorSystem.border.light}`
        },
        
        metric: {
          label: {
            fontSize: "12px",
            color: colorSystem.text.tertiary,
            marginBottom: "4px"
          },
          
          value: {
            fontSize: "16px",
            fontWeight: 600,
            color: colorSystem.text.primary,
            fontFamily: fontSystem.secondary.family
          }
        },
        
        actions: {
          display: "flex",
          gap: "8px",
          marginTop: "16px"
        }
      },
      
      // Empty state
      emptyState: {
        container: {
          textAlign: "center",
          padding: "80px 24px"
        },
        
        illustration: {
          width: "200px",
          height: "200px",
          margin: "0 auto 32px",
          opacity: 0.8
        },
        
        title: {
          fontSize: "24px",
          fontWeight: 600,
          color: colorSystem.text.primary,
          marginBottom: "12px"
        },
        
        description: {
          fontSize: "16px",
          color: colorSystem.text.secondary,
          marginBottom: "32px",
          maxWidth: "400px",
          margin: "0 auto"
        }
      }
    };

### 17\. Report Generation Components

#### 17.1 Report Builder Interface

typescript

    // Report builder components
    const reportBuilderComponents = {
      // Report configuration panel
      configPanel: {
        container: {
          backgroundColor: colorSystem.neutral[0],
          borderRadius: "12px",
          padding: "24px",
          marginBottom: "24px"
        },
        
        section: {
          marginBottom: "32px",
          
          lastChild: {
            marginBottom: 0
          }
        },
        
        sectionTitle: {
          fontSize: "16px",
          fontWeight: 600,
          color: colorSystem.text.primary,
          marginBottom: "16px"
        },
        
        optionGrid: {
          display: "grid",
          gridTemplateColumns: "repeat(auto-fit, minmax(200px, 1fr))",
          gap: "12px"
        },
        
        option: {
          padding: "16px",
          border: `2px solid ${colorSystem.border.light}`,
          borderRadius: "8px",
          cursor: "pointer",
          transition: "all 0.2s ease",
          
          hover: {
            borderColor: colorSystem.border.medium
          },
          
          selected: {
            borderColor: colorSystem.brand.primary[500],
            backgroundColor: colorSystem.brand.primary[50]
          },
          
          icon: {
            width: "32px",
            height: "32px",
            marginBottom: "8px",
            color: colorSystem.text.secondary
          },
          
          label: {
            fontSize: "14px",
            fontWeight: 500,
            color: colorSystem.text.primary
          }
        }
      },
      
      // Report preview
      reportPreview: {
        container: {
          backgroundColor: colorSystem.neutral[100],
          borderRadius: "12px",
          padding: "32px",
          minHeight: "600px",
          display: "flex",
          alignItems: "center",
          justifyContent: "center"
        },
        
        document: {
          backgroundColor: colorSystem.neutral[0],
          width: "100%",
          maxWidth: "800px",
          minHeight: "1000px",
          borderRadius: "8px",
          boxShadow: "0 4px 24px rgba(0,0,0,0.08)",
          padding: "60px"
        },
        
        header: {
          borderBottom: `2px solid ${colorSystem.border.light}`,
          paddingBottom: "24px",
          marginBottom: "32px"
        },
        
        title: {
          fontSize: "32px",
          fontWeight: 700,
          color: colorSystem.text.primary,
          marginBottom: "8px"
        },
        
        subtitle: {
          fontSize: "18px",
          color: colorSystem.text.secondary
        },
        
        section: {
          marginBottom: "48px"
        },
        
        sectionTitle: {
          fontSize: "24px",
          fontWeight: 600,
          color: colorSystem.text.primary,
          marginBottom: "24px"
       },
       
       table: {
         width: "100%",
         borderCollapse: "collapse",
         marginTop: "16px",
         
         th: {
           padding: "12px",
           backgroundColor: colorSystem.neutral[100],
           borderBottom: `2px solid ${colorSystem.border.medium}`,
           textAlign: "left",
           fontWeight: 600,
           fontSize: "14px"
         },
         
         td: {
           padding: "12px",
           borderBottom: `1px solid ${colorSystem.border.light}`,
           fontSize: "14px"
         }
       }
     },
     
     // Export options
     exportOptions: {
       container: {
         position: "fixed",
         bottom: "24px",
         right: "24px",
         display: "flex",
         gap: "12px",
         zIndex: 100
       },
       
       button: {
         display: "flex",
         alignItems: "center",
         gap: "8px",
         padding: "12px 24px",
         backgroundColor: colorSystem.neutral[0],
         borderRadius: "8px",
         boxShadow: "0 2px 12px rgba(0,0,0,0.1)",
         fontWeight: 500,
         cursor: "pointer",
         transition: "all 0.2s ease",
         
         hover: {
           transform: "translateY(-2px)",
           boxShadow: "0 4px 20px rgba(0,0,0,0.15)"
         },
         
         primary: {
           backgroundColor: colorSystem.brand.primary[500],
           color: colorSystem.neutral[0]
         }
       }
     }
    };

* * *

Part 4: Patterns & Flows
------------------------

### 18\. User Flow Patterns

#### 18.1 Onboarding Flow

typescript

    // Onboarding flow patterns
    const onboardingPatterns = {
      // Welcome screen
      welcomeScreen: {
        container: {
          minHeight: "100vh",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          backgroundColor: colorSystem.neutral[50]
        },
        
        content: {
          maxWidth: "480px",
          textAlign: "center",
          padding: "48px"
        },
        
        logo: {
          width: "80px",
          height: "80px",
          margin: "0 auto 32px"
        },
        
        title: {
          fontSize: "32px",
          fontWeight: 700,
          color: colorSystem.text.primary,
          marginBottom: "16px"
        },
        
        description: {
          fontSize: "18px",
          color: colorSystem.text.secondary,
          marginBottom: "48px",
          lineHeight: "28px"
        },
        
        actions: {
          display: "flex",
          flexDirection: "column",
          gap: "16px"
        }
      },
      
      // Progress indicator
      progressIndicator: {
        container: {
          display: "flex",
          justifyContent: "center",
          marginBottom: "48px"
        },
        
        steps: {
          display: "flex",
          alignItems: "center",
          gap: "8px"
        },
        
        step: {
          width: "8px",
          height: "8px",
          borderRadius: "50%",
          backgroundColor: colorSystem.neutral[300],
          transition: "all 0.3s ease",
          
          active: {
            width: "24px",
            borderRadius: "4px",
            backgroundColor: colorSystem.brand.primary[500]
          },
          
          completed: {
            backgroundColor: colorSystem.brand.primary[500]
          }
        }
      },
      
      // Step screens
      stepScreen: {
        container: {
          maxWidth: "600px",
          margin: "0 auto",
          padding: "48px 24px"
        },
        
        header: {
          marginBottom: "48px"
        },
        
        stepNumber: {
          fontSize: "14px",
          fontWeight: 600,
          color: colorSystem.text.tertiary,
          textTransform: "uppercase",
          letterSpacing: "0.05em",
          marginBottom: "12px"
        },
        
        title: {
          fontSize: "28px",
          fontWeight: 600,
          color: colorSystem.text.primary,
          marginBottom: "12px"
        },
        
        description: {
          fontSize: "16px",
          color: colorSystem.text.secondary,
          lineHeight: "24px"
        },
        
        content: {
          marginBottom: "48px"
        },
        
        actions: {
          display: "flex",
          justifyContent: "space-between",
          gap: "16px"
        }
      },
      
      // Tooltip tours
      tooltipTour: {
        overlay: {
          position: "fixed",
          inset: 0,
          backgroundColor: "rgba(0,0,0,0.7)",
          zIndex: 999
        },
        
        highlight: {
          position: "absolute",
          borderRadius: "8px",
          boxShadow: "0 0 0 4px rgba(255,255,255,0.2)",
          transition: "all 0.3s ease"
        },
        
        tooltip: {
          position: "absolute",
          backgroundColor: colorSystem.neutral[0],
          borderRadius: "12px",
          padding: "20px",
          maxWidth: "320px",
          boxShadow: "0 4px 24px rgba(0,0,0,0.2)",
          
          arrow: {
            position: "absolute",
            width: "12px",
            height: "12px",
            backgroundColor: colorSystem.neutral[0],
            transform: "rotate(45deg)"
          },
          
          title: {
            fontSize: "16px",
            fontWeight: 600,
            marginBottom: "8px"
          },
          
          description: {
            fontSize: "14px",
            color: colorSystem.text.secondary,
            marginBottom: "16px",
            lineHeight: "20px"
          },
          
          actions: {
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center"
          },
          
          progress: {
            fontSize: "13px",
            color: colorSystem.text.tertiary
          }
        }
      }
    };

#### 18.2 Form Flow Patterns

typescript

    // Multi-step form patterns
    const formFlowPatterns = {
      // Step navigation
      stepNavigation: {
        container: {
          display: "flex",
          justifyContent: "space-between",
          marginBottom: "48px",
          position: "relative"
        },
        
        progressLine: {
          position: "absolute",
          top: "20px",
          left: "40px",
          right: "40px",
          height: "2px",
          backgroundColor: colorSystem.border.light,
          zIndex: 0
        },
        
        progressFill: {
          height: "100%",
          backgroundColor: colorSystem.brand.primary[500],
          transition: "width 0.3s ease"
        },
        
        step: {
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          gap: "8px",
          position: "relative",
          zIndex: 1
        },
        
        stepCircle: {
          width: "40px",
          height: "40px",
          borderRadius: "50%",
          backgroundColor: colorSystem.neutral[0],
          border: `2px solid ${colorSystem.border.medium}`,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          fontSize: "14px",
          fontWeight: 600,
          transition: "all 0.3s ease",
          
          active: {
            borderColor: colorSystem.brand.primary[500],
            backgroundColor: colorSystem.brand.primary[500],
            color: colorSystem.neutral[0]
          },
          
          completed: {
            borderColor: colorSystem.semantic.success.main,
            backgroundColor: colorSystem.semantic.success.main,
            color: colorSystem.neutral[0]
          },
          
          error: {
            borderColor: colorSystem.semantic.error.main,
            backgroundColor: colorSystem.semantic.error.light,
            color: colorSystem.semantic.error.main
          }
        },
        
        stepLabel: {
          fontSize: "13px",
          color: colorSystem.text.secondary,
          textAlign: "center",
          maxWidth: "100px"
        }
      },
      
      // Form sections
      formSection: {
        container: {
          animation: "fadeIn 0.3s ease"
        },
        
        header: {
          marginBottom: "32px"
        },
        
        title: {
          fontSize: "24px",
          fontWeight: 600,
          color: colorSystem.text.primary,
          marginBottom: "8px"
        },
        
        description: {
          fontSize: "16px",
          color: colorSystem.text.secondary
        },
        
        fieldGroup: {
          marginBottom: "24px"
        },
        
        fieldRow: {
          display: "grid",
          gridTemplateColumns: "repeat(auto-fit, minmax(280px, 1fr))",
          gap: "20px",
          marginBottom: "20px"
        }
      },
      
      // Validation feedback
      validationFeedback: {
        summary: {
          padding: "16px",
          borderRadius: "8px",
          marginBottom: "24px",
          backgroundColor: colorSystem.semantic.error.light,
          color: colorSystem.semantic.error.dark,
          
          title: {
            fontWeight: 600,
            marginBottom: "8px"
          },
          
          list: {
            listStyle: "disc",
            paddingLeft: "20px",
            
            item: {
              marginBottom: "4px"
            }
          }
        },
        
        fieldError: {
          display: "flex",
          alignItems: "center",
          gap: "6px",
          marginTop: "6px",
          fontSize: "13px",
          color: colorSystem.semantic.error.main,
          
          icon: {
            width: "16px",
            height: "16px"
          }
        },
        
        success: {
          display: "flex",
          alignItems: "center",
          gap: "8px",
          padding: "12px 16px",
          borderRadius: "8px",
          backgroundColor: colorSystem.semantic.success.light,
          color: colorSystem.semantic.success.dark,
          marginBottom: "24px",
          
          icon: {
            width: "20px",
            height: "20px"
          }
        }
      },
      
      // Save progress
      saveProgress: {
        container: {
          position: "fixed",
          bottom: "24px",
          left: "50%",
          transform: "translateX(-50%)",
          display: "flex",
          alignItems: "center",
          gap: "12px",
          padding: "12px 20px",
          backgroundColor: colorSystem.neutral[900],
          color: colorSystem.neutral[0],
          borderRadius: "24px",
          fontSize: "14px",
          opacity: 0,
          visibility: "hidden",
          transition: "all 0.3s ease",
          
          visible: {
            opacity: 1,
            visibility: "visible"
          }
        },
        
        icon: {
          animation: "spin 1s linear infinite"
        },
        
        states: {
          saving: "Saving your progress...",
          saved: "Progress saved",
          error: "Failed to save"
        }
      }
    };

### 19\. Responsive Design Patterns

#### 19.1 Breakpoint System

typescript

    // Responsive breakpoint system
    const breakpointSystem = {
      // Breakpoint values
      breakpoints: {
        xs: 0,
        sm: 640,
        md: 768,
        lg: 1024,
        xl: 1280,
        xxl: 1536
      },
      
      // Media query helpers
      mediaQueries: {
        xs: "@media (min-width: 0px)",
        sm: "@media (min-width: 640px)",
        md: "@media (min-width: 768px)",
        lg: "@media (min-width: 1024px)",
        xl: "@media (min-width: 1280px)",
        xxl: "@media (min-width: 1536px)",
        
        // Max-width queries
        xsMax: "@media (max-width: 639px)",
        smMax: "@media (max-width: 767px)",
        mdMax: "@media (max-width: 1023px)",
        lgMax: "@media (max-width: 1279px)",
        xlMax: "@media (max-width: 1535px)",
        
        // Range queries
        smOnly: "@media (min-width: 640px) and (max-width: 767px)",
        mdOnly: "@media (min-width: 768px) and (max-width: 1023px)",
        lgOnly: "@media (min-width: 1024px) and (max-width: 1279px)"
      },
      
      // Container widths
      containers: {
        xs: "100%",
        sm: "640px",
        md: "768px",
        lg: "1024px",
        xl: "1280px",
        xxl: "1536px"
      },
      
      // Responsive utilities
      utilities: {
        hideOn: {
          mobile: { [mediaQueries.xsMax]: { display: "none" } },
          tablet: { [mediaQueries.mdMax]: { display: "none" } },
          desktop: { [mediaQueries.lg]: { display: "none" } }
        },
        
        showOn: {
          mobile: {
            display: "none",
            [mediaQueries.xsMax]: { display: "block" }
          },
          tablet: {
            display: "none",
            [mediaQueries.smOnly]: { display: "block" },
            [mediaQueries.mdOnly]: { display: "block" }
          },
          desktop: {
            display: "none",
            [mediaQueries.lg]: { display: "block" }
          }
        }
      }
    };

#### 19.2 Responsive Component Patterns

typescript

    // Responsive component behaviors
    const responsivePatterns = {
      // Navigation responsiveness
      navigation: {
        desktop: {
          display: "flex",
          horizontal: true,
          fullWidth: false
        },
        
        tablet: {
          display: "flex",
          horizontal: true,
          condensed: true
        },
        
        mobile: {
          display: "drawer",
          hamburgerMenu: true,
          fullScreen: true
        }
      },
      
      // Grid responsiveness
      gridLayouts: {
        // Card grid
        cardGrid: {
          xs: "grid-cols-1",
          sm: "grid-cols-2",
          lg: "grid-cols-3",
          xl: "grid-cols-4"
        },
        
        // Feature grid
        featureGrid: {
          xs: "grid-cols-1",
          md: "grid-cols-2",
          lg: "grid-cols-3"
        },
        
        // Dashboard grid
        dashboardGrid: {
          xs: "grid-cols-1",
          md: "grid-cols-2",
          lg: "grid-cols-4"
        }
      },
      
      // Table responsiveness
      responsiveTable: {
        desktop: {
          display: "table",
          scrollable: false
        },
        
        tablet: {
          display: "table",
          scrollable: true,
          stickyHeader: true
        },
        
        mobile: {
          display: "cards",
          stackedLayout: true,
          labelValuePairs: true
        }
      },
      
      // Form responsiveness
      responsiveForm: {
        fieldLayout: {
          desktop: "side-by-side where logical",
          tablet: "mostly stacked",
          mobile: "fully stacked"
        },
        
        buttonPlacement: {
          desktop: "right aligned",
          mobile: "full width stacked"
        },
        
        labelPosition: {
          desktop: "top or side",
          mobile: "always top"
        }
      },
      
      // Modal responsiveness
      responsiveModal: {
        desktop: {
          maxWidth: "600px",
          centered: true,
          margin: "auto"
        },
        
        tablet: {
          maxWidth: "90%",
          centered: true
        },
        
        mobile: {
          fullScreen: true,
          slideUp: true,
          margin: 0
        }
      }
    };

### 20\. Animation & Interaction Patterns

#### 20.1 Animation System

typescript

    // Animation specifications
    const animationSystem = {
      // Timing functions
      easings: {
        linear: "linear",
        ease: "ease",
        easeIn: "cubic-bezier(0.4, 0, 1, 1)",
        easeOut: "cubic-bezier(0, 0, 0.2, 1)",
        easeInOut: "cubic-bezier(0.4, 0, 0.2, 1)",
        spring: "cubic-bezier(0.175, 0.885, 0.32, 1.275)"
      },
      
      // Duration scale
      durations: {
        instant: "0ms",
        fast: "150ms",
        normal: "300ms",
        slow: "500ms",
        slower: "700ms"
      },
      
      // Common animations
      animations: {
        fadeIn: {
          from: { opacity: 0 },
          to: { opacity: 1 },
          duration: "normal",
          easing: "easeOut"
        },
        
        fadeOut: {
          from: { opacity: 1 },
          to: { opacity: 0 },
          duration: "fast",
          easing: "easeIn"
        },
        
        slideUp: {
          from: { transform: "translateY(16px)", opacity: 0 },
          to: { transform: "translateY(0)", opacity: 1 },
          duration: "normal",
          easing: "easeOut"
        },
        
        slideDown: {
          from: { transform: "translateY(-16px)", opacity: 0 },
          to: { transform: "translateY(0)", opacity: 1 },
          duration: "normal",
          easing: "easeOut"
        },
        
        scaleIn: {
          from: { transform: "scale(0.95)", opacity: 0 },
          to: { transform: "scale(1)", opacity: 1 },
          duration: "normal",
          easing: "spring"
        },
        
        rotate: {
          from: { transform: "rotate(0deg)" },
          to: { transform: "rotate(360deg)" },
          duration: "slow",
          easing: "linear"
        }
      },
      
      // Micro-interactions
      microInteractions: {
        hover: {
          scale: 1.02,
          duration: "fast",
          easing: "easeOut"
        },
        
        tap: {
          scale: 0.98,
          duration: "instant",
          easing: "easeIn"
        },
        
        focus: {
          outlineOffset: "2px",
          duration: "fast"
        }
      },
      
      // Page transitions
      pageTransitions: {
        fade: {
          exit: { opacity: 0, duration: "fast" },
          enter: { opacity: 1, duration: "normal" }
        },
        
        slide: {
          exit: { transform: "translateX(-20px)", opacity: 0 },
          enter: { transform: "translateX(0)", opacity: 1 }
        },
        
        scale: {
          exit: { transform: "scale(0.98)", opacity: 0 },
          enter: { transform: "scale(1)", opacity: 1 }
        }
      }
    };

#### 20.2 Interaction Patterns

typescript

    // Common interaction patterns
    const interactionPatterns = {
      // Hover states
      hoverEffects: {
        elevation: {
          default: "0 1px 3px rgba(0,0,0,0.08)",
          hover: "0 4px 12px rgba(0,0,0,0.12)"
        },
        
        colorShift: {
          default: colorSystem.brand.primary[500],
          hover: colorSystem.brand.primary[600]
        },
        
        reveal: {
          default: { opacity: 0, visibility: "hidden" },
          hover: { opacity: 1, visibility: "visible" }
        }
      },
      
      // Loading patterns
      loadingPatterns: {
        skeleton: {
          description: "Show layout structure while loading",
          usage: "Content that maintains layout"
        },
        
        spinner: {
          description: "Centered spinning indicator",
          usage: "Unknown content size"
        },
        
        progressBar: {
          description: "Linear progress indicator",
          usage: "Known progress steps"
        },
        
        shimmer: {
          description: "Animated placeholder",
          usage: "Text and image placeholders"
        }
      },
      
      // Feedback patterns
      feedbackPatterns: {
        success: {
          visual: "Green checkmark animation",
          duration: "2 seconds",
          behavior: "Auto-dismiss"
        },
        
        error: {
          visual: "Red shake animation",
          duration: "Until dismissed",
          behavior: "Requires action"
        },
        
        processing: {
          visual: "Pulsing or progress",
          duration: "Until complete",
          behavior: "Blocks interaction"
        }
      },
      
      // Gesture support
      gestures: {
        swipe: {
          threshold: 50, // pixels
          velocity: 0.3,
          usage: ["Carousel navigation", "Dismiss modals", "Pull to refresh"]
        },
        
        pinch: {
          minScale: 0.5,
          maxScale: 3,
          usage: ["Image zoom", "Map interaction"]
        },
        
        longPress: {
          duration: 500, // ms
          usage: ["Context menu", "Selection mode"]
        }
      }
    };

### 21\. Accessibility Patterns

#### 21.1 Accessibility Guidelines

typescript

    // Accessibility implementation patterns
    const accessibilityPatterns = {
      // Focus management
      focusManagement: {
        // Focus indicators
        focusIndicator: {
          outline: `2px solid ${colorSystem.brand.primary[500]}`,
          outlineOffset: "2px",
          borderRadius: "inherit"
        },
        
        // Focus trap for modals
        focusTrap: {
          implementation: "Focus cycles within modal",
          escapeKey: "Closes modal",
          returnFocus: "To triggering element"
        },
        
        // Skip links
        skipLinks: {
          position: "absolute",
          top: "-40px",
          left: "0",
          padding: "8px",
          
          onFocus: {
            top: "0"
          }
        }
      },
      
      // ARIA patterns
      ariaPatterns: {
        // Live regions
        liveRegions: {
          polite: "aria-live='polite'",
          assertive: "aria-live='assertive'",
          usage: {
            polite: ["Form validation", "Search results"],
            assertive: ["Error messages", "Time-sensitive alerts"]
          }
        },
        
        // Landmarks
        landmarks: {
          navigation: "role='navigation'",
          main: "role='main'",
          complementary: "role='complementary'",
          search: "role='search'"
        },
        
        // States
        states: {
          expanded: "aria-expanded",
          selected: "aria-selected",
          checked: "aria-checked",
          disabled: "aria-disabled",
          invalid: "aria-invalid"
        }
      },
      
      // Keyboard navigation
      keyboardPatterns: {
        // Tab order
        tabOrder: {
          natural: "Follow visual flow",
          custom: "tabindex for special cases",
          skip: "tabindex='-1' for decorative"
        },
        
        // Keyboard shortcuts
        shortcuts: {
          navigation: {
            "Tab": "Next focusable element",
            "Shift+Tab": "Previous focusable element",
            "Enter": "Activate button/link",
            "Space": "Toggle checkbox/button",
            "Arrow keys": "Navigate within component"
          },
          
          global: {
            "Cmd/Ctrl+K": "Open search",
            "Cmd/Ctrl+/": "Open shortcuts help",
            "Esc": "Close modal/dropdown"
          }
        }
      },
      
      // Screen reader support
      screenReaderSupport: {
        // Hidden text
        visuallyHidden: {
          position: "absolute",
          width: "1px",
          height: "1px",
          padding: 0,
          margin: "-1px",
          overflow: "hidden",
          clip: "rect(0,0,0,0)",
          whiteSpace: "nowrap",
          border: 0
        },
        
        // Descriptive text
        descriptions: {
          buttons: "Include action in label",
          links: "Describe destination",
          images: "Meaningful alt text",
          icons: "aria-label for icon buttons"
        }
      }
    };

#### 21.2 Accessibility Testing Checklist

typescript

    // Accessibility testing requirements
    const accessibilityChecklist = {
      // Color contrast
      colorContrast: {
        requirements: {
          normalText: "4.5:1 minimum",
          largeText: "3:1 minimum",
          interactive: "3:1 minimum"
        },
        
        testing: [
          "Use contrast checker tools",
          "Test with color blindness simulators",
          "Verify in high contrast mode"
        ]
      },
      
      // Keyboard testing
      keyboardTesting: {
        requirements: [
          "All interactive elements reachable by keyboard",
          "Visible focus indicators",
          "Logical tab order",
          "No keyboard traps"
        ],
        
        testing: [
          "Navigate entire app with keyboard only",
          "Test with screen reader active",
          "Verify custom shortcuts don't conflict"
        ]
      },
      
      // Screen reader testing
      screenReaderTesting: {
        tools: ["NVDA", "JAWS", "VoiceOver", "TalkBack"],
        
        requirements: [
          "All content readable",
          "Interactive elements announced correctly",
          "State changes announced",
          "Error messages associated with fields"
        ]
      },
      
      // Mobile accessibility
      mobileAccessibility: {
        requirements: [
          "Touch targets minimum 44x44px",
          "Adequate spacing between targets",
          "Gestures have alternatives",
          "Text resizable to 200%"
        ]
      }
    };

* * *

Part 5: Implementation Guidelines
---------------------------------

### 22\. Component Architecture

#### 22.1 Component Structure

typescript

    // Standard component structure
    interface ComponentStructure {
      // Component file organization
      folderStructure: {
        ComponentName: {
          'index.tsx': 'Main component export',
          'ComponentName.tsx': 'Component implementation',
          'ComponentName.styles.ts': 'Styled components/styles',
          'ComponentName.types.ts': 'TypeScript interfaces',
          'ComponentName.test.tsx': 'Unit tests',
          'ComponentName.stories.tsx': 'Storybook stories',
          'hooks/': 'Component-specific hooks',
          'utils/': 'Component-specific utilities'
        }
      };
      
      // Component template
      template: string;
    }
    
    const componentTemplate = `
    import React from 'react';
    import { styled } from '@/styles';
    import { ComponentNameProps } from './ComponentName.types';
    import { useComponentLogic } from './hooks/useComponentLogic';
    
    const StyledContainer = styled('div', {
      // Base styles from design system
    });
    
    export const ComponentName: React.FC<ComponentNameProps> = ({
      variant = 'default',
      size = 'medium',
      children,
      ...props
    }) => {
      const { state, handlers } = useComponentLogic(props);
      
      return (
        <StyledContainer
          variant={variant}
          size={size}
          {...props}
        >
          {children}
        </StyledContainer>
      );
    };
    
    ComponentName.displayName = 'ComponentName';
    `;

#### 22.2 Component Guidelines

typescript

    // Component development guidelines
    const componentGuidelines = {
      // Naming conventions
      naming: {
        components: "PascalCase",
        files: "PascalCase",
        hooks: "camelCase with 'use' prefix",
        utils: "camelCase",
        types: "PascalCase with 'I' or 'T' prefix",
        props: "ComponentNameProps"
      },
      
      // Props design
      propsDesign: {
        required: "Minimize required props",
        defaults: "Provide sensible defaults",
        types: "Use TypeScript strictly",
        children: "Support when appropriate",
        callbacks: "Prefix with 'on'",
        booleans: "Prefix with 'is', 'has', 'should'"
      },
      
      // Composition patterns
      composition: {
        compoundComponents: `
          <Card>
            <Card.Header>Title</Card.Header>
            <Card.Body>Content</Card.Body>
            <Card.Footer>Actions</Card.Footer>
          </Card>
        `,
        
        renderProps: `
          <DataProvider>
            {({ data, loading }) => (
              <Component data={data} loading={loading} />
            )}
          </DataProvider>
        `,
        
        slots: `
          <Layout
            header={<Header />}
            sidebar={<Sidebar />}
            content={<Content />}
          />
        `
      },
      
      // State management
      stateManagement: {
        local: "useState for component state",
        shared: "Context for shared state",
        global: "Zustand for global state",
        server: "React Query for server state"
      },
      
      // Performance guidelines
      performance: {
        memoization: "React.memo for expensive renders",
        callbacks: "useCallback for stable references",
        values: "useMemo for expensive computations",
        lazy: "React.lazy for code splitting",
        virtualization: "For long lists"
      }
    };

### 23\. Theming System

#### 23.1 Theme Configuration

typescript

    // Theme system implementation
    const themeSystem = {
      // Theme structure
      theme: {
        colors: colorSystem,
        typography: typographySystem,
        spacing: spacingSystem,
        breakpoints: breakpointSystem,
        animations: animationSystem,
        
        components: {
          button: buttonComponent,
          input: inputComponent,
          card: cardComponent,
          // ... other component tokens
        }
      },
      
      // Theme provider
      ThemeProvider: `
        import { createContext, useContext } from 'react';
        
        const ThemeContext = createContext(defaultTheme);
        
        export const ThemeProvider = ({ theme, children }) => {
          return (
            <ThemeContext.Provider value={theme}>
              {children}
            </ThemeContext.Provider>
          );
        };
        
        export const useTheme = () => {
          const theme = useContext(ThemeContext);
          if (!theme) {
            throw new Error('useTheme must be used within ThemeProvider');
          }
          return theme;
        };
      `,
      
      // CSS variables
      cssVariables: {
        colors: `
          :root {
            --color-primary-50: #E6F2FF;
            --color-primary-100: #BAE0FF;
            --color-primary-200: #7AC2FF;
            --color-primary-300: #3AA4FF;
            --color-primary-400: #0084E3;
            --color-primary-500: #006BB3;
            --color-primary-600: #005A96;
            --color-primary-700: #004878;
            --color-primary-800: #00365A;
            --color-primary-900: #00243D;
            
            // ... other color variables
          }
        `,
        
        spacing: `
          :root {
            --space-0: 0px;
            --space-1: 8px;
            --space-2: 16px;
            --space-3: 24px;
            --space-4: 32px;
            --space-5: 40px;
            --space-6: 48px;
            --space-7: 56px;
            --space-8: 64px;
            
            // ... other spacing variables
          }
        `
      },
      
      // Dark mode support
      darkMode: {
        implementation: "CSS variables with data attribute",
        
        toggle: `
          [data-theme="dark"] {
            --color-background: #1A202C;
            --color-text-primary: #FFFFFF;
            // ... other dark mode overrides
          }
        `,
        
        detection: `
          const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
          const savedTheme = localStorage.getItem('theme');
          const theme = savedTheme || (prefersDark ? 'dark' : 'light');
        `
      }
    };

#### 23.2 Styled Components Integration

typescript

    // Styled components setup
    const styledComponentsSetup = {
      // Base configuration
      config: {
        shouldForwardProp: (prop: string) => !prop.startsWith('$'),
        prefix: 'ce', // clarity-engine prefix
      },
      
      // Utility functions
      utilities: {
        // Responsive styles
        responsive: `
          export const responsive = (property: string, values: ResponsiveValue) => {
            if (typeof values !== 'object') {
              return { [property]: values };
            }
            
            return Object.entries(values).reduce((acc, [breakpoint, value]) => {
              const mediaQuery = theme.breakpoints[breakpoint];
              return {
                ...acc,
                [mediaQuery]: { [property]: value }
              };
            }, {});
          };
        `,
        
        // Variant styles
        variant: `
          export const variant = (variants: VariantMap) => {
            return (props: any) => {
              const variantKey = props.variant || 'default';
              return variants[variantKey] || variants.default;
            };
          };
        `,
        
        // Space utilities
        space: `
          export const space = (value: SpaceValue) => {
            if (typeof value === 'number') {
              return theme.spacing[value] || value;
            }
            return value;
          };
        `
      },
      
      // Global styles
      globalStyles: `
        import { createGlobalStyle } from 'styled-components';
        
        export const GlobalStyles = createGlobalStyle\`
          * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
          }
          
          html {
            font-size: 16px;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
          }
          
          body {
            font-family: \${({ theme }) => theme.typography.fonts.primary.family};
            color: \${({ theme }) => theme.colors.text.primary};
            background-color: \${({ theme }) => theme.colors.background.primary};
            line-height: 1.5;
          }
          
          // ... other global styles
        \`;
      `
    };

### 24\. Performance Guidelines

#### 24.1 Performance Optimization

typescript

    // Performance optimization strategies
    const performanceOptimization = {
      // Bundle optimization
      bundleOptimization: {
        // Code splitting
        codeSplitting: {
          routes: "Lazy load route components",
          components: "Lazy load heavy components",
          libraries: "Dynamic import for large libraries"
        },
        
        // Tree shaking
        treeShaking: {
          imports: "Use specific imports",
          sideEffects: "Mark packages correctly",
          unused: "Remove unused exports"
        },
        
        // Bundle analysis
        analysis: {
          tool: "webpack-bundle-analyzer",
          target: {
            initial: "<200KB",
            lazy: "<100KB per chunk"
          }
        }
      },
      
      // Rendering optimization
      renderingOptimization: {
        // Component optimization
        components: {
          memo: "Use React.memo for pure components",
          keys: "Stable keys for lists",
          virtualization: "For lists >100 items"
        },
        
        // State optimization
        state: {
          lifting: "Lift state only when needed",
          splitting: "Split contexts by concern",
          batching: "Use React 18 automatic batching"
        },
        
        // Effect optimization
        effects: {
          dependencies: "Correct dependency arrays",
          cleanup: "Always cleanup subscriptions",
          throttle: "Throttle expensive operations"
        }
      },
      
      // Asset optimization
      assetOptimization: {
        // Images
        images: {
          formats: "WebP with fallbacks",
          lazy: "Lazy load below fold",
          responsive: "Multiple sizes with srcset",
          optimization: "Compress and optimize"
        },
        
        // Fonts
        fonts: {
          subset: "Include only used characters",
          preload: "Preload critical fonts",
          display: "font-display: swap",
          variable: "Use variable fonts"
        },
        
        // Icons
        icons: {
          sprites: "SVG sprites for many icons",
          inline: "Inline critical icons",
          lazy: "Lazy load decorative icons"
        }
      },
      
      // Runtime optimization
      runtimeOptimization: {
        // Caching
        caching: {
          api: "Cache API responses",
          computed: "Memoize expensive computations",
          components: "Cache rendered components"
        },
        
        // Debouncing
        debouncing: {
          search: "300ms delay",
          resize: "150ms delay",
          scroll: "100ms delay"
        },
        
        // Web Workers
        webWorkers: {
          calculations: "Heavy computations",
          parsing: "Large data parsing",
          offline: "Background sync"
        }
      }
    };

#### 24.2 Performance Monitoring

typescript

    // Performance monitoring setup
    const performanceMonitoring = {
      // Core Web Vitals
      coreWebVitals: {
        LCP: {
          target: "<2.5s",
          metric: "Largest Contentful Paint",
          optimization: [
            "Optimize server response time",
            "Preload critical resources",
            "Optimize images and fonts"
          ]
        },
        
        FID: {
          target: "<100ms",
          metric: "First Input Delay",
          optimization: [
            "Break up long tasks",
            "Optimize JavaScript execution",
            "Use web workers"
          ]
        },
        
        CLS: {
          target: "<0.1",
          metric: "Cumulative Layout Shift",
          optimization: [
            "Set dimensions for media",
            "Reserve space for dynamic content",
            "Avoid inserting content above existing content"
          ]
        }
      },
      
      // Custom metrics
      customMetrics: {
        calculatorLoad: {
          measure: "Time to interactive calculator",
          target: "<1s",
          tracking: "performance.mark() and measure()"
        },
        
        materialSearch: {
          measure: "Search response time",
          target: "<300ms",
          tracking: "API response times"
        },
        
        reportGeneration: {
          measure: "PDF generation time",
          target: "<5s",
          tracking: "Backend processing time"
        }
      },
      
      // Monitoring tools
      tools: {
        realUserMonitoring: {
          service: "Sentry Performance",
          sampling: "10% in production",
          alerts: "Set up for degradation"
        },
        
        syntheticMonitoring: {
          service: "Lighthouse CI",
          frequency: "On every PR",
          threshold: "Performance budget"
        }
      }
    };

### 25\. Testing & Documentation

#### 25.1 Testing Strategy

typescript

    // Comprehensive testing approach
    const testingStrategy = {
      // Unit testing
      unitTesting: {
        framework: "Jest + React Testing Library",
        coverage: {
          target: ">80%",
          critical: ">95% for utilities"
        },
        
        patterns: {
          components: "Test user interactions",
          hooks: "Test state changes",
          utilities: "Test edge cases"
        },
        
        example: `
          describe('Button Component', () => {
            it('should render with correct text', () => {
              render(<Button>Click me</Button>);
              expect(screen.getByRole('button')).toHaveTextContent('Click me');
            });
            
            it('should call onClick when clicked', () => {
              const handleClick = jest.fn();
              render(<Button onClick={handleClick}>Click me</Button>);
              fireEvent.click(screen.getByRole('button'));
              expect(handleClick).toHaveBeenCalledTimes(1);
            });
          });
        `
      },
      
      // Integration testing
      integrationTesting: {
        framework: "Cypress",
        coverage: "Critical user flows",
        
        flows: [
          "Complete calculation flow",
          "Material selection and comparison",
          "Project creation and management",
          "Report generation"
        ]
      },
      
      // Visual regression
      visualTesting: {
        tool: "Chromatic + Storybook",
        coverage: "All components",
        threshold: "0.01% difference",
        
        workflow: [
          "Capture baseline",
          "Detect changes",
          "Review and approve"
        ]
      },
      
      // Accessibility testing
      a11yTesting: {
        tools: ["jest-axe", "Cypress Axe", "Manual testing"],
        
        automated: {
          unit: "jest-axe in component tests",
          integration: "cypress-axe in E2E tests"
        },
        
        manual: {
          screenReader: "Test with NVDA/VoiceOver",
          keyboard: "Full keyboard navigation",
          contrast: "Color contrast analyzer"
        }
      }
    };

#### 25.2 Documentation Standards

typescript

    // Documentation requirements
    const documentationStandards = {
      // Component documentation
      componentDocs: {
        required: [
          "Component purpose",
          "Props documentation",
          "Usage examples",
          "Accessibility notes",
          "Performance considerations"
        ],
        
        format: `
          /**
           * Button component for user actions
           * 
           * @component
           * @example
           * <Button variant="primary" onClick={handleClick}>
           *   Click me
           * </Button>
           */
        `,
        
        storybook: {
          stories: "Interactive examples",
          controls: "Props playground",
          docs: "Auto-generated from comments"
        }
      },
      
      // API documentation
      apiDocs: {
        format: "JSDoc comments",
        
        example: `
          /**
           * Calculate construction cost
           * @param {CalculationInput} input - Project specifications
           * @returns {Promise<CalculationResult>} Detailed cost breakdown
           * @throws {ValidationError} If input is invalid
           */
        `
      },
      
      // Design system docs
      designSystemDocs: {
        structure: {
          introduction: "Philosophy and principles",
          foundations: "Colors, typography, spacing",
          components: "Component library",
          patterns: "Common UI patterns",
          resources: "Tools and assets"
        },
        
        maintenance: {
          versioning: "Semantic versioning",
          changelog: "Detailed change history",
          migration: "Upgrade guides"
        }
      }
    };

* * *

Design System & UI Component Library - Conclusion
-------------------------------------------------

This comprehensive Design System and UI Component Library provides the complete visual and interaction foundation for the Clarity Engine platform. With over 300 components, patterns, and guidelines, the development team has everything needed to build a consistent, accessible, and delightful user experience.

### Key Implementation Priorities

1.  **Set Up Foundation**: Implement color system, typography, and spacing first
2.  **Build Core Components**: Start with buttons, inputs, and cards
3.  **Create Complex Components**: Calculator and material selection interfaces
4.  **Implement Patterns**: Navigation, responsive layouts, and animations
5.  **Ensure Accessibility**: Test with screen readers and keyboard navigation
6.  **Document Everything**: Storybook for component documentation
7.  **Test Thoroughly**: Unit, integration, and visual regression tests
8.  **Monitor Performance**: Track Core Web Vitals and custom metrics

### Design System Maintenance

*   **Regular Reviews**: Monthly design system review meetings
*   **Version Control**: Semantic versioning for all changes
*   **Documentation**: Keep Storybook and docs up to date
*   **Feedback Loop**: Collect feedback from developers and users
*   **Evolution**: Adapt based on usage patterns and needs

The combination of thoughtful design principles, comprehensive component library, and clear implementation guidelines ensures that Clarity Engine will deliver a world-class user experience that builds trust and empowers users in their construction journey.

* * *

**Document Version:** 2.0  
**Last Updated:** November 2024  
**Total Pages:** 198  
**Status:** Final - Ready for Implementation  
**Next Review:** Post-MVP launch for feedback incorporation