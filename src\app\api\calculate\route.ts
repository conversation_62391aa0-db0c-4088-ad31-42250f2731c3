/**
 * Calculator API Route - Enhanced Version
 * Handles construction cost calculations with comprehensive validation,
 * error handling, rate limiting, and performance monitoring
 */

import { NextRequest, NextResponse } from 'next/server';
import { calculate, validateInput, sanitizeInput } from '@/core/calculator';
import type {
  CalculationInput,
  CalculationApiResponse,
} from '@/core/calculator/types';

// Rate limiting configuration (simple in-memory store for demo)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();
const RATE_LIMIT_WINDOW = 60 * 1000; // 1 minute
const RATE_LIMIT_MAX = 100; // 100 requests per minute per IP

// Performance monitoring
interface PerformanceMetrics {
  requestId: string;
  startTime: number;
  validationTime?: number;
  calculationTime?: number;
  totalTime?: number;
  inputSize: number;
  outputSize?: number;
}

/**
 * Enhanced POST handler with comprehensive validation and monitoring
 */
export async function POST(request: NextRequest) {
  const requestId = generateRequestId();
  const startTime = performance.now();
  const metrics: PerformanceMetrics = {
    requestId,
    startTime,
    inputSize: 0,
  };

  try {
    // Rate limiting check
    const clientIP = getClientIP(request);
    const rateLimitResult = checkRateLimit(clientIP);
    if (!rateLimitResult.allowed) {
      return NextResponse.json(
        {
          success: false,
          error: {
            type: 'rate_limit',
            message: 'Too many requests. Please try again later.',
            code: 'RATE_LIMIT_EXCEEDED',
            retryAfter: Math.ceil(rateLimitResult.retryAfter / 1000),
          },
          timestamp: new Date().toISOString(),
          requestId,
        } as CalculationApiResponse,
        {
          status: 429,
          headers: {
            'Retry-After': String(Math.ceil(rateLimitResult.retryAfter / 1000)),
            'X-RateLimit-Limit': String(RATE_LIMIT_MAX),
            'X-RateLimit-Remaining': String(rateLimitResult.remaining),
            'X-RateLimit-Reset': String(rateLimitResult.resetTime),
          }
        }
      );
    }

    // Parse and validate request body
    let body: any;
    try {
      const bodyText = await request.text();
      metrics.inputSize = bodyText.length;
      body = JSON.parse(bodyText);
    } catch (parseError) {
      return NextResponse.json(
        {
          success: false,
          error: {
            type: 'validation',
            message: 'Invalid JSON in request body',
            code: 'INVALID_JSON',
          },
          timestamp: new Date().toISOString(),
          requestId,
        } as CalculationApiResponse,
        { status: 400 }
      );
    }

    // Enhanced validation for required fields
    const requiredFields = ['builtUpArea', 'qualityTier', 'location'];
    const missingFields = requiredFields.filter(field =>
      body[field] === undefined || body[field] === null || body[field] === ''
    );

    if (missingFields.length > 0) {
      return NextResponse.json(
        {
          success: false,
          error: {
            type: 'validation',
            message: `Missing required fields: ${missingFields.join(', ')}`,
            code: 'MISSING_REQUIRED_FIELDS',
            details: {
              missingFields,
              requiredFields,
            },
          },
          timestamp: new Date().toISOString(),
          requestId,
        } as CalculationApiResponse,
        { status: 400 }
      );
    }

    // Input sanitization with comprehensive type checking
    const validationStart = performance.now();
    let input: CalculationInput;

    try {
      input = sanitizeInput({
        builtUpArea: parseFloat(body.builtUpArea),
        floors: body.floors ? parseInt(body.floors) : 0,
        qualityTier: body.qualityTier,
        location: body.location,
        plotArea: body.plotArea ? parseFloat(body.plotArea) : undefined,
        hasStilt: Boolean(body.hasStilt),
        parkingType: body.parkingType || undefined,
        hasBasement: Boolean(body.hasBasement),
        specialFeatures: Array.isArray(body.specialFeatures) ? body.specialFeatures : [],
      });
    } catch (sanitizeError) {
      return NextResponse.json(
        {
          success: false,
          error: {
            type: 'validation',
            message: 'Invalid input data types',
            code: 'INVALID_DATA_TYPES',
            details: sanitizeError instanceof Error ? sanitizeError.message : 'Unknown sanitization error',
          },
          timestamp: new Date().toISOString(),
          requestId,
        } as CalculationApiResponse,
        { status: 400 }
      );
    }

    // Comprehensive input validation
    const validationErrors = validateInput(input);
    metrics.validationTime = performance.now() - validationStart;

    if (validationErrors.length > 0) {
      return NextResponse.json(
        {
          success: false,
          error: {
            type: 'validation',
            message: 'Input validation failed',
            code: 'VALIDATION_FAILED',
            details: {
              errors: validationErrors,
              errorCount: validationErrors.length,
            },
          },
          timestamp: new Date().toISOString(),
          requestId,
        } as CalculationApiResponse,
        { status: 400 }
      );
    }

    // Perform calculation with timing
    const calculationStart = performance.now();
    const result = calculate(input);
    metrics.calculationTime = performance.now() - calculationStart;
    metrics.totalTime = performance.now() - startTime;

    // Calculate output size for monitoring
    const responseData = {
      success: true,
      data: result,
      timestamp: new Date().toISOString(),
      requestId,
      performance: {
        validationTime: Math.round(metrics.validationTime * 100) / 100,
        calculationTime: Math.round(metrics.calculationTime * 100) / 100,
        totalTime: Math.round(metrics.totalTime * 100) / 100,
      },
    } as CalculationApiResponse;

    metrics.outputSize = JSON.stringify(responseData).length;

    // Enhanced logging for analytics and monitoring
    logCalculationMetrics(input, result, metrics, clientIP);

    // Success response with performance headers
    return NextResponse.json(responseData, {
      status: 200,
      headers: {
        'Cache-Control': 'public, max-age=300, s-maxage=300',
        'Content-Type': 'application/json',
        'X-Request-ID': requestId,
        'X-Performance-Total': String(Math.round(metrics.totalTime)),
        'X-Performance-Calculation': String(Math.round(metrics.calculationTime)),
        'X-Performance-Validation': String(Math.round(metrics.validationTime)),
        'X-RateLimit-Limit': String(RATE_LIMIT_MAX),
        'X-RateLimit-Remaining': String(rateLimitResult.remaining - 1),
      },
    });

  } catch (error) {
    metrics.totalTime = performance.now() - startTime;

    // Enhanced error logging
    console.error('Calculation API error:', {
      requestId,
      error: error instanceof Error ? {
        name: error.name,
        message: error.message,
        stack: error.stack,
      } : error,
      metrics,
      timestamp: new Date().toISOString(),
    });

    // Determine error type and appropriate response
    const errorResponse = handleError(error, requestId);

    return NextResponse.json(errorResponse.data, {
      status: errorResponse.status,
      headers: {
        'X-Request-ID': requestId,
        'Content-Type': 'application/json',
      },
    });
  }
}

/**
 * Rate limiting implementation
 */
function checkRateLimit(clientIP: string): {
  allowed: boolean;
  remaining: number;
  retryAfter: number;
  resetTime: number;
} {
  const now = Date.now();
  const key = clientIP;
  const existing = rateLimitStore.get(key);

  if (!existing || now > existing.resetTime) {
    // Reset window
    const resetTime = now + RATE_LIMIT_WINDOW;
    rateLimitStore.set(key, { count: 1, resetTime });
    return {
      allowed: true,
      remaining: RATE_LIMIT_MAX - 1,
      retryAfter: 0,
      resetTime,
    };
  }

  if (existing.count >= RATE_LIMIT_MAX) {
    return {
      allowed: false,
      remaining: 0,
      retryAfter: existing.resetTime - now,
      resetTime: existing.resetTime,
    };
  }

  existing.count++;
  rateLimitStore.set(key, existing);

  return {
    allowed: true,
    remaining: RATE_LIMIT_MAX - existing.count,
    retryAfter: 0,
    resetTime: existing.resetTime,
  };
}

/**
 * Extract client IP address
 */
function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const real = request.headers.get('x-real-ip');
  const host = request.headers.get('host');

  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }
  if (real) {
    return real;
  }
  return host || 'unknown';
}

/**
 * Generate unique request ID
 */
function generateRequestId(): string {
  return `calc_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
}

/**
 * Enhanced error handling
 */
function handleError(error: unknown, requestId: string): {
  data: CalculationApiResponse;
  status: number;
} {
  if (error instanceof Error) {
    // Check for specific error types
    if (error.message.includes('Validation failed')) {
      return {
        data: {
          success: false,
          error: {
            type: 'validation',
            message: error.message,
            code: 'VALIDATION_ERROR',
          },
          timestamp: new Date().toISOString(),
          requestId,
        },
        status: 400,
      };
    }

    if (error.message.includes('Calculation failed') || error.message.includes('Division by zero')) {
      return {
        data: {
          success: false,
          error: {
            type: 'calculation',
            message: 'Calculation engine error',
            code: 'CALCULATION_ERROR',
            details: error.message,
          },
          timestamp: new Date().toISOString(),
          requestId,
        },
        status: 422,
      };
    }
  }

  // Generic server error
  return {
    data: {
      success: false,
      error: {
        type: 'server',
        message: 'Internal server error',
        code: 'INTERNAL_SERVER_ERROR',
      },
      timestamp: new Date().toISOString(),
      requestId,
    },
    status: 500,
  };
}

/**
 * Enhanced logging for monitoring and analytics
 */
function logCalculationMetrics(
  input: CalculationInput,
  result: any,
  metrics: PerformanceMetrics,
  clientIP: string
) {
  const logEntry = {
    event: 'calculation_completed',
    requestId: metrics.requestId,
    timestamp: new Date().toISOString(),
    input: {
      builtUpArea: input.builtUpArea,
      floors: input.floors,
      qualityTier: input.qualityTier,
      location: input.location,
      hasSpecialFeatures: input.specialFeatures && input.specialFeatures.length > 0,
    },
    result: {
      totalCost: result.totalCost,
      costPerSqft: result.costPerSqft,
      totalBuiltUpArea: result.summary?.totalBuiltUpArea,
    },
    performance: {
      validationTime: metrics.validationTime,
      calculationTime: metrics.calculationTime,
      totalTime: metrics.totalTime,
      inputSize: metrics.inputSize,
      outputSize: metrics.outputSize,
    },
    client: {
      ip: clientIP,
      userAgent: 'browser', // Would extract from headers in production
    },
  };

  // In production, send to monitoring service (DataDog, New Relic, etc.)
  console.info('Calculation metrics:', logEntry);
}

// Handle OPTIONS request for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}

// Optional: Handle GET request for API documentation
export async function GET() {
  const apiDoc = {
    name: 'Clarity Engine Calculator API',
    version: '1.0.0',
    description: 'Construction cost calculation API for Indian market',
    endpoints: {
      POST: {
        description: 'Calculate construction costs',
        required_fields: ['builtUpArea', 'qualityTier', 'location'],
        optional_fields: [
          'floors',
          'plotArea',
          'hasStilt',
          'parkingType',
          'hasBasement',
          'specialFeatures',
        ],
        response: 'CalculationResult with cost breakdown and materials list',
      },
    },
    supported_locations: [
      'bangalore',
      'mumbai',
      'delhi',
      'pune',
      'hyderabad',
      'chennai',
      'kolkata',
      'ahmedabad',
      'jaipur',
      'lucknow',
      'bhubaneswar',
    ],
    quality_tiers: {
      smart: '₹1,600-2,000 per sqft - Standard finishes',
      premium: '₹2,200-2,800 per sqft - Branded materials',
      luxury: '₹3,000-4,000+ per sqft - Premium finishes',
    },
    example_request: {
      builtUpArea: 1200,
      floors: 1,
      qualityTier: 'premium',
      location: 'bangalore',
    },
  };

  return NextResponse.json(apiDoc, {
    headers: {
      'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
    },
  });
}
