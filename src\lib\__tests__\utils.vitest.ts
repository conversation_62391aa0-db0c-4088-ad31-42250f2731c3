import { describe, it, expect, vi } from 'vitest';
import { cn, formatCurrency, formatNumber, validateEmail, validatePhone, debounce, throttle } from '../utils';

describe('Utility Functions', () => {
  describe('cn (className utility)', () => {
    it('should merge class names correctly', () => {
      expect(cn('class1', 'class2')).toBe('class1 class2');
    });

    it('should handle conditional classes', () => {
      expect(cn('class1', false && 'class2', 'class3')).toBe('class1 class3');
      expect(cn('class1', true && 'class2', 'class3')).toBe('class1 class2 class3');
    });

    it('should handle undefined and null values', () => {
      expect(cn('class1', undefined, null, 'class2')).toBe('class1 class2');
    });

    it('should handle empty strings', () => {
      expect(cn('class1', '', 'class2')).toBe('class1 class2');
    });

    it('should handle arrays', () => {
      expect(cn(['class1', 'class2'], 'class3')).toBe('class1 class2 class3');
    });

    it('should handle objects', () => {
      expect(cn({ class1: true, class2: false, class3: true })).toBe('class1 class3');
    });
  });

  describe('formatCurrency', () => {
    it('should format currency correctly', () => {
      expect(formatCurrency(1000)).toBe('₹1,000');
      expect(formatCurrency(1000000)).toBe('₹10,00,000');
      expect(formatCurrency(1000000000)).toBe('₹1,00,00,00,000');
    });

    it('should handle decimal values', () => {
      expect(formatCurrency(1000.50)).toBe('₹1,000.50');
      expect(formatCurrency(1000.99)).toBe('₹1,000.99');
    });

    it('should handle zero', () => {
      expect(formatCurrency(0)).toBe('₹0');
    });

    it('should handle negative values', () => {
      expect(formatCurrency(-1000)).toBe('-₹1,000');
    });

    it('should handle large numbers', () => {
      expect(formatCurrency(999999999999)).toBe('₹9,99,99,99,99,999');
    });

    it('should handle different currencies', () => {
      expect(formatCurrency(1000, 'USD')).toBe('$1,000');
      expect(formatCurrency(1000, 'EUR')).toBe('€1,000');
    });
  });

  describe('formatNumber', () => {
    it('should format numbers correctly', () => {
      expect(formatNumber(1000)).toBe('1,000');
      expect(formatNumber(1000000)).toBe('10,00,000');
      expect(formatNumber(1000000000)).toBe('1,00,00,00,000');
    });

    it('should handle decimal values', () => {
      expect(formatNumber(1000.50)).toBe('1,000.50');
      expect(formatNumber(1000.99)).toBe('1,000.99');
    });

    it('should handle zero', () => {
      expect(formatNumber(0)).toBe('0');
    });

    it('should handle negative values', () => {
      expect(formatNumber(-1000)).toBe('-1,000');
    });

    it('should handle precision', () => {
      expect(formatNumber(1000.12345, 2)).toBe('1,000.12');
      expect(formatNumber(1000.12345, 0)).toBe('1,000');
    });
  });

  describe('validateEmail', () => {
    it('should validate correct email addresses', () => {
      expect(validateEmail('<EMAIL>')).toBe(true);
      expect(validateEmail('<EMAIL>')).toBe(true);
      expect(validateEmail('<EMAIL>')).toBe(true);
      expect(validateEmail('<EMAIL>')).toBe(true);
    });

    it('should reject invalid email addresses', () => {
      expect(validateEmail('invalid-email')).toBe(false);
      expect(validateEmail('test@')).toBe(false);
      expect(validateEmail('@example.com')).toBe(false);
      expect(validateEmail('test.example.com')).toBe(false);
      expect(validateEmail('test@example')).toBe(false);
      expect(validateEmail('')).toBe(false);
    });

    it('should handle edge cases', () => {
      expect(validateEmail('<EMAIL>')).toBe(false);
      expect(validateEmail('<EMAIL>')).toBe(false);
      expect(validateEmail('<EMAIL>')).toBe(false);
      expect(validateEmail('<EMAIL>.')).toBe(false);
    });
  });

  describe('validatePhone', () => {
    it('should validate correct Indian phone numbers', () => {
      expect(validatePhone('+91-9876543210')).toBe(true);
      expect(validatePhone('+************')).toBe(true);
      expect(validatePhone('9876543210')).toBe(true);
      expect(validatePhone('91-9876543210')).toBe(true);
      expect(validatePhone('(+91) 9876543210')).toBe(true);
    });

    it('should reject invalid phone numbers', () => {
      expect(validatePhone('123')).toBe(false);
      expect(validatePhone('12345678901234567890')).toBe(false);
      expect(validatePhone('abcdefghij')).toBe(false);
      expect(validatePhone('')).toBe(false);
      expect(validatePhone('+91-123')).toBe(false);
    });

    it('should handle different formats', () => {
      expect(validatePhone('91 9876543210')).toBe(true);
      expect(validatePhone('91-9876-543-210')).toBe(true);
      expect(validatePhone('(91) 9876543210')).toBe(true);
    });
  });

  describe('debounce', () => {
    it('should debounce function calls', async () => {
      const mockFn = vi.fn();
      const debouncedFn = debounce(mockFn, 100);

      debouncedFn();
      debouncedFn();
      debouncedFn();

      expect(mockFn).not.toHaveBeenCalled();

      await new Promise(resolve => setTimeout(resolve, 150));

      expect(mockFn).toHaveBeenCalledTimes(1);
    });

    it('should pass arguments correctly', async () => {
      const mockFn = vi.fn();
      const debouncedFn = debounce(mockFn, 100);

      debouncedFn('arg1', 'arg2');

      await new Promise(resolve => setTimeout(resolve, 150));

      expect(mockFn).toHaveBeenCalledWith('arg1', 'arg2');
    });

    it('should reset timer on subsequent calls', async () => {
      const mockFn = vi.fn();
      const debouncedFn = debounce(mockFn, 100);

      debouncedFn();
      
      setTimeout(() => debouncedFn(), 50);
      setTimeout(() => debouncedFn(), 100);

      await new Promise(resolve => setTimeout(resolve, 250));

      expect(mockFn).toHaveBeenCalledTimes(1);
    });

    it('should handle immediate option', () => {
      const mockFn = vi.fn();
      const debouncedFn = debounce(mockFn, 100, true);

      debouncedFn();

      expect(mockFn).toHaveBeenCalledTimes(1);

      debouncedFn();
      debouncedFn();

      expect(mockFn).toHaveBeenCalledTimes(1);
    });
  });

  describe('throttle', () => {
    it('should throttle function calls', async () => {
      const mockFn = vi.fn();
      const throttledFn = throttle(mockFn, 100);

      throttledFn();
      throttledFn();
      throttledFn();

      expect(mockFn).toHaveBeenCalledTimes(1);

      await new Promise(resolve => setTimeout(resolve, 150));

      throttledFn();

      expect(mockFn).toHaveBeenCalledTimes(2);
    });

    it('should pass arguments correctly', () => {
      const mockFn = vi.fn();
      const throttledFn = throttle(mockFn, 100);

      throttledFn('arg1', 'arg2');

      expect(mockFn).toHaveBeenCalledWith('arg1', 'arg2');
    });

    it('should maintain context', () => {
      const obj = {
        value: 'test',
        method: vi.fn(function(this: any) {
          return this.value;
        }),
      };

      const throttledMethod = throttle(obj.method.bind(obj), 100);

      throttledMethod();

      expect(obj.method).toHaveBeenCalled();
    });
  });

  describe('Performance Tests', () => {
    it('should handle large number formatting efficiently', () => {
      const start = performance.now();
      
      for (let i = 0; i < 1000; i++) {
        formatCurrency(Math.random() * 10000000);
      }
      
      const end = performance.now();
      
      expect(end - start).toBeLessThan(100);
    });

    it('should handle email validation efficiently', () => {
      const emails = [
        '<EMAIL>',
        'invalid.email',
        '<EMAIL>',
        '<EMAIL>',
        'invalid@',
        '@example.com',
      ];

      const start = performance.now();
      
      for (let i = 0; i < 1000; i++) {
        validateEmail(emails[i % emails.length]);
      }
      
      const end = performance.now();
      
      expect(end - start).toBeLessThan(50);
    });

    it('should handle phone validation efficiently', () => {
      const phones = [
        '+91-9876543210',
        '9876543210',
        'invalid-phone',
        '91-9876543210',
        '123',
        'abcdefghij',
      ];

      const start = performance.now();
      
      for (let i = 0; i < 1000; i++) {
        validatePhone(phones[i % phones.length]);
      }
      
      const end = performance.now();
      
      expect(end - start).toBeLessThan(50);
    });
  });

  describe('Edge Cases', () => {
    it('should handle undefined and null values in formatCurrency', () => {
      expect(formatCurrency(undefined as any)).toBe('₹0');
      expect(formatCurrency(null as any)).toBe('₹0');
    });

    it('should handle NaN in formatCurrency', () => {
      expect(formatCurrency(NaN)).toBe('₹0');
    });

    it('should handle Infinity in formatCurrency', () => {
      expect(formatCurrency(Infinity)).toBe('₹∞');
      expect(formatCurrency(-Infinity)).toBe('-₹∞');
    });

    it('should handle empty string in validation functions', () => {
      expect(validateEmail('')).toBe(false);
      expect(validatePhone('')).toBe(false);
    });

    it('should handle null and undefined in validation functions', () => {
      expect(validateEmail(null as any)).toBe(false);
      expect(validateEmail(undefined as any)).toBe(false);
      expect(validatePhone(null as any)).toBe(false);
      expect(validatePhone(undefined as any)).toBe(false);
    });

    it('should handle very long strings in validation functions', () => {
      const longString = 'a'.repeat(1000);
      expect(validateEmail(longString + '@example.com')).toBe(false);
      expect(validatePhone(longString)).toBe(false);
    });
  });

  describe('Memory Usage', () => {
    it('should not create memory leaks with debounce', async () => {
      const mockFn = vi.fn();
      
      // Create many debounced functions
      for (let i = 0; i < 1000; i++) {
        const debouncedFn = debounce(mockFn, 10);
        debouncedFn();
      }
      
      // Wait for all to complete
      await new Promise(resolve => setTimeout(resolve, 50));
      
      // Should complete without memory issues
      expect(mockFn).toHaveBeenCalledTimes(1000);
    });

    it('should not create memory leaks with throttle', async () => {
      const mockFn = vi.fn();
      
      // Create many throttled functions
      for (let i = 0; i < 1000; i++) {
        const throttledFn = throttle(mockFn, 10);
        throttledFn();
      }
      
      // Wait for all to complete
      await new Promise(resolve => setTimeout(resolve, 50));
      
      // Should complete without memory issues
      expect(mockFn).toHaveBeenCalled();
    });
  });
});