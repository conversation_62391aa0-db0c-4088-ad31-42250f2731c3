/**
 * Enhanced PDF Export Hook
 * Provides state management and UX improvements for PDF export
 */

import { useState, useCallback, useRef } from 'react';
import { 
  exportCalculationToPDFEnhanced, 
  exportCalculationToPDFWithRetry,
  validatePDFExportData,
  estimatePDFGenerationTime,
  getPDFExportAnalytics
} from '@/lib/pdf/enhanced-generator';
import type { CalculationInput, CalculationResult } from '@/core/calculator/types';
import type { PDFExportOptions, PDFExportResult } from '@/lib/pdf/enhanced-generator';

export interface PDFExportState {
  isGenerating: boolean;
  progress: number;
  error: Error | null;
  success: boolean;
  filename?: string;
  duration?: number;
  estimatedTime?: number;
}

export interface UsePDFExportOptions {
  enableRetry?: boolean;
  maxRetries?: number;
  enableProgress?: boolean;
  enableAnalytics?: boolean;
  onSuccess?: (filename: string) => void;
  onError?: (error: Error) => void;
  onProgress?: (progress: number) => void;
}

export interface UsePDFExportReturn {
  state: PDFExportState;
  exportPDF: (
    input: CalculationInput,
    result: CalculationResult,
    options?: PDFExportOptions
  ) => Promise<PDFExportResult>;
  reset: () => void;
  cancel: () => void;
  validateData: (input: CalculationInput, result: CalculationResult) => string[];
}

export function usePDFExport(options: UsePDFExportOptions = {}): UsePDFExportReturn {
  const {
    enableRetry = true,
    maxRetries = 3,
    enableProgress = true,
    enableAnalytics = true,
    onSuccess: onSuccessCallback,
    onError: onErrorCallback,
    onProgress: onProgressCallback
  } = options;

  const [state, setState] = useState<PDFExportState>({
    isGenerating: false,
    progress: 0,
    error: null,
    success: false
  });

  const cancelledRef = useRef(false);

  const updateState = useCallback((updates: Partial<PDFExportState>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  const handleProgress = useCallback((progress: number) => {
    if (cancelledRef.current) return;
    
    updateState({ progress });
    onProgressCallback?.(progress);
  }, [updateState, onProgressCallback]);

  const handleSuccess = useCallback((filename: string) => {
    if (cancelledRef.current) return;
    
    updateState({ 
      success: true, 
      filename,
      progress: 100 
    });
    onSuccessCallback?.(filename);
  }, [updateState, onSuccessCallback]);

  const handleError = useCallback((error: Error) => {
    if (cancelledRef.current) return;
    
    updateState({ 
      error, 
      success: false,
      progress: 0 
    });
    onErrorCallback?.(error);
  }, [updateState, onErrorCallback]);

  const validateData = useCallback((input: CalculationInput, result: CalculationResult): string[] => {
    return validatePDFExportData(input, result);
  }, []);

  const exportPDF = useCallback(async (
    input: CalculationInput,
    result: CalculationResult,
    exportOptions: PDFExportOptions = {}
  ): Promise<PDFExportResult> => {
    // Reset state
    cancelledRef.current = false;
    updateState({
      isGenerating: true,
      progress: 0,
      error: null,
      success: false,
      filename: undefined,
      duration: undefined
    });

    // Validate data
    const validationErrors = validateData(input, result);
    if (validationErrors.length > 0) {
      const error = new Error(`Validation failed: ${validationErrors.join(', ')}`);
      handleError(error);
      return { success: false, error };
    }

    // Estimate completion time
    if (enableProgress) {
      const estimatedTime = estimatePDFGenerationTime(exportOptions);
      updateState({ estimatedTime });
    }

    // Track analytics
    if (enableAnalytics) {
      const analytics = getPDFExportAnalytics(input, result);
      console.log('PDF Export Analytics:', analytics);
    }

    try {
      const exportFunction = enableRetry 
        ? exportCalculationToPDFWithRetry
        : exportCalculationToPDFEnhanced;

      const exportResult = await exportFunction(
        input,
        result,
        {
          ...exportOptions,
          onProgress: enableProgress ? handleProgress : undefined,
          onSuccess: handleSuccess,
          onError: handleError
        },
        maxRetries
      );

      if (exportResult.success) {
        updateState({
          isGenerating: false,
          success: true,
          filename: exportResult.filename,
          duration: exportResult.duration,
          progress: 100
        });
      } else {
        updateState({
          isGenerating: false,
          success: false,
          error: exportResult.error || new Error('PDF generation failed'),
          progress: 0
        });
      }

      return exportResult;

    } catch (error) {
      const err = error instanceof Error ? error : new Error('PDF export failed');
      
      updateState({
        isGenerating: false,
        success: false,
        error: err,
        progress: 0
      });

      return { success: false, error: err };
    }
  }, [
    enableRetry,
    maxRetries,
    enableProgress,
    enableAnalytics,
    updateState,
    validateData,
    handleProgress,
    handleSuccess,
    handleError
  ]);

  const reset = useCallback(() => {
    cancelledRef.current = false;
    setState({
      isGenerating: false,
      progress: 0,
      error: null,
      success: false,
      filename: undefined,
      duration: undefined,
      estimatedTime: undefined
    });
  }, []);

  const cancel = useCallback(() => {
    cancelledRef.current = true;
    updateState({
      isGenerating: false,
      progress: 0,
      error: new Error('PDF generation cancelled'),
      success: false
    });
  }, [updateState]);

  return {
    state,
    exportPDF,
    reset,
    cancel,
    validateData
  };
}

/**
 * Helper hook for PDF export with notifications
 */
export function usePDFExportWithNotifications() {
  const showNotification = useCallback((message: string, type: 'success' | 'error' | 'info') => {
    // This would integrate with your notification system
    console.log(`[${type.toUpperCase()}] ${message}`);
  }, []);

  return usePDFExport({
    enableRetry: true,
    enableProgress: true,
    enableAnalytics: true,
    onSuccess: (filename) => {
      showNotification(`PDF report "${filename}" generated successfully!`, 'success');
    },
    onError: (error) => {
      showNotification(`PDF generation failed: ${error.message}`, 'error');
    },
    onProgress: (progress) => {
      if (progress === 100) {
        showNotification('PDF generation completed!', 'success');
      }
    }
  });
}