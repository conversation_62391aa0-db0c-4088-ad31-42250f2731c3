import React from 'react';
import { Search, Home, Calculator } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import Link from 'next/link';

export default function NotFound() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4">
      <Card className="w-full max-w-lg">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 p-3 bg-blue-100 rounded-full w-fit">
            <Search className="h-10 w-10 text-blue-600" />
          </div>
          <CardTitle className="text-2xl text-gray-900">Page Not Found</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="text-center">
            <p className="text-gray-600 mb-2">
              The page you&apos;re looking for doesn&apos;t exist or has been moved.
            </p>
            <p className="text-sm text-gray-500">
              Let&apos;s get you back on track with these helpful links.
            </p>
          </div>

          <div className="space-y-3">
            <Link href="/" className="block">
              <Button variant="default" className="w-full">
                <Home className="mr-2 h-4 w-4" />
                Go to Homepage
              </Button>
            </Link>
            
            <Link href="/calculator" className="block">
              <Button variant="outline" className="w-full">
                <Calculator className="mr-2 h-4 w-4" />
                Construction Calculator
              </Button>
            </Link>
          </div>

          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-sm font-medium text-gray-900 mb-2">Popular Pages</h3>
            <div className="space-y-1">
              <Link href="/about" className="block text-sm text-blue-600 hover:text-blue-800">
                About Clarity Engine
              </Link>
              <Link href="/contact" className="block text-sm text-blue-600 hover:text-blue-800">
                Contact Support
              </Link>
            </div>
          </div>

          <div className="text-center">
            <p className="text-xs text-gray-400">
              Error 404 - Page Not Found
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}