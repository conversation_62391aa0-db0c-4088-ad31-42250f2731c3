'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { AnimatedButton } from '@/components/ui/animated-button';
import { CountUp } from '@/components/ui/count-up';
import { 
  fadeInUp, 
  staggerContainer, 
  staggerItem, 
  scaleIn 
} from '@/lib/animations';

export function AnimationTest() {
  const [count, setCount] = useState(0);
  const [showNumbers, setShowNumbers] = useState(false);

  return (
    <div className="max-w-4xl mx-auto p-8 space-y-8">
      <motion.div
        variants={fadeInUp}
        initial="initial"
        animate="animate"
      >
        <Card>
          <CardHeader>
            <CardTitle>Animation Test Suite</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            
            {/* Button Tests */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Button Animations</h3>
              <div className="flex gap-4">
                <AnimatedButton onClick={() => setCount(count + 1)}>
                  Hover Me ({count})
                </AnimatedButton>
                <AnimatedButton 
                  variant="outline"
                  loading={false}
                  onClick={() => setShowNumbers(!showNumbers)}
                >
                  Toggle Numbers
                </AnimatedButton>
                <AnimatedButton variant="destructive" disabled>
                  Disabled Button
                </AnimatedButton>
              </div>
            </div>

            {/* Count Up Numbers */}
            {showNumbers && (
              <motion.div
                variants={scaleIn}
                initial="initial"
                animate="animate"
                className="space-y-4"
              >
                <h3 className="text-lg font-semibold">Count Up Animations</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Card>
                    <CardContent className="p-4 text-center">
                      <div className="text-3xl font-bold text-blue-600">
                        <CountUp 
                          value={1896}
                          prefix="₹"
                          separator=","
                          duration={2}
                        />
                      </div>
                      <p className="text-sm text-gray-600">Cost per sqft</p>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardContent className="p-4 text-center">
                      <div className="text-3xl font-bold text-green-600">
                        <CountUp 
                          value={2500}
                          suffix=" sqft"
                          separator=","
                          duration={1.5}
                          delay={0.5}
                        />
                      </div>
                      <p className="text-sm text-gray-600">Built-up Area</p>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardContent className="p-4 text-center">
                      <div className="text-3xl font-bold text-purple-600">
                        <CountUp 
                          value={12}
                          suffix=" months"
                          duration={1}
                          delay={1}
                        />
                      </div>
                      <p className="text-sm text-gray-600">Duration</p>
                    </CardContent>
                  </Card>
                </div>
              </motion.div>
            )}

            {/* Stagger Animation */}
            <motion.div
              variants={staggerContainer}
              initial="initial"
              animate="animate"
              className="space-y-4"
            >
              <h3 className="text-lg font-semibold">Stagger Animations</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {['Structure', 'Finishing', 'MEP', 'External'].map((item, index) => (
                  <motion.div key={item} variants={staggerItem}>
                    <Card className="transition-all duration-200 hover:shadow-lg">
                      <CardContent className="p-4">
                        <div className="flex justify-between items-center">
                          <span className="font-medium">{item}</span>
                          <span className="text-2xl font-bold">
                            {25 + index * 5}%
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                          <motion.div
                            className="bg-blue-500 h-2 rounded-full"
                            initial={{ width: 0 }}
                            animate={{ width: `${25 + index * 5}%` }}
                            transition={{ duration: 1, delay: 0.5 + index * 0.2 }}
                          />
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            {/* Loading Animation */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Loading States</h3>
              <div className="flex gap-4">
                <AnimatedButton loading loadingText="Calculating...">
                  Loading Button
                </AnimatedButton>
                
                <motion.div
                  className="h-8 w-8 border-4 border-blue-500 border-t-transparent rounded-full"
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
                />
              </div>
            </div>

          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}