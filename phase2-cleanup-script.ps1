# Phase 2 Cleanup Script - Code Structure Optimization
# This script will clean up redundant files and improve code organization

Write-Host "🧹 Starting Phase 2 Cleanup - Code Structure Optimization..." -ForegroundColor Green

# Create backup first
Write-Host "Creating backup branch..." -ForegroundColor Cyan
git add -A 2>$null
git commit -m "Backup before Phase 2 cleanup" 2>$null

# 1. Remove redundant documentation files
Write-Host "Removing redundant documentation files..." -ForegroundColor Yellow
$redundantDocs = @(
    "ACTUAL_AUTOMATED_UI_TESTING_REPORT.md",
    "API_TESTING_EXECUTIVE_SUMMARY.md", 
    "API_TESTING_GUIDE.md",
    "BUNDLE_OPTIMIZATION_REPORT.md",
    "OPENAPI_SPEC.yaml"
)
foreach ($file in $redundantDocs) {
    if (Test-Path $file) { 
        Write-Host "  Removing $file" -ForegroundColor Gray
        Remove-Item -Force $file 
    }
}

# 2. Remove duplicate calculator components (keep main ones)
Write-Host "Removing duplicate calculator components..." -ForegroundColor Yellow
$duplicateCalculatorComponents = @(
    "src/components/calculator/EnhancedCalculatorDemo.tsx",
    "src/components/calculator/LazyCalculatorContainer.tsx",
    "src/components/calculator/OptimizedCalculatorContainer.tsx"
)
foreach ($file in $duplicateCalculatorComponents) {
    if (Test-Path $file) { 
        Write-Host "  Removing $file" -ForegroundColor Gray
        Remove-Item -Force $file 
    }
}

# 3. Remove duplicate layout components (keep main ones)
Write-Host "Removing duplicate layout components..." -ForegroundColor Yellow
$duplicateLayoutComponents = @(
    "src/components/layout/SimpleFooter.tsx",
    "src/components/layout/SimpleHeader.tsx"
)
foreach ($file in $duplicateLayoutComponents) {
    if (Test-Path $file) { 
        Write-Host "  Removing $file" -ForegroundColor Gray
        Remove-Item -Force $file 
    }
}

# 4. Remove test components (move to test directory if needed)
Write-Host "Removing test components from main components..." -ForegroundColor Yellow
if (Test-Path "src/components/test") { 
    Write-Host "  Removing src/components/test directory" -ForegroundColor Gray
    Remove-Item -Recurse -Force "src/components/test" 
}

# 5. Remove duplicate performance components
Write-Host "Removing duplicate performance components..." -ForegroundColor Yellow
if (Test-Path "src/components/performance/ComprehensivePerformanceDashboard.tsx") {
    Write-Host "  Removing ComprehensivePerformanceDashboard.tsx (keeping PerformanceDashboard.tsx)" -ForegroundColor Gray
    Remove-Item -Force "src/components/performance/ComprehensivePerformanceDashboard.tsx"
}

# 6. Clean up duplicate utility files
Write-Host "Removing duplicate utility files..." -ForegroundColor Yellow
$duplicateUtils = @(
    "src/lib/utils/optimized-utils.ts",
    "src/lib/utils/performance-monitor.ts"
)
foreach ($file in $duplicateUtils) {
    if (Test-Path $file) { 
        Write-Host "  Removing $file" -ForegroundColor Gray
        Remove-Item -Force $file 
    }
}

# 7. Remove duplicate mobile files
Write-Host "Removing duplicate mobile files..." -ForegroundColor Yellow
if (Test-Path "src/lib/mobile.ts") {
    Write-Host "  Removing src/lib/mobile.ts (keeping src/lib/mobile/ directory)" -ForegroundColor Gray
    Remove-Item -Force "src/lib/mobile.ts"
}

# 8. Clean up package.json scripts (remove references to deleted configs)
Write-Host "Cleaning up package.json scripts..." -ForegroundColor Yellow
$packageJson = Get-Content "package.json" -Raw | ConvertFrom-Json
$scriptsToRemove = @(
    "test:e2e:enhanced",
    "test:e2e:mobile", 
    "test:e2e:performance",
    "test:e2e:accessibility",
    "test:e2e:visual",
    "test:performance:regression",
    "test:mobile",
    "test:visual"
)

foreach ($script in $scriptsToRemove) {
    if ($packageJson.scripts.PSObject.Properties.Name -contains $script) {
        $packageJson.scripts.PSObject.Properties.Remove($script)
        Write-Host "  Removed script: $script" -ForegroundColor Gray
    }
}

# Save cleaned package.json
$packageJson | ConvertTo-Json -Depth 10 | Set-Content "package.json"

# 9. Remove empty directories
Write-Host "Cleaning up empty directories..." -ForegroundColor Yellow
Get-ChildItem -Path . -Recurse -Directory | Where-Object { 
    (Get-ChildItem -Path $_.FullName -Recurse -File).Count -eq 0 
} | Remove-Item -Recurse -Force -ErrorAction SilentlyContinue

# 10. Update .gitignore to prevent future bloat
Write-Host "Updating .gitignore..." -ForegroundColor Yellow
$gitignoreContent = @"
# Dependencies
node_modules/
.pnp
.pnp.js

# Testing
coverage/
.nyc_output/
test-results/
playwright-report/
playwright/.cache/
*.log

# Next.js
.next/
out/
build/
dist/

# Production
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Cache
.turbo
.cache
.vercel
tsconfig.tsbuildinfo

# Environment
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Temporary
tmp/
temp/
*.tmp
*.pid

# Build artifacts
storybook-static/

# Reports (generated)
*-report-*.json
*-report-*.html
lighthouse-*.html
bundle-report.*
security-validation-results.json
"@

Set-Content -Path ".gitignore" -Value $gitignoreContent

Write-Host "✅ Phase 2 Cleanup completed!" -ForegroundColor Green
Write-Host "💡 Code structure optimized and redundant files removed." -ForegroundColor Cyan
