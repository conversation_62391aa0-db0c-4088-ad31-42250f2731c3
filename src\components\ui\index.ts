// UI Components Barrel Export
export { Button } from './button';
export { Input } from './input';
export { Label } from './label';
export { Card, CardContent, CardHeader, CardTitle, CardDescription } from './card';
export { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './select';
export { Badge } from './badge';
export { Alert } from './alert';
export { Progress } from './progress';
export { Skeleton } from './skeleton';
export { Tabs } from './tabs';

// Enhanced MVP Components
export { 
  EnhancedButton,
  type ButtonProps as EnhancedButtonProps,
  type ButtonVariant,
  type ButtonSize 
} from './enhanced-button';

export { 
  EnhancedInput,
  type InputProps as EnhancedInputProps,
  type InputVariant,
  type InputSize 
} from './enhanced-input';

export { 
  EnhancedCard,
  CardHeader as EnhancedCardHeader,
  CardTitle as EnhancedCardTitle,
  CardDescription as EnhancedCardDescription,
  CardContent as EnhancedCardContent,
  Card<PERSON>ooter as EnhancedCardFooter,
  type CardProps as EnhancedCardProps,
  type CardVariant,
  type CardSize 
} from './enhanced-card';

export { 
  EnhancedSelect,
  type SelectProps as EnhancedSelectProps,
  type SelectOption,
  type SelectVariant,
  type SelectSize 
} from './enhanced-select';

export { 
  EnhancedProgress,
  CircularProgress,
  type ProgressProps as EnhancedProgressProps,
  type CircularProgressProps,
  type ProgressVariant,
  type ProgressSize 
} from './enhanced-progress';

// Typography Components
export { 
  Typography, 
  H1, H2, H3, H4, H5, H6,
  Body, BodyLarge, BodySmall, Lead, Caption, Overline, Muted,
  Code, Blockquote, GradientText, TypographyShowcase,
  type TypographyProps, type TypographyVariant, type TypographyElement 
} from './typography';

// Mobile-specific components
export { MobileInput } from './mobile-input';
export { MobileSheet } from './mobile-sheet';
export { TouchButton } from './touch-button';
export { GestureCard } from './gesture-card';
export { PullToRefresh } from './pull-to-refresh';

// Accessibility components
export { AccessibleButton, CalculateButton, ResetButton } from './accessible-button';
export { AccessibleLoading } from './accessible-loading';

// Animation components
export { AnimatedButton } from './animated-button';
export { AnimatedInput } from './animated-input';
export { OptimizedMotion } from './optimized-motion';
export { SmartTransitions } from './smart-transitions';

// Utility components
export { CalculatorErrorBoundary } from './error-boundary';
export { LoadingCard, ErrorState, LoadingSpinner } from './loading-states';
export { StatusBadge, StepIndicator } from './status-indicators';
export { CountUp } from './count-up';
export { PDFExportButton } from './pdf-export-button';
export { OptimizedImage } from './optimized-image';
export { ProgressiveImage } from './progressive-image';
export { ScrollArea } from './scroll-area';
export { SwipeableCards, QualityTierSelector } from './swipeable-cards';
export { RefreshableContainer } from './pull-to-refresh';
