Core Architecture Decisions
1. Project Structure for AI Agents
typescriptconst projectStructure = {
  repository: "clarity-engine",
  architecture: "Modular Monorepo", // Best for AI agents
  
  structure: `
clarity-engine/
├── .github/
│   └── workflows/         # CI/CD
├── _agents/              # AI Agent workspace
│   ├── context/          # Shared context files
│   │   ├── STATUS.md     # Master status tracker
│   │   ├── ARCHITECTURE.md
│   │   └── DECISIONS.md
│   ├── tasks/            # Task definitions
│   │   ├── completed/
│   │   ├── in-progress/
│   │   └── pending/
│   └── logs/             # Agent activity logs
├── src/
│   ├── core/             # Core business logic
│   │   ├── calculator/   # Agent 1 territory
│   │   ├── materials/    # Agent 2 territory
│   │   └── README.md     # Module ownership map
│   ├── ui/               # UI components
│   │   ├── components/   # Agent 3 territory
│   │   └── pages/        # Agent 4 territory
│   ├── api/              # Backend logic
│   ├── db/               # Database
│   └── tests/            # All tests
├── data/
│   ├── materials/        # Scraped data
│   └── prices/           # Price database
└── scripts/
    ├── test-runner.ts    # Automated testing
    └── merge-check.ts    # Conflict detection
`,
  
  rationale: "Clear boundaries prevent conflicts, STATUS.md maintains context"
};
2. Context Management System
typescriptconst contextManagementSystem = {
  masterStatus: {
    file: "_agents/context/STATUS.md",
    format: `
# Clarity Engine - Master Status Tracker
Last Updated: [TIMESTAMP]

## Project State
- Current Phase: [PHASE_NUMBER]
- Overall Progress: [X]%
- Active Agents: [LIST]

## Completed Modules
### Module: Authentication
- Status: ✅ Complete
- Agent: AGENT_001
- Completed: [DATE]
- Tests: 15/15 passing
- Integration: ✅ Verified

## In Progress
### Module: Calculator Engine
- Status: 🔄 60% complete
- Agent: AGENT_002
- Started: [DATE]
- Blockers: None
- Next: Add material calculations

## Pending Tasks
[Prioritized list with dependencies]

## Critical Information
- Database Schema Version: 1.2
- API Endpoints Completed: 5/25
- UI Components: 10/50
- Test Coverage: 75%

## Integration Points
[Key interfaces between modules]
`,
    updateFrequency: "After every significant change"
  },
  
  agentHandoff: {
    protocol: `
    1. Update STATUS.md with current state
    2. Commit all changes with descriptive message
    3. Document any decisions in DECISIONS.md
    4. Update task file with completion status
    5. Note any edge cases or TODOs
    `,
    
    contextFiles: {
      decisions: "Document why, not just what",
      architecture: "Update when structure changes",
      dependencies: "Track all external dependencies"
    }
  }
};
3. Git Workflow for AI Agents
typescriptconst gitWorkflow = {
  strategy: "Feature Branch with Frequent Integration",
  
  branches: {
    main: "Stable, tested code only",
    develop: "Integration branch",
    "feature/[agent-id]-[feature]": "Agent work branches"
  },
  
  commitConvention: {
    format: "[AGENT_ID] type: description",
    types: ["feat", "fix", "test", "docs", "refactor"],
    example: "[AGENT_001] feat: implement cost calculation engine"
  },
  
  mergeStrategy: {
    approach: "Frequent small merges",
    process: `
    1. Agent completes atomic task
    2. Run automated tests
    3. Self-merge to develop if tests pass
    4. Update STATUS.md
    5. Next agent pulls latest
    `,
    
    conflictResolution: {
      prevention: "Modular architecture minimizes conflicts",
      detection: "Automated conflict checker script",
      resolution: "Orchestrator agent handles conflicts"
    }
  }
};
4. Automated Testing Strategy
typescriptconst automatedTestingStrategy = {
  approach: "Continuous Testing with Self-Healing",
  
  testTypes: {
    unit: {
      tool: "Vitest",
      location: "Colocated with code",
      requirement: "Every function must have tests",
      autoGeneration: "Agent writes tests with code"
    },
    
    integration: {
      tool: "Supertest",
      trigger: "After module completion",
      coverage: "All API endpoints"
    },
    
    e2e: {
      tool: "Playwright", // Better for AI agents than Puppeteer
      trigger: "After feature completion",
      approach: "Visual regression + functional"
    }
  },
  
  playwrightSetup: {
    config: `
// playwright.config.ts
export default {
  testDir: './tests/e2e',
  timeout: 30000,
  retries: 2,
  use: {
    baseURL: 'http://localhost:3000',
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
    trace: 'on-first-retry'
  },
  projects: [
    { name: 'chromium', use: { ...devices['Desktop Chrome'] } },
    { name: 'mobile', use: { ...devices['iPhone 12'] } }
  ]
}`,
    
    automatedChecks: `
// Self-healing test example
test('calculator should compute costs correctly', async ({ page }) => {
  await page.goto('/calculator');
  
  // AI-friendly selectors
  await page.fill('[data-testid="area-input"]', '1000');
  await page.click('[data-testid="calculate-button"]');
  
  // Smart assertions
  await expect(page.locator('[data-testid="total-cost"]'))
    .toHaveText(/₹[0-9,]+/);
    
  // Visual regression
  await expect(page).toHaveScreenshot('calculator-result.png', {
    maxDiffPixels: 100
  });
});`
  }
};

Phase-wise AI Agent Execution Plan
Phase 1: Foundation Setup (Days 1-3)
typescriptconst phase1Tasks = {
  agent: "SETUP_AGENT",
  duration: "3 days",
  
  sequentialTasks: [
    {
      id: "TASK_001",
      title: "Initialize Next.js Project with TypeScript",
      prompt: `
Create a new Next.js 14 project with:
- TypeScript configuration
- Tailwind CSS
- ESLint + Prettier
- Folder structure as defined in _agents/context/ARCHITECTURE.md
- Git repository initialization
- Basic README.md

Specific requirements:
1. Use App Router (not Pages Router)
2. Set up path aliases (@/components, @/lib, etc.)
3. Configure Tailwind with custom design tokens
4. Add pre-commit hooks for linting
5. Create initial STATUS.md file

Test: Ensure 'npm run dev' works and shows welcome page
`,
      output: "Base project structure",
      verification: "npm run dev successfully starts"
    },
    
    {
      id: "TASK_002", 
      title: "Supabase Setup and Configuration",
      prompt: `
Set up Supabase integration:
1. Create .env.local with placeholder values
2. Install @supabase/supabase-js and @supabase/auth-helpers-nextjs
3. Create /src/lib/supabase/client.ts and server.ts
4. Set up database types generation script
5. Create initial migration for users table
6. Document Supabase setup in /docs/SUPABASE_SETUP.md

SQL for initial schema:
- users table (id, email, name, role, created_at, updated_at)
- projects table (id, user_id, name, data, created_at)
- Enable RLS policies

Test: Connection to Supabase should work (add connection test)
`,
      dependencies: ["TASK_001"],
      verification: "Supabase client connects successfully"
    },
    
    {
      id: "TASK_003",
      title: "Testing Infrastructure Setup",
      prompt: `
Set up comprehensive testing infrastructure:

1. Install testing dependencies:
   - vitest for unit tests
   - @testing-library/react for component tests
   - @playwright/test for E2E tests
   - @vitest/coverage-v8 for coverage

2. Create test configurations:
   - vitest.config.ts for unit/integration tests
   - playwright.config.ts for E2E tests
   - Add test scripts to package.json

3. Create test utilities:
   - /src/test/setup.ts (global test setup)
   - /src/test/utils.tsx (render helpers)
   - /src/test/factories.ts (test data factories)

4. Create first test examples:
   - /src/lib/calculator/__tests__/basic.test.ts
   - /tests/e2e/homepage.spec.ts

5. Set up CI workflow (.github/workflows/test.yml)

Test: Run 'npm test' and 'npm run test:e2e'
Update STATUS.md with testing setup completion
`,
      dependencies: ["TASK_002"]
    }
  ]
};
Phase 2: Core Calculator Engine (Days 4-7)
typescriptconst phase2ParallelTasks = {
  parallelAgents: 2,
  coordination: "Through STATUS.md and module interfaces",
  
  agent1Tasks: {
    agentId: "CALC_AGENT",
    branch: "feature/calc-engine",
    
    tasks: [
      {
        id: "CALC_001",
        title: "Build Calculation Engine Core",
        prompt: `
Create the core calculation engine in /src/core/calculator/:

1. Create TypeScript interfaces:
   - IProjectInput (area, floors, quality, location)
   - ICalculationResult (totalCost, breakdown, materials)
   - ICostBreakdown (structure, finishing, electrical, etc.)

2. Implement calculation functions:
   - calculateStructureCost(area, floors) 
   - calculateFinishingCost(area, quality)
   - calculateMEPCost(area)
   - calculateTotalCost(input)

3. Use these formulas:
   - Structure: ₹600-800/sqft based on floors
   - Finishing: ₹400-800/sqft based on quality
   - MEP: ₹300/sqft fixed
   - Add 10% contractor margin

4. Create comprehensive tests:
   - Test each calculation function
   - Test edge cases (min/max values)
   - Test different quality tiers
   - Achieve 100% coverage

5. Add JSDoc documentation for all functions

Update STATUS.md after completion
`,
        testRequirement: "100% test coverage",
        output: "/src/core/calculator/engine.ts with tests"
      },
      
      {
        id: "CALC_002",
        title: "Add Location-based Pricing",
        prompt: `
Extend calculator with location multipliers:

1. Create location configuration:
   - Bangalore: 1.0 (base)
   - Mumbai: 1.2
   - Delhi NCR: 1.15
   - Pune: 0.95
   - Other Tier-1: 1.1
   - Other Tier-2: 0.9

2. Update calculation functions to accept location
3. Apply multipliers to material and labor costs
4. Add tests for each location
5. Document pricing logic

File: /src/core/calculator/location-pricing.ts
`,
        dependencies: ["CALC_001"]
      }
    ]
  },
  
  agent2Tasks: {
    agentId: "UI_AGENT",
    branch: "feature/ui-foundation",
    
    tasks: [
      {
        id: "UI_001",
        title: "Create Base UI Components",
        prompt: `
Build foundation UI components in /src/ui/components/:

1. Create base components with Tailwind:
   - Button (variants: primary, secondary, ghost)
   - Input (with error states)
   - Select (custom styled)
   - Card (with shadow variants)
   - Layout (header, main, footer)

2. Each component must:
   - Be fully typed with TypeScript
   - Have Storybook stories
   - Be tested with React Testing Library
   - Be accessible (ARIA labels)
   - Be responsive

3. Create Tailwind config with design tokens:
   - Colors: primary, secondary, error, success
   - Spacing: 4px base unit
   - Typography: Inter font

4. Set up Storybook:
   - Install and configure
   - Create stories for each component
   - Add accessibility addon

Test with: npm run storybook
Update STATUS.md with component list
`,
        output: "Complete component library foundation"
      },
      
      {
        id: "UI_002",
        title: "Calculator UI Layout",
        prompt: `
Create calculator page layout:

1. Create /src/app/calculator/page.tsx
2. Build responsive layout:
   - Left: Input form
   - Right: Live results
   - Mobile: Stacked layout

3. Use components from UI_001
4. Add loading states
5. Add error boundaries
6. Make it beautiful and intuitive

Include Playwright test for responsive behavior
`,
        dependencies: ["UI_001"]
      }
    ]
  }
};
Phase 3: Integration and Polish (Days 8-10)
typescriptconst phase3Integration = {
  agent: "INTEGRATION_AGENT",
  focusOn: "Connecting all pieces",
  
  tasks: [
    {
      id: "INT_001",
      title: "Connect Calculator UI to Engine",
      prompt: `
Integrate calculator engine with UI:

1. Create /src/app/calculator/CalculatorForm.tsx:
   - Use React Hook Form for form management
   - Connect to calculation engine
   - Add real-time calculation on input change
   - Debounce calculations by 500ms

2. Create /src/app/calculator/ResultsDisplay.tsx:
   - Show total cost prominently
   - Show breakdown in cards
   - Add cost per sqft
   - Make it visually appealing

3. Add state management:
   - Use Zustand for calculator state
   - Persist last calculation in localStorage
   - Add reset functionality

4. Error handling:
   - Validate inputs
   - Show user-friendly error messages
   - Log errors to console

5. Write E2E test:
   - Fill form
   - Verify calculation
   - Test error states
   - Test mobile layout

Update STATUS.md with integration status
`,
      criticalTest: "E2E test must pass"
    },
    
    {
      id: "INT_002",
      title: "Add Save Calculation Feature",
      prompt: `
Implement save calculation functionality:

1. Add authentication check
2. Create save button in ResultsDisplay
3. Implement API route /api/projects/save
4. Store in Supabase with user_id
5. Show success/error toast
6. Add "My Projects" page
7. Test full flow

This connects auth, database, and UI
`,
      dependencies: ["INT_001", "AUTH_001"]
    }
  ]
};
Data Collection Agent Mega-Prompt
typescriptconst dataCollectionPrompt = {
  agentId: "DATA_AGENT",
  runInParallel: true,
  
  megaPrompt: `
You are a data collection specialist for construction materials in India.

OBJECTIVE: Collect comprehensive data for 1000+ construction materials with accurate pricing.

TARGET LOCATIONS: Delhi/NCR and Bangalore (collect both if available)

PHASE 1 - CORE MATERIALS (Start here):
1. Cement (All brands: UltraTech, ACC, Ambuja, Dalmia, etc.)
2. Steel/TMT Bars (TATA Tiscon, SAIL, Jindal, etc.)
3. Bricks/Blocks (Red bricks, Fly ash, AAC blocks)
4. Sand (River sand, M-sand, Plastering sand)
5. Aggregates (20mm, 40mm, 12mm)

For EACH material, collect:
REQUIRED:
- Material Name (exact brand name)
- Category (Cement/Steel/Brick/etc.)
- Sub-category (OPC 43/OPC 53/PPC for cement)
- Unit (bag/ton/piece/cum/sqft)
- Price Range (min-max)
- Location (Delhi/Bangalore/Both)
- Source URL
- Last Updated Date
- Specifications (grade/size/weight)

NICE TO HAVE:
- Dealer price vs Retail price
- Bulk discounts
- Quality certifications (ISI/ISO)
- Delivery charges
- Popular/Recommended flag

OUTPUT FORMAT (JSON):
{
  "materials": [
    {
      "id": "auto-generate-uuid",
      "name": "UltraTech Cement OPC 43 Grade",
      "category": "Cement",
      "subcategory": "OPC 43",
      "brand": "UltraTech",
      "unit": "bag",
      "weight": "50kg",
      "specifications": {
        "grade": "43",
        "type": "Ordinary Portland Cement",
        "standard": "IS 8112:2013"
      },
      "pricing": {
        "bangalore": {
          "retail": { "min": 380, "max": 420 },
          "dealer": { "min": 360, "max": 390 },
          "bulk_discount": "5% on 100+ bags"
        },
        "delhi": {
          "retail": { "min": 390, "max": 430 },
          "dealer": { "min": 370, "max": 400 }
        }
      },
      "source": {
        "urls": ["https://..."],
        "last_updated": "2024-01-15",
        "confidence": "high|medium|low"
      }
    }
  ]
}

SEARCH STRATEGY:
1. Start with manufacturer websites
2. Check major e-commerce (IndiaMART, TradeIndia)
3. Local dealer websites
4. Construction material aggregators
5. Price comparison sites

VALIDATION:
- If prices vary >30%, note as "requires verification"
- Cross-reference at least 2 sources
- Flag suspiciously low/high prices

Save output to: /data/materials/core-materials-[date].json
Create summary report: /data/materials/COLLECTION_SUMMARY.md

After Phase 1, proceed to:
- Phase 2: Finishing materials (Tiles, Paint, Sanitaryware)
- Phase 3: Electrical & Plumbing
- Phase 4: Doors, Windows, Hardware

Update STATUS.md with collection progress.
`
};
Orchestrator Agent Instructions
typescriptconst orchestratorAgent = {
  role: "Coordinate parallel agents and resolve conflicts",
  
  dailyTasks: [
    {
      task: "Morning Sync",
      prompt: `
Check STATUS.md and:
1. Identify completed tasks from last session
2. Run test suite to ensure stability
3. Merge completed feature branches to develop
4. Identify next priority tasks
5. Assign tasks to available agents
6. Update STATUS.md with daily plan
`
    },
    
    {
      task: "Conflict Resolution",
      prompt: `
If merge conflict detected:
1. Analyze conflicting changes
2. Determine correct resolution based on:
   - Feature priority
   - Code quality
   - Test results
3. Merge with detailed commit message
4. Re-run affected tests
5. Update both agents about resolution
`
    },
    
    {
      task: "Evening Wrap-up",
      prompt: `
1. Collect all agent updates
2. Run full test suite
3. Update master STATUS.md
4. Create backup of current state
5. Plan next day's tasks
6. Flag any blockers or issues
`
    }
  ]
};
Automated UI Testing Orchestration
typescriptconst uiTestingOrchestration = {
  trigger: "After every UI-related merge",
  
  playwrightTestSuite: {
    setup: `
// tests/e2e/visual-regression.spec.ts
import { test, expect } from '@playwright/test';

test.describe('Visual Regression Suite', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
  });

  test('homepage renders correctly', async ({ page }) => {
    // Wait for content
    await page.waitForLoadState('networkidle');
    
    // Full page screenshot
    await expect(page).toHaveScreenshot('homepage-full.png', {
      fullPage: true,
      animations: 'disabled'
    });
    
    // Test responsive
    await page.setViewportSize({ width: 375, height: 667 });
    await expect(page).toHaveScreenshot('homepage-mobile.png');
  });

  test('calculator flow works end-to-end', async ({ page }) => {
    await page.goto('/calculator');
    
    // Fill form
    await page.fill('[data-testid="area-input"]', '1500');
    await page.selectOption('[data-testid="floors-select"]', '2');
    await page.click('[data-testid="quality-premium"]');
    
    // Calculate
    await page.click('[data-testid="calculate-button"]');
    
    // Verify result appears
    await expect(page.locator('[data-testid="total-cost"]')).toBeVisible();
    await expect(page.locator('[data-testid="breakdown"]')).toBeVisible();
    
    // Visual regression of results
    await expect(page.locator('[data-testid="results-section"]'))
      .toHaveScreenshot('calculation-results.png');
  });
});`,
    
    selfHealing: `
// If test fails, agent should:
1. Analyze failure screenshot/video
2. Determine if it's a real bug or test issue
3. If test issue: Update selectors and retry
4. If real bug: Create detailed bug report
5. Fix the bug if possible
6. Re-run tests until passing
7. Update STATUS.md with resolution
`
  }
};

Critical Success Factors
1. Context Preservation

Every agent MUST update STATUS.md before ending session
Use descriptive commit messages
Document decisions in DECISIONS.md

2. Parallel Execution Rules

Work on separate modules
Communicate through interfaces
Test your module in isolation
Merge frequently (at least daily)

3. Quality Gates

No merge without passing tests
No feature without tests
No UI without accessibility
No API without documentation

4. Daily Routine
Morning: Check STATUS.md → Pull latest → Start assigned task
Coding: Write tests first → Implement → Verify locally
Evening: Commit → Update STATUS.md → Push → Note blockers

Week 1 Execution Timeline
Day 1-3: Foundation

Morning: Setup agent initializes project
Afternoon: Parallel setup of Supabase and testing
Evening: Verify all infrastructure working

Day 4-5: Core Development

2 parallel agents: Calculator Engine + UI Components
Data agent starts material collection
Orchestrator monitors progress

Day 6-7: Integration

Integration agent connects UI to engine
Add authentication base
Run comprehensive tests

Day 8-10: Polish & Launch Prep

Fix all bugs
Add final UI polish
Deploy to Vercel
Celebrate MVP! 🎉