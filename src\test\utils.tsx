import React from 'react';
import { render, RenderOptions, RenderResult } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { vi } from 'vitest';

// Custom render function with providers
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  queryClient?: QueryClient;
  user?: any;
  initialState?: any;
  route?: string;
}

const createQueryClient = () => {
  return new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        refetchOnWindowFocus: false,
        staleTime: 0,
        gcTime: 0,
      },
      mutations: {
        retry: false,
      },
    },
  });
};

// All providers wrapper
const AllProviders: React.FC<{
  children: React.ReactNode;
  queryClient?: QueryClient;
  user?: any;
  initialState?: any;
  route?: string;
}> = ({ children, queryClient, user, initialState, route }) => {
  const client = queryClient || createQueryClient();

  return (
    <QueryClientProvider client={client}>
      {children}
    </QueryClientProvider>
  );
};

// Custom render function
export const renderWithProviders = (
  ui: React.ReactElement,
  options: CustomRenderOptions = {}
): RenderResult => {
  const { queryClient, user, initialState, route, ...renderOptions } = options;

  const Wrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
    <AllProviders
      queryClient={queryClient}
      user={user}
      initialState={initialState}
      route={route}
    >
      {children}
    </AllProviders>
  );

  return render(ui, { wrapper: Wrapper, ...renderOptions });
};

// Re-export everything from testing library
export * from '@testing-library/react';
export * from '@testing-library/jest-dom/vitest';
export { renderWithProviders as render };

// Custom queries and utilities
export const findByRole = (container: HTMLElement, role: string, options?: any) => {
  return container.querySelector(`[role="${role}"]`);
};

export const findByTestId = (container: HTMLElement, testId: string) => {
  return container.querySelector(`[data-testid="${testId}"]`);
};

export const findByClassName = (container: HTMLElement, className: string) => {
  return container.querySelector(`.${className}`);
};

// Form testing utilities
export const fillForm = async (
  container: HTMLElement,
  formData: Record<string, string>
) => {
  const { userEvent } = await import('@testing-library/user-event');
  const user = userEvent.setup();

  for (const [name, value] of Object.entries(formData)) {
    const input = container.querySelector(`[name="${name}"]`) as HTMLInputElement;
    if (input) {
      await user.clear(input);
      await user.type(input, value);
    }
  }
};

export const submitForm = async (container: HTMLElement) => {
  const { userEvent } = await import('@testing-library/user-event');
  const user = userEvent.setup();

  const form = container.querySelector('form') as HTMLFormElement;
  const submitButton = form?.querySelector('[type="submit"]') as HTMLButtonElement;
  
  if (submitButton) {
    await user.click(submitButton);
  }
};

// Animation testing utilities
export const waitForAnimationEnd = () => {
  return new Promise((resolve) => {
    setTimeout(resolve, 1000); // Wait for animations to complete
  });
};

export const mockAnimationFrame = () => {
  let callbacks: Array<() => void> = [];
  
  const mockRaf = vi.fn((callback: () => void) => {
    callbacks.push(callback);
    return callbacks.length;
  });
  
  const mockCancelRaf = vi.fn((id: number) => {
    callbacks = callbacks.filter((_, index) => index !== id - 1);
  });
  
  const flush = () => {
    callbacks.forEach(callback => callback());
    callbacks = [];
  };
  
  Object.defineProperty(window, 'requestAnimationFrame', {
    value: mockRaf,
    writable: true,
  });
  
  Object.defineProperty(window, 'cancelAnimationFrame', {
    value: mockCancelRaf,
    writable: true,
  });
  
  return { flush };
};

// Performance testing utilities
export const measureRenderPerformance = async (
  component: React.ReactElement,
  iterations: number = 100
) => {
  const times: number[] = [];
  
  for (let i = 0; i < iterations; i++) {
    const start = performance.now();
    const { unmount } = renderWithProviders(component);
    const end = performance.now();
    
    times.push(end - start);
    unmount();
  }
  
  return {
    average: times.reduce((a, b) => a + b, 0) / times.length,
    min: Math.min(...times),
    max: Math.max(...times),
    median: times.sort((a, b) => a - b)[Math.floor(times.length / 2)],
    times,
  };
};

// Memory testing utilities
export const measureMemoryUsage = () => {
  if ('memory' in performance) {
    return (performance as any).memory;
  }
  return null;
};

// Mock DOM utilities
export const mockIntersectionObserver = () => {
  const mockObserver = vi.fn();
  const mockUnobserve = vi.fn();
  const mockDisconnect = vi.fn();
  
  Object.defineProperty(window, 'IntersectionObserver', {
    value: vi.fn().mockImplementation(() => ({
      observe: mockObserver,
      unobserve: mockUnobserve,
      disconnect: mockDisconnect,
    })),
    writable: true,
  });
  
  return { mockObserver, mockUnobserve, mockDisconnect };
};

export const mockResizeObserver = () => {
  const mockObserver = vi.fn();
  const mockUnobserve = vi.fn();
  const mockDisconnect = vi.fn();
  
  Object.defineProperty(window, 'ResizeObserver', {
    value: vi.fn().mockImplementation(() => ({
      observe: mockObserver,
      unobserve: mockUnobserve,
      disconnect: mockDisconnect,
    })),
    writable: true,
  });
  
  return { mockObserver, mockUnobserve, mockDisconnect };
};

// Accessibility testing utilities
export const checkAccessibility = async (container: HTMLElement) => {
  const { axe } = await import('jest-axe');
  const results = await axe(container);
  return results;
};

export const checkKeyboardNavigation = async (container: HTMLElement) => {
  const { userEvent } = await import('@testing-library/user-event');
  const user = userEvent.setup();
  
  const focusableElements = container.querySelectorAll(
    'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
  );
  
  const results = [];
  
  for (const element of focusableElements) {
    await user.tab();
    results.push({
      element,
      focused: document.activeElement === element,
    });
  }
  
  return results;
};

// Error boundary testing utilities
export const withErrorBoundary = (
  component: React.ReactElement,
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void
) => {
  class TestErrorBoundary extends React.Component<
    { children: React.ReactNode; onError?: (error: Error, errorInfo: React.ErrorInfo) => void },
    { hasError: boolean }
  > {
    constructor(props: any) {
      super(props);
      this.state = { hasError: false };
    }
    
    static getDerivedStateFromError() {
      return { hasError: true };
    }
    
    componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
      if (this.props.onError) {
        this.props.onError(error, errorInfo);
      }
    }
    
    render() {
      if (this.state.hasError) {
        return <div>Something went wrong.</div>;
      }
      
      return this.props.children;
    }
  }
  
  return (
    <TestErrorBoundary onError={onError}>
      {component}
    </TestErrorBoundary>
  );
};

// Custom hooks testing utilities
export const renderHook = <T extends any[], R>(
  hook: (...args: T) => R,
  options: { initialProps?: T } = {}
) => {
  const { initialProps } = options;
  let result: { current: R } = { current: undefined as any };
  
  const HookWrapper: React.FC<{ children?: React.ReactNode }> = () => {
    result.current = hook(...(initialProps || [] as any));
    return null;
  };
  
  const { rerender, unmount } = renderWithProviders(<HookWrapper />);
  
  return {
    result,
    rerender: (newProps?: T) => {
      rerender(<HookWrapper />);
    },
    unmount,
  };
};

// Test data generators
export const generateTestData = <T extends unknown>(
  factory: () => T,
  count: number = 10
): T[] => {
  return Array.from({ length: count }, factory);
};

// Mock API utilities
export const mockApiCall = <T extends unknown>(
  data: T,
  delay: number = 0,
  shouldFail: boolean = false
) => {
  return vi.fn().mockImplementation(() => {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        if (shouldFail) {
          reject(new Error('API call failed'));
        } else {
          resolve({ data, status: 200 });
        }
      }, delay);
    });
  });
};

// Test cleanup utilities
export const cleanupTestEnvironment = () => {
  // Clear all mocks
  vi.clearAllMocks();
  
  // Clear timers
  vi.clearAllTimers();
  
  // Restore all mocks
  vi.restoreAllMocks();
  
  // Clear DOM
  document.body.innerHTML = '';
  
  // Clear localStorage
  localStorage.clear();
  
  // Clear sessionStorage
  sessionStorage.clear();
};

// Test validation utilities
export const validateTestCoverage = (coverage: any, threshold: number = 80) => {
  const { functions, lines, statements, branches } = coverage;
  
  return {
    functions: functions.pct >= threshold,
    lines: lines.pct >= threshold,
    statements: statements.pct >= threshold,
    branches: branches.pct >= threshold,
    overall: [functions, lines, statements, branches].every(metric => metric.pct >= threshold),
  };
};

// Export default render function
export default renderWithProviders;