import { vi, describe, it, expect, beforeEach, type MockedFunction } from 'vitest';
import { NextRequest } from 'next/server';
import { POST } from '../route';
import { mockCalculatorFormData, mockApiResponse } from '@/test/utils/mock-data';

// Mock the calculator engine
vi.mock('@/core/calculator/engine', () => ({
  calculate: vi.fn(),
}));

import { calculate } from '@/core/calculator/engine';

// Mock the rate limiter
vi.mock('@/lib/rate-limiter', () => ({
  rateLimiter: {
    check: vi.fn(),
  },
}));

describe('/api/calculate', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Default successful calculation mock
    (calculate as MockedFunction<typeof calculate>).mockReturnValue(mockApiResponse);
  });

  const createRequest = (body: any, headers: Record<string, string> = {}) => {
    return new NextRequest('http://localhost:3000/api/calculate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...headers,
      },
      body: JSON.stringify(body),
    });
  };

  describe('Successful Requests', () => {
    it('should return calculation result for valid request', async () => {
      const request = createRequest(mockCalculatorFormData);
      const response = await POST(request);
      
      expect(response.status).toBe(200);
      
      const data = await response.json();
      expect(data.success).toBe(true);
      expect(data.data).toBeDefined();
      expect(data.requestId).toBeDefined();
      expect(data.processingTime).toBeDefined();
      
      expect(calculate).toHaveBeenCalledWith(mockCalculatorFormData);
    });

    it('should include proper response headers', async () => {
      const request = createRequest(mockCalculatorFormData);
      const response = await POST(request);
      
      expect(response.headers.get('Content-Type')).toBe('application/json');
      expect(response.headers.get('Cache-Control')).toBe('public, max-age=300');
      expect(response.headers.get('X-Content-Type-Options')).toBe('nosniff');
      expect(response.headers.get('X-Request-ID')).toBeDefined();
    });

    it('should handle different quality tiers', async () => {
      const tiers = ['smart', 'premium', 'luxury'] as const;
      
      for (const tier of tiers) {
        const request = createRequest({
          ...mockCalculatorFormData,
          qualityTier: tier,
        });
        
        const response = await POST(request);
        expect(response.status).toBe(200);
        
        const data = await response.json();
        expect(data.success).toBe(true);
      }
    });

    it('should handle different floor configurations', async () => {
      const floors = ['G', 'G+1', 'G+2', 'G+3', 'G+4'] as const;
      
      for (const floor of floors) {
        const request = createRequest({
          ...mockCalculatorFormData,
          floors: floor,
        });
        
        const response = await POST(request);
        expect(response.status).toBe(200);
      }
    });

    it('should handle basement inclusion', async () => {
      const withBasement = createRequest({
        ...mockCalculatorFormData,
        hasBasement: true,
      });
      
      const response = await POST(withBasement);
      expect(response.status).toBe(200);
      
      const data = await response.json();
      expect(data.success).toBe(true);
    });
  });

  describe('Input Validation', () => {
    it('should reject invalid JSON', async () => {
      const request = new NextRequest('http://localhost:3000/api/calculate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: 'invalid json',
      });
      
      const response = await POST(request);
      expect(response.status).toBe(400);
      
      const data = await response.json();
      expect(data.success).toBe(false);
      expect(data.error).toBe('INVALID_JSON');
    });

    it('should validate required fields', async () => {
      const incompleteData = {
        plotArea: 1000,
        // Missing required fields
      };
      
      const request = createRequest(incompleteData);
      const response = await POST(request);
      
      expect(response.status).toBe(400);
      
      const data = await response.json();
      expect(data.success).toBe(false);
      expect(data.error).toBe('MISSING_FIELDS');
      expect(data.details.missing).toContain('builtUpArea');
    });

    it('should validate data types and ranges', async () => {
      const invalidData = {
        ...mockCalculatorFormData,
        plotArea: -100, // Invalid negative value
        builtUpArea: 'invalid', // Invalid type
        qualityTier: 'invalid_tier', // Invalid enum value
      };
      
      const request = createRequest(invalidData);
      const response = await POST(request);
      
      expect(response.status).toBe(400);
      
      const data = await response.json();
      expect(data.success).toBe(false);
      expect(data.error).toBe('VALIDATION_FAILED');
      expect(data.details).toBeDefined();
    });

    it('should validate area constraints', async () => {
      const invalidAreas = [
        { plotArea: 100, builtUpArea: 1000 }, // Built-up > plot area
        { plotArea: 100, builtUpArea: 50 }, // Areas too small
      ];
      
      for (const areas of invalidAreas) {
        const request = createRequest({
          ...mockCalculatorFormData,
          ...areas,
        });
        
        const response = await POST(request);
        expect(response.status).toBe(400);
        
        const data = await response.json();
        expect(data.success).toBe(false);
      }
    });

    it('should validate location', async () => {
      const request = createRequest({
        ...mockCalculatorFormData,
        location: 'invalid_location',
      });
      
      const response = await POST(request);
      expect(response.status).toBe(400);
      
      const data = await response.json();
      expect(data.success).toBe(false);
      expect(data.error).toBe('VALIDATION_FAILED');
    });

    it('should validate parking type', async () => {
      const request = createRequest({
        ...mockCalculatorFormData,
        parkingType: 'invalid_parking',
      });
      
      const response = await POST(request);
      expect(response.status).toBe(400);
    });
  });

  describe('Error Handling', () => {
    it('should handle calculation engine errors', async () => {
      (calculate as MockedFunction<typeof calculate>).mockImplementation(() => {
        throw new Error('Calculation failed');
      });
      
      const request = createRequest(mockCalculatorFormData);
      const response = await POST(request);
      
      expect(response.status).toBe(500);
      
      const data = await response.json();
      expect(data.success).toBe(false);
      expect(data.error).toBe('SERVER_ERROR');
    });

    it('should handle unexpected errors', async () => {
      (calculate as MockedFunction<typeof calculate>).mockImplementation(() => {
        throw new Error('Unexpected error');
      });
      
      const request = createRequest(mockCalculatorFormData);
      const response = await POST(request);
      
      expect(response.status).toBe(500);
      
      const data = await response.json();
      expect(data.success).toBe(false);
      expect(data.error).toBe('SERVER_ERROR');
    });

    it('should include request ID in error responses', async () => {
      const request = createRequest({ invalid: 'data' });
      const response = await POST(request);
      
      const data = await response.json();
      expect(data.requestId).toBeDefined();
    });
  });

  describe('HTTP Methods', () => {
    it('should only accept POST requests', async () => {
      // Test with GET method (should be handled by Next.js routing)
      const request = new NextRequest('http://localhost:3000/api/calculate', {
        method: 'GET',
      });
      
      // Since we only export POST, other methods should not reach our handler
      // This is more of a documentation test
      expect(POST).toBeDefined();
    });
  });

  describe('Security Headers', () => {
    it('should include security headers in response', async () => {
      const request = createRequest(mockCalculatorFormData);
      const response = await POST(request);
      
      expect(response.headers.get('X-Content-Type-Options')).toBe('nosniff');
      expect(response.headers.get('X-Frame-Options')).toBe('DENY');
      expect(response.headers.get('Content-Type')).toBe('application/json');
    });
  });

  describe('Performance', () => {
    it('should complete calculation within reasonable time', async () => {
      const startTime = Date.now();
      
      const request = createRequest(mockCalculatorFormData);
      const response = await POST(request);
      
      const endTime = Date.now();
      const processingTime = endTime - startTime;
      
      expect(response.status).toBe(200);
      expect(processingTime).toBeLessThan(5000); // Should complete within 5 seconds
    });

    it('should include processing time in response', async () => {
      const request = createRequest(mockCalculatorFormData);
      const response = await POST(request);
      
      const data = await response.json();
      expect(data.processingTime).toBeDefined();
      expect(typeof data.processingTime).toBe('string');
      expect(data.processingTime).toMatch(/\d+ms/);
    });
  });

  describe('Edge Cases', () => {
    it('should handle very large areas', async () => {
      const largeAreaData = {
        ...mockCalculatorFormData,
        plotArea: 50000,
        builtUpArea: 40000,
      };
      
      const request = createRequest(largeAreaData);
      const response = await POST(request);
      
      expect(response.status).toBe(200);
    });

    it('should handle minimum viable inputs', async () => {
      const minimalData = {
        plotArea: 500,
        builtUpArea: 400,
        floors: 'G',
        qualityTier: 'smart',
        location: 'bangalore',
        hasBasement: false,
        parkingType: 'open',
        projectName: 'Test',
        includeInteriors: true,
        includeSiteDevelopment: true,
      };
      
      const request = createRequest(minimalData);
      const response = await POST(request);
      
      expect(response.status).toBe(200);
    });
  });

  describe('Content Type Handling', () => {
    it('should reject non-JSON content type', async () => {
      const request = new NextRequest('http://localhost:3000/api/calculate', {
        method: 'POST',
        headers: { 'Content-Type': 'text/plain' },
        body: JSON.stringify(mockCalculatorFormData),
      });
      
      const response = await POST(request);
      expect(response.status).toBe(400);
      
      const data = await response.json();
      expect(data.error).toBe('INVALID_JSON');
    });
  });
});