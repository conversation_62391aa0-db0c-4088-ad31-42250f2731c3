/**
 * Security Monitoring API Endpoint
 * Provides security metrics, alerts, and incident information
 */

import { NextRequest, NextResponse } from 'next/server';
import { securityMonitor } from '@/lib/security/security-monitor';
import { advancedRateLimiter } from '@/lib/security/advanced-rate-limiter';
import { vulnerabilityScanner } from '@/lib/security/vulnerability-scanner';
import { securityTester } from '@/lib/security/security-tester';
import { incidentResponseManager } from '@/lib/security/incident-response';

interface SecurityDashboard {
  timestamp: Date;
  status: 'healthy' | 'warning' | 'critical';
  metrics: {
    requests: {
      total: number;
      blocked: number;
      suspicious: number;
      rateLimited: number;
    };
    threats: {
      activeThreats: number;
      blockedIPs: number;
      vulnerabilities: number;
      incidents: number;
    };
    performance: {
      averageResponseTime: number;
      errorRate: number;
      uptime: number;
    };
  };
  alerts: any[];
  incidents: any[];
  recommendations: string[];
}

/**
 * GET /api/security/monitor
 * Returns comprehensive security monitoring dashboard
 */
export async function GET(request: NextRequest) {
  try {
    // Check authorization
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized access to security endpoint' },
        { status: 401 }
      );
    }

    // Get security metrics from various sources
    const rateLimiterStats = advancedRateLimiter.getStatistics();
    const vulnerabilityMetrics = vulnerabilityScanner.getMetrics();
    const threatAnalysis = vulnerabilityScanner.getThreatAnalysis();
    const dashboardData = securityMonitor.getDashboardData();

    // Compile comprehensive dashboard
    const dashboard: SecurityDashboard = {
      timestamp: new Date(),
      status: dashboardData.systemHealth.status,
      metrics: {
        requests: {
          total: rateLimiterStats.totalRequests,
          blocked: dashboardData.metrics.requests.blocked,
          suspicious: dashboardData.metrics.requests.suspicious,
          rateLimited: rateLimiterStats.totalRequests - dashboardData.metrics.requests.total,
        },
        threats: {
          activeThreats: threatAnalysis.recentAttacks,
          blockedIPs: rateLimiterStats.blockedIPs,
          vulnerabilities: vulnerabilityMetrics.vulnerabilitiesFound,
          incidents: dashboardData.activeIncidents.length,
        },
        performance: {
          averageResponseTime: dashboardData.metrics.performance.averageResponseTime,
          errorRate: dashboardData.metrics.performance.errorRate,
          uptime: dashboardData.metrics.performance.uptime,
        },
      },
      alerts: dashboardData.unacknowledgedAlerts,
      incidents: dashboardData.activeIncidents,
      recommendations: dashboardData.systemHealth.issues.length > 0 
        ? dashboardData.systemHealth.issues 
        : ['System is operating normally'],
    };

    return NextResponse.json(dashboard);

  } catch (error) {
    console.error('Security monitoring error:', error);
    return NextResponse.json(
      { error: 'Failed to retrieve security metrics' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/security/monitor
 * Trigger security actions (block IP, run tests, etc.)
 */
export async function POST(request: NextRequest) {
  try {
    // Check authorization
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized access to security endpoint' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { action, parameters } = body;

    let result: any = {};

    switch (action) {
      case 'block_ip':
        if (!parameters.ip) {
          return NextResponse.json(
            { error: 'IP address is required' },
            { status: 400 }
          );
        }
        advancedRateLimiter.blockIP(parameters.ip, parameters.duration || 24 * 60 * 60 * 1000);
        result = { message: `IP ${parameters.ip} blocked successfully` };
        break;

      case 'unblock_ip':
        if (!parameters.ip) {
          return NextResponse.json(
            { error: 'IP address is required' },
            { status: 400 }
          );
        }
        advancedRateLimiter.unblockIP(parameters.ip);
        result = { message: `IP ${parameters.ip} unblocked successfully` };
        break;

      case 'run_security_audit':
        result = await securityTester.runSecurityAudit();
        break;

      case 'run_vulnerability_scan':
        const scanResult = vulnerabilityScanner.scanInput(
          parameters.input || '',
          parameters.context || 'manual'
        );
        result = scanResult;
        break;

      case 'create_incident':
        if (!parameters.type || !parameters.severity) {
          return NextResponse.json(
            { error: 'Incident type and severity are required' },
            { status: 400 }
          );
        }
        const incidentId = await incidentResponseManager.triggerIncidentResponse({
          type: parameters.type,
          severity: parameters.severity,
          source: {
            ip: parameters.ip || 'manual',
            userAgent: request.headers.get('user-agent') || undefined,
            path: parameters.path || '/api/security/monitor',
          },
          evidence: parameters.evidence || [],
          description: parameters.description || 'Manually created incident',
        });
        result = { incidentId, message: 'Incident created successfully' };
        break;

      case 'acknowledge_alert':
        if (!parameters.alertId || !parameters.acknowledgedBy) {
          return NextResponse.json(
            { error: 'Alert ID and acknowledgedBy are required' },
            { status: 400 }
          );
        }
        securityMonitor.acknowledgeAlert(parameters.alertId, parameters.acknowledgedBy);
        result = { message: 'Alert acknowledged successfully' };
        break;

      default:
        return NextResponse.json(
          { error: `Unknown action: ${action}` },
          { status: 400 }
        );
    }

    // Log the security action
    securityMonitor.recordEvent({
      type: 'security_action',
      severity: 'low',
      message: `Security action executed: ${action}`,
      source: {
        ip: (request as any).ip || request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
        userAgent: request.headers.get('user-agent') || undefined,
        path: '/api/security/monitor',
        method: 'POST',
      },
      details: { action, parameters, result },
    });

    return NextResponse.json(result);

  } catch (error) {
    console.error('Security action error:', error);
    return NextResponse.json(
      { error: 'Failed to execute security action' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/security/monitor
 * Update security configuration
 */
export async function PUT(request: NextRequest) {
  try {
    // Check authorization
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized access to security endpoint' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { type, config } = body;

    switch (type) {
      case 'rate_limit':
        // Update rate limiting configuration
        // This would require extending the rate limiter to accept config updates
        break;

      case 'vulnerability_rules':
        // Add custom vulnerability rules
        if (config.rule) {
          vulnerabilityScanner.addCustomRule(config.rule);
        }
        break;

      case 'notification_channels':
        // Update notification channels
        // This would require implementing configuration updates
        break;

      default:
        return NextResponse.json(
          { error: `Unknown configuration type: ${type}` },
          { status: 400 }
        );
    }

    return NextResponse.json({ message: 'Configuration updated successfully' });

  } catch (error) {
    console.error('Security configuration error:', error);
    return NextResponse.json(
      { error: 'Failed to update security configuration' },
      { status: 500 }
    );
  }
}