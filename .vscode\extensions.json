{
  "recommendations": [
    // Essential extensions
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "bradlc.vscode-tailwindcss",
    "ms-vscode.vscode-typescript-next",

    // React and Next.js
    "dsznajder.es7-react-js-snippets",
    "formulahendry.auto-rename-tag",
    "christian-kohler.npm-intellisense",

    // Git and productivity
    "eamodio.gitlens",
    "mhutchie.git-graph",
    "gruntfuggly.todo-tree",

    // Code quality
    "streetsidesoftware.code-spell-checker",
    "oderwat.indent-rainbow",
    "aaron-bond.better-comments",

    // Database and API
    "supabase.supabase",
    "humao.rest-client",

    // Theme and UI
    "pkief.material-icon-theme",
    "github.github-vscode-theme",

    // Productivity
    "usernamehw.errorlens",
    "ms-vscode.vscode-json",
    "redhat.vscode-yaml"
  ],
  "unwantedRecommendations": ["ms-vscode.vscode-typescript", "hookyqr.beautify"]
}
