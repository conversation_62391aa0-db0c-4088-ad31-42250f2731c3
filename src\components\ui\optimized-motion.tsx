'use client';

import { lazy, Suspense } from 'react';
import { Skeleton } from './skeleton';

// Lazy load framer-motion components
const LazyMotion = lazy(() => 
  import('framer-motion').then(m => ({ default: m.LazyMotion }))
);

const LazyMotionDiv = lazy(() => 
  import('framer-motion').then(m => ({ default: m.motion.div }))
);

const LazyAnimatePresence = lazy(() => 
  import('framer-motion').then(m => ({ default: m.AnimatePresence }))
);

// Optimized motion components with lazy loading
export const OptimizedMotion = {
  div: (props: any) => (
    <Suspense fallback={<div {...props} />}>
      <LazyMotionDiv {...props} />
    </Suspense>
  ),
  AnimatePresence: (props: any) => (
    <Suspense fallback={<div>{props.children}</div>}>
      <LazyAnimatePresence {...props} />
    </Suspense>
  ),
  LazyMotion: (props: any) => (
    <Suspense fallback={<div>{props.children}</div>}>
      <LazyMotion {...props} />
    </Suspense>
  ),
};

// Reduced motion fallback for better performance
export const ReducedMotion = {
  div: (props: any) => {
    // Strip out animation props for reduced motion
    const { 
      initial, 
      animate, 
      exit, 
      transition, 
      whileHover, 
      whileTap, 
      whileFocus,
      ...safeProps 
    } = props;
    
    return <div {...safeProps} />;
  },
  AnimatePresence: ({ children, ...props }: any) => <div>{children}</div>,
  LazyMotion: ({ children, ...props }: any) => <div>{children}</div>,
};

// Hook to detect user's motion preference
export const useMotionPreference = () => {
  if (typeof window !== 'undefined') {
    return !window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  }
  return true;
};

// Export the appropriate motion components based on user preference
export const AdaptiveMotion = {
  div: (props: any) => {
    const prefersMotion = useMotionPreference();
    return prefersMotion ? OptimizedMotion.div(props) : ReducedMotion.div(props);
  },
  AnimatePresence: (props: any) => {
    const prefersMotion = useMotionPreference();
    return prefersMotion ? OptimizedMotion.AnimatePresence(props) : ReducedMotion.AnimatePresence(props);
  },
  LazyMotion: (props: any) => {
    const prefersMotion = useMotionPreference();
    return prefersMotion ? OptimizedMotion.LazyMotion(props) : ReducedMotion.LazyMotion(props);
  },
};