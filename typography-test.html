<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Typography & Color System Test</title>
    <style>
        /* Import our CSS system */
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&family=Poppins:wght@400;500;600;700;800;900&display=swap');
        
        /* Enhanced MVP Typography & Color System */
        :root {
            /* Font Families */
            --font-inter: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            --font-poppins: 'Poppins', 'Inter', system-ui, sans-serif;
            
            /* Typography Scale */
            --typography-xs: clamp(0.75rem, 0.7rem + 0.25vw, 0.875rem);
            --typography-sm: clamp(0.875rem, 0.825rem + 0.25vw, 1rem);
            --typography-base: clamp(1rem, 0.95rem + 0.25vw, 1.125rem);
            --typography-lg: clamp(1.125rem, 1.05rem + 0.375vw, 1.25rem);
            --typography-xl: clamp(1.25rem, 1.15rem + 0.5vw, 1.5rem);
            --typography-2xl: clamp(1.5rem, 1.35rem + 0.75vw, 1.875rem);
            --typography-3xl: clamp(1.875rem, 1.65rem + 1.125vw, 2.25rem);
            --typography-4xl: clamp(2.25rem, 1.95rem + 1.5vw, 3rem);
            --typography-5xl: clamp(3rem, 2.55rem + 2.25vw, 4rem);
            
            /* Colors */
            --primary-50: #eff6ff;
            --primary-500: #3b82f6;
            --primary-600: #2563eb;
            --primary-800: #1e40af;
            --secondary-50: #f8fafc;
            --secondary-100: #f1f5f9;
            --secondary-500: #64748b;
            --secondary-900: #0f172a;
            --success-500: #10b981;
            --warning-500: #f59e0b;
            --error-500: #ef4444;
            
            /* Spacing */
            --spacing-4: 1rem;
            --spacing-6: 1.5rem;
            --spacing-8: 2rem;
            
            /* Shadows */
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            
            /* Base Colors */
            --background: var(--secondary-50);
            --foreground: var(--secondary-900);
            --muted-foreground: var(--secondary-500);
        }
        
        * {
            box-sizing: border-box;
        }
        
        body {
            font-family: var(--font-inter);
            font-size: var(--typography-base);
            line-height: 1.5;
            color: var(--foreground);
            background-color: var(--background);
            margin: 0;
            padding: var(--spacing-8);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        /* Typography Classes */
        .heading-1 {
            font-family: var(--font-poppins);
            font-size: var(--typography-5xl);
            font-weight: 700;
            line-height: 1;
            letter-spacing: -0.025em;
            color: var(--foreground);
            margin: 0 0 var(--spacing-6) 0;
        }
        
        .heading-2 {
            font-family: var(--font-poppins);
            font-size: var(--typography-4xl);
            font-weight: 700;
            line-height: 1.25;
            letter-spacing: -0.025em;
            color: var(--foreground);
            margin: 0 0 var(--spacing-4) 0;
        }
        
        .heading-3 {
            font-family: var(--font-poppins);
            font-size: var(--typography-3xl);
            font-weight: 600;
            line-height: 1.25;
            color: var(--foreground);
            margin: 0 0 var(--spacing-4) 0;
        }
        
        .body-large {
            font-size: var(--typography-lg);
            line-height: 1.625;
            color: var(--foreground);
            margin: 0 0 var(--spacing-4) 0;
        }
        
        .body-normal {
            font-size: var(--typography-base);
            line-height: 1.625;
            color: var(--foreground);
            margin: 0 0 var(--spacing-4) 0;
        }
        
        .body-small {
            font-size: var(--typography-sm);
            line-height: 1.5;
            color: var(--muted-foreground);
            margin: 0 0 var(--spacing-4) 0;
        }
        
        .caption {
            font-size: var(--typography-xs);
            line-height: 1.5;
            color: var(--muted-foreground);
            letter-spacing: 0.025em;
        }
        
        /* Text Colors */
        .text-primary { color: var(--primary-500); }
        .text-success { color: var(--success-500); }
        .text-warning { color: var(--warning-500); }
        .text-error { color: var(--error-500); }
        .text-muted { color: var(--muted-foreground); }
        
        /* Gradient Text */
        .text-gradient-primary {
            background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-800) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        /* Cards */
        .card {
            background: white;
            border-radius: 0.75rem;
            padding: var(--spacing-6);
            box-shadow: var(--shadow-md);
            margin-bottom: var(--spacing-6);
        }
        
        .grid {
            display: grid;
            gap: var(--spacing-4);
        }
        
        .grid-cols-2 {
            grid-template-columns: repeat(2, minmax(0, 1fr));
        }
        
        .grid-cols-3 {
            grid-template-columns: repeat(3, minmax(0, 1fr));
        }
        
        /* Color Swatches */
        .color-swatch {
            display: flex;
            align-items: center;
            gap: var(--spacing-4);
            padding: var(--spacing-4);
            border-radius: 0.5rem;
            border: 1px solid #e5e7eb;
        }
        
        .color-box {
            width: 3rem;
            height: 3rem;
            border-radius: 0.375rem;
            border: 1px solid #e5e7eb;
        }
        
        .code {
            font-family: 'Monaco', 'Cascadia Code', 'Roboto Mono', monospace;
            font-size: var(--typography-sm);
            background: var(--secondary-100);
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            color: var(--foreground);
        }
        
        @media (max-width: 768px) {
            .grid-cols-2, .grid-cols-3 {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header style="margin-bottom: 3rem;">
            <h1 class="heading-1">
                <span class="text-gradient-primary">Enhanced MVP Design System</span>
            </h1>
            <p class="body-large">Typography & Color System for Nirmaan AI Construction Calculator</p>
        </header>

        <!-- Typography Section -->
        <section class="card">
            <h2 class="heading-2">Typography Scale</h2>
            <p class="body-normal">Responsive typography using Inter (body) and Poppins (headings) with fluid scaling.</p>
            
            <div style="margin-top: 2rem;">
                <div style="margin-bottom: 1.5rem;">
                    <p class="caption">HEADING 1 • var(--typography-5xl)</p>
                    <h1 class="heading-1" style="margin: 0.5rem 0;">The Future of Construction Intelligence</h1>
                </div>
                
                <div style="margin-bottom: 1.5rem;">
                    <p class="caption">HEADING 2 • var(--typography-4xl)</p>
                    <h2 class="heading-2" style="margin: 0.5rem 0;">Advanced Cost Calculation Engine</h2>
                </div>
                
                <div style="margin-bottom: 1.5rem;">
                    <p class="caption">HEADING 3 • var(--typography-3xl)</p>
                    <h3 class="heading-3" style="margin: 0.5rem 0;">Premium Features & Benefits</h3>
                </div>
                
                <div style="margin-bottom: 1.5rem;">
                    <p class="caption">BODY LARGE • var(--typography-lg)</p>
                    <p class="body-large">Transform your construction planning with AI-powered precision and professional-grade insights that revolutionize the industry.</p>
                </div>
                
                <div style="margin-bottom: 1.5rem;">
                    <p class="caption">BODY NORMAL • var(--typography-base)</p>
                    <p class="body-normal">Regular body text optimized for readability across all devices. Perfect for most content with excellent reading experience.</p>
                </div>
                
                <div style="margin-bottom: 1.5rem;">
                    <p class="caption">BODY SMALL • var(--typography-sm)</p>
                    <p class="body-small">Small body text for secondary information, metadata, and supporting content that complements the main narrative.</p>
                </div>
                
                <div>
                    <p class="caption">CAPTION • var(--typography-xs)</p>
                    <p class="caption">Caption text for labels, metadata, and auxiliary information</p>
                </div>
            </div>
        </section>

        <!-- Color System -->
        <section class="card">
            <h2 class="heading-2">Color System</h2>
            <p class="body-normal">Professional color palette with semantic meaning and accessibility compliance.</p>
            
            <div style="margin-top: 2rem;">
                <h3 class="heading-3">Brand Colors</h3>
                <div class="grid grid-cols-2">
                    <div class="color-swatch">
                        <div class="color-box" style="background-color: var(--primary-500);"></div>
                        <div>
                            <p class="body-normal" style="margin: 0;">Primary Blue</p>
                            <p class="code" style="margin: 0;">#3b82f6</p>
                        </div>
                    </div>
                    
                    <div class="color-swatch">
                        <div class="color-box" style="background-color: var(--secondary-500);"></div>
                        <div>
                            <p class="body-normal" style="margin: 0;">Secondary Gray</p>
                            <p class="code" style="margin: 0;">#64748b</p>
                        </div>
                    </div>
                </div>
                
                <h3 class="heading-3" style="margin-top: 2rem;">Semantic Colors</h3>
                <div class="grid grid-cols-3">
                    <div class="color-swatch">
                        <div class="color-box" style="background-color: var(--success-500);"></div>
                        <div>
                            <p class="body-normal" style="margin: 0;">Success</p>
                            <p class="code" style="margin: 0;">#10b981</p>
                        </div>
                    </div>
                    
                    <div class="color-swatch">
                        <div class="color-box" style="background-color: var(--warning-500);"></div>
                        <div>
                            <p class="body-normal" style="margin: 0;">Warning</p>
                            <p class="code" style="margin: 0;">#f59e0b</p>
                        </div>
                    </div>
                    
                    <div class="color-swatch">
                        <div class="color-box" style="background-color: var(--error-500);"></div>
                        <div>
                            <p class="body-normal" style="margin: 0;">Error</p>
                            <p class="code" style="margin: 0;">#ef4444</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Text Colors -->
        <section class="card">
            <h2 class="heading-2">Text Color Variants</h2>
            <div style="margin-top: 1rem;">
                <p class="body-normal">Default text color</p>
                <p class="body-normal text-primary">Primary color text</p>
                <p class="body-normal text-success">Success color text</p>
                <p class="body-normal text-warning">Warning color text</p>
                <p class="body-normal text-error">Error color text</p>
                <p class="body-normal text-muted">Muted color text</p>
                <p class="body-normal text-gradient-primary">Gradient text effect</p>
            </div>
        </section>

        <!-- Font Families -->
        <section class="card">
            <h2 class="heading-2">Font Families</h2>
            <div class="grid grid-cols-2">
                <div>
                    <h3 class="heading-3">Inter (Body Text)</h3>
                    <p class="body-normal">The quick brown fox jumps over the lazy dog. 0123456789</p>
                    <p class="body-small">Used for body text, UI elements, and readable content</p>
                </div>
                
                <div>
                    <h3 class="heading-3" style="font-family: var(--font-poppins);">Poppins (Display)</h3>
                    <p class="body-normal" style="font-family: var(--font-poppins);">The quick brown fox jumps over the lazy dog. 0123456789</p>
                    <p class="body-small">Used for headings, display text, and brand elements</p>
                </div>
            </div>
        </section>

        <!-- Responsive Demonstration -->
        <section class="card">
            <h2 class="heading-2">Responsive Typography</h2>
            <p class="body-normal">Resize your browser window to see the fluid typography scaling in action.</p>
            <div style="background: var(--secondary-100); padding: var(--spacing-4); border-radius: 0.5rem; margin-top: 1rem;">
                <p class="caption">FLUID SCALING DEMO</p>
                <h1 class="heading-1" style="margin: 0.5rem 0;">Scales from mobile to desktop</h1>
                <p class="body-large">Typography that adapts beautifully to any screen size</p>
            </div>
        </section>

        <!-- Footer -->
        <footer style="margin-top: 3rem; text-align: center; border-top: 1px solid #e5e7eb; padding-top: 2rem;">
            <p class="body-small">Enhanced MVP Design System v1.0.0</p>
            <p class="caption">Built for Nirmaan AI Construction Calculator</p>
        </footer>
    </div>
</body>
</html>