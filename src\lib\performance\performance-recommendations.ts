/**
 * Performance Recommendations Engine
 * Automated analysis and optimization suggestions
 */

import * as React from 'react';
import { reactProfiler } from './react-profiler';
import { memoryOptimizer } from './memory-optimizer';
import { performanceBudgetMonitor } from './performance-budget';

// Recommendation categories
export enum RecommendationCategory {
  REACT_OPTIMIZATION = 'react_optimization',
  MEMORY_MANAGEMENT = 'memory_management',
  BUNDLE_OPTIMIZATION = 'bundle_optimization',
  RUNTIME_PERFORMANCE = 'runtime_performance',
  USER_EXPERIENCE = 'user_experience',
  ACCESSIBILITY = 'accessibility',
  SECURITY = 'security',
}

// Recommendation priority
export enum RecommendationPriority {
  CRITICAL = 'critical',
  HIGH = 'high',
  MEDIUM = 'medium',
  LOW = 'low',
}

// Recommendation type
export enum RecommendationType {
  CODE_CHANGE = 'code_change',
  CONFIGURATION = 'configuration',
  ARCHITECTURE = 'architecture',
  TOOLING = 'tooling',
  MONITORING = 'monitoring',
}

// Performance recommendation
export interface PerformanceRecommendation {
  id: string;
  title: string;
  description: string;
  category: RecommendationCategory;
  priority: RecommendationPriority;
  type: RecommendationType;
  impact: {
    performance: number; // 1-10 scale
    maintainability: number;
    userExperience: number;
    development: number;
  };
  effort: {
    timeEstimate: string; // e.g., "2-4 hours", "1 day"
    complexity: 'low' | 'medium' | 'high';
    skillLevel: 'beginner' | 'intermediate' | 'advanced';
  };
  implementation: {
    steps: string[];
    codeExamples: string[];
    resources: string[];
  };
  metrics: {
    before: Record<string, number>;
    expectedAfter: Record<string, number>;
    measurableImprovement: string;
  };
  component?: string;
  timestamp: Date;
}

// Analysis results
export interface PerformanceAnalysis {
  score: number; // Overall performance score (0-100)
  issues: {
    critical: number;
    high: number;
    medium: number;
    low: number;
  };
  recommendations: PerformanceRecommendation[];
  summary: {
    totalRecommendations: number;
    estimatedImpact: number;
    implementationEffort: string;
    priorityActions: string[];
  };
}

class PerformanceRecommendationsEngine {
  private recommendations: PerformanceRecommendation[] = [];
  private analysisHistory: PerformanceAnalysis[] = [];

  // Generate comprehensive performance recommendations
  public generateRecommendations(): PerformanceAnalysis {
    this.recommendations = [];

    // Analyze React performance
    this.analyzeReactPerformance();
    
    // Analyze memory usage
    this.analyzeMemoryUsage();
    
    // Analyze budget violations
    this.analyzeBudgetViolations();
    
    // Analyze component structure
    this.analyzeComponentStructure();
    
    // Analyze user experience metrics
    this.analyzeUserExperience();
    
    // Generate overall analysis
    const analysis = this.generateOverallAnalysis();
    
    // Store in history
    this.analysisHistory.push(analysis);
    
    // Keep only last 10 analyses
    if (this.analysisHistory.length > 10) {
      this.analysisHistory.shift();
    }

    return analysis;
  }

  // Analyze React performance
  private analyzeReactPerformance(): void {
    const profiles = reactProfiler.getAllProfiles();
    const summary = reactProfiler.getPerformanceSummary();

    // Slow rendering components
    profiles.forEach(profile => {
      if (profile.metrics.averageRenderTime > 50) {
        this.addRecommendation({
          id: `react-slow-render-${profile.id}`,
          title: `Optimize slow rendering component: ${profile.id}`,
          description: `Component ${profile.id} has an average render time of ${profile.metrics.averageRenderTime.toFixed(2)}ms, which exceeds the 50ms threshold.`,
          category: RecommendationCategory.REACT_OPTIMIZATION,
          priority: profile.metrics.averageRenderTime > 100 ? RecommendationPriority.CRITICAL : RecommendationPriority.HIGH,
          type: RecommendationType.CODE_CHANGE,
          impact: {
            performance: 8,
            maintainability: 6,
            userExperience: 9,
            development: 5,
          },
          effort: {
            timeEstimate: '2-4 hours',
            complexity: 'medium',
            skillLevel: 'intermediate',
          },
          implementation: {
            steps: [
              'Wrap component with React.memo()',
              'Identify expensive calculations and wrap with useMemo()',
              'Optimize event handlers with useCallback()',
              'Consider component splitting if too complex',
            ],
            codeExamples: [
              `const OptimizedComponent = React.memo(({ props }) => {
  const expensiveValue = useMemo(() => {
    return expensiveCalculation(props.data);
  }, [props.data]);

  const handleClick = useCallback(() => {
    // Handle click
  }, []);

  return <div>{expensiveValue}</div>;
});`,
            ],
            resources: [
              'https://react.dev/reference/react/memo',
              'https://react.dev/reference/react/useMemo',
              'https://react.dev/reference/react/useCallback',
            ],
          },
          metrics: {
            before: { renderTime: profile.metrics.averageRenderTime },
            expectedAfter: { renderTime: profile.metrics.averageRenderTime * 0.3 },
            measurableImprovement: '70% reduction in render time',
          },
          component: profile.id,
          timestamp: new Date(),
        });
      }

      // Excessive re-renders
      if (profile.metrics.renderCount > 10) {
        this.addRecommendation({
          id: `react-excessive-renders-${profile.id}`,
          title: `Reduce excessive re-renders: ${profile.id}`,
          description: `Component ${profile.id} has rendered ${profile.metrics.renderCount} times, indicating potential optimization opportunities.`,
          category: RecommendationCategory.REACT_OPTIMIZATION,
          priority: RecommendationPriority.MEDIUM,
          type: RecommendationType.CODE_CHANGE,
          impact: {
            performance: 7,
            maintainability: 8,
            userExperience: 6,
            development: 7,
          },
          effort: {
            timeEstimate: '1-2 hours',
            complexity: 'low',
            skillLevel: 'beginner',
          },
          implementation: {
            steps: [
              'Identify state updates causing re-renders',
              'Use React.memo() with proper comparison function',
              'Optimize parent component to prevent unnecessary re-renders',
              'Consider state lifting or context optimization',
            ],
            codeExamples: [
              `const MemoizedComponent = React.memo(Component, (prevProps, nextProps) => {
  return prevProps.importantProp === nextProps.importantProp;
});`,
            ],
            resources: [
              'https://react.dev/reference/react/memo',
              'https://react.dev/learn/render-and-commit',
            ],
          },
          metrics: {
            before: { renderCount: profile.metrics.renderCount },
            expectedAfter: { renderCount: Math.max(3, profile.metrics.renderCount * 0.4) },
            measurableImprovement: '60% reduction in re-renders',
          },
          component: profile.id,
          timestamp: new Date(),
        });
      }
    });
  }

  // Analyze memory usage
  private analyzeMemoryUsage(): void {
    const memoryStatus = memoryOptimizer.getMemoryStatus();
    const leakReport = memoryOptimizer.getMemoryLeakReport();

    // High memory usage
    if (memoryStatus.usage > 80) {
      this.addRecommendation({
        id: 'memory-high-usage',
        title: 'Optimize high memory usage',
        description: `Memory usage is at ${memoryStatus.usage.toFixed(1)}% of budget, approaching critical levels.`,
        category: RecommendationCategory.MEMORY_MANAGEMENT,
        priority: memoryStatus.usage > 90 ? RecommendationPriority.CRITICAL : RecommendationPriority.HIGH,
        type: RecommendationType.CODE_CHANGE,
        impact: {
          performance: 9,
          maintainability: 7,
          userExperience: 8,
          development: 6,
        },
        effort: {
          timeEstimate: '4-8 hours',
          complexity: 'high',
          skillLevel: 'advanced',
        },
        implementation: {
          steps: [
            'Identify memory-intensive components',
            'Implement object pooling for frequently created objects',
            'Use WeakMap/WeakSet for object references',
            'Add cleanup in useEffect return functions',
            'Consider virtualization for large lists',
          ],
          codeExamples: [
            `useEffect(() => {
  const interval = setInterval(() => {
    // Do something
  }, 1000);

  return () => clearInterval(interval); // Cleanup
}, []);`,
          ],
          resources: [
            'https://developer.mozilla.org/en-US/docs/Web/JavaScript/Memory_Management',
            'https://react.dev/learn/synchronizing-with-effects#how-to-handle-the-effect-firing-twice-in-development',
          ],
        },
        metrics: {
          before: { memoryUsage: memoryStatus.current },
          expectedAfter: { memoryUsage: memoryStatus.current * 0.7 },
          measurableImprovement: '30% reduction in memory usage',
        },
        timestamp: new Date(),
      });
    }

    // Memory leaks
    if (leakReport.totalLeaks > 0) {
      leakReport.leakingComponents.forEach(componentId => {
        this.addRecommendation({
          id: `memory-leak-${componentId}`,
          title: `Fix memory leak in ${componentId}`,
          description: `Memory leak detected in component ${componentId}. This can cause performance degradation over time.`,
          category: RecommendationCategory.MEMORY_MANAGEMENT,
          priority: RecommendationPriority.HIGH,
          type: RecommendationType.CODE_CHANGE,
          impact: {
            performance: 8,
            maintainability: 9,
            userExperience: 7,
            development: 6,
          },
          effort: {
            timeEstimate: '2-6 hours',
            complexity: 'medium',
            skillLevel: 'intermediate',
          },
          implementation: {
            steps: [
              'Review component for uncleaned event listeners',
              'Check for uncleaned timeouts/intervals',
              'Ensure proper cleanup in useEffect',
              'Review global state subscriptions',
            ],
            codeExamples: [
              `useEffect(() => {
  const handleResize = () => {
    // Handle resize
  };

  window.addEventListener('resize', handleResize);
  
  return () => {
    window.removeEventListener('resize', handleResize);
  };
}, []);`,
            ],
            resources: [
              'https://react.dev/learn/synchronizing-with-effects#how-to-handle-the-effect-firing-twice-in-development',
            ],
          },
          metrics: {
            before: { memoryLeaks: 1 },
            expectedAfter: { memoryLeaks: 0 },
            measurableImprovement: 'Memory leak eliminated',
          },
          component: componentId,
          timestamp: new Date(),
        });
      });
    }
  }

  // Analyze budget violations
  private analyzeBudgetViolations(): void {
    const budgetReport = performanceBudgetMonitor.getBudgetReport();

    budgetReport.violations.forEach(violation => {
      const priority = violation.severity === 'critical' ? RecommendationPriority.CRITICAL :
                     violation.severity === 'high' ? RecommendationPriority.HIGH :
                     violation.severity === 'medium' ? RecommendationPriority.MEDIUM :
                     RecommendationPriority.LOW;

      this.addRecommendation({
        id: `budget-violation-${violation.id}`,
        title: `Fix budget violation: ${violation.metric}`,
        description: violation.description,
        category: RecommendationCategory.RUNTIME_PERFORMANCE,
        priority,
        type: RecommendationType.CODE_CHANGE,
        impact: {
          performance: violation.severity === 'critical' ? 9 : 7,
          maintainability: 6,
          userExperience: 8,
          development: 5,
        },
        effort: {
          timeEstimate: violation.severity === 'critical' ? '1-2 days' : '2-4 hours',
          complexity: violation.severity === 'critical' ? 'high' : 'medium',
          skillLevel: 'intermediate',
        },
        implementation: {
          steps: violation.recommendations,
          codeExamples: [],
          resources: [],
        },
        metrics: {
          before: { [violation.metric]: violation.actualValue },
          expectedAfter: { [violation.metric]: violation.budgetValue },
          measurableImprovement: `Bring ${violation.metric} within budget`,
        },
        component: violation.component,
        timestamp: new Date(),
      });
    });
  }

  // Analyze component structure
  private analyzeComponentStructure(): void {
    const profiles = reactProfiler.getAllProfiles();

    profiles.forEach(profile => {
      // Large components
      if (profile.metrics.childrenCount > 50) {
        this.addRecommendation({
          id: `component-size-${profile.id}`,
          title: `Consider splitting large component: ${profile.id}`,
          description: `Component ${profile.id} has ${profile.metrics.childrenCount} children, which may indicate it's doing too much.`,
          category: RecommendationCategory.REACT_OPTIMIZATION,
          priority: RecommendationPriority.MEDIUM,
          type: RecommendationType.ARCHITECTURE,
          impact: {
            performance: 6,
            maintainability: 9,
            userExperience: 5,
            development: 8,
          },
          effort: {
            timeEstimate: '4-8 hours',
            complexity: 'medium',
            skillLevel: 'intermediate',
          },
          implementation: {
            steps: [
              'Identify logical sections of the component',
              'Extract sections into separate components',
              'Use composition instead of inheritance',
              'Consider using compound components pattern',
            ],
            codeExamples: [
              `// Before: Large component
const LargeComponent = () => {
  return (
    <div>
      <Header />
      <Navigation />
      <Content />
      <Sidebar />
      <Footer />
    </div>
  );
};

// After: Split into smaller components
const Page = () => {
  return (
    <div>
      <Header />
      <MainContent />
      <Footer />
    </div>
  );
};

const MainContent = () => {
  return (
    <div>
      <Navigation />
      <Content />
      <Sidebar />
    </div>
  );
};`,
            ],
            resources: [
              'https://react.dev/learn/thinking-in-react',
              'https://react.dev/learn/passing-props-to-a-component',
            ],
          },
          metrics: {
            before: { childrenCount: profile.metrics.childrenCount },
            expectedAfter: { childrenCount: Math.min(20, profile.metrics.childrenCount) },
            measurableImprovement: 'Improved maintainability and testability',
          },
          component: profile.id,
          timestamp: new Date(),
        });
      }
    });
  }

  // Analyze user experience metrics
  private analyzeUserExperience(): void {
    // This would integrate with Web Vitals and other UX metrics
    // For now, we'll add some general UX recommendations
    
    this.addRecommendation({
      id: 'ux-loading-states',
      title: 'Improve loading states and feedback',
      description: 'Enhance user experience with better loading states and feedback mechanisms.',
      category: RecommendationCategory.USER_EXPERIENCE,
      priority: RecommendationPriority.MEDIUM,
      type: RecommendationType.CODE_CHANGE,
      impact: {
        performance: 3,
        maintainability: 5,
        userExperience: 9,
        development: 4,
      },
      effort: {
        timeEstimate: '2-4 hours',
        complexity: 'low',
        skillLevel: 'beginner',
      },
      implementation: {
        steps: [
          'Add skeleton screens for loading states',
          'Implement progress indicators for long operations',
          'Add error boundaries for graceful error handling',
          'Consider optimistic updates for better perceived performance',
        ],
        codeExamples: [
          `const LoadingButton = ({ loading, children, ...props }) => {
  return (
    <button disabled={loading} {...props}>
      {loading ? <Spinner /> : children}
    </button>
  );
};`,
        ],
        resources: [
          'https://www.nngroup.com/articles/response-times-3-important-limits/',
          'https://react.dev/reference/react/Suspense',
        ],
      },
      metrics: {
        before: { userFeedback: 5 },
        expectedAfter: { userFeedback: 9 },
        measurableImprovement: 'Better user experience and perceived performance',
      },
      timestamp: new Date(),
    });
  }

  // Add recommendation
  private addRecommendation(recommendation: Omit<PerformanceRecommendation, 'id' | 'timestamp'>): void {
    this.recommendations.push({
      ...recommendation,
      id: recommendation.id || `rec-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
    } as PerformanceRecommendation);
  }

  // Generate overall analysis
  private generateOverallAnalysis(): PerformanceAnalysis {
    const issues = this.recommendations.reduce((acc, rec) => {
      acc[rec.priority]++;
      return acc;
    }, {
      critical: 0,
      high: 0,
      medium: 0,
      low: 0,
    });

    // Calculate overall score
    const totalRecommendations = this.recommendations.length;
    const weightedScore = this.recommendations.reduce((acc, rec) => {
      const weight = rec.priority === RecommendationPriority.CRITICAL ? 4 :
                    rec.priority === RecommendationPriority.HIGH ? 3 :
                    rec.priority === RecommendationPriority.MEDIUM ? 2 : 1;
      return acc + (10 - weight);
    }, 0);

    const score = totalRecommendations > 0 ? Math.max(0, (weightedScore / (totalRecommendations * 10)) * 100) : 100;

    // Calculate estimated impact
    const estimatedImpact = this.recommendations.reduce((acc, rec) => {
      return acc + rec.impact.performance;
    }, 0) / Math.max(1, totalRecommendations);

    // Generate priority actions
    const priorityActions = this.recommendations
      .filter(rec => rec.priority === RecommendationPriority.CRITICAL || rec.priority === RecommendationPriority.HIGH)
      .sort((a, b) => a.priority === RecommendationPriority.CRITICAL ? -1 : 1)
      .slice(0, 3)
      .map(rec => rec.title);

    return {
      score,
      issues,
      recommendations: this.recommendations,
      summary: {
        totalRecommendations,
        estimatedImpact,
        implementationEffort: this.calculateImplementationEffort(),
        priorityActions,
      },
    };
  }

  // Calculate implementation effort
  private calculateImplementationEffort(): string {
    const efforts = this.recommendations.map(rec => {
      const timeMap = {
        '1-2 hours': 1.5,
        '2-4 hours': 3,
        '4-8 hours': 6,
        '1 day': 8,
        '1-2 days': 12,
        '2-3 days': 20,
      };
      return timeMap[rec.effort.timeEstimate] || 3;
    });

    const totalHours = efforts.reduce((sum, hours) => sum + hours, 0);
    
    if (totalHours < 8) return 'Less than 1 day';
    if (totalHours < 24) return '1-3 days';
    if (totalHours < 40) return '1 week';
    return 'More than 1 week';
  }

  // Get recommendations by category
  public getRecommendationsByCategory(category: RecommendationCategory): PerformanceRecommendation[] {
    return this.recommendations.filter(rec => rec.category === category);
  }

  // Get recommendations by priority
  public getRecommendationsByPriority(priority: RecommendationPriority): PerformanceRecommendation[] {
    return this.recommendations.filter(rec => rec.priority === priority);
  }

  // Get recommendations by component
  public getRecommendationsByComponent(componentId: string): PerformanceRecommendation[] {
    return this.recommendations.filter(rec => rec.component === componentId);
  }

  // Get analysis history
  public getAnalysisHistory(): PerformanceAnalysis[] {
    return this.analysisHistory;
  }

  // Clear recommendations
  public clearRecommendations(): void {
    this.recommendations = [];
  }
}

// Create singleton instance
export const performanceRecommendationsEngine = new PerformanceRecommendationsEngine();

// React hook for performance recommendations
export const usePerformanceRecommendations = (componentId?: string) => {
  const [analysis, setAnalysis] = React.useState<PerformanceAnalysis | null>(null);
  const [loading, setLoading] = React.useState(false);

  const generateRecommendations = React.useCallback(async () => {
    setLoading(true);
    try {
      const newAnalysis = performanceRecommendationsEngine.generateRecommendations();
      setAnalysis(newAnalysis);
    } catch (error) {
      console.error('Error generating recommendations:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  const componentRecommendations = React.useMemo(() => {
    if (!analysis || !componentId) return [];
    return analysis.recommendations.filter(rec => rec.component === componentId);
  }, [analysis, componentId]);

  return {
    analysis,
    componentRecommendations,
    loading,
    generateRecommendations,
  };
};

export default performanceRecommendationsEngine;