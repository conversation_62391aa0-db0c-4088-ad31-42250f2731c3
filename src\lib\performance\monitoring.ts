/**
 * Performance Monitoring and Analytics
 * Enhanced monitoring for production deployment
 */

interface PerformanceMetrics {
  requestCount: number;
  errorCount: number;
  responseTimeAvg: number;
  responseTimeMax: number;
  responseTimeMin: number;
  throughput: number;
  memoryUsage: number;
  cpuUsage: number;
  activeConnections: number;
  cacheHitRate: number;
  lastReset: Date;
}

interface RequestMetrics {
  method: string;
  path: string;
  duration: number;
  statusCode: number;
  timestamp: Date;
  userAgent?: string;
  ip?: string;
  region?: string;
}

class PerformanceMonitor {
  private metrics: PerformanceMetrics;
  private requestHistory: RequestMetrics[] = [];
  private readonly maxHistorySize = 1000;
  private readonly resetInterval = 60000; // 1 minute

  constructor() {
    this.metrics = this.initializeMetrics();
    this.startPeriodicReset();
  }

  private initializeMetrics(): PerformanceMetrics {
    return {
      requestCount: 0,
      errorCount: 0,
      responseTimeAvg: 0,
      responseTimeMax: 0,
      responseTimeMin: Infinity,
      throughput: 0,
      memoryUsage: 0,
      cpuUsage: 0,
      activeConnections: 0,
      cacheHitRate: 0,
      lastReset: new Date(),
    };
  }

  private startPeriodicReset(): void {
    setInterval(() => {
      this.resetMetrics();
    }, this.resetInterval);
  }

  private resetMetrics(): void {
    // Calculate averages before reset
    const timeWindow = (Date.now() - this.metrics.lastReset.getTime()) / 1000; // seconds
    this.metrics.throughput = this.metrics.requestCount / timeWindow;
    
    // Keep essential metrics, reset counters
    this.metrics.requestCount = 0;
    this.metrics.errorCount = 0;
    this.metrics.lastReset = new Date();
  }

  public recordRequest(request: RequestMetrics): void {
    this.metrics.requestCount++;
    
    // Update response time metrics
    this.updateResponseTimeMetrics(request.duration);
    
    // Track errors
    if (request.statusCode >= 400) {
      this.metrics.errorCount++;
    }
    
    // Store in history
    this.requestHistory.push(request);
    if (this.requestHistory.length > this.maxHistorySize) {
      this.requestHistory.shift();
    }
  }

  private updateResponseTimeMetrics(duration: number): void {
    this.metrics.responseTimeMax = Math.max(this.metrics.responseTimeMax, duration);
    this.metrics.responseTimeMin = Math.min(this.metrics.responseTimeMin, duration);
    
    // Calculate running average
    const totalRequests = this.metrics.requestCount;
    this.metrics.responseTimeAvg = 
      (this.metrics.responseTimeAvg * (totalRequests - 1) + duration) / totalRequests;
  }

  public getMetrics(): PerformanceMetrics {
    // Update memory usage
    if (typeof process !== 'undefined') {
      const memUsage = process.memoryUsage();
      this.metrics.memoryUsage = memUsage.heapUsed / 1024 / 1024; // MB
    }
    
    return { ...this.metrics };
  }

  public getRequestHistory(): RequestMetrics[] {
    return [...this.requestHistory];
  }

  public getErrorRate(): number {
    return this.metrics.requestCount > 0 
      ? (this.metrics.errorCount / this.metrics.requestCount) * 100 
      : 0;
  }

  public getRecentErrors(): RequestMetrics[] {
    return this.requestHistory.filter(req => req.statusCode >= 400);
  }

  public getSlowRequests(threshold: number = 1000): RequestMetrics[] {
    return this.requestHistory.filter(req => req.duration > threshold);
  }

  public getTopEndpoints(limit: number = 10): { path: string; count: number }[] {
    const pathCounts: { [key: string]: number } = {};
    
    this.requestHistory.forEach(req => {
      pathCounts[req.path] = (pathCounts[req.path] || 0) + 1;
    });
    
    return Object.entries(pathCounts)
      .sort((a, b) => b[1] - a[1])
      .slice(0, limit)
      .map(([path, count]) => ({ path, count }));
  }

  public getHealthStatus(): 'healthy' | 'degraded' | 'unhealthy' {
    const errorRate = this.getErrorRate();
    const avgResponseTime = this.metrics.responseTimeAvg;
    const memoryUsage = this.metrics.memoryUsage;
    
    if (errorRate > 10 || avgResponseTime > 5000 || memoryUsage > 512) {
      return 'unhealthy';
    } else if (errorRate > 5 || avgResponseTime > 2000 || memoryUsage > 256) {
      return 'degraded';
    }
    
    return 'healthy';
  }
}

// Create singleton instance
export const performanceMonitor = new PerformanceMonitor();

// Middleware function for Next.js
export function withPerformanceMonitoring(handler: Function) {
  return async (req: any, res: any, ...args: any[]) => {
    const startTime = Date.now();
    
    try {
      const result = await handler(req, res, ...args);
      
      // Record successful request
      performanceMonitor.recordRequest({
        method: req.method || 'GET',
        path: req.url || req.nextUrl?.pathname || 'unknown',
        duration: Date.now() - startTime,
        statusCode: res.status || 200,
        timestamp: new Date(),
        userAgent: req.headers?.get?.('user-agent') || req.headers?.['user-agent'],
        ip: req.ip || req.headers?.get?.('x-forwarded-for') || req.headers?.['x-forwarded-for'],
        region: req.headers?.get?.('x-vercel-ip-country') || req.headers?.['x-vercel-ip-country'],
      });
      
      return result;
    } catch (error) {
      // Record error
      performanceMonitor.recordRequest({
        method: req.method || 'GET',
        path: req.url || req.nextUrl?.pathname || 'unknown',
        duration: Date.now() - startTime,
        statusCode: 500,
        timestamp: new Date(),
        userAgent: req.headers?.get?.('user-agent') || req.headers?.['user-agent'],
        ip: req.ip || req.headers?.get?.('x-forwarded-for') || req.headers?.['x-forwarded-for'],
        region: req.headers?.get?.('x-vercel-ip-country') || req.headers?.['x-vercel-ip-country'],
      });
      
      throw error;
    }
  };
}

// Performance analytics for client-side
export class ClientPerformanceMonitor {
  private metrics: {
    pageLoadTime: number;
    firstContentfulPaint: number;
    largestContentfulPaint: number;
    firstInputDelay: number;
    cumulativeLayoutShift: number;
    networkRequests: number;
    errorCount: number;
  };

  constructor() {
    this.metrics = {
      pageLoadTime: 0,
      firstContentfulPaint: 0,
      largestContentfulPaint: 0,
      firstInputDelay: 0,
      cumulativeLayoutShift: 0,
      networkRequests: 0,
      errorCount: 0,
    };
    
    this.initializePerformanceObserver();
  }

  private initializePerformanceObserver(): void {
    if (typeof window !== 'undefined' && 'PerformanceObserver' in window) {
      // Web Vitals monitoring
      const observer = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          if (entry.entryType === 'paint') {
            if (entry.name === 'first-contentful-paint') {
              this.metrics.firstContentfulPaint = entry.startTime;
            }
          } else if (entry.entryType === 'largest-contentful-paint') {
            this.metrics.largestContentfulPaint = entry.startTime;
          } else if (entry.entryType === 'first-input') {
            this.metrics.firstInputDelay = entry.processingStart - entry.startTime;
          } else if (entry.entryType === 'layout-shift') {
            if (!(entry as any).hadRecentInput) {
              this.metrics.cumulativeLayoutShift += (entry as any).value;
            }
          }
        });
      });

      observer.observe({ entryTypes: ['paint', 'largest-contentful-paint', 'first-input', 'layout-shift'] });
    }
  }

  public recordPageLoad(): void {
    if (typeof window !== 'undefined' && window.performance) {
      const navigation = window.performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      if (navigation) {
        this.metrics.pageLoadTime = navigation.loadEventEnd - navigation.navigationStart;
      }
    }
  }

  public recordError(): void {
    this.metrics.errorCount++;
  }

  public getMetrics() {
    return { ...this.metrics };
  }

  public getWebVitalsScore(): number {
    const lcp = this.metrics.largestContentfulPaint;
    const fid = this.metrics.firstInputDelay;
    const cls = this.metrics.cumulativeLayoutShift;
    
    let score = 100;
    
    // LCP scoring (0-2.5s good, 2.5-4s needs improvement, >4s poor)
    if (lcp > 4000) score -= 40;
    else if (lcp > 2500) score -= 20;
    
    // FID scoring (0-100ms good, 100-300ms needs improvement, >300ms poor)
    if (fid > 300) score -= 30;
    else if (fid > 100) score -= 15;
    
    // CLS scoring (0-0.1 good, 0.1-0.25 needs improvement, >0.25 poor)
    if (cls > 0.25) score -= 30;
    else if (cls > 0.1) score -= 15;
    
    return Math.max(0, score);
  }
}

// Export performance utilities
export const performanceUtils = {
  measureAsyncOperation: async <T>(
    operation: () => Promise<T>,
    name: string
  ): Promise<{ result: T; duration: number }> => {
    const start = Date.now();
    try {
      const result = await operation();
      const duration = Date.now() - start;
      
      console.log(`Performance: ${name} completed in ${duration}ms`);
      return { result, duration };
    } catch (error) {
      const duration = Date.now() - start;
      console.error(`Performance: ${name} failed in ${duration}ms`, error);
      throw error;
    }
  },

  measureSyncOperation: <T>(
    operation: () => T,
    name: string
  ): { result: T; duration: number } => {
    const start = Date.now();
    try {
      const result = operation();
      const duration = Date.now() - start;
      
      console.log(`Performance: ${name} completed in ${duration}ms`);
      return { result, duration };
    } catch (error) {
      const duration = Date.now() - start;
      console.error(`Performance: ${name} failed in ${duration}ms`, error);
      throw error;
    }
  },
};