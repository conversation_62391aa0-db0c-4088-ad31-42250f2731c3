/**
 * Accessibility Module Index
 * Central export for all accessibility utilities and components
 */

// Core accessibility managers
export { focusManager, useFocusManagement } from './focus-management';
export { screenReader, useScreenReader } from './screen-reader';
export { keyboardNav, useKeyboardNavigation } from './keyboard-navigation';
export { themeManager, useAccessibilityTheme } from './theme-manager';
export { voiceNav, useVoiceNavigation } from './voice-navigation';
export { mobileTouchAccessibility, useMobileTouchAccessibility } from './mobile-touch';
export { accessibilityTester, useAccessibilityTesting } from './testing';

// Types from focus-management
export type {
  FocusOptions,
  FocusTrap
} from './focus-management';

// Types from screen-reader
export type {
  ScreenReaderContent,
  AriaLiveRegion
} from './screen-reader';

// Types from keyboard-navigation
export type {
  KeyboardShortcut,
  NavigationContext
} from './keyboard-navigation';

// Types from theme-manager
export type {
  ThemeMode,
  MotionPreference,
  FontSizeScale,
  AccessibilityThemeConfig,
  ColorContrastData
} from './theme-manager';

// Types from voice-navigation
export type {
  VoiceCommand,
  SpeechOptions,
  VoiceNavigationConfig
} from './voice-navigation';

// Types from mobile-touch
export type {
  TouchGesture,
  MobileTouchConfig
} from './mobile-touch';

// Types from testing
export type {
  AccessibilityIssue,
  AccessibilityTestResult,
  AccessibilityTestConfig
} from './testing';

/**
 * Initialize all accessibility systems
 */
export const initializeAccessibility = async () => {
  const focusModule = await import('./focus-management');
  const screenModule = await import('./screen-reader');
  const keyboardModule = await import('./keyboard-navigation');
  const themeModule = await import('./theme-manager');
  const voiceModule = await import('./voice-navigation');
  const touchModule = await import('./mobile-touch');

  // Announce that accessibility systems are ready
  screenModule.screenReader.announce('Accessibility systems initialized');

  return {
    focusManager: focusModule.focusManager,
    screenReader: screenModule.screenReader,
    keyboardNav: keyboardModule.keyboardNav,
    themeManager: themeModule.themeManager,
    voiceNav: voiceModule.voiceNav,
    mobileTouchAccessibility: touchModule.mobileTouchAccessibility
  };
};

/**
 * Quick accessibility setup for components
 */
export const setupComponentAccessibility = async (element: HTMLElement, options: {
  context?: string;
  enableVoice?: boolean;
  enableTouch?: boolean;
  enhanceForm?: boolean;
} = {}) => {
  const { context, enableVoice, enableTouch, enhanceForm } = options;

  const { keyboardNav } = await import('./keyboard-navigation');
  const { voiceNav } = await import('./voice-navigation');
  const { mobileTouchAccessibility } = await import('./mobile-touch');

  // Set up keyboard navigation context
  if (context) {
    keyboardNav.activateContext(context);
  }

  // Enable voice navigation
  if (enableVoice && voiceNav.isSupported()) {
    voiceNav.setContext(context || 'global');
  }

  // Enable touch accessibility
  if (enableTouch && mobileTouchAccessibility.isTouchDevice()) {
    mobileTouchAccessibility.enhanceElement(element);
  }

  // Enhance form fields
  if (enhanceForm) {
    const formFields = element.querySelectorAll('input, select, textarea');
    formFields.forEach(field => {
      if (mobileTouchAccessibility.isTouchDevice()) {
        mobileTouchAccessibility.enhanceFormField(field as HTMLElement);
      }
    });
  }

  return () => {
    // Cleanup function
    if (context) {
      keyboardNav.deactivateContext(context);
    }
  };
};

/**
 * Comprehensive accessibility audit
 */
export const auditAccessibility = async (container?: HTMLElement) => {
  const { accessibilityTester } = await import('./testing');
  
  const results = await accessibilityTester.audit({
    selector: container ? `#${container.id}` : undefined,
    level: 'AA'
  });

  const report = accessibilityTester.generateReport(results);
  
  console.group('🔍 Accessibility Audit Results');
  console.log(`Score: ${results.summary.score}%`);
  console.log(`Errors: ${results.summary.errors}`);
  console.log(`Warnings: ${results.summary.warnings}`);
  console.log('Full Report:', report);
  console.groupEnd();

  return { results, report };
};

/**
 * Enhanced accessibility utilities
 */
export const a11yUtils = {
  /**
   * Create accessible loading state
   */
  createLoadingState: async (message = 'Loading...') => {
    const { screenReader } = await import('./screen-reader');
    screenReader.announceLoading(message);
    return () => screenReader.announce('Loading complete');
  },

  /**
   * Create accessible error state
   */
  createErrorState: async (error: string, field?: string) => {
    const { screenReader } = await import('./screen-reader');
    if (field) {
      screenReader.announceValidationError(field, error);
    } else {
      screenReader.announce(`Error: ${error}`, 'assertive');
    }
  },

  /**
   * Create accessible success state
   */
  createSuccessState: async (message: string) => {
    const { screenReader } = await import('./screen-reader');
    screenReader.announceSuccess(message);
  },

  /**
   * Check if element is accessible
   */
  isAccessible: (element: HTMLElement): boolean => {
    const hasLabel = !!(
      element.getAttribute('aria-label') ||
      element.getAttribute('aria-labelledby') ||
      element.textContent?.trim()
    );

    const hasRole = !!(
      element.getAttribute('role') ||
      ['button', 'input', 'select', 'textarea', 'a'].includes(element.tagName.toLowerCase())
    );

    const isFocusable = element.tabIndex >= 0 || 
      ['button', 'input', 'select', 'textarea', 'a'].includes(element.tagName.toLowerCase());

    return hasLabel && hasRole && isFocusable;
  },

  /**
   * Get accessibility score for element
   */
  getAccessibilityScore: async (element: HTMLElement): Promise<number> => {
    const { accessibilityTester } = await import('./testing');
    const results = await accessibilityTester.audit({
      selector: `#${element.id}` || element.tagName.toLowerCase()
    });
    return results.summary.score;
  },

  /**
   * Enhance element accessibility
   */
  enhanceElement: async (element: HTMLElement, options: {
    label?: string;
    description?: string;
    role?: string;
    focusable?: boolean;
  } = {}) => {
    const { label, description, role, focusable } = options;

    if (label) {
      element.setAttribute('aria-label', label);
    }

    if (description) {
      const descId = `desc-${Date.now()}`;
      const descElement = document.createElement('div');
      descElement.id = descId;
      descElement.className = 'sr-only';
      descElement.textContent = description;
      element.appendChild(descElement);
      element.setAttribute('aria-describedby', descId);
    }

    if (role) {
      element.setAttribute('role', role);
    }

    if (focusable) {
      element.tabIndex = 0;
    }

    // Enhance for mobile if touch device
    const { mobileTouchAccessibility } = await import('./mobile-touch');
    if (mobileTouchAccessibility.isTouchDevice()) {
      mobileTouchAccessibility.enhanceElement(element);
    }
  }
};

/**
 * Accessibility status checker
 */
export const checkAccessibilityStatus = async () => {
  try {
    const { focusManager } = await import('./focus-management');
    const { screenReader } = await import('./screen-reader');
    const { keyboardNav } = await import('./keyboard-navigation');
    const { themeManager } = await import('./theme-manager');
    const { voiceNav } = await import('./voice-navigation');
    const { mobileTouchAccessibility } = await import('./mobile-touch');
    const { accessibilityTester } = await import('./testing');

    return {
      focusManagement: !!focusManager,
      screenReader: !!screenReader,
      keyboardNavigation: !!keyboardNav,
      themeManagement: !!themeManager,
      voiceNavigation: voiceNav.isSupported(),
      mobileTouch: mobileTouchAccessibility.isTouchDevice(),
      testing: !!accessibilityTester
    };
  } catch {
    return {
      focusManagement: false,
      screenReader: false,
      keyboardNavigation: false,
      themeManagement: false,
      voiceNavigation: false,
      mobileTouch: false,
      testing: false
    };
  }
};

/**
 * Create accessibility provider for React
 */
export const createAccessibilityProvider = async () => {
  const { focusManager } = await import('./focus-management');
  const { screenReader } = await import('./screen-reader');
  const { keyboardNav } = await import('./keyboard-navigation');
  const { themeManager } = await import('./theme-manager');
  const { voiceNav } = await import('./voice-navigation');
  const { mobileTouchAccessibility } = await import('./mobile-touch');
  const { accessibilityTester } = await import('./testing');

  return {
    focus: focusManager,
    screenReader,
    keyboard: keyboardNav,
    theme: themeManager,
    voice: voiceNav,
    touch: mobileTouchAccessibility,
    testing: accessibilityTester,
    utils: a11yUtils
  };
};