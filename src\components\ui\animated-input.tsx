'use client';

import * as React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { inputFocus, prefersReducedMotion } from '@/lib/animations';

interface AnimatedInputProps {
  id?: string;
  type?: string;
  label?: string;
  placeholder?: string;
  error?: string;
  success?: boolean;
  animate?: boolean;
  className?: string;
  value?: string | number;
  onChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onFocus?: (event: React.FocusEvent<HTMLInputElement>) => void;
  onBlur?: (event: React.FocusEvent<HTMLInputElement>) => void;
  disabled?: boolean;
  required?: boolean;
  name?: string;
}

const AnimatedInput = React.forwardRef<HTMLInputElement, AnimatedInputProps>(
  ({ 
    className, 
    type = 'text', 
    animate = true, 
    label, 
    error, 
    success, 
    onFocus,
    onBlur,
    ...props 
  }, ref) => {
    const [isFocused, setIsFocused] = React.useState(false);
    
    const inputClasses = cn(
      'file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow,border-color] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',
      'focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]',
      'aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive',
      error && 'border-destructive ring-destructive/20',
      success && 'border-green-500 ring-green-500/20',
      isFocused && 'ring-2',
      className
    );

    const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
      setIsFocused(true);
      onFocus?.(e);
    };

    const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
      setIsFocused(false);
      onBlur?.(e);
    };

    return (
      <div className="space-y-1">
        {label && (
          <motion.label
            className={cn(
              'text-sm font-medium text-gray-700 transition-colors',
              isFocused && 'text-primary',
              error && 'text-destructive'
            )}
            initial={false}
            animate={{
              color: isFocused 
                ? 'hsl(var(--primary))' 
                : error 
                ? 'hsl(var(--destructive))' 
                : undefined
            }}
            transition={{ duration: 0.15 }}
          >
            {label}
          </motion.label>
        )}
        
        {animate && !prefersReducedMotion ? (
          <motion.input
            ref={ref}
            type={type}
            data-slot="input"
            className={inputClasses}
            onFocus={handleFocus}
            onBlur={handleBlur}
            whileFocus={inputFocus}
            transition={{ duration: 0.15 }}
            {...props}
          />
        ) : (
          <input
            ref={ref}
            type={type}
            data-slot="input"
            className={inputClasses}
            onFocus={handleFocus}
            onBlur={handleBlur}
            {...props}
          />
        )}
        
        {error && (
          <motion.p
            initial={{ opacity: 0, y: -5 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -5 }}
            className="text-sm text-destructive"
          >
            {error}
          </motion.p>
        )}
      </div>
    );
  }
);

AnimatedInput.displayName = 'AnimatedInput';

export { AnimatedInput };
export type { AnimatedInputProps };