/**
 * Security Monitoring and Incident Response System
 * Real-time security monitoring, alerting, and incident management
 */

interface SecurityIncident {
  id: string;
  type: 'attack' | 'breach' | 'anomaly' | 'policy_violation' | 'system_failure';
  severity: 'low' | 'medium' | 'high' | 'critical';
  status: 'open' | 'investigating' | 'contained' | 'resolved' | 'false_positive';
  title: string;
  description: string;
  source: {
    ip: string;
    userAgent?: string;
    country?: string;
    path?: string;
    method?: string;
  };
  timeline: SecurityEvent[];
  impact: {
    affectedSystems: string[];
    affectedUsers: number;
    dataAtRisk: boolean;
    serviceDisruption: boolean;
  };
  response: {
    containmentActions: string[];
    investigationNotes: string[];
    remediationSteps: string[];
    preventionMeasures: string[];
  };
  metadata: {
    createdAt: Date;
    updatedAt: Date;
    assignedTo?: string;
    escalatedAt?: Date;
    resolvedAt?: Date;
  };
}

interface SecurityEvent {
  id: string;
  timestamp: Date;
  type: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  source: {
    ip: string;
    userAgent?: string;
    path?: string;
    method?: string;
  };
  details: any;
  incidentId?: string;
}

interface SecurityAlert {
  id: string;
  type: 'threshold_exceeded' | 'anomaly_detected' | 'attack_pattern' | 'system_health';
  message: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  conditions: string[];
  timestamp: Date;
  acknowledged: boolean;
  acknowledgedBy?: string;
  acknowledgedAt?: Date;
}

interface SecurityMetrics {
  requests: {
    total: number;
    blocked: number;
    suspicious: number;
    legitimate: number;
  };
  attacks: {
    sql_injection: number;
    xss: number;
    csrf: number;
    path_traversal: number;
    command_injection: number;
    rate_limit_violations: number;
    bot_attempts: number;
  };
  performance: {
    averageResponseTime: number;
    errorRate: number;
    uptime: number;
  };
  threats: {
    blockedIPs: number;
    activeThreats: number;
    highSeverityIncidents: number;
  };
  timestamp: Date;
}

interface ThreatPattern {
  id: string;
  name: string;
  description: string;
  pattern: RegExp | ((event: SecurityEvent) => boolean);
  severity: 'low' | 'medium' | 'high' | 'critical';
  threshold: number;
  timeWindow: number; // minutes
  enabled: boolean;
}

export class SecurityMonitor {
  private events: SecurityEvent[] = [];
  private incidents: SecurityIncident[] = [];
  private alerts: SecurityAlert[] = [];
  private metrics: SecurityMetrics;
  private threatPatterns: ThreatPattern[] = [];
  private blockedIPs: Map<string, { reason: string; timestamp: Date; expiresAt: Date }> = new Map();
  private suspiciousIPs: Map<string, { score: number; lastActivity: Date }> = new Map();
  
  private readonly maxEvents = 10000;
  private readonly maxIncidents = 1000;
  private readonly maxAlerts = 500;

  constructor() {
    this.metrics = this.initializeMetrics();
    this.initializeThreatPatterns();
    this.startPeriodicTasks();
  }

  /**
   * Record a security event
   */
  recordEvent(event: Omit<SecurityEvent, 'id' | 'timestamp'>): string {
    const securityEvent: SecurityEvent = {
      id: this.generateId(),
      timestamp: new Date(),
      ...event,
    };

    this.events.push(securityEvent);
    this.updateMetrics(securityEvent);
    this.checkThreatPatterns(securityEvent);
    this.updateIPReputation(securityEvent);

    // Maintain event history size
    if (this.events.length > this.maxEvents) {
      this.events = this.events.slice(-this.maxEvents);
    }

    // Auto-escalate critical events
    if (securityEvent.severity === 'critical') {
      this.createIncident(securityEvent);
    }

    this.logEvent(securityEvent);
    return securityEvent.id;
  }

  /**
   * Create a security incident
   */
  createIncident(event: SecurityEvent): string {
    const incident: SecurityIncident = {
      id: this.generateId(),
      type: this.determineIncidentType(event),
      severity: event.severity,
      status: 'open',
      title: this.generateIncidentTitle(event),
      description: this.generateIncidentDescription(event),
      source: event.source,
      timeline: [event],
      impact: this.assessImpact(event),
      response: {
        containmentActions: this.getAutomaticContainmentActions(event),
        investigationNotes: [],
        remediationSteps: [],
        preventionMeasures: [],
      },
      metadata: {
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    };

    this.incidents.push(incident);
    this.executeAutomaticResponse(incident);

    // Maintain incident history size
    if (this.incidents.length > this.maxIncidents) {
      this.incidents = this.incidents.slice(-this.maxIncidents);
    }

    // Create alert for high/critical incidents
    if (incident.severity === 'high' || incident.severity === 'critical') {
      this.createAlert({
        type: 'attack_pattern',
        message: `Security incident created: ${incident.title}`,
        severity: incident.severity,
        conditions: [`Incident ID: ${incident.id}`],
      });
    }

    this.logIncident(incident);
    return incident.id;
  }

  /**
   * Update incident status and add timeline entry
   */
  updateIncident(incidentId: string, update: Partial<SecurityIncident>): void {
    const incident = this.incidents.find(i => i.id === incidentId);
    if (!incident) {
      throw new Error(`Incident ${incidentId} not found`);
    }

    // Update fields
    Object.assign(incident, update);
    incident.metadata.updatedAt = new Date();

    // Add timeline entry if status changed
    if (update.status) {
      const timelineEvent: SecurityEvent = {
        id: this.generateId(),
        timestamp: new Date(),
        type: 'incident_update',
        severity: 'low',
        message: `Incident status changed to ${update.status}`,
        source: { ip: 'system' },
        details: { previousStatus: incident.status, newStatus: update.status },
        incidentId: incident.id,
      };
      incident.timeline.push(timelineEvent);
    }

    // Set resolved timestamp
    if (update.status === 'resolved' && !incident.metadata.resolvedAt) {
      incident.metadata.resolvedAt = new Date();
    }

    this.logIncident(incident);
  }

  /**
   * Create a security alert
   */
  createAlert(alert: Omit<SecurityAlert, 'id' | 'timestamp' | 'acknowledged'>): string {
    const securityAlert: SecurityAlert = {
      id: this.generateId(),
      timestamp: new Date(),
      acknowledged: false,
      ...alert,
    };

    this.alerts.push(securityAlert);

    // Maintain alert history size
    if (this.alerts.length > this.maxAlerts) {
      this.alerts = this.alerts.slice(-this.maxAlerts);
    }

    // Send notifications for high/critical alerts
    if (securityAlert.severity === 'high' || securityAlert.severity === 'critical') {
      this.sendAlertNotification(securityAlert);
    }

    this.logAlert(securityAlert);
    return securityAlert.id;
  }

  /**
   * Acknowledge an alert
   */
  acknowledgeAlert(alertId: string, acknowledgedBy: string): void {
    const alert = this.alerts.find(a => a.id === alertId);
    if (alert) {
      alert.acknowledged = true;
      alert.acknowledgedBy = acknowledgedBy;
      alert.acknowledgedAt = new Date();
    }
  }

  /**
   * Block an IP address
   */
  blockIP(ip: string, reason: string, duration: number = 24 * 60 * 60 * 1000): void {
    const now = new Date();
    this.blockedIPs.set(ip, {
      reason,
      timestamp: now,
      expiresAt: new Date(now.getTime() + duration),
    });

    this.recordEvent({
      type: 'ip_blocked',
      severity: 'medium',
      message: `IP ${ip} blocked: ${reason}`,
      source: { ip },
      details: { reason, duration },
    });
  }

  /**
   * Unblock an IP address
   */
  unblockIP(ip: string): void {
    if (this.blockedIPs.delete(ip)) {
      this.recordEvent({
        type: 'ip_unblocked',
        severity: 'low',
        message: `IP ${ip} unblocked`,
        source: { ip },
        details: { timestamp: new Date() },
      });
    }
  }

  /**
   * Check if IP is blocked
   */
  isIPBlocked(ip: string): boolean {
    const block = this.blockedIPs.get(ip);
    if (!block) return false;

    // Check if block has expired
    if (new Date() > block.expiresAt) {
      this.blockedIPs.delete(ip);
      return false;
    }

    return true;
  }

  /**
   * Get IP reputation score
   */
  getIPReputation(ip: string): { score: number; category: string; details: any } {
    const suspiciousInfo = this.suspiciousIPs.get(ip);
    const isBlocked = this.isIPBlocked(ip);
    
    let score = 100; // Start with perfect score
    let category = 'clean';
    const details: any = {};

    if (isBlocked) {
      score = 0;
      category = 'malicious';
      details.blocked = true;
      details.blockInfo = this.blockedIPs.get(ip);
    } else if (suspiciousInfo) {
      score = Math.max(0, 100 - suspiciousInfo.score);
      if (score < 30) category = 'malicious';
      else if (score < 60) category = 'suspicious';
      else if (score < 80) category = 'questionable';
      
      details.suspiciousScore = suspiciousInfo.score;
      details.lastActivity = suspiciousInfo.lastActivity;
    }

    // Check recent events for this IP
    const recentEvents = this.events
      .filter(e => e.source.ip === ip)
      .filter(e => Date.now() - e.timestamp.getTime() < 24 * 60 * 60 * 1000);

    if (recentEvents.length > 0) {
      const highSeverityEvents = recentEvents.filter(e => e.severity === 'high' || e.severity === 'critical');
      if (highSeverityEvents.length > 0) {
        score = Math.max(0, score - (highSeverityEvents.length * 20));
        details.recentHighSeverityEvents = highSeverityEvents.length;
      }
    }

    return { score, category, details };
  }

  /**
   * Get security dashboard data
   */
  getDashboardData(): {
    metrics: SecurityMetrics;
    recentEvents: SecurityEvent[];
    activeIncidents: SecurityIncident[];
    unacknowledgedAlerts: SecurityAlert[];
    topThreats: { ip: string; reputation: any }[];
    systemHealth: {
      status: 'healthy' | 'warning' | 'critical';
      issues: string[];
    };
  } {
    const recentEvents = this.events.slice(-50);
    const activeIncidents = this.incidents.filter(i => i.status !== 'resolved' && i.status !== 'false_positive');
    const unacknowledgedAlerts = this.alerts.filter(a => !a.acknowledged);

    // Get top threatening IPs
    const ipScores = new Map<string, any>();
    this.events.slice(-1000).forEach(event => {
      if (event.source.ip && event.source.ip !== 'system') {
        const reputation = this.getIPReputation(event.source.ip);
        if (reputation.score < 80) {
          ipScores.set(event.source.ip, reputation);
        }
      }
    });

    const topThreats = Array.from(ipScores.entries())
      .map(([ip, reputation]) => ({ ip, reputation }))
      .sort((a, b) => a.reputation.score - b.reputation.score)
      .slice(0, 10);

    // Assess system health
    const systemHealth = this.assessSystemHealth();

    return {
      metrics: this.metrics,
      recentEvents,
      activeIncidents,
      unacknowledgedAlerts,
      topThreats,
      systemHealth,
    };
  }

  /**
   * Export security report
   */
  exportSecurityReport(timeRange: { start: Date; end: Date }): {
    summary: any;
    events: SecurityEvent[];
    incidents: SecurityIncident[];
    recommendations: string[];
  } {
    const filteredEvents = this.events.filter(e => 
      e.timestamp >= timeRange.start && e.timestamp <= timeRange.end
    );

    const filteredIncidents = this.incidents.filter(i => 
      i.metadata.createdAt >= timeRange.start && i.metadata.createdAt <= timeRange.end
    );

    const summary = {
      timeRange,
      totalEvents: filteredEvents.length,
      totalIncidents: filteredIncidents.length,
      severityBreakdown: this.getSeverityBreakdown(filteredEvents),
      attackTypes: this.getAttackTypeBreakdown(filteredEvents),
      topSourceIPs: this.getTopSourceIPs(filteredEvents, 10),
      resolutionTime: this.calculateAverageResolutionTime(filteredIncidents),
    };

    const recommendations = this.generateSecurityRecommendations(filteredEvents, filteredIncidents);

    return {
      summary,
      events: filteredEvents,
      incidents: filteredIncidents,
      recommendations,
    };
  }

  /**
   * Initialize security metrics
   */
  private initializeMetrics(): SecurityMetrics {
    return {
      requests: { total: 0, blocked: 0, suspicious: 0, legitimate: 0 },
      attacks: {
        sql_injection: 0,
        xss: 0,
        csrf: 0,
        path_traversal: 0,
        command_injection: 0,
        rate_limit_violations: 0,
        bot_attempts: 0,
      },
      performance: { averageResponseTime: 0, errorRate: 0, uptime: 100 },
      threats: { blockedIPs: 0, activeThreats: 0, highSeverityIncidents: 0 },
      timestamp: new Date(),
    };
  }

  /**
   * Initialize threat detection patterns
   */
  private initializeThreatPatterns(): void {
    this.threatPatterns = [
      {
        id: 'sql_injection_burst',
        name: 'SQL Injection Attack Burst',
        description: 'Multiple SQL injection attempts from same source',
        pattern: (event: SecurityEvent) => event.type === 'sql_injection',
        severity: 'high',
        threshold: 5,
        timeWindow: 5,
        enabled: true,
      },
      {
        id: 'rate_limit_abuse',
        name: 'Rate Limit Abuse',
        description: 'Excessive rate limit violations',
        pattern: (event: SecurityEvent) => event.type === 'rate_limit_violation',
        severity: 'medium',
        threshold: 10,
        timeWindow: 15,
        enabled: true,
      },
      {
        id: 'path_traversal_scan',
        name: 'Path Traversal Scanning',
        description: 'Systematic path traversal attempts',
        pattern: (event: SecurityEvent) => event.type === 'path_traversal',
        severity: 'high',
        threshold: 3,
        timeWindow: 10,
        enabled: true,
      },
      {
        id: 'bot_activity',
        name: 'Automated Bot Activity',
        description: 'Suspicious automated activity patterns',
        pattern: (event: SecurityEvent) => event.type === 'bot_activity',
        severity: 'medium',
        threshold: 20,
        timeWindow: 30,
        enabled: true,
      },
    ];
  }

  /**
   * Update metrics based on security event
   */
  private updateMetrics(event: SecurityEvent): void {
    this.metrics.requests.total++;
    
    switch (event.type) {
      case 'request_blocked':
        this.metrics.requests.blocked++;
        break;
      case 'suspicious_activity':
        this.metrics.requests.suspicious++;
        break;
      case 'sql_injection':
        this.metrics.attacks.sql_injection++;
        break;
      case 'xss_attempt':
        this.metrics.attacks.xss++;
        break;
      case 'csrf_violation':
        this.metrics.attacks.csrf++;
        break;
      case 'path_traversal':
        this.metrics.attacks.path_traversal++;
        break;
      case 'command_injection':
        this.metrics.attacks.command_injection++;
        break;
      case 'rate_limit_violation':
        this.metrics.attacks.rate_limit_violations++;
        break;
      case 'bot_activity':
        this.metrics.attacks.bot_attempts++;
        break;
      default:
        this.metrics.requests.legitimate++;
    }

    this.metrics.threats.blockedIPs = this.blockedIPs.size;
    this.metrics.threats.highSeverityIncidents = this.incidents.filter(
      i => (i.severity === 'high' || i.severity === 'critical') && 
           i.status !== 'resolved' && i.status !== 'false_positive'
    ).length;

    this.metrics.timestamp = new Date();
  }

  /**
   * Check for threat patterns and create alerts
   */
  private checkThreatPatterns(event: SecurityEvent): void {
    this.threatPatterns.forEach(pattern => {
      if (!pattern.enabled) return;

      const timeWindow = pattern.timeWindow * 60 * 1000; // Convert to milliseconds
      const cutoffTime = new Date(Date.now() - timeWindow);
      
      // Find matching events in time window
      const matchingEvents = this.events.filter(e => {
        const matches = typeof pattern.pattern === 'function' 
          ? pattern.pattern(e) 
          : pattern.pattern.test(e.type);
        
        return matches && 
               e.timestamp > cutoffTime && 
               e.source.ip === event.source.ip;
      });

      if (matchingEvents.length >= pattern.threshold) {
        this.createAlert({
          type: 'threshold_exceeded',
          message: `${pattern.name} detected from ${event.source.ip}`,
          severity: pattern.severity,
          conditions: [
            `${matchingEvents.length} events in ${pattern.timeWindow} minutes`,
            `Pattern: ${pattern.description}`,
            `Source IP: ${event.source.ip}`,
          ],
        });

        // Auto-block for critical patterns
        if (pattern.severity === 'critical' || pattern.severity === 'high') {
          this.blockIP(event.source.ip, `Automated block: ${pattern.name}`, 4 * 60 * 60 * 1000); // 4 hours
        }
      }
    });
  }

  /**
   * Update IP reputation based on security event
   */
  private updateIPReputation(event: SecurityEvent): void {
    const ip = event.source.ip;
    if (!ip || ip === 'system') return;

    let suspiciousInfo = this.suspiciousIPs.get(ip) || { score: 0, lastActivity: new Date() };
    
    // Increase suspicious score based on event severity
    const scoreIncrease = {
      low: 1,
      medium: 3,
      high: 8,
      critical: 15,
    };

    suspiciousInfo.score += scoreIncrease[event.severity];
    suspiciousInfo.lastActivity = new Date();

    this.suspiciousIPs.set(ip, suspiciousInfo);

    // Auto-block if score is too high
    if (suspiciousInfo.score >= 50) {
      this.blockIP(ip, 'High suspicious activity score', 24 * 60 * 60 * 1000); // 24 hours
    }
  }

  /**
   * Start periodic maintenance tasks
   */
  private startPeriodicTasks(): void {
    // Cleanup expired blocks every hour
    setInterval(() => {
      const now = new Date();
      for (const [ip, block] of this.blockedIPs.entries()) {
        if (now > block.expiresAt) {
          this.blockedIPs.delete(ip);
        }
      }
    }, 60 * 60 * 1000);

    // Reset suspicious scores daily
    setInterval(() => {
      for (const [ip, info] of this.suspiciousIPs.entries()) {
        info.score = Math.max(0, info.score - 10);
        if (info.score === 0) {
          this.suspiciousIPs.delete(ip);
        }
      }
    }, 24 * 60 * 60 * 1000);

    // Generate health reports every 6 hours
    setInterval(() => {
      const health = this.assessSystemHealth();
      if (health.status === 'critical') {
        this.createAlert({
          type: 'system_health',
          message: 'Critical system health issues detected',
          severity: 'critical',
          conditions: health.issues,
        });
      }
    }, 6 * 60 * 60 * 1000);
  }

  // Additional helper methods for internal use...
  private generateId(): string {
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
  }

  private determineIncidentType(event: SecurityEvent): SecurityIncident['type'] {
    if (event.type.includes('injection') || event.type.includes('xss')) return 'attack';
    if (event.type.includes('breach') || event.type.includes('unauthorized')) return 'breach';
    if (event.type.includes('anomaly')) return 'anomaly';
    if (event.type.includes('policy')) return 'policy_violation';
    return 'attack';
  }

  private generateIncidentTitle(event: SecurityEvent): string {
    return `${event.type.replace(/_/g, ' ').toUpperCase()} from ${event.source.ip}`;
  }

  private generateIncidentDescription(event: SecurityEvent): string {
    return `Security incident detected: ${event.message}. Source: ${JSON.stringify(event.source)}`;
  }

  private assessImpact(event: SecurityEvent): SecurityIncident['impact'] {
    return {
      affectedSystems: ['web_application'],
      affectedUsers: 0,
      dataAtRisk: event.severity === 'critical' || event.severity === 'high',
      serviceDisruption: false,
    };
  }

  private getAutomaticContainmentActions(event: SecurityEvent): string[] {
    const actions = ['Event logged and analyzed'];
    
    if (event.severity === 'critical' || event.severity === 'high') {
      actions.push(`IP ${event.source.ip} flagged for review`);
      actions.push('Security team notified');
    }

    return actions;
  }

  private executeAutomaticResponse(incident: SecurityIncident): void {
    // Implementation would depend on your infrastructure
    console.warn(`[SECURITY INCIDENT] ${incident.title}`, incident);
  }

  private assessSystemHealth(): { status: 'healthy' | 'warning' | 'critical'; issues: string[] } {
    const issues: string[] = [];
    
    // Check recent error rates
    const recentEvents = this.events.slice(-100);
    const errorRate = recentEvents.filter(e => e.severity === 'high' || e.severity === 'critical').length / recentEvents.length;
    
    if (errorRate > 0.2) {
      issues.push(`High error rate: ${Math.round(errorRate * 100)}%`);
    }

    // Check for active critical incidents
    const criticalIncidents = this.incidents.filter(
      i => i.severity === 'critical' && i.status !== 'resolved'
    );
    
    if (criticalIncidents.length > 0) {
      issues.push(`${criticalIncidents.length} active critical incidents`);
    }

    // Check blocked IPs count
    if (this.blockedIPs.size > 100) {
      issues.push(`High number of blocked IPs: ${this.blockedIPs.size}`);
    }

    const status = issues.length === 0 ? 'healthy' : 
                  issues.some(i => i.includes('critical')) ? 'critical' : 'warning';

    return { status, issues };
  }

  private sendAlertNotification(alert: SecurityAlert): void {
    // Implementation would send notifications via email, Slack, etc.
    console.error(`[SECURITY ALERT] ${alert.message}`, alert);
  }

  private logEvent(event: SecurityEvent): void {
    console.log(`[SECURITY EVENT] ${event.type}: ${event.message}`);
  }

  private logIncident(incident: SecurityIncident): void {
    console.warn(`[SECURITY INCIDENT] ${incident.status}: ${incident.title}`);
  }

  private logAlert(alert: SecurityAlert): void {
    console.warn(`[SECURITY ALERT] ${alert.type}: ${alert.message}`);
  }

  // Additional utility methods...
  private getSeverityBreakdown(events: SecurityEvent[]): Record<string, number> {
    const breakdown: Record<string, number> = { low: 0, medium: 0, high: 0, critical: 0 };
    events.forEach(event => breakdown[event.severity]++);
    return breakdown;
  }

  private getAttackTypeBreakdown(events: SecurityEvent[]): Record<string, number> {
    const breakdown: Record<string, number> = {};
    events.forEach(event => {
      breakdown[event.type] = (breakdown[event.type] || 0) + 1;
    });
    return breakdown;
  }

  private getTopSourceIPs(events: SecurityEvent[], limit: number): { ip: string; count: number }[] {
    const ipCounts: Record<string, number> = {};
    events.forEach(event => {
      if (event.source.ip) {
        ipCounts[event.source.ip] = (ipCounts[event.source.ip] || 0) + 1;
      }
    });

    return Object.entries(ipCounts)
      .map(([ip, count]) => ({ ip, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, limit);
  }

  private calculateAverageResolutionTime(incidents: SecurityIncident[]): number {
    const resolvedIncidents = incidents.filter(i => i.metadata.resolvedAt);
    if (resolvedIncidents.length === 0) return 0;

    const totalTime = resolvedIncidents.reduce((sum, incident) => {
      const resolutionTime = incident.metadata.resolvedAt!.getTime() - incident.metadata.createdAt.getTime();
      return sum + resolutionTime;
    }, 0);

    return Math.round(totalTime / resolvedIncidents.length / (1000 * 60)); // Return in minutes
  }

  private generateSecurityRecommendations(events: SecurityEvent[], incidents: SecurityIncident[]): string[] {
    const recommendations: string[] = [];

    // Analyze attack patterns
    const attackTypes = this.getAttackTypeBreakdown(events);
    
    if (attackTypes.sql_injection > 0) {
      recommendations.push('Implement parameterized queries and input validation');
    }
    
    if (attackTypes.xss_attempt > 0) {
      recommendations.push('Strengthen Content Security Policy and output encoding');
    }
    
    if (attackTypes.rate_limit_violation > 10) {
      recommendations.push('Consider implementing more aggressive rate limiting');
    }

    // Analyze resolution patterns
    const avgResolutionTime = this.calculateAverageResolutionTime(incidents);
    if (avgResolutionTime > 60) { // More than 1 hour
      recommendations.push('Improve incident response time through automation');
    }

    return recommendations;
  }
}

// Export singleton instance
export const securityMonitor = new SecurityMonitor();

// Export types
export type { SecurityIncident, SecurityEvent, SecurityAlert, SecurityMetrics, ThreatPattern };