# =============================================================================
# CLARITY ENGINE - ENVIRONMENT VARIABLES TEMPLATE
# =============================================================================
# Copy this file to .env.local and fill in your actual values
# Never commit actual environment variables to version control!

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
NEXT_PUBLIC_APP_NAME="Clarity Engine"
NEXT_PUBLIC_APP_VERSION="1.0.0"
NEXT_PUBLIC_APP_DESCRIPTION="AI-Powered Construction Cost Calculator for India"
NEXT_PUBLIC_APP_URL="https://clarity-engine.vercel.app"

# Development
NODE_ENV="production"
NEXT_TELEMETRY_DISABLED="1"

# =============================================================================
# SUPABASE CONFIGURATION
# =============================================================================
NEXT_PUBLIC_SUPABASE_URL="your-supabase-project-url"
NEXT_PUBLIC_SUPABASE_ANON_KEY="your-supabase-anon-key"
SUPABASE_SERVICE_ROLE_KEY="your-supabase-service-role-key"

# =============================================================================
# AUTHENTICATION
# =============================================================================
# Google OAuth
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

# GitHub OAuth
GITHUB_CLIENT_ID="your-github-client-id"
GITHUB_CLIENT_SECRET="your-github-client-secret"

# =============================================================================
# MONITORING & ANALYTICS
# =============================================================================
# Google Analytics
NEXT_PUBLIC_GA_MEASUREMENT_ID="G-XXXXXXXXXX"

# PostHog Analytics
NEXT_PUBLIC_POSTHOG_KEY="your-posthog-key"
NEXT_PUBLIC_POSTHOG_HOST="https://app.posthog.com"

# Sentry Error Tracking
NEXT_PUBLIC_SENTRY_DSN="your-sentry-dsn"
SENTRY_ORG="your-sentry-org"
SENTRY_PROJECT="your-sentry-project"
SENTRY_AUTH_TOKEN="your-sentry-auth-token"

# =============================================================================
# CRON JOBS & MONITORING
# =============================================================================
CRON_SECRET="your-secure-cron-secret-key"
HEALTH_CHECK_TOKEN="your-health-check-token"

# =============================================================================
# NOTIFICATIONS
# =============================================================================
# Slack Webhooks
SLACK_WEBHOOK_URL="your-slack-webhook-url"
SLACK_ALERTS_WEBHOOK="your-slack-alerts-webhook"

# Email Configuration
EMAIL_FROM="<EMAIL>"
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASSWORD="your-email-app-password"

# Alert Recipients
ALERT_EMAIL="<EMAIL>"
NOTIFICATION_EMAIL="<EMAIL>"

# =============================================================================
# DEPLOYMENT CONFIGURATION
# =============================================================================
# Vercel
VERCEL_TOKEN="your-vercel-token"
VERCEL_ORG_ID="your-vercel-org-id"
VERCEL_PROJECT_ID="your-vercel-project-id"

# Database (if using external)
DATABASE_URL="your-database-connection-string"

# Redis (if using external caching)
REDIS_URL="your-redis-connection-string"

# =============================================================================
# API KEYS & EXTERNAL SERVICES
# =============================================================================
# Material Price APIs (future integration)
MATERIAL_PRICE_API_KEY="your-material-price-api-key"

# Weather API (for construction timing)
WEATHER_API_KEY="your-weather-api-key"

# Maps API (for location services)
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY="your-google-maps-api-key"

# =============================================================================
# FEATURE FLAGS
# =============================================================================
FEATURE_PDF_EXPORT="true"
FEATURE_SAVE_CALCULATIONS="true"
FEATURE_MATERIAL_PRICES="true"
FEATURE_ADVANCED_ANALYTICS="true"
FEATURE_MOBILE_APP="false"

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# JWT Secret for session management
JWT_SECRET="your-jwt-secret-key-minimum-32-characters"

# Rate Limiting
RATE_LIMIT_MAX="100"
RATE_LIMIT_WINDOW="60000"

# CORS Origins
CORS_ORIGINS="https://clarity-engine.vercel.app,https://www.clarity-engine.com"

# =============================================================================
# DEVELOPMENT TOOLS
# =============================================================================
# Bundle Analysis
ANALYZE="false"

# Debug Mode
DEBUG="false"

# =============================================================================
# NOTES FOR DEPLOYMENT
# =============================================================================
# 1. All NEXT_PUBLIC_ variables are exposed to the browser
# 2. Keep secrets secure and rotate them regularly
# 3. Use different values for staging and production
# 4. Set up monitoring for all external service dependencies
# 5. Test all integrations before going live

# =============================================================================
# REQUIRED FOR PRODUCTION DEPLOYMENT
# =============================================================================
# The following variables are REQUIRED for production:
# - NEXT_PUBLIC_SUPABASE_URL
# - NEXT_PUBLIC_SUPABASE_ANON_KEY
# - SUPABASE_SERVICE_ROLE_KEY
# - NEXT_PUBLIC_APP_URL
# - CRON_SECRET
# - JWT_SECRET