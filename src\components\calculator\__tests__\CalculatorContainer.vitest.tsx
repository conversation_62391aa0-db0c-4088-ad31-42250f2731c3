import { describe, it, expect, vi, beforeEach } from 'vitest';
import { screen, fireEvent, waitFor } from '@testing-library/react';
import { render } from '../../../test/utils';
import { CalculatorContainer } from '../CalculatorContainer';
import { createMockCalculationInput, createMockCalculationResult } from '../../../test/factories';

// Mock the calculator engine
vi.mock('../../../core/calculator/engine', () => ({
  calculateConstruction: vi.fn().mockResolvedValue(createMockCalculationResult()),
  validateInput: vi.fn().mockReturnValue({ isValid: true, errors: [] }),
}));

// Mock the materials loader
vi.mock('../../../lib/materials/loader', () => ({
  loadMaterials: vi.fn().mockResolvedValue({
    materials: [],
    locations: [
      { id: 'bangalore', name: 'Bangalore', multiplier: 1.0 },
      { id: 'mumbai', name: 'Mumbai', multiplier: 1.2 },
      { id: 'delhi', name: 'Delhi', multiplier: 1.05 },
    ],
    qualityTiers: ['smart', 'premium', 'luxury'],
    categories: ['structural', 'finishing', 'electrical', 'plumbing', 'external'],
  }),
}));

describe('CalculatorContainer', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render calculator form correctly', async () => {
    render(<CalculatorContainer />);

    await waitFor(() => {
      expect(screen.getByRole('form')).toBeInTheDocument();
    });

    expect(screen.getByLabelText(/square feet/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/location/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/quality tier/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/floors/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /calculate/i })).toBeInTheDocument();
  });

  it('should handle form submission correctly', async () => {
    const { calculateConstruction } = await import('../../../core/calculator/engine');
    
    render(<CalculatorContainer />);

    await waitFor(() => {
      expect(screen.getByRole('form')).toBeInTheDocument();
    });

    // Fill form
    const squareFeetInput = screen.getByLabelText(/square feet/i);
    const locationSelect = screen.getByLabelText(/location/i);
    const qualitySelect = screen.getByLabelText(/quality tier/i);
    const floorsInput = screen.getByLabelText(/floors/i);
    const calculateButton = screen.getByRole('button', { name: /calculate/i });

    fireEvent.change(squareFeetInput, { target: { value: '1200' } });
    fireEvent.change(locationSelect, { target: { value: 'bangalore' } });
    fireEvent.change(qualitySelect, { target: { value: 'smart' } });
    fireEvent.change(floorsInput, { target: { value: '2' } });

    fireEvent.click(calculateButton);

    await waitFor(() => {
      expect(calculateConstruction).toHaveBeenCalledWith({
        squareFeet: 1200,
        location: 'bangalore',
        qualityTier: 'smart',
        floors: 2,
        constructionType: 'independent_house',
      });
    });
  });

  it('should display calculation results', async () => {
    const mockResult = createMockCalculationResult({
      totalCost: 2160000,
      costPerSqft: 1800,
    });

    const { calculateConstruction } = await import('../../../core/calculator/engine');
    (calculateConstruction as any).mockResolvedValue(mockResult);

    render(<CalculatorContainer />);

    await waitFor(() => {
      expect(screen.getByRole('form')).toBeInTheDocument();
    });

    // Fill and submit form
    const squareFeetInput = screen.getByLabelText(/square feet/i);
    const calculateButton = screen.getByRole('button', { name: /calculate/i });

    fireEvent.change(squareFeetInput, { target: { value: '1200' } });
    fireEvent.click(calculateButton);

    await waitFor(() => {
      expect(screen.getByText(/₹21,60,000/)).toBeInTheDocument();
      expect(screen.getByText(/₹1,800/)).toBeInTheDocument();
    });
  });

  it('should handle loading state correctly', async () => {
    const { calculateConstruction } = await import('../../../core/calculator/engine');
    
    // Mock delayed response
    (calculateConstruction as any).mockImplementation(
      () => new Promise(resolve => setTimeout(resolve, 100))
    );

    render(<CalculatorContainer />);

    await waitFor(() => {
      expect(screen.getByRole('form')).toBeInTheDocument();
    });

    const squareFeetInput = screen.getByLabelText(/square feet/i);
    const calculateButton = screen.getByRole('button', { name: /calculate/i });

    fireEvent.change(squareFeetInput, { target: { value: '1200' } });
    fireEvent.click(calculateButton);

    // Should show loading state
    expect(screen.getByText(/calculating/i)).toBeInTheDocument();
    expect(calculateButton).toBeDisabled();

    await waitFor(() => {
      expect(screen.queryByText(/calculating/i)).not.toBeInTheDocument();
    });
  });

  it('should handle validation errors', async () => {
    const { validateInput } = await import('../../../core/calculator/engine');
    
    (validateInput as any).mockReturnValue({
      isValid: false,
      errors: ['Square footage must be greater than 0'],
    });

    render(<CalculatorContainer />);

    await waitFor(() => {
      expect(screen.getByRole('form')).toBeInTheDocument();
    });

    const squareFeetInput = screen.getByLabelText(/square feet/i);
    const calculateButton = screen.getByRole('button', { name: /calculate/i });

    fireEvent.change(squareFeetInput, { target: { value: '0' } });
    fireEvent.click(calculateButton);

    await waitFor(() => {
      expect(screen.getByText(/Square footage must be greater than 0/)).toBeInTheDocument();
    });
  });

  it('should handle calculation errors', async () => {
    const { calculateConstruction } = await import('../../../core/calculator/engine');
    
    (calculateConstruction as any).mockRejectedValue(new Error('Calculation failed'));

    render(<CalculatorContainer />);

    await waitFor(() => {
      expect(screen.getByRole('form')).toBeInTheDocument();
    });

    const squareFeetInput = screen.getByLabelText(/square feet/i);
    const calculateButton = screen.getByRole('button', { name: /calculate/i });

    fireEvent.change(squareFeetInput, { target: { value: '1200' } });
    fireEvent.click(calculateButton);

    await waitFor(() => {
      expect(screen.getByText(/error.*calculation/i)).toBeInTheDocument();
    });
  });

  it('should reset form when reset button is clicked', async () => {
    render(<CalculatorContainer />);

    await waitFor(() => {
      expect(screen.getByRole('form')).toBeInTheDocument();
    });

    const squareFeetInput = screen.getByLabelText(/square feet/i) as HTMLInputElement;
    const resetButton = screen.getByRole('button', { name: /reset/i });

    fireEvent.change(squareFeetInput, { target: { value: '1200' } });
    expect(squareFeetInput.value).toBe('1200');

    fireEvent.click(resetButton);

    await waitFor(() => {
      expect(squareFeetInput.value).toBe('');
    });
  });

  it('should update form values correctly', async () => {
    render(<CalculatorContainer />);

    await waitFor(() => {
      expect(screen.getByRole('form')).toBeInTheDocument();
    });

    const squareFeetInput = screen.getByLabelText(/square feet/i) as HTMLInputElement;
    const locationSelect = screen.getByLabelText(/location/i) as HTMLSelectElement;
    const qualitySelect = screen.getByLabelText(/quality tier/i) as HTMLSelectElement;
    const floorsInput = screen.getByLabelText(/floors/i) as HTMLInputElement;

    fireEvent.change(squareFeetInput, { target: { value: '1500' } });
    fireEvent.change(locationSelect, { target: { value: 'mumbai' } });
    fireEvent.change(qualitySelect, { target: { value: 'premium' } });
    fireEvent.change(floorsInput, { target: { value: '3' } });

    expect(squareFeetInput.value).toBe('1500');
    expect(locationSelect.value).toBe('mumbai');
    expect(qualitySelect.value).toBe('premium');
    expect(floorsInput.value).toBe('3');
  });

  it('should handle keyboard navigation', async () => {
    render(<CalculatorContainer />);

    await waitFor(() => {
      expect(screen.getByRole('form')).toBeInTheDocument();
    });

    const squareFeetInput = screen.getByLabelText(/square feet/i);
    const locationSelect = screen.getByLabelText(/location/i);
    const qualitySelect = screen.getByLabelText(/quality tier/i);
    const floorsInput = screen.getByLabelText(/floors/i);
    const calculateButton = screen.getByRole('button', { name: /calculate/i });

    // Tab through form elements
    squareFeetInput.focus();
    fireEvent.keyDown(squareFeetInput, { key: 'Tab' });
    expect(locationSelect).toHaveFocus();

    fireEvent.keyDown(locationSelect, { key: 'Tab' });
    expect(qualitySelect).toHaveFocus();

    fireEvent.keyDown(qualitySelect, { key: 'Tab' });
    expect(floorsInput).toHaveFocus();

    fireEvent.keyDown(floorsInput, { key: 'Tab' });
    expect(calculateButton).toHaveFocus();
  });

  it('should handle form submission with Enter key', async () => {
    const { calculateConstruction } = await import('../../../core/calculator/engine');
    
    render(<CalculatorContainer />);

    await waitFor(() => {
      expect(screen.getByRole('form')).toBeInTheDocument();
    });

    const squareFeetInput = screen.getByLabelText(/square feet/i);

    fireEvent.change(squareFeetInput, { target: { value: '1200' } });
    fireEvent.keyDown(squareFeetInput, { key: 'Enter' });

    await waitFor(() => {
      expect(calculateConstruction).toHaveBeenCalled();
    });
  });

  it('should validate input on blur', async () => {
    render(<CalculatorContainer />);

    await waitFor(() => {
      expect(screen.getByRole('form')).toBeInTheDocument();
    });

    const squareFeetInput = screen.getByLabelText(/square feet/i);

    fireEvent.change(squareFeetInput, { target: { value: '-100' } });
    fireEvent.blur(squareFeetInput);

    await waitFor(() => {
      expect(screen.getByText(/must be greater than 0/i)).toBeInTheDocument();
    });
  });

  it('should handle mobile responsive layout', async () => {
    // Mock mobile viewport
    Object.defineProperty(window, 'innerWidth', { value: 375 });
    Object.defineProperty(window, 'innerHeight', { value: 667 });

    render(<CalculatorContainer />);

    await waitFor(() => {
      expect(screen.getByRole('form')).toBeInTheDocument();
    });

    const container = screen.getByRole('form').parentElement;
    expect(container).toHaveClass('mobile-layout');
  });

  it('should handle accessibility requirements', async () => {
    render(<CalculatorContainer />);

    await waitFor(() => {
      expect(screen.getByRole('form')).toBeInTheDocument();
    });

    // Check ARIA labels
    expect(screen.getByLabelText(/square feet/i)).toHaveAttribute('aria-label');
    expect(screen.getByLabelText(/location/i)).toHaveAttribute('aria-label');
    expect(screen.getByLabelText(/quality tier/i)).toHaveAttribute('aria-label');
    expect(screen.getByLabelText(/floors/i)).toHaveAttribute('aria-label');

    // Check button accessibility
    const calculateButton = screen.getByRole('button', { name: /calculate/i });
    expect(calculateButton).toHaveAttribute('type', 'submit');
    expect(calculateButton).toHaveAttribute('aria-label');
  });

  it('should handle performance optimization', async () => {
    const renderStart = performance.now();
    
    render(<CalculatorContainer />);

    await waitFor(() => {
      expect(screen.getByRole('form')).toBeInTheDocument();
    });

    const renderEnd = performance.now();
    const renderTime = renderEnd - renderStart;

    // Should render within 100ms
    expect(renderTime).toBeLessThan(100);
  });

  it('should handle memory cleanup', async () => {
    const { unmount } = render(<CalculatorContainer />);

    await waitFor(() => {
      expect(screen.getByRole('form')).toBeInTheDocument();
    });

    // Unmount component
    unmount();

    // Should not throw errors or memory leaks
    expect(document.body.innerHTML).toBe('');
  });
});