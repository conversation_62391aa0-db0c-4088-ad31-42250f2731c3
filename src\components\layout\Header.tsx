'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Menu, X, Home, Calculator, Info, FolderIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';
import { AuthModal } from '@/components/auth/AuthModal';
import { UserMenu } from '@/components/auth/UserMenu';

export function Header() {
  const { user, loading } = useAuth();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [authModalOpen, setAuthModalOpen] = useState(false);
  const [authMode, setAuthMode] = useState<'signin' | 'signup'>('signin');

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const openSignIn = () => {
    setAuthMode('signin');
    setAuthModalOpen(true);
  };

  const openSignUp = () => {
    setAuthMode('signup');
    setAuthModalOpen(true);
  };

  const baseNavItems = [
    { href: '/', label: 'Home', icon: Home },
    { href: '/calculator', label: 'Calculator', icon: Calculator },
    { href: '/about', label: 'About', icon: Info },
  ];

  const navItems = user 
    ? [...baseNavItems, { href: '/projects', label: 'My Projects', icon: FolderIcon }]
    : baseNavItems;

  return (
    <header className='sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60'>
      <div className='container flex h-16 items-center justify-between'>
        {/* Logo */}
        <Link href='/' className='flex items-center space-x-2'>
          <div className='h-8 w-8 rounded bg-primary flex items-center justify-center'>
            <span className='text-primary-foreground font-bold text-lg'>C</span>
          </div>
          <span className='font-bold text-xl hidden sm:inline-block'>
            Clarity Engine
          </span>
        </Link>

        {/* Desktop Navigation */}
        <nav className='hidden md:flex items-center space-x-6'>
          {navItems.map(item => (
            <Link
              key={item.href}
              href={item.href}
              className='flex items-center space-x-1 text-sm font-medium transition-colors hover:text-primary'
            >
              <item.icon className='h-4 w-4' />
              <span>{item.label}</span>
            </Link>
          ))}
        </nav>

        {/* User Account Area (Desktop) */}
        <div className='hidden md:flex items-center space-x-4'>
          {loading ? (
            <div className="h-8 w-8 animate-pulse rounded-full bg-gray-200" />
          ) : user ? (
            <UserMenu />
          ) : (
            <>
              <Button variant='ghost' size='sm' onClick={openSignIn}>
                Sign In
              </Button>
              <Button size='sm' onClick={openSignUp}>
                Get Started
              </Button>
            </>
          )}
        </div>

        {/* Mobile Menu Button */}
        <Button
          variant='ghost'
          size='icon'
          className='md:hidden'
          onClick={toggleMobileMenu}
          aria-label='Toggle navigation menu'
          aria-expanded={isMobileMenuOpen}
        >
          {isMobileMenuOpen ? (
            <X className='h-5 w-5' />
          ) : (
            <Menu className='h-5 w-5' />
          )}
        </Button>
      </div>

      {/* Mobile Menu */}
      {isMobileMenuOpen && (
        <div className='md:hidden border-t'>
          <nav className='container py-4 space-y-3'>
            {navItems.map(item => (
              <Link
                key={item.href}
                href={item.href}
                className='flex items-center space-x-2 text-sm font-medium py-2 px-3 rounded-md hover:bg-accent transition-colors'
                onClick={() => setIsMobileMenuOpen(false)}
              >
                <item.icon className='h-4 w-4' />
                <span>{item.label}</span>
              </Link>
            ))}
            <div className='pt-3 border-t space-y-2'>
              {loading ? (
                <div className="h-8 w-full animate-pulse rounded bg-gray-200" />
              ) : user ? (
                <div className="flex items-center justify-between py-2">
                  <span className="text-sm text-gray-600">{user.email}</span>
                  <UserMenu />
                </div>
              ) : (
                <>
                  <Button
                    variant='ghost'
                    className='w-full justify-start'
                    size='sm'
                    onClick={openSignIn}
                  >
                    Sign In
                  </Button>
                  <Button className='w-full' size='sm' onClick={openSignUp}>
                    Get Started
                  </Button>
                </>
              )}
            </div>
          </nav>
        </div>
      )}

      {/* Auth Modal */}
      <AuthModal
        isOpen={authModalOpen}
        onClose={() => setAuthModalOpen(false)}
        mode={authMode}
        onModeChange={setAuthMode}
      />
    </header>
  );
}
