/**
 * Material Integration Test Suite
 * Tests the integration between materials database and calculation engine
 */

const path = require('path');

console.log('🔗 Testing Material Database Integration...\n');

// Mock calculation input
const mockInput = {
  builtUpArea: 1200,
  floors: 1,
  qualityTier: 'premium',
  location: 'bangalore',
  hasStilt: false,
  parkingType: 'open',
  hasBasement: false
};

console.log('📋 Test Input:');
console.log(`   Built-up Area: ${mockInput.builtUpArea} sqft`);
console.log(`   Floors: G+${mockInput.floors}`);
console.log(`   Quality Tier: ${mockInput.qualityTier}`);
console.log(`   Location: ${mockInput.location}`);

// Test 1: Material requirements calculation
function testMaterialRequirements() {
  console.log('\n🧮 Test 1: Material Requirements Calculation...');

  try {
    // Load materials data
    const materialsPath = path.join(__dirname, '../../data/materials/core-materials.json');
    const materialsData = JSON.parse(require('fs').readFileSync(materialsPath, 'utf8'));

    // Calculate areas (mock implementation)
    const areas = {
      builtUpArea: mockInput.builtUpArea,
      wallArea: mockInput.builtUpArea * 3.2, // 3.2:1 ratio
      floorArea: mockInput.builtUpArea,
      roofArea: mockInput.builtUpArea / (mockInput.floors + 1),
      foundationArea: mockInput.builtUpArea / (mockInput.floors + 1) * 1.1
    };

    console.log('📐 Calculated Areas:');
    console.log(`   Built-up: ${areas.builtUpArea} sqft`);
    console.log(`   Wall: ${areas.wallArea} sqft`);
    console.log(`   Floor: ${areas.floorArea} sqft`);
    console.log(`   Roof: ${areas.roofArea} sqft`);

    // Mock material requirements calculation
    const requirements = [
      {
        materialId: 'cement_opc53_ultratech',
        quantityRequired: Math.ceil(areas.builtUpArea * 0.35 * 1.2), // Premium tier multiplier
        unit: 'bag',
        category: 'Cement'
      },
      {
        materialId: 'steel_tmt_fe500d_tata',
        quantityRequired: Math.ceil(areas.builtUpArea * 4.2 * 1.2),
        unit: 'kg',
        category: 'Steel'
      },
      {
        materialId: 'brick_flyash_billtech', // Premium tier selection
        quantityRequired: Math.ceil(areas.wallArea * 12.0),
        unit: 'piece',
        category: 'Bricks'
      },
      {
        materialId: 'tile_vitrified_kajaria', // Premium tier selection
        quantityRequired: Math.ceil(areas.floorArea * 1.1 * 100) / 100,
        unit: 'sqm',
        category: 'Tiles'
      },
      {
        materialId: 'wire_copper_havells',
        quantityRequired: Math.ceil(areas.builtUpArea * 8.5),
        unit: 'meter',
        category: 'Electrical'
      },
      {
        materialId: 'pipe_pvc_supreme',
        quantityRequired: Math.ceil(areas.builtUpArea * 2.2),
        unit: 'meter',
        category: 'Plumbing'
      },
      {
        materialId: 'paint_interior_asian',
        quantityRequired: Math.ceil(areas.wallArea * 0.007 * 100) / 100,
        unit: 'liter',
        category: 'Paint'
      }
    ];

    console.log('\n📦 Material Requirements:');
    requirements.forEach(req => {
      const material = materialsData.materials.find(m => m.id === req.materialId);
      console.log(`   ${material?.name || req.materialId}: ${req.quantityRequired} ${req.unit}`);
    });

    return requirements;

  } catch (error) {
    console.error('❌ Error in material requirements calculation:', error.message);
    return [];
  }
}

// Test 2: Cost calculation
function testCostCalculation(requirements) {
  console.log('\n💰 Test 2: Cost Calculation...');

  try {
    const materialsPath = path.join(__dirname, '../../data/materials/core-materials.json');
    const materialsData = JSON.parse(require('fs').readFileSync(materialsPath, 'utf8'));

    let totalCost = 0;
    let totalWastage = 0;
    const categoryTotals = {};
    const costBreakdown = [];

    requirements.forEach(req => {
      const material = materialsData.materials.find(m => m.id === req.materialId);
      if (!material) {
        console.log(`⚠️  Material not found: ${req.materialId}`);
        return;
      }

      // Get regional pricing
      const pricing = material.pricing[mockInput.location] || material.pricing.default;
      const unitPrice = pricing.bulk; // Use bulk pricing

      // Calculate costs
      const subtotal = unitPrice * req.quantityRequired;
      const wastageAmount = (subtotal * material.wastagePercentage) / 100;
      const finalCost = subtotal + wastageAmount;

      totalCost += finalCost;
      totalWastage += wastageAmount;

      // Category totals
      categoryTotals[req.category] = (categoryTotals[req.category] || 0) + finalCost;

      costBreakdown.push({
        materialName: material.name,
        category: req.category,
        quantity: req.quantityRequired,
        unit: req.unit,
        unitPrice,
        subtotal,
        wastage: material.wastagePercentage,
        wastageAmount,
        finalCost,
        supplier: material.brand
      });
    });

    console.log('🧾 Detailed Cost Breakdown:');
    costBreakdown.forEach(item => {
      console.log(`   ${item.materialName}:`);
      console.log(`      ${item.quantity} ${item.unit} × ₹${item.unitPrice} = ₹${item.subtotal.toFixed(2)}`);
      console.log(`      + Wastage (${item.wastage}%): ₹${item.wastageAmount.toFixed(2)}`);
      console.log(`      = Final Cost: ₹${item.finalCost.toFixed(2)}`);
      console.log('');
    });

    console.log('📊 Category-wise Totals:');
    Object.entries(categoryTotals).forEach(([category, cost]) => {
      const percentage = (cost / totalCost) * 100;
      console.log(`   ${category}: ₹${cost.toFixed(2)} (${percentage.toFixed(1)}%)`);
    });

    console.log(`\n💸 Total Material Cost: ₹${totalCost.toFixed(2)}`);
    console.log(`⚠️  Total Wastage: ₹${totalWastage.toFixed(2)} (${((totalWastage/totalCost)*100).toFixed(1)}%)`);
    console.log(`📈 Cost per sqft: ₹${(totalCost/mockInput.builtUpArea).toFixed(2)}`);

    return {
      totalCost,
      totalWastage,
      categoryTotals,
      costBreakdown,
      costPerSqft: totalCost / mockInput.builtUpArea
    };

  } catch (error) {
    console.error('❌ Error in cost calculation:', error.message);
    return null;
  }
}

// Test 3: Quality tier comparison
function testQualityTierComparison() {
  console.log('\n⭐ Test 3: Quality Tier Comparison...');

  try {
    const materialsPath = path.join(__dirname, '../../data/materials/core-materials.json');
    const materialsData = JSON.parse(require('fs').readFileSync(materialsPath, 'utf8'));

    const tiers = ['smart', 'premium', 'luxury'];
    const tierMaterials = {
      smart: {
        cement: 'cement_opc43_acc',
        steel: 'steel_tmt_fe500_jsw',
        brick: 'brick_red_clay_standard',
        tile: 'tile_ceramic_somany'
      },
      premium: {
        cement: 'cement_opc53_ultratech',
        steel: 'steel_tmt_fe500d_tata',
        brick: 'brick_flyash_billtech',
        tile: 'tile_vitrified_kajaria'
      },
      luxury: {
        cement: 'cement_opc53_ultratech',
        steel: 'steel_tmt_fe500d_tata',
        brick: 'block_aac_siporex',
        tile: 'tile_vitrified_kajaria'
      }
    };

    console.log('🏗️ Material Selection by Quality Tier:');

    tiers.forEach(tier => {
      console.log(`\n   ${tier.toUpperCase()} CHOICE:`);

      let tierQualityTotal = 0;
      let tierCostExample = 0;
      let materialCount = 0;

      Object.entries(tierMaterials[tier]).forEach(([category, materialId]) => {
        const material = materialsData.materials.find(m => m.id === materialId);
        if (material) {
          const pricing = material.pricing[mockInput.location] || material.pricing.default;
          console.log(`      ${category}: ${material.name} (Quality: ${material.qualityScore}/10, Price: ₹${pricing.bulk}/${material.unit})`);

          tierQualityTotal += material.qualityScore;
          tierCostExample += pricing.bulk;
          materialCount++;
        }
      });

      if (materialCount > 0) {
        const avgQuality = tierQualityTotal / materialCount;
        console.log(`      Average Quality Score: ${avgQuality.toFixed(1)}/10`);
        console.log(`      Sample Cost Index: ₹${tierCostExample.toFixed(2)}`);
      }
    });

    return true;

  } catch (error) {
    console.error('❌ Error in quality tier comparison:', error.message);
    return false;
  }
}

// Test 4: Alternative materials
function testMaterialAlternatives() {
  console.log('\n🔄 Test 4: Material Alternatives...');

  try {
    const materialsPath = path.join(__dirname, '../../data/materials/core-materials.json');
    const materialsData = JSON.parse(require('fs').readFileSync(materialsPath, 'utf8'));

    const testMaterialId = 'cement_opc53_ultratech';
    const testMaterial = materialsData.materials.find(m => m.id === testMaterialId);

    if (!testMaterial) {
      console.log('❌ Test material not found');
      return false;
    }

    console.log(`🔍 Finding alternatives for: ${testMaterial.name}`);
    console.log(`   Original - Quality: ${testMaterial.qualityScore}/10, Price: ₹${testMaterial.pricing.bangalore.bulk}`);

    // Find materials in same category
    const alternatives = materialsData.materials.filter(m =>
      m.category === testMaterial.category &&
      m.id !== testMaterial.id
    );

    console.log('\n📋 Available Alternatives:');
    alternatives.forEach(alt => {
      const priceDiff = ((alt.pricing.bangalore.bulk - testMaterial.pricing.bangalore.bulk) / testMaterial.pricing.bangalore.bulk) * 100;
      const qualityDiff = alt.qualityScore - testMaterial.qualityScore;

      let recommendation = 'Alternative';
      if (qualityDiff > 0.5) recommendation = 'Upgrade';
      else if (qualityDiff < -0.5) recommendation = 'Downgrade';

      console.log(`   ${alt.name}:`);
      console.log(`      Quality: ${alt.qualityScore}/10 (${qualityDiff > 0 ? '+' : ''}${qualityDiff.toFixed(1)})`);
      console.log(`      Price: ₹${alt.pricing.bangalore.bulk} (${priceDiff > 0 ? '+' : ''}${priceDiff.toFixed(1)}%)`);
      console.log(`      Recommendation: ${recommendation}`);
      console.log('');
    });

    return true;

  } catch (error) {
    console.error('❌ Error in material alternatives test:', error.message);
    return false;
  }
}

// Test 5: Regional pricing comparison
function testRegionalPricing() {
  console.log('\n🌍 Test 5: Regional Pricing Comparison...');

  try {
    const materialsPath = path.join(__dirname, '../../data/materials/core-materials.json');
    const materialsData = JSON.parse(require('fs').readFileSync(materialsPath, 'utf8'));

    const testMaterial = materialsData.materials[0]; // First material
    const regions = materialsData.metadata.regions;

    console.log(`📍 Regional pricing for: ${testMaterial.name}`);
    console.log('   (Bulk pricing comparison)\n');

    let minPrice = Infinity;
    let maxPrice = 0;
    let minRegion = '';
    let maxRegion = '';

    regions.forEach(region => {
      const pricing = testMaterial.pricing[region];
      if (pricing && pricing.bulk) {
        console.log(`   ${region.toUpperCase()}: ₹${pricing.bulk}`);

        if (pricing.bulk < minPrice) {
          minPrice = pricing.bulk;
          minRegion = region;
        }
        if (pricing.bulk > maxPrice) {
          maxPrice = pricing.bulk;
          maxRegion = region;
        }
      }
    });

    const priceVariation = ((maxPrice - minPrice) / minPrice) * 100;

    console.log(`\n📊 Price Analysis:`);
    console.log(`   Cheapest: ${minRegion.toUpperCase()} - ₹${minPrice}`);
    console.log(`   Most Expensive: ${maxRegion.toUpperCase()} - ₹${maxPrice}`);
    console.log(`   Price Variation: ${priceVariation.toFixed(1)}%`);

    return true;

  } catch (error) {
    console.error('❌ Error in regional pricing test:', error.message);
    return false;
  }
}

// Run all integration tests
function runIntegrationTests() {
  console.log('🚀 Material Database Integration Test Suite');
  console.log('============================================');

  const requirements = testMaterialRequirements();

  if (requirements.length === 0) {
    console.log('\n❌ Cannot proceed with cost tests due to requirements failure');
    return;
  }

  const costResult = testCostCalculation(requirements);
  testQualityTierComparison();
  testMaterialAlternatives();
  testRegionalPricing();

  console.log('\n🎉 Integration Test Summary:');
  console.log(`   ✅ Material requirements calculated: ${requirements.length} materials`);

  if (costResult) {
    console.log(`   ✅ Total project cost: ₹${costResult.totalCost.toFixed(2)}`);
    console.log(`   ✅ Cost per sqft: ₹${costResult.costPerSqft.toFixed(2)}`);
    console.log(`   ✅ Material wastage: ${((costResult.totalWastage/costResult.totalCost)*100).toFixed(1)}%`);
  }

  console.log('\n🔗 Integration tests completed successfully!');
  console.log('📈 Ready for production integration with calculator engine.');
}

// Execute integration tests
runIntegrationTests();