/**
 * API Integration Tests for Calculator - Jest Format
 * Comprehensive test suite for API endpoints and integration functionality
 */

import { testScenarios, validationTestCases, validateCalculationResult } from './api-integration-utils';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';

/**
 * Utility Functions
 */
async function makeApiRequest(endpoint, method = 'GET', body = null) {
  const config = {
    method,
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
  };

  if (body) {
    config.body = JSON.stringify(body);
  }

  const response = await fetch(`${API_BASE_URL}${endpoint}`, config);
  const data = await response.json();

  return {
    status: response.status,
    headers: Object.fromEntries(response.headers.entries()),
    data,
    response
  };
}

describe('Calculator API Integration Tests', () => {
  describe('Calculation Scenarios', () => {
    it('should calculate smart tier Bangalore project correctly', async () => {
      const testData = testScenarios.smartTierBangalore;
      const result = await makeApiRequest('/api/calculate', 'POST', testData);
      
      expect(result.status).toBe(200);
      expect(result.data.success).toBe(true);
      
      const validationErrors = validateCalculationResult(
        result.data.data,
        testData.expectedRange,
        testData
      );
      
      expect(validationErrors).toHaveLength(0);
    });

    it('should calculate premium tier Mumbai project correctly', async () => {
      const testData = testScenarios.premiumTierMumbai;
      const result = await makeApiRequest('/api/calculate', 'POST', testData);
      
      expect(result.status).toBe(200);
      expect(result.data.success).toBe(true);
      
      const validationErrors = validateCalculationResult(
        result.data.data,
        testData.expectedRange,
        testData
      );
      
      expect(validationErrors).toHaveLength(0);
    });

    it('should calculate luxury tier Delhi project correctly', async () => {
      const testData = testScenarios.luxuryTierDelhi;
      const result = await makeApiRequest('/api/calculate', 'POST', testData);
      
      expect(result.status).toBe(200);
      expect(result.data.success).toBe(true);
      
      const validationErrors = validateCalculationResult(
        result.data.data,
        testData.expectedRange,
        testData
      );
      
      expect(validationErrors).toHaveLength(0);
    });
  });

  describe('Validation Tests', () => {
    it('should reject missing required fields', async () => {
      const testData = validationTestCases.missingRequiredFields;
      const result = await makeApiRequest('/api/calculate', 'POST', testData);
      
      expect(result.status).toBe(400);
      expect(result.data.success).toBe(false);
    });

    it('should reject invalid built up area', async () => {
      const testData = validationTestCases.invalidBuiltUpArea;
      const result = await makeApiRequest('/api/calculate', 'POST', testData);
      
      expect(result.status).toBe(400);
      expect(result.data.success).toBe(false);
    });

    it('should reject invalid quality tier', async () => {
      const testData = validationTestCases.invalidQualityTier;
      const result = await makeApiRequest('/api/calculate', 'POST', testData);
      
      expect(result.status).toBe(400);
      expect(result.data.success).toBe(false);
    });
  });

  describe('API Documentation', () => {
    it('should provide API documentation on GET request', async () => {
      const result = await makeApiRequest('/api/calculate', 'GET');
      
      expect(result.status).toBe(200);
      expect(result.data.name).toBeDefined();
    });
  });
});