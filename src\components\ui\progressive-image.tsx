/**
 * Progressive Image Loading Component
 * Provides optimized image loading with skeleton placeholders and lazy loading
 */

'use client';

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import { fadeIn, scaleIn } from '@/lib/animations';
import { Skeleton } from './skeleton';

interface ProgressiveImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  src: string;
  alt: string;
  placeholder?: string;
  blurDataURL?: string;
  className?: string;
  containerClassName?: string;
  skeletonClassName?: string;
  loadingClassName?: string;
  errorClassName?: string;
  onLoad?: () => void;
  onError?: () => void;
  lazy?: boolean;
  aspectRatio?: number;
  sizes?: string;
  priority?: boolean;
  quality?: number;
  showSkeleton?: boolean;
  skeletonVariant?: 'shimmer' | 'pulse' | 'wave';
  transition?: 'fade' | 'scale' | 'slide';
  retry?: boolean;
  maxRetries?: number;
  retryDelay?: number;
}

export function ProgressiveImage({
  src,
  alt,
  placeholder,
  blurDataURL,
  className,
  containerClassName,
  skeletonClassName,
  loadingClassName,
  errorClassName,
  onLoad,
  onError,
  lazy = true,
  aspectRatio,
  sizes,
  priority = false,
  quality = 75,
  showSkeleton = true,
  skeletonVariant = 'shimmer',
  transition = 'fade',
  retry = true,
  maxRetries = 3,
  retryDelay = 1000,
  ...props
}: ProgressiveImageProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [isError, setIsError] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const [isInView, setIsInView] = useState(!lazy || priority);
  const imgRef = useRef<HTMLImageElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (!lazy || priority || isInView) return;

    const observer = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      {
        threshold: 0.1,
        rootMargin: '50px'
      }
    );

    if (containerRef.current) {
      observer.observe(containerRef.current);
    }

    return () => observer.disconnect();
  }, [lazy, priority, isInView]);

  // Handle image load
  const handleLoad = useCallback(() => {
    setIsLoading(false);
    setIsError(false);
    onLoad?.();
  }, [onLoad]);

  // Handle image error with retry logic
  const handleError = useCallback(() => {
    if (retry && retryCount < maxRetries) {
      setTimeout(() => {
        setRetryCount(prev => prev + 1);
        if (imgRef.current) {
          imgRef.current.src = src;
        }
      }, retryDelay * (retryCount + 1));
    } else {
      setIsLoading(false);
      setIsError(true);
      onError?.();
    }
  }, [retry, retryCount, maxRetries, retryDelay, src, onError]);

  // Retry function for manual retry
  const handleRetry = useCallback(() => {
    setIsLoading(true);
    setIsError(false);
    setRetryCount(0);
    if (imgRef.current) {
      imgRef.current.src = src;
    }
  }, [src]);

  // Animation variants
  const transitions = {
    fade: fadeIn,
    scale: scaleIn,
    slide: {
      initial: { opacity: 0, x: -20 },
      animate: { opacity: 1, x: 0 },
      exit: { opacity: 0, x: 20 }
    }
  };

  // Skeleton animation mapping
  const skeletonAnimations = {
    shimmer: 'shimmer',
    pulse: 'pulse',
    wave: 'wave'
  };

  return (
    <div
      ref={containerRef}
      className={cn(
        'relative overflow-hidden',
        aspectRatio && 'w-full',
        containerClassName
      )}
      style={{
        aspectRatio: aspectRatio ? `${aspectRatio}` : undefined
      }}
    >
      <AnimatePresence mode="wait">
        {/* Loading skeleton */}
        {isLoading && showSkeleton && (
          <motion.div
            key="skeleton"
            variants={transitions[transition]}
            initial="initial"
            animate="animate"
            exit="exit"
            className="absolute inset-0"
          >
            <Skeleton
              variant="image"
              animation={skeletonAnimations[skeletonVariant]}
              className={cn(
                'w-full h-full',
                skeletonClassName
              )}
            />
          </motion.div>
        )}

        {/* Error state */}
        {isError && (
          <motion.div
            key="error"
            variants={transitions[transition]}
            initial="initial"
            animate="animate"
            exit="exit"
            className={cn(
              'absolute inset-0 flex flex-col items-center justify-center bg-gray-100 text-gray-500',
              errorClassName
            )}
          >
            <svg
              className="w-8 h-8 mb-2 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
              />
            </svg>
            <span className="text-sm text-center mb-2">Failed to load image</span>
            {retry && retryCount < maxRetries && (
              <button
                onClick={handleRetry}
                className="px-3 py-1 text-xs bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors"
              >
                Retry ({maxRetries - retryCount} left)
              </button>
            )}
          </motion.div>
        )}

        {/* Actual image */}
        {isInView && (
          <motion.img
            key="image"
            ref={imgRef}
            src={src}
            alt={alt}
            variants={transitions[transition]}
            initial="initial"
            animate={!isLoading && !isError ? "animate" : "initial"}
            exit="exit"
            className={cn(
              'w-full h-full object-cover',
              isLoading && loadingClassName,
              className
            )}
            onLoad={handleLoad}
            onError={handleError}
            loading={lazy && !priority ? 'lazy' : 'eager'}
            {...props}
          />
        )}
      </AnimatePresence>

      {/* Progress indicator for loading */}
      {isLoading && (
        <div className="absolute bottom-2 right-2">
          <div className="w-4 h-4 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin" />
        </div>
      )}
    </div>
  );
}

// Gallery component with progressive loading
interface ProgressiveGalleryProps {
  images: Array<{
    src: string;
    alt: string;
    placeholder?: string;
    blurDataURL?: string;
  }>;
  className?: string;
  imageClassName?: string;
  columns?: number;
  gap?: number;
  aspectRatio?: number;
  lazy?: boolean;
  staggerDelay?: number;
  showCounter?: boolean;
  onImageClick?: (image: any, index: number) => void;
}

export function ProgressiveGallery({
  images,
  className,
  imageClassName,
  columns = 3,
  gap = 4,
  aspectRatio = 1,
  lazy = true,
  staggerDelay = 100,
  showCounter = false,
  onImageClick
}: ProgressiveGalleryProps) {
  const [loadedCount, setLoadedCount] = useState(0);

  const handleImageLoad = useCallback(() => {
    setLoadedCount(prev => prev + 1);
  }, []);

  return (
    <div className={cn('space-y-4', className)}>
      {showCounter && (
        <div className="flex items-center justify-between text-sm text-gray-600">
          <span>Loading images...</span>
          <span>{loadedCount} / {images.length}</span>
        </div>
      )}
      
      <motion.div
        className={cn(
          'grid gap-4',
          columns === 2 && 'grid-cols-1 sm:grid-cols-2',
          columns === 3 && 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3',
          columns === 4 && 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
        )}
        style={{ gap: `${gap * 0.25}rem` }}
        variants={{
          initial: {},
          animate: {
            transition: {
              staggerChildren: staggerDelay / 1000,
              delayChildren: 0.1
            }
          }
        }}
        initial="initial"
        animate="animate"
      >
        {images.map((image, index) => (
          <motion.div
            key={index}
            variants={{
              initial: { opacity: 0, y: 20 },
              animate: { opacity: 1, y: 0 }
            }}
            className={cn(
              'cursor-pointer hover:opacity-90 transition-opacity',
              onImageClick && 'hover:scale-105 transition-transform'
            )}
            onClick={() => onImageClick?.(image, index)}
          >
            <ProgressiveImage
              src={image.src}
              alt={image.alt}
              placeholder={image.placeholder}
              blurDataURL={image.blurDataURL}
              aspectRatio={aspectRatio}
              lazy={lazy}
              className={imageClassName}
              onLoad={handleImageLoad}
              transition="scale"
            />
          </motion.div>
        ))}
      </motion.div>
      
      {showCounter && loadedCount === images.length && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-center text-green-600 text-sm"
        >
          All images loaded successfully!
        </motion.div>
      )}
    </div>
  );
}

// Image with blur placeholder
interface BlurImageProps extends Omit<ProgressiveImageProps, 'placeholder'> {
  blurDataURL: string;
  width?: number;
  height?: number;
}

export function BlurImage({
  blurDataURL,
  width,
  height,
  ...props
}: BlurImageProps) {
  const [isLoading, setIsLoading] = useState(true);

  return (
    <div className="relative">
      {/* Blur placeholder */}
      <img
        src={blurDataURL}
        alt=""
        className={cn(
          'absolute inset-0 w-full h-full object-cover transition-opacity duration-300',
          isLoading ? 'opacity-100' : 'opacity-0'
        )}
        style={{
          filter: 'blur(10px)',
          transform: 'scale(1.1)'
        }}
        aria-hidden="true"
      />
      
      {/* Actual image */}
      <ProgressiveImage
        {...props}
        onLoad={() => {
          setIsLoading(false);
          props.onLoad?.();
        }}
        showSkeleton={false}
        style={{
          width: width ? `${width}px` : undefined,
          height: height ? `${height}px` : undefined,
          ...props.style
        }}
      />
    </div>
  );
}

// Optimized image with multiple sources
interface OptimizedImageProps extends ProgressiveImageProps {
  sources?: Array<{
    srcSet: string;
    media?: string;
    type?: string;
  }>;
  webpSrc?: string;
  avifSrc?: string;
}

export function OptimizedImage({
  sources,
  webpSrc,
  avifSrc,
  src,
  ...props
}: OptimizedImageProps) {
  return (
    <picture>
      {/* AVIF source */}
      {avifSrc && (
        <source srcSet={avifSrc} type="image/avif" />
      )}
      
      {/* WebP source */}
      {webpSrc && (
        <source srcSet={webpSrc} type="image/webp" />
      )}
      
      {/* Custom sources */}
      {sources?.map((source, index) => (
        <source
          key={index}
          srcSet={source.srcSet}
          media={source.media}
          type={source.type}
        />
      ))}
      
      {/* Fallback image */}
      <ProgressiveImage src={src} {...props} />
    </picture>
  );
}