/**
 * React Performance Profiler
 * Comprehensive monitoring and optimization tools for React components
 */

import * as React from 'react';
import { Profiler, ProfilerOnRenderCallback } from 'react';

// Component Performance Metrics
interface ComponentMetrics {
  id: string;
  displayName: string;
  renderCount: number;
  totalRenderTime: number;
  averageRenderTime: number;
  maxRenderTime: number;
  minRenderTime: number;
  lastRenderTime: number;
  memorySizeEstimate: number;
  childrenCount: number;
  propsCount: number;
  stateCount: number;
  hooksCount: number;
  reRenderReasons: string[];
  performance: {
    isOptimized: boolean;
    optimizationScore: number;
    warnings: string[];
    suggestions: string[];
  };
}

// Memory Leak Detection
interface MemoryLeakDetector {
  componentId: string;
  mountTime: number;
  unmountTime?: number;
  renderCount: number;
  memoryGrowth: number[];
  isLeaking: boolean;
  leakSeverity: 'low' | 'medium' | 'high' | 'critical';
}

// Performance Budget
interface PerformanceBudget {
  maxRenderTime: number;
  maxMemoryUsage: number;
  maxReRenders: number;
  maxChildren: number;
  budgetPeriod: number; // in milliseconds
}

// Component Performance Profile
interface ComponentProfile {
  id: string;
  metrics: ComponentMetrics;
  memoryLeaks: MemoryLeakDetector[];
  budget: PerformanceBudget;
  violations: string[];
  recommendations: string[];
}

class ReactPerformanceProfiler {
  private componentMetrics: Map<string, ComponentMetrics> = new Map();
  private memoryLeakDetectors: Map<string, MemoryLeakDetector> = new Map();
  private performanceBudgets: Map<string, PerformanceBudget> = new Map();
  private renderTimeline: Array<{
    componentId: string;
    timestamp: number;
    renderTime: number;
    phase: 'mount' | 'update';
  }> = [];
  private memorySnapshots: number[] = [];
  private _isProfilingEnabled: boolean = false;

  public get isProfilingEnabled(): boolean {
    return this._isProfilingEnabled;
  }

  constructor() {
    this.initializeDefaultBudgets();
    this.startMemoryMonitoring();
  }

  private initializeDefaultBudgets(): void {
    const defaultBudget: PerformanceBudget = {
      maxRenderTime: 16, // 60fps target
      maxMemoryUsage: 50 * 1024 * 1024, // 50MB
      maxReRenders: 5,
      maxChildren: 100,
      budgetPeriod: 1000, // 1 second
    };

    // Set budgets for different component types
    this.performanceBudgets.set('calculator', {
      ...defaultBudget,
      maxRenderTime: 50, // Complex calculations allowed more time
      maxMemoryUsage: 100 * 1024 * 1024, // 100MB
    });

    this.performanceBudgets.set('form', {
      ...defaultBudget,
      maxRenderTime: 8, // Forms should be very responsive
      maxReRenders: 3,
    });

    this.performanceBudgets.set('list', {
      ...defaultBudget,
      maxRenderTime: 32, // Lists can be more expensive
      maxChildren: 1000,
    });

    this.performanceBudgets.set('default', defaultBudget);
  }

  private startMemoryMonitoring(): void {
    if (typeof window !== 'undefined' && (window as any).performance?.memory) {
      setInterval(() => {
        const memoryInfo = (window as any).performance.memory;
        this.memorySnapshots.push(memoryInfo.usedJSHeapSize);
        
        // Keep only last 100 snapshots
        if (this.memorySnapshots.length > 100) {
          this.memorySnapshots.shift();
        }
        
        this.detectMemoryLeaks();
      }, 1000);
    }
  }

  private detectMemoryLeaks(): void {
    this.memoryLeakDetectors.forEach((detector, componentId) => {
      if (detector.unmountTime) return; // Component is unmounted
      
      const currentMemory = this.getCurrentMemoryUsage();
      detector.memoryGrowth.push(currentMemory);
      
      // Keep only last 10 samples
      if (detector.memoryGrowth.length > 10) {
        detector.memoryGrowth.shift();
      }
      
      // Detect memory growth pattern
      if (detector.memoryGrowth.length >= 5) {
        const trend = this.calculateMemoryTrend(detector.memoryGrowth);
        const growthRate = trend.slope;
        
        if (growthRate > 1024 * 1024) { // 1MB/second growth
          detector.isLeaking = true;
          detector.leakSeverity = growthRate > 10 * 1024 * 1024 ? 'critical' : 
                                 growthRate > 5 * 1024 * 1024 ? 'high' : 
                                 growthRate > 2 * 1024 * 1024 ? 'medium' : 'low';
        }
      }
    });
  }

  private calculateMemoryTrend(samples: number[]): { slope: number; correlation: number } {
    const n = samples.length;
    if (n < 2) return { slope: 0, correlation: 0 };
    
    const sumX = samples.reduce((sum, _, i) => sum + i, 0);
    const sumY = samples.reduce((sum, val) => sum + val, 0);
    const sumXY = samples.reduce((sum, val, i) => sum + (i * val), 0);
    const sumX2 = samples.reduce((sum, _, i) => sum + (i * i), 0);
    const sumY2 = samples.reduce((sum, val) => sum + (val * val), 0);
    
    const slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
    const correlation = (n * sumXY - sumX * sumY) / 
                       Math.sqrt((n * sumX2 - sumX * sumX) * (n * sumY2 - sumY * sumY));
    
    return { slope, correlation };
  }

  private getCurrentMemoryUsage(): number {
    if (typeof window !== 'undefined' && (window as any).performance?.memory) {
      return (window as any).performance.memory.usedJSHeapSize;
    }
    return 0;
  }

  public enableProfiling(): void {
    this._isProfilingEnabled = true;
  }

  public disableProfiling(): void {
    this._isProfilingEnabled = false;
  }

  public onRenderCallback = (
    id: string,
    phase: "mount" | "update" | "nested-update",
    actualDuration: number,
    baseDuration: number,
    startTime: number,
    commitTime: number,
    interactions: Set<any>
  ) => {
    if (!this._isProfilingEnabled) return;

    const currentTime = typeof performance !== 'undefined' ? performance.now() : Date.now();
    
    // Update component metrics
    const existingMetrics = this.componentMetrics.get(id);
    if (existingMetrics) {
      existingMetrics.renderCount++;
      existingMetrics.totalRenderTime += actualDuration;
      existingMetrics.averageRenderTime = existingMetrics.totalRenderTime / existingMetrics.renderCount;
      existingMetrics.maxRenderTime = Math.max(existingMetrics.maxRenderTime, actualDuration);
      existingMetrics.minRenderTime = Math.min(existingMetrics.minRenderTime, actualDuration);
      existingMetrics.lastRenderTime = actualDuration;
    } else {
      this.componentMetrics.set(id, {
        id,
        displayName: id,
        renderCount: 1,
        totalRenderTime: actualDuration,
        averageRenderTime: actualDuration,
        maxRenderTime: actualDuration,
        minRenderTime: actualDuration,
        lastRenderTime: actualDuration,
        memorySizeEstimate: this.getCurrentMemoryUsage(),
        childrenCount: 0,
        propsCount: 0,
        stateCount: 0,
        hooksCount: 0,
        reRenderReasons: [],
        performance: {
          isOptimized: false,
          optimizationScore: 0,
          warnings: [],
          suggestions: [],
        },
      });
    }

    // Update memory leak detector
    if (!this.memoryLeakDetectors.has(id)) {
      this.memoryLeakDetectors.set(id, {
        componentId: id,
        mountTime: currentTime,
        renderCount: 1,
        memoryGrowth: [this.getCurrentMemoryUsage()],
        isLeaking: false,
        leakSeverity: 'low',
      });
    } else {
      const detector = this.memoryLeakDetectors.get(id)!;
      detector.renderCount++;
    }

    // Add to render timeline
    this.renderTimeline.push({
      componentId: id,
      timestamp: currentTime,
      renderTime: actualDuration,
      phase: phase as 'mount' | 'update',
    });

    // Keep timeline manageable
    if (this.renderTimeline.length > 1000) {
      this.renderTimeline.shift();
    }

    // Check performance budget violations
    this.checkBudgetViolations(id, actualDuration);
  };

  private checkBudgetViolations(componentId: string, renderTime: number): void {
    const componentType = this.getComponentType(componentId);
    const budget = this.performanceBudgets.get(componentType) || 
                  this.performanceBudgets.get('default')!;
    
    const metrics = this.componentMetrics.get(componentId);
    if (!metrics) return;

    const violations: string[] = [];
    
    if (renderTime > budget.maxRenderTime) {
      violations.push(`Render time ${renderTime.toFixed(2)}ms exceeds budget ${budget.maxRenderTime}ms`);
    }
    
    if (metrics.renderCount > budget.maxReRenders) {
      violations.push(`Render count ${metrics.renderCount} exceeds budget ${budget.maxReRenders}`);
    }
    
    if (metrics.memorySizeEstimate > budget.maxMemoryUsage) {
      violations.push(`Memory usage ${this.formatBytes(metrics.memorySizeEstimate)} exceeds budget ${this.formatBytes(budget.maxMemoryUsage)}`);
    }
    
    if (violations.length > 0) {
      console.warn(`Performance budget violations for ${componentId}:`, violations);
    }
  }

  private getComponentType(componentId: string): string {
    if (componentId.toLowerCase().includes('calculator')) return 'calculator';
    if (componentId.toLowerCase().includes('form')) return 'form';
    if (componentId.toLowerCase().includes('list')) return 'list';
    return 'default';
  }

  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  public getComponentProfile(componentId: string): ComponentProfile | null {
    const metrics = this.componentMetrics.get(componentId);
    if (!metrics) return null;

    const memoryLeaks = Array.from(this.memoryLeakDetectors.values())
      .filter(detector => detector.componentId === componentId);
    
    const componentType = this.getComponentType(componentId);
    const budget = this.performanceBudgets.get(componentType) || 
                  this.performanceBudgets.get('default')!;

    const violations: string[] = [];
    const recommendations: string[] = [];

    // Generate performance analysis
    if (metrics.averageRenderTime > budget.maxRenderTime) {
      violations.push(`Average render time too high: ${metrics.averageRenderTime.toFixed(2)}ms`);
      recommendations.push('Consider using React.memo() for expensive components');
      recommendations.push('Use useMemo() for expensive calculations');
      recommendations.push('Use useCallback() for event handlers');
    }

    if (metrics.renderCount > budget.maxReRenders) {
      violations.push(`Too many re-renders: ${metrics.renderCount}`);
      recommendations.push('Check for unnecessary state updates');
      recommendations.push('Use React.memo() with proper comparison function');
      recommendations.push('Optimize parent component re-renders');
    }

    // Memory leak warnings
    memoryLeaks.forEach(leak => {
      if (leak.isLeaking) {
        violations.push(`Memory leak detected: ${leak.leakSeverity} severity`);
        recommendations.push('Check for unremoved event listeners');
        recommendations.push('Clear intervals and timeouts in useEffect cleanup');
        recommendations.push('Remove global references in component cleanup');
      }
    });

    return {
      id: componentId,
      metrics,
      memoryLeaks,
      budget,
      violations,
      recommendations,
    };
  }

  public getAllProfiles(): ComponentProfile[] {
    return Array.from(this.componentMetrics.keys())
      .map(id => this.getComponentProfile(id))
      .filter(profile => profile !== null) as ComponentProfile[];
  }

  public getMemoryLeakReport(): {
    summary: {
      totalComponents: number;
      leakingComponents: number;
      criticalLeaks: number;
      totalMemoryGrowth: number;
    };
    details: MemoryLeakDetector[];
  } {
    const detectors = Array.from(this.memoryLeakDetectors.values());
    const leakingComponents = detectors.filter(d => d.isLeaking);
    const criticalLeaks = leakingComponents.filter(d => d.leakSeverity === 'critical');
    
    const totalMemoryGrowth = detectors.reduce((sum, detector) => {
      if (detector.memoryGrowth.length >= 2) {
        return sum + (detector.memoryGrowth[detector.memoryGrowth.length - 1] - detector.memoryGrowth[0]);
      }
      return sum;
    }, 0);

    return {
      summary: {
        totalComponents: detectors.length,
        leakingComponents: leakingComponents.length,
        criticalLeaks: criticalLeaks.length,
        totalMemoryGrowth,
      },
      details: detectors,
    };
  }

  public getPerformanceSummary(): {
    totalComponents: number;
    averageRenderTime: number;
    slowestComponent: string;
    fastestComponent: string;
    budgetViolations: number;
    optimizationScore: number;
  } {
    const metrics = Array.from(this.componentMetrics.values());
    
    if (metrics.length === 0) {
      return {
        totalComponents: 0,
        averageRenderTime: 0,
        slowestComponent: '',
        fastestComponent: '',
        budgetViolations: 0,
        optimizationScore: 100,
      };
    }

    const totalRenderTime = metrics.reduce((sum, m) => sum + m.averageRenderTime, 0);
    const averageRenderTime = totalRenderTime / metrics.length;
    
    const sortedByRenderTime = metrics.sort((a, b) => b.averageRenderTime - a.averageRenderTime);
    const slowestComponent = sortedByRenderTime[0]?.id || '';
    const fastestComponent = sortedByRenderTime[sortedByRenderTime.length - 1]?.id || '';
    
    const budgetViolations = metrics.filter(m => {
      const componentType = this.getComponentType(m.id);
      const budget = this.performanceBudgets.get(componentType) || 
                    this.performanceBudgets.get('default')!;
      return m.averageRenderTime > budget.maxRenderTime;
    }).length;

    const optimizationScore = Math.max(0, 100 - (budgetViolations * 20) - 
                                     (averageRenderTime > 16 ? 30 : 0));

    return {
      totalComponents: metrics.length,
      averageRenderTime,
      slowestComponent,
      fastestComponent,
      budgetViolations,
      optimizationScore,
    };
  }

  public clearMetrics(): void {
    this.componentMetrics.clear();
    this.memoryLeakDetectors.clear();
    this.renderTimeline.length = 0;
    this.memorySnapshots.length = 0;
  }

  public exportReport(): string {
    const summary = this.getPerformanceSummary();
    const memoryReport = this.getMemoryLeakReport();
    const profiles = this.getAllProfiles();

    return JSON.stringify({
      timestamp: new Date().toISOString(),
      summary,
      memoryReport,
      profiles,
      renderTimeline: this.renderTimeline.slice(-100), // Last 100 renders
    }, null, 2);
  }
}

// Create singleton instance
export const reactProfiler = new ReactPerformanceProfiler();

// React Profiler wrapper component
export interface ProfilerWrapperProps {
  id: string;
  children: React.ReactNode;
}

export const ProfilerWrapper: React.FC<ProfilerWrapperProps> = ({ id, children }) => {
  return React.createElement(
    Profiler,
    { id, onRender: reactProfiler.onRenderCallback as ProfilerOnRenderCallback },
    children
  );
};

// Performance hooks
export const usePerformanceMonitoring = (componentId: string) => {
  const [metrics, setMetrics] = React.useState<ComponentMetrics | null>(null);
  const [profile, setProfile] = React.useState<ComponentProfile | null>(null);

  React.useEffect(() => {
    const interval = setInterval(() => {
      const componentProfile = reactProfiler.getComponentProfile(componentId);
      if (componentProfile) {
        setMetrics(componentProfile.metrics);
        setProfile(componentProfile);
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [componentId]);

  return { metrics, profile };
};

// Performance budget hook
export const usePerformanceBudget = (componentId: string, budget: Partial<PerformanceBudget>) => {
  React.useEffect(() => {
    const componentType = componentId.toLowerCase().includes('calculator') ? 'calculator' :
                         componentId.toLowerCase().includes('form') ? 'form' :
                         componentId.toLowerCase().includes('list') ? 'list' : 'default';
    
    const currentBudget = reactProfiler['performanceBudgets'].get(componentType) || 
                         reactProfiler['performanceBudgets'].get('default')!;
    
    reactProfiler['performanceBudgets'].set(componentType, {
      ...currentBudget,
      ...budget,
    });
  }, [componentId, budget]);
};

export default reactProfiler;