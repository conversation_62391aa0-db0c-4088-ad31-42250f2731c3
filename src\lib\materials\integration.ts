/**
 * Materials Database Integration with Calculation Engine
 * Bridges the gap between the new materials database and existing calculator
 */

import { materialManager } from './loader';
import {
  calculateMaterialRequirements,
  calculateConstructionAreas,
  QUALITY_TIER_MULTIPLIERS
} from './consumption';
import {
  Material,
  MaterialConsumption,
  MaterialCostBreakdown,
  PurchaseType
} from '../../types/materials';

// Import calculation engine types
import type {
  CalculationInput,
  CostBreakdown,
  CategoryCost,
  MaterialQuantity
} from '../../core/calculator/types';

/**
 * Enhanced Material Cost Calculator
 * Integrates real material pricing with calculation engine
 */
export class MaterialCostCalculator {

  /**
   * Calculate detailed material costs for a construction project
   */
  static calculateProjectMaterialCosts(
    input: CalculationInput,
    purchaseType: PurchaseType = PurchaseType.BULK
  ): {
    materialBreakdown: MaterialCostBreakdown[];
    categoryTotals: Record<string, number>;
    totalMaterialCost: number;
    totalWastage: number;
    costPerSqft: number;
    qualityAnalysis: {
      averageQualityScore: number;
      premiumMaterialsCount: number;
      totalMaterialsCount: number;
    };
  } {
    // Map quality tier names
    const qualityTierMap = {
      'smart': 'Smart Choice' as const,
      'premium': 'Premium Selection' as const,
      'luxury': 'Luxury Collection' as const
    };

    const qualityTier = qualityTierMap[input.qualityTier] || 'Smart Choice';

    // Calculate construction areas
    const areas = calculateConstructionAreas(
      input.builtUpArea,
      input.floors || 1,
      'residential'
    );

    // Get material requirements
    const materialRequirements = calculateMaterialRequirements({
      builtUpArea: areas.builtUpArea,
      wallArea: areas.wallArea,
      floorArea: areas.floorArea,
      roofArea: areas.roofArea,
      qualityTier,
      constructionType: 'residential'
    });

    // Calculate costs for each material
    const materialBreakdown = materialRequirements.map(requirement =>
      materialManager.calculateMaterialCost(
        requirement,
        input.location.toLowerCase(),
        purchaseType
      )
    );

    // Calculate totals and statistics
    const totalMaterialCost = materialBreakdown.reduce((sum, item) => sum + item.finalCost, 0);
    const totalWastage = materialBreakdown.reduce((sum, item) => sum + item.wastageAmount, 0);
    const costPerSqft = totalMaterialCost / input.builtUpArea;

    // Category-wise breakdown
    const categoryTotals: Record<string, number> = {};
    materialBreakdown.forEach(item => {
      categoryTotals[item.category] = (categoryTotals[item.category] || 0) + item.finalCost;
    });

    // Quality analysis
    const materials = materialBreakdown.map(item =>
      materialManager.getMaterialById(item.materialId)
    ).filter(Boolean) as Material[];

    const averageQualityScore = materials.length > 0
      ? materials.reduce((sum, m) => sum + m.qualityScore, 0) / materials.length
      : 0;

    const premiumMaterialsCount = materials.filter(m => m.qualityScore >= 8.5).length;

    return {
      materialBreakdown,
      categoryTotals,
      totalMaterialCost,
      totalWastage,
      costPerSqft,
      qualityAnalysis: {
        averageQualityScore,
        premiumMaterialsCount,
        totalMaterialsCount: materials.length
      }
    };
  }

  /**
   * Generate material-based cost breakdown compatible with existing calculator
   */
  static generateMaterialCostBreakdown(
    input: CalculationInput
  ): CostBreakdown {
    const materialCosts = this.calculateProjectMaterialCosts(input);

    // Map material categories to calculator categories
    const categoryMapping: Record<string, keyof CostBreakdown> = {
      'Cement': 'structure',
      'Steel': 'structure',
      'Bricks': 'structure',
      'Sand': 'structure',
      'Aggregate': 'structure',
      'Electrical': 'mep',
      'Plumbing': 'mep',
      'Tiles': 'finishing',
      'Paint': 'finishing',
      'Waterproofing': 'external'
    };

    // Initialize cost breakdown
    const costBreakdown: CostBreakdown = {
      structure: { amount: 0, percentage: 0, subCategories: [] },
      finishing: { amount: 0, percentage: 0, subCategories: [] },
      mep: { amount: 0, percentage: 0, subCategories: [] },
      external: { amount: 0, percentage: 0, subCategories: [] },
      other: { amount: 0, percentage: 0, subCategories: [] },
      total: 0
    };

    // Allocate material costs to categories
    Object.entries(materialCosts.categoryTotals).forEach(([materialCategory, cost]) => {
      const calculatorCategory = categoryMapping[materialCategory] || 'external';
      const categoryData = costBreakdown[calculatorCategory];
      if (categoryData && typeof categoryData === 'object' && 'amount' in categoryData) {
        categoryData.amount += cost;

        // Add as subcategory
        categoryData.subCategories.push({
          name: materialCategory,
          amount: cost,
          percentage: 0, // Will be calculated below
          description: `${materialCategory} materials and components`
        });
      }
    });

    // Calculate total and percentages
    const categoryCosts = [
      costBreakdown.structure,
      costBreakdown.finishing,
      costBreakdown.mep,
      costBreakdown.external,
      costBreakdown.other
    ];

    const totalCost = categoryCosts.reduce((sum, cat) => sum + cat.amount, 0);
    costBreakdown.total = totalCost;

    // Calculate percentages for main categories and subcategories
    categoryCosts.forEach(category => {
      category.percentage = totalCost > 0 ? (category.amount / totalCost) * 100 : 0;

      // Calculate subcategory percentages
      category.subCategories.forEach(subCat => {
        subCat.percentage = category.amount > 0 ? (subCat.amount / category.amount) * 100 : 0;
      });
    });

    return costBreakdown;
  }

  /**
   * Generate material quantities list compatible with existing calculator
   */
  static generateMaterialQuantities(
    input: CalculationInput
  ): MaterialQuantity[] {
    const qualityTierMap = {
      'smart': 'Smart Choice' as const,
      'premium': 'Premium Selection' as const,
      'luxury': 'Luxury Collection' as const
    };

    const qualityTier = qualityTierMap[input.qualityTier] || 'Smart Choice';

    const areas = calculateConstructionAreas(
      input.builtUpArea,
      input.floors || 1,
      'residential'
    );

    const materialRequirements = calculateMaterialRequirements({
      builtUpArea: areas.builtUpArea,
      wallArea: areas.wallArea,
      floorArea: areas.floorArea,
      roofArea: areas.roofArea,
      qualityTier,
      constructionType: 'residential'
    });

    return materialRequirements.map(requirement => {
      const material = materialManager.getMaterialById(requirement.materialId);
      const materialCost = materialManager.calculateMaterialCost(
        requirement,
        input.location.toLowerCase(),
        PurchaseType.BULK
      );

      return {
        name: material?.name || 'Unknown Material',
        category: requirement.category,
        quantity: requirement.quantityRequired,
        unit: requirement.unit,
        rate: materialCost.unitPrice,
        totalCost: materialCost.finalCost,
        purpose: `${requirement.category} - ${material?.subcategory || 'Construction'}`,
        specifications: material?.specifications?.standardCompliance || ''
      };
    });
  }

  /**
   * Get material alternatives for cost optimization
   */
  static getMaterialAlternatives(
    materialId: string,
    region: string = 'bangalore',
    maxPriceIncrease: number = 20 // percentage
  ): Array<{
    material: Material;
    priceDifference: number;
    qualityDifference: number;
    recommendation: 'upgrade' | 'downgrade' | 'alternative';
  }> {
    const originalMaterial = materialManager.getMaterialById(materialId);
    if (!originalMaterial) return [];

    const alternatives = materialManager.getMaterialAlternatives(materialId);
    const originalPrice = materialManager.getMaterialPrice(materialId, region, PurchaseType.BULK);

    return alternatives.map(alternative => {
      const altPrice = materialManager.getMaterialPrice(alternative.id, region, PurchaseType.BULK);
      const priceDifference = ((altPrice - originalPrice) / originalPrice) * 100;
      const qualityDifference = alternative.qualityScore - originalMaterial.qualityScore;

      let recommendation: 'upgrade' | 'downgrade' | 'alternative';
      if (qualityDifference > 0.5) {
        recommendation = 'upgrade';
      } else if (qualityDifference < -0.5) {
        recommendation = 'downgrade';
      } else {
        recommendation = 'alternative';
      }

      return {
        material: alternative,
        priceDifference,
        qualityDifference,
        recommendation
      };
    }).filter(alt => alt.priceDifference <= maxPriceIncrease);
  }

  /**
   * Generate supplier recommendations
   */
  static getSupplierRecommendations(
    materialIds: string[],
    region: string = 'bangalore'
  ): Array<{
    materialId: string;
    materialName: string;
    recommendedSupplier: string;
    price: number;
    qualityScore: number;
    availability: string;
    leadTime: number;
  }> {
    return materialIds.map(materialId => {
      const material = materialManager.getMaterialById(materialId);
      if (!material) {
        return null;
      }

      const price = materialManager.getMaterialPrice(materialId, region, PurchaseType.BULK);

      return {
        materialId: material.id,
        materialName: material.name,
        recommendedSupplier: material.brand,
        price,
        qualityScore: material.qualityScore,
        availability: material.availability,
        leadTime: material.leadTimeDays
      };
    }).filter(Boolean) as Array<{
      materialId: string;
      materialName: string;
      recommendedSupplier: string;
      price: number;
      qualityScore: number;
      availability: string;
      leadTime: number;
    }>;
  }

  /**
   * Calculate cost optimization opportunities
   */
  static calculateCostOptimization(
    input: CalculationInput
  ): {
    currentCost: number;
    optimizedCost: number;
    savings: number;
    savingsPercentage: number;
    optimizations: Array<{
      category: string;
      originalMaterial: string;
      suggestedMaterial: string;
      savings: number;
      qualityImpact: number;
    }>;
  } {
    const currentCosts = this.calculateProjectMaterialCosts(input);
    const optimizations: Array<{
      category: string;
      originalMaterial: string;
      suggestedMaterial: string;
      savings: number;
      qualityImpact: number;
    }> = [];

    let totalSavings = 0;

    // Look for cost optimization opportunities
    currentCosts.materialBreakdown.forEach(materialCost => {
      const alternatives = this.getMaterialAlternatives(
        materialCost.materialId,
        input.location.toLowerCase(),
        0 // Only consider cheaper alternatives
      );

      const cheaperAlternatives = alternatives.filter(alt => alt.priceDifference < -5); // At least 5% cheaper

      if (cheaperAlternatives.length > 0) {
        const bestAlternative = cheaperAlternatives.sort((a, b) => a.priceDifference - b.priceDifference)[0];
        const savings = (Math.abs(bestAlternative.priceDifference) / 100) * materialCost.finalCost;

        optimizations.push({
          category: materialCost.category,
          originalMaterial: materialCost.materialName,
          suggestedMaterial: bestAlternative.material.name,
          savings,
          qualityImpact: bestAlternative.qualityDifference
        });

        totalSavings += savings;
      }
    });

    return {
      currentCost: currentCosts.totalMaterialCost,
      optimizedCost: currentCosts.totalMaterialCost - totalSavings,
      savings: totalSavings,
      savingsPercentage: (totalSavings / currentCosts.totalMaterialCost) * 100,
      optimizations
    };
  }
}

// Export convenience functions
export const calculateProjectMaterialCosts = MaterialCostCalculator.calculateProjectMaterialCosts;
export const generateMaterialCostBreakdown = MaterialCostCalculator.generateMaterialCostBreakdown;
export const generateMaterialQuantities = MaterialCostCalculator.generateMaterialQuantities;
export const getMaterialAlternatives = MaterialCostCalculator.getMaterialAlternatives;
export const getSupplierRecommendations = MaterialCostCalculator.getSupplierRecommendations;
export const calculateCostOptimization = MaterialCostCalculator.calculateCostOptimization;

export default MaterialCostCalculator;