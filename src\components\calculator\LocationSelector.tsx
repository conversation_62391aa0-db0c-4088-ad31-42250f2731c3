'use client';

import { MapPin } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { REGIONAL_MULTIPLIERS } from '@/core/calculator/constants';

interface LocationSelectorProps {
  value: string;
  onChange: (value: string) => void;
  error?: string;
}

// Group cities by region
const locationGroups = {
  'Metro Cities': {
    mumbai: 'Mumbai',
    delhi: 'Delhi NCR',
    bangalore: 'Bangalore',
    chennai: 'Chennai',
    kolkata: 'Kolkata',
  },
  'Major Cities': {
    pune: 'Pune',
    hyderabad: 'Hyderabad',
    ahmedabad: 'Ahmedabad',
    jaipur: 'Jaipur',
    lucknow: 'Lucknow',
    bhubaneswar: 'Bhubaneswar',
  },
  'Other': {
    tier2: 'Tier 2 Cities',
    tier3: 'Tier 3 Cities',
  },
};

export function LocationSelector({ value, onChange, error }: LocationSelectorProps) {
  const getMultiplierText = (location: string) => {
    const multiplier = REGIONAL_MULTIPLIERS[location];
    if (multiplier === 1) return '';
    if (multiplier > 1) return `+${Math.round((multiplier - 1) * 100)}%`;
    return `-${Math.round((1 - multiplier) * 100)}%`;
  };

  return (
    <div className="space-y-2">
      <label className="text-sm font-medium text-gray-700 flex items-center gap-2">
        <MapPin className="h-4 w-4" />
        Project Location
      </label>

      <Select value={value} onValueChange={onChange}>
        <SelectTrigger className={error ? 'border-red-500' : ''}>
          <SelectValue placeholder="Select city or region" />
        </SelectTrigger>
        <SelectContent className="max-h-[300px]">
          {Object.entries(locationGroups).map(([groupName, cities]) => (
            <SelectGroup key={groupName}>
              <SelectLabel className="font-semibold text-gray-900">
                {groupName}
              </SelectLabel>
              {Object.entries(cities).map(([key, cityName]) => {
                const multiplierText = getMultiplierText(key);
                return (
                  <SelectItem key={key} value={key}>
                    <div className="flex items-center justify-between w-full">
                      <span>{cityName}</span>
                      {multiplierText && (
                        <span className={`text-xs ml-2 ${
                          REGIONAL_MULTIPLIERS[key] > 1
                            ? 'text-red-600'
                            : 'text-green-600'
                        }`}>
                          {multiplierText}
                        </span>
                      )}
                    </div>
                  </SelectItem>
                );
              })}
            </SelectGroup>
          ))}
        </SelectContent>
      </Select>

      {error && (
        <p className="text-sm text-red-600 mt-1">{error}</p>
      )}

      {value && (
        <p className="text-xs text-gray-500">
          Regional cost adjustment: {getMultiplierText(value) || 'Base rates apply'}
        </p>
      )}
    </div>
  );
}