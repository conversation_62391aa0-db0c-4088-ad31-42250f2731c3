'use client';

import { motion } from 'framer-motion';
import { 
  FolderIcon, 
  PlusIcon, 
  TrashIcon, 
  LoaderIcon, 
  MapPinIcon,
  CalendarIcon,
  BuildingIcon,
  TrendingUpIcon,
  ClockIcon,
  SearchIcon,
  FilterIcon
} from 'lucide-react';
import Link from 'next/link';
import { useState } from 'react';

import { Layout } from '@/components/layout/Layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useAuth } from '@/contexts/AuthContext';
import { useProjects, useDeleteProject, Project } from '@/hooks/useProjects';
import { AuthModal } from '@/components/auth/AuthModal';
import { fadeInUp, staggerContainer, staggerItem } from '@/lib/animations';

export default function ProjectsPage() {
  const { user, loading: authLoading } = useAuth();
  const { data, isLoading, error } = useProjects();
  const deleteProject = useDeleteProject();
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [authMode, setAuthMode] = useState<'signin' | 'signup'>('signin');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedProject, setSelectedProject] = useState<string | null>(null);

  // Filter projects based on search query
  const filteredProjects = data?.projects?.filter((project: Project) =>
    project.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    project.location.toLowerCase().includes(searchQuery.toLowerCase())
  ) || [];

  const handleDeleteProject = async (projectId: string, projectName: string) => {
    if (window.confirm(`Are you sure you want to delete "${projectName}"? This action cannot be undone.`)) {
      deleteProject.mutate(projectId);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getQualityTierColor = (tier: string) => {
    switch (tier) {
      case 'smart':
        return 'bg-green-100 text-green-800';
      case 'premium':
        return 'bg-blue-100 text-blue-800';
      case 'luxury':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (authLoading) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-[400px]">
          <LoaderIcon className="h-8 w-8 animate-spin text-blue-600" />
        </div>
      </Layout>
    );
  }

  if (!user) {
    return (
      <Layout>
        <div className="container mx-auto px-4 py-12">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center max-w-md mx-auto"
          >
            <FolderIcon className="h-16 w-16 text-gray-400 mx-auto mb-6" />
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              Sign In to View Projects
            </h1>
            <p className="text-gray-600 mb-8">
              Create an account or sign in to save and manage your construction calculations.
            </p>
            <div className="space-y-3">
              <Button 
                onClick={() => {
                  setAuthMode('signin');
                  setShowAuthModal(true);
                }}
                className="w-full"
              >
                Sign In
              </Button>
              <Button 
                variant="outline"
                onClick={() => {
                  setAuthMode('signup');
                  setShowAuthModal(true);
                }}
                className="w-full"
              >
                Create Account
              </Button>
            </div>
          </motion.div>
        </div>

        <AuthModal
          isOpen={showAuthModal}
          onClose={() => setShowAuthModal(false)}
          mode={authMode}
          onModeChange={setAuthMode}
        />
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <motion.div {...fadeInUp} className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">My Projects</h1>
            <p className="text-gray-600 mt-2">
              Manage your saved construction calculations
            </p>
          </div>
          <Link href="/calculator">
            <Button>
              <PlusIcon className="h-4 w-4 mr-2" />
              New Calculation
            </Button>
          </Link>
        </motion.div>

        {/* Search and Filters */}
        <motion.div {...fadeInUp} className="mb-6">
          <div className="flex gap-4">
            <div className="relative flex-1">
              <SearchIcon className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search projects..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <Button variant="outline" size="sm">
              <FilterIcon className="h-4 w-4 mr-2" />
              Filter
            </Button>
          </div>
        </motion.div>

        {/* Loading State */}
        {isLoading && (
          <div className="text-center py-12">
            <LoaderIcon className="h-8 w-8 animate-spin text-blue-600 mx-auto mb-4" />
            <p className="text-gray-600">Loading your projects...</p>
          </div>
        )}

        {/* Error State */}
        {error && (
          <motion.div {...fadeInUp} className="text-center py-12">
            <div className="bg-red-50 border border-red-200 rounded-lg p-6">
              <p className="text-red-600">Failed to load projects. Please try again.</p>
            </div>
          </motion.div>
        )}

        {/* Empty State */}
        {!isLoading && !error && filteredProjects.length === 0 && !searchQuery && (
          <motion.div {...fadeInUp} className="text-center py-12">
            <FolderIcon className="h-16 w-16 text-gray-400 mx-auto mb-6" />
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              No projects yet
            </h3>
            <p className="text-gray-600 mb-6">
              Start by creating your first construction calculation.
            </p>
            <Link href="/calculator">
              <Button>
                <PlusIcon className="h-4 w-4 mr-2" />
                Create First Project
              </Button>
            </Link>
          </motion.div>
        )}

        {/* No Search Results */}
        {!isLoading && !error && filteredProjects.length === 0 && searchQuery && (
          <motion.div {...fadeInUp} className="text-center py-12">
            <SearchIcon className="h-16 w-16 text-gray-400 mx-auto mb-6" />
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              No projects found
            </h3>
            <p className="text-gray-600">
              No projects match your search for "{searchQuery}"
            </p>
          </motion.div>
        )}

        {/* Projects Grid */}
        {filteredProjects.length > 0 && (
          <motion.div
            variants={staggerContainer}
            initial="initial"
            animate="animate"
            className="grid gap-6 md:grid-cols-2 lg:grid-cols-3"
          >
            {filteredProjects.map((project: Project) => (
              <motion.div key={project.id} variants={staggerItem}>
                <Card className="group hover:shadow-lg transition-shadow duration-200">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <CardTitle className="text-lg mb-2 line-clamp-2">
                          {project.name}
                        </CardTitle>
                        <div className="flex items-center text-sm text-gray-600 mb-1">
                          <MapPinIcon className="h-4 w-4 mr-1" />
                          {project.location}
                        </div>
                        <div className="flex items-center text-sm text-gray-600">
                          <CalendarIcon className="h-4 w-4 mr-1" />
                          {formatDate(project.updated_at)}
                        </div>
                      </div>
                      <span
                        className={`px-2 py-1 rounded-full text-xs font-medium capitalize ${getQualityTierColor(
                          project.quality_tier
                        )}`}
                      >
                        {project.quality_tier}
                      </span>
                    </div>
                  </CardHeader>

                  <CardContent className="pt-0">
                    {/* Project Stats */}
                    <div className="grid grid-cols-2 gap-4 mb-4">
                      <div className="text-center">
                        <BuildingIcon className="h-5 w-5 text-gray-400 mx-auto mb-1" />
                        <p className="text-xs text-gray-600">Area</p>
                        <p className="font-semibold">{project.area_sqft.toLocaleString()} sq ft</p>
                      </div>
                      <div className="text-center">
                        <TrendingUpIcon className="h-5 w-5 text-gray-400 mx-auto mb-1" />
                        <p className="text-xs text-gray-600">Cost/sq ft</p>
                        <p className="font-semibold">₹{project.costPerSqft.toLocaleString()}</p>
                      </div>
                    </div>

                    {/* Total Cost */}
                    <div className="bg-blue-50 rounded-lg p-3 mb-4">
                      <p className="text-sm text-blue-600 font-medium">Total Cost</p>
                      <p className="text-xl font-bold text-blue-900">
                        ₹{project.totalCost.toLocaleString()}
                      </p>
                    </div>

                    {/* Actions */}
                    <div className="flex gap-2">
                      <Link href={`/projects/${project.id}`} className="flex-1">
                        <Button variant="outline" size="sm" className="w-full">
                          View Details
                        </Button>
                      </Link>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeleteProject(project.id, project.name)}
                        disabled={deleteProject.isPending}
                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                      >
                        {deleteProject.isPending && selectedProject === project.id ? (
                          <LoaderIcon className="h-4 w-4 animate-spin" />
                        ) : (
                          <TrashIcon className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        )}
      </div>
    </Layout>
  );
}