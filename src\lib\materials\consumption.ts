/**
 * Material Consumption Rates and Calculations
 * Standard consumption rates for different construction activities and quality tiers
 */

import { MaterialConsumption } from '../../types/materials';

/**
 * Standard material consumption rates per square foot of construction
 * Based on Indian construction practices and IS codes
 */

// Quality tier multipliers for material selection
export const QUALITY_TIER_MULTIPLIERS = {
  'Smart Choice': {
    materialQuality: 1.0,
    brandPreference: 'standard',
    specifications: 'basic'
  },
  'Premium Selection': {
    materialQuality: 1.2,
    brandPreference: 'premium',
    specifications: 'enhanced'
  },
  'Luxury Collection': {
    materialQuality: 1.5,
    brandPreference: 'luxury',
    specifications: 'premium'
  }
} as const;

// Material consumption rates per sqft for different construction phases
export const MATERIAL_CONSUMPTION_RATES = {
  // Foundation and Structure (per sqft of built-up area)
  foundation: {
    cement_opc53_ultratech: 0.35, // bags per sqft
    steel_tmt_fe500d_tata: 4.2,   // kg per sqft
    sand_river_natural: 0.012,     // tons per sqft
    aggregate_20mm_crushed: 0.024,  // tons per sqft
    aggregate_10mm_crushed: 0.016   // tons per sqft
  },

  // Masonry work (per sqft of wall area)
  masonry: {
    brick_red_clay_standard: 12.5,     // pieces per sqft
    brick_flyash_billtech: 12.0,       // pieces per sqft (slightly larger)
    block_aac_siporex: 1.8,            // pieces per sqft (larger blocks)
    cement_opc43_acc: 0.08,            // bags per sqft
    sand_manufactured_msand: 0.003      // tons per sqft
  },

  // Flooring (per sqft of floor area)
  flooring: {
    tile_vitrified_kajaria: 1.1,       // sqm per sqft (including wastage)
    tile_ceramic_somany: 1.1,          // sqm per sqft (including wastage)
    cement_opc53_ultratech: 0.02,      // bags per sqft for bedding
    sand_river_natural: 0.001           // tons per sqft for bedding
  },

  // Electrical work (per sqft of built-up area)
  electrical: {
    wire_copper_havells: 8.5,          // meters per sqft (all circuits)
    switch_legrand_mylinc: 0.08        // pieces per sqft
  },

  // Plumbing work (per sqft of built-up area)
  plumbing: {
    pipe_pvc_supreme: 2.2,             // meters per sqft
    fitting_cpvc_astral: 0.15          // pieces per sqft
  },

  // Painting (per sqft of wall area)
  painting: {
    paint_interior_asian: 0.007,       // liters per sqft
    paint_exterior_berger: 0.008       // liters per sqft
  },

  // Waterproofing (per sqft of roof/terrace area)
  waterproofing: {
    waterproofing_fosroc: 0.012        // liters per sqft
  }
} as const;

/**
 * Calculate material requirements for a construction project
 */
export function calculateMaterialRequirements(
  projectSpecs: {
    builtUpArea: number;          // sqft
    wallArea: number;             // sqft
    floorArea: number;            // sqft
    roofArea: number;             // sqft
    qualityTier: keyof typeof QUALITY_TIER_MULTIPLIERS;
    constructionType: 'residential' | 'commercial' | 'industrial';
  }
): MaterialConsumption[] {
  const requirements: MaterialConsumption[] = [];
  const { builtUpArea, wallArea, floorArea, roofArea, qualityTier, constructionType } = projectSpecs;

  const tierMultiplier = QUALITY_TIER_MULTIPLIERS[qualityTier].materialQuality;

  // Foundation and Structure materials
  requirements.push(
    {
      materialId: 'cement_opc53_ultratech',
      quantityRequired: Math.ceil(builtUpArea * MATERIAL_CONSUMPTION_RATES.foundation.cement_opc53_ultratech * tierMultiplier),
      unit: 'bag',
      consumptionRate: MATERIAL_CONSUMPTION_RATES.foundation.cement_opc53_ultratech,
      category: 'Cement'
    },
    {
      materialId: 'steel_tmt_fe500d_tata',
      quantityRequired: Math.ceil(builtUpArea * MATERIAL_CONSUMPTION_RATES.foundation.steel_tmt_fe500d_tata * tierMultiplier),
      unit: 'kg',
      consumptionRate: MATERIAL_CONSUMPTION_RATES.foundation.steel_tmt_fe500d_tata,
      category: 'Steel'
    },
    {
      materialId: 'sand_river_natural',
      quantityRequired: Math.ceil(builtUpArea * MATERIAL_CONSUMPTION_RATES.foundation.sand_river_natural * tierMultiplier * 100) / 100,
      unit: 'ton',
      consumptionRate: MATERIAL_CONSUMPTION_RATES.foundation.sand_river_natural,
      category: 'Sand'
    },
    {
      materialId: 'aggregate_20mm_crushed',
      quantityRequired: Math.ceil(builtUpArea * MATERIAL_CONSUMPTION_RATES.foundation.aggregate_20mm_crushed * tierMultiplier * 100) / 100,
      unit: 'ton',
      consumptionRate: MATERIAL_CONSUMPTION_RATES.foundation.aggregate_20mm_crushed,
      category: 'Aggregate'
    },
    {
      materialId: 'aggregate_10mm_crushed',
      quantityRequired: Math.ceil(builtUpArea * MATERIAL_CONSUMPTION_RATES.foundation.aggregate_10mm_crushed * tierMultiplier * 100) / 100,
      unit: 'ton',
      consumptionRate: MATERIAL_CONSUMPTION_RATES.foundation.aggregate_10mm_crushed,
      category: 'Aggregate'
    }
  );

  // Masonry materials - select based on quality tier
  const brickMaterialId = qualityTier === 'Luxury Collection'
    ? 'block_aac_siporex'
    : qualityTier === 'Premium Selection'
      ? 'brick_flyash_billtech'
      : 'brick_red_clay_standard';

  const brickConsumptionRate = qualityTier === 'Luxury Collection'
    ? MATERIAL_CONSUMPTION_RATES.masonry.block_aac_siporex
    : qualityTier === 'Premium Selection'
      ? MATERIAL_CONSUMPTION_RATES.masonry.brick_flyash_billtech
      : MATERIAL_CONSUMPTION_RATES.masonry.brick_red_clay_standard;

  requirements.push(
    {
      materialId: brickMaterialId,
      quantityRequired: Math.ceil(wallArea * brickConsumptionRate),
      unit: 'piece',
      consumptionRate: brickConsumptionRate,
      category: 'Bricks'
    },
    {
      materialId: 'cement_opc43_acc',
      quantityRequired: Math.ceil(wallArea * MATERIAL_CONSUMPTION_RATES.masonry.cement_opc43_acc * tierMultiplier),
      unit: 'bag',
      consumptionRate: MATERIAL_CONSUMPTION_RATES.masonry.cement_opc43_acc,
      category: 'Cement'
    },
    {
      materialId: 'sand_manufactured_msand',
      quantityRequired: Math.ceil(wallArea * MATERIAL_CONSUMPTION_RATES.masonry.sand_manufactured_msand * tierMultiplier * 100) / 100,
      unit: 'ton',
      consumptionRate: MATERIAL_CONSUMPTION_RATES.masonry.sand_manufactured_msand,
      category: 'Sand'
    }
  );

  // Flooring materials - select based on quality tier
  const tileMaterialId = qualityTier === 'Smart Choice'
    ? 'tile_ceramic_somany'
    : 'tile_vitrified_kajaria';

  const tileConsumptionRate = qualityTier === 'Smart Choice'
    ? MATERIAL_CONSUMPTION_RATES.flooring.tile_ceramic_somany
    : MATERIAL_CONSUMPTION_RATES.flooring.tile_vitrified_kajaria;

  requirements.push(
    {
      materialId: tileMaterialId,
      quantityRequired: Math.ceil(floorArea * tileConsumptionRate * 100) / 100,
      unit: 'sqm',
      consumptionRate: tileConsumptionRate,
      category: 'Tiles'
    }
  );

  // Electrical materials
  requirements.push(
    {
      materialId: 'wire_copper_havells',
      quantityRequired: Math.ceil(builtUpArea * MATERIAL_CONSUMPTION_RATES.electrical.wire_copper_havells),
      unit: 'meter',
      consumptionRate: MATERIAL_CONSUMPTION_RATES.electrical.wire_copper_havells,
      category: 'Electrical'
    },
    {
      materialId: 'switch_legrand_mylinc',
      quantityRequired: Math.ceil(builtUpArea * MATERIAL_CONSUMPTION_RATES.electrical.switch_legrand_mylinc),
      unit: 'piece',
      consumptionRate: MATERIAL_CONSUMPTION_RATES.electrical.switch_legrand_mylinc,
      category: 'Electrical'
    }
  );

  // Plumbing materials
  requirements.push(
    {
      materialId: 'pipe_pvc_supreme',
      quantityRequired: Math.ceil(builtUpArea * MATERIAL_CONSUMPTION_RATES.plumbing.pipe_pvc_supreme),
      unit: 'meter',
      consumptionRate: MATERIAL_CONSUMPTION_RATES.plumbing.pipe_pvc_supreme,
      category: 'Plumbing'
    },
    {
      materialId: 'fitting_cpvc_astral',
      quantityRequired: Math.ceil(builtUpArea * MATERIAL_CONSUMPTION_RATES.plumbing.fitting_cpvc_astral),
      unit: 'piece',
      consumptionRate: MATERIAL_CONSUMPTION_RATES.plumbing.fitting_cpvc_astral,
      category: 'Plumbing'
    }
  );

  // Painting materials
  requirements.push(
    {
      materialId: 'paint_interior_asian',
      quantityRequired: Math.ceil(wallArea * MATERIAL_CONSUMPTION_RATES.painting.paint_interior_asian * 100) / 100,
      unit: 'liter',
      consumptionRate: MATERIAL_CONSUMPTION_RATES.painting.paint_interior_asian,
      category: 'Paint'
    },
    {
      materialId: 'paint_exterior_berger',
      quantityRequired: Math.ceil(wallArea * 0.3 * MATERIAL_CONSUMPTION_RATES.painting.paint_exterior_berger * 100) / 100, // 30% of wall area is exterior
      unit: 'liter',
      consumptionRate: MATERIAL_CONSUMPTION_RATES.painting.paint_exterior_berger,
      category: 'Paint'
    }
  );

  // Waterproofing materials (optional for luxury tier)
  if (qualityTier === 'Luxury Collection' || qualityTier === 'Premium Selection') {
    requirements.push({
      materialId: 'waterproofing_fosroc',
      quantityRequired: Math.ceil(roofArea * MATERIAL_CONSUMPTION_RATES.waterproofing.waterproofing_fosroc * 100) / 100,
      unit: 'liter',
      consumptionRate: MATERIAL_CONSUMPTION_RATES.waterproofing.waterproofing_fosroc,
      category: 'Waterproofing',
      isOptional: true
    });
  }

  return requirements;
}

/**
 * Get material alternatives based on quality tier and budget
 */
export function getMaterialAlternatives(
  originalMaterialId: string,
  targetQualityTier: keyof typeof QUALITY_TIER_MULTIPLIERS
): string[] {
  const alternatives: Record<string, Record<string, string[]>> = {
    // Cement alternatives by tier
    cement_opc53_ultratech: {
      'Smart Choice': ['cement_opc43_acc', 'cement_ppc_ambuja'],
      'Premium Selection': ['cement_opc53_ultratech'],
      'Luxury Collection': ['cement_opc53_ultratech']
    },

    // Steel alternatives by tier
    steel_tmt_fe500_jsw: {
      'Smart Choice': ['steel_tmt_fe500_jsw'],
      'Premium Selection': ['steel_tmt_fe500d_tata', 'steel_tmt_fe500_jsw'],
      'Luxury Collection': ['steel_tmt_fe500d_tata']
    },

    // Brick alternatives by tier
    brick_red_clay_standard: {
      'Smart Choice': ['brick_red_clay_standard'],
      'Premium Selection': ['brick_flyash_billtech', 'brick_red_clay_standard'],
      'Luxury Collection': ['block_aac_siporex', 'brick_flyash_billtech']
    },

    // Tile alternatives by tier
    tile_ceramic_somany: {
      'Smart Choice': ['tile_ceramic_somany'],
      'Premium Selection': ['tile_vitrified_kajaria', 'tile_ceramic_somany'],
      'Luxury Collection': ['tile_vitrified_kajaria']
    }
  };

  return alternatives[originalMaterialId]?.[targetQualityTier] || [originalMaterialId];
}

/**
 * Calculate area requirements for different construction elements
 */
export function calculateConstructionAreas(
  builtUpArea: number,
  floors: number = 1,
  constructionType: 'residential' | 'commercial' | 'industrial' = 'residential'
): {
  builtUpArea: number;
  wallArea: number;
  floorArea: number;
  roofArea: number;
  foundationArea: number;
} {
  // Standard ratios based on construction type
  const ratios = {
    residential: {
      wallToFloorRatio: 3.2,    // 3.2 sqft wall per sqft floor
      roofToFloorRatio: 1.0,    // 1:1 ratio
      foundationToFloorRatio: 1.1 // 10% more than floor area
    },
    commercial: {
      wallToFloorRatio: 2.8,
      roofToFloorRatio: 1.0,
      foundationToFloorRatio: 1.15
    },
    industrial: {
      wallToFloorRatio: 2.2,
      roofToFloorRatio: 1.0,
      foundationToFloorRatio: 1.2
    }
  };

  const ratio = ratios[constructionType];

  return {
    builtUpArea,
    wallArea: builtUpArea * ratio.wallToFloorRatio,
    floorArea: builtUpArea,
    roofArea: (builtUpArea / floors) * ratio.roofToFloorRatio,
    foundationArea: (builtUpArea / floors) * ratio.foundationToFloorRatio
  };
}

/**
 * Export consumption calculation utilities
 */
export const MaterialConsumptionUtils = {
  calculateMaterialRequirements,
  getMaterialAlternatives,
  calculateConstructionAreas,
  QUALITY_TIER_MULTIPLIERS,
  MATERIAL_CONSUMPTION_RATES
};