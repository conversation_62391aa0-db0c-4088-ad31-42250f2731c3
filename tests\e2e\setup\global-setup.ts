import { chromium, FullConfig } from '@playwright/test';
import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting global test setup...');
  
  const startTime = Date.now();
  
  try {
    // 1. Ensure test environment is clean
    await cleanupTestEnvironment();
    
    // 2. Setup test database if needed
    await setupTestDatabase();
    
    // 3. Create test user and authentication
    await setupTestAuthentication();
    
    // 4. Verify application is running
    await verifyApplicationHealth();
    
    // 5. Setup test data
    await setupTestData();
    
    // 6. Create test artifacts directories
    await createTestDirectories();
    
    console.log(`✅ Global setup completed in ${Date.now() - startTime}ms`);
    
  } catch (error) {
    console.error('❌ Global setup failed:', error);
    throw error;
  }
}

async function cleanupTestEnvironment() {
  console.log('🧹 Cleaning up test environment...');
  
  // Clear any existing test data
  const testDirs = [
    'tests/e2e/results',
    'tests/e2e/reports',
    'tests/e2e/screenshots',
    'tests/e2e/videos',
    'tests/e2e/traces',
  ];
  
  for (const dir of testDirs) {
    const fullPath = path.join(process.cwd(), dir);
    if (fs.existsSync(fullPath)) {
      fs.rmSync(fullPath, { recursive: true, force: true });
    }
  }
  
  // Clear any test cookies or local storage
  const browser = await chromium.launch();
  const context = await browser.newContext();
  await context.clearCookies();
  await browser.close();
}

async function setupTestDatabase() {
  console.log('🗄️  Setting up test database...');
  
  try {
    // Run database migrations for test environment
    execSync('npm run db:migrate:test', { stdio: 'inherit' });
    
    // Seed test data
    execSync('npm run db:seed:test', { stdio: 'inherit' });
    
    console.log('✅ Test database setup completed');
  } catch (error) {
    console.log('ℹ️  Database setup skipped (not configured)');
  }
}

async function setupTestAuthentication() {
  console.log('🔐 Setting up test authentication...');
  
  const browser = await chromium.launch();
  const context = await browser.newContext();
  const page = await context.newPage();
  
  try {
    // Create test user session
    await page.goto(process.env.BASE_URL || 'http://localhost:3000');
    
    // Login as test user if authentication is required
    const loginButton = page.locator('[data-testid="login-button"]');
    if (await loginButton.isVisible()) {
      await loginButton.click();
      await page.fill('[data-testid="email-input"]', '<EMAIL>');
      await page.fill('[data-testid="password-input"]', 'testpassword');
      await page.click('[data-testid="submit-button"]');
      
      // Wait for successful login
      await page.waitForURL('**/dashboard');
    }
    
    // Save authentication state
    await context.storageState({ path: 'tests/e2e/auth/user.json' });
    
    console.log('✅ Test authentication setup completed');
  } catch (error) {
    console.log('ℹ️  Authentication setup skipped (not required)');
  } finally {
    await browser.close();
  }
}

async function verifyApplicationHealth() {
  console.log('🏥 Verifying application health...');
  
  const browser = await chromium.launch();
  const context = await browser.newContext();
  const page = await context.newPage();
  
  try {
    const baseURL = process.env.BASE_URL || 'http://localhost:3000';
    
    // Check if application is running
    const response = await page.goto(`${baseURL}/api/health`);
    if (!response || response.status() !== 200) {
      throw new Error(`Application health check failed: ${response?.status()}`);
    }
    
    // Check if main page loads
    await page.goto(baseURL);
    await page.waitForLoadState('domcontentloaded');
    
    // Check if calculator page loads
    await page.goto(`${baseURL}/calculator`);
    await page.waitForLoadState('domcontentloaded');
    
    console.log('✅ Application health check passed');
  } catch (error) {
    console.error('❌ Application health check failed:', error);
    throw error;
  } finally {
    await browser.close();
  }
}

async function setupTestData() {
  console.log('📊 Setting up test data...');
  
  const testData = {
    materials: [
      { id: 'cement', name: 'Portland Cement', price: 450 },
      { id: 'steel', name: 'TMT Steel', price: 65 },
      { id: 'sand', name: 'River Sand', price: 1800 },
    ],
    locations: [
      { id: 'bangalore', name: 'Bangalore', multiplier: 1.0 },
      { id: 'mumbai', name: 'Mumbai', multiplier: 1.2 },
      { id: 'delhi', name: 'Delhi', multiplier: 1.05 },
    ],
    qualityTiers: ['smart', 'premium', 'luxury'],
  };
  
  // Save test data to file
  const testDataPath = path.join(process.cwd(), 'tests/e2e/data/test-data.json');
  fs.writeFileSync(testDataPath, JSON.stringify(testData, null, 2));
  
  console.log('✅ Test data setup completed');
}

async function createTestDirectories() {
  console.log('📁 Creating test directories...');
  
  const directories = [
    'tests/e2e/results',
    'tests/e2e/reports',
    'tests/e2e/reports/html',
    'tests/e2e/reports/json',
    'tests/e2e/reports/junit',
    'tests/e2e/screenshots',
    'tests/e2e/videos',
    'tests/e2e/traces',
    'tests/e2e/auth',
    'tests/e2e/data',
  ];
  
  for (const dir of directories) {
    const fullPath = path.join(process.cwd(), dir);
    if (!fs.existsSync(fullPath)) {
      fs.mkdirSync(fullPath, { recursive: true });
    }
  }
  
  console.log('✅ Test directories created');
}

export default globalSetup;