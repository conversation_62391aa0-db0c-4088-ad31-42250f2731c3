import type { Metadata, Viewport } from 'next';
import { <PERSON>ei<PERSON>, <PERSON>eist_Mono } from 'next/font/google';
import { Suspense } from 'react';
import { QueryProvider } from '@/lib/providers/QueryProvider';
import { AuthProvider } from '@/contexts/AuthContext';
import { LoadingProvider } from '@/contexts/LoadingContext';
import { ErrorBoundary } from '@/components/ui/error-boundary';
import { Analytics } from '@/components/common/Analytics';
import { Toaster } from 'react-hot-toast';
import { appConfig } from '@/lib/env';
import './globals.css';

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
  display: 'swap',
  preload: true,
});

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
  display: 'swap',
});

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 5,
  userScalable: true,
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: '#ffffff' },
    { media: '(prefers-color-scheme: dark)', color: '#0f172a' },
  ],
};

export const metadata: Metadata = {
  title: {
    default: 'Clarity Engine - AI-Powered Construction Cost Calculator for India',
    template: '%s | Clarity Engine - Construction Cost Calculator',
  },
  description: 'Get accurate construction cost estimates for Indian market. AI-powered calculator with real-time material prices, regional rates, and IS code compliance. Smart, Premium & Luxury quality tiers available.',
  keywords: [
    'construction cost calculator',
    'building cost estimation',
    'construction rates India',
    'material cost calculator',
    'residential construction cost',
    'commercial construction cost',
    'construction planning tool',
    'building material prices',
    'Indian construction rates',
    'IS code compliant calculator',
    'construction budget planner',
    'architectural cost estimation',
    'civil engineering calculator',
    'real estate cost calculator'
  ].join(', '),
  authors: [
    { name: 'Clarity Engine Team', url: 'https://clarityengine.app' }
  ],
  creator: 'Clarity Engine',
  publisher: 'Clarity Engine',
  applicationName: 'Clarity Engine',
  category: 'Construction Technology',
  classification: 'Construction Cost Calculator',
  
  // Open Graph
  openGraph: {
    type: 'website',
    locale: 'en_IN',
    url: appConfig.url,
    siteName: 'Clarity Engine',
    title: 'Clarity Engine - AI-Powered Construction Cost Calculator for India',
    description: 'Get accurate construction cost estimates for Indian market with real-time material prices and regional rates.',
    images: [
      {
        url: `${appConfig.url}/og-image.png`,
        width: 1200,
        height: 630,
        alt: 'Clarity Engine - Construction Cost Calculator',
        type: 'image/png',
      },
      {
        url: `${appConfig.url}/og-image-square.png`,
        width: 800,
        height: 800,
        alt: 'Clarity Engine Logo',
        type: 'image/png',
      },
    ],
  },

  // Twitter
  twitter: {
    card: 'summary_large_image',
    site: '@clarityengine',
    creator: '@clarityengine',
    title: 'Clarity Engine - Construction Cost Calculator',
    description: 'AI-powered construction cost calculator for India with real-time pricing',
    images: [`${appConfig.url}/twitter-image.png`],
  },

  // Additional meta tags
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },

  // App links
  appleWebApp: {
    capable: true,
    statusBarStyle: 'default',
    title: 'Clarity Engine',
  },

  // Verification
  verification: {
    google: 'google-site-verification-code',
    // Add other verification codes as needed
  },

  // Additional metadata
  alternates: {
    canonical: appConfig.url,
    languages: {
      'en-IN': `${appConfig.url}/en-IN`,
      'hi-IN': `${appConfig.url}/hi-IN`,
    },
  },

  // Web app manifest
  manifest: '/manifest.json',

  // Icons
  icons: {
    icon: [
      { url: '/favicon-16x16.png', sizes: '16x16', type: 'image/png' },
      { url: '/favicon-32x32.png', sizes: '32x32', type: 'image/png' },
    ],
    apple: [
      { url: '/apple-touch-icon.png', sizes: '180x180', type: 'image/png' },
    ],
    other: [
      { rel: 'mask-icon', url: '/safari-pinned-tab.svg', color: '#0ea5e9' },
    ],
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang='en'>
      <head>
        <meta name="format-detection" content="telephone=no" />
        <meta name="msapplication-TileColor" content="#0ea5e9" />
        <meta name="msapplication-config" content="/browserconfig.xml" />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <ErrorBoundary level="critical">
          <QueryProvider>
            <LoadingProvider>
              <AuthProvider>
                {children}
              </AuthProvider>
            </LoadingProvider>
          </QueryProvider>
          <Toaster
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: '#363636',
                color: '#fff',
              },
            }}
          />
          <Suspense fallback={null}>
            <Analytics />
          </Suspense>
        </ErrorBoundary>
      </body>
    </html>
  );
}
