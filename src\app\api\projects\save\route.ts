import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import type { Database } from '@/types/supabase';

// Validation schema for saving projects
const saveProjectSchema = z.object({
  name: z.string().min(1, 'Project name is required').max(100, 'Project name too long'),
  location: z.string().min(1, 'Location is required'),
  area_sqft: z.number().min(1, 'Area must be greater than 0'),
  floors: z.number().min(0, 'Floors cannot be negative').max(10, 'Maximum 10 floors'),
  quality_tier: z.enum(['smart', 'premium', 'luxury']),
  calculation_data: z.object({
    // Core form data
    formData: z.object({
      builtUpArea: z.number(),
      plotSize: z.number().optional(),
      floors: z.number(),
      basements: z.number().optional(),
      qualityTier: z.enum(['smart', 'premium', 'luxury']),
      location: z.string(),
      hasParking: z.boolean().optional(),
      parkingType: z.enum(['open', 'covered']).optional(),
    }),
    // Calculation results
    results: z.object({
      totalCost: z.number(),
      costPerSqft: z.number(),
      costBreakdown: z.record(z.string(), z.unknown()),
      materials: z.array(z.unknown()).optional(),
      timeline: z.object({
        totalDuration: z.number(),
        phases: z.array(z.unknown()),
      }),
      summary: z.object({
        projectType: z.string(),
        constructionType: z.string(),
        totalBuiltUpArea: z.number(),
        estimatedTimeline: z.string(),
      }),
    }),
    // Metadata
    calculatedAt: z.string(),
    version: z.string().optional(),
  }),
});

export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient<Database>({ 
      cookies 
    });

    // Check authentication
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = saveProjectSchema.parse(body);

    // Create project record
    const { data: project, error: insertError } = await supabase
      .from('projects')
      .insert({
        user_id: user.id,
        name: validatedData.name,
        location: validatedData.location,
        area_sqft: validatedData.area_sqft,
        floors: validatedData.floors,
        quality_tier: validatedData.quality_tier,
        calculation_data: validatedData.calculation_data,
      })
      .select()
      .single();

    if (insertError) {
      console.error('Error inserting project:', insertError);
      return NextResponse.json(
        { error: 'Failed to save project' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      project: {
        id: project.id,
        name: project.name,
        location: project.location,
        area_sqft: project.area_sqft,
        quality_tier: project.quality_tier,
        created_at: project.created_at,
      },
    });
  } catch (error) {
    console.error('Save project error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid data', details: error.issues },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}