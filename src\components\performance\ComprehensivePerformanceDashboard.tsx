/**
 * Comprehensive Performance Dashboard
 * Real-time monitoring and analysis of React components and memory usage
 */

'use client';

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { <PERSON><PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { ScrollArea } from '@/components/ui/scroll-area';
import { reactProfiler } from '@/lib/performance/react-profiler';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  Area,
  AreaChart,
} from 'recharts';
import { AlertTriangle, TrendingUp, TrendingDown, Activity, Cpu, Clock, Brain } from 'lucide-react';

interface PerformanceMetrics {
  componentCount: number;
  totalRenderTime: number;
  averageRenderTime: number;
  memoryUsage: number;
  memoryLeaks: number;
  budgetViolations: number;
  optimizationScore: number;
}

interface ComponentStats {
  id: string;
  renderCount: number;
  averageRenderTime: number;
  memoryUsage: number;
  isOptimized: boolean;
  warnings: string[];
  recommendations: string[];
}

interface MemoryLeakInfo {
  componentId: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  memoryGrowth: number;
  timeDetected: Date;
  status: 'active' | 'resolved' | 'monitoring';
}

// Alias for compatibility
interface BrainLeakInfo extends MemoryLeakInfo {}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

const severityColors = {
  low: 'bg-green-100 text-green-800',
  medium: 'bg-yellow-100 text-yellow-800',
  high: 'bg-orange-100 text-orange-800',
  critical: 'bg-red-100 text-red-800',
};

const formatBytes = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

const formatDuration = (ms: number): string => {
  if (ms < 1000) return `${ms.toFixed(2)}ms`;
  return `${(ms / 1000).toFixed(2)}s`;
};

export const ComprehensivePerformanceDashboard: React.FC = () => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    componentCount: 0,
    totalRenderTime: 0,
    averageRenderTime: 0,
    memoryUsage: 0,
    memoryLeaks: 0,
    budgetViolations: 0,
    optimizationScore: 100,
  });

  const [componentStats, setComponentStats] = useState<ComponentStats[]>([]);
  const [memoryLeaks, setMemoryLeaks] = useState<BrainLeakInfo[]>([]);
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [renderTimeline, setRenderTimeline] = useState<any[]>([]);
  const [memoryTimeline, setMemoryTimeline] = useState<any[]>([]);

  // Enable profiling when dashboard is mounted
  useEffect(() => {
    reactProfiler.enableProfiling();
    return () => {
      if (!isMonitoring) {
        reactProfiler.disableProfiling();
      }
    };
  }, [isMonitoring]);

  // Fetch performance data
  const fetchPerformanceData = useCallback(async () => {
    try {
      const summary = reactProfiler.getPerformanceSummary();
      const profiles = reactProfiler.getAllProfiles();
      const memoryReport = reactProfiler.getMemoryLeakReport();

      // Update metrics
      setMetrics({
        componentCount: summary.totalComponents,
        totalRenderTime: summary.totalComponents * summary.averageRenderTime,
        averageRenderTime: summary.averageRenderTime,
        memoryUsage: getCurrentMemoryUsage(),
        memoryLeaks: memoryReport.summary.leakingComponents,
        budgetViolations: summary.budgetViolations,
        optimizationScore: summary.optimizationScore,
      });

      // Update component stats
      const stats: ComponentStats[] = profiles.map(profile => ({
        id: profile.id,
        renderCount: profile.metrics.renderCount,
        averageRenderTime: profile.metrics.averageRenderTime,
        memoryUsage: profile.metrics.memorySizeEstimate,
        isOptimized: profile.metrics.performance.isOptimized,
        warnings: profile.violations,
        recommendations: profile.recommendations,
      }));
      setComponentStats(stats);

      // Update memory leaks
      const leaks: MemoryLeakInfo[] = memoryReport.details
        .filter(detector => detector.isLeaking)
        .map(detector => ({
          componentId: detector.componentId,
          severity: detector.leakSeverity,
          memoryGrowth: detector.memoryGrowth.length > 1 
            ? detector.memoryGrowth[detector.memoryGrowth.length - 1] - detector.memoryGrowth[0]
            : 0,
          timeDetected: new Date(detector.mountTime),
          status: 'active' as const,
        }));
      setMemoryLeaks(leaks);

      // Update timelines
      const now = Date.now();
      setRenderTimeline(prev => {
        const newEntry = {
          time: now,
          averageRenderTime: summary.averageRenderTime,
          componentCount: summary.totalComponents,
        };
        const updated = [...prev, newEntry].slice(-20); // Keep last 20 entries
        return updated;
      });

      setMemoryTimeline(prev => {
        const newEntry = {
          time: now,
          memoryUsage: getCurrentMemoryUsage(),
          memoryLeaks: memoryReport.summary.leakingComponents,
        };
        const updated = [...prev, newEntry].slice(-20); // Keep last 20 entries
        return updated;
      });

    } catch (error) {
      console.error('Error fetching performance data:', error);
    }
  }, []);

  // Auto-refresh effect
  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(fetchPerformanceData, 2000);
      return () => clearInterval(interval);
    }
  }, [autoRefresh, fetchPerformanceData]);

  // Initial data fetch
  useEffect(() => {
    fetchPerformanceData();
  }, [fetchPerformanceData]);

  const getCurrentMemoryUsage = (): number => {
    if (typeof window !== 'undefined' && (window as any).performance?.memory) {
      return (window as any).performance.memory.usedJSHeapSize;
    }
    return 0;
  };

  // Component render performance chart data
  const renderChartData = useMemo(() => {
    return componentStats.map(stat => ({
      name: stat.id.substring(0, 15) + (stat.id.length > 15 ? '...' : ''),
      renderTime: stat.averageRenderTime,
      renderCount: stat.renderCount,
      memoryUsage: stat.memoryUsage / (1024 * 1024), // Convert to MB
    }));
  }, [componentStats]);

  // Memory leak severity distribution
  const memoryLeakDistribution = useMemo(() => {
    const distribution = memoryLeaks.reduce((acc, leak) => {
      acc[leak.severity] = (acc[leak.severity] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return Object.entries(distribution).map(([severity, count]) => ({
      name: severity,
      value: count,
      color: severityColors[severity as keyof typeof severityColors],
    }));
  }, [memoryLeaks]);

  // Performance score calculation
  const getPerformanceGrade = (score: number): { grade: string; color: string } => {
    if (score >= 90) return { grade: 'A+', color: 'text-green-600' };
    if (score >= 80) return { grade: 'A', color: 'text-green-500' };
    if (score >= 70) return { grade: 'B', color: 'text-blue-500' };
    if (score >= 60) return { grade: 'C', color: 'text-yellow-500' };
    if (score >= 50) return { grade: 'D', color: 'text-orange-500' };
    return { grade: 'F', color: 'text-red-500' };
  };

  const performanceGrade = getPerformanceGrade(metrics.optimizationScore);

  const toggleMonitoring = () => {
    setIsMonitoring(!isMonitoring);
    if (!isMonitoring) {
      reactProfiler.enableProfiling();
    } else {
      reactProfiler.disableProfiling();
    }
  };

  const clearMetrics = () => {
    reactProfiler.clearMetrics();
    setMetrics({
      componentCount: 0,
      totalRenderTime: 0,
      averageRenderTime: 0,
      memoryUsage: 0,
      memoryLeaks: 0,
      budgetViolations: 0,
      optimizationScore: 100,
    });
    setComponentStats([]);
    setMemoryLeaks([]);
    setRenderTimeline([]);
    setMemoryTimeline([]);
  };

  const exportReport = () => {
    const report = reactProfiler.exportReport();
    const blob = new Blob([report], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `performance-report-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="w-full max-w-7xl mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Performance Dashboard</h1>
          <p className="text-muted-foreground">Real-time React component monitoring and optimization</p>
        </div>
        <div className="flex gap-2">
          <Button
            variant={autoRefresh ? 'default' : 'outline'}
            onClick={() => setAutoRefresh(!autoRefresh)}
            size="sm"
          >
            {autoRefresh ? 'Auto-Refresh On' : 'Auto-Refresh Off'}
          </Button>
          <Button
            variant={isMonitoring ? 'destructive' : 'default'}
            onClick={toggleMonitoring}
            size="sm"
          >
            {isMonitoring ? 'Stop Monitoring' : 'Start Monitoring'}
          </Button>
          <Button variant="outline" onClick={clearMetrics} size="sm">
            Clear Metrics
          </Button>
          <Button variant="outline" onClick={exportReport} size="sm">
            Export Report
          </Button>
        </div>
      </div>

      {/* Performance Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Activity className="h-4 w-4" />
              Components
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.componentCount}</div>
            <p className="text-xs text-muted-foreground">Active components</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Clock className="h-4 w-4" />
              Render Time
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatDuration(metrics.averageRenderTime)}</div>
            <p className="text-xs text-muted-foreground">Average render time</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Brain className="h-4 w-4" />
              Memory Usage
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatBytes(metrics.memoryUsage)}</div>
            <p className="text-xs text-muted-foreground">Current memory usage</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Cpu className="h-4 w-4" />
              Performance Score
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${performanceGrade.color}`}>
              {performanceGrade.grade}
            </div>
            <p className="text-xs text-muted-foreground">{metrics.optimizationScore}/100</p>
          </CardContent>
        </Card>
      </div>

      {/* Alerts */}
      {(metrics.memoryLeaks > 0 || metrics.budgetViolations > 0) && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Performance Issues Detected</AlertTitle>
          <AlertDescription>
            {metrics.memoryLeaks > 0 && (
              <div>Memory leaks detected in {metrics.memoryLeaks} components.</div>
            )}
            {metrics.budgetViolations > 0 && (
              <div>Performance budget violations: {metrics.budgetViolations}</div>
            )}
          </AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="components">Components</TabsTrigger>
          <TabsTrigger value="memory">Memory</TabsTrigger>
          <TabsTrigger value="timeline">Timeline</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Component Render Performance</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={renderChartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="renderTime" fill="#8884d8" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Memory Leak Distribution</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={memoryLeakDistribution}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, value }) => `${name}: ${value}`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {memoryLeakDistribution.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="components" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Component Performance Details</CardTitle>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-[600px]">
                <div className="space-y-4">
                  {componentStats.map((stat) => (
                    <div key={stat.id} className="border rounded-lg p-4">
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <h3 className="font-medium">{stat.id}</h3>
                          <div className="flex gap-4 text-sm text-muted-foreground">
                            <span>Renders: {stat.renderCount}</span>
                            <span>Avg Time: {formatDuration(stat.averageRenderTime)}</span>
                            <span>Memory: {formatBytes(stat.memoryUsage)}</span>
                          </div>
                        </div>
                        <Badge variant={stat.isOptimized ? 'default' : 'destructive'}>
                          {stat.isOptimized ? 'Optimized' : 'Needs Optimization'}
                        </Badge>
                      </div>
                      
                      {stat.warnings.length > 0 && (
                        <div className="mt-2">
                          <h4 className="text-sm font-medium text-red-600 mb-1">Warnings:</h4>
                          <ul className="text-sm text-red-600 space-y-1">
                            {stat.warnings.map((warning, index) => (
                              <li key={index}>• {warning}</li>
                            ))}
                          </ul>
                        </div>
                      )}
                      
                      {stat.recommendations.length > 0 && (
                        <div className="mt-2">
                          <h4 className="text-sm font-medium text-blue-600 mb-1">Recommendations:</h4>
                          <ul className="text-sm text-blue-600 space-y-1">
                            {stat.recommendations.map((rec, index) => (
                              <li key={index}>• {rec}</li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="memory" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Memory Leak Detection</CardTitle>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-[400px]">
                <div className="space-y-4">
                  {memoryLeaks.length === 0 ? (
                    <div className="text-center text-muted-foreground py-8">
                      No memory leaks detected
                    </div>
                  ) : (
                    memoryLeaks.map((leak, index) => (
                      <div key={index} className="border rounded-lg p-4">
                        <div className="flex justify-between items-start mb-2">
                          <div>
                            <h3 className="font-medium">{leak.componentId}</h3>
                            <p className="text-sm text-muted-foreground">
                              Growth: {formatBytes(leak.memoryGrowth)}
                            </p>
                            <p className="text-sm text-muted-foreground">
                              Detected: {leak.timeDetected.toLocaleString()}
                            </p>
                          </div>
                          <Badge className={severityColors[leak.severity]}>
                            {leak.severity}
                          </Badge>
                        </div>
                        <div className="mt-2">
                          <Badge variant="outline">{leak.status}</Badge>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="timeline" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Render Time Timeline</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={renderTimeline}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="time" tickFormatter={(time) => new Date(time).toLocaleTimeString()} />
                    <YAxis />
                    <Tooltip labelFormatter={(time) => new Date(time).toLocaleTimeString()} />
                    <Line type="monotone" dataKey="averageRenderTime" stroke="#8884d8" />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Memory Usage Timeline</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={memoryTimeline}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="time" tickFormatter={(time) => new Date(time).toLocaleTimeString()} />
                    <YAxis tickFormatter={(value) => formatBytes(value)} />
                    <Tooltip 
                      labelFormatter={(time) => new Date(time).toLocaleTimeString()}
                      formatter={(value) => formatBytes(value as number)}
                    />
                    <Area type="monotone" dataKey="memoryUsage" stroke="#82ca9d" fill="#82ca9d" />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ComprehensivePerformanceDashboard;