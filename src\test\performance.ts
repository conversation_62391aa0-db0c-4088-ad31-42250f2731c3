import { vi } from 'vitest';

// Performance measurement utilities
export class PerformanceMonitor {
  private metrics: Map<string, number[]> = new Map();
  private startTimes: Map<string, number> = new Map();

  start(label: string): void {
    this.startTimes.set(label, performance.now());
  }

  end(label: string): number {
    const startTime = this.startTimes.get(label);
    if (!startTime) {
      throw new Error(`No start time found for label: ${label}`);
    }

    const duration = performance.now() - startTime;
    
    if (!this.metrics.has(label)) {
      this.metrics.set(label, []);
    }
    
    this.metrics.get(label)!.push(duration);
    this.startTimes.delete(label);
    
    return duration;
  }

  getMetrics(label: string) {
    const measurements = this.metrics.get(label) || [];
    
    if (measurements.length === 0) {
      return null;
    }

    const sorted = [...measurements].sort((a, b) => a - b);
    const sum = measurements.reduce((a, b) => a + b, 0);
    
    return {
      count: measurements.length,
      min: Math.min(...measurements),
      max: Math.max(...measurements),
      average: sum / measurements.length,
      median: sorted[Math.floor(sorted.length / 2)],
      p95: sorted[Math.floor(sorted.length * 0.95)],
      p99: sorted[Math.floor(sorted.length * 0.99)],
      measurements,
    };
  }

  getAllMetrics() {
    const result: Record<string, any> = {};
    
    for (const [label, _] of this.metrics) {
      result[label] = this.getMetrics(label);
    }
    
    return result;
  }

  clear(): void {
    this.metrics.clear();
    this.startTimes.clear();
  }

  reset(label?: string): void {
    if (label) {
      this.metrics.delete(label);
      this.startTimes.delete(label);
    } else {
      this.clear();
    }
  }
}

// Memory usage utilities
export class MemoryMonitor {
  private snapshots: Array<{
    timestamp: number;
    memory: any;
    label?: string;
  }> = [];

  takeSnapshot(label?: string): any {
    const memory = this.getCurrentMemory();
    const snapshot = {
      timestamp: Date.now(),
      memory,
      label,
    };
    
    this.snapshots.push(snapshot);
    return memory;
  }

  getCurrentMemory(): any {
    if ('memory' in performance) {
      return (performance as any).memory;
    }
    
    // Fallback for browsers without performance.memory
    return {
      usedJSHeapSize: 0,
      totalJSHeapSize: 0,
      jsHeapSizeLimit: 0,
    };
  }

  getMemoryDiff(startLabel: string, endLabel: string) {
    const startSnapshot = this.snapshots.find(s => s.label === startLabel);
    const endSnapshot = this.snapshots.find(s => s.label === endLabel);
    
    if (!startSnapshot || !endSnapshot) {
      return null;
    }
    
    return {
      usedJSHeapSize: endSnapshot.memory.usedJSHeapSize - startSnapshot.memory.usedJSHeapSize,
      totalJSHeapSize: endSnapshot.memory.totalJSHeapSize - startSnapshot.memory.totalJSHeapSize,
      duration: endSnapshot.timestamp - startSnapshot.timestamp,
    };
  }

  getSnapshots() {
    return [...this.snapshots];
  }

  clear(): void {
    this.snapshots = [];
  }
}

// Benchmark utilities
export class BenchmarkSuite {
  private benchmarks: Map<string, () => Promise<void> | void> = new Map();
  private results: Map<string, any> = new Map();
  private performanceMonitor = new PerformanceMonitor();
  private memoryMonitor = new MemoryMonitor();

  add(name: string, fn: () => Promise<void> | void): void {
    this.benchmarks.set(name, fn);
  }

  async run(iterations: number = 100): Promise<void> {
    console.log(`Running ${this.benchmarks.size} benchmarks with ${iterations} iterations each...`);
    
    for (const [name, fn] of this.benchmarks) {
      console.log(`Running benchmark: ${name}`);
      
      // Warm up
      await this.runBenchmark(name, fn, 10);
      
      // Reset metrics
      this.performanceMonitor.reset(name);
      this.memoryMonitor.clear();
      
      // Take initial memory snapshot
      this.memoryMonitor.takeSnapshot(`${name}_start`);
      
      // Run actual benchmark
      await this.runBenchmark(name, fn, iterations);
      
      // Take final memory snapshot
      this.memoryMonitor.takeSnapshot(`${name}_end`);
      
      // Store results
      const performanceMetrics = this.performanceMonitor.getMetrics(name);
      const memoryDiff = this.memoryMonitor.getMemoryDiff(`${name}_start`, `${name}_end`);
      
      this.results.set(name, {
        performance: performanceMetrics,
        memory: memoryDiff,
      });
    }
  }

  private async runBenchmark(name: string, fn: () => Promise<void> | void, iterations: number): Promise<void> {
    for (let i = 0; i < iterations; i++) {
      this.performanceMonitor.start(name);
      await fn();
      this.performanceMonitor.end(name);
    }
  }

  getResults(): Map<string, any> {
    return new Map(this.results);
  }

  getResult(name: string): any {
    return this.results.get(name);
  }

  clear(): void {
    this.benchmarks.clear();
    this.results.clear();
    this.performanceMonitor.clear();
    this.memoryMonitor.clear();
  }
}

// Load testing utilities
export class LoadTester {
  private concurrentTasks: number = 10;
  private totalRequests: number = 100;
  private requestDelay: number = 0;

  configure(options: {
    concurrentTasks?: number;
    totalRequests?: number;
    requestDelay?: number;
  }): void {
    this.concurrentTasks = options.concurrentTasks ?? this.concurrentTasks;
    this.totalRequests = options.totalRequests ?? this.totalRequests;
    this.requestDelay = options.requestDelay ?? this.requestDelay;
  }

  async test(
    taskFn: () => Promise<any>,
    options?: { timeout?: number }
  ): Promise<{
    totalTime: number;
    averageTime: number;
    successRate: number;
    errors: Error[];
    throughput: number;
  }> {
    const results: Array<{ success: boolean; duration: number; error?: Error }> = [];
    const startTime = performance.now();
    
    const executeTask = async (): Promise<void> => {
      const taskStart = performance.now();
      
      try {
        await taskFn();
        results.push({
          success: true,
          duration: performance.now() - taskStart,
        });
      } catch (error) {
        results.push({
          success: false,
          duration: performance.now() - taskStart,
          error: error instanceof Error ? error : new Error(String(error)),
        });
      }
      
      if (this.requestDelay > 0) {
        await new Promise(resolve => setTimeout(resolve, this.requestDelay));
      }
    };

    // Execute tasks in batches
    const batches = Math.ceil(this.totalRequests / this.concurrentTasks);
    
    for (let batch = 0; batch < batches; batch++) {
      const batchSize = Math.min(this.concurrentTasks, this.totalRequests - batch * this.concurrentTasks);
      const batchTasks = Array.from({ length: batchSize }, () => executeTask());
      
      if (options?.timeout) {
        await Promise.race([
          Promise.all(batchTasks),
          new Promise((_, reject) => 
            setTimeout(() => reject(new Error('Timeout')), options.timeout)
          ),
        ]);
      } else {
        await Promise.all(batchTasks);
      }
    }

    const totalTime = performance.now() - startTime;
    const successfulResults = results.filter(r => r.success);
    const errors = results.filter(r => !r.success).map(r => r.error!);
    
    return {
      totalTime,
      averageTime: successfulResults.reduce((sum, r) => sum + r.duration, 0) / successfulResults.length,
      successRate: successfulResults.length / results.length,
      errors,
      throughput: results.length / (totalTime / 1000), // requests per second
    };
  }
}

// React component performance testing
export class ComponentPerformanceTester {
  private renderTimes: number[] = [];
  private memoryUsage: any[] = [];

  async measureRenderPerformance(
    renderFn: () => { unmount: () => void },
    iterations: number = 100
  ): Promise<{
    averageRenderTime: number;
    minRenderTime: number;
    maxRenderTime: number;
    totalTime: number;
    memoryUsage: any[];
  }> {
    this.renderTimes = [];
    this.memoryUsage = [];

    for (let i = 0; i < iterations; i++) {
      // Take memory snapshot before render
      const memoryBefore = this.getCurrentMemory();
      
      const renderStart = performance.now();
      const { unmount } = renderFn();
      const renderEnd = performance.now();
      
      const renderTime = renderEnd - renderStart;
      this.renderTimes.push(renderTime);
      
      // Take memory snapshot after render
      const memoryAfter = this.getCurrentMemory();
      this.memoryUsage.push({
        before: memoryBefore,
        after: memoryAfter,
        diff: memoryAfter.usedJSHeapSize - memoryBefore.usedJSHeapSize,
      });
      
      // Clean up
      unmount();
      
      // Give garbage collector time to run
      await new Promise(resolve => setTimeout(resolve, 0));
    }

    return {
      averageRenderTime: this.renderTimes.reduce((sum, time) => sum + time, 0) / this.renderTimes.length,
      minRenderTime: Math.min(...this.renderTimes),
      maxRenderTime: Math.max(...this.renderTimes),
      totalTime: this.renderTimes.reduce((sum, time) => sum + time, 0),
      memoryUsage: this.memoryUsage,
    };
  }

  private getCurrentMemory(): any {
    if ('memory' in performance) {
      return (performance as any).memory;
    }
    
    return {
      usedJSHeapSize: 0,
      totalJSHeapSize: 0,
      jsHeapSizeLimit: 0,
    };
  }
}

// Assertion utilities for performance testing
export const performanceAssertions = {
  expectRenderTime: (actualTime: number, maxTime: number) => {
    if (actualTime > maxTime) {
      throw new Error(`Render time ${actualTime}ms exceeds maximum ${maxTime}ms`);
    }
  },

  expectMemoryUsage: (memoryUsage: number, maxUsage: number) => {
    if (memoryUsage > maxUsage) {
      throw new Error(`Memory usage ${memoryUsage} bytes exceeds maximum ${maxUsage} bytes`);
    }
  },

  expectThroughput: (actualThroughput: number, minThroughput: number) => {
    if (actualThroughput < minThroughput) {
      throw new Error(`Throughput ${actualThroughput} req/s is below minimum ${minThroughput} req/s`);
    }
  },

  expectSuccessRate: (actualRate: number, minRate: number) => {
    if (actualRate < minRate) {
      throw new Error(`Success rate ${actualRate * 100}% is below minimum ${minRate * 100}%`);
    }
  },
};

// Global performance monitor instance
export const globalPerformanceMonitor = new PerformanceMonitor();
export const globalMemoryMonitor = new MemoryMonitor();

// Performance test helper functions
export const withPerformanceMonitoring = async <T>(
  label: string,
  fn: () => Promise<T> | T
): Promise<{ result: T; duration: number }> => {
  globalPerformanceMonitor.start(label);
  const result = await fn();
  const duration = globalPerformanceMonitor.end(label);
  
  return { result, duration };
};

export const withMemoryMonitoring = async <T>(
  label: string,
  fn: () => Promise<T> | T
): Promise<{ result: T; memoryDiff: any }> => {
  globalMemoryMonitor.takeSnapshot(`${label}_start`);
  const result = await fn();
  globalMemoryMonitor.takeSnapshot(`${label}_end`);
  const memoryDiff = globalMemoryMonitor.getMemoryDiff(`${label}_start`, `${label}_end`);
  
  return { result, memoryDiff };
};

// Export default performance utilities
export default {
  PerformanceMonitor,
  MemoryMonitor,
  BenchmarkSuite,
  LoadTester,
  ComponentPerformanceTester,
  performanceAssertions,
  globalPerformanceMonitor,
  globalMemoryMonitor,
  withPerformanceMonitoring,
  withMemoryMonitoring,
};