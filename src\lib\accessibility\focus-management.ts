/**
 * Focus Management Utilities for Enhanced Accessibility
 * Provides comprehensive focus management for WCAG 2.1 AA compliance
 */

export interface FocusOptions {
  preventScroll?: boolean;
  restoreFocus?: boolean;
  selectText?: boolean;
}

export interface FocusTrap {
  activate: () => void;
  deactivate: () => void;
  pause: () => void;
  unpause: () => void;
}

/**
 * Enhanced focus management with accessibility features
 */
export class AccessibilityFocusManager {
  private static instance: AccessibilityFocusManager;
  private focusHistory: HTMLElement[] = [];
  private activeFocusTraps: Set<FocusTrap> = new Set();
  private announcements: HTMLElement;

  constructor() {
    // Only initialize on client side
    if (typeof window !== 'undefined') {
      this.createAnnouncementRegion();
      this.setupGlobalListeners();
    }
  }

  static getInstance(): AccessibilityFocusManager {
    if (!AccessibilityFocusManager.instance) {
      AccessibilityFocusManager.instance = new AccessibilityFocusManager();
    }
    return AccessibilityFocusManager.instance;
  }

  private createAnnouncementRegion(): void {
    // Only create on client side
    if (typeof window === 'undefined') return;
    
    // Create live region for screen reader announcements
    this.announcements = document.createElement('div');
    this.announcements.setAttribute('aria-live', 'polite');
    this.announcements.setAttribute('aria-atomic', 'true');
    this.announcements.className = 'sr-only';
    this.announcements.id = 'accessibility-announcements';
    document.body.appendChild(this.announcements);
  }

  private setupGlobalListeners(): void {
    // Track focus changes for history
    document.addEventListener('focusin', (event) => {
      const target = event.target as HTMLElement;
      if (target && this.isFocusable(target)) {
        this.addToFocusHistory(target);
      }
    });

    // Handle Escape key for focus traps and modals
    document.addEventListener('keydown', (event) => {
      if (event.key === 'Escape') {
        this.handleEscapeKey(event);
      }
    });
  }

  /**
   * Focus an element with enhanced options
   */
  focus(element: HTMLElement | string, options: FocusOptions = {}): boolean {
    const target = typeof element === 'string' 
      ? document.querySelector(element) as HTMLElement
      : element;

    if (!target || !this.isFocusable(target)) {
      return false;
    }

    // Store current focus for potential restoration
    if (options.restoreFocus && document.activeElement) {
      this.addToFocusHistory(document.activeElement as HTMLElement);
    }

    // Focus the element
    target.focus({ preventScroll: options.preventScroll });

    // Select text if requested and element supports it
    if (options.selectText && 'select' in target) {
      (target as HTMLInputElement).select();
    }

    return document.activeElement === target;
  }

  /**
   * Move focus to next focusable element
   */
  focusNext(container?: HTMLElement): boolean {
    const focusableElements = this.getFocusableElements(container);
    const currentIndex = focusableElements.indexOf(document.activeElement as HTMLElement);
    const nextIndex = (currentIndex + 1) % focusableElements.length;
    
    if (focusableElements[nextIndex]) {
      return this.focus(focusableElements[nextIndex]);
    }
    return false;
  }

  /**
   * Move focus to previous focusable element
   */
  focusPrevious(container?: HTMLElement): boolean {
    const focusableElements = this.getFocusableElements(container);
    const currentIndex = focusableElements.indexOf(document.activeElement as HTMLElement);
    const prevIndex = currentIndex <= 0 ? focusableElements.length - 1 : currentIndex - 1;
    
    if (focusableElements[prevIndex]) {
      return this.focus(focusableElements[prevIndex]);
    }
    return false;
  }

  /**
   * Move focus to first focusable element in container
   */
  focusFirst(container?: HTMLElement): boolean {
    const focusableElements = this.getFocusableElements(container);
    if (focusableElements.length > 0) {
      return this.focus(focusableElements[0]);
    }
    return false;
  }

  /**
   * Move focus to last focusable element in container
   */
  focusLast(container?: HTMLElement): boolean {
    const focusableElements = this.getFocusableElements(container);
    if (focusableElements.length > 0) {
      return this.focus(focusableElements[focusableElements.length - 1]);
    }
    return false;
  }

  /**
   * Restore focus to previous element
   */
  restoreFocus(): boolean {
    const lastFocused = this.focusHistory.pop();
    if (lastFocused && document.contains(lastFocused)) {
      return this.focus(lastFocused);
    }
    return false;
  }

  /**
   * Create a focus trap for modals and overlays
   */
  createFocusTrap(container: HTMLElement): FocusTrap {
    let isActive = false;
    let isPaused = false;

    const handleKeyDown = (event: KeyboardEvent) => {
      if (!isActive || isPaused) return;

      if (event.key === 'Tab') {
        this.trapTabKey(event, container);
      }
    };

    const focusTrap: FocusTrap = {
      activate: () => {
        if (isActive) return;
        isActive = true;
        this.activeFocusTraps.add(focusTrap);
        document.addEventListener('keydown', handleKeyDown);
        this.focusFirst(container);
      },

      deactivate: () => {
        if (!isActive) return;
        isActive = false;
        isPaused = false;
        this.activeFocusTraps.delete(focusTrap);
        document.removeEventListener('keydown', handleKeyDown);
      },

      pause: () => {
        isPaused = true;
      },

      unpause: () => {
        isPaused = false;
      }
    };

    return focusTrap;
  }

  /**
   * Announce text to screen readers
   */
  announce(message: string, priority: 'polite' | 'assertive' = 'polite'): void {
    if (!this.announcements) return;

    this.announcements.setAttribute('aria-live', priority);
    this.announcements.textContent = message;

    // Clear after announcement to allow repeated messages
    setTimeout(() => {
      this.announcements.textContent = '';
    }, 1000);
  }

  /**
   * Check if element is focusable
   */
  private isFocusable(element: HTMLElement): boolean {
    if (element.tabIndex < 0) return false;
    if (element.getAttribute('disabled') !== null) return false;
    if (element.getAttribute('aria-hidden') === 'true') return false;

    const style = window.getComputedStyle(element);
    if (style.display === 'none' || style.visibility === 'hidden') return false;

    const focusableElements = [
      'button', 'input', 'select', 'textarea', 'a', 'audio', 'video',
      'summary', 'iframe', 'object', 'embed', 'area', 'map'
    ];

    return (
      focusableElements.includes(element.tagName.toLowerCase()) ||
      element.tabIndex >= 0 ||
      element.contentEditable === 'true'
    );
  }

  /**
   * Get all focusable elements in container
   */
  private getFocusableElements(container: HTMLElement = document.body): HTMLElement[] {
    const selector = [
      'button:not([disabled])',
      'input:not([disabled])',
      'select:not([disabled])',
      'textarea:not([disabled])',
      'a[href]',
      'audio[controls]',
      'video[controls]',
      'summary',
      'iframe',
      'object',
      'embed',
      'area[href]',
      '[tabindex]:not([tabindex="-1"])',
      '[contenteditable="true"]'
    ].join(', ');

    const elements = Array.from(container.querySelectorAll(selector)) as HTMLElement[];
    
    return elements.filter(element => {
      const style = window.getComputedStyle(element);
      return (
        style.display !== 'none' &&
        style.visibility !== 'hidden' &&
        element.getAttribute('aria-hidden') !== 'true' &&
        !element.hasAttribute('disabled')
      );
    });
  }

  /**
   * Handle tab key trapping
   */
  private trapTabKey(event: KeyboardEvent, container: HTMLElement): void {
    const focusableElements = this.getFocusableElements(container);
    if (focusableElements.length === 0) {
      event.preventDefault();
      return;
    }

    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];

    if (event.shiftKey) {
      // Shift + Tab
      if (document.activeElement === firstElement) {
        event.preventDefault();
        this.focus(lastElement);
      }
    } else {
      // Tab
      if (document.activeElement === lastElement) {
        event.preventDefault();
        this.focus(firstElement);
      }
    }
  }

  /**
   * Add element to focus history
   */
  private addToFocusHistory(element: HTMLElement): void {
    // Remove element if already in history
    const index = this.focusHistory.indexOf(element);
    if (index > -1) {
      this.focusHistory.splice(index, 1);
    }

    // Add to end and limit history size
    this.focusHistory.push(element);
    if (this.focusHistory.length > 10) {
      this.focusHistory.shift();
    }
  }

  /**
   * Handle global escape key
   */
  private handleEscapeKey(event: KeyboardEvent): void {
    // Let the most recent focus trap handle escape first
    if (this.activeFocusTraps.size > 0) {
      // Focus traps will handle their own escape logic
      return;
    }

    // Global escape handling for other scenarios
    const modal = document.querySelector('[role="dialog"][aria-modal="true"]');
    if (modal && modal.contains(document.activeElement)) {
      // Close modal if focus is within it
      const closeButton = modal.querySelector('[aria-label*="close"], [aria-label*="Close"]');
      if (closeButton) {
        (closeButton as HTMLElement).click();
      }
    }
  }
}

// Export singleton instance
export const focusManager = AccessibilityFocusManager.getInstance();

/**
 * React hook for focus management
 */
export function useFocusManagement() {
  return {
    focus: focusManager.focus.bind(focusManager),
    focusNext: focusManager.focusNext.bind(focusManager),
    focusPrevious: focusManager.focusPrevious.bind(focusManager),
    focusFirst: focusManager.focusFirst.bind(focusManager),
    focusLast: focusManager.focusLast.bind(focusManager),
    restoreFocus: focusManager.restoreFocus.bind(focusManager),
    createFocusTrap: focusManager.createFocusTrap.bind(focusManager),
    announce: focusManager.announce.bind(focusManager)
  };
}