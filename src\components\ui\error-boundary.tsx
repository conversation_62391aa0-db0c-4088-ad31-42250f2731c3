/**
 * Error Boundary Components
 * Provides comprehensive error handling for the calculator application
 */

'use client';

import React, { Component, ReactNode } from 'react';
import { AlertTriangle, RefreshCw, Home } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

// Error Types
export interface ErrorInfo {
  componentStack: string;
  errorBoundary: string;
}

export interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string;
}

// Props for Error Boundary
interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: (error: Error, errorInfo: ErrorInfo, retry: () => void) => ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  level?: 'page' | 'component' | 'critical';
}

// Main Error Boundary Class Component
export class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  private retryTimeoutId: number | null = null;

  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: '',
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    // Generate unique error ID for tracking
    const errorId = `error_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

    return {
      hasError: true,
      error,
      errorId,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    const enhancedErrorInfo: ErrorInfo = {
      componentStack: errorInfo.componentStack || 'Unknown component stack',
      errorBoundary: this.constructor.name || 'ErrorBoundary',
    };

    this.setState({
      errorInfo: enhancedErrorInfo,
    });

    // Log error for monitoring
    this.logError(error, enhancedErrorInfo);

    // Call onError callback if provided
    if (this.props.onError) {
      this.props.onError(error, enhancedErrorInfo);
    }
  }

  componentWillUnmount() {
    if (this.retryTimeoutId) {
      window.clearTimeout(this.retryTimeoutId);
    }
  }

  private logError = (error: Error, errorInfo: ErrorInfo) => {
    const logData = {
      errorId: this.state.errorId,
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      level: this.props.level || 'component',
      timestamp: new Date().toISOString(),
      url: typeof window !== 'undefined' ? window.location.href : 'unknown',
      userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : 'unknown',
    };

    // In production, send to monitoring service
    console.error('Error Boundary caught error:', logData);

    // For critical errors, also send to external monitoring
    if (this.props.level === 'critical') {
      // In production: send to Sentry, DataDog, etc.
      console.error('CRITICAL ERROR:', logData);
    }
  };

  private handleRetry = () => {
    // Clear error state to retry rendering
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: '',
    });
  };

  private handleRefresh = () => {
    if (typeof window !== 'undefined') {
      window.location.reload();
    }
  };

  render() {
    if (this.state.hasError && this.state.error && this.state.errorInfo) {
      // Use custom fallback if provided
      if (this.props.fallback) {
        return this.props.fallback(this.state.error, this.state.errorInfo, this.handleRetry);
      }

      // Default error UI based on level
      return this.renderDefaultErrorUI();
    }

    return this.props.children;
  }

  private renderDefaultErrorUI() {
    const { level = 'component' } = this.props;
    const { error, errorId } = this.state;

    if (level === 'page') {
      return <ErrorPage error={error!} errorId={errorId} onRetry={this.handleRetry} onRefresh={this.handleRefresh} />;
    }

    if (level === 'critical') {
      return <CriticalError error={error!} errorId={errorId} onRefresh={this.handleRefresh} />;
    }

    // Default component-level error
    return <ComponentError error={error!} errorId={errorId} onRetry={this.handleRetry} />;
  }
}

// Page-level Error Component
interface ErrorPageProps {
  error: Error;
  errorId: string;
  onRetry: () => void;
  onRefresh: () => void;
}

function ErrorPage({ error, errorId, onRetry, onRefresh }: ErrorPageProps) {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 p-3 bg-red-100 rounded-full w-fit">
            <AlertTriangle className="h-8 w-8 text-red-600" />
          </div>
          <CardTitle className="text-xl text-gray-900">Something went wrong</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-gray-600 text-center">
            We encountered an unexpected error. Please try refreshing the page or contact support if the problem persists.
          </p>

          <div className="text-xs text-gray-400 bg-gray-50 p-2 rounded">
            <div>Error ID: {errorId}</div>
            <div>Message: {error.message}</div>
          </div>

          <div className="flex flex-col gap-2">
            <Button onClick={onRetry} variant="default" className="w-full">
              <RefreshCw className="mr-2 h-4 w-4" />
              Try Again
            </Button>
            <Button onClick={onRefresh} variant="outline" className="w-full">
              <Home className="mr-2 h-4 w-4" />
              Refresh Page
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

// Component-level Error Component
interface ComponentErrorProps {
  error: Error;
  errorId: string;
  onRetry: () => void;
}

function ComponentError({ error, errorId, onRetry }: ComponentErrorProps) {
  return (
    <Card className="border-red-200 bg-red-50">
      <CardContent className="p-4">
        <div className="flex items-start gap-3">
          <AlertTriangle className="h-5 w-5 text-red-600 mt-0.5" />
          <div className="flex-1">
            <h3 className="text-sm font-medium text-red-800">Component Error</h3>
            <p className="text-sm text-red-700 mt-1">
              This component encountered an error and cannot be displayed.
            </p>
            <details className="mt-2">
              <summary className="text-xs text-red-600 cursor-pointer">Error Details</summary>
              <div className="text-xs text-red-600 mt-1 font-mono bg-red-100 p-2 rounded">
                <div>ID: {errorId}</div>
                <div>Message: {error.message}</div>
              </div>
            </details>
            <Button
              onClick={onRetry}
              variant="outline"
              size="sm"
              className="mt-3 border-red-300 text-red-700 hover:bg-red-100"
            >
              <RefreshCw className="mr-1 h-3 w-3" />
              Retry
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// Critical Error Component
interface CriticalErrorProps {
  error: Error;
  errorId: string;
  onRefresh: () => void;
}

function CriticalError({ error, errorId, onRefresh }: CriticalErrorProps) {
  return (
    <div className="fixed inset-0 bg-red-50 flex items-center justify-center p-4 z-50">
      <Card className="w-full max-w-lg border-red-300">
        <CardHeader className="text-center bg-red-100">
          <div className="mx-auto mb-4 p-3 bg-red-200 rounded-full w-fit">
            <AlertTriangle className="h-10 w-10 text-red-700" />
          </div>
          <CardTitle className="text-xl text-red-900">Critical Error</CardTitle>
        </CardHeader>
        <CardContent className="p-6 space-y-4">
          <p className="text-red-800 text-center">
            A critical error has occurred that prevents the application from working properly.
            Please refresh the page or contact support.
          </p>

          <div className="text-xs text-red-600 bg-red-50 p-3 rounded border border-red-200">
            <div className="font-semibold mb-1">Error Information:</div>
            <div>ID: {errorId}</div>
            <div>Message: {error.message}</div>
            <div>Time: {new Date().toLocaleString()}</div>
          </div>

          <Button
            onClick={onRefresh}
            variant="default"
            className="w-full bg-red-600 hover:bg-red-700"
          >
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh Application
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}

// Hook for programmatic error boundary usage
export function useErrorHandler() {
  return (error: Error, errorInfo?: any) => {
    // Log error
    console.error('Manual error report:', { error, errorInfo });

    // In production, send to monitoring service
    // Example: Sentry.captureException(error, { extra: errorInfo });

    // Optionally re-throw to trigger error boundary
    throw error;
  };
}

// Higher-order component for wrapping components with error boundary
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryConfig?: Omit<ErrorBoundaryProps, 'children'>
) {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary {...errorBoundaryConfig}>
      <Component {...props} />
    </ErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;

  return WrappedComponent;
}

// Specific Error Boundary for Calculator Components
export const CalculatorErrorBoundary = ({ children }: { children: ReactNode }) => (
  <ErrorBoundary
    level="component"
    onError={(error, errorInfo) => {
      // Custom calculator error handling
      console.error('Calculator component error:', { error, errorInfo });
    }}
    fallback={(error, errorInfo, retry) => (
      <Card className="border-orange-200 bg-orange-50">
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            <AlertTriangle className="h-5 w-5 text-orange-600 mt-0.5" />
            <div className="flex-1">
              <h3 className="text-sm font-medium text-orange-800">Calculator Error</h3>
              <p className="text-sm text-orange-700 mt-1">
                The calculator encountered an issue. Please check your inputs and try again.
              </p>
              <Button
                onClick={retry}
                variant="outline"
                size="sm"
                className="mt-3 border-orange-300 text-orange-700 hover:bg-orange-100"
              >
                <RefreshCw className="mr-1 h-3 w-3" />
                Retry Calculation
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    )}
  >
    {children}
  </ErrorBoundary>
);