'use client';

import React, { useState, useCallback, useMemo, memo, useRef, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { AnimatedButton } from '@/components/ui/animated-button';
import { LoadingCard, ErrorState, LoadingSpinner } from '@/components/ui/loading-states';
import { CalculatorErrorBoundary } from '@/components/ui/error-boundary';
import { StatusBadge, StepIndicator } from '@/components/ui/status-indicators';
import { MobileInput } from '@/components/ui/mobile-input';
import { QualityTierSelector } from '@/components/ui/swipeable-cards';
import { MobileResultsDisplay } from '@/components/calculator/MobileResultsDisplay';
import { RefreshableContainer } from '@/components/ui/pull-to-refresh';
import { ProfilerWrapper, usePerformanceMonitoring, usePerformanceBudget } from '@/lib/performance/react-profiler';
import { isMobileViewport } from '@/lib/mobile';
import { debounce } from '@/lib/utils';
import { calculateConstruction } from '@/core/calculator/engine';
import type { CalculationInput, CalculationResult } from '@/core/calculator/types';
import type { QualityTier } from '@/types/materials';

interface CalculationInputs {
  plotSize: string;
  floors: string;
  quality: QualityTier;
  location: string;
  buildingType: string;
}

interface ValidationErrors {
  [key: string]: string;
}

// Memoized components for performance
const MemoizedInput = memo(({ 
  label, 
  id, 
  type = 'text', 
  placeholder, 
  value, 
  onChange, 
  error, 
  disabled = false 
}: {
  label: string;
  id: string;
  type?: string;
  placeholder: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  error?: string;
  disabled?: boolean;
}) => (
  <div className="space-y-2">
    <Label htmlFor={id}>{label}</Label>
    <Input
      id={id}
      type={type}
      placeholder={placeholder}
      value={value}
      onChange={onChange}
      disabled={disabled}
      className={error ? 'border-red-500 focus:border-red-500' : ''}
    />
    {error && (
      <p className="text-sm text-red-600">{error}</p>
    )}
  </div>
));

const MemoizedSelect = memo(({ 
  label, 
  value, 
  onValueChange, 
  error, 
  disabled = false,
  children 
}: {
  label: string;
  value: string;
  onValueChange: (value: string) => void;
  error?: string;
  disabled?: boolean;
  children: React.ReactNode;
}) => (
  <div className="space-y-2">
    <Label>{label}</Label>
    <Select
      value={value}
      onValueChange={onValueChange}
      disabled={disabled}
    >
      <SelectTrigger className={error ? 'border-red-500 focus:border-red-500' : ''}>
        <SelectValue placeholder={`Select ${label.toLowerCase()}`} />
      </SelectTrigger>
      <SelectContent>
        {children}
      </SelectContent>
    </Select>
    {error && (
      <p className="text-sm text-red-600">{error}</p>
    )}
  </div>
));

const MemoizedResultsDisplay = memo(({ 
  result, 
  inputs, 
  formatCurrency 
}: {
  result: CalculationResult;
  inputs: CalculationInputs;
  formatCurrency: (amount: number) => string;
}) => (
  <Card>
    <CardHeader>
      <CardTitle>Cost Estimation Results</CardTitle>
    </CardHeader>
    <CardContent>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {/* Summary */}
        <div className="space-y-4">
          <div className="text-center p-6 bg-blue-50 rounded-lg">
            <h3 className="text-lg font-semibold text-gray-800 mb-2">
              Total Estimated Cost
            </h3>
            <p className="text-3xl font-bold text-blue-600">
              {formatCurrency(result.totalCost)}
            </p>
            <p className="text-sm text-gray-600 mt-2">
              {formatCurrency(result.costPerSqft)} per sq ft
            </p>
          </div>

          <div className="space-y-2">
            <p className="text-sm text-gray-600">
              • Built-up area: {Math.round(parseInt(inputs.plotSize) * 0.6 * parseInt(inputs.floors))} sq ft
            </p>
            <p className="text-sm text-gray-600">
              • Quality tier: {inputs.quality.charAt(0).toUpperCase() + inputs.quality.slice(1)}
            </p>
            <p className="text-sm text-gray-600">
              • Location: {inputs.location.charAt(0).toUpperCase() + inputs.location.slice(1)}
            </p>
          </div>
        </div>

        {/* Breakdown */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-800">Cost Breakdown</h3>
          <div className="space-y-3">
            <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
              <span className="font-medium">Structure & Foundation</span>
              <span className="font-semibold">
                {formatCurrency(result.breakdown.structure.cost)}
              </span>
            </div>
            <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
              <span className="font-medium">Finishing & Interiors</span>
              <span className="font-semibold">
                {formatCurrency(result.breakdown.finishing.cost)}
              </span>
            </div>
            <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
              <span className="font-medium">MEP Systems</span>
              <span className="font-semibold">
                {formatCurrency(result.breakdown.mep.cost)}
              </span>
            </div>
            <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
              <span className="font-medium">Professional Fees & Others</span>
              <span className="font-semibold">
                {formatCurrency(result.breakdown.other.cost)}
              </span>
            </div>
          </div>
        </div>
      </div>

      <div className="mt-6 p-4 bg-yellow-50 rounded-lg">
        <p className="text-sm text-yellow-800">
          <strong>Note:</strong> These are preliminary estimates based on
          current market rates. Actual costs may vary based on specific
          requirements, material choices, and market conditions. For
          detailed estimates, please consult with our professional team.
        </p>
      </div>
    </CardContent>
  </Card>
));

export const OptimizedCalculatorContainer = memo(() => {
  const [inputs, setInputs] = useState<CalculationInputs>({
    plotSize: '',
    floors: '1',
    quality: 'smart',
    location: 'delhi',
    buildingType: 'residential',
  });

  const [result, setResult] = useState<CalculationResult | null>(null);
  const [isCalculating, setIsCalculating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<ValidationErrors>({});
  const [showMobileResults, setShowMobileResults] = useState(false);

  // Performance monitoring
  const { metrics, profile } = usePerformanceMonitoring('OptimizedCalculatorContainer');
  const renderCountRef = useRef(0);
  const lastRenderTimeRef = useRef(Date.now());
  const memoryUsageRef = useRef<number[]>([]);

  // Set performance budget
  usePerformanceBudget('OptimizedCalculatorContainer', {
    maxRenderTime: 50, // 50ms for calculator
    maxMemoryUsage: 100 * 1024 * 1024, // 100MB
    maxReRenders: 5,
    maxChildren: 50,
  });

  // Performance monitoring effect
  useEffect(() => {
    renderCountRef.current++;
    const now = Date.now();
    const timeSinceLastRender = now - lastRenderTimeRef.current;
    lastRenderTimeRef.current = now;
    
    // Track memory usage
    if (typeof window !== 'undefined' && (window as any).performance?.memory) {
      const memoryInfo = (window as any).performance.memory;
      memoryUsageRef.current.push(memoryInfo.usedJSHeapSize);
      
      // Keep only last 10 samples
      if (memoryUsageRef.current.length > 10) {
        memoryUsageRef.current.shift();
      }
    }
    
    if (process.env.NODE_ENV === 'development') {
      console.log(`Calculator render #${renderCountRef.current}, time since last: ${timeSinceLastRender}ms`);
      
      // Log performance warnings
      if (timeSinceLastRender > 50) {
        console.warn(`Slow render detected: ${timeSinceLastRender}ms`);
      }
      
      if (renderCountRef.current > 10) {
        console.warn(`High render count: ${renderCountRef.current}`);
      }
    }
  });

  // Memory leak detection
  useEffect(() => {
    const interval = setInterval(() => {
      if (memoryUsageRef.current.length >= 5) {
        const recent = memoryUsageRef.current.slice(-5);
        const growth = recent[recent.length - 1] - recent[0];
        if (growth > 10 * 1024 * 1024) { // 10MB growth
          console.warn('Potential memory leak detected in Calculator');
        }
      }
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  // Memoized values
  const isMobile = useMemo(() => isMobileViewport(), []);
  
  const qualityTiers = useMemo(() => [
    {
      id: 'smart',
      title: 'Smart Choice',
      price: '₹1,600-2,000/sqft',
      value: 'smart',
      features: [
        'M20 concrete grade',
        'Standard finishes',
        'Cera/Parryware fixtures',
        'Basic electrical fittings',
        'Standard tiles & paint'
      ],
    },
    {
      id: 'premium',
      title: 'Premium Selection',
      price: '₹2,200-2,800/sqft',
      value: 'premium',
      popular: true,
      features: [
        'M25 concrete grade',
        'Branded materials',
        'Kohler/Grohe fixtures',
        'Premium electrical fittings',
        'Designer tiles & textures'
      ],
    },
    {
      id: 'luxury',
      title: 'Luxury Collection',
      price: '₹3,000+/sqft',
      value: 'luxury',
      features: [
        'M30+ concrete grade',
        'International brands',
        'Imported fixtures',
        'Home automation ready',
        'Premium finishes & materials'
      ],
    },
  ], []);

  const formatCurrency = useCallback((amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0,
    }).format(amount);
  }, []);

  // Debounced validation
  const debouncedValidation = useMemo(
    () => debounce((inputs: CalculationInputs) => {
      const errors: ValidationErrors = {};
      
      if (!inputs.plotSize || parseFloat(inputs.plotSize) <= 0) {
        errors.plotSize = 'Please enter a valid plot size';
      }
      
      if (!inputs.floors || parseInt(inputs.floors) <= 0) {
        errors.floors = 'Please enter a valid number of floors';
      }
      
      if (!inputs.quality) {
        errors.quality = 'Please select a quality tier';
      }
      
      if (!inputs.location) {
        errors.location = 'Please select a location';
      }
      
      setValidationErrors(errors);
    }, 300),
    []
  );

  // Optimized input handlers
  const handleInputChange = useCallback((field: keyof CalculationInputs, value: string) => {
    setInputs(prev => {
      const newInputs = { ...prev, [field]: value };
      debouncedValidation(newInputs);
      return newInputs;
    });
    
    // Clear validation error when user starts typing
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  }, [validationErrors, debouncedValidation]);

  const validateInputs = useCallback((): boolean => {
    const errors: ValidationErrors = {};
    
    if (!inputs.plotSize || parseFloat(inputs.plotSize) <= 0) {
      errors.plotSize = 'Please enter a valid plot size';
    }
    
    if (!inputs.floors || parseInt(inputs.floors) <= 0) {
      errors.floors = 'Please enter a valid number of floors';
    }
    
    if (!inputs.quality) {
      errors.quality = 'Please select a quality tier';
    }
    
    if (!inputs.location) {
      errors.location = 'Please select a location';
    }
    
    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  }, [inputs]);

  const resetCalculation = useCallback(() => {
    setResult(null);
    setError(null);
    setValidationErrors({});
    setShowMobileResults(false);
  }, []);

  const handleCalculate = useCallback(async () => {
    if (!validateInputs()) {
      return;
    }

    setIsCalculating(true);
    setError(null);
    setResult(null);

    try {
      const calculationInput: CalculationInput = {
        squareFeet: parseInt(inputs.plotSize),
        location: inputs.location,
        qualityTier: inputs.quality,
        floors: parseInt(inputs.floors),
        constructionType: inputs.buildingType === 'residential' ? 'independent_house' : 'commercial',
      };

      const calculationResult = await calculateConstruction(calculationInput);
      setResult(calculationResult);
      
      // Show mobile results if on mobile
      if (isMobile) {
        setShowMobileResults(true);
      }
    } catch (error) {
      console.error('Error calculating costs:', error);
      setError(error instanceof Error ? error.message : 'An unexpected error occurred');
    } finally {
      setIsCalculating(false);
    }
  }, [inputs, validateInputs, isMobile]);

  const handleRefresh = useCallback(async () => {
    resetCalculation();
    await new Promise(resolve => setTimeout(resolve, 500));
  }, [resetCalculation]);

  // Memoized handlers for form inputs
  const handlePlotSizeChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    handleInputChange('plotSize', e.target.value);
  }, [handleInputChange]);

  const handleFloorsChange = useCallback((value: string) => {
    handleInputChange('floors', value);
  }, [handleInputChange]);

  const handleQualityChange = useCallback((value: string) => {
    handleInputChange('quality', value as QualityTier);
  }, [handleInputChange]);

  const handleLocationChange = useCallback((value: string) => {
    handleInputChange('location', value);
  }, [handleInputChange]);

  const handleBuildingTypeChange = useCallback((value: string) => {
    handleInputChange('buildingType', value);
  }, [handleInputChange]);

  const handleMobileResultsClose = useCallback(() => {
    setShowMobileResults(false);
  }, []);

  return (
    <ProfilerWrapper id="OptimizedCalculatorContainer">
      <CalculatorErrorBoundary>
        <RefreshableContainer onRefresh={handleRefresh}>
          <div className="max-w-6xl mx-auto space-y-8">
          {/* Input Form */}
          <Card>
            <CardHeader>
              <CardTitle>Project Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <MemoizedInput
                  label="Plot Size (sq ft)"
                  id="plotSize"
                  type="number"
                  placeholder="Enter plot size"
                  value={inputs.plotSize}
                  onChange={handlePlotSizeChange}
                  error={validationErrors.plotSize}
                  disabled={isCalculating}
                />

                <MemoizedSelect
                  label="Number of Floors"
                  value={inputs.floors}
                  onValueChange={handleFloorsChange}
                  error={validationErrors.floors}
                  disabled={isCalculating}
                >
                  <SelectItem value="1">1 Floor</SelectItem>
                  <SelectItem value="2">2 Floors</SelectItem>
                  <SelectItem value="3">3 Floors</SelectItem>
                  <SelectItem value="4">4 Floors</SelectItem>
                </MemoizedSelect>

                <div className="space-y-2 col-span-full">
                  <Label>Quality Tier</Label>
                  {isMobile ? (
                    <QualityTierSelector
                      tiers={qualityTiers}
                      selectedTier={inputs.quality}
                      onTierSelect={handleQualityChange}
                    />
                  ) : (
                    <MemoizedSelect
                      label="Quality Tier"
                      value={inputs.quality}
                      onValueChange={handleQualityChange}
                      error={validationErrors.quality}
                      disabled={isCalculating}
                    >
                      <SelectItem value="smart">Smart Choice (₹1,600-2,000/sqft)</SelectItem>
                      <SelectItem value="premium">Premium Selection (₹2,200-2,800/sqft)</SelectItem>
                      <SelectItem value="luxury">Luxury Collection (₹3,000+/sqft)</SelectItem>
                    </MemoizedSelect>
                  )}
                  {validationErrors.quality && (
                    <p className="text-sm text-red-600">{validationErrors.quality}</p>
                  )}
                </div>

                <MemoizedSelect
                  label="Location"
                  value={inputs.location}
                  onValueChange={handleLocationChange}
                  error={validationErrors.location}
                  disabled={isCalculating}
                >
                  <SelectItem value="delhi">Delhi NCR</SelectItem>
                  <SelectItem value="mumbai">Mumbai</SelectItem>
                  <SelectItem value="bangalore">Bangalore</SelectItem>
                  <SelectItem value="chennai">Chennai</SelectItem>
                  <SelectItem value="hyderabad">Hyderabad</SelectItem>
                  <SelectItem value="pune">Pune</SelectItem>
                </MemoizedSelect>

                <MemoizedSelect
                  label="Building Type"
                  value={inputs.buildingType}
                  onValueChange={handleBuildingTypeChange}
                  disabled={isCalculating}
                >
                  <SelectItem value="residential">Residential</SelectItem>
                  <SelectItem value="commercial">Commercial</SelectItem>
                  <SelectItem value="mixed">Mixed Use</SelectItem>
                </MemoizedSelect>
              </div>

              <div className="flex justify-center gap-4">
                <AnimatedButton
                  onClick={handleCalculate}
                  disabled={!inputs.plotSize || isCalculating}
                  loading={isCalculating}
                  loadingText="Calculating..."
                  className="px-8 py-2"
                  pulse={!isCalculating}
                >
                  Calculate Construction Cost
                </AnimatedButton>
                
                {result && (
                  <AnimatedButton
                    onClick={resetCalculation}
                    variant="outline"
                    className="px-6 py-2"
                    disabled={isCalculating}
                  >
                    Reset
                  </AnimatedButton>
                )}
              </div>
              
              {/* Display general error */}
              {error && (
                <div className="mt-4">
                  <StatusBadge status="error" text={error} />
                </div>
              )}
            </CardContent>
          </Card>

          {/* Loading State */}
          {isCalculating && (
            <LoadingCard 
              title="Calculating Construction Cost"
              description="Please wait while we process your project details and generate accurate cost estimates."
            />
          )}

          {/* Results */}
          {result && !isCalculating && (
            <MemoizedResultsDisplay 
              result={result} 
              inputs={inputs} 
              formatCurrency={formatCurrency} 
            />
          )}

          {/* Mobile Results Display */}
          {result && (
            <MobileResultsDisplay
              result={result}
              isOpen={showMobileResults}
              onClose={handleMobileResultsClose}
            />
          )}
          </div>
        </RefreshableContainer>
      </CalculatorErrorBoundary>
    </ProfilerWrapper>
  );
});

OptimizedCalculatorContainer.displayName = 'OptimizedCalculatorContainer';