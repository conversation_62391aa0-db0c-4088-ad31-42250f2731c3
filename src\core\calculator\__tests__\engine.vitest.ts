import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { calculateConstruction, validateInput } from '../engine';
import { createMockCalculationInput, createMockMaterialDatabase } from '../../../test/factories';
import type { CalculationInput, QualityTier } from '../types';

// Mock material loader
vi.mock('../../../lib/materials/loader', () => ({
  loadMaterials: vi.fn().mockResolvedValue(createMockMaterialDatabase()),
  getMaterialPrice: vi.fn().mockReturnValue(450),
  getMaterialConsumption: vi.fn().mockReturnValue(6.5),
}));

describe('Calculator Engine', () => {
  describe('calculateConstruction', () => {
    it('should calculate construction cost correctly for basic input', async () => {
      const input = createMockCalculationInput({
        squareFeet: 1000,
        location: 'bangalore',
        qualityTier: 'smart',
        floors: 1,
      });

      const result = await calculateConstruction(input);

      expect(result).toMatchObject({
        totalCost: expect.any(Number),
        costPerSqft: expect.any(Number),
        breakdown: {
          structure: expect.objectContaining({
            cost: expect.any(Number),
            percentage: 35,
          }),
          finishing: expect.objectContaining({
            cost: expect.any(Number),
            percentage: 30,
          }),
          mep: expect.objectContaining({
            cost: expect.any(Number),
            percentage: 20,
          }),
          external: expect.objectContaining({
            cost: expect.any(Number),
            percentage: 10,
          }),
          other: expect.objectContaining({
            cost: expect.any(Number),
            percentage: 5,
          }),
        },
        metadata: expect.objectContaining({
          location: 'bangalore',
          qualityTier: 'smart',
          calculatedAt: expect.any(String),
        }),
      });
    });

    it('should calculate different costs for different quality tiers', async () => {
      const baseInput = createMockCalculationInput({
        squareFeet: 1000,
        location: 'bangalore',
        floors: 1,
      });

      const smartResult = await calculateConstruction({
        ...baseInput,
        qualityTier: 'smart',
      });

      const premiumResult = await calculateConstruction({
        ...baseInput,
        qualityTier: 'premium',
      });

      const luxuryResult = await calculateConstruction({
        ...baseInput,
        qualityTier: 'luxury',
      });

      expect(smartResult.totalCost).toBeLessThan(premiumResult.totalCost);
      expect(premiumResult.totalCost).toBeLessThan(luxuryResult.totalCost);
      expect(luxuryResult.costPerSqft).toBeGreaterThan(smartResult.costPerSqft);
    });

    it('should adjust costs based on location multiplier', async () => {
      const baseInput = createMockCalculationInput({
        squareFeet: 1000,
        qualityTier: 'smart',
        floors: 1,
      });

      const bangaloreResult = await calculateConstruction({
        ...baseInput,
        location: 'bangalore',
      });

      const mumbaiResult = await calculateConstruction({
        ...baseInput,
        location: 'mumbai',
      });

      // Mumbai has 1.2x multiplier vs Bangalore 1.0x
      expect(mumbaiResult.totalCost).toBeGreaterThan(bangaloreResult.totalCost);
      expect(mumbaiResult.costPerSqft).toBeGreaterThan(bangaloreResult.costPerSqft);
    });

    it('should scale costs proportionally with square footage', async () => {
      const baseInput = createMockCalculationInput({
        location: 'bangalore',
        qualityTier: 'smart',
        floors: 1,
      });

      const result1000 = await calculateConstruction({
        ...baseInput,
        squareFeet: 1000,
      });

      const result2000 = await calculateConstruction({
        ...baseInput,
        squareFeet: 2000,
      });

      // Cost should roughly double (allowing for some variance in calculations)
      expect(result2000.totalCost).toBeGreaterThan(result1000.totalCost * 1.8);
      expect(result2000.totalCost).toBeLessThan(result1000.totalCost * 2.2);
      
      // Cost per sqft should remain similar
      expect(Math.abs(result2000.costPerSqft - result1000.costPerSqft)).toBeLessThan(100);
    });

    it('should handle multiple floors correctly', async () => {
      const baseInput = createMockCalculationInput({
        squareFeet: 1000,
        location: 'bangalore',
        qualityTier: 'smart',
      });

      const singleFloorResult = await calculateConstruction({
        ...baseInput,
        floors: 1,
      });

      const doubleFloorResult = await calculateConstruction({
        ...baseInput,
        floors: 2,
      });

      // Double floor should cost significantly more
      expect(doubleFloorResult.totalCost).toBeGreaterThan(singleFloorResult.totalCost * 1.5);
    });

    it('should return consistent results for identical inputs', async () => {
      const input = createMockCalculationInput({
        squareFeet: 1200,
        location: 'bangalore',
        qualityTier: 'premium',
        floors: 2,
      });

      const result1 = await calculateConstruction(input);
      const result2 = await calculateConstruction(input);

      expect(result1.totalCost).toBe(result2.totalCost);
      expect(result1.costPerSqft).toBe(result2.costPerSqft);
      expect(result1.breakdown.structure.cost).toBe(result2.breakdown.structure.cost);
    });

    it('should include all required breakdown components', async () => {
      const input = createMockCalculationInput();
      const result = await calculateConstruction(input);

      const breakdown = result.breakdown;
      
      expect(breakdown.structure).toBeDefined();
      expect(breakdown.finishing).toBeDefined();
      expect(breakdown.mep).toBeDefined();
      expect(breakdown.external).toBeDefined();
      expect(breakdown.other).toBeDefined();

      // Check percentages add up to 100%
      const totalPercentage = 
        breakdown.structure.percentage +
        breakdown.finishing.percentage +
        breakdown.mep.percentage +
        breakdown.external.percentage +
        breakdown.other.percentage;
      
      expect(totalPercentage).toBe(100);
    });

    it('should handle edge cases and boundary values', async () => {
      // Very small area
      const smallAreaResult = await calculateConstruction({
        squareFeet: 100,
        location: 'bangalore',
        qualityTier: 'smart',
        floors: 1,
      });
      
      expect(smallAreaResult.totalCost).toBeGreaterThan(0);
      expect(smallAreaResult.costPerSqft).toBeGreaterThan(0);

      // Very large area
      const largeAreaResult = await calculateConstruction({
        squareFeet: 10000,
        location: 'bangalore',
        qualityTier: 'smart',
        floors: 1,
      });
      
      expect(largeAreaResult.totalCost).toBeGreaterThan(smallAreaResult.totalCost);
    });

    it('should generate proper metadata', async () => {
      const input = createMockCalculationInput({
        squareFeet: 1000,
        location: 'mumbai',
        qualityTier: 'luxury',
      });

      const result = await calculateConstruction(input);

      expect(result.metadata).toMatchObject({
        location: 'mumbai',
        qualityTier: 'luxury',
        calculatedAt: expect.any(String),
        version: expect.any(String),
      });

      // Check that calculatedAt is a valid ISO date
      expect(new Date(result.metadata.calculatedAt).getTime()).not.toBeNaN();
    });
  });

  describe('validateInput', () => {
    it('should validate correct input', () => {
      const validInput = createMockCalculationInput({
        squareFeet: 1200,
        location: 'bangalore',
        qualityTier: 'smart',
        floors: 2,
      });

      const result = validateInput(validInput);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject invalid square footage', () => {
      const invalidInput = createMockCalculationInput({
        squareFeet: 0,
      });

      const result = validateInput(invalidInput);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Square footage must be greater than 0');
    });

    it('should reject negative square footage', () => {
      const invalidInput = createMockCalculationInput({
        squareFeet: -100,
      });

      const result = validateInput(invalidInput);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Square footage must be greater than 0');
    });

    it('should reject unrealistic square footage', () => {
      const invalidInput = createMockCalculationInput({
        squareFeet: 1000000,
      });

      const result = validateInput(invalidInput);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Square footage cannot exceed 100,000');
    });

    it('should reject invalid location', () => {
      const invalidInput = createMockCalculationInput({
        location: 'invalid_location',
      });

      const result = validateInput(invalidInput);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Invalid location');
    });

    it('should reject invalid quality tier', () => {
      const invalidInput = createMockCalculationInput({
        qualityTier: 'invalid_tier' as QualityTier,
      });

      const result = validateInput(invalidInput);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Invalid quality tier');
    });

    it('should reject invalid floors', () => {
      const invalidInput = createMockCalculationInput({
        floors: 0,
      });

      const result = validateInput(invalidInput);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Number of floors must be between 1 and 10');
    });

    it('should reject too many floors', () => {
      const invalidInput = createMockCalculationInput({
        floors: 15,
      });

      const result = validateInput(invalidInput);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Number of floors must be between 1 and 10');
    });

    it('should handle multiple validation errors', () => {
      const invalidInput = createMockCalculationInput({
        squareFeet: -100,
        location: 'invalid_location',
        qualityTier: 'invalid_tier' as QualityTier,
        floors: 0,
      });

      const result = validateInput(invalidInput);
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(1);
      expect(result.errors).toContain('Square footage must be greater than 0');
      expect(result.errors).toContain('Invalid location');
      expect(result.errors).toContain('Invalid quality tier');
      expect(result.errors).toContain('Number of floors must be between 1 and 10');
    });

    it('should validate construction type if provided', () => {
      const validInput = createMockCalculationInput({
        constructionType: 'independent_house',
      });

      const result = validateInput(validInput);
      expect(result.isValid).toBe(true);

      const invalidInput = createMockCalculationInput({
        constructionType: 'invalid_type' as any,
      });

      const invalidResult = validateInput(invalidInput);
      expect(invalidResult.isValid).toBe(false);
      expect(invalidResult.errors).toContain('Invalid construction type');
    });
  });

  describe('Performance Tests', () => {
    it('should calculate results within acceptable time limits', async () => {
      const input = createMockCalculationInput();
      
      const start = performance.now();
      await calculateConstruction(input);
      const end = performance.now();
      
      // Should complete within 100ms
      expect(end - start).toBeLessThan(100);
    });

    it('should handle multiple concurrent calculations', async () => {
      const inputs = Array.from({ length: 10 }, () => createMockCalculationInput({
        squareFeet: Math.floor(Math.random() * 2000) + 500,
        location: ['bangalore', 'mumbai', 'delhi'][Math.floor(Math.random() * 3)],
        qualityTier: ['smart', 'premium', 'luxury'][Math.floor(Math.random() * 3)] as QualityTier,
      }));

      const start = performance.now();
      const results = await Promise.all(inputs.map(calculateConstruction));
      const end = performance.now();

      expect(results).toHaveLength(10);
      expect(results.every(r => r.totalCost > 0)).toBe(true);
      
      // All 10 calculations should complete within 1 second
      expect(end - start).toBeLessThan(1000);
    });

    it('should maintain performance with large square footage', async () => {
      const input = createMockCalculationInput({
        squareFeet: 50000,
      });

      const start = performance.now();
      const result = await calculateConstruction(input);
      const end = performance.now();

      expect(result.totalCost).toBeGreaterThan(0);
      expect(end - start).toBeLessThan(200);
    });
  });

  describe('Error Handling', () => {
    it('should handle missing material data gracefully', async () => {
      // Mock loader to return empty data
      vi.doMock('../../../lib/materials/loader', () => ({
        loadMaterials: vi.fn().mockResolvedValue({ materials: [], locations: [] }),
      }));

      const input = createMockCalculationInput();
      
      await expect(calculateConstruction(input)).rejects.toThrow();
    });

    it('should handle network errors gracefully', async () => {
      // Mock loader to throw network error
      vi.doMock('../../../lib/materials/loader', () => ({
        loadMaterials: vi.fn().mockRejectedValue(new Error('Network error')),
      }));

      const input = createMockCalculationInput();
      
      await expect(calculateConstruction(input)).rejects.toThrow('Network error');
    });

    it('should handle invalid material data gracefully', async () => {
      // Mock loader to return invalid data
      vi.doMock('../../../lib/materials/loader', () => ({
        loadMaterials: vi.fn().mockResolvedValue({
          materials: [{ id: 'invalid' }], // Missing required fields
          locations: [],
        }),
      }));

      const input = createMockCalculationInput();
      
      await expect(calculateConstruction(input)).rejects.toThrow();
    });
  });
});