import { test, expect } from '@playwright/test';

test.describe('Construction Calculator E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/calculator');
  });

  test.describe('Calculator Form Flow', () => {
    test('should complete full calculator journey', async ({ page }) => {
      // Check form title is visible
      await expect(page.getByTestId('form-title')).toBeVisible();

      // Fill plot size
      await page.fill('[data-testid="plot-size-input"]', '1200');

      // Select floors
      await page.click('[data-testid="floors-select"]');
      await page.locator('text="2 Floors"').click();

      // Select quality tier
      await page.click('[data-testid="quality-select"]');
      await page.locator('text="Premium Selection (₹2,500/sqft)"').click();

      // Select location
      await page.click('[data-testid="location-select"]');
      await page.locator('text="Bangalore"').click();

      // Select building type
      await page.click('[data-testid="building-type-select"]');
      await page.locator('text="Residential"').first().click();

      // Submit calculation
      const calculateButton = page.getByTestId('calculate-button');
      await expect(calculateButton).toBeEnabled();
      await calculateButton.click();

      // Wait for results
      await expect(page.getByTestId('results-container')).toBeVisible({ timeout: 10000 });

      // Verify results display
      await expect(page.getByTestId('total-cost')).toBeVisible();
      await expect(page.getByTestId('cost-per-sqft')).toBeVisible();
      
      // Check cost breakdown sections
      await expect(page.getByTestId('breakdown-structure')).toBeVisible();
      await expect(page.getByTestId('breakdown-finishing')).toBeVisible();
      await expect(page.getByTestId('breakdown-mep')).toBeVisible();
    });

    test('should validate form fields correctly', async ({ page }) => {
      // Try to calculate without filling required fields
      const calculateButton = page.getByTestId('calculate-button');
      await expect(calculateButton).toBeDisabled();

      // Fill invalid plot size (below minimum)
      await page.fill('[data-testid="plot-size-input"]', '100');
      await calculateButton.click();
      
      // Should show validation error
      await expect(page.getByTestId('validation-error')).toBeVisible();
      await expect(page.getByText(/at least 500/i)).toBeVisible();

      // Fix validation with valid plot size
      await page.fill('[data-testid="plot-size-input"]', '1200');
      
      await expect(calculateButton).toBeEnabled();
    });

    test('should maintain form data when resetting', async ({ page }) => {
      // Fill form
      await page.fill('[data-testid="plot-size-input"]', '1000');
      
      await page.click('[data-testid="floors-select"]');
      await page.click('text="3 Floors"');
      
      await page.click('[data-testid="quality-select"]');
      await page.click('[data-testid="quality-luxury"]');

      // Reset form
      await page.getByTestId('reset-button').click();

      // Verify form is reset to defaults
      await expect(page.locator('[data-testid="plot-size-input"]')).toHaveValue('');
      
      // Check default values are set
      await expect(page.getByTestId('floors-select')).toContainText('1 Floor');
      await expect(page.getByTestId('quality-select')).toContainText('Smart Choice');
    });
  });

  test.describe('Quality Tier Selection', () => {
    test('should show different quality tiers with descriptions', async ({ page }) => {
      // Check quality tier dropdown
      await page.click('[data-testid="quality-select"]');

      // Check quality tier options
      await expect(page.getByText('Smart Choice')).toBeVisible();
      await expect(page.getByText('Premium Selection')).toBeVisible();
      await expect(page.getByText('Luxury Collection')).toBeVisible();

      // Check price descriptions
      await expect(page.getByText(/₹1,600.*2,000/)).toBeVisible();
      await expect(page.getByText(/₹2,200.*2,800/)).toBeVisible();
      await expect(page.getByText(/₹3,000.*4,000/)).toBeVisible();

      // Select different tiers and verify selection
      await page.locator('text="Smart Choice (₹1,600-2,000/sqft)"').click();
      await expect(page.getByTestId('quality-select')).toContainText('Smart Choice');

      await page.click('[data-testid="quality-select"]');
      await page.locator('text="Luxury Collection (₹3,000-4,000/sqft)"').click();
      await expect(page.getByTestId('quality-select')).toContainText('Luxury Collection');
    });
  });

  test.describe('Results Display', () => {
    const fillCompleteForm = async (page: any) => {
      // Fill plot size
      await page.fill('[data-testid="plot-size-input"]', '1200');
      
      // Select floors
      await page.click('[data-testid="floors-select"]');
      await page.locator('text="2 Floors"').click();
      
      // Select quality tier
      await page.click('[data-testid="quality-select"]');
      await page.locator('text="Premium Selection (₹2,200-2,800/sqft)"').click();
      
      // Select location
      await page.click('[data-testid="location-select"]');
      await page.locator('text="Bangalore"').click();
      
      // Calculate
      await page.getByTestId('calculate-button').click();
    };

    test('should display calculation results with proper formatting', async ({ page }) => {
      await fillCompleteForm(page);

      // Wait for results
      await expect(page.getByTestId('results-container')).toBeVisible({ timeout: 15000 });

      // Check total cost formatting
      const totalCostElement = page.getByTestId('total-cost');
      await expect(totalCostElement).toBeVisible();
      
      const totalCostText = await totalCostElement.textContent();
      expect(totalCostText).toMatch(/₹.*[0-9,]+/);

      // Check cost per sq ft
      const costPerSqFtElement = page.getByTestId('cost-per-sqft');
      await expect(costPerSqFtElement).toBeVisible();
      
      const costPerSqFtText = await costPerSqFtElement.textContent();
      expect(costPerSqFtText).toMatch(/₹.*[0-9,]+.*sqft/);

      // Check timeline section exists
      await expect(page.getByTestId('timeline')).toBeVisible();
    });

    test('should show cost breakdown', async ({ page }) => {
      await fillCompleteForm(page);

      // Wait for results
      await expect(page.getByTestId('results-container')).toBeVisible({ timeout: 15000 });

      // Check breakdown sections
      await expect(page.getByTestId('breakdown-structure')).toBeVisible();
      await expect(page.getByTestId('breakdown-finishing')).toBeVisible();
      await expect(page.getByTestId('breakdown-mep')).toBeVisible();
      await expect(page.getByTestId('breakdown-external')).toBeVisible();
      await expect(page.getByTestId('breakdown-other')).toBeVisible();

      // Check percentage display
      await expect(page.getByText(/35%/)).toBeVisible();
      await expect(page.getByText(/30%/)).toBeVisible();
      await expect(page.getByText(/20%/)).toBeVisible();
      await expect(page.getByText(/10%/)).toBeVisible();
      await expect(page.getByText(/5%/)).toBeVisible();
    });

    test('should show materials list', async ({ page }) => {
      await fillCompleteForm(page);

      // Wait for results
      await expect(page.getByTestId('results-container')).toBeVisible({ timeout: 15000 });

      // Check materials list section
      await expect(page.getByTestId('materials-list')).toBeVisible();
      
      // Check if materials are displayed or placeholder text
      const materialsContent = page.getByTestId('materials-content');
      const materialsEmpty = page.getByTestId('materials-empty');
      
      // Either materials are loaded or placeholder is shown
      const hasContent = await materialsContent.isVisible().catch(() => false);
      const hasPlaceholder = await materialsEmpty.isVisible().catch(() => false);
      
      expect(hasContent || hasPlaceholder).toBeTruthy();
    });

    test('should have working PDF download button', async ({ page }) => {
      await fillCompleteForm(page);

      // Wait for results
      await expect(page.getByTestId('results-container')).toBeVisible({ timeout: 15000 });

      // Check PDF download button exists and is visible
      const pdfButton = page.getByTestId('download-pdf-button');
      await expect(pdfButton).toBeVisible();
      await expect(pdfButton).toBeEnabled();
      
      // Click button (functionality may not be fully implemented yet)
      await pdfButton.click();
    });
  });

  test.describe('Mobile Responsiveness', () => {
    test('should work correctly on mobile devices', async ({ page }) => {
      // Set mobile viewport
      await page.setViewportSize({ width: 375, height: 667 });

      await page.goto('/calculator');

      // Check mobile layout
      await expect(page.getByTestId('form-title')).toBeVisible();

      // Fill form on mobile
      await page.fill('[data-testid="plot-size-input"]', '1000');
      
      await page.click('[data-testid="floors-select"]');
      await page.click('text="2 Floors"');

      // Verify buttons are touch-friendly
      const calculateButton = page.getByTestId('calculate-button');
      const buttonBox = await calculateButton.boundingBox();
      expect(buttonBox?.height).toBeGreaterThanOrEqual(44); // Minimum touch target
      
      await expect(calculateButton).toBeEnabled();

      // Check form is properly laid out on mobile
      await expect(page.getByTestId('calculator-container')).toBeVisible();
    });

    test('should handle touch interactions', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 });
      await page.goto('/calculator');

      // Fill form with touch interactions
      await page.fill('[data-testid="plot-size-input"]', '1000');
      
      // Test touch interaction on selects
      await page.tap('[data-testid="quality-select"]');
      await page.tap('[data-testid="quality-premium"]');
      
      await expect(page.getByTestId('quality-select')).toContainText('Premium Selection');
    });
  });

  test.describe('Performance', () => {
    test('should load calculator page quickly', async ({ page }) => {
      const startTime = Date.now();
      await page.goto('/calculator');
      
      // Wait for form to be visible
      await expect(page.getByTestId('form-title')).toBeVisible();
      
      const loadTime = Date.now() - startTime;
      expect(loadTime).toBeLessThan(3000); // Should load within 3 seconds
    });

    test('should calculate results within acceptable time', async ({ page }) => {
      // Fill complete form
      await page.fill('[data-testid="plot-size-input"]', '1200');
      
      await page.click('[data-testid="floors-select"]');
      await page.click('text="2 Floors"');
      
      await page.click('[data-testid="quality-select"]');
      await page.click('[data-testid="quality-premium"]');
      
      await page.click('[data-testid="location-select"]');
      await page.click('text="Bangalore"');

      // Measure calculation time
      const startTime = Date.now();
      await page.getByTestId('calculate-button').click();
      
      await expect(page.getByTestId('results-container')).toBeVisible({ timeout: 10000 });
      
      const calculationTime = Date.now() - startTime;
      expect(calculationTime).toBeLessThan(5000); // Should calculate within 5 seconds
    });
  });

  test.describe('Error Handling', () => {
    test('should handle API errors gracefully', async ({ page }) => {
      // Mock API error
      await page.route('**/api/calculate', route => {
        route.fulfill({
          status: 500,
          contentType: 'application/json',
          body: JSON.stringify({
            success: false,
            error: 'SERVER_ERROR',
            message: 'Internal server error'
          })
        });
      });

      // Fill and submit form
      await page.fill('[data-testid="plot-size-input"]', '1000');
      
      await page.click('[data-testid="floors-select"]');
      await page.click('text="2 Floors"');
      
      await page.click('[data-testid="quality-select"]');
      await page.click('[data-testid="quality-smart"]');
      
      await page.click('[data-testid="location-select"]');
      await page.click('text="Bangalore"');

      await page.getByTestId('calculate-button').click();

      // Should show error message
      await expect(page.getByTestId('validation-error')).toBeVisible();
    });

    test('should show network error when offline', async ({ page, context }) => {
      // Fill complete form first
      await page.fill('[data-testid="plot-size-input"]', '1000');
      
      await page.click('[data-testid="floors-select"]');
      await page.click('text="2 Floors"');
      
      await page.click('[data-testid="quality-select"]');
      await page.click('[data-testid="quality-smart"]');
      
      await page.click('[data-testid="location-select"]');
      await page.click('text="Bangalore"');

      // Go offline
      await context.setOffline(true);

      await page.getByTestId('calculate-button').click();

      // Should show error message or loading state
      await expect(page.getByTestId('validation-error')).toBeVisible().catch(async () => {
        // Alternative: check for loading state that doesn't complete
        await expect(page.getByTestId('loading-spinner')).toBeVisible();
      });
    });
  });

  test.describe('Accessibility', () => {
    test('should be keyboard navigable', async ({ page }) => {
      await page.goto('/calculator');

      // Tab through form elements
      await page.keyboard.press('Tab');
      await expect(page.getByTestId('plot-size-input')).toBeFocused();

      await page.keyboard.press('Tab');
      await expect(page.getByTestId('floors-select')).toBeFocused();

      await page.keyboard.press('Tab');
      await expect(page.getByTestId('quality-select')).toBeFocused();

      await page.keyboard.press('Tab');
      await expect(page.getByTestId('location-select')).toBeFocused();
    });

    test('should have proper ARIA labels and roles', async ({ page }) => {
      await page.goto('/calculator');

      // Check required fields have proper ARIA labels
      await expect(page.getByTestId('plot-size-input')).toHaveAttribute('aria-label');
      await expect(page.getByTestId('floors-select')).toHaveAttribute('aria-label');
      await expect(page.getByTestId('quality-select')).toHaveAttribute('aria-label');
      await expect(page.getByTestId('location-select')).toHaveAttribute('aria-label');

      // Check headings structure
      await expect(page.getByTestId('form-title')).toBeVisible();
      
      // Check buttons have proper labels
      await expect(page.getByTestId('calculate-button')).toHaveAttribute('aria-label');
      await expect(page.getByTestId('reset-button')).toHaveAttribute('aria-label');
    });

    test('should work with screen reader patterns', async ({ page }) => {
      await page.goto('/calculator');

      // Check form validation when trying to calculate with invalid input
      await page.fill('[data-testid="plot-size-input"]', '100');
      await page.getByTestId('calculate-button').click();
      
      // Error messages should be visible
      await expect(page.getByTestId('validation-error')).toBeVisible();
      
      // Check if input field is labeled properly
      const plotSizeInput = page.getByTestId('plot-size-input');
      const ariaLabel = await plotSizeInput.getAttribute('aria-label');
      expect(ariaLabel).toBeTruthy();
    });
  });
});