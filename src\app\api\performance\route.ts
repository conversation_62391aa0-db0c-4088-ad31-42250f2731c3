import { NextRequest, NextResponse } from 'next/server';
import { withPerformanceMonitoring } from '@/lib/performance/monitoring';

// In-memory storage for development (use database in production)
const metricsStore: any[] = [];
const MAX_METRICS = 1000;

// Performance metrics collection endpoint
export async function POST(request: NextRequest) {
  return withPerformanceMonitoring(async () => {
    try {
      const metrics = await request.json();
      
      // Validate metrics structure
      if (!metrics || typeof metrics !== 'object') {
        return NextResponse.json(
          { error: 'Invalid metrics data' },
          { status: 400 }
        );
      }

      // Store metrics with additional context
      const metricEntry = {
        ...metrics,
        timestamp: Date.now(),
        userAgent: request.headers.get('user-agent'),
        ip: request.headers.get('x-forwarded-for') || 'unknown',
        url: metrics.url || 'unknown',
        id: Date.now().toString(36) + Math.random().toString(36).substr(2),
      };

      metricsStore.push(metricEntry);

      // Keep only recent metrics
      if (metricsStore.length > MAX_METRICS) {
        metricsStore.splice(0, metricsStore.length - MAX_METRICS);
      }

      // Log metrics (in production, send to monitoring service)
      console.log('Performance metrics:', {
        timestamp: new Date().toISOString(),
        userAgent: request.headers.get('user-agent'),
        url: metrics.url || 'unknown',
        metrics,
      });

      // In production, you would send this data to:
      // - DataDog, New Relic, or similar APM
      // - Google Analytics with custom events
      // - Internal analytics database
      if (process.env.NODE_ENV === 'production') {
        await sendToMonitoringService(metricEntry);
      }

      return NextResponse.json(
        { status: 'received', id: metricEntry.id },
        { 
          status: 200,
          headers: {
            'Cache-Control': 'no-cache',
          },
        }
      );
    } catch (error) {
      console.error('Performance metrics error:', error);
      
      return NextResponse.json(
        { error: 'Failed to process metrics' },
        { status: 500 }
      );
    }
  });
}

async function sendToMonitoringService(metric: any) {
  // Implementation for production monitoring service
  console.log('Sending to monitoring service:', metric);
}

// Get performance summary (for admin dashboard)
export async function GET(request: NextRequest) {
  return withPerformanceMonitoring(async () => {
    try {
      const { searchParams } = new URL(request.url);
      const timeRange = searchParams.get('timeRange') || '1h';
      const detailed = searchParams.get('detailed') === 'true';

      // Calculate time range
      const now = Date.now();
      const timeRanges = {
        '1h': now - 3600000,
        '24h': now - 86400000,
        '7d': now - 604800000,
        '30d': now - 2592000000
      };
      const startTime = timeRanges[timeRange as keyof typeof timeRanges] || timeRanges['1h'];

      // Filter metrics by time range
      const recentMetrics = metricsStore.filter(m => m.timestamp >= startTime);

      // Calculate performance summary
      const summary = calculatePerformanceSummary(recentMetrics);

      if (detailed) {
        return NextResponse.json({
          summary,
          metrics: recentMetrics.slice(-100), // Last 100 metrics
          timeRange,
          totalCount: recentMetrics.length,
          timestamp: new Date().toISOString(),
        }, {
          headers: {
            'Cache-Control': 'private, max-age=30',
          },
        });
      }

      return NextResponse.json({
        summary,
        timeRange,
        totalCount: recentMetrics.length,
        timestamp: new Date().toISOString(),
      }, {
        headers: {
          'Cache-Control': 'private, max-age=60',
        },
      });
    } catch (error) {
      console.error('Performance summary error:', error);
      
      return NextResponse.json(
        { error: 'Failed to get performance summary' },
        { status: 500 }
      );
    }
  });
}

function calculatePerformanceSummary(metrics: any[]) {
  if (metrics.length === 0) {
    return {
      avgResponseTime: '0ms',
      calculationsPerMinute: 0,
      errorRate: '0%',
      uptime: '100%',
      totalRequests: 0,
      performanceScore: 0,
      webVitals: {
        lcp: null,
        fid: null,
        cls: null,
        fcp: null,
        ttfb: null,
      },
    };
  }

  // Calculate basic metrics
  const totalRequests = metrics.length;
  const errors = metrics.filter(m => m.statusCode >= 400).length;
  const errorRate = (errors / totalRequests) * 100;

  // Calculate response times
  const responseTimes = metrics
    .filter(m => m.duration)
    .map(m => m.duration);
  
  const avgResponseTime = responseTimes.length > 0 ? 
    responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length : 0;

  // Calculate calculations per minute
  const calculationMetrics = metrics.filter(m => m.metric?.name === 'calculation-time');
  const timeSpanMinutes = (Date.now() - metrics[0]?.timestamp || 0) / 60000;
  const calculationsPerMinute = timeSpanMinutes > 0 ? 
    calculationMetrics.length / timeSpanMinutes : 0;

  // Calculate Web Vitals
  const webVitals = {
    lcp: getLatestMetric(metrics, 'LCP'),
    fid: getLatestMetric(metrics, 'FID'),
    cls: getLatestMetric(metrics, 'CLS'),
    fcp: getLatestMetric(metrics, 'FCP'),
    ttfb: getLatestMetric(metrics, 'TTFB'),
  };

  // Calculate performance score
  const performanceScore = calculateOverallPerformanceScore(webVitals);

  return {
    avgResponseTime: `${Math.round(avgResponseTime)}ms`,
    calculationsPerMinute: Math.round(calculationsPerMinute),
    errorRate: `${errorRate.toFixed(2)}%`,
    uptime: `${Math.max(0, 100 - errorRate).toFixed(1)}%`,
    totalRequests,
    performanceScore,
    webVitals,
  };
}

function getLatestMetric(metrics: any[], metricName: string) {
  const metricData = metrics
    .filter(m => m.metric?.name === metricName)
    .sort((a, b) => b.timestamp - a.timestamp)[0];
  
  return metricData ? {
    value: metricData.metric.value,
    rating: metricData.metric.rating,
    timestamp: metricData.timestamp,
  } : null;
}

function calculateOverallPerformanceScore(webVitals: any) {
  const weights = {
    lcp: 0.25,
    fid: 0.25,
    cls: 0.25,
    fcp: 0.15,
    ttfb: 0.10,
  };

  let totalScore = 0;
  let totalWeight = 0;

  for (const [metric, weight] of Object.entries(weights)) {
    const vital = webVitals[metric];
    if (vital) {
      let score = 0;
      switch (vital.rating) {
        case 'good': score = 100; break;
        case 'needs-improvement': score = 50; break;
        case 'poor': score = 0; break;
      }
      totalScore += score * weight;
      totalWeight += weight;
    }
  }

  return totalWeight > 0 ? Math.round(totalScore / totalWeight) : 0;
}