name: 🧪 Test Suite

on:
  push:
    branches: [ main, master, develop ]
  pull_request:
    branches: [ main, master, develop ]
  schedule:
    # Run tests daily at 2 AM UTC
    - cron: '0 2 * * *'
  workflow_dispatch:

env:
  NODE_VERSION: '18'
  SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
  SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_ANON_KEY }}

jobs:
  # Unit Tests with Matrix Strategy
  unit-tests:
    name: 🧪 Unit Tests - Node ${{ matrix.node-version }}
    runs-on: ${{ matrix.os }}
    timeout-minutes: 15
    
    strategy:
      matrix:
        node-version: ['18', '20']
        os: [ubuntu-latest, windows-latest, macos-latest]
        
    steps:
      - name: 📦 Checkout code
        uses: actions/checkout@v4
        
      - name: 🔧 Setup Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'npm'
          
      - name: 📥 Install dependencies
        run: npm ci --prefer-offline --no-audit --progress=false
        
      - name: 🧪 Run Jest tests
        run: npm run test:unit
        
      - name: 🧪 Run Vitest tests
        run: npm run test:vitest
        
      - name: 📊 Generate test report
        run: npm run test:coverage
        
      - name: 📤 Upload coverage reports
        uses: codecov/codecov-action@v3
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          files: ./coverage/lcov.info
          flags: unit-tests-${{ matrix.os }}-${{ matrix.node-version }}
          name: codecov-${{ matrix.os }}-${{ matrix.node-version }}

  # Integration Tests
  integration-tests:
    name: 🔗 Integration Tests
    runs-on: ubuntu-latest
    timeout-minutes: 20
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
    
    steps:
      - name: 📦 Checkout code
        uses: actions/checkout@v4
        
      - name: 🔧 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 📥 Install dependencies
        run: npm ci --prefer-offline --no-audit --progress=false
        
      - name: 🗄️ Setup test database
        run: |
          npm run db:reset
          npm run db:seed
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db
          
      - name: 🔗 Run integration tests
        run: npm run test:integration
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db
          
      - name: 📊 Upload test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: integration-test-results
          path: test-results/
          retention-days: 7

  # API Tests
  api-tests:
    name: 🌐 API Tests
    runs-on: ubuntu-latest
    timeout-minutes: 15
    
    steps:
      - name: 📦 Checkout code
        uses: actions/checkout@v4
        
      - name: 🔧 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 📥 Install dependencies
        run: npm ci --prefer-offline --no-audit --progress=false
        
      - name: 🏗️ Build application
        run: npm run build
        
      - name: 🚀 Start application
        run: npm start &
        env:
          PORT: 3000
          
      - name: ⏳ Wait for application
        run: npx wait-on http://localhost:3000 --timeout 60000
        
      - name: 🌐 Run API tests
        run: npm run test:api
        
      - name: 📊 Generate API test report
        run: npm run test:api:report
        
      - name: 📤 Upload API test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: api-test-results
          path: api-test-results/
          retention-days: 7

  # E2E Tests with Browser Matrix
  e2e-tests:
    name: 🎭 E2E Tests - ${{ matrix.browser }}
    runs-on: ubuntu-latest
    timeout-minutes: 30
    
    strategy:
      matrix:
        browser: [chromium, firefox, webkit]
        
    steps:
      - name: 📦 Checkout code
        uses: actions/checkout@v4
        
      - name: 🔧 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 📥 Install dependencies
        run: npm ci --prefer-offline --no-audit --progress=false
        
      - name: 🎭 Install Playwright browsers
        run: npx playwright install --with-deps ${{ matrix.browser }}
        
      - name: 🏗️ Build application
        run: npm run build
        
      - name: 🚀 Start application
        run: npm start &
        env:
          PORT: 3000
          
      - name: ⏳ Wait for application
        run: npx wait-on http://localhost:3000 --timeout 60000
        
      - name: 🎭 Run E2E tests
        run: npx playwright test --project=${{ matrix.browser }}
        
      - name: 📸 Upload test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: e2e-results-${{ matrix.browser }}
          path: |
            playwright-report/
            test-results/
          retention-days: 7

  # Performance Tests
  performance-tests:
    name: ⚡ Performance Tests
    runs-on: ubuntu-latest
    timeout-minutes: 25
    
    steps:
      - name: 📦 Checkout code
        uses: actions/checkout@v4
        
      - name: 🔧 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 📥 Install dependencies
        run: npm ci --prefer-offline --no-audit --progress=false
        
      - name: 🏗️ Build application
        run: npm run build
        
      - name: 🚀 Start application
        run: npm start &
        env:
          PORT: 3000
          
      - name: ⏳ Wait for application
        run: npx wait-on http://localhost:3000 --timeout 60000
        
      - name: ⚡ Run performance tests
        run: npm run test:performance
        
      - name: 📊 Run Lighthouse audit
        run: |
          npm install -g @lhci/cli@0.12.x
          lhci autorun
        env:
          LHCI_GITHUB_APP_TOKEN: ${{ secrets.LHCI_GITHUB_APP_TOKEN }}
          
      - name: 📈 Performance regression check
        run: npm run test:performance:regression
        
      - name: 📊 Upload performance results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: performance-results
          path: |
            lighthouse-results/
            performance-results/
          retention-days: 7

  # Visual Regression Tests
  visual-tests:
    name: 👁️ Visual Regression Tests
    runs-on: ubuntu-latest
    timeout-minutes: 20
    
    steps:
      - name: 📦 Checkout code
        uses: actions/checkout@v4
        
      - name: 🔧 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 📥 Install dependencies
        run: npm ci --prefer-offline --no-audit --progress=false
        
      - name: 🎭 Install Playwright
        run: npx playwright install --with-deps
        
      - name: 🏗️ Build application
        run: npm run build
        
      - name: 🚀 Start application
        run: npm start &
        env:
          PORT: 3000
          
      - name: ⏳ Wait for application
        run: npx wait-on http://localhost:3000 --timeout 60000
        
      - name: 👁️ Run visual regression tests
        run: npm run test:visual
        
      - name: 📸 Upload visual test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: visual-test-results
          path: |
            visual-results/
            test-results/
          retention-days: 7

  # Mobile Tests
  mobile-tests:
    name: 📱 Mobile Tests
    runs-on: ubuntu-latest
    timeout-minutes: 25
    
    steps:
      - name: 📦 Checkout code
        uses: actions/checkout@v4
        
      - name: 🔧 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 📥 Install dependencies
        run: npm ci --prefer-offline --no-audit --progress=false
        
      - name: 🎭 Install Playwright
        run: npx playwright install --with-deps
        
      - name: 🏗️ Build application
        run: npm run build
        
      - name: 🚀 Start application
        run: npm start &
        env:
          PORT: 3000
          
      - name: ⏳ Wait for application
        run: npx wait-on http://localhost:3000 --timeout 60000
        
      - name: 📱 Run mobile tests
        run: npm run test:mobile
        
      - name: 📊 Upload mobile test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: mobile-test-results
          path: test-results/
          retention-days: 7

  # Test Summary
  test-summary:
    name: 📊 Test Summary
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests, api-tests, e2e-tests, performance-tests, visual-tests, mobile-tests]
    if: always()
    
    steps:
      - name: 📦 Checkout code
        uses: actions/checkout@v4
        
      - name: 📊 Generate test summary
        run: |
          echo "# 🧪 Test Summary" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "| Test Type | Status |" >> $GITHUB_STEP_SUMMARY
          echo "|-----------|---------|" >> $GITHUB_STEP_SUMMARY
          echo "| Unit Tests | ${{ needs.unit-tests.result == 'success' && '✅' || '❌' }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Integration Tests | ${{ needs.integration-tests.result == 'success' && '✅' || '❌' }} |" >> $GITHUB_STEP_SUMMARY
          echo "| API Tests | ${{ needs.api-tests.result == 'success' && '✅' || '❌' }} |" >> $GITHUB_STEP_SUMMARY
          echo "| E2E Tests | ${{ needs.e2e-tests.result == 'success' && '✅' || '❌' }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Performance Tests | ${{ needs.performance-tests.result == 'success' && '✅' || '❌' }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Visual Tests | ${{ needs.visual-tests.result == 'success' && '✅' || '❌' }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Mobile Tests | ${{ needs.mobile-tests.result == 'success' && '✅' || '❌' }} |" >> $GITHUB_STEP_SUMMARY
          
      - name: 📤 Upload consolidated test report
        uses: actions/upload-artifact@v3
        with:
          name: test-summary-report
          path: test-summary/
          retention-days: 30