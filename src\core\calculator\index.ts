/**
 * Calculator Module Entry Point
 * Clarity Engine - Construction Cost Calculator
 */

// Main calculation engine
export { calculate } from './engine';

// Individual calculation functions
export { calculateStructureCost } from './calculations/structure';
export { calculateFinishingCost } from './calculations/finishing';
export { calculateMEPCost } from './calculations/mep';

// Validation utilities
export {
  validateInput,
  sanitizeInput,
  isLocationSupported,
  getSuggestedLocations,
  validateCostReasonableness,
  validateMaterialQuantities,
} from './validation';

// Types
export type {
  CalculationInput,
  CalculationResult,
  CostBreakdown,
  CategoryCost,
  SubCategory,
  MaterialQuantity,
  ConstructionPhase,
  ProjectSummary,
  ValidationError,
  CalculationError,
  ApiResponse,
  CalculationApiResponse,
  QualityTierRates,
  RegionalMultiplier,
  MaterialConsumptionRate,
  CostCalculationParams,
  SpecialFeature,
} from './types';

// Constants
export {
  BASE_CONSTRUCTION_RATES,
  REGIONAL_MULTIPLIERS,
  FLOOR_MULTIPLIERS,
  COST_BREAKDOWN_PERCENTAGES,
  STRUCTURE_BREAKDOWN,
  MATERIAL_CONSUMPTION_RATES,
  QUALITY_TIER_SPECS,
  WASTAGE_FACTORS,
  CONSTRUCTION_PHASES,
  VALIDATION_LIMITS,
} from './constants';

import { REGIONAL_MULTIPLIERS, QUALITY_TIER_SPECS } from './constants';

// Version and metadata
export const CALCULATOR_VERSION = '1.0.0';
export const LAST_UPDATED = '2025-01-12';
export const SUPPORTED_LOCATIONS = Object.keys(REGIONAL_MULTIPLIERS).filter(loc => loc !== 'default');

/**
 * Quick calculation function for basic estimates
 * Useful for quick quotes and initial estimates
 */
export async function quickCalculate(
  area: number,
  floors: number = 0,
  qualityTier: 'smart' | 'premium' | 'luxury' = 'smart',
  location: string = 'bangalore'
): Promise<{ totalCost: number; costPerSqft: number }> {
  try {
    const { calculate } = await import('./engine');
    const result = calculate({
      builtUpArea: area,
      floors,
      qualityTier,
      location,
    });

    return {
      totalCost: result.totalCost,
      costPerSqft: result.costPerSqft,
    };
  } catch (error) {
    throw new Error(
      `Quick calculation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

/**
 * Get quality tier information
 */
export function getQualityTierInfo(tier: 'smart' | 'premium' | 'luxury') {
  return QUALITY_TIER_SPECS[tier];
}

/**
 * Get location multiplier for a given city
 */
export function getLocationMultiplier(location: string): number {
  return (
    REGIONAL_MULTIPLIERS[location.toLowerCase()] || REGIONAL_MULTIPLIERS.default
  );
}

/**
 * Calculate area-based recommendations
 */
export function getAreaRecommendations(area: number): {
  suggestedRooms: number;
  suggestedBathrooms: number;
  suggestedFloors: number;
  notes: string[];
} {
  const recommendations = {
    suggestedRooms: Math.max(2, Math.floor(area / 150)), // ~150 sqft per room
    suggestedBathrooms: Math.max(1, Math.floor(area / 400)), // ~400 sqft per bathroom
    suggestedFloors: area > 2000 ? 1 : 0, // Suggest additional floor for larger areas
    notes: [] as string[],
  };

  if (area < 600) {
    recommendations.notes.push(
      'Compact design recommended for efficient space utilization'
    );
  } else if (area > 3000) {
    recommendations.notes.push(
      'Consider multi-story design for better land utilization'
    );
    recommendations.notes.push('Luxury features and spacious layouts possible');
  }

  return recommendations;
}
