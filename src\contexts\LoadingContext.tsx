/**
 * Loading Context for Global Loading State Management
 * Provides centralized loading state management with progressive loading support
 */

'use client';

import React, { createContext, useContext, useReducer, useCallback, useRef } from 'react';

// Loading states enum
export enum LoadingState {
  IDLE = 'idle',
  LOADING = 'loading',
  SUCCESS = 'success',
  ERROR = 'error',
  RETRYING = 'retrying'
}

// Loading operation types
export enum LoadingType {
  CALCULATION = 'calculation',
  FORM_SUBMIT = 'form_submit',
  PDF_EXPORT = 'pdf_export',
  PROJECT_SAVE = 'project_save',
  DATA_FETCH = 'data_fetch',
  IMAGE_LOAD = 'image_load',
  NAVIGATION = 'navigation',
  AUTHENTICATION = 'authentication'
}

interface LoadingOperation {
  id: string;
  type: LoadingType;
  state: LoadingState;
  message?: string;
  progress?: number; // 0-100
  startTime: number;
  duration?: number;
  error?: string;
  retryCount?: number;
  maxRetries?: number;
  priority?: 'low' | 'medium' | 'high';
}

interface LoadingContextState {
  operations: Map<string, LoadingOperation>;
  globalState: LoadingState;
  isGlobalLoading: boolean;
  highestPriorityOperation?: LoadingOperation;
}

// Action types
type LoadingAction =
  | { type: 'START_OPERATION'; payload: Omit<LoadingOperation, 'id' | 'startTime' | 'state'> & { id?: string } }
  | { type: 'UPDATE_OPERATION'; payload: { id: string; updates: Partial<LoadingOperation> } }
  | { type: 'COMPLETE_OPERATION'; payload: { id: string; success?: boolean; error?: string } }
  | { type: 'RETRY_OPERATION'; payload: { id: string } }
  | { type: 'CANCEL_OPERATION'; payload: { id: string } }
  | { type: 'CLEAR_ALL_OPERATIONS' }
  | { type: 'UPDATE_PROGRESS'; payload: { id: string; progress: number; message?: string } };

// Generate unique operation ID
const generateOperationId = (): string => {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
};

// Determine global state based on operations
const getGlobalState = (operations: Map<string, LoadingOperation>): LoadingState => {
  const operationArray = Array.from(operations.values());
  
  if (operationArray.length === 0) return LoadingState.IDLE;
  
  // Check for high priority operations first
  const highPriorityOps = operationArray.filter(op => op.priority === 'high');
  const relevantOps = highPriorityOps.length > 0 ? highPriorityOps : operationArray;
  
  if (relevantOps.some(op => op.state === LoadingState.ERROR)) return LoadingState.ERROR;
  if (relevantOps.some(op => op.state === LoadingState.RETRYING)) return LoadingState.RETRYING;
  if (relevantOps.some(op => op.state === LoadingState.LOADING)) return LoadingState.LOADING;
  if (relevantOps.every(op => op.state === LoadingState.SUCCESS)) return LoadingState.SUCCESS;
  
  return LoadingState.IDLE;
};

// Get highest priority operation
const getHighestPriorityOperation = (operations: Map<string, LoadingOperation>): LoadingOperation | undefined => {
  const operationArray = Array.from(operations.values());
  
  const priorityOrder = { high: 3, medium: 2, low: 1 };
  
  return operationArray
    .filter(op => op.state === LoadingState.LOADING || op.state === LoadingState.RETRYING)
    .sort((a, b) => {
      const aPriority = priorityOrder[a.priority || 'medium'];
      const bPriority = priorityOrder[b.priority || 'medium'];
      return bPriority - aPriority;
    })[0];
};

// Reducer function
const loadingReducer = (state: LoadingContextState, action: LoadingAction): LoadingContextState => {
  const newOperations = new Map(state.operations);
  
  switch (action.type) {
    case 'START_OPERATION': {
      const id = action.payload.id || generateOperationId();
      const operation: LoadingOperation = {
        ...action.payload,
        id,
        state: LoadingState.LOADING,
        startTime: Date.now(),
        retryCount: 0,
        priority: action.payload.priority || 'medium'
      };
      
      newOperations.set(id, operation);
      break;
    }
    
    case 'UPDATE_OPERATION': {
      const existing = newOperations.get(action.payload.id);
      if (existing) {
        newOperations.set(action.payload.id, {
          ...existing,
          ...action.payload.updates
        });
      }
      break;
    }
    
    case 'COMPLETE_OPERATION': {
      const existing = newOperations.get(action.payload.id);
      if (existing) {
        const endTime = Date.now();
        newOperations.set(action.payload.id, {
          ...existing,
          state: action.payload.success === false ? LoadingState.ERROR : LoadingState.SUCCESS,
          duration: endTime - existing.startTime,
          error: action.payload.error
        });
        
        // Auto-remove successful operations after 2 seconds
        if (action.payload.success !== false) {
          setTimeout(() => {
            newOperations.delete(action.payload.id);
          }, 2000);
        }
      }
      break;
    }
    
    case 'RETRY_OPERATION': {
      const existing = newOperations.get(action.payload.id);
      if (existing && existing.retryCount! < (existing.maxRetries || 3)) {
        newOperations.set(action.payload.id, {
          ...existing,
          state: LoadingState.RETRYING,
          retryCount: (existing.retryCount || 0) + 1,
          error: undefined
        });
      }
      break;
    }
    
    case 'CANCEL_OPERATION': {
      newOperations.delete(action.payload.id);
      break;
    }
    
    case 'CLEAR_ALL_OPERATIONS': {
      newOperations.clear();
      break;
    }
    
    case 'UPDATE_PROGRESS': {
      const existing = newOperations.get(action.payload.id);
      if (existing) {
        newOperations.set(action.payload.id, {
          ...existing,
          progress: action.payload.progress,
          message: action.payload.message || existing.message
        });
      }
      break;
    }
  }
  
  const globalState = getGlobalState(newOperations);
  const highestPriorityOperation = getHighestPriorityOperation(newOperations);
  
  return {
    operations: newOperations,
    globalState,
    isGlobalLoading: globalState === LoadingState.LOADING || globalState === LoadingState.RETRYING,
    highestPriorityOperation
  };
};

// Initial state
const initialState: LoadingContextState = {
  operations: new Map(),
  globalState: LoadingState.IDLE,
  isGlobalLoading: false
};

// Context
interface LoadingContextValue {
  // State
  state: LoadingContextState;
  
  // Actions
  startOperation: (operation: Omit<LoadingOperation, 'id' | 'startTime' | 'state'> & { id?: string }) => string;
  updateOperation: (id: string, updates: Partial<LoadingOperation>) => void;
  completeOperation: (id: string, success?: boolean, error?: string) => void;
  retryOperation: (id: string) => void;
  cancelOperation: (id: string) => void;
  clearAllOperations: () => void;
  updateProgress: (id: string, progress: number, message?: string) => void;
  
  // Utilities
  getOperation: (id: string) => LoadingOperation | undefined;
  getOperationsByType: (type: LoadingType) => LoadingOperation[];
  isOperationLoading: (id: string) => boolean;
  isTypeLoading: (type: LoadingType) => boolean;
  getLoadingProgress: (id?: string) => number;
}

const LoadingContext = createContext<LoadingContextValue | undefined>(undefined);

// Provider component
interface LoadingProviderProps {
  children: React.ReactNode;
}

export function LoadingProvider({ children }: LoadingProviderProps) {
  const [state, dispatch] = useReducer(loadingReducer, initialState);
  const timeoutRefs = useRef<Map<string, NodeJS.Timeout>>(new Map());
  
  // Actions
  const startOperation = useCallback((operation: Omit<LoadingOperation, 'id' | 'startTime' | 'state'> & { id?: string }): string => {
    const id = operation.id || generateOperationId();
    dispatch({ type: 'START_OPERATION', payload: { ...operation, id } });
    
    // Auto-complete operation after timeout if no manual completion
    const timeout = setTimeout(() => {
      completeOperation(id, false, 'Operation timed out');
    }, operation.type === LoadingType.PDF_EXPORT ? 30000 : 10000);
    
    timeoutRefs.current.set(id, timeout);
    
    return id;
  }, []);
  
  const updateOperation = useCallback((id: string, updates: Partial<LoadingOperation>) => {
    dispatch({ type: 'UPDATE_OPERATION', payload: { id, updates } });
  }, []);
  
  const completeOperation = useCallback((id: string, success = true, error?: string) => {
    // Clear timeout
    const timeout = timeoutRefs.current.get(id);
    if (timeout) {
      clearTimeout(timeout);
      timeoutRefs.current.delete(id);
    }
    
    dispatch({ type: 'COMPLETE_OPERATION', payload: { id, success, error } });
  }, []);
  
  const retryOperation = useCallback((id: string) => {
    dispatch({ type: 'RETRY_OPERATION', payload: { id } });
  }, []);
  
  const cancelOperation = useCallback((id: string) => {
    // Clear timeout
    const timeout = timeoutRefs.current.get(id);
    if (timeout) {
      clearTimeout(timeout);
      timeoutRefs.current.delete(id);
    }
    
    dispatch({ type: 'CANCEL_OPERATION', payload: { id } });
  }, []);
  
  const clearAllOperations = useCallback(() => {
    // Clear all timeouts
    timeoutRefs.current.forEach(timeout => clearTimeout(timeout));
    timeoutRefs.current.clear();
    
    dispatch({ type: 'CLEAR_ALL_OPERATIONS' });
  }, []);
  
  const updateProgress = useCallback((id: string, progress: number, message?: string) => {
    dispatch({ type: 'UPDATE_PROGRESS', payload: { id, progress, message } });
  }, []);
  
  // Utilities
  const getOperation = useCallback((id: string) => {
    return state.operations.get(id);
  }, [state.operations]);
  
  const getOperationsByType = useCallback((type: LoadingType) => {
    return Array.from(state.operations.values()).filter(op => op.type === type);
  }, [state.operations]);
  
  const isOperationLoading = useCallback((id: string) => {
    const operation = state.operations.get(id);
    return operation?.state === LoadingState.LOADING || operation?.state === LoadingState.RETRYING;
  }, [state.operations]);
  
  const isTypeLoading = useCallback((type: LoadingType) => {
    return Array.from(state.operations.values()).some(
      op => op.type === type && (op.state === LoadingState.LOADING || op.state === LoadingState.RETRYING)
    );
  }, [state.operations]);
  
  const getLoadingProgress = useCallback((id?: string) => {
    if (id) {
      const operation = state.operations.get(id);
      return operation?.progress || 0;
    }
    
    // Return average progress of all loading operations
    const loadingOps = Array.from(state.operations.values()).filter(
      op => op.state === LoadingState.LOADING && op.progress !== undefined
    );
    
    if (loadingOps.length === 0) return 0;
    
    const totalProgress = loadingOps.reduce((sum, op) => sum + (op.progress || 0), 0);
    return Math.round(totalProgress / loadingOps.length);
  }, [state.operations]);
  
  const value: LoadingContextValue = {
    state,
    startOperation,
    updateOperation,
    completeOperation,
    retryOperation,
    cancelOperation,
    clearAllOperations,
    updateProgress,
    getOperation,
    getOperationsByType,
    isOperationLoading,
    isTypeLoading,
    getLoadingProgress
  };
  
  return (
    <LoadingContext.Provider value={value}>
      {children}
    </LoadingContext.Provider>
  );
}

// Hook to use loading context
export function useLoading() {
  const context = useContext(LoadingContext);
  if (context === undefined) {
    throw new Error('useLoading must be used within a LoadingProvider');
  }
  return context;
}

// Convenience hooks for specific operations
export function useCalculationLoading() {
  const { state, isTypeLoading, getOperationsByType } = useLoading();
  
  const isLoading = isTypeLoading(LoadingType.CALCULATION);
  const operations = getOperationsByType(LoadingType.CALCULATION);
  const currentOperation = operations.find(op => 
    op.state === LoadingState.LOADING || op.state === LoadingState.RETRYING
  );
  
  return {
    isLoading,
    progress: currentOperation?.progress || 0,
    message: currentOperation?.message,
    error: currentOperation?.error
  };
}

export function usePDFExportLoading() {
  const { isTypeLoading, getOperationsByType } = useLoading();
  
  const isLoading = isTypeLoading(LoadingType.PDF_EXPORT);
  const operations = getOperationsByType(LoadingType.PDF_EXPORT);
  const currentOperation = operations.find(op => 
    op.state === LoadingState.LOADING || op.state === LoadingState.RETRYING
  );
  
  return {
    isLoading,
    progress: currentOperation?.progress || 0,
    message: currentOperation?.message,
    error: currentOperation?.error
  };
}

export function useFormSubmitLoading() {
  const { isTypeLoading, getOperationsByType } = useLoading();
  
  const isLoading = isTypeLoading(LoadingType.FORM_SUBMIT);
  const operations = getOperationsByType(LoadingType.FORM_SUBMIT);
  const currentOperation = operations.find(op => 
    op.state === LoadingState.LOADING || op.state === LoadingState.RETRYING
  );
  
  return {
    isLoading,
    progress: currentOperation?.progress || 0,
    message: currentOperation?.message,
    error: currentOperation?.error
  };
}

// Hook for managing operation lifecycle
export function useOperationManager() {
  const { startOperation, completeOperation, updateProgress, retryOperation } = useLoading();
  
  const withLoading = useCallback(async <T,>(
    operationType: LoadingType,
    operation: () => Promise<T>,
    options?: {
      id?: string;
      message?: string;
      priority?: 'low' | 'medium' | 'high';
      maxRetries?: number;
      onProgress?: (progress: number, message?: string) => void;
    }
  ): Promise<T> => {
    const operationId = startOperation({
      type: operationType,
      message: options?.message || `Performing ${operationType}...`,
      priority: options?.priority || 'medium',
      maxRetries: options?.maxRetries || 3,
      id: options?.id
    });
    
    try {
      // Set up progress callback
      if (options?.onProgress) {
        const progressCallback = (progress: number, message?: string) => {
          updateProgress(operationId, progress, message);
          options.onProgress!(progress, message);
        };
        
        // Add progress callback to operation context if needed
        (operation as any).progressCallback = progressCallback;
      }
      
      const result = await operation();
      completeOperation(operationId, true);
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      completeOperation(operationId, false, errorMessage);
      throw error;
    }
  }, [startOperation, completeOperation, updateProgress]);
  
  return { withLoading, retryOperation };
}