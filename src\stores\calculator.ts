import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

interface CalculationInput {
  builtUpArea: number;
  floors: number;
  qualityTier: 'smart' | 'premium' | 'luxury';
  location: string;
  plotArea?: number;
  hasBasement?: boolean;
  parkingType?: 'none' | 'open' | 'covered';
}

interface CalculationResult {
  totalCost: number;
  costPerSqft: number;
  breakdown: {
    structure: number;
    finishing: number;
    mep: number;
    external: number;
    other: number;
  };
  materials?: Array<{
    material: string;
    quantity: number;
    unit: string;
    rate: number;
    totalCost: number;
    category: string;
  }>;
  timeline?: Array<{
    phase: string;
    duration: number;
    cost: number;
  }>;
}

interface CalculatorState {
  // Form data
  input: CalculationInput;

  // Results
  result: CalculationResult | null;

  // UI state
  isLoading: boolean;
  error: string | null;

  // History
  calculations: Array<{
    id: string;
    timestamp: number;
    input: CalculationInput;
    result: CalculationResult;
  }>;

  // Actions
  setInput: (input: Partial<CalculationInput>) => void;
  setResult: (result: CalculationResult | null) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  calculate: () => Promise<void>;
  saveCalculation: () => void;
  clearHistory: () => void;
  resetForm: () => void;
}

const defaultInput: CalculationInput = {
  builtUpArea: 1200,
  floors: 0,
  qualityTier: 'smart',
  location: 'bangalore',
  hasBasement: false,
  parkingType: 'open',
};

export const useCalculatorStore = create<CalculatorState>()(
  devtools(
    (set, get) => ({
      // Initial state
      input: defaultInput,
      result: null,
      isLoading: false,
      error: null,
      calculations: [],

      // Actions
      setInput: newInput =>
        set(state => ({
          input: { ...state.input, ...newInput },
          error: null, // Clear error when input changes
        })),

      setResult: result => set({ result }),

      setLoading: isLoading => set({ isLoading }),

      setError: error => set({ error }),

      calculate: async () => {
        const { input, setLoading, setResult, setError } = get();

        setLoading(true);
        setError(null);

        try {
          const response = await fetch('/api/calculate', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(input),
          });

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || 'Calculation failed');
          }

          const data = await response.json();
          setResult(data.data);
        } catch (error) {
          setError(
            error instanceof Error ? error.message : 'An unknown error occurred'
          );
        } finally {
          setLoading(false);
        }
      },

      saveCalculation: () => {
        const { input, result } = get();

        if (!result) return;

        const calculation = {
          id: `calc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          timestamp: Date.now(),
          input: { ...input },
          result: { ...result },
        };

        set(state => ({
          calculations: [calculation, ...state.calculations.slice(0, 19)], // Keep only last 20
        }));
      },

      clearHistory: () => set({ calculations: [] }),

      resetForm: () =>
        set({
          input: defaultInput,
          result: null,
          error: null,
        }),
    }),
    {
      name: 'calculator-store',
    }
  )
);

// Helper hooks for common patterns
export const useCalculationInput = () =>
  useCalculatorStore(state => state.input);
export const useCalculationResult = () =>
  useCalculatorStore(state => state.result);
export const useCalculationStatus = () =>
  useCalculatorStore(state => ({
    isLoading: state.isLoading,
    error: state.error,
  }));
export const useCalculationHistory = () =>
  useCalculatorStore(state => state.calculations);
