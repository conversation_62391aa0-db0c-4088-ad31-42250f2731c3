/**
 * Wizard Navigation Component
 * Professional navigation controls with animations and accessibility
 */

import React from 'react';
import { motion, type Variants } from 'framer-motion';
import { 
  ChevronLeft, 
  ChevronRight, 
  SkipForward, 
  Save, 
  Calculator,
  RotateCcw,
  KeyboardIcon
} from 'lucide-react';
import { EnhancedButton } from '@/components/ui/enhanced-button';
import { cn } from '@/lib/utils';
import { WizardState, WizardActions } from './types/wizard';

interface WizardNavigationProps {
  state: WizardState;
  actions: WizardActions;
  isSubmitting?: boolean;
  allowSkip?: boolean;
  showShortcuts?: boolean;
  showSave?: boolean;
  className?: string;
}

// Animation variants
const navigationVariants: Variants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.3,
      staggerChildren: 0.1,
    },
  },
};

const buttonVariants: Variants = {
  hidden: { opacity: 0, x: -20 },
  visible: { opacity: 1, x: 0 },
  disabled: { opacity: 0.5, scale: 0.95 },
};

export function WizardNavigation({
  state,
  actions,
  isSubmitting = false,
  allowSkip = false,
  showShortcuts = true,
  showSave = false,
  className,
}: WizardNavigationProps) {
  const { 
    currentStep, 
    totalSteps, 
    canProceed, 
    canGoBack, 
    isValid 
  } = state;
  
  const { 
    nextStep, 
    previousStep, 
    skipStep, 
    resetWizard, 
    submitForm 
  } = actions;

  const isLastStep = currentStep === totalSteps - 1;
  const isFirstStep = currentStep === 0;

  // Keyboard shortcut handlers
  React.useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Don't trigger shortcuts if user is typing in inputs
      if (
        event.target instanceof HTMLInputElement ||
        event.target instanceof HTMLTextAreaElement ||
        event.target instanceof HTMLSelectElement
      ) {
        return;
      }

      switch (event.key) {
        case 'Enter':
          if (event.ctrlKey || event.metaKey) {
            event.preventDefault();
            if (isLastStep && canProceed) {
              submitForm();
            } else if (canProceed) {
              nextStep();
            }
          }
          break;
        case 'Escape':
          event.preventDefault();
          if (canGoBack) {
            previousStep();
          }
          break;
        case 'ArrowRight':
          if (event.altKey) {
            event.preventDefault();
            if (canProceed) {
              nextStep();
            }
          }
          break;
        case 'ArrowLeft':
          if (event.altKey) {
            event.preventDefault();
            if (canGoBack) {
              previousStep();
            }
          }
          break;
        case 's':
          if (event.ctrlKey || event.metaKey) {
            event.preventDefault();
            // Handle save functionality if implemented
            console.log('Save triggered');
          }
          break;
        case 'r':
          if (event.ctrlKey && event.shiftKey) {
            event.preventDefault();
            resetWizard();
          }
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [
    currentStep,
    canProceed,
    canGoBack,
    isLastStep,
    nextStep,
    previousStep,
    submitForm,
    resetWizard,
  ]);

  return (
    <motion.div
      className={cn(
        'flex items-center justify-between gap-4 p-6',
        'bg-white border-t border-secondary-200',
        'sticky bottom-0 z-10',
        className
      )}
      variants={navigationVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Left Side - Back Button */}
      <motion.div
        className="flex items-center gap-3"
        variants={buttonVariants}
      >
        <EnhancedButton
          variant="outline"
          size="lg"
          leftIcon={<ChevronLeft className="h-4 w-4" />}
          onClick={previousStep}
          disabled={!canGoBack || isSubmitting}
          className={cn(
            'transition-all duration-200',
            !canGoBack && 'invisible'
          )}
        >
          Back
        </EnhancedButton>

        {/* Reset Button */}
        <EnhancedButton
          variant="ghost"
          size="md"
          leftIcon={<RotateCcw className="h-4 w-4" />}
          onClick={resetWizard}
          disabled={isSubmitting}
          className="text-secondary-600 hover:text-secondary-800"
        >
          Reset
        </EnhancedButton>
      </motion.div>

      {/* Center - Step Info & Shortcuts */}
      <motion.div
        className="flex flex-col items-center gap-2"
        variants={buttonVariants}
      >
        <div className="text-sm text-secondary-600">
          Step {currentStep + 1} of {totalSteps}
        </div>
        
        {showShortcuts && (
          <div className="flex items-center gap-4 text-xs text-secondary-500">
            <div className="flex items-center gap-1">
              <KeyboardIcon className="h-3 w-3" />
              <span>Enter: Next</span>
            </div>
            <div className="flex items-center gap-1">
              <span>Esc: Back</span>
            </div>
            <div className="flex items-center gap-1">
              <span>Alt + ←→: Navigate</span>
            </div>
          </div>
        )}
      </motion.div>

      {/* Right Side - Forward Actions */}
      <motion.div
        className="flex items-center gap-3"
        variants={buttonVariants}
      >
        {/* Save Button (if enabled) */}
        {showSave && (
          <EnhancedButton
            variant="ghost"
            size="md"
            leftIcon={<Save className="h-4 w-4" />}
            disabled={isSubmitting}
            className="text-secondary-600 hover:text-secondary-800"
          >
            Save
          </EnhancedButton>
        )}

        {/* Skip Button (if allowed and not last step) */}
        {allowSkip && !isLastStep && (
          <EnhancedButton
            variant="ghost"
            size="md"
            leftIcon={<SkipForward className="h-4 w-4" />}
            onClick={skipStep}
            disabled={isSubmitting}
            className="text-yellow-600 hover:text-yellow-800"
          >
            Skip
          </EnhancedButton>
        )}

        {/* Next/Submit Button */}
        <EnhancedButton
          variant="primary"
          size="lg"
          rightIcon={
            isLastStep ? 
              <Calculator className="h-4 w-4" /> : 
              <ChevronRight className="h-4 w-4" />
          }
          onClick={isLastStep ? submitForm : nextStep}
          disabled={!canProceed || isSubmitting}
          loading={isSubmitting}
          className={cn(
            'min-w-[120px]',
            !isValid && 'opacity-50 cursor-not-allowed'
          )}
        >
          {isSubmitting
            ? 'Calculating...'
            : isLastStep
            ? 'Calculate Cost'
            : 'Continue'
          }
        </EnhancedButton>
      </motion.div>

      {/* Mobile Layout Adjustments */}
      <style jsx>{`
        @media (max-width: 768px) {
          .wizard-navigation {
            flex-direction: column;
            gap: 1rem;
          }
          
          .wizard-navigation .nav-section {
            width: 100%;
            justify-content: center;
          }
          
          .wizard-navigation .center-section {
            order: -1;
          }
        }
      `}</style>
    </motion.div>
  );
}

// Compact Navigation for Mobile
export function CompactWizardNavigation({
  state,
  actions,
  isSubmitting = false,
  className,
}: Omit<WizardNavigationProps, 'showShortcuts' | 'showSave' | 'allowSkip'>) {
  const { canProceed, canGoBack } = state;
  const { nextStep, previousStep, submitForm } = actions;
  const isLastStep = state.currentStep === state.totalSteps - 1;

  return (
    <motion.div
      className={cn(
        'flex items-center justify-between p-4',
        'bg-white border-t border-secondary-200',
        'sticky bottom-0 z-10',
        className
      )}
      variants={navigationVariants}
      initial="hidden"
      animate="visible"
    >
      <EnhancedButton
        variant="outline"
        size="md"
        leftIcon={<ChevronLeft className="h-4 w-4" />}
        onClick={previousStep}
        disabled={!canGoBack || isSubmitting}
        className={!canGoBack ? 'invisible' : ''}
      >
        Back
      </EnhancedButton>

      <div className="text-sm text-secondary-600">
        {state.currentStep + 1} / {state.totalSteps}
      </div>

      <EnhancedButton
        variant="primary"
        size="md"
        rightIcon={
          isLastStep ? 
            <Calculator className="h-4 w-4" /> : 
            <ChevronRight className="h-4 w-4" />
        }
        onClick={isLastStep ? submitForm : nextStep}
        disabled={!canProceed || isSubmitting}
        loading={isSubmitting}
      >
        {isSubmitting ? 'Calculating...' : isLastStep ? 'Calculate' : 'Next'}
      </EnhancedButton>
    </motion.div>
  );
}

export default WizardNavigation;