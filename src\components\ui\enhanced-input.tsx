/**
 * Enhanced Input Component for MVP Design System
 * Professional input with validation, icons, and accessibility
 */

import * as React from "react";
import { motion, type Variants } from "framer-motion";
import { Eye, EyeOff, AlertCircle, CheckCircle } from "lucide-react";
import { cn } from "@/lib/utils";

type InputVariant = "default" | "success" | "error" | "warning";
type InputSize = "sm" | "md" | "lg";

interface InputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'> {
  variant?: InputVariant;
  size?: InputSize;
  label?: string;
  helperText?: string;
  errorMessage?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  fullWidth?: boolean;
  loading?: boolean;
  showPasswordToggle?: boolean;
}

// Animation variants
const containerVariants: Variants = {
  focus: {
    scale: 1.01,
    transition: { duration: 0.15 },
  },
  blur: {
    scale: 1,
    transition: { duration: 0.15 },
  },
};

const labelVariants: Variants = {
  focus: {
    y: -8,
    scale: 0.85,
    color: "var(--primary-600)",
    transition: { duration: 0.15 },
  },
  blur: {
    y: 0,
    scale: 1,
    color: "var(--secondary-500)",
    transition: { duration: 0.15 },
  },
};

// Base styles
const baseClasses = cn(
  "w-full rounded-lg border transition-all duration-200",
  "focus:outline-none focus:ring-4 focus:ring-primary-500/20",
  "disabled:opacity-50 disabled:cursor-not-allowed",
  "placeholder:text-secondary-400"
);

// Variant styles
const variantClasses: Record<InputVariant, string> = {
  default: cn(
    "border-secondary-200 bg-white text-secondary-900",
    "hover:border-secondary-300",
    "focus:border-primary-500"
  ),
  success: cn(
    "border-green-300 bg-green-50 text-green-900",
    "hover:border-green-400",
    "focus:border-green-500 focus:ring-green-500/20"
  ),
  error: cn(
    "border-red-300 bg-red-50 text-red-900",
    "hover:border-red-400",
    "focus:border-red-500 focus:ring-red-500/20"
  ),
  warning: cn(
    "border-yellow-300 bg-yellow-50 text-yellow-900",
    "hover:border-yellow-400",
    "focus:border-yellow-500 focus:ring-yellow-500/20"
  ),
};

// Size styles
const sizeClasses: Record<InputSize, string> = {
  sm: "px-3 py-1.5 text-sm h-8",
  md: "px-4 py-2 text-base h-10",
  lg: "px-5 py-3 text-lg h-12",
};

const iconSizes: Record<InputSize, string> = {
  sm: "h-4 w-4",
  md: "h-5 w-5", 
  lg: "h-6 w-6",
};

export const EnhancedInput = React.forwardRef<HTMLInputElement, InputProps>(
  (
    {
      className,
      variant = "default",
      size = "md",
      label,
      helperText,
      errorMessage,
      leftIcon,
      rightIcon,
      fullWidth = true,
      loading = false,
      showPasswordToggle = false,
      type = "text",
      disabled,
      ...props
    },
    ref
  ) => {
    const [isFocused, setIsFocused] = React.useState(false);
    const [showPassword, setShowPassword] = React.useState(false);
    const [hasValue, setHasValue] = React.useState(false);
    const inputId = React.useId();
    const helperId = React.useId();
    const errorId = React.useId();

    // Determine actual input type
    const inputType = React.useMemo(() => {
      if (type === "password" && showPasswordToggle) {
        return showPassword ? "text" : "password";
      }
      return type;
    }, [type, showPassword, showPasswordToggle]);

    // Handle focus/blur
    const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
      setIsFocused(true);
      props.onFocus?.(e);
    };

    const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
      setIsFocused(false);
      props.onBlur?.(e);
    };

    // Handle value change
    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      setHasValue(e.target.value.length > 0);
      props.onChange?.(e);
    };

    // Toggle password visibility
    const togglePasswordVisibility = () => {
      setShowPassword(!showPassword);
    };

    // Determine effective variant
    const effectiveVariant = errorMessage ? "error" : variant;

    // Status icon
    const statusIcon = React.useMemo(() => {
      if (effectiveVariant === "success") {
        return <CheckCircle className={cn(iconSizes[size], "text-green-500")} />;
      }
      if (effectiveVariant === "error") {
        return <AlertCircle className={cn(iconSizes[size], "text-red-500")} />;
      }
      return null;
    }, [effectiveVariant, size]);

    return (
      <div className={cn("relative", fullWidth && "w-full")}>
        {/* Label */}
        {label && (
          <motion.label
            htmlFor={inputId}
            className={cn(
              "block text-sm font-medium mb-1",
              effectiveVariant === "error" ? "text-red-700" : "text-secondary-700"
            )}
            variants={labelVariants}
            animate={isFocused || hasValue ? "focus" : "blur"}
          >
            {label}
            {props.required && <span className="text-red-500 ml-1">*</span>}
          </motion.label>
        )}

        {/* Input Container */}
        <motion.div
          className="relative"
          variants={containerVariants}
          animate={isFocused ? "focus" : "blur"}
        >
          {/* Left Icon */}
          {leftIcon && (
            <div className="absolute left-3 top-1/2 -translate-y-1/2 pointer-events-none">
              <span className={cn(iconSizes[size], "text-secondary-400")}>
                {leftIcon}
              </span>
            </div>
          )}

          {/* Input Field */}
          <input
            ref={ref}
            id={inputId}
            type={inputType}
            className={cn(
              baseClasses,
              variantClasses[effectiveVariant],
              sizeClasses[size],
              leftIcon && "pl-10",
              (rightIcon || statusIcon || showPasswordToggle) && "pr-10",
              className
            )}
            disabled={disabled || loading}
            onFocus={handleFocus}
            onBlur={handleBlur}
            onChange={handleChange}
            aria-invalid={effectiveVariant === "error"}
            aria-describedby={cn(
              helperText && helperId,
              errorMessage && errorId
            )}
            {...props}
          />

          {/* Right Side Icons */}
          <div className="absolute right-3 top-1/2 -translate-y-1/2 flex items-center gap-1">
            {/* Status Icon */}
            {statusIcon}

            {/* Password Toggle */}
            {showPasswordToggle && type === "password" && (
              <button
                type="button"
                onClick={togglePasswordVisibility}
                className={cn(
                  "p-1 text-secondary-400 hover:text-secondary-600",
                  "focus:outline-none focus:text-secondary-600",
                  "transition-colors duration-150"
                )}
                aria-label={showPassword ? "Hide password" : "Show password"}
              >
                {showPassword ? (
                  <EyeOff className={iconSizes[size]} />
                ) : (
                  <Eye className={iconSizes[size]} />
                )}
              </button>
            )}

            {/* Custom Right Icon */}
            {rightIcon && !statusIcon && (
              <span className={cn(iconSizes[size], "text-secondary-400")}>
                {rightIcon}
              </span>
            )}
          </div>

          {/* Loading State */}
          {loading && (
            <div className="absolute inset-0 bg-white/50 flex items-center justify-center">
              <motion.div
                animate={{ rotate: 360 }}
                transition={{
                  duration: 1,
                  repeat: Infinity,
                  ease: "linear",
                }}
                className={cn(iconSizes[size], "text-primary-500")}
              >
                ⟳
              </motion.div>
            </div>
          )}
        </motion.div>

        {/* Helper Text */}
        {helperText && !errorMessage && (
          <p
            id={helperId}
            className="mt-1 text-sm text-secondary-500"
          >
            {helperText}
          </p>
        )}

        {/* Error Message */}
        {errorMessage && (
          <motion.p
            id={errorId}
            className="mt-1 text-sm text-red-600"
            initial={{ opacity: 0, y: -5 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.15 }}
          >
            {errorMessage}
          </motion.p>
        )}
      </div>
    );
  }
);

EnhancedInput.displayName = "EnhancedInput";

// Export types
export type { InputProps, InputVariant, InputSize };