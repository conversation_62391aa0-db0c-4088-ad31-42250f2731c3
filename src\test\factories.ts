import { vi } from 'vitest';
import type { CalculationInput, CalculationResult } from '@/core/calculator/types';
import type { QualityTier } from '@/core/calculator/types';

// Material Factory
export const createMockMaterial = (overrides = {}) => ({
  id: 'cement',
  name: 'Portland Cement',
  category: 'structural',
  unit: 'bag',
  consumption: {
    smart: 6.5,
    premium: 7.2,
    luxury: 8.0,
  },
  pricing: {
    bangalore: {
      smart: 420,
      premium: 480,
      luxury: 520,
    },
    mumbai: {
      smart: 504,
      premium: 576,
      luxury: 624,
    },
  },
  wastage: 0.02,
  brand: {
    smart: 'ACC',
    premium: 'UltraTech',
    luxury: 'Ambuja',
  },
  specifications: {
    smart: 'OPC 53 Grade',
    premium: 'PPC 53 Grade',
    luxury: 'Blended Cement',
  },
  ...overrides,
});

// Location Factory
export const createMockLocation = (overrides = {}) => ({
  id: 'bangalore',
  name: 'Bangalore',
  state: 'Karnataka',
  multiplier: 1.0,
  tier: 'tier1',
  ...overrides,
});

// Calculation Input Factory
export const createMockCalculationInput = (overrides: Partial<CalculationInput> = {}): CalculationInput => ({
  builtUpArea: 1200,
  location: 'bangalore',
  qualityTier: 'smart' as QualityTier,
  floors: 2,
  constructionType: 'independent_house',
  basement: false,
  parkingType: 'open',
  ...overrides,
});

// Calculation Result Factory
export const createMockCalculationResult = (overrides: Partial<CalculationResult> = {}): CalculationResult => ({
  totalCost: 2160000,
  costPerSqft: 1800,
  breakdown: {
    structure: {
      amount: 756000,
      percentage: 35,
      materials: [
        { name: 'Cement', quantity: 150, unit: 'bags', unitCost: 450, totalCost: 67500 },
        { name: 'Steel', quantity: 2400, unit: 'kg', unitCost: 65, totalCost: 156000 },
        { name: 'Concrete', quantity: 120, unit: 'cum', unitCost: 4500, totalCost: 540000 },
      ],
    },
    finishing: {
      amount: 648000,
      percentage: 30,
      materials: [
        { name: 'Tiles', quantity: 1200, unit: 'sqft', unitCost: 80, totalCost: 96000 },
        { name: 'Paint', quantity: 50, unit: 'liters', unitCost: 350, totalCost: 17500 },
        { name: 'Doors', quantity: 8, unit: 'nos', unitCost: 12000, totalCost: 96000 },
      ],
    },
    mep: {
      amount: 432000,
      percentage: 20,
      materials: [
        { name: 'Electrical', quantity: 1, unit: 'lumpsum', unitCost: 180000, totalCost: 180000 },
        { name: 'Plumbing', quantity: 1, unit: 'lumpsum', unitCost: 120000, totalCost: 120000 },
        { name: 'Sanitary', quantity: 1, unit: 'lumpsum', unitCost: 80000, totalCost: 80000 },
      ],
    },
    external: {
      amount: 216000,
      percentage: 10,
      materials: [
        { name: 'Compound Wall', quantity: 200, unit: 'sqft', unitCost: 150, totalCost: 30000 },
        { name: 'Landscaping', quantity: 1, unit: 'lumpsum', unitCost: 50000, totalCost: 50000 },
      ],
    },
    other: {
      amount: 108000,
      percentage: 5,
      materials: [
        { name: 'Architect Fee', quantity: 1, unit: 'lumpsum', unitCost: 50000, totalCost: 50000 },
        { name: 'Approvals', quantity: 1, unit: 'lumpsum', unitCost: 30000, totalCost: 30000 },
      ],
    },
  },
  metadata: {
    location: 'bangalore',
    qualityTier: 'smart' as QualityTier,
    calculatedAt: new Date().toISOString(),
    version: '1.0.0',
  },
  ...overrides,
});

// User Factory
export const createMockUser = (overrides = {}) => ({
  id: 'user-123',
  email: '<EMAIL>',
  name: 'Test User',
  phone: '+91-9876543210',
  role: 'user',
  verified: true,
  ...overrides,
});

// Project Factory
export const createMockProject = (overrides = {}) => ({
  id: 'project-123',
  name: 'Test Project',
  description: 'Test project description',
  builtUpArea: 1200,
  location: 'bangalore',
  qualityTier: 'smart',
  status: 'active',
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  userId: 'user-123',
  ...overrides,
});

// Form Data Factory
export const createMockFormData = (overrides = {}) => ({
  builtUpArea: '1200',
  location: 'bangalore',
  qualityTier: 'smart',
  floors: '2',
  constructionType: 'independent_house',
  ...overrides,
});

// API Response Factory
export const createMockApiResponse = <T>(data: T, overrides = {}) => ({
  data,
  success: true,
  message: 'Success',
  timestamp: new Date().toISOString(),
  ...overrides,
});

// Error Factory
export const createMockError = (overrides = {}) => ({
  message: 'Test error',
  code: 'TEST_ERROR',
  status: 500,
  ...overrides,
});

// Mock Component Props Factory
export const createMockComponentProps = (overrides = {}) => ({
  className: 'test-class',
  children: null,
  ...overrides,
});

// Mock Event Factory
export const createMockEvent = (overrides = {}) => ({
  preventDefault: vi.fn(),
  stopPropagation: vi.fn(),
  target: { value: '' },
  currentTarget: { value: '' },
  ...overrides,
});

// Mock Query Factory
export const createMockQuery = (overrides = {}) => ({
  data: null,
  error: null,
  isLoading: false,
  isFetching: false,
  isError: false,
  isSuccess: true,
  refetch: vi.fn(),
  ...overrides,
});

// Mock Mutation Factory
export const createMockMutation = (overrides = {}) => ({
  mutate: vi.fn(),
  mutateAsync: vi.fn(),
  isLoading: false,
  isError: false,
  isSuccess: false,
  error: null,
  data: null,
  reset: vi.fn(),
  ...overrides,
});

// Performance Test Data Factory
export const createPerformanceTestData = (size: number = 1000) => {
  const data = [];
  for (let i = 0; i < size; i++) {
    data.push(createMockCalculationInput({
      builtUpArea: Math.floor(Math.random() * 3000) + 500,
      location: ['bangalore', 'mumbai', 'delhi'][Math.floor(Math.random() * 3)],
      qualityTier: ['smart', 'premium', 'luxury'][Math.floor(Math.random() * 3)] as QualityTier,
    }));
  }
  return data;
};

// Material Database Factory
export const createMockMaterialDatabase = () => ({
  materials: [
    createMockMaterial({ id: 'cement', name: 'Portland Cement' }),
    createMockMaterial({ id: 'steel', name: 'TMT Steel Bars' }),
    createMockMaterial({ id: 'sand', name: 'River Sand' }),
    createMockMaterial({ id: 'aggregate', name: 'Coarse Aggregate' }),
    createMockMaterial({ id: 'bricks', name: 'Red Clay Bricks' }),
  ],
  locations: [
    createMockLocation({ id: 'bangalore', multiplier: 1.0 }),
    createMockLocation({ id: 'mumbai', multiplier: 1.2 }),
    createMockLocation({ id: 'delhi', multiplier: 1.05 }),
  ],
  qualityTiers: ['smart', 'premium', 'luxury'],
  categories: ['structural', 'finishing', 'electrical', 'plumbing', 'external'],
});

// Test Suite Factory
export const createTestSuite = (name: string, tests: Array<() => void>) => ({
  name,
  tests,
  setup: vi.fn(),
  teardown: vi.fn(),
  beforeEach: vi.fn(),
  afterEach: vi.fn(),
});

// Mock Store Factory
export const createMockStore = (initialState = {}) => ({
  getState: vi.fn().mockReturnValue(initialState),
  dispatch: vi.fn(),
  subscribe: vi.fn(),
  replaceReducer: vi.fn(),
});

// Mock Context Factory
export const createMockContext = (value: any) => ({
  Provider: ({ children }: { children: React.ReactNode }) => children,
  Consumer: ({ children }: { children: (value: any) => React.ReactNode }) => children(value),
  displayName: 'MockContext',
});

// Test Environment Factory
export const createTestEnvironment = (overrides = {}) => ({
  isDevelopment: false,
  isProduction: false,
  isTest: true,
  baseUrl: 'http://localhost:3000',
  apiUrl: 'http://localhost:3000/api',
  ...overrides,
});

// Mock Async Function Factory
export const createMockAsync = <T>(result: T, delay: number = 0) => {
  return vi.fn().mockImplementation(() => 
    new Promise((resolve) => setTimeout(() => resolve(result), delay))
  );
};

// Mock Rejected Promise Factory
export const createMockRejection = (error: any, delay: number = 0) => {
  return vi.fn().mockImplementation(() => 
    new Promise((_, reject) => setTimeout(() => reject(error), delay))
  );
};

// Batch Test Data Factory
export const createBatchTestData = (count: number, factory: () => any) => {
  return Array.from({ length: count }, factory);
};

// Random Data Generators
export const randomString = (length: number = 10) => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  return Array.from({ length }, () => chars[Math.floor(Math.random() * chars.length)]).join('');
};

export const randomNumber = (min: number = 0, max: number = 100) => {
  return Math.floor(Math.random() * (max - min + 1)) + min;
};

export const randomBoolean = () => Math.random() > 0.5;

export const randomDate = (start: Date = new Date(2020, 0, 1), end: Date = new Date()) => {
  return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
};

export const randomEmail = () => `${randomString(8)}@${randomString(5)}.com`;

export const randomPhone = () => `+91-${randomNumber(6000000000, 9999999999)}`;

// Export all factories as a combined object
export const factories = {
  material: createMockMaterial,
  location: createMockLocation,
  calculationInput: createMockCalculationInput,
  calculationResult: createMockCalculationResult,
  user: createMockUser,
  project: createMockProject,
  formData: createMockFormData,
  apiResponse: createMockApiResponse,
  error: createMockError,
  componentProps: createMockComponentProps,
  event: createMockEvent,
  query: createMockQuery,
  mutation: createMockMutation,
  performanceTestData: createPerformanceTestData,
  materialDatabase: createMockMaterialDatabase,
  testSuite: createTestSuite,
  store: createMockStore,
  context: createMockContext,
  testEnvironment: createTestEnvironment,
  async: createMockAsync,
  rejection: createMockRejection,
  batchTestData: createBatchTestData,
};