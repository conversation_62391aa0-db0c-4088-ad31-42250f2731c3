# Clarity Engine - Calculator Architecture

## Overview
The calculator engine is the core business logic module that performs construction cost calculations based on Indian market standards and engineering practices.

## Architecture

### Entry Point
- `index.ts` - Main exports and public API
- `engine.ts` - Core calculation orchestrator

### Calculation Modules
- `calculations/structure.ts` - Structural work calculations (foundation, RCC)
- `calculations/finishing.ts` - Finishing work calculations (flooring, painting, doors/windows)
- `calculations/mep.ts` - MEP calculations (electrical, plumbing, HVAC)

### Support Modules
- `types.ts` - TypeScript type definitions
- `constants.ts` - Industry constants and rates
- `validation.ts` - Input validation and sanitization

## Key Features

### Quality Tiers
1. **Smart Choice** (₹1,600-2,000/sqft)
   - Standard finishes, local materials
   - M20 concrete, Fe415 steel
   - Basic fixtures and fittings

2. **Premium Selection** (₹2,200-2,800/sqft)
   - Branded materials, better finishes
   - M25 concrete, Fe500 steel
   - Kohler/American Standard fixtures

3. **Luxury Collection** (₹3,000-4,000+/sqft)
   - Premium brands, high-end finishes
   - M30+ concrete, Fe500D steel
   - Grohe/Hansgrohe fixtures

### Regional Coverage
- 15+ major Indian cities with regional multipliers
- Mumbai (1.25x), Delhi (1.15x), Bangalore (1.0x base)
- Tier 2/3 city support with appropriate cost adjustments

### Cost Breakdown
- **Structure (35%)**: Foundation, columns, beams, slabs
- **Finishing (30%)**: Flooring, painting, doors, windows
- **MEP (20%)**: Electrical, plumbing, HVAC
- **External (10%)**: Compound wall, landscaping
- **Other (5%)**: Contingency, supervision, approvals

## Usage Examples

### Basic Calculation
```typescript
import { calculate } from '@/core/calculator'

const result = calculate({
  builtUpArea: 1200,
  floors: 1,
  qualityTier: 'premium',
  location: 'bangalore'
})

console.log(`Total Cost: ₹${result.totalCost.toLocaleString()}`)
console.log(`Cost per sqft: ₹${result.costPerSqft}`)
```

### Quick Estimate
```typescript
import { quickCalculate } from '@/core/calculator'

const { totalCost, costPerSqft } = quickCalculate(1000, 0, 'smart', 'mumbai')
```

### Validation
```typescript
import { validateInput, sanitizeInput } from '@/core/calculator'

const input = sanitizeInput(userInput)
const errors = validateInput(input)

if (errors.length > 0) {
  console.log('Validation errors:', errors)
}
```

## Material Calculations

### Standard Consumption Rates (per sqft)
- **Cement**: 0.38 bags (50kg) - varies by quality tier
- **Steel**: 4.0 kg - Fe415/Fe500/Fe500D based on tier
- **Bricks**: 8 pieces - includes 5% wastage
- **Sand**: 0.816 cft - includes 20% wastage
- **Aggregate**: 0.608 cft - includes 15% wastage

### Wastage Factors
- Cement: 2%
- Steel: 5% (cutting, bending losses)
- Bricks: 5% (breakage)
- Tiles: 10% (cutting, breakage)
- Paint: 15% (absorption, spillage)

## Construction Timeline

Standard phases with duration in weeks:
1. Site Preparation (1 week)
2. Foundation Work (2 weeks)
3. Superstructure (3 weeks per floor)
4. Masonry Work (4 weeks)
5. MEP Work (3 weeks)
6. Finishing Work (6 weeks)

Total duration: ~20-30 weeks depending on floors and complexity.

## Validation Rules

### Input Validation
- Area: 500 - 50,000 sqft
- Floors: 0 - 10 (Ground to G+10)
- Quality tier: smart | premium | luxury
- Location: Must be supported city or region

### Cost Validation
- Smart: ₹1,200-2,500 per sqft
- Premium: ₹2,000-3,500 per sqft
- Luxury: ₹2,800-5,000 per sqft

## Error Handling

The calculator includes comprehensive error handling:
- Input validation with specific error codes
- Cost reasonableness checks
- Material quantity validation
- Location support verification

## Standards Compliance

Based on Indian Standards (IS):
- IS 456: Concrete structures
- IS 1893: Earthquake resistant design
- IS 12269: Cement specifications
- IS 1786: Steel reinforcement
- NBC: National Building Code

## Future Enhancements

1. **Real-time Pricing**: Integration with material suppliers
2. **Regional Variations**: More granular location-based pricing
3. **Seasonal Adjustments**: Monsoon and peak season multipliers
4. **Sustainability Index**: Green building cost premiums
5. **3D Visualization**: Cost mapping to building components