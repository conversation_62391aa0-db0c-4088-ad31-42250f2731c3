# Dependencies
node_modules/
.pnp
.pnp.js

# Testing
coverage/
.nyc_output/
test-results/
playwright-report/
playwright/.cache/
*.log

# Next.js
.next/
out/
build/
dist/

# Production
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Cache
.turbo
.cache
.vercel
tsconfig.tsbuildinfo

# Environment
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Temporary
tmp/
temp/
*.tmp
*.pid

# Build artifacts
storybook-static/

# Reports (generated)
*-report-*.json
*-report-*.html
lighthouse-*.html
bundle-report.*
security-validation-results.json
