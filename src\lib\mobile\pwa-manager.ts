/**
 * Progressive Web App Manager
 * Handles PWA features, offline functionality, and installation
 */

interface PWAConfig {
  enableOffline?: boolean;
  cacheStrategy?: 'cache-first' | 'network-first' | 'stale-while-revalidate';
  offlineMessage?: string;
  updateAvailableMessage?: string;
  installPromptMessage?: string;
}

interface BeforeInstallPromptEvent extends Event {
  readonly platforms: string[];
  readonly userChoice: Promise<{
    outcome: 'accepted' | 'dismissed';
    platform: string;
  }>;
  prompt(): Promise<void>;
}

class PWAManager {
  private config: Required<PWAConfig>;
  private deferredPrompt: BeforeInstallPromptEvent | null = null;
  private isInstalled = false;
  private isOnline = navigator.onLine;
  private cache: Cache | null = null;
  private cacheName = 'nirmaan-calculator-v1';
  private offlineQueue: Array<{ url: string; options: RequestInit }> = [];
  private callbacks: Map<string, Function[]> = new Map();

  constructor(config: Partial<PWAConfig> = {}) {
    this.config = {
      enableOffline: true,
      cacheStrategy: 'stale-while-revalidate',
      offlineMessage: 'You are currently offline. Some features may be limited.',
      updateAvailableMessage: 'A new version is available. Refresh to update.',
      installPromptMessage: 'Install Nirmaan Calculator for the best experience!',
      ...config
    };

    this.init();
  }

  private async init(): Promise<void> {
    this.checkInstallation();
    this.setupServiceWorker();
    this.setupOfflineHandling();
    this.setupInstallPrompt();
    this.setupNetworkMonitoring();
    await this.initializeCache();
    this.processOfflineQueue();
  }

  private checkInstallation(): void {
    this.isInstalled = 
      window.matchMedia('(display-mode: standalone)').matches ||
      (window.navigator as any).standalone ||
      document.referrer.includes('android-app://');

    if (this.isInstalled) {
      document.documentElement.classList.add('pwa-installed');
      this.emit('installed', { isInstalled: true });
    }
  }

  private setupServiceWorker(): void {
    if ('serviceWorker' in navigator) {
      window.addEventListener('load', async () => {
        try {
          const registration = await navigator.serviceWorker.register('/sw.js');
          
          registration.addEventListener('updatefound', () => {
            const newWorker = registration.installing;
            if (newWorker) {
              newWorker.addEventListener('statechange', () => {
                if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                  this.showUpdateNotification();
                }
              });
            }
          });

          this.emit('serviceWorkerRegistered', { registration });
        } catch (error) {
          console.warn('Service worker registration failed:', error);
        }
      });
    }
  }

  private setupOfflineHandling(): void {
    if (!this.config.enableOffline) return;

    // Intercept fetch requests for offline handling
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.addEventListener('message', (event) => {
        if (event.data.type === 'OFFLINE_REQUEST') {
          this.offlineQueue.push(event.data.request);
          this.emit('offlineRequest', event.data.request);
        }
      });
    }
  }

  private setupInstallPrompt(): void {
    window.addEventListener('beforeinstallprompt', (event) => {
      event.preventDefault();
      this.deferredPrompt = event as BeforeInstallPromptEvent;
      this.showInstallBanner();
      this.emit('installPromptAvailable', { prompt: this.deferredPrompt });
    });

    window.addEventListener('appinstalled', () => {
      this.isInstalled = true;
      this.deferredPrompt = null;
      document.documentElement.classList.add('pwa-installed');
      this.hideInstallBanner();
      this.emit('installed', { isInstalled: true });
    });
  }

  private setupNetworkMonitoring(): void {
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.hideOfflineNotification();
      this.processOfflineQueue();
      this.emit('online', { isOnline: true });
    });

    window.addEventListener('offline', () => {
      this.isOnline = false;
      this.showOfflineNotification();
      this.emit('offline', { isOnline: false });
    });
  }

  private async initializeCache(): Promise<void> {
    if (!this.config.enableOffline) return;

    try {
      this.cache = await caches.open(this.cacheName);
      
      // Cache essential resources
      const essentialResources = [
        '/',
        '/calculator',
        '/offline.html',
        '/manifest.json',
        '/icons/icon-192x192.png',
        '/icons/icon-512x512.png'
      ];

      await this.cache.addAll(essentialResources);
    } catch (error) {
      console.warn('Cache initialization failed:', error);
    }
  }

  private async processOfflineQueue(): Promise<void> {
    if (!this.isOnline || this.offlineQueue.length === 0) return;

    const queue = [...this.offlineQueue];
    this.offlineQueue = [];

    for (const request of queue) {
      try {
        await fetch(request.url, request.options);
        this.emit('offlineRequestProcessed', request);
      } catch (error) {
        console.warn('Failed to process offline request:', error);
        this.offlineQueue.push(request); // Re-queue if failed
      }
    }
  }

  private showInstallBanner(): void {
    const existingBanner = document.getElementById('pwa-install-banner');
    if (existingBanner) return;

    const banner = document.createElement('div');
    banner.id = 'pwa-install-banner';
    banner.className = 'pwa-install-banner';
    banner.innerHTML = `
      <div class="banner-content">
        <div class="banner-icon">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
            <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
          </svg>
        </div>
        <div class="banner-text">
          <strong>Install App</strong>
          <p>${this.config.installPromptMessage}</p>
        </div>
        <div class="banner-actions">
          <button class="banner-install">Install</button>
          <button class="banner-close">×</button>
        </div>
      </div>
    `;

    banner.style.cssText = `
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 16px;
      box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.15);
      z-index: 10000;
      transform: translateY(100%);
      transition: transform 0.3s ease;
      backdrop-filter: blur(10px);
    `;

    const bannerContent = banner.querySelector('.banner-content') as HTMLElement;
    bannerContent.style.cssText = `
      display: flex;
      align-items: center;
      gap: 12px;
      max-width: 500px;
      margin: 0 auto;
    `;

    const installButton = banner.querySelector('.banner-install') as HTMLButtonElement;
    const closeButton = banner.querySelector('.banner-close') as HTMLButtonElement;

    installButton.style.cssText = `
      background: rgba(255, 255, 255, 0.2);
      border: 1px solid rgba(255, 255, 255, 0.3);
      color: white;
      padding: 8px 16px;
      border-radius: 20px;
      font-weight: 500;
      cursor: pointer;
      backdrop-filter: blur(10px);
    `;

    closeButton.style.cssText = `
      background: none;
      border: none;
      color: white;
      font-size: 20px;
      cursor: pointer;
      padding: 4px;
      margin-left: auto;
    `;

    installButton.addEventListener('click', () => {
      this.promptInstall();
    });

    closeButton.addEventListener('click', () => {
      this.hideInstallBanner();
    });

    document.body.appendChild(banner);

    // Animate in
    requestAnimationFrame(() => {
      banner.style.transform = 'translateY(0)';
    });
  }

  private hideInstallBanner(): void {
    const banner = document.getElementById('pwa-install-banner');
    if (banner) {
      banner.style.transform = 'translateY(100%)';
      setTimeout(() => {
        banner.remove();
      }, 300);
    }
  }

  private showOfflineNotification(): void {
    const existingNotification = document.getElementById('pwa-offline-notification');
    if (existingNotification) return;

    const notification = document.createElement('div');
    notification.id = 'pwa-offline-notification';
    notification.className = 'pwa-offline-notification';
    notification.innerHTML = `
      <div class="notification-content">
        <div class="notification-icon">📡</div>
        <span>${this.config.offlineMessage}</span>
      </div>
    `;

    notification.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      background: #ff6b6b;
      color: white;
      padding: 12px;
      text-align: center;
      z-index: 10001;
      transform: translateY(-100%);
      transition: transform 0.3s ease;
      font-size: 14px;
    `;

    document.body.appendChild(notification);

    requestAnimationFrame(() => {
      notification.style.transform = 'translateY(0)';
    });
  }

  private hideOfflineNotification(): void {
    const notification = document.getElementById('pwa-offline-notification');
    if (notification) {
      notification.style.transform = 'translateY(-100%)';
      setTimeout(() => {
        notification.remove();
      }, 300);
    }
  }

  private showUpdateNotification(): void {
    const notification = document.createElement('div');
    notification.className = 'pwa-update-notification';
    notification.innerHTML = `
      <div class="notification-content">
        <span>${this.config.updateAvailableMessage}</span>
        <button class="update-button">Refresh</button>
      </div>
    `;

    notification.style.cssText = `
      position: fixed;
      top: 16px;
      right: 16px;
      background: #4CAF50;
      color: white;
      padding: 16px;
      border-radius: 8px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
      z-index: 10002;
      max-width: 300px;
    `;

    const updateButton = notification.querySelector('.update-button') as HTMLButtonElement;
    updateButton.style.cssText = `
      background: rgba(255, 255, 255, 0.2);
      border: 1px solid rgba(255, 255, 255, 0.3);
      color: white;
      padding: 8px 16px;
      border-radius: 4px;
      margin-left: 12px;
      cursor: pointer;
    `;

    updateButton.addEventListener('click', () => {
      window.location.reload();
    });

    document.body.appendChild(notification);

    // Auto-hide after 10 seconds
    setTimeout(() => {
      notification.remove();
    }, 10000);
  }

  // Public API
  async promptInstall(): Promise<boolean> {
    if (!this.deferredPrompt) {
      console.warn('Install prompt not available');
      return false;
    }

    try {
      await this.deferredPrompt.prompt();
      const choiceResult = await this.deferredPrompt.userChoice;
      
      if (choiceResult.outcome === 'accepted') {
        this.hideInstallBanner();
        this.emit('installAccepted', choiceResult);
        return true;
      } else {
        this.emit('installDismissed', choiceResult);
        return false;
      }
    } catch (error) {
      console.warn('Install prompt failed:', error);
      return false;
    }
  }

  isAppInstalled(): boolean {
    return this.isInstalled;
  }

  isInstallPromptAvailable(): boolean {
    return this.deferredPrompt !== null;
  }

  getNetworkStatus(): boolean {
    return this.isOnline;
  }

  getOfflineQueueLength(): number {
    return this.offlineQueue.length;
  }

  async cacheResource(url: string): Promise<boolean> {
    if (!this.cache) return false;

    try {
      await this.cache.add(url);
      return true;
    } catch (error) {
      console.warn('Failed to cache resource:', error);
      return false;
    }
  }

  async getCachedResource(url: string): Promise<Response | undefined> {
    if (!this.cache) return undefined;

    try {
      return await this.cache.match(url);
    } catch (error) {
      console.warn('Failed to get cached resource:', error);
      return undefined;
    }
  }

  async clearCache(): Promise<boolean> {
    try {
      const cacheNames = await caches.keys();
      await Promise.all(
        cacheNames.map(name => caches.delete(name))
      );
      return true;
    } catch (error) {
      console.warn('Failed to clear cache:', error);
      return false;
    }
  }

  updateConfig(newConfig: Partial<PWAConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  // Event system
  on(eventName: string, callback: Function): void {
    if (!this.callbacks.has(eventName)) {
      this.callbacks.set(eventName, []);
    }
    this.callbacks.get(eventName)!.push(callback);
  }

  off(eventName: string, callback?: Function): void {
    if (!callback) {
      this.callbacks.delete(eventName);
      return;
    }

    const callbacks = this.callbacks.get(eventName);
    if (callbacks) {
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  private emit(eventName: string, data: any): void {
    const callbacks = this.callbacks.get(eventName);
    if (callbacks) {
      callbacks.forEach(callback => callback(data));
    }
  }
}

// Create singleton instance
const pwaManager = new PWAManager();

export default pwaManager;
export { PWAManager };
export type { PWAConfig, BeforeInstallPromptEvent };