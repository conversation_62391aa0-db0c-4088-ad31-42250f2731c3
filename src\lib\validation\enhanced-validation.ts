/**
 * Enhanced Validation System
 * Smart validation with contextual warnings and construction feasibility checks
 */

import { CalculatorFormData, StepValidation } from '@/components/calculator/types/wizard';

export interface EnhancedValidation extends StepValidation {
  warnings: Record<string, string>;
  suggestions: Record<string, string>;
  feasibilityIssues: FeasibilityIssue[];
  marketAlerts: MarketAlert[];
}

export interface FeasibilityIssue {
  id: string;
  severity: 'critical' | 'major' | 'minor';
  category: 'structural' | 'legal' | 'practical' | 'financial';
  title: string;
  description: string;
  recommendation: string;
  affectedFields: (keyof CalculatorFormData)[];
}

export interface MarketAlert {
  id: string;
  type: 'price-trend' | 'regulation' | 'seasonal' | 'availability';
  urgency: 'high' | 'medium' | 'low';
  title: string;
  message: string;
  actionable: boolean;
}

export interface ValidationContext {
  location: string;
  currentMarketConditions?: {
    materialPriceInflation: number;
    laborAvailability: 'high' | 'medium' | 'low';
    seasonalFactor: number;
  };
  localRegulations?: {
    maxFloors: number;
    setbackRequirements: number;
    mandatoryFeatures: string[];
  };
}

export class EnhancedValidationEngine {
  private context: ValidationContext;

  constructor(context: ValidationContext) {
    this.context = context;
  }

  /**
   * Perform comprehensive validation
   */
  validateStep(
    data: Partial<CalculatorFormData>,
    step: 'basic' | 'rooms' | 'quality' | 'features'
  ): EnhancedValidation {
    const validation: EnhancedValidation = {
      isValid: true,
      errors: {},
      warnings: {},
      suggestions: {},
      feasibilityIssues: [],
      marketAlerts: [],
    };

    // Basic validation first
    this.performBasicValidation(data, step, validation);

    // Smart validation checks
    this.performSmartValidation(data, step, validation);

    // Feasibility checks
    this.performFeasibilityChecks(data, step, validation);

    // Market-based validation
    this.performMarketValidation(data, step, validation);

    // Regional compliance checks
    this.performRegionalValidation(data, step, validation);

    // Set overall validity
    validation.isValid = Object.keys(validation.errors).length === 0 &&
                        validation.feasibilityIssues.filter(i => i.severity === 'critical').length === 0;

    return validation;
  }

  /**
   * Basic field validation
   */
  private performBasicValidation(
    data: Partial<CalculatorFormData>,
    step: string,
    validation: EnhancedValidation
  ): void {
    switch (step) {
      case 'basic':
        this.validateBasicFields(data, validation);
        break;
      case 'rooms':
        this.validateRoomFields(data, validation);
        break;
      case 'quality':
        this.validateQualityFields(data, validation);
        break;
      case 'features':
        this.validateFeatureFields(data, validation);
        break;
    }
  }

  /**
   * Smart contextual validation
   */
  private performSmartValidation(
    data: Partial<CalculatorFormData>,
    step: string,
    validation: EnhancedValidation
  ): void {
    // Budget reality checks
    this.validateBudgetReality(data, validation);

    // Configuration logic checks
    this.validateConfigurationLogic(data, validation);

    // Construction practicality
    this.validateConstructionPracticality(data, validation);

    // Quality consistency
    this.validateQualityConsistency(data, validation);
  }

  /**
   * Construction feasibility checks
   */
  private performFeasibilityChecks(
    data: Partial<CalculatorFormData>,
    step: string,
    validation: EnhancedValidation
  ): void {
    // Structural feasibility
    this.checkStructuralFeasibility(data, validation);

    // Plot utilization
    this.checkPlotUtilization(data, validation);

    // Local building codes
    this.checkBuildingCodes(data, validation);

    // Timeline feasibility
    this.checkTimelineFeasibility(data, validation);
  }

  /**
   * Market-based validation
   */
  private performMarketValidation(
    data: Partial<CalculatorFormData>,
    step: string,
    validation: EnhancedValidation
  ): void {
    // Material availability
    this.checkMaterialAvailability(data, validation);

    // Seasonal considerations
    this.checkSeasonalFactors(data, validation);

    // Price trend alerts
    this.checkPriceTrends(data, validation);
  }

  /**
   * Regional compliance validation
   */
  private performRegionalValidation(
    data: Partial<CalculatorFormData>,
    step: string,
    validation: EnhancedValidation
  ): void {
    if (!this.context.localRegulations) return;

    const regulations = this.context.localRegulations;

    // Floor limit check
    if (data.floors && parseInt(data.floors) > regulations.maxFloors) {
      validation.errors.floors = `Maximum ${regulations.maxFloors} floors allowed in ${this.context.location}`;
    }

    // Mandatory features
    for (const feature of regulations.mandatoryFeatures) {
      if (feature === 'rainwaterHarvesting' && !data.rainwaterHarvesting) {
        validation.warnings.rainwaterHarvesting = 'Rainwater harvesting is mandatory in this location';
      }
    }
  }

  /**
   * Basic field validation methods
   */
  private validateBasicFields(data: Partial<CalculatorFormData>, validation: EnhancedValidation): void {
    // Plot size validation
    if (!data.plotSize) {
      validation.errors.plotSize = 'Plot size is required';
    } else {
      const plotSize = parseFloat(data.plotSize);
      if (plotSize < 300) {
        validation.errors.plotSize = 'Minimum plot size is 300 sq ft';
      } else if (plotSize > 50000) {
        validation.errors.plotSize = 'Maximum plot size is 50,000 sq ft';
      } else if (plotSize < 600) {
        validation.warnings.plotSize = 'Small plot size may limit construction options';
      }
    }

    // Built-up area validation
    if (data.builtUpArea && data.plotSize) {
      const builtUp = parseFloat(data.builtUpArea);
      const plot = parseFloat(data.plotSize);
      
      if (builtUp > plot) {
        validation.errors.builtUpArea = 'Built-up area cannot exceed plot size';
      } else if (builtUp > plot * 0.8) {
        validation.warnings.builtUpArea = 'High plot coverage may violate local setback requirements';
      } else if (builtUp < plot * 0.3) {
        validation.suggestions.builtUpArea = 'Consider utilizing more plot area for better value';
      }
    }

    // Floors validation
    if (!data.floors) {
      validation.errors.floors = 'Number of floors is required';
    } else {
      const floors = parseInt(data.floors);
      if (floors > 3 && !data.elevator) {
        validation.warnings.elevator = 'Consider adding elevator for 3+ floor buildings';
      }
    }
  }

  private validateRoomFields(data: Partial<CalculatorFormData>, validation: EnhancedValidation): void {
    const bedrooms = parseInt(data.bedrooms || '0');
    const bathrooms = parseInt(data.bathrooms || '0');

    // Bedroom-bathroom ratio
    if (bedrooms > 0 && bathrooms > 0) {
      if (bathrooms > bedrooms + 1) {
        validation.warnings.bathrooms = 'More bathrooms than bedrooms may be excessive';
      } else if (bedrooms >= 3 && bathrooms === 1) {
        validation.warnings.bathrooms = 'Consider adding more bathrooms for convenience';
      }
    }

    // Family size vs. room count
    if (bedrooms >= 5) {
      validation.suggestions.bedrooms = 'Large families benefit from additional storage and utility areas';
    }

    // Balcony recommendations
    if (data.balconies === '0' && bedrooms >= 2) {
      validation.suggestions.balconies = 'Balconies improve ventilation and add value';
    }
  }

  private validateQualityFields(data: Partial<CalculatorFormData>, validation: EnhancedValidation): void {
    // Quality-budget alignment
    if (data.quality && data.builtUpArea) {
      const area = parseFloat(data.builtUpArea);
      const qualityCosts = { smart: 1800, premium: 2500, luxury: 3500 };
      const estimatedCost = area * qualityCosts[data.quality as keyof typeof qualityCosts];

      if (estimatedCost > 10000000) { // 1 Crore+
        validation.warnings.quality = 'High-budget project - consider phased construction';
      }
    }

    // Material consistency
    if (data.quality === 'luxury' && data.flooringType === 'ceramic') {
      validation.suggestions.flooringType = 'Consider marble or premium tiles for luxury quality';
    }
  }

  private validateFeatureFields(data: Partial<CalculatorFormData>, validation: EnhancedValidation): void {
    // Swimming pool size check
    if (data.swimmingPool && data.plotSize) {
      const plotSize = parseFloat(data.plotSize);
      if (plotSize < 2000) {
        validation.warnings.swimmingPool = 'Limited space for pool - consider plunge pool option';
      }
    }

    // Generator necessity
    if (!data.generator && this.context.location === 'delhi') {
      validation.suggestions.generator = 'Backup power recommended due to frequent outages';
    }
  }

  /**
   * Smart validation methods
   */
  private validateBudgetReality(data: Partial<CalculatorFormData>, validation: EnhancedValidation): void {
    if (!data.builtUpArea || !data.quality) return;

    const area = parseFloat(data.builtUpArea);
    const qualityCosts = { smart: 1800, premium: 2500, luxury: 3500 };
    const baseCost = area * qualityCosts[data.quality as keyof typeof qualityCosts];

    // Add feature costs
    let additionalCosts = 0;
    if (data.swimmingPool) additionalCosts += 1150000;
    if (data.elevator) additionalCosts += 1600000;
    if (data.homeAutomation) additionalCosts += 400000;
    if (data.solarPanels) additionalCosts += 550000;

    const totalCost = baseCost + additionalCosts;

    if (totalCost > 20000000) { // 2 Crores+
      validation.feasibilityIssues.push({
        id: 'high-budget-warning',
        severity: 'major',
        category: 'financial',
        title: 'Very High Budget Project',
        description: `Estimated cost: ₹${(totalCost / 10000000).toFixed(1)} Cr`,
        recommendation: 'Consider phased construction or reducing scope',
        affectedFields: ['quality', 'swimmingPool', 'elevator'],
      });
    }
  }

  private validateConfigurationLogic(data: Partial<CalculatorFormData>, validation: EnhancedValidation): void {
    // Illogical combinations
    if (data.bedrooms === '1' && data.bathrooms && parseInt(data.bathrooms) > 2) {
      validation.warnings.bathrooms = 'Excessive bathrooms for 1-bedroom configuration';
    }

    // Missing essential rooms
    if (data.bedrooms && parseInt(data.bedrooms) >= 3 && data.storeRooms === '0') {
      validation.suggestions.storeRooms = 'Storage room recommended for larger homes';
    }
  }

  private validateConstructionPracticality(data: Partial<CalculatorFormData>, validation: EnhancedValidation): void {
    // Access and construction space
    if (data.plotSize && parseFloat(data.plotSize) < 800) {
      validation.feasibilityIssues.push({
        id: 'construction-access',
        severity: 'minor',
        category: 'practical',
        title: 'Limited Construction Access',
        description: 'Small plot may limit machinery access',
        recommendation: 'Plan for manual construction methods',
        affectedFields: ['plotSize'],
      });
    }

    // Structural requirements
    if (data.floors && parseInt(data.floors) >= 3 && data.quality === 'smart') {
      validation.warnings.quality = 'Higher floors may require premium structural quality';
    }
  }

  private validateQualityConsistency(data: Partial<CalculatorFormData>, validation: EnhancedValidation): void {
    // Inconsistent quality selections
    if (data.quality === 'luxury' && data.electricalFittings === 'standard') {
      validation.suggestions.electricalFittings = 'Consider premium electrical fittings for luxury quality';
    }

    if (data.quality === 'smart' && data.homeAutomation) {
      validation.warnings.homeAutomation = 'Home automation may not be cost-effective with basic quality';
    }
  }

  /**
   * Feasibility check methods
   */
  private checkStructuralFeasibility(data: Partial<CalculatorFormData>, validation: EnhancedValidation): void {
    if (data.floors && data.plotSize && data.swimmingPool) {
      const floors = parseInt(data.floors);
      const plotSize = parseFloat(data.plotSize);

      if (floors >= 3 && plotSize < 1500 && data.swimmingPool) {
        validation.feasibilityIssues.push({
          id: 'structural-complexity',
          severity: 'major',
          category: 'structural',
          title: 'Complex Foundation Requirements',
          description: 'Multi-story building with pool requires specialized foundation',
          recommendation: 'Consult structural engineer for foundation design',
          affectedFields: ['floors', 'swimmingPool'],
        });
      }
    }
  }

  private checkPlotUtilization(data: Partial<CalculatorFormData>, validation: EnhancedValidation): void {
    if (!data.plotSize || !data.builtUpArea) return;

    const plotSize = parseFloat(data.plotSize);
    const builtUp = parseFloat(data.builtUpArea);
    const utilization = builtUp / plotSize;

    if (utilization > 0.7) {
      validation.feasibilityIssues.push({
        id: 'setback-violation',
        severity: 'critical',
        category: 'legal',
        title: 'Possible Setback Violation',
        description: 'High plot coverage may violate local building regulations',
        recommendation: 'Check local setback requirements and reduce built-up area',
        affectedFields: ['builtUpArea', 'plotSize'],
      });
    }
  }

  private checkBuildingCodes(data: Partial<CalculatorFormData>, validation: EnhancedValidation): void {
    // Location-specific building code checks
    const locationCodes = {
      mumbai: { maxFloors: 4, mandatoryRainwater: true },
      delhi: { maxFloors: 4, mandatoryRainwater: true },
      bangalore: { maxFloors: 3, mandatoryRainwater: true },
    };

    const codes = locationCodes[this.context.location as keyof typeof locationCodes];
    if (!codes) return;

    if (data.floors && parseInt(data.floors) > codes.maxFloors) {
      validation.feasibilityIssues.push({
        id: 'building-code-floors',
        severity: 'critical',
        category: 'legal',
        title: 'Building Code Violation',
        description: `Maximum ${codes.maxFloors} floors allowed in ${this.context.location}`,
        recommendation: 'Reduce number of floors or obtain special permissions',
        affectedFields: ['floors'],
      });
    }

    if (codes.mandatoryRainwater && !data.rainwaterHarvesting) {
      validation.warnings.rainwaterHarvesting = 'Rainwater harvesting is mandatory in this location';
    }
  }

  private checkTimelineFeasibility(data: Partial<CalculatorFormData>, validation: EnhancedValidation): void {
    if (!data.builtUpArea) return;

    const area = parseFloat(data.builtUpArea);
    let timelineMonths = Math.ceil(area / 500); // Rough estimate: 500 sqft per month

    // Adjust for complexity
    if (data.swimmingPool) timelineMonths += 2;
    if (data.elevator) timelineMonths += 1;
    if (parseInt(data.floors || '1') >= 3) timelineMonths += 1;

    if (timelineMonths > 18) {
      validation.marketAlerts.push({
        id: 'long-timeline',
        type: 'seasonal',
        urgency: 'medium',
        title: 'Extended Timeline',
        message: `Estimated ${timelineMonths} months - plan for monsoon delays`,
        actionable: true,
      });
    }
  }

  /**
   * Market validation methods
   */
  private checkMaterialAvailability(data: Partial<CalculatorFormData>, validation: EnhancedValidation): void {
    if (data.quality === 'luxury') {
      validation.marketAlerts.push({
        id: 'luxury-materials',
        type: 'availability',
        urgency: 'low',
        title: 'Premium Material Lead Time',
        message: 'Luxury finishes may have 2-3 month lead times',
        actionable: true,
      });
    }
  }

  private checkSeasonalFactors(data: Partial<CalculatorFormData>, validation: EnhancedValidation): void {
    const currentMonth = new Date().getMonth();
    const isMonsoonSeason = currentMonth >= 5 && currentMonth <= 9;

    if (isMonsoonSeason) {
      validation.marketAlerts.push({
        id: 'monsoon-season',
        type: 'seasonal',
        urgency: 'high',
        title: 'Monsoon Construction Challenges',
        message: 'Foundation and structural work may be affected by rain',
        actionable: true,
      });
    }
  }

  private checkPriceTrends(data: Partial<CalculatorFormData>, validation: EnhancedValidation): void {
    // Simulated price trend data
    const currentInflation = 0.08; // 8% yearly inflation

    if (currentInflation > 0.10) {
      validation.marketAlerts.push({
        id: 'material-inflation',
        type: 'price-trend',
        urgency: 'high',
        title: 'High Material Inflation',
        message: `Material costs rising at ${(currentInflation * 100).toFixed(1)}% annually`,
        actionable: true,
      });
    }
  }
}

// Validation factory function
export function createValidator(context: ValidationContext): EnhancedValidationEngine {
  return new EnhancedValidationEngine(context);
}

// Quick validation helper
export function validateQuick(
  data: Partial<CalculatorFormData>,
  step: 'basic' | 'rooms' | 'quality' | 'features',
  location: string = 'delhi'
): EnhancedValidation {
  const validator = new EnhancedValidationEngine({ location });
  return validator.validateStep(data, step);
}