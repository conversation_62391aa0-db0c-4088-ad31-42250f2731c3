import Link from 'next/link';

export function SimpleHeader() {
  return (
    <header className="bg-white shadow-sm border-b" data-testid="header" role="banner">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="flex h-16 items-center justify-between">
          <div className="flex items-center" data-testid="header-logo-section">
            <Link href="/" className="flex items-center space-x-2" data-testid="header-logo-link" aria-label="Clarity Engine home">
              <div className="h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center" data-testid="header-logo-icon">
                <span className="text-white font-bold text-lg">C</span>
              </div>
              <span className="text-xl font-bold text-gray-900" data-testid="header-logo-text">Clarity Engine</span>
            </Link>
          </div>
          
          <nav className="hidden md:flex space-x-8" data-testid="header-navigation" role="navigation" aria-label="Main navigation">
            <Link href="/calculator" className="text-gray-700 hover:text-blue-600" data-testid="nav-calculator" aria-label="Construction Calculator">
              Calculator
            </Link>
            <Link href="/materials" className="text-gray-700 hover:text-blue-600" data-testid="nav-materials" aria-label="Materials Database">
              Materials
            </Link>
            <Link href="/about" className="text-gray-700 hover:text-blue-600" data-testid="nav-about" aria-label="About Clarity Engine">
              About
            </Link>
          </nav>
          
          <div className="flex items-center space-x-4" data-testid="header-actions">
            <button 
              className="text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium"
              data-testid="header-signin-button"
              aria-label="Sign in to your account"
            >
              Sign In
            </button>
          </div>
        </div>
      </div>
    </header>
  );
}