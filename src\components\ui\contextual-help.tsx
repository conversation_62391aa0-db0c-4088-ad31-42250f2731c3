/**
 * Contextual Help System
 * Just-in-time help and explanations with progressive disclosure
 */

import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence, type Variants } from 'framer-motion';
import { 
  HelpCircle, 
  Info, 
  Lightbulb, 
  AlertTriangle, 
  TrendingUp,
  Users,
  Calculator,
  Eye,
  EyeOff,
  X,
  ChevronDown,
  ChevronRight,
  ExternalLink
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { EnhancedCard } from '@/components/ui/enhanced-card';

export interface HelpContent {
  id: string;
  type: 'definition' | 'explanation' | 'tip' | 'warning' | 'calculation' | 'example';
  title: string;
  content: string;
  details?: string;
  links?: Array<{
    text: string;
    url: string;
    external?: boolean;
  }>;
  examples?: Array<{
    title: string;
    description: string;
    value?: string;
  }>;
  relatedTerms?: string[];
}

export interface HelpTriggerProps {
  helpId: string;
  content: HelpContent;
  trigger?: 'hover' | 'click' | 'focus';
  placement?: 'top' | 'bottom' | 'left' | 'right' | 'auto';
  size?: 'sm' | 'md' | 'lg';
  variant?: 'icon' | 'text' | 'inline';
  className?: string;
  children?: React.ReactNode;
}

export interface SmartBadgeProps {
  type: 'ai-suggestion' | 'popular-choice' | 'cost-saving' | 'recommended';
  confidence?: number;
  onClick?: () => void;
  className?: string;
}

export interface ProgressiveDisclosureProps {
  title: string;
  children: React.ReactNode;
  defaultOpen?: boolean;
  level?: 'beginner' | 'intermediate' | 'advanced';
  className?: string;
}

export interface ContextualTipProps {
  content: string;
  type?: 'info' | 'warning' | 'success' | 'tip';
  dismissible?: boolean;
  onDismiss?: () => void;
  className?: string;
}

// Animation variants
const tooltipVariants: Variants = {
  hidden: { 
    opacity: 0, 
    scale: 0.95, 
    y: 10 
  },
  visible: { 
    opacity: 1, 
    scale: 1, 
    y: 0,
    transition: {
      duration: 0.2,
      ease: 'easeOut'
    }
  },
  exit: { 
    opacity: 0, 
    scale: 0.95, 
    y: 10,
    transition: { duration: 0.15 }
  }
};

const badgeVariants: Variants = {
  hidden: { opacity: 0, scale: 0 },
  visible: { 
    opacity: 1, 
    scale: 1,
    transition: {
      type: 'spring',
      stiffness: 500,
      damping: 30
    }
  },
  hover: {
    scale: 1.05,
    transition: { duration: 0.2 }
  }
};

/**
 * Help Trigger Component
 * Shows contextual help on demand
 */
export function HelpTrigger({
  helpId,
  content,
  trigger = 'hover',
  placement = 'auto',
  size = 'sm',
  variant = 'icon',
  className,
  children
}: HelpTriggerProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [actualPlacement, setActualPlacement] = useState(placement);
  const triggerRef = useRef<HTMLButtonElement>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);

  // Calculate optimal placement
  useEffect(() => {
    if (isVisible && placement === 'auto' && triggerRef.current) {
      const rect = triggerRef.current.getBoundingClientRect();
      const spaceBelow = window.innerHeight - rect.bottom;
      const spaceAbove = rect.top;
      
      setActualPlacement(spaceBelow < 200 && spaceAbove > 200 ? 'top' : 'bottom');
    }
  }, [isVisible, placement]);

  const handleShow = () => {
    if (trigger === 'click' || trigger === 'focus') {
      setIsVisible(!isVisible);
    } else {
      setIsVisible(true);
    }
  };

  const handleHide = () => {
    if (trigger === 'hover') {
      setIsVisible(false);
    }
  };

  const getIcon = () => {
    switch (content.type) {
      case 'warning':
        return <AlertTriangle className="h-4 w-4" />;
      case 'tip':
        return <Lightbulb className="h-4 w-4" />;
      case 'calculation':
        return <Calculator className="h-4 w-4" />;
      default:
        return <HelpCircle className="h-4 w-4" />;
    }
  };

  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-5 w-5',
    lg: 'h-6 w-6'
  };

  return (
    <div className="relative inline-block">
      {variant === 'icon' ? (
        <Button
          ref={triggerRef}
          variant="ghost"
          size="sm"
          className={cn(
            'p-1 h-auto text-secondary-500 hover:text-secondary-700',
            sizeClasses[size],
            className
          )}
          onMouseEnter={trigger === 'hover' ? handleShow : undefined}
          onMouseLeave={trigger === 'hover' ? handleHide : undefined}
          onClick={trigger === 'click' ? handleShow : undefined}
          onFocus={trigger === 'focus' ? handleShow : undefined}
          onBlur={trigger === 'focus' ? handleHide : undefined}
          aria-describedby={`tooltip-${helpId}`}
        >
          {getIcon()}
        </Button>
      ) : (
        <button
          ref={triggerRef}
          className={cn(
            'inline-flex items-center gap-1 text-primary-600 hover:text-primary-700 underline',
            className
          )}
          onMouseEnter={trigger === 'hover' ? handleShow : undefined}
          onMouseLeave={trigger === 'hover' ? handleHide : undefined}
          onClick={trigger === 'click' ? handleShow : undefined}
        >
          {children}
          {getIcon()}
        </button>
      )}

      <AnimatePresence>
        {isVisible && (
          <motion.div
            ref={tooltipRef}
            id={`tooltip-${helpId}`}
            className={cn(
              'absolute z-50 w-80 p-4 bg-white border border-gray-200 rounded-lg shadow-lg',
              {
                'bottom-full mb-2': actualPlacement === 'top',
                'top-full mt-2': actualPlacement === 'bottom',
                'right-full mr-2': actualPlacement === 'left',
                'left-full ml-2': actualPlacement === 'right',
              }
            )}
            variants={tooltipVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            onMouseEnter={() => trigger === 'hover' && setIsVisible(true)}
            onMouseLeave={() => trigger === 'hover' && setIsVisible(false)}
          >
            <div className="space-y-3">
              {/* Header */}
              <div className="flex items-start justify-between">
                <h4 className="font-semibold text-gray-900 pr-2">
                  {content.title}
                </h4>
                {trigger === 'click' && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="p-1 h-auto text-gray-400 hover:text-gray-600"
                    onClick={() => setIsVisible(false)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                )}
              </div>

              {/* Content */}
              <p className="text-sm text-gray-600 leading-relaxed">
                {content.content}
              </p>

              {/* Details */}
              {content.details && (
                <div className="text-xs text-gray-500 border-t pt-2">
                  {content.details}
                </div>
              )}

              {/* Examples */}
              {content.examples && content.examples.length > 0 && (
                <div className="space-y-2">
                  <h5 className="text-xs font-medium text-gray-700 uppercase tracking-wider">
                    Examples
                  </h5>
                  {content.examples.map((example, index) => (
                    <div key={index} className="text-xs bg-gray-50 p-2 rounded">
                      <div className="font-medium text-gray-700">{example.title}</div>
                      <div className="text-gray-600">{example.description}</div>
                      {example.value && (
                        <div className="font-mono text-primary-600 mt-1">{example.value}</div>
                      )}
                    </div>
                  ))}
                </div>
              )}

              {/* Links */}
              {content.links && content.links.length > 0 && (
                <div className="flex flex-wrap gap-2 pt-2 border-t">
                  {content.links.map((link, index) => (
                    <a
                      key={index}
                      href={link.url}
                      target={link.external ? '_blank' : undefined}
                      rel={link.external ? 'noopener noreferrer' : undefined}
                      className="inline-flex items-center gap-1 text-xs text-primary-600 hover:text-primary-700"
                    >
                      {link.text}
                      {link.external && <ExternalLink className="h-3 w-3" />}
                    </a>
                  ))}
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}

/**
 * Smart Badge Component
 * Shows AI suggestions and recommendations
 */
export function SmartBadge({ 
  type, 
  confidence, 
  onClick, 
  className 
}: SmartBadgeProps) {
  const getBadgeConfig = () => {
    switch (type) {
      case 'ai-suggestion':
        return {
          icon: <Lightbulb className="h-3 w-3" />,
          text: 'AI Suggests',
          color: 'bg-blue-100 text-blue-700 border-blue-200',
        };
      case 'popular-choice':
        return {
          icon: <Users className="h-3 w-3" />,
          text: 'Popular',
          color: 'bg-green-100 text-green-700 border-green-200',
        };
      case 'cost-saving':
        return {
          icon: <TrendingUp className="h-3 w-3" />,
          text: 'Saves Money',
          color: 'bg-orange-100 text-orange-700 border-orange-200',
        };
      case 'recommended':
        return {
          icon: <Info className="h-3 w-3" />,
          text: 'Recommended',
          color: 'bg-purple-100 text-purple-700 border-purple-200',
        };
    }
  };

  const config = getBadgeConfig();

  return (
    <motion.button
      className={cn(
        'inline-flex items-center gap-1 px-2 py-1 text-xs font-medium rounded-full border',
        config.color,
        'hover:shadow-sm transition-shadow',
        className
      )}
      variants={badgeVariants}
      initial="hidden"
      animate="visible"
      whileHover="hover"
      onClick={onClick}
    >
      {config.icon}
      <span>{config.text}</span>
      {confidence && (
        <span className="ml-1 opacity-75">
          {Math.round(confidence * 100)}%
        </span>
      )}
    </motion.button>
  );
}

/**
 * Progressive Disclosure Component
 * Reveals advanced options based on user level
 */
export function ProgressiveDisclosure({
  title,
  children,
  defaultOpen = false,
  level = 'intermediate',
  className
}: ProgressiveDisclosureProps) {
  const [isOpen, setIsOpen] = useState(defaultOpen);

  const getLevelBadge = () => {
    const badges = {
      beginner: { text: 'Basic', color: 'bg-green-100 text-green-700' },
      intermediate: { text: 'Advanced', color: 'bg-yellow-100 text-yellow-700' },
      advanced: { text: 'Expert', color: 'bg-red-100 text-red-700' },
    };

    const badge = badges[level];
    return (
      <span className={cn('px-2 py-0.5 text-xs rounded-full', badge.color)}>
        {badge.text}
      </span>
    );
  };

  return (
    <div className={cn('border border-gray-200 rounded-lg', className)}>
      <button
        className="w-full flex items-center justify-between p-4 text-left hover:bg-gray-50"
        onClick={() => setIsOpen(!isOpen)}
      >
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            {isOpen ? (
              <ChevronDown className="h-4 w-4 text-gray-500" />
            ) : (
              <ChevronRight className="h-4 w-4 text-gray-500" />
            )}
            <span className="font-medium text-gray-900">{title}</span>
          </div>
          {getLevelBadge()}
        </div>
        <div className="flex items-center gap-2 text-sm text-gray-500">
          {isOpen ? (
            <>
              <Eye className="h-4 w-4" />
              <span>Hide</span>
            </>
          ) : (
            <>
              <EyeOff className="h-4 w-4" />
              <span>Show</span>
            </>
          )}
        </div>
      </button>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            className="border-t border-gray-200"
          >
            <div className="p-4">
              {children}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}

/**
 * Contextual Tip Component
 * Shows inline tips and warnings
 */
export function ContextualTip({
  content,
  type = 'info',
  dismissible = false,
  onDismiss,
  className
}: ContextualTipProps) {
  const [isDismissed, setIsDismissed] = useState(false);

  if (isDismissed) return null;

  const handleDismiss = () => {
    setIsDismissed(true);
    onDismiss?.();
  };

  const getTypeConfig = () => {
    switch (type) {
      case 'warning':
        return {
          icon: <AlertTriangle className="h-4 w-4" />,
          color: 'bg-yellow-50 border-yellow-200 text-yellow-800',
          iconColor: 'text-yellow-600',
        };
      case 'success':
        return {
          icon: <Info className="h-4 w-4" />,
          color: 'bg-green-50 border-green-200 text-green-800',
          iconColor: 'text-green-600',
        };
      case 'tip':
        return {
          icon: <Lightbulb className="h-4 w-4" />,
          color: 'bg-blue-50 border-blue-200 text-blue-800',
          iconColor: 'text-blue-600',
        };
      default:
        return {
          icon: <Info className="h-4 w-4" />,
          color: 'bg-gray-50 border-gray-200 text-gray-800',
          iconColor: 'text-gray-600',
        };
    }
  };

  const config = getTypeConfig();

  return (
    <motion.div
      className={cn(
        'flex items-start gap-3 p-3 border rounded-lg',
        config.color,
        className
      )}
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
    >
      <div className={cn('flex-shrink-0 mt-0.5', config.iconColor)}>
        {config.icon}
      </div>
      <p className="flex-1 text-sm leading-relaxed">{content}</p>
      {dismissible && (
        <Button
          variant="ghost"
          size="sm"
          className="p-1 h-auto -mt-1 -mr-1 text-gray-400 hover:text-gray-600"
          onClick={handleDismiss}
        >
          <X className="h-4 w-4" />
        </Button>
      )}
    </motion.div>
  );
}

/**
 * Help Content Repository
 * Predefined help content for common terms
 */
export const helpContent: Record<string, HelpContent> = {
  plotSize: {
    id: 'plot-size',
    type: 'definition',
    title: 'Plot Size',
    content: 'The total area of your land measured in square feet. This includes the entire property boundary.',
    details: 'Plot size determines how much you can build and affects the overall project cost.',
    examples: [
      { title: '30x40 plot', description: 'Common residential plot', value: '1,200 sq ft' },
      { title: '40x60 plot', description: 'Spacious family home', value: '2,400 sq ft' },
    ]
  },
  builtUpArea: {
    id: 'built-up-area',
    type: 'definition',
    title: 'Built-up Area',
    content: 'The total covered area of your construction including all floors, measured in square feet.',
    details: 'This includes all rooms, walls, corridors, and covered areas but excludes open balconies and terraces.',
    examples: [
      { title: 'Ground floor only', description: '60% of plot size', value: '720 sq ft (for 1,200 sq ft plot)' },
      { title: 'Ground + First floor', description: '120% of plot size', value: '1,440 sq ft (for 1,200 sq ft plot)' },
    ]
  },
  qualityTiers: {
    id: 'quality-tiers',
    type: 'explanation',
    title: 'Quality Tiers',
    content: 'Different levels of materials and finishes that affect both cost and durability of your construction.',
    details: 'Higher tiers use better materials, branded fixtures, and provide better long-term value.',
    examples: [
      { title: 'Smart Choice', description: 'Standard quality, good value', value: '₹1,600-2,000/sq ft' },
      { title: 'Premium Selection', description: 'Branded materials, better finishes', value: '₹2,200-2,800/sq ft' },
      { title: 'Luxury Collection', description: 'Premium brands, luxury finishes', value: '₹3,000+/sq ft' },
    ]
  },
  solarPanels: {
    id: 'solar-panels',
    type: 'tip',
    title: 'Solar Panel Benefits',
    content: 'Solar panels reduce electricity bills and provide clean energy. Typical payback period is 4-5 years.',
    details: 'Government subsidies and net metering make solar installations very attractive for homeowners.',
    examples: [
      { title: '3kW system', description: 'Suitable for 2-3BR homes', value: '₹2.5-3L after subsidy' },
      { title: '5kW system', description: 'Suitable for larger homes', value: '₹4-5L after subsidy' },
    ],
    links: [
      { text: 'Government Solar Subsidy', url: 'https://solarrooftop.gov.in/', external: true }
    ]
  }
};

export default HelpTrigger;