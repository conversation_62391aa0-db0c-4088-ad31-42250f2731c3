# Clarity Engine - Final Granular Execution Plan v3.0
**The Master Implementation Guide for AI Agents**

## Document Purpose & Usage
This is the SINGLE SOURCE OF TRUTH for building Clarity Engine. Every AI agent must:

- Read this document completely before starting any task
- Update STATUS.md after completing each micro-task
- Reference linked documentation sections as specified
- Follow the exact file paths and naming conventions

**Vision**: Build India's most accurate, transparent, and user-friendly construction cost calculator using AI agents in parallel execution.

## Quick Reference to Core Documents
```typescript
const documentReferences = {
  productRequirements: "Master PRD v2.0 - Pages 1-189",
  technicalDesign: "Technical Design Document v2.0 - Pages 1-165",
  businessContext: "Business Requirements Document v2.0 - Pages 1-142",
  designSystem: "Design System & UI Component Library v2.0 - Pages 1-198",
  testingPlan: "Testing & QA Plan v2.0 - Pages 1-215",
  goToMarket: "Go-To-Market Strategy v2.0 - Pages 1-178"
};

## Project Configuration & Structure

### Repository Setup
bash# Repository name: clarity-engine
# Structure: Monorepo with clear boundaries

clarity-engine/
├── .github/
│   ├── workflows/
│   │   ├── ci.yml                 # CI/CD pipeline
│   │   └── deploy.yml             # Deployment workflow
├── _agents/                       # AI Agent workspace
│   ├── context/
│   │   ├── STATUS.md             # Real-time progress tracker
│   │   ├── ARCHITECTURE.md       # System design decisions
│   │   ├── DECISIONS.md          # Technical decisions log
│   │   └── BLOCKERS.md           # Current blockers
│   ├── tasks/
│   │   ├── completed/            # Finished task files
│   │   ├── in-progress/          # Current task files
│   │   └── backlog/              # Future tasks
│   └── prompts/                  # Reusable prompts
├── src/
│   ├── app/                      # Next.js 14 app directory
│   ├── components/               # Reusable UI components
│   ├── core/                     # Business logic
│   │   ├── calculator/          # Calculation engine
│   │   ├── materials/           # Material management
│   │   └── pricing/             # Pricing logic
│   ├── lib/                     # Utilities and helpers
│   ├── types/                   # TypeScript definitions
│   └── styles/                  # Global styles
├── data/
│   ├── materials/               # Material database
│   └── locations/               # Location data
├── tests/
│   ├── unit/                    # Unit tests
│   ├── integration/             # Integration tests
│   └── e2e/                     # End-to-end tests
├── public/                      # Static assets
└── docs/                        # Documentation

## Agent Architecture & Responsibilities

### Agent Distribution Model
typescriptconst agentModel = {
  maxParallelAgents: 4, // Manageable number
  contextWindow: "200K tokens",
  contextPreservation: "STATUS.md + task files",

  agents: {
    ORCHESTRATOR: {
      id: "ORCH",
      role: "Coordinator and merger",
      keyFile: "_agents/context/STATUS.md"
    },

    FOUNDATION: {
      id: "FOUND",
      role: "Setup, infrastructure, tooling",
      branch: "feature/foundation"
    },

    CALCULATOR: {
      id: "CALC",
      role: "Calculation engine and business logic",
      branch: "feature/calculator"
    },

    INTERFACE: {
      id: "UI",
      role: "User interface and components",
      branch: "feature/interface"
    },

    QUALITY: {
      id: "QA",
      role: "Testing and quality assurance",
      branch: "feature/testing"
    }
  }
};

## Context Management Protocol

### STATUS.md Template
markdown# Clarity Engine - Master Status Tracker
Last Updated: [YYYY-MM-DD HH:MM IST]

## Current Sprint
- Day: [1-10]
- Phase: [Foundation/Core/Integration/Polish]
- Overall Progress: [X]%

## Active Tasks
| Agent | Task ID | Description | Progress | Branch |
|-------|---------|-------------|----------|---------|
| FOUND | F-001   | Next.js setup | 100% | feature/foundation |
| CALC  | C-001   | Core formulas | 60%  | feature/calculator |

## Last 4 Hours Progress
- [HH:MM] FOUND: Completed Next.js setup, all tests passing
- [HH:MM] CALC: Implemented structure cost calculation
- [HH:MM] UI: Created Button and Input components
- [HH:MM] ORCH: Merged foundation branch to develop

## Blockers
- None | [Describe any blocking issues]

## Integration Points Ready
- Calculator API: `/src/core/calculator/index.ts` ✅
- UI Components: `/src/components/base/` ⏳
- Database Schema: `/src/lib/db/schema.ts` ❌

## Next Critical Path
1. Complete calculator core functions (CALC)
2. Wire up basic UI form (UI)
3. Connect UI to calculator (ORCH)

## Important Decisions
- Using Tailwind for styling (ref: DECISIONS.md)
- PostgreSQL for database (ref: ARCHITECTURE.md)
- Vitest for testing framework
### Update Protocol
typescriptconst updateProtocol = {
  frequency: "After each micro-task (30-60 min)",
  format: "Append to relevant section",

  commitMessage: "[AGENT_ID] type: description",
  // Examples:
  // [CALC] feat: add material quantity calculations
  // [UI] fix: responsive layout issues
  // [ORCH] merge: calculator engine to develop

  beforeContextSwitch: [
    "Commit all changes",
    "Update STATUS.md",
    "Document any decisions",
    "Note any TODOs"
  ]
};

## 10-Day Execution Timeline

### Day 1-2: Foundation Phase

#### ORCHESTRATOR Tasks (Day 1)
typescriptconst orchestratorDay1 = {
  "ORCH-001": {
    title: "Initialize Repository and Agent Workspace",
    duration: "1 hour",
    priority: "CRITICAL",

    steps: [
      "1. Create GitHub repository 'clarity-engine'",
      "2. Clone locally and create initial structure",
      "3. Create _agents/ directory with all subdirectories",
      "4. Initialize STATUS.md with template",
      "5. Create .gitignore with proper exclusions",
      "6. Set up branch protection rules",
      "7. Create initial README.md"
    ],

    deliverables: [
      "_agents/context/STATUS.md",
      "_agents/context/ARCHITECTURE.md",
      "_agents/context/DECISIONS.md",
      ".gitignore",
      "README.md"
    ],

    verification: "Repository accessible, structure correct"
  },

  "ORCH-002": {
    title: "Assign Day 1 Tasks to Agents",
    duration: "30 minutes",

    assignments: {
      FOUNDATION: ["F-001", "F-002"],
      CALCULATOR: ["C-001"],
      INTERFACE: ["UI-001"]
    }
  }
};
#### FOUNDATION Agent Tasks (Day 1-2)
typescriptconst foundationTasks = {
  "F-001": {
    title: "Create Next.js 14 Project with TypeScript",
    duration: "2 hours",
    priority: "CRITICAL",
    references: ["Technical Design Doc - Section 3.1 Frontend Architecture"],

    prompt: `
Create a new Next.js 14 project with ALL modern best practices:

1. Initialize project:
   npx create-next-app@latest . --typescript --eslint --tailwind --app --src-dir --import-alias "@/*"

2. Configure TypeScript (tsconfig.json):
   - Strict mode enabled
   - Path aliases for @/components, @/lib, @/core, @/types
   - Target ES2022

3. Set up folder structure exactly as specified in project structure above

4. Configure Tailwind CSS (tailwind.config.ts):
   - Custom color palette from Design System doc
   - Custom spacing scale
   - Font configuration

5. Set up environment variables (.env.local):
   NEXT_PUBLIC_APP_URL=http://localhost:3000
   NEXT_PUBLIC_SUPABASE_URL=your-project-url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key

6. Create src/lib/utils.ts with cn() helper for className merging

7. Verify with: npm run dev

Update STATUS.md after completion.
    `,

    deliverables: [
      "package.json with all dependencies",
      "tsconfig.json properly configured",
      "tailwind.config.ts with design system",
      ".env.local with placeholders",
      "src/ directory structure"
    ]
  },

  "F-002": {
    title: "Set Up Development Tooling",
    duration: "1.5 hours",
    dependencies: ["F-001"],

    prompt: `
Configure all development tools for code quality:

1. Install and configure Prettier:
   npm install --save-dev prettier
   Create .prettierrc with project standards

2. Configure ESLint (.eslintrc.json):
   - Extend next/core-web-vitals
   - Add custom rules for consistency
   - Configure import sorting

3. Set up Husky pre-commit hooks:
   npm install --save-dev husky lint-staged
   npx husky init
   Configure to run linting and formatting

4. Install development dependencies:
   npm install --save-dev @types/node

5. Create VS Code workspace settings (.vscode/settings.json):
   - Format on save
   - ESLint auto-fix
   - Tailwind CSS IntelliSense

6. Set up Git hooks for commit message format

Test: Make a commit and verify hooks work
Update STATUS.md with tooling setup complete
    `
  },

  "F-003": {
    title: "Supabase Setup and Database Schema",
    duration: "2 hours",
    priority: "HIGH",
    references: ["Technical Design Doc - Section 4.2 Database Schema"],

    prompt: `
Set up Supabase and create initial schema:

1. Install Supabase client:
   npm install @supabase/supabase-js

2. Create src/lib/supabase/client.ts:
   - Browser client configuration
   - Proper TypeScript types

3. Create src/lib/supabase/server.ts:
   - Server client for API routes
   - Cookie handling

4. Create initial migration (supabase/migrations/):

   -- Users table (use Supabase auth.users)

   -- Projects table
   CREATE TABLE projects (
     id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
     user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
     name TEXT NOT NULL,
     location TEXT NOT NULL,
     area_sqft INTEGER NOT NULL,
     floors INTEGER NOT NULL,
     quality_tier TEXT CHECK (quality_tier IN ('smart', 'premium', 'luxury')),
     calculation_data JSONB NOT NULL,
     created_at TIMESTAMPTZ DEFAULT NOW(),
     updated_at TIMESTAMPTZ DEFAULT NOW()
   );

   -- Materials table
   CREATE TABLE materials (
     id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
     category TEXT NOT NULL,
     name TEXT NOT NULL,
     brand TEXT,
     unit TEXT NOT NULL,
     base_price DECIMAL(10,2),
     specifications JSONB,
     created_at TIMESTAMPTZ DEFAULT NOW()
   );

   -- Enable RLS
   ALTER TABLE projects ENABLE ROW LEVEL SECURITY;
   ALTER TABLE materials ENABLE ROW LEVEL SECURITY;

5. Create TypeScript types from schema

6. Test connection with a simple query

Update STATUS.md with database setup status
    `
  }
};
#### CALCULATOR Agent Tasks (Day 1-2)
typescriptconst calculatorTasks = {
  "C-001": {
    title: "Design Calculator Architecture",
    duration: "2 hours",
    priority: "CRITICAL",
    references: ["PRD - Section 7.1 Calculator Feature", "Technical Design - Section 5.1"],

    prompt: `
Create the calculator architecture in src/core/calculator/:

1. Create type definitions (src/core/calculator/types.ts):

export interface CalculationInput {
  plotArea?: number;
  builtUpArea: number;
  floors: number; // 0 = Ground, 1 = G+1, etc.
  qualityTier: 'smart' | 'premium' | 'luxury';
  location: string;
  hasStilt?: boolean;
  parkingType?: 'open' | 'covered';
}

export interface CalculationResult {
  totalCost: number;
  costPerSqft: number;
  breakdown: CostBreakdown;
  materials: MaterialQuantity[];
  timeline: ConstructionPhase[];
}

export interface CostBreakdown {
  structure: CategoryCost;
  finishing: CategoryCost;
  mep: CategoryCost;
  external: CategoryCost;
  other: CategoryCost;
}

export interface CategoryCost {
  amount: number;
  percentage: number;
  subCategories: SubCategory[];
}

2. Create constants (src/core/calculator/constants.ts):
   - Base rates per sqft by quality tier
   - Floor multipliers
   - Regional multipliers
   - Material consumption rates

3. Create calculation functions structure (src/core/calculator/index.ts):
   - Export main calculate() function
   - Individual calculation modules

4. Document the architecture in src/core/calculator/README.md

Update STATUS.md with architecture complete
    `
  },

  "C-002": {
    title: "Implement Core Calculation Functions",
    duration: "3 hours",
    dependencies: ["C-001"],
    priority: "CRITICAL",

    prompt: `
Implement the core calculation engine:

1. Create src/core/calculator/calculations/structure.ts:

export function calculateStructureCost(input: CalculationInput): CategoryCost {
  const { builtUpArea, floors, qualityTier } = input;

  // Base structure cost per sqft
  const baseRates = {
    smart: 600,
    premium: 750,
    luxury: 900
  };

  // Floor multipliers (higher floors need stronger structure)
  const floorMultipliers = {
    0: 1.0,   // Ground only
    1: 1.05,  // G+1
    2: 1.10,  // G+2
    3: 1.15,  // G+3
    4: 1.20   // G+4
  };

  const baseRate = baseRates[qualityTier];
  const floorMultiplier = floorMultipliers[Math.min(floors, 4)];
  const totalArea = builtUpArea * (floors + 1); // +1 for ground

  const structureCost = totalArea * baseRate * floorMultiplier;

  return {
    amount: Math.round(structureCost),
    percentage: 0, // Will be calculated at parent level
    subCategories: [
      { name: 'Foundation', amount: structureCost * 0.15 },
      { name: 'Columns & Beams', amount: structureCost * 0.25 },
      { name: 'Slabs', amount: structureCost * 0.35 },
      { name: 'Walls', amount: structureCost * 0.20 },
      { name: 'Staircase', amount: structureCost * 0.05 }
    ]
  };
}

2. Create similar functions for:
   - calculateFinishingCost()
   - calculateMEPCost()
   - calculateExternalCost()
   - calculateOtherCost()

3. Create main calculator (src/core/calculator/engine.ts):
   - Orchestrate all calculations
   - Apply location multipliers
   - Calculate percentages
   - Generate final result

4. Add comprehensive tests for each function

Update STATUS.md with calculation engine progress
    `
  },

  "C-003": {
    title: "Material Quantity Calculations",
    duration: "2 hours",
    dependencies: ["C-002"],
    references: ["PRD - Material Database Requirements"],

    prompt: `
Implement material quantity calculations:

1. Create src/core/calculator/materials/quantities.ts:

export function calculateMaterialQuantities(input: CalculationInput): MaterialQuantity[] {
  const { builtUpArea, floors } = input;
  const totalArea = builtUpArea * (floors + 1);

  return [
    {
      category: 'Cement',
      name: 'OPC 53 Grade Cement',
      quantity: Math.ceil(totalArea * 0.38), // bags per sqft
      unit: 'bags (50kg)',
      purpose: 'RCC, Plastering, Flooring'
    },
    {
      category: 'Steel',
      name: 'TMT Steel Fe500',
      quantity: Math.round(totalArea * 4), // kg per sqft
      unit: 'kg',
      purpose: 'RCC Structure'
    },
    {
      category: 'Bricks',
      name: 'Red Clay Bricks',
      quantity: Math.round(totalArea * 8), // per sqft for 9" walls
      unit: 'pieces',
      purpose: 'Wall Construction'
    },
    {
      category: 'Sand',
      name: 'River Sand',
      quantity: Math.round(totalArea * 0.816), // cft per sqft
      unit: 'cft',
      purpose: 'RCC, Plastering, Flooring'
    },
    {
      category: 'Aggregate',
      name: '20mm Aggregate',
      quantity: Math.round(totalArea * 0.608), // cft per sqft
      unit: 'cft',
      purpose: 'RCC Work'
    }
  ];
}

2. Create quality-tier based variations
3. Add formulas documentation
4. Create tests with IS code validation

Update STATUS.md
    `
  }
};
#### INTERFACE Agent Tasks (Day 1-2)
typescriptconst interfaceTasks = {
  "UI-001": {
    title: "Set Up Component Library Foundation",
    duration: "2 hours",
    priority: "HIGH",
    references: ["Design System Doc - Component Specifications"],

    prompt: `
Create the base component library:

1. Install shadcn/ui CLI and initialize:
   npx shadcn-ui@latest init
   - Choose all default options
   - Use CSS variables for colors

2. Create base components in src/components/ui/:

// Button.tsx
import * as React from "react"
import { cn } from "@/lib/utils"

export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'default' | 'secondary' | 'outline' | 'ghost'
  size?: 'sm' | 'md' | 'lg'
}

export const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant = 'default', size = 'md', ...props }, ref) => {
    return (
      <button
        className={cn(
          "inline-flex items-center justify-center rounded-md font-medium transition-colors",
          "focus-visible:outline-none focus-visible:ring-2",
          "disabled:pointer-events-none disabled:opacity-50",
          {
            // Variants
            'bg-primary text-white hover:bg-primary/90': variant === 'default',
            'bg-secondary hover:bg-secondary/80': variant === 'secondary',
            'border border-input hover:bg-accent': variant === 'outline',
            'hover:bg-accent hover:text-accent-foreground': variant === 'ghost',
            // Sizes
            'h-9 px-3 text-sm': size === 'sm',
            'h-10 px-4': size === 'md',
            'h-11 px-8': size === 'lg',
          },
          className
        )}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

3. Create similar components:
   - Input.tsx (with error states)
   - Select.tsx
   - Card.tsx
   - Label.tsx

4. Create Storybook stories for each component

5. Add component tests

Update STATUS.md with component list
    `
  },

  "UI-002": {
    title: "Create Layout Components",
    duration: "1.5 hours",
    dependencies: ["UI-001"],

    prompt: `
Create application layout components:

1. Create src/components/layout/Header.tsx:
   - Logo placement
   - Navigation menu (desktop)
   - Mobile menu button
   - User account area

2. Create src/components/layout/Footer.tsx:
   - Copyright info
   - Quick links
   - Social media links

3. Create src/components/layout/Container.tsx:
   - Responsive max-width container
   - Proper padding

4. Create src/components/layout/Layout.tsx:
   - Combine Header, Footer, Container
   - Children rendering

5. Create responsive navigation system

Test responsiveness at all breakpoints
Update STATUS.md
    `
  }
};
### Day 3-5: Core Development Phase

#### Day 3: Calculator UI Development
typescriptconst day3Tasks = {
  "UI-003": {
    title: "Build Calculator Form Interface",
    duration: "4 hours",
    priority: "CRITICAL",
    dependencies: ["UI-001", "C-001"],
    references: ["PRD - Calculator UX Requirements", "Design System - Form Patterns"],

    prompt: `
Create the calculator form interface:

1. Create src/app/calculator/page.tsx (main page)

2. Create src/components/calculator/CalculatorForm.tsx:

import { useState } from 'react'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Select } from '@/components/ui/Select'
import { Card } from '@/components/ui/Card'
import { Label } from '@/components/ui/Label'
import type { CalculationInput } from '@/core/calculator/types'

export function CalculatorForm({ onCalculate }: { onCalculate: (input: CalculationInput) => void }) {
  const [formData, setFormData] = useState<CalculationInput>({
    builtUpArea: 0,
    floors: 0,
    qualityTier: 'smart',
    location: 'bangalore'
  })

  // Form implementation with:
  // - Built-up area input (500-50000 sqft)
  // - Floors dropdown (G to G+4)
  // - Quality tier selection cards
  // - Location dropdown
  // - Validation on inputs
  // - Loading state during calculation
  // - Error handling

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Area Input Section */}
      <div>
        <Label htmlFor="builtUpArea">Built-up Area (sq ft)</Label>
        <Input
          id="builtUpArea"
          type="number"
          min={500}
          max={50000}
          value={formData.builtUpArea}
          onChange={(e) => setFormData({...formData, builtUpArea: parseInt(e.target.value)})}
          placeholder="Enter area in square feet"
        />
      </div>

      {/* Continue with other form fields... */}
    </form>
  )
}

3. Create quality tier selector with visual cards
4. Add real-time validation
5. Mobile responsive design
6. Smooth animations

Test form on mobile and desktop
Update STATUS.md
    `
  },

  "UI-004": {
    title: "Create Results Display Component",
    duration: "3 hours",
    dependencies: ["UI-003"],

    prompt: `
Build the results display:

1. Create src/components/calculator/ResultsDisplay.tsx:
   - Total cost hero display (large, prominent)
   - Cost per sqft indicator
   - Animated number counting
   - Visual breakdown chart
   - Download/Share buttons

2. Create src/components/calculator/CostBreakdownCard.tsx:
   - Category name and icon
   - Amount with formatting
   - Percentage bar
   - Expand for details

3. Create src/components/calculator/MaterialsList.tsx:
   - Table format for materials
   - Quantity and unit display
   - Sortable columns

4. Add loading skeleton while calculating
5. Add error state display
6. Print-friendly styling

Update STATUS.md
    `
  },

  "C-004": {
    title: "Create Calculator API Integration",
    duration: "2 hours",
    dependencies: ["C-002", "UI-003"],
    agent: "CALCULATOR",

    prompt: `
Create API route for calculations:

1. Create src/app/api/calculate/route.ts:

import { NextRequest, NextResponse } from 'next/server'
import { calculate } from '@/core/calculator'
import { CalculationInput } from '@/core/calculator/types'

export async function POST(request: NextRequest) {
  try {
    const input: CalculationInput = await request.json()

    // Validate input
    if (!input.builtUpArea || input.builtUpArea < 500) {
      return NextResponse.json(
        { error: 'Built-up area must be at least 500 sqft' },
        { status: 400 }
      )
    }

    // Perform calculation
    const result = calculate(input)

    // Log for analytics (later)
    console.log('Calculation performed:', { input, result })

    return NextResponse.json(result)
  } catch (error) {
    console.error('Calculation error:', error)
    return NextResponse.json(
      { error: 'Calculation failed' },
      { status: 500 }
    )
  }
}

2. Add input validation
3. Add rate limiting (later)
4. Add response caching headers
5. Test with various inputs

Update STATUS.md
    `
  }
};
#### Day 4-5: Integration and Data
typescriptconst integrationTasks = {
  "ORCH-003": {
    title: "Integrate Calculator UI with Engine",
    duration: "3 hours",
    agent: "ORCHESTRATOR",
    priority: "CRITICAL",

    prompt: `
Connect all calculator components:

1. Create src/app/calculator/CalculatorContainer.tsx:
   - Manage state between form and results
   - Handle API calls
   - Error boundaries
   - Loading states

2. Create custom hook src/hooks/useCalculator.ts:
   - Calculation state management
   - API integration
   - Error handling
   - Caching previous calculations

3. Wire up the complete flow:
   - Form submission triggers calculation
   - Show loading state
   - Display results or errors
   - Allow recalculation

4. Add success animations
5. Test complete flow
6. Fix any integration issues

Update STATUS.md with integration complete
    `
  },

  "DATA-001": {
    title: "Create Initial Materials Database",
    duration: "4 hours",
    agent: "FOUNDATION",
    references: ["Business Requirements - Material Specifications"],

    prompt: `
Create initial materials data:

1. Create src/data/materials/core-materials.json:

{
  "materials": [
    {
      "id": "mat_cement_ultratech_opc53",
      "category": "Cement",
      "name": "UltraTech OPC 53 Grade",
      "brand": "UltraTech",
      "unit": "bag",
      "weight": "50kg",
      "specifications": {
        "grade": "53",
        "type": "Ordinary Portland Cement",
        "standardCompliance": "IS 12269:2013",
        "compressiveStrength": "53 MPa",
        "settingTime": "30 min initial, 600 min final"
      },
      "pricing": {
        "bangalore": { "retail": 420, "bulk": 400 },
        "mumbai": { "retail": 440, "bulk": 420 },
        "delhi": { "retail": 430, "bulk": 410 },
        "default": { "retail": 425, "bulk": 405 }
      },
      "qualityScore": 9.5,
      "popularityRank": 1
    },
    // Add 20 more core materials covering:
    // - 3 cement types
    // - 2 steel grades
    // - 3 brick types
    // - 2 sand types
    // - 2 aggregate sizes
    // - Basic electrical (wires, switches)
    // - Basic plumbing (pipes, fittings)
    // - Basic tiles
    // - Basic paint
  ]
}

2. Create src/lib/materials/loader.ts to load this data
3. Create TypeScript types for materials
4. Add material search functionality
5. Test material data loading

Update STATUS.md with materials count
    `
  },

  "QA-001": {
    title: "Set Up Testing Infrastructure",
    duration: "3 hours",
    agent: "QUALITY",
    references: ["Testing Plan - Section 5.1"],

    prompt: `
Configure comprehensive testing:

1. Install testing dependencies:
   npm install --save-dev vitest @testing-library/react @testing-library/user-event
   npm install --save-dev @vitest/ui @vitest/coverage-v8
   npm install --save-dev playwright @playwright/test

2. Create vitest.config.ts:
   - Configure test environment
   - Set up coverage thresholds (80%)
   - Path aliases

3. Create test utilities (src/test/):
   - Setup file with providers
   - Mock data factories
   - Custom render functions

4. Write tests for calculator engine:
   - Unit tests for each calculation
   - Edge cases
   - Integration tests

5. Create E2E test for calculator flow:
   - Fill form
   - Submit calculation
   - Verify results

6. Set up CI to run tests

Update STATUS.md with test coverage %
    `
  }
};
### Day 6-7: Polish and Features

#### Day 6: User Experience Enhancements
typescriptconst day6Tasks = {
  "UI-005": {
    title: "Add Animations and Transitions",
    duration: "2 hours",
    agent: "INTERFACE",

    prompt: `
Enhance UI with smooth animations:

1. Add Framer Motion:
   npm install framer-motion

2. Animate calculator results:
   - Fade in results section
   - Count up animation for numbers
   - Stagger cost breakdown cards
   - Progress bars for percentages

3. Add micro-interactions:
   - Button hover effects
   - Input focus animations
   - Card hover elevations
   - Loading spinners

4. Page transitions
5. Mobile gesture support
6. Respect prefers-reduced-motion

Test all animations
Update STATUS.md
    `
  },

  "F-004": {
    title: "Implement Save Calculation Feature",
    duration: "3 hours",
    agent: "FOUNDATION",
    dependencies: ["F-003"],

    prompt: `
Add ability to save calculations:

1. Create database migration for saved calculations
2. Add "Save" button to results (requires auth)
3. Create API route /api/projects/save
4. Implement guest user prompt
5. Store calculation with timestamp
6. Create "My Projects" page
7. List saved calculations
8. Allow loading previous calculations

Test save/load flow
Update STATUS.md
    `
  },

  "UI-006": {
    title: "Create PDF Export Feature",
    duration: "3 hours",
    agent: "INTERFACE",
    references: ["PRD - Report Generation Feature"],

    prompt: `
Implement PDF report generation:

1. Install jsPDF:
   npm install jspdf

2. Create src/lib/pdf/generator.ts:
   - Format calculation results
   - Add company header
   - Include all breakdowns
   - Add material list table
   - Footer with disclaimer

3. Add "Download PDF" button
4. Generate filename with date
5. Test PDF generation
6. Ensure proper formatting

Update STATUS.md
    `
  }
};
#### Day 7: Mobile and Performance
typescriptconst day7Tasks = {
  "UI-007": {
    title: "Mobile Optimization",
    duration: "3 hours",
    agent: "INTERFACE",
    priority: "HIGH",

    prompt: `
Optimize for mobile devices:

1. Review all components for mobile:
   - Touch-friendly tap targets (44px min)
   - Proper input types (number pad)
   - Swipeable quality tier cards
   - Collapsible sections
   - Bottom sheet for results

2. Fix any layout issues:
   - Stacked layout on mobile
   - Horizontal scrolling prevention
   - Proper padding/margins
   - Font size adjustments

3. Add mobile-specific features:
   - Pull to refresh
   - Share via native share API
   - Haptic feedback (if available)

4. Test on various screen sizes
5. Fix landscape orientation

Update STATUS.md
    `
  },

  "CALC-005": {
    title: "Performance Optimization",
    duration: "2 hours",
    agent: "CALCULATOR",

    prompt: `
Optimize calculation performance:

1. Add memoization to expensive calculations
2. Implement calculation caching
3. Use Web Workers for heavy computation
4. Optimize material search algorithm
5. Add performance monitoring
6. Lazy load non-critical data
7. Implement virtualization for long lists

Measure improvements
Update STATUS.md
    `
  },

  "QA-002": {
    title: "Comprehensive E2E Testing",
    duration: "3 hours",
    agent: "QUALITY",

    prompt: `
Create full E2E test suite:

1. Write Playwright tests for:
   - Complete calculation flow
   - Different quality tiers
   - Edge cases (min/max values)
   - Error scenarios
   - Mobile responsive behavior
   - PDF generation
   - Cross-browser compatibility

2. Visual regression tests:
   - Screenshot key states
   - Compare with baselines
   - Flag visual changes

3. Performance tests:
   - Measure calculation time
   - Check bundle size
   - Lighthouse scores

4. Accessibility tests:
   - Keyboard navigation
   - Screen reader compatibility
   - Color contrast

Update STATUS.md with test results
    `
  }
};
### Day 8-9: Production Readiness

#### Day 8: Bug Fixes and Polish
typescriptconst day8Tasks = {
  "ORCH-004": {
    title: "Bug Fixing Sprint",
    duration: "4 hours",
    agent: "ORCHESTRATOR",
    priority: "CRITICAL",

    prompt: `
Coordinate bug fixing across all agents:

1. Review all test results
2. Prioritize bugs:
   - P1: Calculation errors
   - P2: UI breaking issues
   - P3: Minor visual bugs
   - P4: Enhancement requests

3. Assign bugs to appropriate agents
4. Track fixes in BLOCKERS.md
5. Verify fixes with tests
6. Update documentation

Create bug fix summary
Update STATUS.md
    `
  },

  "F-005": {
    title: "Production Configuration",
    duration: "2 hours",
    agent: "FOUNDATION",

    prompt: `
Prepare for production deployment:

1. Environment variables:
   - Separate dev/staging/prod configs
   - Secure all sensitive keys
   - Validate all are set

2. Security headers:
   - Content Security Policy
   - X-Frame-Options
   - HSTS configuration

3. Error handling:
   - Global error boundary
   - Sentry integration (free tier)
   - User-friendly error pages

4. Analytics setup:
   - Google Analytics 4
   - Custom events tracking
   - Performance monitoring

5. SEO optimization:
   - Meta tags
   - OpenGraph tags
   - Sitemap.xml
   - Robots.txt

Update STATUS.md
    `
  },

  "UI-008": {
    title: "Final UI Polish",
    duration: "3 hours",
    agent: "INTERFACE",

    prompt: `
Final UI improvements:

1. Loading states for everything
2. Empty states design
3. Success confirmations
4. Consistent spacing
5. Final color adjustments
6. Icon consistency
7. Typography hierarchy
8. Print styles
9. Dark mode preparation (future)

Update STATUS.md
    `
  }
};
#### Day 9: Documentation and Deployment Prep
typescriptconst day9Tasks = {
  "ORCH-005": {
    title: "Create User Documentation",
    duration: "2 hours",
    agent: "ORCHESTRATOR",

    prompt: `
Create comprehensive documentation:

1. README.md:
   - Project overview
   - Quick start guide
   - Architecture overview
   - Contributing guidelines

2. docs/USER_GUIDE.md:
   - How to use calculator
   - Understanding results
   - Saving projects
   - FAQ section

3. docs/DEPLOYMENT.md:
   - Deployment steps
   - Environment setup
   - Troubleshooting

4. API documentation
5. Component documentation

Update STATUS.md
    `
  },

  "F-006": {
    title: "CI/CD Pipeline Setup",
    duration: "2 hours",
    agent: "FOUNDATION",

    prompt: `
Configure deployment pipeline:

1. Create .github/workflows/deploy.yml:
   - Build and test on PR
   - Deploy preview on PR
   - Deploy to production on main
   - Run E2E tests post-deploy

2. Vercel configuration:
   - Connect GitHub repo
   - Set environment variables
   - Configure domains
   - Enable analytics

3. Monitoring setup:
   - Uptime monitoring
   - Error alerts
   - Performance alerts

4. Backup strategy

Update STATUS.md
    `
  }
};
### Day 10: Launch Preparation

#### Day 10: Final Testing and Launch
typescriptconst day10Tasks = {
  "QA-003": {
    title: "Final Pre-Launch Testing",
    duration: "3 hours",
    agent: "QUALITY",
    priority: "CRITICAL",

    prompt: `
Complete final testing checklist:

1. Functionality Testing:
   - All calculation scenarios
   - All UI interactions
   - All error cases
   - Cross-browser testing

2. Performance Testing:
   - Page load times < 3s
   - Calculation time < 500ms
   - Bundle size check
   - Memory leak check

3. Security Testing:
   - Input validation
   - XSS prevention
   - API rate limiting
   - Data sanitization

4. Compatibility:
   - Chrome, Firefox, Safari, Edge
   - Mobile browsers
   - Different screen sizes
   - Slow network conditions

5. Create launch checklist

Report: READY/NOT READY
Update STATUS.md
    `
  },

  "ORCH-006": {
    title: "Production Deployment",
    duration: "2 hours",
    agent: "ORCHESTRATOR",
    dependencies: ["QA-003"],

    prompt: `
Deploy to production:

1. Final code review
2. Merge all branches to main
3. Tag release v1.0.0
4. Deploy to Vercel production
5. Verify all features working
6. Submit to search engines
7. Create launch announcement
8. Monitor for issues

Update STATUS.md: LAUNCHED! 🚀
    `
  },

  "ORCH-007": {
    title: "Create Demo and Marketing Assets",
    duration: "2 hours",
    agent: "ORCHESTRATOR",

    prompt: `
Create launch assets:

1. Record demo video:
   - Show calculation flow
   - Highlight key features
   - Mobile demonstration
   - 2-3 minutes max

2. Create screenshots:
   - Homepage
   - Calculator form
   - Results display
   - Mobile views

3. Write launch post:
   - Problem we solve
   - Key features
   - How to use
   - Future roadmap

4. Social media assets

Update STATUS.md: Ready for promotion
    `
  }
};

## Critical Success Checkpoints

### Daily Checkpoints
typescriptconst dailyCheckpoints = {
  everyDay: {
    morning: [
      "Check STATUS.md for current state",
      "Review blocked tasks",
      "Plan day's tasks",
      "Check for merge conflicts"
    ],

    every2Hours: [
      "Update STATUS.md",
      "Commit current work",
      "Check integration points"
    ],

    evening: [
      "Update STATUS.md with day summary",
      "Merge completed work",
      "Plan tomorrow's tasks",
      "Note any blockers"
    ]
  }
};
### Phase Completion Criteria
typescriptconst phaseCompletion = {
  foundation: {
    day2: [
      "✓ Next.js running locally",
      "✓ Database schema created",
      "✓ Basic components ready",
      "✓ Testing framework setup"
    ]
  },

  core: {
    day5: [
      "✓ Calculator engine working",
      "✓ UI connected to engine",
      "✓ 20+ materials in database",
      "✓ Basic tests passing"
    ]
  },

  integration: {
    day7: [
      "✓ Complete flow working",
      "✓ Mobile responsive",
      "✓ PDF export working",
      "✓ Performance optimized"
    ]
  },

  launch: {
    day10: [
      "✓ All tests passing",
      "✓ Deployed to production",
      "✓ Documentation complete",
      "✓ Demo video ready"
    ]
  }
};

## Emergency Procedures

### Context Loss Recovery
typescriptconst contextRecovery = {
  ifContextLost: [
    "1. Read STATUS.md completely",
    "2. Check last 5 commits",
    "3. Review DECISIONS.md",
    "4. Check current branch",
    "5. Run tests to verify state",
    "6. Continue from last known good state"
  ],

  preventionTips: [
    "Update STATUS.md every 30 minutes",
    "Commit with descriptive messages",
    "Document decisions immediately",
    "Keep tasks small and atomic"
  ]
};
### Conflict Resolution
typescriptconst conflictResolution = {
  gitConflicts: [
    "1. Pull latest develop branch",
    "2. Identify conflicting files",
    "3. Understand both changes",
    "4. Merge preserving both functionalities",
    "5. Run all tests",
    "6. Update STATUS.md with resolution"
  ],

  designConflicts: [
    "1. Check DECISIONS.md for precedent",
    "2. Evaluate both approaches",
    "3. Choose simpler solution",
    "4. Document decision",
    "5. Inform other agents via STATUS.md"
  ]
};

## Final Launch Checklist

### Pre-Launch Verification
markdown## Launch Readiness Checklist

### Core Functionality
- [ ] Calculator produces accurate results
- [ ] All quality tiers working
- [ ] Location-based pricing active
- [ ] Material quantities correct

### User Interface
- [ ] Mobile responsive design
- [ ] All forms validating properly
- [ ] Error messages user-friendly
- [ ] Loading states smooth

### Technical
- [ ] All tests passing (>80% coverage)
- [ ] No console errors
- [ ] Performance metrics met
- [ ] Security headers configured

### Production
- [ ] Environment variables set
- [ ] Error monitoring active
- [ ] Analytics configured
- [ ] Deployment successful

### Documentation
- [ ] README comprehensive
- [ ] User guide complete
- [ ] API documented
- [ ] Contributing guide ready

### Marketing
- [ ] Demo video recorded
- [ ] Screenshots captured
- [ ] Launch post written
- [ ] Social media ready

LAUNCH STATUS: [ ] READY [ ] NOT READY

## Success Metrics

### MVP Success Criteria
typescriptconst successCriteria = {
  technical: {
    calculations: "100% accurate vs manual",
    performance: "< 3s page load",
    mobile: "Works on all devices",
    uptime: "99%+ availability"
  },

  user: {
    day1: "10+ test calculations",
    week1: "100+ users",
    feedback: "Positive response",
    bugs: "No critical issues"
  },

  business: {
    validation: "Users find value",
    retention: "Users return",
    growth: "Organic sharing",
    path: "Clear to monetization"
  }
};

## Post-Launch Roadmap References
After successful launch, refer to:

- **PRD Section 9**: Feature Prioritization
- **Business Requirements Section 8**: Growth Strategy
- **Go-To-Market Section 16-20**: Launch and scaling plans
- **Technical Design Section 10**: Future architecture


## Conclusion
This granular execution plan provides everything needed to build Clarity Engine in 10 days using parallel AI agents. The key to success:

- **Follow the plan exactly** - Don't deviate or add features
- **Update STATUS.md religiously** - This maintains context
- **Keep tasks atomic** - 2-hour maximum per task
- **Test continuously** - Don't accumulate technical debt
- **Merge frequently** - Avoid integration hell

With discipline and focus, you'll have a working construction cost calculator ready for users in 10 days.
**Remember**: The goal is a WORKING product, not a PERFECT product. Polish comes after validation.
