'use client';

import { motion } from 'framer-motion';
import { FolderIcon, LogOutIcon, SettingsIcon, UserIcon } from 'lucide-react';
import Link from 'next/link';
import { useState } from 'react';

import { useAuth } from '@/contexts/AuthContext';
import { fadeInUp } from '@/lib/animations';

export function UserMenu() {
  const { user, signOut } = useAuth();
  const [isOpen, setIsOpen] = useState(false);

  if (!user) return null;

  const handleSignOut = async () => {
    try {
      await signOut();
      setIsOpen(false);
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-600 text-white hover:bg-blue-700"
      >
        <UserIcon className="h-4 w-4" />
      </button>

      {isOpen && (
        <>
          {/* Backdrop */}
          <div
            className="fixed inset-0 z-10"
            onClick={() => setIsOpen(false)}
          />

          {/* Menu */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: -10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: -10 }}
            className="absolute right-0 top-full z-20 mt-2 w-64 rounded-lg bg-white py-2 shadow-lg ring-1 ring-black/5"
          >
            {/* User Info */}
            <motion.div {...fadeInUp} className="border-b border-gray-100 px-4 py-3">
              <p className="text-sm font-medium text-gray-900">
                {user.email}
              </p>
              <p className="text-xs text-gray-500">
                Signed in {user.created_at ? new Date(user.created_at).toLocaleDateString() : ''}
              </p>
            </motion.div>

            {/* Menu Items */}
            <motion.div {...fadeInUp} className="py-1">
              <Link
                href="/projects"
                onClick={() => setIsOpen(false)}
                className="flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
              >
                <FolderIcon className="h-4 w-4" />
                My Projects
              </Link>

              <Link
                href="/settings"
                onClick={() => setIsOpen(false)}
                className="flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
              >
                <SettingsIcon className="h-4 w-4" />
                Settings
              </Link>

              <div className="border-t border-gray-100 my-1" />

              <button
                onClick={handleSignOut}
                className="flex w-full items-center gap-3 px-4 py-2 text-sm text-red-600 hover:bg-red-50"
              >
                <LogOutIcon className="h-4 w-4" />
                Sign Out
              </button>
            </motion.div>
          </motion.div>
        </>
      )}
    </div>
  );
}