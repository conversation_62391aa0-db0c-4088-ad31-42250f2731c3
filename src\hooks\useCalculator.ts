/**
 * Calculator API Integration Hook
 * Provides comprehensive API integration with TanStack Query for the calculator
 * Includes caching, error handling, and performance optimization
 */

'use client';

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useCallback, useMemo } from 'react';
import type {
  CalculationInput,
  CalculationResult,
  CalculationApiResponse,
} from '@/core/calculator/types';

// API Configuration
const API_BASE_URL = '/api';
const CACHE_TIME = 5 * 60 * 1000; // 5 minutes
const STALE_TIME = 2 * 60 * 1000; // 2 minutes

// Query Keys
const CALCULATOR_QUERY_KEYS = {
  all: ['calculator'] as const,
  calculations: () => [...CALCULATOR_QUERY_KEYS.all, 'calculations'] as const,
  calculation: (input: CalculationInput) =>
    [...CALCULATOR_QUERY_KEYS.calculations(), input] as const,
  locations: () => [...CALCULATOR_QUERY_KEYS.all, 'locations'] as const,
  qualityTiers: () => [...CALCULATOR_QUERY_KEYS.all, 'qualityTiers'] as const,
};

// API Client Functions
class CalculatorApiClient {
  private static async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`;

    const defaultHeaders = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    const config: RequestInit = {
      ...options,
      headers: {
        ...defaultHeaders,
        ...options.headers,
      },
    };

    try {
      const response = await fetch(url, config);

      // Handle rate limiting
      if (response.status === 429) {
        const retryAfter = response.headers.get('Retry-After');
        throw new CalculatorApiError(
          'Rate limit exceeded',
          'RATE_LIMIT_EXCEEDED',
          response.status,
          { retryAfter: retryAfter ? parseInt(retryAfter) : 60 }
        );
      }

      // Handle other HTTP errors
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new CalculatorApiError(
          errorData.error?.message || `HTTP ${response.status}`,
          errorData.error?.code || 'HTTP_ERROR',
          response.status,
          errorData
        );
      }

      const data = await response.json();

      if (!data.success) {
        throw new CalculatorApiError(
          data.error?.message || 'API request failed',
          data.error?.code || 'API_ERROR',
          response.status,
          data.error
        );
      }

      return data as T;
    } catch (error) {
      if (error instanceof CalculatorApiError) {
        throw error;
      }

      // Network or other errors
      throw new CalculatorApiError(
        error instanceof Error ? error.message : 'Network error',
        'NETWORK_ERROR',
        0,
        { originalError: error }
      );
    }
  }

  static async calculate(input: CalculationInput): Promise<CalculationResult> {
    const response = await CalculatorApiClient.makeRequest<CalculationApiResponse>(
      '/calculate',
      {
        method: 'POST',
        body: JSON.stringify(input),
      }
    );

    return response.data!;
  }

  static async getApiInfo(): Promise<any> {
    return CalculatorApiClient.makeRequest('/calculate', { method: 'GET' });
  }
}

// Custom Error Class
export class CalculatorApiError extends Error {
  constructor(
    message: string,
    public code: string,
    public status: number,
    public details?: any
  ) {
    super(message);
    this.name = 'CalculatorApiError';
  }

  get isRateLimit(): boolean {
    return this.code === 'RATE_LIMIT_EXCEEDED';
  }

  get isValidation(): boolean {
    return this.code === 'VALIDATION_FAILED' || this.code === 'MISSING_REQUIRED_FIELDS';
  }

  get isNetwork(): boolean {
    return this.code === 'NETWORK_ERROR';
  }

  get retryable(): boolean {
    return this.isRateLimit || this.isNetwork || this.status >= 500;
  }
}

// Input Validation Utilities
export function validateCalculatorInput(input: Partial<CalculationInput>): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  if (!input.builtUpArea || input.builtUpArea <= 0) {
    errors.push('Built-up area must be greater than 0');
  }

  if (input.builtUpArea && input.builtUpArea > 50000) {
    errors.push('Built-up area cannot exceed 50,000 sqft');
  }

  if (!input.qualityTier || !['smart', 'premium', 'luxury'].includes(input.qualityTier)) {
    errors.push('Quality tier must be smart, premium, or luxury');
  }

  if (!input.location || input.location.trim().length === 0) {
    errors.push('Location is required');
  }

  if (input.floors !== undefined && (input.floors < 0 || input.floors > 10)) {
    errors.push('Floors must be between 0 and 10');
  }

  if (input.plotArea !== undefined && input.plotArea <= 0) {
    errors.push('Plot area must be greater than 0');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

// Main Hook: useCalculator
export function useCalculator() {
  const queryClient = useQueryClient();

  // Mutation for performing calculations
  const calculationMutation = useMutation({
    mutationFn: CalculatorApiClient.calculate,
    onSuccess: (data, variables) => {
      // Cache the successful calculation
      queryClient.setQueryData(
        CALCULATOR_QUERY_KEYS.calculation(variables),
        data
      );

      // Invalidate related queries if needed
      queryClient.invalidateQueries({
        queryKey: CALCULATOR_QUERY_KEYS.calculations(),
        exact: false,
      });
    },
    onError: (error: CalculatorApiError) => {
      console.error('Calculation failed:', error);
    },
  });

  // Calculate function with validation
  const calculate = useCallback(
    async (input: CalculationInput) => {
      const validation = validateCalculatorInput(input);

      if (!validation.isValid) {
        throw new CalculatorApiError(
          validation.errors.join(', '),
          'CLIENT_VALIDATION_ERROR',
          400,
          { errors: validation.errors }
        );
      }

      return calculationMutation.mutateAsync(input);
    },
    [calculationMutation]
  );

  // Get cached calculation result
  const getCachedResult = useCallback(
    (input: CalculationInput): CalculationResult | undefined => {
      return queryClient.getQueryData(
        CALCULATOR_QUERY_KEYS.calculation(input)
      );
    },
    [queryClient]
  );

  // Prefetch calculation (for predictive caching)
  const prefetchCalculation = useCallback(
    async (input: CalculationInput) => {
      const validation = validateCalculatorInput(input);
      if (!validation.isValid) return;

      return queryClient.prefetchQuery({
        queryKey: CALCULATOR_QUERY_KEYS.calculation(input),
        queryFn: () => CalculatorApiClient.calculate(input),
        staleTime: STALE_TIME,
      });
    },
    [queryClient]
  );

  return {
    // Main calculation function
    calculate,

    // Mutation state
    isCalculating: calculationMutation.isPending,
    calculationError: calculationMutation.error as CalculatorApiError | null,
    lastResult: calculationMutation.data,

    // Cache utilities
    getCachedResult,
    prefetchCalculation,

    // Reset functions
    resetCalculation: calculationMutation.reset,
    clearCache: () => {
      queryClient.invalidateQueries({
        queryKey: CALCULATOR_QUERY_KEYS.calculations(),
      });
    },

    // Mutation object for advanced usage
    mutation: calculationMutation,
  };
}

// Hook for API metadata
export function useCalculatorApi() {
  const query = useQuery({
    queryKey: ['calculator', 'api-info'],
    queryFn: CalculatorApiClient.getApiInfo,
    staleTime: 30 * 60 * 1000, // 30 minutes - API info changes rarely
  });

  return {
    apiInfo: query.data,
    isLoading: query.isLoading,
    error: query.error,
  };
}

// Hook for quick calculations (with automatic caching)
export function useQuickCalculation(
  input: CalculationInput | null,
  options: {
    enabled?: boolean;
    refetchOnWindowFocus?: boolean;
    retry?: boolean | number;
  } = {}
) {
  const {
    enabled = true,
    refetchOnWindowFocus = false,
    retry = 3,
  } = options;

  const isValidInput = input ? validateCalculatorInput(input).isValid : false;

  return useQuery({
    queryKey: input ? CALCULATOR_QUERY_KEYS.calculation(input) : ['calculator', 'disabled'],
    queryFn: () => {
      if (!input) throw new Error('No input provided');
      return CalculatorApiClient.calculate(input);
    },
    enabled: enabled && isValidInput && !!input,
    staleTime: STALE_TIME,
    refetchOnWindowFocus,
    retry: (failureCount, error) => {
      if (error instanceof CalculatorApiError) {
        // Don't retry validation errors
        if (!error.retryable) return false;
        // Retry up to specified times for retryable errors
        return failureCount < (typeof retry === 'number' ? retry : 3);
      }
      return failureCount < 3;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff
  });
}

// Hook for batch calculations (future enhancement)
export function useBatchCalculation() {
  const queryClient = useQueryClient();

  const batchMutation = useMutation({
    mutationFn: async (inputs: CalculationInput[]) => {
      // For now, calculate sequentially
      // In production, this could be optimized with a batch API endpoint
      const results = await Promise.allSettled(
        inputs.map(input => CalculatorApiClient.calculate(input))
      );

      return results.map((result, index) => ({
        input: inputs[index],
        result: result.status === 'fulfilled' ? result.value : null,
        error: result.status === 'rejected' ? result.reason : null,
      }));
    },
    onSuccess: (results) => {
      // Cache successful results
      results.forEach(({ input, result }) => {
        if (result) {
          queryClient.setQueryData(
            CALCULATOR_QUERY_KEYS.calculation(input),
            result
          );
        }
      });
    },
  });

  return {
    calculateBatch: batchMutation.mutateAsync,
    isCalculating: batchMutation.isPending,
    results: batchMutation.data,
    error: batchMutation.error,
    reset: batchMutation.reset,
  };
}

// Performance monitoring hook
export function useCalculatorPerformance() {
  const queryClient = useQueryClient();

  const getPerformanceMetrics = useCallback(() => {
    const cache = queryClient.getQueryCache();
    const queries = cache.getAll();
    const calculatorQueries = queries.filter(query =>
      query.queryKey[0] === 'calculator'
    );

    return {
      totalQueries: calculatorQueries.length,
      cachedResults: calculatorQueries.filter(q => q.state.data).length,
      errorQueries: calculatorQueries.filter(q => q.state.error).length,
      pendingQueries: calculatorQueries.filter(q => q.state.fetchStatus === 'fetching').length,
    };
  }, [queryClient]);

  return {
    metrics: getPerformanceMetrics(),
    clearAllCache: () => {
      queryClient.clear();
    },
    removeErrorQueries: () => {
      queryClient.removeQueries({
        predicate: (query) => !!query.state.error,
      });
    },
  };
}

// Export utility functions
export {
  CALCULATOR_QUERY_KEYS,
  CalculatorApiClient,
};