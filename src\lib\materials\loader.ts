/**
 * Material Data Loader and Management System
 * Handles loading, searching, filtering, and cost calculations for construction materials
 */

// @ts-ignore
import materialsData from '../../data/materials/core-materials.json';
import {
  Material,
  MaterialDatabase,
  MaterialSearchCriteria,
  MaterialSearchResult,
  MaterialCostBreakdown,
  MaterialConsumption,
  PurchaseType,
  AvailabilityLevel,
  MaterialCategory
} from '../../types/materials';

/**
 * Material Manager Class
 * Provides comprehensive material management functionality
 */
export class MaterialManager {
  private materials: Material[] = [];
  private database: MaterialDatabase;

  constructor() {
    this.database = materialsData as MaterialDatabase;
    this.materials = this.database.materials;
  }

  /**
   * Get all materials
   */
  getAllMaterials(): Material[] {
    return [...this.materials];
  }

  /**
   * Get material by ID
   */
  getMaterialById(materialId: string): Material | null {
    return this.materials.find(material => material.id === materialId) || null;
  }

  /**
   * Get materials by category
   */
  getMaterialsByCategory(category: string): Material[] {
    return this.materials.filter(material =>
      material.category.toLowerCase() === category.toLowerCase()
    );
  }

  /**
   * Get materials by subcategory
   */
  getMaterialsBySubcategory(subcategory: string): Material[] {
    return this.materials.filter(material =>
      material.subcategory.toLowerCase() === subcategory.toLowerCase()
    );
  }

  /**
   * Get materials by brand
   */
  getMaterialsByBrand(brand: string): Material[] {
    return this.materials.filter(material =>
      material.brand.toLowerCase().includes(brand.toLowerCase())
    );
  }

  /**
   * Search materials with comprehensive criteria including new enhanced features
   */
  searchMaterials(criteria: MaterialSearchCriteria, region: string = 'default'): MaterialSearchResult[] {
    let filteredMaterials = [...this.materials];

    // Filter by category
    if (criteria.category) {
      filteredMaterials = filteredMaterials.filter(material =>
        material.category.toLowerCase() === criteria.category!.toLowerCase()
      );
    }

    // Filter by subcategory
    if (criteria.subcategory) {
      filteredMaterials = filteredMaterials.filter(material =>
        material.subcategory.toLowerCase() === criteria.subcategory!.toLowerCase()
      );
    }

    // Filter by brand
    if (criteria.brand) {
      filteredMaterials = filteredMaterials.filter(material =>
        material.brand.toLowerCase().includes(criteria.brand!.toLowerCase())
      );
    }

    // Filter by availability
    if (criteria.availability) {
      filteredMaterials = filteredMaterials.filter(material =>
        material.availability === criteria.availability
      );
    }

    // Filter by quality score
    if (criteria.minQualityScore) {
      filteredMaterials = filteredMaterials.filter(material =>
        material.qualityScore >= criteria.minQualityScore!
      );
    }

    // Filter by lead time
    if (criteria.maxLeadTime) {
      filteredMaterials = filteredMaterials.filter(material =>
        material.leadTimeDays <= criteria.maxLeadTime!
      );
    }

    // Filter by search text (name, brand, specifications)
    if (criteria.searchText) {
      const searchText = criteria.searchText.toLowerCase();
      filteredMaterials = filteredMaterials.filter(material =>
        material.name.toLowerCase().includes(searchText) ||
        material.brand.toLowerCase().includes(searchText) ||
        material.category.toLowerCase().includes(searchText) ||
        material.subcategory.toLowerCase().includes(searchText) ||
        Object.values(material.specifications).some(spec =>
          String(spec).toLowerCase().includes(searchText)
        )
      );
    }

    // Calculate prices and create search results
    const purchaseType = criteria.purchaseType || PurchaseType.RETAIL;
    const results: MaterialSearchResult[] = filteredMaterials.map(material => {
      const price = this.getMaterialPrice(material.id, region, purchaseType);

      return {
        ...material,
        calculatedPrice: price,
        pricePerUnit: price,
        totalCostForQuantity: criteria.quantity ? price * criteria.quantity : undefined,
        quantity: criteria.quantity
      };
    });

    // Filter by max price if specified
    if (criteria.maxPrice) {
      return results.filter(result => result.calculatedPrice <= criteria.maxPrice!);
    }

    // Sort by popularity rank (lower rank = more popular)
    return results.sort((a, b) => a.popularityRank - b.popularityRank);
  }

  /**
   * Get material price for specific region and purchase type
   */
  getMaterialPrice(
    materialId: string,
    region: string = 'default',
    purchaseType: PurchaseType = PurchaseType.RETAIL
  ): number {
    const material = this.getMaterialById(materialId);
    if (!material) {
      throw new Error(`Material with ID ${materialId} not found`);
    }

    // Get regional pricing, fallback to default if region not found
    const regionKey = region.toLowerCase() as keyof typeof material.pricing;
    const regionalPricing = material.pricing[regionKey] || material.pricing.default;

    return regionalPricing[purchaseType];
  }

  /**
   * Calculate material cost breakdown for a project
   */
  calculateMaterialCost(
    materialConsumption: MaterialConsumption,
    region: string = 'default',
    purchaseType: PurchaseType = PurchaseType.RETAIL
  ): MaterialCostBreakdown {
    const material = this.getMaterialById(materialConsumption.materialId);
    if (!material) {
      throw new Error(`Material with ID ${materialConsumption.materialId} not found`);
    }

    const unitPrice = this.getMaterialPrice(materialConsumption.materialId, region, purchaseType);
    const totalCost = unitPrice * materialConsumption.quantityRequired;
    const wastageAmount = (totalCost * material.wastagePercentage) / 100;
    const finalCost = totalCost + wastageAmount;

    return {
      materialId: materialConsumption.materialId,
      materialName: material.name,
      category: material.category,
      quantity: materialConsumption.quantityRequired,
      unit: material.unit,
      unitPrice,
      totalCost,
      wastage: material.wastagePercentage,
      wastageAmount,
      finalCost,
      supplier: material.brand,
      leadTime: material.leadTimeDays
    };
  }

  /**
   * Calculate total project material costs
   */
  calculateProjectMaterialCosts(
    materialConsumptions: MaterialConsumption[],
    region: string = 'default',
    purchaseType: PurchaseType = PurchaseType.RETAIL
  ): {
    breakdown: MaterialCostBreakdown[];
    totalCost: number;
    totalWastage: number;
    averageLeadTime: number;
    categoryTotals: Record<string, number>;
  } {
    const breakdown = materialConsumptions.map(consumption =>
      this.calculateMaterialCost(consumption, region, purchaseType)
    );

    const totalCost = breakdown.reduce((sum, item) => sum + item.finalCost, 0);
    const totalWastage = breakdown.reduce((sum, item) => sum + item.wastageAmount, 0);
    const averageLeadTime = breakdown.length > 0
      ? breakdown.reduce((sum, item) => sum + item.leadTime, 0) / breakdown.length
      : 0;

    // Calculate category-wise totals
    const categoryTotals: Record<string, number> = {};
    breakdown.forEach(item => {
      categoryTotals[item.category] = (categoryTotals[item.category] || 0) + item.finalCost;
    });

    return {
      breakdown,
      totalCost,
      totalWastage,
      averageLeadTime: Math.ceil(averageLeadTime),
      categoryTotals
    };
  }

  /**
   * Get popular materials by category
   */
  getPopularMaterials(category?: string, limit: number = 10): Material[] {
    const materials = category
      ? this.getMaterialsByCategory(category)
      : this.getAllMaterials();

    return materials
      .sort((a, b) => a.popularityRank - b.popularityRank)
      .slice(0, limit);
  }

  /**
   * Get premium materials (high quality score)
   */
  getPremiumMaterials(minQualityScore: number = 8.5, limit: number = 10): Material[] {
    return this.materials
      .filter(material => material.qualityScore >= minQualityScore)
      .sort((a, b) => b.qualityScore - a.qualityScore)
      .slice(0, limit);
  }

  /**
   * Get budget-friendly materials
   */
  getBudgetFriendlyMaterials(
    region: string = 'default',
    purchaseType: PurchaseType = PurchaseType.BULK,
    category?: string,
    limit: number = 10
  ): MaterialSearchResult[] {
    const materials = category
      ? this.getMaterialsByCategory(category)
      : this.getAllMaterials();

    const results = materials.map(material => ({
      ...material,
      calculatedPrice: this.getMaterialPrice(material.id, region, purchaseType),
      pricePerUnit: this.getMaterialPrice(material.id, region, purchaseType)
    }));

    return results
      .sort((a, b) => a.calculatedPrice - b.calculatedPrice)
      .slice(0, limit);
  }

  /**
   * Get material alternatives (similar materials in same category)
   */
  getMaterialAlternatives(materialId: string, limit: number = 5): Material[] {
    const material = this.getMaterialById(materialId);
    if (!material) {
      return [];
    }

    return this.materials
      .filter(m =>
        m.id !== materialId &&
        m.category === material.category &&
        m.subcategory === material.subcategory
      )
      .sort((a, b) => b.qualityScore - a.qualityScore)
      .slice(0, limit);
  }

  /**
   * Get all available categories
   */
  getCategories(): string[] {
    const categories = new Set(this.materials.map(material => material.category));
    return Array.from(categories).sort();
  }

  /**
   * Get subcategories for a specific category
   */
  getSubcategories(category: string): string[] {
    const subcategories = new Set(
      this.materials
        .filter(material => material.category.toLowerCase() === category.toLowerCase())
        .map(material => material.subcategory)
    );
    return Array.from(subcategories).sort();
  }

  /**
   * Get all available brands
   */
  getBrands(category?: string): string[] {
    const materials = category
      ? this.getMaterialsByCategory(category)
      : this.getAllMaterials();

    const brands = new Set(materials.map(material => material.brand));
    return Array.from(brands).sort();
  }

  /**
   * Get all available regions
   */
  getAvailableRegions(): string[] {
    return this.database.metadata.regions;
  }

  /**
   * Get database metadata
   */
  getDatabaseMetadata() {
    return this.database.metadata;
  }

  /**
   * Enhanced search by material specifications
   */
  searchBySpecifications(specificationKey: string, specificationValue: string): Material[] {
    return this.materials.filter(material =>
      material.specifications[specificationKey] &&
      String(material.specifications[specificationKey]).toLowerCase().includes(specificationValue.toLowerCase())
    );
  }

  /**
   * Filter materials by price range
   */
  filterByPriceRange(minPrice: number, maxPrice: number, region: string = 'default', purchaseType: PurchaseType = PurchaseType.RETAIL): Material[] {
    return this.materials.filter(material => {
      const price = this.getMaterialPrice(material.id, region, purchaseType);
      return price >= minPrice && price <= maxPrice;
    });
  }

  /**
   * Get materials by availability status
   */
  getMaterialsByAvailability(availability: AvailabilityLevel): Material[] {
    return this.materials.filter(material => material.availability === availability);
  }

  /**
   * Search materials with regional availability
   */
  searchMaterialsByRegion(region: string): Material[] {
    return this.materials.filter(material => {
      const regionKey = region.toLowerCase() as keyof typeof material.pricing;
      return material.pricing[regionKey] !== undefined;
    });
  }

  /**
   * Get materials with seasonal pricing variations
   */
  getMaterialsWithSeasonalPricing(): Material[] {
    return this.materials.filter(material => 
      material.seasonalPricing && material.seasonalPricing.length > 0
    );
  }

  /**
   * Calculate seasonal price for a material
   */
  getSeasonalPrice(materialId: string, season: 'summer' | 'monsoon' | 'winter', region: string = 'default', purchaseType: PurchaseType = PurchaseType.RETAIL): number {
    const material = this.getMaterialById(materialId);
    if (!material) {
      throw new Error(`Material with ID ${materialId} not found`);
    }

    const basePrice = this.getMaterialPrice(materialId, region, purchaseType);
    
    // Apply seasonal multiplier for cement and steel in summer
    if (season === 'summer' && (material.category === 'Cement' || material.category === 'Steel')) {
      return basePrice * 1.1; // 10% increase in summer
    }
    
    // Apply seasonal pricing if defined
    if (material.seasonalPricing) {
      const seasonalPricing = material.seasonalPricing.find(sp => sp.season === season);
      if (seasonalPricing) {
        return basePrice * seasonalPricing.priceMultiplier;
      }
    }
    
    return basePrice;
  }

  /**
   * Advanced material search with multiple filters
   */
  advancedSearch(filters: {
    categories?: string[];
    brands?: string[];
    regions?: string[];
    priceRange?: { min: number; max: number };
    qualityRange?: { min: number; max: number };
    availabilityLevels?: AvailabilityLevel[];
    maxLeadTime?: number;
    specifications?: { [key: string]: string };
  }): MaterialSearchResult[] {
    let filteredMaterials = [...this.materials];

    // Filter by categories
    if (filters.categories && filters.categories.length > 0) {
      filteredMaterials = filteredMaterials.filter(material =>
        filters.categories!.includes(material.category)
      );
    }

    // Filter by brands
    if (filters.brands && filters.brands.length > 0) {
      filteredMaterials = filteredMaterials.filter(material =>
        filters.brands!.some(brand => material.brand.toLowerCase().includes(brand.toLowerCase()))
      );
    }

    // Filter by quality range
    if (filters.qualityRange) {
      filteredMaterials = filteredMaterials.filter(material =>
        material.qualityScore >= filters.qualityRange!.min &&
        material.qualityScore <= filters.qualityRange!.max
      );
    }

    // Filter by availability levels
    if (filters.availabilityLevels && filters.availabilityLevels.length > 0) {
      filteredMaterials = filteredMaterials.filter(material =>
        filters.availabilityLevels!.includes(material.availability)
      );
    }

    // Filter by lead time
    if (filters.maxLeadTime) {
      filteredMaterials = filteredMaterials.filter(material =>
        material.leadTimeDays <= filters.maxLeadTime!
      );
    }

    // Filter by specifications
    if (filters.specifications) {
      Object.entries(filters.specifications).forEach(([key, value]) => {
        filteredMaterials = filteredMaterials.filter(material =>
          material.specifications[key] &&
          String(material.specifications[key]).toLowerCase().includes(value.toLowerCase())
        );
      });
    }

    // Convert to search results
    return filteredMaterials.map(material => ({
      ...material,
      calculatedPrice: this.getMaterialPrice(material.id, 'default', PurchaseType.RETAIL),
      pricePerUnit: this.getMaterialPrice(material.id, 'default', PurchaseType.RETAIL)
    }));
  }

  /**
   * Validate material data integrity
   */
  validateMaterialData(): {
    isValid: boolean;
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];

    this.materials.forEach((material, index) => {
      // Check required fields
      if (!material.id) errors.push(`Material at index ${index}: Missing ID`);
      if (!material.name) errors.push(`Material ${material.id}: Missing name`);
      if (!material.category) errors.push(`Material ${material.id}: Missing category`);
      if (!material.unit) errors.push(`Material ${material.id}: Missing unit`);

      // Check pricing structure
      if (!material.pricing || !material.pricing.default) {
        errors.push(`Material ${material.id}: Missing default pricing`);
      }

      // Check quality score range
      if (material.qualityScore < 1 || material.qualityScore > 10) {
        warnings.push(`Material ${material.id}: Quality score out of range (1-10)`);
      }

      // Check wastage percentage
      if (material.wastagePercentage < 0 || material.wastagePercentage > 50) {
        warnings.push(`Material ${material.id}: Wastage percentage seems unusual`);
      }
    });

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }
}

// Create singleton instance
export const materialManager = new MaterialManager();

// Convenience functions for common operations
export const getAllMaterials = () => materialManager.getAllMaterials();
export const getMaterialById = (id: string) => materialManager.getMaterialById(id);
export const searchMaterials = (criteria: MaterialSearchCriteria, region?: string) =>
  materialManager.searchMaterials(criteria, region);
export const getMaterialPrice = (materialId: string, region?: string, purchaseType?: PurchaseType) =>
  materialManager.getMaterialPrice(materialId, region, purchaseType);
export const calculateMaterialCost = (consumption: MaterialConsumption, region?: string, purchaseType?: PurchaseType) =>
  materialManager.calculateMaterialCost(consumption, region, purchaseType);
export const getPopularMaterials = (category?: string, limit?: number) =>
  materialManager.getPopularMaterials(category, limit);
export const getCategories = () => materialManager.getCategories();
export const getBrands = (category?: string) => materialManager.getBrands(category);

// Export the manager class and instance
export default materialManager;