/**
 * Enhanced Modal Component for MVP Design System
 * Professional modal with animations, variants, and accessibility
 */

import * as React from "react";
import * as DialogPrimitive from "@radix-ui/react-dialog";
import { motion, AnimatePresence, type Variants } from "framer-motion";
import { X } from "lucide-react";
import { cn } from "@/lib/utils";

type ModalVariant = "default" | "success" | "warning" | "error" | "info";
type ModalSize = "sm" | "md" | "lg" | "xl" | "full";

interface ModalProps {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  variant?: ModalVariant;
  size?: ModalSize;
  title?: string;
  description?: string;
  showCloseButton?: boolean;
  closeOnEscape?: boolean;
  closeOnOverlayClick?: boolean;
  preventScroll?: boolean;
  children?: React.ReactNode;
  footer?: React.ReactNode;
  className?: string;
}

// Animation variants
const overlayVariants: Variants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: { duration: 0.2 },
  },
  exit: {
    opacity: 0,
    transition: { duration: 0.2 },
  },
};

const contentVariants: Variants = {
  hidden: {
    opacity: 0,
    scale: 0.95,
    y: 20,
  },
  visible: {
    opacity: 1,
    scale: 1,
    y: 0,
    transition: {
      duration: 0.3,
      ease: "easeOut",
    },
  },
  exit: {
    opacity: 0,
    scale: 0.95,
    y: 20,
    transition: {
      duration: 0.2,
      ease: "easeIn",
    },
  },
};

// Variant styles for title area
const variantClasses: Record<ModalVariant, string> = {
  default: "border-secondary-200",
  success: "border-green-200 bg-green-50/50",
  warning: "border-yellow-200 bg-yellow-50/50",
  error: "border-red-200 bg-red-50/50",
  info: "border-blue-200 bg-blue-50/50",
};

// Size styles
const sizeClasses: Record<ModalSize, string> = {
  sm: "max-w-sm",
  md: "max-w-md",
  lg: "max-w-lg",
  xl: "max-w-xl",
  full: "max-w-none m-4",
};

// Variant icons
const variantIcons: Record<ModalVariant, React.ReactNode> = {
  default: null,
  success: (
    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-green-100">
      <span className="text-green-600">✓</span>
    </div>
  ),
  warning: (
    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-yellow-100">
      <span className="text-yellow-600">⚠</span>
    </div>
  ),
  error: (
    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-red-100">
      <span className="text-red-600">✕</span>
    </div>
  ),
  info: (
    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100">
      <span className="text-blue-600">i</span>
    </div>
  ),
};

export const EnhancedModal = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Content>,
  ModalProps
>(
  (
    {
      open,
      onOpenChange,
      variant = "default",
      size = "md",
      title,
      description,
      showCloseButton = true,
      closeOnEscape = true,
      closeOnOverlayClick = true,
      preventScroll = true,
      children,
      footer,
      className,
    },
    ref
  ) => {
    return (
      <DialogPrimitive.Root
        open={open}
        onOpenChange={onOpenChange}
      >
        <AnimatePresence>
          {open && (
            <DialogPrimitive.Portal forceMount>
              {/* Overlay */}
              <DialogPrimitive.Overlay asChild>
                <motion.div
                  className="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm"
                  variants={overlayVariants}
                  initial="hidden"
                  animate="visible"
                  exit="exit"
                  onClick={closeOnOverlayClick ? undefined : (e) => e.preventDefault()}
                />
              </DialogPrimitive.Overlay>

              {/* Content */}
              <DialogPrimitive.Content
                ref={ref}
                className={cn(
                  "fixed left-1/2 top-1/2 z-50 w-full -translate-x-1/2 -translate-y-1/2",
                  "bg-white rounded-lg shadow-2xl",
                  "focus:outline-none focus:ring-4 focus:ring-primary-500/20",
                  sizeClasses[size],
                  size === "full" && "min-h-[calc(100vh-2rem)]",
                  className
                )}
                onEscapeKeyDown={closeOnEscape ? undefined : (e) => e.preventDefault()}
                asChild
              >
                <motion.div
                  variants={contentVariants}
                  initial="hidden"
                  animate="visible"
                  exit="exit"
                >
                  {/* Header */}
                  {(title || showCloseButton) && (
                    <div
                      className={cn(
                        "flex items-center justify-between p-6 border-b",
                        variantClasses[variant]
                      )}
                    >
                      <div className="flex items-center gap-3">
                        {/* Variant Icon */}
                        {variantIcons[variant]}

                        <div>
                          {/* Title */}
                          {title && (
                            <DialogPrimitive.Title className="text-lg font-semibold text-secondary-900">
                              {title}
                            </DialogPrimitive.Title>
                          )}

                          {/* Description */}
                          {description && (
                            <DialogPrimitive.Description className="text-sm text-secondary-600 mt-1">
                              {description}
                            </DialogPrimitive.Description>
                          )}
                        </div>
                      </div>

                      {/* Close Button */}
                      {showCloseButton && (
                        <DialogPrimitive.Close asChild>
                          <motion.button
                            className={cn(
                              "rounded-lg p-2 text-secondary-400 transition-colors",
                              "hover:bg-secondary-100 hover:text-secondary-600",
                              "focus:outline-none focus:ring-2 focus:ring-primary-500/20"
                            )}
                            whileHover={{ scale: 1.1 }}
                            whileTap={{ scale: 0.95 }}
                            aria-label="Close modal"
                          >
                            <X className="h-4 w-4" />
                          </motion.button>
                        </DialogPrimitive.Close>
                      )}
                    </div>
                  )}

                  {/* Content */}
                  <div className={cn("p-6", footer && "pb-4")}>
                    {children}
                  </div>

                  {/* Footer */}
                  {footer && (
                    <div className="border-t border-secondary-200 px-6 py-4 bg-secondary-50/50">
                      {footer}
                    </div>
                  )}
                </motion.div>
              </DialogPrimitive.Content>
            </DialogPrimitive.Portal>
          )}
        </AnimatePresence>
      </DialogPrimitive.Root>
    );
  }
);

EnhancedModal.displayName = "EnhancedModal";

// Compound Components
export const ModalTrigger = DialogPrimitive.Trigger;
export const ModalClose = DialogPrimitive.Close;

// Pre-built Modal Types
interface ConfirmModalProps {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  title: string;
  description?: string;
  confirmText?: string;
  cancelText?: string;
  variant?: "default" | "destructive";
  loading?: boolean;
  onConfirm?: () => void | Promise<void>;
  onCancel?: () => void;
}

export const ConfirmModal: React.FC<ConfirmModalProps> = ({
  open,
  onOpenChange,
  title,
  description,
  confirmText = "Confirm",
  cancelText = "Cancel",
  variant = "default",
  loading = false,
  onConfirm,
  onCancel,
}) => {
  const handleConfirm = async () => {
    if (onConfirm) {
      await onConfirm();
    }
    onOpenChange?.(false);
  };

  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    }
    onOpenChange?.(false);
  };

  return (
    <EnhancedModal
      open={open}
      onOpenChange={onOpenChange}
      variant={variant === "destructive" ? "error" : "default"}
      size="sm"
      title={title}
      description={description}
      footer={
        <div className="flex gap-3 justify-end">
          <button
            onClick={handleCancel}
            disabled={loading}
            className={cn(
              "px-4 py-2 text-sm font-medium rounded-lg transition-colors",
              "bg-secondary-100 text-secondary-700 hover:bg-secondary-200",
              "focus:outline-none focus:ring-2 focus:ring-secondary-500/20",
              "disabled:opacity-50 disabled:cursor-not-allowed"
            )}
          >
            {cancelText}
          </button>
          <button
            onClick={handleConfirm}
            disabled={loading}
            className={cn(
              "px-4 py-2 text-sm font-medium rounded-lg transition-colors",
              "text-white focus:outline-none focus:ring-2",
              variant === "destructive"
                ? "bg-red-600 hover:bg-red-700 focus:ring-red-500/20"
                : "bg-primary-600 hover:bg-primary-700 focus:ring-primary-500/20",
              "disabled:opacity-50 disabled:cursor-not-allowed"
            )}
          >
            {loading ? "Loading..." : confirmText}
          </button>
        </div>
      }
    />
  );
};

// Export types
export type { ModalProps, ModalVariant, ModalSize, ConfirmModalProps };