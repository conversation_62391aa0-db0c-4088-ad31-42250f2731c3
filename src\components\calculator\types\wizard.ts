/**
 * Form Wizard Types and Interfaces
 * Comprehensive type definitions for the multi-step calculator wizard
 */

import { ReactNode } from 'react';

// Result interface for the calculator
export interface CalculationResult {
  totalCost: number;
  costPerSqft: number;
  breakdown: {
    structure: number;
    finishing: number;
    mep: number;
    other: number;
  };
  builtUpArea: number;
  quality: string;
  location: string;
}

// Form data structure
export interface CalculatorFormData {
  // Step 1: Project Basics
  plotSize: string;
  builtUpArea: string;
  floors: string;
  location: string;
  buildingType: string;
  
  // Step 2: Room Configuration (handled by Room Config Agent)
  bedrooms: string;
  bathrooms: string;
  kitchens: string;
  livingRooms: string;
  studyRooms: string;
  storeRooms: string;
  balconies: string;
  
  // Step 3: Quality & Finishes (handled by Quality Agent)
  quality: string;
  flooringType: string;
  wallFinish: string;
  ceilingType: string;
  kitchenType: string;
  bathroomFixtures: string;
  electricalFittings: string;
  
  // Step 4: Advanced Features
  parkingSpaces: string;
  garden: boolean;
  swimmingPool: boolean;
  solarPanels: boolean;
  rainwaterHarvesting: boolean;
  homeAutomation: boolean;
  securitySystem: boolean;
  elevator: boolean;
  generator: boolean;
  internetCabling: boolean;
}

// Individual step validation
export interface StepValidation {
  isValid: boolean;
  errors: Record<string, string>;
  warnings?: Record<string, string>;
}

// Step configuration
export interface WizardStep {
  id: string;
  title: string;
  description: string;
  component: ReactNode;
  fields: (keyof CalculatorFormData)[];
  validate: (data: Partial<CalculatorFormData>) => StepValidation;
  optional?: boolean;
  estimatedTime?: string;
}

// Wizard navigation state
export interface WizardState {
  currentStep: number;
  totalSteps: number;
  completedSteps: Set<number>;
  skippedSteps: Set<number>;
  isValid: boolean;
  canProceed: boolean;
  canGoBack: boolean;
  progress: number;
}

// Wizard navigation actions
export interface WizardActions {
  nextStep: () => void;
  previousStep: () => void;
  goToStep: (step: number) => void;
  skipStep: () => void;
  resetWizard: () => void;
  submitForm: () => void;
}

// Form context
export interface WizardFormContext {
  data: Partial<CalculatorFormData>;
  updateData: (updates: Partial<CalculatorFormData>) => void;
  errors: Record<string, string>;
  setErrors: (errors: Record<string, string>) => void;
  isSubmitting: boolean;
  isDirty: boolean;
}

// Keyboard navigation
export interface KeyboardShortcuts {
  nextStep: string[];
  previousStep: string[];
  submitForm: string[];
  resetForm: string[];
}

// Animation settings
export interface WizardAnimations {
  stepTransition: 'slide' | 'fade' | 'scale';
  duration: number;
  easing: string;
  staggerDelay: number;
}

// Wizard configuration
export interface WizardConfig {
  steps: WizardStep[];
  animations: WizardAnimations;
  shortcuts: KeyboardShortcuts;
  autosave: boolean;
  allowSkip: boolean;
  showProgress: boolean;
  showStepNumbers: boolean;
  compactMode: boolean;
  mobileOptimized: boolean;
}

// Step component props
export interface StepComponentProps {
  data: Partial<CalculatorFormData>;
  updateData: (updates: Partial<CalculatorFormData>) => void;
  errors: Record<string, string>;
  onValidation?: (validation: StepValidation) => void;
  isActive: boolean;
  isMobile: boolean;
}

// Progress bar variants
export type ProgressVariant = 'linear' | 'circular' | 'steps' | 'dots';

// Wizard theme
export interface WizardTheme {
  primaryColor: string;
  secondaryColor: string;
  successColor: string;
  errorColor: string;
  warningColor: string;
  borderRadius: string;
  spacing: string;
}

// Local storage keys
export const WIZARD_STORAGE_KEYS = {
  FORM_DATA: 'nirmaan_calculator_form_data',
  CURRENT_STEP: 'nirmaan_calculator_current_step',
  COMPLETED_STEPS: 'nirmaan_calculator_completed_steps',
  LAST_SAVED: 'nirmaan_calculator_last_saved',
} as const;

// Default form data
export const DEFAULT_FORM_DATA: CalculatorFormData = {
  // Step 1: Project Basics
  plotSize: '',
  builtUpArea: '',
  floors: '1',
  location: 'delhi',
  buildingType: 'residential',
  
  // Step 2: Room Configuration
  bedrooms: '2',
  bathrooms: '2',
  kitchens: '1',
  livingRooms: '1',
  studyRooms: '0',
  storeRooms: '1',
  balconies: '1',
  
  // Step 3: Quality & Finishes
  quality: 'smart',
  flooringType: 'ceramic',
  wallFinish: 'paint',
  ceilingType: 'false',
  kitchenType: 'modular',
  bathroomFixtures: 'standard',
  electricalFittings: 'standard',
  
  // Step 4: Advanced Features
  parkingSpaces: '1',
  garden: false,
  swimmingPool: false,
  solarPanels: false,
  rainwaterHarvesting: false,
  homeAutomation: false,
  securitySystem: false,
  elevator: false,
  generator: false,
  internetCabling: true,
};

// Validation limits
export const VALIDATION_LIMITS = {
  plotSize: { min: 300, max: 50000 },
  builtUpArea: { min: 200, max: 40000 },
  floors: { min: 1, max: 4 },
  bedrooms: { min: 1, max: 8 },
  bathrooms: { min: 1, max: 10 },
  kitchens: { min: 1, max: 3 },
  livingRooms: { min: 1, max: 3 },
  parkingSpaces: { min: 0, max: 10 },
} as const;

// City options
export const LOCATION_OPTIONS = [
  { value: 'delhi', label: 'Delhi NCR', multiplier: 1.05 },
  { value: 'mumbai', label: 'Mumbai', multiplier: 1.2 },
  { value: 'bangalore', label: 'Bangalore', multiplier: 1.0 },
  { value: 'chennai', label: 'Chennai', multiplier: 0.95 },
  { value: 'hyderabad', label: 'Hyderabad', multiplier: 0.9 },
  { value: 'pune', label: 'Pune', multiplier: 1.0 },
  { value: 'kolkata', label: 'Kolkata', multiplier: 0.85 },
  { value: 'ahmedabad', label: 'Ahmedabad', multiplier: 0.8 },
  { value: 'jaipur', label: 'Jaipur', multiplier: 0.75 },
  { value: 'lucknow', label: 'Lucknow', multiplier: 0.7 },
] as const;

// Building type options
export const BUILDING_TYPE_OPTIONS = [
  { value: 'residential', label: 'Residential Villa' },
  { value: 'apartment', label: 'Apartment/Flat' },
  { value: 'commercial', label: 'Commercial Building' },
  { value: 'mixed', label: 'Mixed Use' },
  { value: 'farmhouse', label: 'Farmhouse' },
] as const;

// Quality tier options
export const QUALITY_OPTIONS = [
  {
    value: 'smart',
    label: 'Smart Choice',
    price: '₹1,600-2,000/sqft',
    description: 'M20 concrete, standard finishes, reliable quality',
  },
  {
    value: 'premium',
    label: 'Premium Selection', 
    price: '₹2,200-2,800/sqft',
    description: 'M25 concrete, branded materials, enhanced features',
    popular: true,
  },
  {
    value: 'luxury',
    label: 'Luxury Collection',
    price: '₹3,000+/sqft',
    description: 'M30+ concrete, international brands, premium finishes',
  },
] as const;