/**
 * Main Calculation Engine for Clarity Engine
 * Orchestrates all calculations and generates final results
 */

import type {
  CalculationInput,
  CalculationResult,
  CostBreakdown,
  CategoryCost,
  MaterialQuantity,
  ProjectSummary,
} from './types';

import {
  BASE_CONSTRUCTION_RATES,
  REGIONAL_MULTIPLIERS,
  MATERIAL_CONSUMPTION_RATES,
  CONSTRUCTION_PHASES,
  QUALITY_TIER_SPECS,
  WASTAGE_FACTORS,
} from './constants';

import { calculateStructureCost } from './calculations/structure';
import { calculateFinishingCost } from './calculations/finishing';
import { calculateMEPCost } from './calculations/mep';
import { calculateExternalCost } from './calculations/external';
import {
  getMaterialSummary,
} from './materials/quantities';
import { validateInput } from './validation';

/**
 * Main calculation function - entry point for all cost calculations
 */
export function calculate(input: CalculationInput): CalculationResult {
  // Validate input
  const validationErrors = validateInput(input);
  if (validationErrors.length > 0) {
    throw new Error(
      `Validation failed: ${validationErrors.map(e => e.message).join(', ')}`
    );
  }

  // Get regional multiplier
  const locationMultiplier =
    REGIONAL_MULTIPLIERS[input.location.toLowerCase()] ||
    REGIONAL_MULTIPLIERS.default;

  // Calculate individual cost categories
  const structureCost = calculateStructureCost(input);
  const finishingCost = calculateFinishingCost(input);
  const mepCost = calculateMEPCost(input);
  const externalCost = calculateExternalCost(input);
  const otherCost = calculateOtherCost(input);

  // Apply regional multipliers with category-specific weights
  const adjustedCosts = applyRegionalMultipliers(
    structureCost,
    finishingCost,
    mepCost,
    externalCost,
    otherCost,
    locationMultiplier
  );

  const adjustedStructure = adjustedCosts.structure;
  const adjustedFinishing = adjustedCosts.finishing;
  const adjustedMEP = adjustedCosts.mep;
  const adjustedExternal = adjustedCosts.external;
  const adjustedOther = adjustedCosts.other;

  // Calculate total cost
  const totalCost =
    adjustedStructure.amount +
    adjustedFinishing.amount +
    adjustedMEP.amount +
    adjustedExternal.amount +
    adjustedOther.amount;

  // Recalculate percentages based on actual totals
  const recalculatePercentages = (categoryAmount: number) =>
    Math.round((categoryAmount / totalCost) * 100 * 10) / 10;

  const breakdown: CostBreakdown = {
    structure: {
      ...adjustedStructure,
      percentage: recalculatePercentages(adjustedStructure.amount),
    },
    finishing: {
      ...adjustedFinishing,
      percentage: recalculatePercentages(adjustedFinishing.amount),
    },
    mep: {
      ...adjustedMEP,
      percentage: recalculatePercentages(adjustedMEP.amount),
    },
    external: {
      ...adjustedExternal,
      percentage: recalculatePercentages(adjustedExternal.amount),
    },
    other: {
      ...adjustedOther,
      percentage: recalculatePercentages(adjustedOther.amount),
    },
    total: totalCost,
  };

  // Calculate material quantities using detailed calculation method
  const materialSummary = getMaterialSummary(input);
  const materials = materialSummary.materials.map(material => ({
    category: material.material,
    name: getQualityMaterialName(
      material.material.toLowerCase(),
      input.qualityTier
    ),
    quantity: material.totalQuantity,
    unit: material.unit,
    rate: Math.round(material.rate * locationMultiplier),
    totalCost: Math.round(material.totalAmount * locationMultiplier),
    purpose: getMaterialPurpose(material.material.toLowerCase()),
    specifications: getQualitySpecifications(
      material.material.toLowerCase(),
      input.qualityTier
    ),
  }));

  // Generate construction timeline
  const timeline = generateConstructionTimeline(input);

  // Calculate total built-up area
  const totalBuiltUpArea = input.builtUpArea * (input.floors + 1);
  const costPerSqft = Math.round(totalCost / totalBuiltUpArea);

  // Generate project summary
  const summary: ProjectSummary = {
    totalBuiltUpArea,
    carpetArea: Math.round(totalBuiltUpArea * 0.7), // Approx 70% carpet area
    constructionDuration:
      Math.ceil(
        timeline[timeline.length - 1].startAfter +
          timeline[timeline.length - 1].duration
      ) / 4, // Convert weeks to months
    totalCost,
    costPerSqft,
    qualityTier: input.qualityTier,
    location: input.location,
    estimateAccuracy: getEstimateAccuracy(input),
  };

  return {
    totalCost,
    costPerSqft,
    breakdown,
    materials,
    timeline,
    summary,
  };
}

/**
 * Apply regional multipliers with different weights for different cost categories
 * Some costs like labor vary more by region than materials
 */
function applyRegionalMultipliers(
  structureCost: CategoryCost,
  finishingCost: CategoryCost,
  mepCost: CategoryCost,
  externalCost: CategoryCost,
  otherCost: CategoryCost,
  locationMultiplier: number
) {
  // Different categories have different regional sensitivity
  const regionalWeights = {
    structure: 0.85, // Structure costs are more standardized
    finishing: 1.0, // Finishing costs vary significantly by region
    mep: 0.9, // MEP costs have moderate regional variation
    external: 0.8, // External works are less region-sensitive
    other: 1.0, // Other costs (labor, approvals) vary significantly
  };

  return {
    structure: {
      ...structureCost,
      amount: Math.round(
        structureCost.amount *
          (1 + (locationMultiplier - 1) * regionalWeights.structure)
      ),
      subCategories: structureCost.subCategories.map(sub => ({
        ...sub,
        amount: Math.round(
          sub.amount *
            (1 + (locationMultiplier - 1) * regionalWeights.structure)
        ),
      })),
    },
    finishing: {
      ...finishingCost,
      amount: Math.round(
        finishingCost.amount *
          (1 + (locationMultiplier - 1) * regionalWeights.finishing)
      ),
      subCategories: finishingCost.subCategories.map(sub => ({
        ...sub,
        amount: Math.round(
          sub.amount *
            (1 + (locationMultiplier - 1) * regionalWeights.finishing)
        ),
      })),
    },
    mep: {
      ...mepCost,
      amount: Math.round(
        mepCost.amount * (1 + (locationMultiplier - 1) * regionalWeights.mep)
      ),
      subCategories: mepCost.subCategories.map(sub => ({
        ...sub,
        amount: Math.round(
          sub.amount * (1 + (locationMultiplier - 1) * regionalWeights.mep)
        ),
      })),
    },
    external: {
      ...externalCost,
      amount: Math.round(
        externalCost.amount *
          (1 + (locationMultiplier - 1) * regionalWeights.external)
      ),
      subCategories: externalCost.subCategories.map(sub => ({
        ...sub,
        amount: Math.round(
          sub.amount * (1 + (locationMultiplier - 1) * regionalWeights.external)
        ),
      })),
    },
    other: {
      ...otherCost,
      amount: Math.round(
        otherCost.amount *
          (1 + (locationMultiplier - 1) * regionalWeights.other)
      ),
      subCategories: otherCost.subCategories.map(sub => ({
        ...sub,
        amount: Math.round(
          sub.amount * (1 + (locationMultiplier - 1) * regionalWeights.other)
        ),
      })),
    },
  };
}

/**
 * Calculate other costs (contingency, supervision, approvals)
 */
function calculateOtherCost(input: CalculationInput): CategoryCost {
  const { builtUpArea, floors, qualityTier } = input;
  const baseRate = BASE_CONSTRUCTION_RATES[qualityTier];
  const totalArea = builtUpArea * (floors + 1);

  // Other costs are typically 5% of total construction cost
  const otherCostPerSqft = baseRate * 0.05;
  const totalOtherCost = totalArea * otherCostPerSqft;

  const subCategories = [
    {
      name: 'Contingency',
      amount: Math.round(totalOtherCost * 0.5),
      percentage: 50,
      description: 'Unforeseen expenses and cost variations',
    },
    {
      name: 'Supervision',
      amount: Math.round(totalOtherCost * 0.3),
      percentage: 30,
      description: 'Site supervision and project management',
    },
    {
      name: 'Approvals & Documentation',
      amount: Math.round(totalOtherCost * 0.2),
      percentage: 20,
      description: 'Building permits, NOCs, and legal documentation',
    },
  ];

  return {
    amount: Math.round(totalOtherCost),
    percentage: 5,
    subCategories,
  };
}

/**
 * Calculate material quantities with wastage factors
 * TODO: Remove if not needed, currently unused
 */
/*
function calculateMaterialQuantities(
  input: CalculationInput,
  locationMultiplier: number
): MaterialQuantity[] {
  const { builtUpArea, floors, qualityTier } = input;
  const totalArea = builtUpArea * (floors + 1);

  return MATERIAL_CONSUMPTION_RATES.map(material => {
    const baseConsumption = material.qualityVariation
      ? material.qualityVariation[qualityTier]
      : material.consumptionPerSqft;

    // Apply wastage factor
    const wastage =
      WASTAGE_FACTORS[material.material as keyof typeof WASTAGE_FACTORS] ||
      WASTAGE_FACTORS.default;
    const actualConsumption = baseConsumption * (1 + wastage);

    const quantity = Math.ceil(totalArea * actualConsumption);

    // Estimate rates (in real implementation, fetch from materials database)
    const estimatedRates = getEstimatedMaterialRates(
      material.material,
      qualityTier,
      locationMultiplier
    );
    const totalCost = quantity * estimatedRates.rate;

    return {
      category:
        material.material.charAt(0).toUpperCase() + material.material.slice(1),
      name: getQualityMaterialName(material.material, qualityTier),
      quantity,
      unit: material.unit,
      rate: estimatedRates.rate,
      totalCost,
      purpose: getMaterialPurpose(material.material),
      specifications: getQualitySpecifications(material.material, qualityTier),
    };
  });
}
*/

/**
 * Generate construction timeline based on project scope
 */
function generateConstructionTimeline(input: CalculationInput) {
  const { floors } = input;

  return CONSTRUCTION_PHASES.map(phase => {
    let adjustedDuration = phase.duration;

    // Adjust duration based on floors for superstructure
    if (phase.name.includes('Superstructure')) {
      adjustedDuration = phase.duration * (floors + 1); // +1 for ground floor
    }

    return {
      ...phase,
      duration: adjustedDuration,
    };
  });
}

// Helper functions
function getEstimateAccuracy(input: CalculationInput): string {
  // Accuracy depends on input completeness and project complexity
  if (input.builtUpArea < 1000) return '±15%';
  if (input.builtUpArea < 3000) return '±12%';
  return '±10%';
}

function getEstimatedMaterialRates(
  material: string,
  qualityTier: 'smart' | 'premium' | 'luxury',
  locationMultiplier: number
) {
  // Base material rates (should come from materials database in production)
  const baseRates = {
    cement: { smart: 420, premium: 450, luxury: 480 },
    steel: { smart: 65, premium: 70, luxury: 75 },
    bricks: { smart: 8, premium: 12, luxury: 15 },
    sand: { smart: 35, premium: 40, luxury: 45 },
    aggregate: { smart: 45, premium: 50, luxury: 55 },
  };

  const rate =
    (baseRates[material as keyof typeof baseRates]?.[qualityTier] || 100) *
    locationMultiplier;

  return { rate: Math.round(rate) };
}

function getQualityMaterialName(
  material: string,
  qualityTier: 'smart' | 'premium' | 'luxury'
): string {
  const qualityNames = {
    cement: {
      smart: 'OPC 43 Grade Cement',
      premium: 'OPC 53 Grade Cement (UltraTech)',
      luxury: 'OPC 53 Grade Cement (ACC/Ambuja)',
    },
    steel: {
      smart: 'TMT Steel Fe415',
      premium: 'TMT Steel Fe500 (TATA/SAIL)',
      luxury: 'TMT Steel Fe500D (Premium Brand)',
    },
    // Add more materials as needed
  };

  return (
    qualityNames[material as keyof typeof qualityNames]?.[qualityTier] ||
    `${material.charAt(0).toUpperCase() + material.slice(1)} (${qualityTier})`
  );
}

function getMaterialPurpose(material: string): string {
  const purposes = {
    cement: 'RCC work, plastering, and masonry',
    steel: 'RCC reinforcement and structural work',
    bricks: 'Wall construction and masonry work',
    sand: 'RCC work, plastering, and masonry',
    aggregate: 'RCC work and concrete production',
  };

  return purposes[material as keyof typeof purposes] || 'Construction work';
}

function getQualitySpecifications(
  material: string,
  qualityTier: 'smart' | 'premium' | 'luxury'
): string {
  // Return relevant specifications based on quality tier
  const specs = QUALITY_TIER_SPECS[qualityTier];

  if (material === 'cement') return `Grade: ${specs.concreteGrade}`;
  if (material === 'steel') return `Grade: ${specs.steelGrade}`;
  if (material === 'bricks') return specs.brickType;

  return `Quality: ${qualityTier} grade`;
}

// Export alias for backward compatibility with tests
export const calculateConstructionCost = calculate;
export const calculateConstruction = calculate;
