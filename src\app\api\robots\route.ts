import { NextResponse } from 'next/server';

export async function GET() {
  const robotsContent = `
User-agent: *
Allow: /
Allow: /calculator
Allow: /projects
Allow: /about
Allow: /contact

Disallow: /api/
Disallow: /auth/
Disallow: /_next/
Disallow: /admin/
Disallow: /dashboard/private/

# Sitemap location
Sitemap: ${process.env.NEXT_PUBLIC_APP_URL || 'https://clarity-engine.vercel.app'}/sitemap.xml

# Crawl delay (optional, in seconds)
Crawl-delay: 1

# Additional directives for SEO
User-agent: Googlebot
Allow: /
Crawl-delay: 0

User-agent: Bingbot
Allow: /
Crawl-delay: 1

User-agent: *
Disallow: /api/auth/
Disallow: /api/admin/
Disallow: /private/
  `.trim();

  return new NextResponse(robotsContent, {
    headers: {
      'Content-Type': 'text/plain',
      'Cache-Control': 'public, max-age=86400', // Cache for 24 hours
    },
  });
}