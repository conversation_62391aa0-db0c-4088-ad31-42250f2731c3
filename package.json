{"name": "clarity-engine", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,css,md}\"", "format:check": "prettier --check \"**/*.{js,jsx,ts,tsx,json,css,md}\"", "type-check": "tsc --noEmit", "prepare": "husky", "build:analyze": "ANALYZE=true npm run build", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest --testPathPattern=__tests__", "test:integration": "jest --testPathPattern=integration", "test:backend": "vitest run tests/backend/day5-backend-verification.test.ts", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:preview": "PLAYWRIGHT_BASE_URL=$PREVIEW_URL playwright test", "test:e2e:production": "PLAYWRIGHT_BASE_URL=$PRODUCTION_URL playwright test", "test:e2e:enhanced": "playwright test --config=playwright.enhanced.config.ts", "test:e2e:mobile": "playwright test --config=playwright.enhanced.config.ts --project=mobile-chrome,mobile-safari", "test:e2e:performance": "playwright test --config=playwright.enhanced.config.ts --project=performance", "test:e2e:accessibility": "playwright test --config=playwright.enhanced.config.ts --project=accessibility", "test:e2e:visual": "playwright test --config=playwright.enhanced.config.ts --project=visual-regression", "test:vitest": "vitest run", "test:vitest:watch": "vitest", "test:vitest:ui": "vitest --ui", "test:vitest:coverage": "vitest run --coverage", "test:a11y": "jest --testPathPattern=accessibility", "test:performance": "jest --testPathPattern=performance", "test:performance:regression": "playwright test --config=playwright.enhanced.config.ts --project=performance", "test:api": "jest --testPathPattern=api", "test:api:report": "jest --testPathPattern=api --coverage", "test:mobile": "playwright test --config=playwright.enhanced.config.ts --project=mobile", "test:visual": "playwright test --config=playwright.enhanced.config.ts --project=visual-regression", "test:all": "npm run test && npm run test:e2e", "test:comprehensive": "node tests/test-runner.js", "test:quick": "node tests/test-runner.js --quick", "health-check": "curl -f http://localhost:3000/api/health || exit 1", "health-check:production": "curl -f $PRODUCTION_URL/api/health || exit 1", "validate": "npm run type-check && npm run lint && npm run format:check", "clean": "rm -rf .next out dist", "prebuild": "echo 'Skipping type-check for deployment'", "deploy:preview": "vercel", "deploy:production": "vercel --prod", "deploy:check": "npm run validate && npm run build && npm run health-check", "lighthouse": "lighthouse http://localhost:3000 --chrome-flags=\"--headless\" --output=html --output-path=lighthouse-report.html", "lighthouse:production": "lighthouse $PRODUCTION_URL --chrome-flags=\"--headless\" --output=html --output-path=lighthouse-production.html", "analyze": "ANALYZE=true npm run build", "bundle-analyzer": "npm run build:analyze && npx @next/bundle-analyzer", "bundle-report": "npm run build && node scripts/bundle-analyzer.js", "bundle-baseline": "npm run build && node scripts/bundle-analyzer.js baseline", "db:reset": "supabase db reset --local", "db:seed": "supabase db seed --local", "db:migrate": "supabase db push --local", "security-audit": "npm audit --audit-level moderate", "security:test": "node -e \"console.log('🔒 Security tests would run via API endpoints')\"", "security:scan": "node -e \"console.log('🔍 Security scanning would run via API endpoints')\"", "security:monitor": "node -e \"console.log('📊 Security monitoring dashboard available at /api/security/monitor')\"", "precommit-check": "npm run validate && npm run test:quick", "production-ready": "npm run security-audit && npm run validate && npm run test:comprehensive && npm run build"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/supabase-js": "^2.50.5", "@tanstack/react-query": "^5.83.0", "@tanstack/react-query-devtools": "^5.83.0", "@types/validator": "^13.15.2", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.23.3", "jspdf": "^3.0.1", "lucide-react": "^0.525.0", "next": "15.3.5", "node-fetch": "^3.3.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.60.0", "react-hot-toast": "^2.5.2", "recharts": "^3.1.0", "tailwind-merge": "^3.3.1", "zod": "^4.0.5", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@next/bundle-analyzer": "^15.4.1", "@playwright/test": "^1.54.1", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/jest-axe": "^3.5.9", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@typescript-eslint/eslint-plugin": "^8.36.0", "@typescript-eslint/parser": "^8.36.0", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "axe-playwright": "^2.0.1", "eslint": "^9", "eslint-config-next": "15.3.5", "eslint-plugin-import": "^2.32.0", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-unused-imports": "^4.1.4", "husky": "^9.1.7", "identity-obj-proxy": "^3.0.0", "jest": "^30.0.4", "jest-axe": "^10.0.0", "jest-environment-jsdom": "^30.0.4", "lint-staged": "^16.1.2", "prettier": "^3.6.2", "puppeteer": "^24.12.1", "tailwindcss": "^4", "tw-animate-css": "^1.3.5", "typescript": "^5", "vitest": "^3.2.4", "web-vitals": "^5.0.3", "webpack-bundle-analyzer": "^4.10.2"}, "optionalDependencies": {"dompurify": "^3.2.3", "validator": "^13.12.0"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,css,md}": ["prettier --write"], "*.{ts,tsx}": ["tsc --noEmit"]}}