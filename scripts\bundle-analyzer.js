#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const { gzipSync } = require('zlib');

/**
 * Bundle Size Analysis Script
 * Generates detailed reports on bundle size optimization
 */

class BundleAnalyzer {
  constructor() {
    this.rootDir = process.cwd();
    this.buildDir = path.join(this.rootDir, '.next');
    this.reportDir = path.join(this.rootDir, 'reports');
    this.baselineFile = path.join(this.reportDir, 'baseline.json');
    
    // Ensure reports directory exists
    if (!fs.existsSync(this.reportDir)) {
      fs.mkdirSync(this.reportDir, { recursive: true });
    }
  }

  // Get current build statistics
  getBuildStats() {
    const statsPath = path.join(this.buildDir, 'build-manifest.json');
    const serverPath = path.join(this.buildDir, 'server');
    const staticPath = path.join(this.buildDir, 'static');
    
    if (!fs.existsSync(statsPath)) {
      throw new Error('Build manifest not found. Run "npm run build" first.');
    }

    const manifest = JSON.parse(fs.readFileSync(statsPath, 'utf8'));
    const stats = {
      timestamp: Date.now(),
      pages: {},
      chunks: {},
      totalSize: 0,
      gzippedSize: 0,
    };

    // Analyze static files
    if (fs.existsSync(staticPath)) {
      const staticFiles = this.getFileStats(staticPath);
      stats.chunks = staticFiles.files;
      stats.totalSize = staticFiles.totalSize;
      stats.gzippedSize = staticFiles.gzippedSize;
    }

    // Analyze pages
    if (manifest.pages) {
      Object.keys(manifest.pages).forEach(page => {
        const pageFiles = manifest.pages[page];
        stats.pages[page] = {
          files: pageFiles,
          size: this.calculatePageSize(pageFiles),
        };
      });
    }

    return stats;
  }

  // Get file statistics recursively
  getFileStats(dir) {
    const files = {};
    let totalSize = 0;
    let gzippedSize = 0;

    const walkDir = (currentPath) => {
      const items = fs.readdirSync(currentPath);
      
      items.forEach(item => {
        const itemPath = path.join(currentPath, item);
        const stat = fs.statSync(itemPath);
        
        if (stat.isDirectory()) {
          walkDir(itemPath);
        } else if (item.endsWith('.js') || item.endsWith('.css')) {
          const relativePath = path.relative(this.rootDir, itemPath);
          const content = fs.readFileSync(itemPath);
          const size = content.length;
          const gzipped = gzipSync(content).length;
          
          files[relativePath] = {
            size,
            gzipped,
            type: item.endsWith('.js') ? 'javascript' : 'css',
          };
          
          totalSize += size;
          gzippedSize += gzipped;
        }
      });
    };

    walkDir(dir);
    return { files, totalSize, gzippedSize };
  }

  // Calculate page size from file list
  calculatePageSize(files) {
    let size = 0;
    files.forEach(file => {
      const filePath = path.join(this.buildDir, file);
      if (fs.existsSync(filePath)) {
        size += fs.statSync(filePath).size;
      }
    });
    return size;
  }

  // Load baseline data
  loadBaseline() {
    if (fs.existsSync(this.baselineFile)) {
      return JSON.parse(fs.readFileSync(this.baselineFile, 'utf8'));
    }
    return null;
  }

  // Save baseline data
  saveBaseline(stats) {
    fs.writeFileSync(this.baselineFile, JSON.stringify(stats, null, 2));
  }

  // Compare with baseline
  compareWithBaseline(current, baseline) {
    const comparison = {
      totalSize: {
        current: current.totalSize,
        baseline: baseline.totalSize,
        change: current.totalSize - baseline.totalSize,
        changePercent: ((current.totalSize - baseline.totalSize) / baseline.totalSize) * 100,
      },
      gzippedSize: {
        current: current.gzippedSize,
        baseline: baseline.gzippedSize,
        change: current.gzippedSize - baseline.gzippedSize,
        changePercent: ((current.gzippedSize - baseline.gzippedSize) / baseline.gzippedSize) * 100,
      },
      pages: {},
      chunks: {},
    };

    // Compare pages
    Object.keys(current.pages).forEach(page => {
      const currentPage = current.pages[page];
      const baselinePage = baseline.pages[page];
      
      if (baselinePage) {
        comparison.pages[page] = {
          current: currentPage.size,
          baseline: baselinePage.size,
          change: currentPage.size - baselinePage.size,
          changePercent: ((currentPage.size - baselinePage.size) / baselinePage.size) * 100,
        };
      } else {
        comparison.pages[page] = {
          current: currentPage.size,
          baseline: 0,
          change: currentPage.size,
          changePercent: 100,
        };
      }
    });

    // Compare chunks
    Object.keys(current.chunks).forEach(chunk => {
      const currentChunk = current.chunks[chunk];
      const baselineChunk = baseline.chunks[chunk];
      
      if (baselineChunk) {
        comparison.chunks[chunk] = {
          current: currentChunk.size,
          baseline: baselineChunk.size,
          change: currentChunk.size - baselineChunk.size,
          changePercent: ((currentChunk.size - baselineChunk.size) / baselineChunk.size) * 100,
        };
      } else {
        comparison.chunks[chunk] = {
          current: currentChunk.size,
          baseline: 0,
          change: currentChunk.size,
          changePercent: 100,
        };
      }
    });

    return comparison;
  }

  // Format size for display
  formatSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // Generate HTML report
  generateHtmlReport(stats, comparison) {
    const html = `
<!DOCTYPE html>
<html>
<head>
  <title>Bundle Size Report</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .summary { background: #f5f5f5; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
    .metric { display: inline-block; margin: 10px; padding: 10px; background: white; border-radius: 3px; }
    .positive { color: #d32f2f; }
    .negative { color: #388e3c; }
    .table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
    .table th, .table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    .table th { background-color: #f2f2f2; }
    .chart { width: 100%; height: 400px; margin: 20px 0; }
  </style>
</head>
<body>
  <h1>Bundle Size Analysis Report</h1>
  <div class="summary">
    <h2>Summary</h2>
    <div class="metric">
      <strong>Total Size:</strong> ${this.formatSize(stats.totalSize)}
      ${comparison ? `(${comparison.totalSize.change > 0 ? '+' : ''}${this.formatSize(comparison.totalSize.change)})` : ''}
    </div>
    <div class="metric">
      <strong>Gzipped Size:</strong> ${this.formatSize(stats.gzippedSize)}
      ${comparison ? `(${comparison.gzippedSize.change > 0 ? '+' : ''}${this.formatSize(comparison.gzippedSize.change)})` : ''}
    </div>
    <div class="metric">
      <strong>Compression Ratio:</strong> ${((stats.gzippedSize / stats.totalSize) * 100).toFixed(1)}%
    </div>
  </div>

  <h2>Pages</h2>
  <table class="table">
    <thead>
      <tr>
        <th>Page</th>
        <th>Size</th>
        ${comparison ? '<th>Change</th>' : ''}
      </tr>
    </thead>
    <tbody>
      ${Object.keys(stats.pages).map(page => {
        const pageStats = stats.pages[page];
        const pageComparison = comparison?.pages[page];
        return `
          <tr>
            <td>${page}</td>
            <td>${this.formatSize(pageStats.size)}</td>
            ${comparison ? `<td class="${pageComparison.change > 0 ? 'positive' : 'negative'}">
              ${pageComparison.change > 0 ? '+' : ''}${this.formatSize(pageComparison.change)}
              (${pageComparison.changePercent.toFixed(1)}%)
            </td>` : ''}
          </tr>
        `;
      }).join('')}
    </tbody>
  </table>

  <h2>Chunks</h2>
  <table class="table">
    <thead>
      <tr>
        <th>Chunk</th>
        <th>Type</th>
        <th>Size</th>
        <th>Gzipped</th>
        ${comparison ? '<th>Change</th>' : ''}
      </tr>
    </thead>
    <tbody>
      ${Object.keys(stats.chunks).map(chunk => {
        const chunkStats = stats.chunks[chunk];
        const chunkComparison = comparison?.chunks[chunk];
        return `
          <tr>
            <td>${chunk}</td>
            <td>${chunkStats.type}</td>
            <td>${this.formatSize(chunkStats.size)}</td>
            <td>${this.formatSize(chunkStats.gzipped)}</td>
            ${comparison ? `<td class="${chunkComparison.change > 0 ? 'positive' : 'negative'}">
              ${chunkComparison.change > 0 ? '+' : ''}${this.formatSize(chunkComparison.change)}
              (${chunkComparison.changePercent.toFixed(1)}%)
            </td>` : ''}
          </tr>
        `;
      }).join('')}
    </tbody>
  </table>

  <div class="chart">
    <h3>Bundle Size Visualization</h3>
    <canvas id="bundleChart"></canvas>
  </div>

  <script>
    // Simple visualization would go here
    console.log('Bundle analysis complete');
  </script>
</body>
</html>
    `;

    return html;
  }

  // Main analysis function
  analyze(setBaseline = false) {
    console.log('🔍 Analyzing bundle size...');
    
    const currentStats = this.getBuildStats();
    const baseline = this.loadBaseline();
    
    if (setBaseline || !baseline) {
      this.saveBaseline(currentStats);
      console.log('✅ Baseline saved');
      return;
    }

    const comparison = this.compareWithBaseline(currentStats, baseline);
    
    // Generate reports
    const htmlReport = this.generateHtmlReport(currentStats, comparison);
    fs.writeFileSync(path.join(this.reportDir, 'bundle-report.html'), htmlReport);
    
    const jsonReport = {
      current: currentStats,
      baseline,
      comparison,
    };
    fs.writeFileSync(path.join(this.reportDir, 'bundle-report.json'), JSON.stringify(jsonReport, null, 2));
    
    // Console output
    console.log('\n📊 Bundle Size Analysis Report');
    console.log('================================');
    console.log(`Total Size: ${this.formatSize(currentStats.totalSize)} (${comparison.totalSize.change > 0 ? '+' : ''}${this.formatSize(comparison.totalSize.change)})`);
    console.log(`Gzipped Size: ${this.formatSize(currentStats.gzippedSize)} (${comparison.gzippedSize.change > 0 ? '+' : ''}${this.formatSize(comparison.gzippedSize.change)})`);
    console.log(`Compression Ratio: ${((currentStats.gzippedSize / currentStats.totalSize) * 100).toFixed(1)}%`);
    
    // Check for significant changes
    if (Math.abs(comparison.totalSize.changePercent) > 10) {
      console.log(`\n⚠️  Significant bundle size change: ${comparison.totalSize.changePercent.toFixed(1)}%`);
    }
    
    if (comparison.totalSize.changePercent < -5) {
      console.log(`\n🎉 Bundle size reduced by ${Math.abs(comparison.totalSize.changePercent).toFixed(1)}%!`);
    }
    
    console.log(`\n📋 Full report saved to: ${path.join(this.reportDir, 'bundle-report.html')}`);
  }
}

// CLI interface
const command = process.argv[2];
const analyzer = new BundleAnalyzer();

try {
  if (command === 'baseline') {
    analyzer.analyze(true);
  } else {
    analyzer.analyze();
  }
} catch (error) {
  console.error('❌ Error:', error.message);
  process.exit(1);
}