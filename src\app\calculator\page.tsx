import { Suspense } from 'react';
import { LightweightCalculatorContainer } from '@/components/calculator/LightweightCalculatorContainer';
import { Container } from '@/components/layout/Container';
import { SimpleHeader } from '@/components/layout/SimpleHeader';
import { SimpleFooter } from '@/components/layout/SimpleFooter';
import { Skeleton } from '@/components/ui/skeleton';

// Lightweight loading skeleton for the page
const PageSkeleton = () => (
  <div className="max-w-4xl mx-auto space-y-8" data-testid="page-skeleton">
    <div className="text-center space-y-4" data-testid="skeleton-header">
      <Skeleton className="h-8 w-96 mx-auto" />
      <Skeleton className="h-6 w-[500px] mx-auto" />
      <Skeleton className="h-6 w-[400px] mx-auto" />
    </div>
    <div className="space-y-4" data-testid="skeleton-form">
      <Skeleton className="h-20 w-full" />
      <Skeleton className="h-40 w-full" />
      <Skeleton className="h-12 w-full" />
    </div>
  </div>
);

export default function CalculatorPage() {
  return (
    <>
      <SimpleHeader />
      <Container>
        <main className='py-8' data-testid="calculator-page" role="main">
          <div className='text-center mb-8' data-testid="page-header">
            <h1 className='text-3xl font-bold text-gray-900 mb-4' data-testid="page-title">
              Construction Cost Calculator
            </h1>
            <p className='text-lg text-gray-600 max-w-2xl mx-auto' data-testid="page-description">
              Get accurate construction cost estimates for your dream home. Our
              AI-powered calculator provides detailed breakdowns based on current
              market rates and Indian construction standards.
            </p>
          </div>
          <Suspense fallback={<PageSkeleton />}>
            <LightweightCalculatorContainer />
          </Suspense>
        </main>
      </Container>
      <SimpleFooter />
    </>
  );
}
