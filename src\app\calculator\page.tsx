'use client';

import { Suspense } from 'react';
import { CalculatorContainer } from '@/components/calculator/CalculatorContainer';
import { Container } from '@/components/layout/Container';
import { Header } from '@/components/layout/Header';
import { Footer } from '@/components/layout/Footer';
import { Skeleton } from '@/components/ui/skeleton';

// Simple loading skeleton for testing
const PageSkeleton = () => (
  <div className="max-w-4xl mx-auto space-y-8" data-testid="page-skeleton">
    <div className="text-center space-y-4" data-testid="skeleton-header">
      <Skeleton className="h-8 w-96 mx-auto" />
      <Skeleton className="h-6 w-[500px] mx-auto" />
      <Skeleton className="h-6 w-[400px] mx-auto" />
    </div>
    <div className="space-y-4" data-testid="skeleton-form">
      <Skeleton className="h-20 w-full" />
      <Skeleton className="h-40 w-full" />
      <Skeleton className="h-12 w-full" />
    </div>
  </div>
);

export default function CalculatorPage() {
  return (
    <>
      <Header />
      <Container>
        <main className='py-8' data-testid="calculator-page" role="main">
          <div className='text-center mb-8' data-testid="page-header">
            <h1 className='text-3xl font-bold text-gray-900 mb-4' data-testid="page-title">
              Construction Cost Calculator
            </h1>
            <p className='text-gray-600 max-w-2xl mx-auto' data-testid="page-description">
              Get accurate construction cost estimates for your project in India. Our advanced calculator considers location, quality, and specifications.
            </p>
          </div>
          
          <Suspense fallback={<PageSkeleton />}>
            <CalculatorContainer />
          </Suspense>
        </main>
      </Container>
      <Footer />
    </>
  );
}