/**
 * Mobile Calculator Layout Component
 * Provides mobile-optimized calculator interface with native mobile patterns
 */

'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  ArrowLeft, 
  Calculator, 
  MapPin, 
  Home, 
  Layers,
  ChevronRight,
  Check
} from 'lucide-react';

import { cn } from '@/lib/utils';
import { 
  hapticFeedback, 
  mobileClasses, 
  isMobileViewport 
} from '@/lib/mobile';
import { MobileInput } from '@/components/ui/mobile-input';
import { QualityTierSelector } from '@/components/ui/swipeable-cards';
import { AnimatedButton } from '@/components/ui/animated-button';
import { StepIndicator } from '@/components/ui/status-indicators';
import { MobileResultsDisplay } from './MobileResultsDisplay';
import { RefreshableContainer } from '@/components/ui/pull-to-refresh';

interface MobileCalculatorLayoutProps {
  className?: string;
}

interface FormData {
  plotSize: string;
  floors: string;
  quality: string;
  location: string;
  buildingType: string;
}

interface CalculationResult {
  totalCost: number;
  costPerSqft: number;
  breakdown: {
    structure: number;
    finishing: number;
    mep: number;
    other: number;
  };
  builtUpArea: number;
  quality: string;
  location: string;
}

export function MobileCalculatorLayout({ className }: MobileCalculatorLayoutProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [formData, setFormData] = useState<FormData>({
    plotSize: '',
    floors: '1',
    quality: 'smart',
    location: 'delhi',
    buildingType: 'residential',
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [result, setResult] = useState<CalculationResult | null>(null);
  const [isCalculating, setIsCalculating] = useState(false);
  const [showResults, setShowResults] = useState(false);

  const isMobile = isMobileViewport();

  if (!isMobile) {
    return null; // Only render on mobile
  }

  const qualityTiers = [
    {
      id: 'smart',
      title: 'Smart Choice',
      price: '₹1,600-2,000/sqft',
      value: 'smart',
      features: [
        'M20 concrete grade',
        'Standard finishes',
        'Cera/Parryware fixtures',
        'Basic electrical fittings',
        'Standard tiles & paint'
      ],
    },
    {
      id: 'premium',
      title: 'Premium Selection',
      price: '₹2,200-2,800/sqft',
      value: 'premium',
      popular: true,
      features: [
        'M25 concrete grade',
        'Branded materials',
        'Kohler/Grohe fixtures',
        'Premium electrical fittings',
        'Designer tiles & textures'
      ],
    },
    {
      id: 'luxury',
      title: 'Luxury Collection',
      price: '₹3,000+/sqft',
      value: 'luxury',
      features: [
        'M30+ concrete grade',
        'International brands',
        'Imported fixtures',
        'Home automation ready',
        'Premium finishes & materials'
      ],
    },
  ];

  const locations = [
    { value: 'mumbai', label: 'Mumbai', multiplier: 1.2 },
    { value: 'delhi', label: 'Delhi NCR', multiplier: 1.05 },
    { value: 'bangalore', label: 'Bangalore', multiplier: 1.0 },
    { value: 'chennai', label: 'Chennai', multiplier: 0.9 },
    { value: 'hyderabad', label: 'Hyderabad', multiplier: 0.85 },
    { value: 'pune', label: 'Pune', multiplier: 0.95 },
  ];

  const steps = [
    {
      id: 'basic',
      title: 'Basic Details',
      description: 'Plot size and floors',
      status: (currentStep > 0 ? 'completed' : currentStep === 0 ? 'active' : 'pending') as 'active' | 'error' | 'pending' | 'completed',
    },
    {
      id: 'quality',
      title: 'Quality Tier',
      description: 'Select your preference',
      status: (currentStep > 1 ? 'completed' : currentStep === 1 ? 'active' : 'pending') as 'active' | 'error' | 'pending' | 'completed',
    },
    {
      id: 'location',
      title: 'Location',
      description: 'Select your city',
      status: (currentStep > 2 ? 'completed' : currentStep === 2 ? 'active' : 'pending') as 'active' | 'error' | 'pending' | 'completed',
    },
    {
      id: 'calculate',
      title: 'Calculate',
      description: 'Get your estimate',
      status: (currentStep > 3 ? 'completed' : currentStep === 3 ? 'active' : 'pending') as 'active' | 'error' | 'pending' | 'completed',
    },
  ];

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  const validateStep = (step: number): boolean => {
    const newErrors: Record<string, string> = {};

    switch (step) {
      case 0:
        if (!formData.plotSize || parseFloat(formData.plotSize) <= 0) {
          newErrors.plotSize = 'Please enter a valid plot size';
        }
        if (!formData.floors || parseInt(formData.floors) <= 0) {
          newErrors.floors = 'Please select number of floors';
        }
        break;
      case 1:
        if (!formData.quality) {
          newErrors.quality = 'Please select a quality tier';
        }
        break;
      case 2:
        if (!formData.location) {
          newErrors.location = 'Please select a location';
        }
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, steps.length - 1));
      hapticFeedback.light();
    }
  };

  const handleBack = () => {
    setCurrentStep(prev => Math.max(prev - 1, 0));
    hapticFeedback.light();
  };

  const handleCalculate = async () => {
    if (!validateStep(currentStep - 1)) return;

    setIsCalculating(true);
    hapticFeedback.medium();

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));

      const builtUpArea = Math.round(parseInt(formData.plotSize) * 0.6 * parseInt(formData.floors));
      const baseRate = formData.quality === 'smart' ? 1800 : 
                      formData.quality === 'premium' ? 2500 : 3500;
      const locationMultiplier = locations.find(l => l.value === formData.location)?.multiplier || 1;
      const totalCost = Math.round(builtUpArea * baseRate * locationMultiplier);

      const sampleResult: CalculationResult = {
        totalCost,
        costPerSqft: Math.round(baseRate * locationMultiplier),
        breakdown: {
          structure: Math.round(totalCost * 0.35),
          finishing: Math.round(totalCost * 0.30),
          mep: Math.round(totalCost * 0.20),
          other: Math.round(totalCost * 0.15),
        },
        builtUpArea,
        quality: formData.quality,
        location: formData.location,
      };

      setResult(sampleResult);
      setShowResults(true);
    } catch (error) {
      console.error('Calculation failed:', error);
    } finally {
      setIsCalculating(false);
    }
  };

  const handleRefresh = async () => {
    setCurrentStep(0);
    setFormData({
      plotSize: '',
      floors: '1',
      quality: 'smart',
      location: 'delhi',
      buildingType: 'residential',
    });
    setErrors({});
    setResult(null);
    setShowResults(false);
  };

  const pageVariants = {
    enter: (direction: number) => ({
      x: direction > 0 ? 300 : -300,
      opacity: 0,
    }),
    center: {
      zIndex: 1,
      x: 0,
      opacity: 1,
    },
    exit: (direction: number) => ({
      zIndex: 0,
      x: direction < 0 ? 300 : -300,
      opacity: 0,
    }),
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <Home className="h-12 w-12 text-blue-600 mx-auto mb-3" />
              <h2 className="text-2xl font-bold text-gray-800">Basic Details</h2>
              <p className="text-gray-600">Tell us about your project</p>
            </div>

            <MobileInput
              label="Plot Size (sq ft)"
              fieldType="number"
              placeholder="Enter plot size"
              value={formData.plotSize}
              onChange={e => handleInputChange('plotSize', e.target.value)}
              error={errors.plotSize}
              showClearButton={!!formData.plotSize}
              onClear={() => handleInputChange('plotSize', '')}
            />

            <div className="space-y-3">
              <label className="block text-sm font-medium text-gray-700">
                Number of Floors
              </label>
              <div className="grid grid-cols-4 gap-2">
                {[1, 2, 3, 4].map(floor => (
                  <button
                    key={floor}
                    onClick={() => handleInputChange('floors', floor.toString())}
                    className={cn(
                      'p-4 rounded-lg border-2 transition-all',
                      'flex items-center justify-center',
                      mobileClasses.touchTarget,
                      formData.floors === floor.toString()
                        ? 'border-blue-500 bg-blue-50 text-blue-700'
                        : 'border-gray-200 bg-white text-gray-700 hover:border-gray-300'
                    )}
                  >
                    <div className="text-center">
                      <div className="text-lg font-semibold">{floor}</div>
                      <div className="text-xs">Floor{floor > 1 ? 's' : ''}</div>
                    </div>
                  </button>
                ))}
              </div>
              {errors.floors && (
                <p className="text-sm text-red-600">{errors.floors}</p>
              )}
            </div>
          </div>
        );

      case 1:
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <Layers className="h-12 w-12 text-blue-600 mx-auto mb-3" />
              <h2 className="text-2xl font-bold text-gray-800">Quality Tier</h2>
              <p className="text-gray-600">Choose your preferred quality</p>
            </div>

            <QualityTierSelector
              tiers={qualityTiers}
              selectedTier={formData.quality}
              onTierSelect={value => handleInputChange('quality', value)}
            />

            {errors.quality && (
              <p className="text-sm text-red-600 text-center">{errors.quality}</p>
            )}
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <MapPin className="h-12 w-12 text-blue-600 mx-auto mb-3" />
              <h2 className="text-2xl font-bold text-gray-800">Location</h2>
              <p className="text-gray-600">Select your city</p>
            </div>

            <div className="grid grid-cols-1 gap-3">
              {locations.map(location => (
                <button
                  key={location.value}
                  onClick={() => handleInputChange('location', location.value)}
                  className={cn(
                    'p-4 rounded-lg border-2 transition-all',
                    'flex items-center justify-between',
                    mobileClasses.touchTarget,
                    formData.location === location.value
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 bg-white hover:border-gray-300'
                  )}
                >
                  <div className="flex items-center">
                    <div className="text-left">
                      <div className="font-medium text-gray-800">{location.label}</div>
                      <div className="text-sm text-gray-500">
                        {location.multiplier}x base rate
                      </div>
                    </div>
                  </div>
                  {formData.location === location.value && (
                    <Check className="h-5 w-5 text-blue-600" />
                  )}
                </button>
              ))}
            </div>

            {errors.location && (
              <p className="text-sm text-red-600 text-center">{errors.location}</p>
            )}
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <Calculator className="h-12 w-12 text-blue-600 mx-auto mb-3" />
              <h2 className="text-2xl font-bold text-gray-800">Review & Calculate</h2>
              <p className="text-gray-600">Confirm your details</p>
            </div>

            <div className="bg-gray-50 rounded-lg p-4 space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Plot Size</span>
                <span className="font-medium">{formData.plotSize} sq ft</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Floors</span>
                <span className="font-medium">{formData.floors}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Quality</span>
                <span className="font-medium capitalize">{formData.quality}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Location</span>
                <span className="font-medium">
                  {locations.find(l => l.value === formData.location)?.label}
                </span>
              </div>
              <div className="flex justify-between border-t pt-3">
                <span className="text-gray-600">Built-up Area</span>
                <span className="font-semibold">
                  {Math.round(parseInt(formData.plotSize || '0') * 0.6 * parseInt(formData.floors))} sq ft
                </span>
              </div>
            </div>

            <AnimatedButton
              onClick={handleCalculate}
              disabled={isCalculating}
              loading={isCalculating}
              loadingText="Calculating..."
              className="w-full py-4 text-lg"
              glow
            >
              Calculate Construction Cost
            </AnimatedButton>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <RefreshableContainer onRefresh={handleRefresh}>
      <div className={cn('min-h-screen bg-gray-50', className)}>
        {/* Header */}
        <div className="bg-white shadow-sm sticky top-0 z-10">
          <div className="px-4 py-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                {currentStep > 0 && (
                  <button
                    onClick={handleBack}
                    className={cn(
                      'mr-3 p-2 rounded-full hover:bg-gray-100',
                      mobileClasses.touchTarget
                    )}
                  >
                    <ArrowLeft className="h-5 w-5 text-gray-600" />
                  </button>
                )}
                <h1 className="text-lg font-semibold text-gray-800">
                  Construction Calculator
                </h1>
              </div>
              <div className="text-sm text-gray-500">
                {currentStep + 1} of {steps.length}
              </div>
            </div>

            {/* Progress Steps */}
            <div className="mt-4">
              <StepIndicator steps={steps} orientation="horizontal" />
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="px-4 py-6">
          <AnimatePresence mode="wait" custom={currentStep}>
            <motion.div
              key={currentStep}
              custom={currentStep}
              variants={pageVariants}
              initial="enter"
              animate="center"
              exit="exit"
              transition={{
                x: { type: 'spring', stiffness: 300, damping: 30 },
                opacity: { duration: 0.2 },
              }}
              className="bg-white rounded-lg shadow-sm p-6"
            >
              {renderStepContent()}
            </motion.div>
          </AnimatePresence>
        </div>

        {/* Navigation */}
        {currentStep < 3 && (
          <div className="fixed bottom-0 left-0 right-0 bg-white border-t p-4">
            <div className="flex gap-3">
              {currentStep > 0 && (
                <button
                  onClick={handleBack}
                  className={cn(
                    'flex-1 py-3 px-4 rounded-lg border border-gray-300',
                    'text-gray-700 font-medium',
                    'hover:bg-gray-50 transition-colors',
                    mobileClasses.touchTarget
                  )}
                >
                  Back
                </button>
              )}
              <button
                onClick={handleNext}
                className={cn(
                  'flex-1 py-3 px-4 rounded-lg',
                  'bg-blue-600 text-white font-medium',
                  'hover:bg-blue-700 transition-colors',
                  'disabled:opacity-50 disabled:cursor-not-allowed',
                  mobileClasses.touchTarget
                )}
                disabled={currentStep === 0 && !formData.plotSize}
              >
                Next
                <ChevronRight className="h-4 w-4 ml-2 inline" />
              </button>
            </div>
          </div>
        )}

        {/* Results */}
        {result && (
          <MobileResultsDisplay
            result={result}
            isOpen={showResults}
            onClose={() => setShowResults(false)}
          />
        )}
      </div>
    </RefreshableContainer>
  );
}