/**
 * Enhanced Calculator Demo with New Loading System
 * Example of integrating the comprehensive loading states and skeleton UI
 */

'use client';

import { useState, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  useLoading, 
  useCalculationLoading, 
  useOperationManager,
  LoadingType 
} from '@/contexts/LoadingContext';
import { 
  SkeletonCalculator, 
  SkeletonBreakdown,
  AccessibleLoadingState,
  IntelligentLoader,
  StaggerController,
  MobileLoadingButton,
  useMobileLoading
} from '@/components/ui';
import { isMobileViewport } from '@/lib/mobile';
import { motion } from 'framer-motion';
import { staggerContainer, staggerItem } from '@/lib/animations';

interface CalculationInputs {
  plotSize: string;
  floors: string;
  quality: string;
  location: string;
}

interface CalculationResult {
  totalCost: number;
  costPerSqft: number;
  breakdown: {
    structure: number;
    finishing: number;
    mep: number;
    other: number;
  };
  builtUpArea: number;
}

export function EnhancedCalculatorDemo() {
  const [inputs, setInputs] = useState<CalculationInputs>({
    plotSize: '',
    floors: '1',
    quality: 'smart',
    location: 'bangalore'
  });
  
  const [result, setResult] = useState<CalculationResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Use the new loading context
  const { state: loadingState } = useLoading();
  const { isLoading } = useCalculationLoading();
  const { withLoading } = useOperationManager();
  const { updateProgress } = useMobileLoading();

  const isMobile = isMobileViewport();

  // Enhanced calculation function with loading management
  const handleCalculate = useCallback(async () => {
    setError(null);
    
    try {
      const result = await withLoading(
        LoadingType.CALCULATION,
        async () => {
          // Simulate multi-step calculation with progress updates
          updateProgress(20, 'Validating inputs...');
          await new Promise(resolve => setTimeout(resolve, 300));
          
          updateProgress(40, 'Fetching material prices...');
          await new Promise(resolve => setTimeout(resolve, 500));
          
          updateProgress(70, 'Calculating costs...');
          await new Promise(resolve => setTimeout(resolve, 400));
          
          updateProgress(90, 'Generating breakdown...');
          await new Promise(resolve => setTimeout(resolve, 300));
          
          updateProgress(100, 'Complete!');
          
          const calculatedResult: CalculationResult = {
            totalCost: 2500000,
            costPerSqft: 2500,
            breakdown: {
              structure: 875000,
              finishing: 750000,
              mep: 500000,
              other: 375000
            },
            builtUpArea: 1000
          };

          return calculatedResult;
        },
        {
          message: 'Calculating construction costs...',
          priority: 'high'
        }
      );

      setResult(result);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Calculation failed';
      setError(errorMessage);
    }
  }, [inputs, withLoading, updateProgress]);

  const handleInputChange = (field: keyof CalculationInputs, value: string) => {
    setInputs(prev => ({ ...prev, [field]: value }));
    setResult(null);
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Global loading indicator */}
      {loadingState.isGlobalLoading && loadingState.highestPriorityOperation && (
        <motion.div 
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-blue-50 border border-blue-200 rounded-lg p-3"
        >
          <div className="flex items-center space-x-3">
            <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
            <span className="text-sm text-blue-700">
              {loadingState.highestPriorityOperation.message || 'Processing...'}
            </span>
          </div>
        </motion.div>
      )}

      {/* Calculator Form */}
      <AccessibleLoadingState
        isLoading={isLoading && !result}
        loadingContent={<SkeletonCalculator showProgress={true} />}
        loadingLabel="Loading calculator form"
      >
        <Card>
          <CardHeader>
            <CardTitle>Enhanced Construction Calculator</CardTitle>
          </CardHeader>
          <CardContent>
            <StaggerController 
              direction="up" 
              staggerDelay={0.1}
              className="space-y-4"
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Plot Size (sq ft)</label>
                  <input
                    type="number"
                    value={inputs.plotSize}
                    onChange={(e) => handleInputChange('plotSize', e.target.value)}
                    className="w-full px-3 py-2 border rounded-md"
                    placeholder="Enter plot size"
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Number of Floors</label>
                  <select
                    value={inputs.floors}
                    onChange={(e) => handleInputChange('floors', e.target.value)}
                    className="w-full px-3 py-2 border rounded-md"
                  >
                    <option value="1">1 Floor</option>
                    <option value="2">2 Floors</option>
                    <option value="3">3 Floors</option>
                  </select>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Quality Tier</label>
                  <select
                    value={inputs.quality}
                    onChange={(e) => handleInputChange('quality', e.target.value)}
                    className="w-full px-3 py-2 border rounded-md"
                  >
                    <option value="smart">Smart Choice</option>
                    <option value="premium">Premium Selection</option>
                    <option value="luxury">Luxury Collection</option>
                  </select>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Location</label>
                  <select
                    value={inputs.location}
                    onChange={(e) => handleInputChange('location', e.target.value)}
                    className="w-full px-3 py-2 border rounded-md"
                  >
                    <option value="bangalore">Bangalore</option>
                    <option value="mumbai">Mumbai</option>
                    <option value="delhi">Delhi</option>
                  </select>
                </div>
              </div>

              <div className="pt-4">
                {isMobile ? (
                  <MobileLoadingButton
                    onClick={handleCalculate}
                    isLoading={isLoading}
                    loadingText="Calculating..."
                    fullWidth
                    variant="primary"
                    disabled={!inputs.plotSize}
                  >
                    Calculate Cost
                  </MobileLoadingButton>
                ) : (
                  <Button
                    onClick={handleCalculate}
                    disabled={isLoading || !inputs.plotSize}
                    className="w-full"
                  >
                    {isLoading ? 'Calculating...' : 'Calculate Cost'}
                  </Button>
                )}
              </div>
            </StaggerController>
          </CardContent>
        </Card>
      </AccessibleLoadingState>

      {/* Results */}
      {(result || error || isLoading) && (
        <IntelligentLoader
          isLoading={isLoading && !result && !error}
          skeleton={<SkeletonBreakdown itemCount={6} />}
          content={
            result ? (
              <motion.div
                variants={staggerContainer}
                initial="initial"
                animate="animate"
                className="space-y-6"
              >
                <Card>
                  <CardContent className="p-6">
                    <div className="text-center space-y-4">
                      <h3 className="text-lg font-semibold">Total Construction Cost</h3>
                      <div className="text-3xl font-bold text-blue-600">
                        ₹{result.totalCost.toLocaleString('en-IN')}
                      </div>
                      <div className="text-sm text-gray-600">
                        ₹{result.costPerSqft}/sq ft • {result.builtUpArea} sq ft built-up area
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Cost Breakdown</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <StaggerController className="space-y-3">
                      {Object.entries(result.breakdown).map(([category, amount]) => (
                        <div key={category} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                          <span className="font-medium capitalize">{category}</span>
                          <span className="font-semibold">₹{amount.toLocaleString('en-IN')}</span>
                        </div>
                      ))}
                    </StaggerController>
                  </CardContent>
                </Card>
              </motion.div>
            ) : null
          }
          error={error}
          onRetry={handleCalculate}
          transition="fade"
          minimumLoadTime={1000}
        />
      )}
    </div>
  );
}