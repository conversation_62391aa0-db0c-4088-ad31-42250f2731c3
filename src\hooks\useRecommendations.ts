/**
 * Recommendations Hook
 * Manages AI-powered recommendations and cost optimizations
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import { CalculatorFormData } from '@/components/calculator/types/wizard';
import { 
  AIRecommendationEngine,
  Recommendation,
  CostOptimization,
  UserProfile
} from '@/lib/recommendations/ai-suggestions';

export interface UseRecommendationsOptions {
  enabled?: boolean;
  maxRecommendations?: number;
  priorityFilter?: Array<'high' | 'medium' | 'low'>;
  typeFilter?: Array<Recommendation['type']>;
  categoryFilter?: Array<Recommendation['category']>;
  autoRefresh?: boolean; // Auto-refresh when form data changes
}

export interface UseRecommendationsReturn {
  recommendations: Recommendation[];
  costOptimization: CostOptimization | null;
  peopleAlsoChose: Array<{
    feature: string;
    percentage: number;
    description: string;
  }>;
  applyRecommendation: (recommendationId: string) => void;
  dismissRecommendation: (recommendationId: string) => void;
  refreshRecommendations: () => void;
  setUserProfile: (profile: UserProfile) => void;
  isLoading: boolean;
  stats: {
    total: number;
    byPriority: Record<'high' | 'medium' | 'low', number>;
    byCategory: Record<string, number>;
    potentialSavings: number;
  };
}

export function useRecommendations(
  formData: Partial<CalculatorFormData>,
  updateFormData: (updates: Partial<CalculatorFormData>) => void,
  options: UseRecommendationsOptions = {}
): UseRecommendationsReturn {
  const {
    enabled = true,
    maxRecommendations = 10,
    priorityFilter,
    typeFilter,
    categoryFilter,
    autoRefresh = true
  } = options;

  const [engine] = useState(() => new AIRecommendationEngine());
  const [dismissedIds, setDismissedIds] = useState<Set<string>>(new Set());
  const [isLoading, setIsLoading] = useState(false);
  const [lastRefresh, setLastRefresh] = useState(Date.now());

  // Generate recommendations
  const rawRecommendations = useMemo(() => {
    if (!enabled) return [];
    
    setIsLoading(true);
    const recommendations = engine.generateRecommendations(formData);
    setIsLoading(false);
    
    return recommendations;
  }, [formData, enabled, engine, lastRefresh]);

  // Filter recommendations
  const recommendations = useMemo(() => {
    let filtered = rawRecommendations.filter(rec => !dismissedIds.has(rec.id));

    if (priorityFilter) {
      filtered = filtered.filter(rec => priorityFilter.includes(rec.priority));
    }

    if (typeFilter) {
      filtered = filtered.filter(rec => typeFilter.includes(rec.type));
    }

    if (categoryFilter) {
      filtered = filtered.filter(rec => categoryFilter.includes(rec.category));
    }

    return filtered.slice(0, maxRecommendations);
  }, [rawRecommendations, dismissedIds, priorityFilter, typeFilter, categoryFilter, maxRecommendations]);

  // Cost optimization analysis
  const costOptimization = useMemo(() => {
    if (!enabled) return null;
    return engine.generateCostOptimization(formData);
  }, [formData, enabled, engine]);

  // People also chose suggestions
  const peopleAlsoChose = useMemo(() => {
    if (!enabled) return [];
    return engine.getPeopleAlsoChose(formData);
  }, [formData, enabled, engine]);

  // Apply recommendation
  const applyRecommendation = useCallback((recommendationId: string) => {
    const recommendation = recommendations.find(r => r.id === recommendationId);
    if (!recommendation?.action) return;

    const { field, value } = recommendation.action;
    updateFormData({ [field]: value });
    
    // Mark as dismissed since it's applied
    setDismissedIds(prev => new Set([...prev, recommendationId]));
  }, [recommendations, updateFormData]);

  // Dismiss recommendation
  const dismissRecommendation = useCallback((recommendationId: string) => {
    setDismissedIds(prev => new Set([...prev, recommendationId]));
  }, []);

  // Refresh recommendations
  const refreshRecommendations = useCallback(() => {
    setLastRefresh(Date.now());
    setDismissedIds(new Set()); // Clear dismissed when manually refreshing
  }, []);

  // Set user profile
  const setUserProfile = useCallback((profile: UserProfile) => {
    engine.setUserProfile(profile);
    setLastRefresh(Date.now()); // Trigger refresh
  }, [engine]);

  // Auto-refresh when form data changes significantly
  useEffect(() => {
    if (!autoRefresh) return;

    const timer = setTimeout(() => {
      setLastRefresh(Date.now());
    }, 500); // Debounce rapid changes

    return () => clearTimeout(timer);
  }, [formData.quality, formData.bedrooms, formData.location, autoRefresh]);

  // Calculate stats
  const stats = useMemo(() => {
    const total = rawRecommendations.length;
    
    const byPriority = rawRecommendations.reduce((acc, rec) => {
      acc[rec.priority] = (acc[rec.priority] || 0) + 1;
      return acc;
    }, {} as Record<'high' | 'medium' | 'low', number>);

    const byCategory = rawRecommendations.reduce((acc, rec) => {
      acc[rec.category] = (acc[rec.category] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const potentialSavings = rawRecommendations
      .filter(rec => rec.savings)
      .reduce((total, rec) => total + (rec.savings || 0), 0);

    return {
      total,
      byPriority: {
        high: byPriority.high || 0,
        medium: byPriority.medium || 0,
        low: byPriority.low || 0
      },
      byCategory,
      potentialSavings
    };
  }, [rawRecommendations]);

  return {
    recommendations,
    costOptimization,
    peopleAlsoChose,
    applyRecommendation,
    dismissRecommendation,
    refreshRecommendations,
    setUserProfile,
    isLoading,
    stats
  };
}

// Hook for specific recommendation categories
export function useCategoryRecommendations(
  formData: Partial<CalculatorFormData>,
  category: Recommendation['category'],
  options: Omit<UseRecommendationsOptions, 'categoryFilter'> = {}
) {
  return useRecommendations(formData, () => {}, {
    ...options,
    categoryFilter: [category]
  });
}

// Hook for cost optimization only
export function useCostOptimization(
  formData: Partial<CalculatorFormData>
): {
  optimization: CostOptimization | null;
  isLoading: boolean;
  potentialSavings: number;
  alternativeCount: number;
} {
  const [engine] = useState(() => new AIRecommendationEngine());
  const [isLoading, setIsLoading] = useState(false);

  const optimization = useMemo(() => {
    if (!formData.builtUpArea || !formData.quality) return null;
    
    setIsLoading(true);
    const result = engine.generateCostOptimization(formData);
    setIsLoading(false);
    
    return result;
  }, [formData, engine]);

  const potentialSavings = useMemo(() => {
    if (!optimization) return 0;
    return Math.max(0, optimization.currentCost - optimization.optimizedCost);
  }, [optimization]);

  const alternativeCount = optimization?.alternativeConfigurations.length || 0;

  return {
    optimization,
    isLoading,
    potentialSavings,
    alternativeCount
  };
}

// Hook for market insights
export function useMarketInsights(
  formData: Partial<CalculatorFormData>
): {
  insights: Recommendation[];
  pricetrends: Array<{
    material: string;
    trend: 'rising' | 'falling' | 'stable';
    change: number;
  }>;
  timing: {
    optimal: string;
    avoid: string;
    reason: string;
  };
} {
  const { recommendations } = useRecommendations(formData, () => {}, {
    typeFilter: ['tip'],
    categoryFilter: ['budget']
  });

  // Mock price trends data
  const pricetrends = useMemo(() => [
    { material: 'Cement', trend: 'rising' as const, change: 8.5 },
    { material: 'Steel (TMT)', trend: 'stable' as const, change: 2.1 },
    { material: 'Bricks', trend: 'rising' as const, change: 5.3 },
    { material: 'Sand', trend: 'rising' as const, change: 12.7 },
  ], []);

  // Construction timing recommendation
  const timing = useMemo(() => {
    const currentMonth = new Date().getMonth();
    const isMonsoon = currentMonth >= 5 && currentMonth <= 9;
    
    if (isMonsoon) {
      return {
        optimal: 'October-November',
        avoid: 'June-September',
        reason: 'Avoid monsoon for foundation work'
      };
    }

    return {
      optimal: 'October-March',
      avoid: 'April-June',
      reason: 'Better weather and material availability'
    };
  }, []);

  return {
    insights: recommendations,
    pricetrends,
    timing
  };
}

// Hook for progressive enhancement based on user expertise
export function useProgressiveEnhancement(
  formData: Partial<CalculatorFormData>
): {
  userLevel: 'beginner' | 'intermediate' | 'expert';
  shouldShowAdvanced: boolean;
  complexityScore: number;
  suggestions: Array<{
    feature: string;
    reason: string;
    difficulty: 'easy' | 'medium' | 'hard';
  }>;
} {
  const userLevel = useMemo(() => {
    let score = 0;
    
    // Simple scoring based on choices
    if (formData.quality === 'luxury') score += 2;
    if (formData.quality === 'premium') score += 1;
    if (formData.homeAutomation) score += 1;
    if (formData.swimmingPool) score += 2;
    if (formData.solarPanels) score += 1;
    if (parseInt(formData.floors || '1') > 2) score += 1;
    
    if (score >= 4) return 'expert';
    if (score >= 2) return 'intermediate';
    return 'beginner';
  }, [formData]);

  const complexityScore = useMemo(() => {
    let complexity = 0;
    
    const area = parseFloat(formData.builtUpArea || '0');
    const floors = parseInt(formData.floors || '1');
    const bedrooms = parseInt(formData.bedrooms || '1');
    
    // Base complexity
    complexity += Math.min(area / 1000, 3); // 0-3 points for area
    complexity += floors - 1; // Additional floors add complexity
    complexity += Math.min(bedrooms - 1, 3); // More rooms = more complexity
    
    // Feature complexity
    if (formData.swimmingPool) complexity += 2;
    if (formData.elevator) complexity += 2;
    if (formData.homeAutomation) complexity += 1;
    if (formData.solarPanels) complexity += 1;
    
    return Math.min(complexity, 10) / 10; // Normalize to 0-1
  }, [formData]);

  const shouldShowAdvanced = userLevel !== 'beginner' || complexityScore > 0.5;

  const suggestions = useMemo(() => {
    const baseSuggestions = [
      { feature: 'Quality Selection', reason: 'Choose appropriate finishes', difficulty: 'easy' as const },
      { feature: 'Room Layout', reason: 'Optimize space utilization', difficulty: 'medium' as const },
      { feature: 'Feature Selection', reason: 'Add value-adding features', difficulty: 'medium' as const },
    ];

    if (userLevel === 'expert' || complexityScore > 0.7) {
      baseSuggestions.push(
        { feature: 'Custom Materials', reason: 'Specify exact materials', difficulty: 'hard' as const },
        { feature: 'Structural Options', reason: 'Advanced structural choices', difficulty: 'hard' as const }
      );
    }

    return baseSuggestions;
  }, [userLevel, complexityScore]);

  return {
    userLevel,
    shouldShowAdvanced,
    complexityScore,
    suggestions
  };
}