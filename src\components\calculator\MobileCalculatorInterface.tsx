"use client";

import React, { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { MobileOptimizedInput } from '@/components/ui/mobile-optimized-input';
import { TouchButton } from '@/components/ui/touch-button';
import { GestureCard } from '@/components/ui/gesture-card';
import hapticFeedback from '@/lib/mobile/haptic-feedback';
import { SwipeGesture } from '@/lib/mobile/touch-handler';

interface CalculationInputs {
  plotSize: string;
  location: string;
  qualityTier: string;
  floors: string;
}

interface CalculationResult {
  totalCost: number;
  costPerSqft: number;
  breakdown: {
    structure: number;
    finishing: number;
    mep: number;
    external: number;
    other: number;
  };
}

interface MobileCalculatorInterfaceProps {
  onCalculate?: (inputs: CalculationInputs) => Promise<CalculationResult>;
  className?: string;
}

const qualityTiers = [
  { id: 'smart', name: 'Smart Choice', price: '₹1,800/sqft', description: 'Standard quality with essential features' },
  { id: 'premium', name: 'Premium Selection', price: '₹2,500/sqft', description: 'Enhanced quality with branded materials' },
  { id: 'luxury', name: 'Luxury Collection', price: '₹3,500/sqft', description: 'Premium quality with luxury finishes' }
];

const locations = [
  'Mumbai', 'Delhi', 'Bangalore', 'Chennai', 'Hyderabad', 
  'Pune', 'Kolkata', 'Ahmedabad', 'Jaipur', 'Lucknow'
];

const MobileCalculatorInterface: React.FC<MobileCalculatorInterfaceProps> = ({
  onCalculate,
  className
}) => {
  const [inputs, setInputs] = useState<CalculationInputs>({
    plotSize: '',
    location: 'Bangalore',
    qualityTier: 'premium',
    floors: '1'
  });
  
  const [result, setResult] = useState<CalculationResult | null>(null);
  const [isCalculating, setIsCalculating] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [errors, setErrors] = useState<Partial<CalculationInputs>>({});
  const [showResults, setShowResults] = useState(false);

  const steps = [
    { id: 'plotSize', title: 'Plot Size', subtitle: 'Enter your plot area' },
    { id: 'location', title: 'Location', subtitle: 'Select your city' },
    { id: 'qualityTier', title: 'Quality', subtitle: 'Choose quality tier' },
    { id: 'floors', title: 'Floors', subtitle: 'Number of floors' },
    { id: 'review', title: 'Review', subtitle: 'Confirm details' }
  ];

  useEffect(() => {
    // Auto-calculate when all inputs are valid
    if (inputs.plotSize && inputs.location && inputs.qualityTier && inputs.floors) {
      const isValid = validateInputs();
      if (isValid && onCalculate) {
        handleCalculate();
      }
    }
  }, [inputs]);

  const validateInputs = (): boolean => {
    const newErrors: Partial<CalculationInputs> = {};
    
    if (!inputs.plotSize || parseFloat(inputs.plotSize) <= 0) {
      newErrors.plotSize = 'Plot size must be greater than 0';
    }
    
    if (parseFloat(inputs.plotSize) > 10000) {
      newErrors.plotSize = 'Plot size seems too large';
    }
    
    if (!inputs.location) {
      newErrors.location = 'Please select a location';
    }
    
    if (!inputs.qualityTier) {
      newErrors.qualityTier = 'Please select a quality tier';
    }
    
    if (!inputs.floors || parseInt(inputs.floors) <= 0) {
      newErrors.floors = 'Number of floors must be greater than 0';
    }
    
    if (parseInt(inputs.floors) > 5) {
      newErrors.floors = 'Maximum 5 floors supported';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleCalculate = async () => {
    if (!validateInputs()) {
      hapticFeedback.error();
      return;
    }
    
    setIsCalculating(true);
    hapticFeedback.calculation();
    
    try {
      if (onCalculate) {
        const calculationResult = await onCalculate(inputs);
        setResult(calculationResult);
        setShowResults(true);
        hapticFeedback.success();
      } else {
        // Mock calculation for demo
        await new Promise(resolve => setTimeout(resolve, 2000));
        const mockResult: CalculationResult = {
          totalCost: parseFloat(inputs.plotSize) * 2500 * parseInt(inputs.floors),
          costPerSqft: 2500,
          breakdown: {
            structure: 35,
            finishing: 30,
            mep: 20,
            external: 10,
            other: 5
          }
        };
        setResult(mockResult);
        setShowResults(true);
        hapticFeedback.success();
      }
    } catch (error) {
      hapticFeedback.error();
      console.error('Calculation failed:', error);
    } finally {
      setIsCalculating(false);
    }
  };

  const handleInputChange = (field: keyof CalculationInputs, value: string) => {
    setInputs(prev => ({ ...prev, [field]: value }));
    setErrors(prev => ({ ...prev, [field]: undefined }));
    hapticFeedback.tap();
  };

  const handleStepChange = (direction: 'next' | 'prev') => {
    const newStep = direction === 'next' ? 
      Math.min(currentStep + 1, steps.length - 1) : 
      Math.max(currentStep - 1, 0);
    
    setCurrentStep(newStep);
    hapticFeedback.navigation();
  };

  const handleSwipeNavigation = (gesture: SwipeGesture) => {
    if (gesture.direction === 'left' && currentStep < steps.length - 1) {
      handleStepChange('next');
    } else if (gesture.direction === 'right' && currentStep > 0) {
      handleStepChange('prev');
    }
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(amount);
  };

  const renderStepContent = () => {
    const step = steps[currentStep];
    
    switch (step.id) {
      case 'plotSize':
        return (
          <div className="space-y-6">
            <MobileOptimizedInput
              id="plotSize"
              type="number"
              label="Plot Size (sq ft)"
              value={inputs.plotSize}
              onChange={(e) => handleInputChange('plotSize', e.target.value)}
              error={errors.plotSize}
              helpText="Enter the total area of your plot in square feet"
              placeholder="e.g., 1200"
              enableHaptic
              validationPattern={/^\d+(\.\d+)?$/}
              formatValue={(value) => value.replace(/[^\d.]/g, '')}
            />
          </div>
        );
      
      case 'location':
        return (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">Select Location</h3>
            <div className="grid grid-cols-2 gap-3">
              {locations.map((location) => (
                <TouchButton
                  key={location}
                  variant={inputs.location === location ? 'primary' : 'outline'}
                  size="md"
                  onClick={() => handleInputChange('location', location)}
                  className="text-sm"
                >
                  {location}
                </TouchButton>
              ))}
            </div>
          </div>
        );
      
      case 'qualityTier':
        return (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">Choose Quality Tier</h3>
            <div className="space-y-3">
              {qualityTiers.map((tier) => (
                <GestureCard
                  key={tier.id}
                  className={cn(
                    'p-4 cursor-pointer transition-all',
                    inputs.qualityTier === tier.id 
                      ? 'border-blue-500 bg-blue-50 shadow-md' 
                      : 'border-gray-200 hover:border-gray-300'
                  )}
                  onTap={() => handleInputChange('qualityTier', tier.id)}
                  hapticEnabled
                >
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <h4 className="font-semibold text-gray-900">{tier.name}</h4>
                      <p className="text-sm text-gray-600 mt-1">{tier.description}</p>
                    </div>
                    <div className="text-right">
                      <p className="font-bold text-blue-600">{tier.price}</p>
                      {inputs.qualityTier === tier.id && (
                        <div className="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center mt-2">
                          <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        </div>
                      )}
                    </div>
                  </div>
                </GestureCard>
              ))}
            </div>
          </div>
        );
      
      case 'floors':
        return (
          <div className="space-y-6">
            <MobileOptimizedInput
              id="floors"
              type="number"
              label="Number of Floors"
              value={inputs.floors}
              onChange={(e) => handleInputChange('floors', e.target.value)}
              error={errors.floors}
              helpText="Enter the number of floors for your construction"
              placeholder="e.g., 2"
              enableHaptic
              validationPattern={/^[1-5]$/}
              formatValue={(value) => value.replace(/[^1-5]/g, '')}
            />
            
            <div className="grid grid-cols-3 gap-3">
              {[1, 2, 3].map((floor) => (
                <TouchButton
                  key={floor}
                  variant={inputs.floors === floor.toString() ? 'primary' : 'outline'}
                  size="lg"
                  onClick={() => handleInputChange('floors', floor.toString())}
                >
                  {floor}
                </TouchButton>
              ))}
            </div>
          </div>
        );
      
      case 'review':
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900">Review Details</h3>
            
            <div className="space-y-4">
              <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                <span className="text-gray-600">Plot Size:</span>
                <span className="font-semibold">{inputs.plotSize} sq ft</span>
              </div>
              
              <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                <span className="text-gray-600">Location:</span>
                <span className="font-semibold">{inputs.location}</span>
              </div>
              
              <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                <span className="text-gray-600">Quality Tier:</span>
                <span className="font-semibold">
                  {qualityTiers.find(t => t.id === inputs.qualityTier)?.name}
                </span>
              </div>
              
              <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                <span className="text-gray-600">Floors:</span>
                <span className="font-semibold">{inputs.floors}</span>
              </div>
            </div>
            
            <TouchButton
              variant="primary"
              size="lg"
              onClick={handleCalculate}
              loading={isCalculating}
              className="w-full"
              hapticPattern="calculation"
            >
              {isCalculating ? 'Calculating...' : 'Calculate Cost'}
            </TouchButton>
          </div>
        );
      
      default:
        return null;
    }
  };

  const renderResults = () => {
    if (!result) return null;
    
    return (
      <div className="space-y-6">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900">Construction Cost</h2>
          <p className="text-4xl font-bold text-blue-600 mt-2">
            {formatCurrency(result.totalCost)}
          </p>
          <p className="text-gray-600 mt-1">
            {formatCurrency(result.costPerSqft)} per sq ft
          </p>
        </div>
        
        <div className="space-y-3">
          <h3 className="font-semibold text-gray-900">Cost Breakdown</h3>
          
          {Object.entries(result.breakdown).map(([category, percentage]) => {
            const amount = (result.totalCost * percentage) / 100;
            return (
              <div key={category} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                <span className="capitalize text-gray-600">{category}:</span>
                <div className="text-right">
                  <span className="font-semibold">{formatCurrency(amount)}</span>
                  <span className="text-sm text-gray-500 ml-2">({percentage}%)</span>
                </div>
              </div>
            );
          })}
        </div>
        
        <div className="flex gap-3">
          <TouchButton
            variant="outline"
            size="md"
            onClick={() => setShowResults(false)}
            className="flex-1"
          >
            Edit Details
          </TouchButton>
          
          <TouchButton
            variant="primary"
            size="md"
            className="flex-1"
            rightIcon={
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            }
          >
            Download PDF
          </TouchButton>
        </div>
      </div>
    );
  };

  if (showResults) {
    return (
      <div className={cn('p-6', className)}>
        <GestureCard className="p-6">
          {renderResults()}
        </GestureCard>
      </div>
    );
  }

  return (
    <div className={cn('p-6', className)}>
      {/* Progress indicator */}
      <div className="mb-6">
        <div className="flex justify-between items-center mb-2">
          <h1 className="text-xl font-bold text-gray-900">{steps[currentStep].title}</h1>
          <span className="text-sm text-gray-500">{currentStep + 1}/{steps.length}</span>
        </div>
        
        <p className="text-gray-600 mb-4">{steps[currentStep].subtitle}</p>
        
        <div className="flex space-x-2">
          {steps.map((_, index) => (
            <div
              key={index}
              className={cn(
                'h-2 flex-1 rounded-full transition-colors',
                index <= currentStep ? 'bg-blue-500' : 'bg-gray-200'
              )}
            />
          ))}
        </div>
      </div>
      
      {/* Step content with swipe navigation */}
      <GestureCard
        className="p-6 min-h-[400px]"
        swipeEnabled
        onSwipeLeft={handleSwipeNavigation}
        onSwipeRight={handleSwipeNavigation}
        hapticEnabled
      >
        {renderStepContent()}
      </GestureCard>
      
      {/* Navigation buttons */}
      <div className="flex justify-between items-center mt-6">
        <TouchButton
          variant="outline"
          size="md"
          onClick={() => handleStepChange('prev')}
          disabled={currentStep === 0}
          leftIcon={
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          }
        >
          Previous
        </TouchButton>
        
        {currentStep < steps.length - 1 && (
          <TouchButton
            variant="primary"
            size="md"
            onClick={() => handleStepChange('next')}
            rightIcon={
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            }
          >
            Next
          </TouchButton>
        )}
      </div>
      
      {/* Swipe hint */}
      <div className="text-center mt-4">
        <p className="text-xs text-gray-400">
          Swipe left/right to navigate or use buttons
        </p>
      </div>
    </div>
  );
};

export { MobileCalculatorInterface };
export type { MobileCalculatorInterfaceProps, CalculationInputs, CalculationResult };