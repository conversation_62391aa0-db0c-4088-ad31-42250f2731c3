# Error Handling Guide - Nirmaan AI Calculator API

## Overview

This guide provides comprehensive information about error handling in the Nirmaan AI Construction Calculator API, including error codes, response formats, and best practices for client applications.

## Error Response Format

All API errors follow a consistent structure:

```json
{
  "success": false,
  "error": {
    "type": "error_category",
    "message": "Human-readable error message",
    "code": "SPECIFIC_ERROR_CODE",
    "details": "Additional error context (optional)"
  },
  "timestamp": "2025-01-15T10:30:00.000Z",
  "requestId": "unique_request_identifier"
}
```

## Error Categories

### 1. Validation Errors (`validation`)
Errors related to input validation and data format issues.

### 2. Calculation Errors (`calculation`)
Errors in the calculation engine or mathematical operations.

### 3. Rate Limiting Errors (`rate_limit`)
Errors when API rate limits are exceeded.

### 4. Authentication Errors (`authentication`)
Errors related to authentication and authorization.

### 5. Server Errors (`server`)
Internal server errors and system failures.

---

## Complete Error Reference

### Validation Errors (400 Bad Request)

#### INVALID_JSON
```json
{
  "success": false,
  "error": {
    "type": "validation",
    "message": "Invalid JSON in request body",
    "code": "INVALID_JSON"
  },
  "timestamp": "2025-01-15T10:30:00.000Z",
  "requestId": "calc_1640995200_abc123"
}
```

**Cause**: Malformed JSON in request body  
**Solution**: Ensure request body contains valid JSON

**Example Fix**:
```javascript
// ❌ Invalid JSON
fetch('/api/calculate', {
  method: 'POST',
  body: '{builtUpArea: 1000}' // Missing quotes
});

// ✅ Valid JSON
fetch('/api/calculate', {
  method: 'POST',
  body: JSON.stringify({
    builtUpArea: 1000,
    floors: 1,
    qualityTier: 'smart',
    location: 'bangalore'
  })
});
```

#### MISSING_REQUIRED_FIELDS
```json
{
  "success": false,
  "error": {
    "type": "validation",
    "message": "Missing required fields: builtUpArea, qualityTier",
    "code": "MISSING_REQUIRED_FIELDS",
    "details": {
      "missingFields": ["builtUpArea", "qualityTier"],
      "requiredFields": ["builtUpArea", "qualityTier", "location"]
    }
  },
  "timestamp": "2025-01-15T10:30:00.000Z",
  "requestId": "calc_1640995200_abc123"
}
```

**Cause**: Required fields missing from request  
**Solution**: Include all required fields: `builtUpArea`, `qualityTier`, `location`

**Example Fix**:
```javascript
// ❌ Missing required fields
const requestData = {
  floors: 1
};

// ✅ All required fields included
const requestData = {
  builtUpArea: 1200,
  qualityTier: 'premium',
  location: 'bangalore',
  floors: 1
};
```

#### INVALID_DATA_TYPES
```json
{
  "success": false,
  "error": {
    "type": "validation",
    "message": "Invalid input data types",
    "code": "INVALID_DATA_TYPES",
    "details": "builtUpArea must be a number, received string"
  },
  "timestamp": "2025-01-15T10:30:00.000Z",
  "requestId": "calc_1640995200_abc123"
}
```

**Cause**: Incorrect data types in request  
**Solution**: Ensure proper data types for all fields

**Field Type Reference**:
- `builtUpArea`: number
- `floors`: number
- `qualityTier`: string ("smart" | "premium" | "luxury")
- `location`: string
- `plotArea`: number (optional)
- `hasStilt`: boolean (optional)
- `parkingType`: string ("open" | "covered" | "none") (optional)
- `hasBasement`: boolean (optional)
- `specialFeatures`: array of objects (optional)

#### VALIDATION_FAILED
```json
{
  "success": false,
  "error": {
    "type": "validation",
    "message": "Input validation failed",
    "code": "VALIDATION_FAILED",
    "details": {
      "errors": [
        {
          "field": "builtUpArea",
          "message": "Built-up area must be between 100 and 50000 sqft",
          "code": "INVALID_RANGE"
        },
        {
          "field": "floors",
          "message": "Floors must be between 0 and 10",
          "code": "INVALID_RANGE"
        }
      ],
      "errorCount": 2
    }
  },
  "timestamp": "2025-01-15T10:30:00.000Z",
  "requestId": "calc_1640995200_abc123"
}
```

**Cause**: Input values outside acceptable ranges  
**Solution**: Ensure values are within valid ranges

**Validation Rules**:
- `builtUpArea`: 100 - 50,000 sqft
- `floors`: 0 - 10
- `plotArea`: 100 - 100,000 sqft (if provided)
- `qualityTier`: must be "smart", "premium", or "luxury"
- `location`: must be supported city
- `parkingType`: must be "open", "covered", or "none"

### Calculation Errors (422 Unprocessable Entity)

#### CALCULATION_ERROR
```json
{
  "success": false,
  "error": {
    "type": "calculation",
    "message": "Calculation engine error",
    "code": "CALCULATION_ERROR",
    "details": "Division by zero in cost calculation"
  },
  "timestamp": "2025-01-15T10:30:00.000Z",
  "requestId": "calc_1640995200_abc123"
}
```

**Cause**: Error in calculation engine  
**Solution**: Check input values for logical consistency

**Common Causes**:
- Zero or negative built-up area
- Invalid location not in database
- Corrupted material pricing data
- Mathematical overflow in calculations

### Rate Limiting Errors (429 Too Many Requests)

#### RATE_LIMIT_EXCEEDED
```json
{
  "success": false,
  "error": {
    "type": "rate_limit",
    "message": "Too many requests. Please try again later.",
    "code": "RATE_LIMIT_EXCEEDED",
    "retryAfter": 45
  },
  "timestamp": "2025-01-15T10:30:00.000Z",
  "requestId": "calc_1640995200_abc123"
}
```

**Headers**:
```
Retry-After: 45
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 0
X-RateLimit-Reset: 1640995200
```

**Cause**: Rate limit exceeded (100 requests per minute)  
**Solution**: Implement exponential backoff and retry logic

**Example Retry Logic**:
```javascript
async function calculateWithRetry(data, maxRetries = 3) {
  for (let attempt = 0; attempt < maxRetries; attempt++) {
    try {
      const response = await fetch('/api/calculate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      });

      if (response.status === 429) {
        const retryAfter = response.headers.get('Retry-After');
        const delay = retryAfter ? parseInt(retryAfter) * 1000 : Math.pow(2, attempt) * 1000;
        
        console.log(`Rate limited. Retrying in ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
        continue;
      }

      return await response.json();
    } catch (error) {
      if (attempt === maxRetries - 1) throw error;
      await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
    }
  }
}
```

### Authentication Errors (401 Unauthorized)

#### AUTHENTICATION_REQUIRED
```json
{
  "success": false,
  "error": {
    "type": "authentication",
    "message": "Authentication required",
    "code": "AUTHENTICATION_REQUIRED"
  },
  "timestamp": "2025-01-15T10:30:00.000Z",
  "requestId": "calc_1640995200_abc123"
}
```

**Cause**: Missing or invalid JWT token  
**Solution**: Include valid JWT token in Authorization header

**Example**:
```javascript
fetch('/api/projects', {
  headers: {
    'Authorization': `Bearer ${localStorage.getItem('jwt_token')}`,
    'Content-Type': 'application/json'
  }
});
```

### Server Errors (500 Internal Server Error)

#### INTERNAL_SERVER_ERROR
```json
{
  "success": false,
  "error": {
    "type": "server",
    "message": "Internal server error",
    "code": "INTERNAL_SERVER_ERROR"
  },
  "timestamp": "2025-01-15T10:30:00.000Z",
  "requestId": "calc_1640995200_abc123"
}
```

**Cause**: Unexpected server error  
**Solution**: Retry request or contact support with requestId

---

## Error Handling Best Practices

### 1. Comprehensive Error Handling

```javascript
class APIClient {
  async calculate(data) {
    try {
      const response = await fetch('/api/calculate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      });

      const result = await response.json();

      if (!response.ok) {
        throw new APIError(result.error, response.status, result.requestId);
      }

      return result.data;
    } catch (error) {
      if (error instanceof APIError) {
        throw error;
      }
      throw new APIError({
        type: 'network',
        message: 'Network error occurred',
        code: 'NETWORK_ERROR'
      }, 0, null);
    }
  }
}

class APIError extends Error {
  constructor(error, statusCode, requestId) {
    super(error.message);
    this.name = 'APIError';
    this.type = error.type;
    this.code = error.code;
    this.statusCode = statusCode;
    this.requestId = requestId;
    this.details = error.details;
  }
}
```

### 2. Error-Specific Handling

```javascript
async function handleAPIError(error) {
  switch (error.type) {
    case 'validation':
      if (error.code === 'MISSING_REQUIRED_FIELDS') {
        showValidationErrors(error.details.missingFields);
      } else if (error.code === 'VALIDATION_FAILED') {
        showFieldErrors(error.details.errors);
      }
      break;

    case 'rate_limit':
      showRateLimitMessage(error.details?.retryAfter);
      break;

    case 'calculation':
      showCalculationError(error.message);
      break;

    case 'authentication':
      redirectToLogin();
      break;

    case 'server':
      showServerError(error.requestId);
      break;

    default:
      showGenericError(error.message);
  }
}
```

### 3. Retry Logic with Exponential Backoff

```javascript
class RetryableClient {
  constructor(maxRetries = 3) {
    this.maxRetries = maxRetries;
  }

  async makeRequest(url, options) {
    for (let attempt = 0; attempt < this.maxRetries; attempt++) {
      try {
        const response = await fetch(url, options);
        
        if (response.status === 429) {
          const retryAfter = response.headers.get('Retry-After');
          const delay = retryAfter ? parseInt(retryAfter) * 1000 : Math.pow(2, attempt) * 1000;
          
          if (attempt < this.maxRetries - 1) {
            await this.delay(delay);
            continue;
          }
        }

        if (response.status >= 500 && response.status < 600) {
          if (attempt < this.maxRetries - 1) {
            await this.delay(Math.pow(2, attempt) * 1000);
            continue;
          }
        }

        return response;
      } catch (error) {
        if (attempt === this.maxRetries - 1) throw error;
        await this.delay(Math.pow(2, attempt) * 1000);
      }
    }
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
```

### 4. Input Validation on Client Side

```javascript
function validateCalculationInput(data) {
  const errors = [];

  // Required fields
  if (!data.builtUpArea || data.builtUpArea <= 0) {
    errors.push({ field: 'builtUpArea', message: 'Built-up area is required and must be greater than 0' });
  }

  if (!data.qualityTier || !['smart', 'premium', 'luxury'].includes(data.qualityTier)) {
    errors.push({ field: 'qualityTier', message: 'Quality tier must be smart, premium, or luxury' });
  }

  if (!data.location || typeof data.location !== 'string') {
    errors.push({ field: 'location', message: 'Location is required' });
  }

  // Range validation
  if (data.builtUpArea && (data.builtUpArea < 100 || data.builtUpArea > 50000)) {
    errors.push({ field: 'builtUpArea', message: 'Built-up area must be between 100 and 50,000 sqft' });
  }

  if (data.floors !== undefined && (data.floors < 0 || data.floors > 10)) {
    errors.push({ field: 'floors', message: 'Floors must be between 0 and 10' });
  }

  if (data.plotArea && (data.plotArea < 100 || data.plotArea > 100000)) {
    errors.push({ field: 'plotArea', message: 'Plot area must be between 100 and 100,000 sqft' });
  }

  return errors;
}
```

### 5. User-Friendly Error Messages

```javascript
const ERROR_MESSAGES = {
  'MISSING_REQUIRED_FIELDS': 'Please fill in all required fields',
  'INVALID_DATA_TYPES': 'Please check your input values',
  'VALIDATION_FAILED': 'Please correct the highlighted errors',
  'CALCULATION_ERROR': 'Unable to calculate cost. Please check your inputs',
  'RATE_LIMIT_EXCEEDED': 'Too many requests. Please wait a moment and try again',
  'AUTHENTICATION_REQUIRED': 'Please log in to continue',
  'INTERNAL_SERVER_ERROR': 'Something went wrong. Please try again later'
};

function getUserFriendlyMessage(errorCode) {
  return ERROR_MESSAGES[errorCode] || 'An unexpected error occurred';
}
```

---

## Error Logging and Monitoring

### Client-Side Error Logging

```javascript
class ErrorLogger {
  static log(error, context = {}) {
    const errorLog = {
      timestamp: new Date().toISOString(),
      error: {
        type: error.type,
        code: error.code,
        message: error.message,
        requestId: error.requestId
      },
      context,
      userAgent: navigator.userAgent,
      url: window.location.href
    };

    // Send to monitoring service
    if (process.env.NODE_ENV === 'production') {
      this.sendToMonitoring(errorLog);
    } else {
      console.error('API Error:', errorLog);
    }
  }

  static sendToMonitoring(errorLog) {
    // Send to external monitoring service
    fetch('/api/errors', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(errorLog)
    }).catch(console.error);
  }
}
```

### Error Recovery Strategies

```javascript
class ErrorRecovery {
  static async handleValidationError(error, originalData) {
    // Attempt to fix common validation issues
    const fixedData = { ...originalData };

    if (error.code === 'MISSING_REQUIRED_FIELDS') {
      const missingFields = error.details.missingFields;
      
      // Provide sensible defaults
      if (missingFields.includes('floors')) {
        fixedData.floors = 1;
      }
      if (missingFields.includes('qualityTier')) {
        fixedData.qualityTier = 'smart';
      }
      
      return fixedData;
    }

    if (error.code === 'VALIDATION_FAILED') {
      for (const validationError of error.details.errors) {
        switch (validationError.field) {
          case 'builtUpArea':
            if (fixedData.builtUpArea < 100) fixedData.builtUpArea = 100;
            if (fixedData.builtUpArea > 50000) fixedData.builtUpArea = 50000;
            break;
          case 'floors':
            if (fixedData.floors < 0) fixedData.floors = 0;
            if (fixedData.floors > 10) fixedData.floors = 10;
            break;
        }
      }
      return fixedData;
    }

    throw error;
  }

  static async handleCalculationError(error, originalData) {
    // Attempt to fix calculation issues
    const fixedData = { ...originalData };

    // Remove problematic special features
    if (fixedData.specialFeatures) {
      fixedData.specialFeatures = fixedData.specialFeatures.filter(
        feature => feature.cost > 0 && feature.cost < 10000000
      );
    }

    // Ensure reasonable values
    if (fixedData.plotArea && fixedData.plotArea < fixedData.builtUpArea) {
      fixedData.plotArea = fixedData.builtUpArea * 1.2;
    }

    return fixedData;
  }
}
```

---

## Testing Error Scenarios

### Unit Tests for Error Handling

```javascript
describe('Error Handling', () => {
  test('should handle missing required fields', async () => {
    const response = await request(app)
      .post('/api/calculate')
      .send({
        floors: 1
      });

    expect(response.status).toBe(400);
    expect(response.body.success).toBe(false);
    expect(response.body.error.code).toBe('MISSING_REQUIRED_FIELDS');
    expect(response.body.error.details.missingFields).toContain('builtUpArea');
  });

  test('should handle rate limiting', async () => {
    // Make multiple requests to trigger rate limiting
    const requests = Array(101).fill().map(() =>
      request(app)
        .post('/api/calculate')
        .send({
          builtUpArea: 1000,
          floors: 1,
          qualityTier: 'smart',
          location: 'bangalore'
        })
    );

    const responses = await Promise.all(requests);
    const rateLimitedResponse = responses.find(r => r.status === 429);

    expect(rateLimitedResponse).toBeDefined();
    expect(rateLimitedResponse.body.error.code).toBe('RATE_LIMIT_EXCEEDED');
  });

  test('should handle invalid data types', async () => {
    const response = await request(app)
      .post('/api/calculate')
      .send({
        builtUpArea: 'not a number',
        floors: 1,
        qualityTier: 'smart',
        location: 'bangalore'
      });

    expect(response.status).toBe(400);
    expect(response.body.error.code).toBe('INVALID_DATA_TYPES');
  });
});
```

### Integration Tests

```javascript
describe('Error Integration Tests', () => {
  test('should recover from validation errors', async () => {
    const invalidData = {
      builtUpArea: 50, // Too small
      floors: 15, // Too many
      qualityTier: 'smart',
      location: 'bangalore'
    };

    try {
      await calculateCost(invalidData);
      fail('Should have thrown validation error');
    } catch (error) {
      expect(error.code).toBe('VALIDATION_FAILED');
      
      // Test recovery
      const recoveredData = await ErrorRecovery.handleValidationError(error, invalidData);
      expect(recoveredData.builtUpArea).toBe(100);
      expect(recoveredData.floors).toBe(10);
      
      // Should succeed with recovered data
      const result = await calculateCost(recoveredData);
      expect(result.totalCost).toBeGreaterThan(0);
    }
  });
});
```

---

## FAQ

### Q: What should I do if I get a rate limit error?
A: Implement exponential backoff retry logic. Wait for the time specified in the `Retry-After` header before making another request.

### Q: How do I handle authentication errors?
A: Authentication errors indicate that you need to log in again. Redirect the user to the login page or refresh the JWT token.

### Q: What information should I log when an error occurs?
A: Log the error type, code, message, requestId, timestamp, and any relevant context (user actions, input data, etc.).

### Q: How do I test error handling in my application?
A: Use the provided test cases and simulate error conditions by sending invalid data or exceeding rate limits.

### Q: When should I retry a failed request?
A: Retry on rate limit errors (429) and server errors (5xx). Don't retry on client errors (4xx) except for authentication errors.

---

*This error handling guide provides comprehensive coverage of all error scenarios in the API. Use it to build robust client applications that gracefully handle all error conditions.*