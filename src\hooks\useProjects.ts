import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';

export interface Project {
  id: string;
  name: string;
  location: string;
  area_sqft: number;
  floors: number;
  quality_tier: 'smart' | 'premium' | 'luxury';
  created_at: string;
  updated_at: string;
  totalCost: number;
  costPerSqft: number;
  timeline: number;
}

export interface SaveProjectData {
  name: string;
  location: string;
  area_sqft: number;
  floors: number;
  quality_tier: 'smart' | 'premium' | 'luxury';
  calculation_data: {
    formData: any;
    results: any;
    calculatedAt: string;
    version?: string;
  };
}

// Fetch user's projects
export function useProjects(options?: { limit?: number; offset?: number }) {
  return useQuery({
    queryKey: ['projects', options?.limit, options?.offset],
    queryFn: async () => {
      const params = new URLSearchParams();
      if (options?.limit) params.set('limit', options.limit.toString());
      if (options?.offset) params.set('offset', options.offset.toString());

      const response = await fetch(`/api/projects?${params.toString()}`, {
        credentials: 'include',
      });

      if (!response.ok) {
        if (response.status === 401) {
          throw new Error('Authentication required');
        }
        throw new Error('Failed to fetch projects');
      }

      return response.json();
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
}

// Save new project
export function useSaveProject() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: SaveProjectData) => {
      const response = await fetch('/api/projects/save', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        if (response.status === 401) {
          throw new Error('Please sign in to save projects');
        }
        throw new Error(error.error || 'Failed to save project');
      }

      return response.json();
    },
    onSuccess: (data) => {
      // Invalidate projects query to refetch
      queryClient.invalidateQueries({ queryKey: ['projects'] });
      toast.success(`Project "${data.project.name}" saved successfully!`);
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}

// Delete project
export function useDeleteProject() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (projectId: string) => {
      const response = await fetch(`/api/projects?id=${projectId}`, {
        method: 'DELETE',
        credentials: 'include',
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to delete project');
      }

      return response.json();
    },
    onSuccess: () => {
      // Invalidate projects query to refetch
      queryClient.invalidateQueries({ queryKey: ['projects'] });
      toast.success('Project deleted successfully');
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}

// Load project calculation data
export function useLoadProject() {
  return useMutation({
    mutationFn: async (projectId: string) => {
      const response = await fetch(`/api/projects/${projectId}`, {
        credentials: 'include',
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to load project');
      }

      return response.json();
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}