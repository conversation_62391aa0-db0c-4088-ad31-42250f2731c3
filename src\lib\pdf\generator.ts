/**
 * Professional PDF Report Generator for Clarity Engine
 * Generates comprehensive construction cost calculation reports
 */

import jsPDF from 'jspdf';
import type { CalculationInput, CalculationResult } from '@/core/calculator/types';

// PDF Configuration Constants
const PDF_CONFIG = {
  // Page setup
  pageWidth: 210, // A4 width in mm
  pageHeight: 297, // A4 height in mm
  margin: 20,
  
  // Colors
  colors: {
    primary: '#2563eb', // Blue
    secondary: '#64748b', // Slate
    accent: '#059669', // Emerald
    text: '#1f2937', // Gray-800
    lightText: '#6b7280', // Gray-500
    background: '#f8fafc', // Slate-50
    border: '#e2e8f0', // Slate-200
  },
  
  // Fonts and sizes
  fonts: {
    title: 20,
    heading: 14,
    subheading: 12,
    body: 10,
    small: 8,
  },
  
  // Spacing
  spacing: {
    section: 15,
    paragraph: 8,
    line: 5,
  },
};

/**
 * Main PDF generation function
 */
export async function generatePDFReport(
  input: CalculationInput,
  result: CalculationResult,
  options?: {
    filename?: string;
    includeTimeline?: boolean;
    includeMaterials?: boolean;
    includeDetailedBreakdown?: boolean;
  }
): Promise<void> {
  const {
    filename = generateDefaultFilename(input),
    includeTimeline = true,
    includeMaterials = true,
    includeDetailedBreakdown = true,
  } = options || {};

  // Initialize PDF with proper settings
  const pdf = new jsPDF({
    orientation: 'portrait',
    unit: 'mm',
    format: 'a4',
    compress: true,
  });

  // Set initial font
  pdf.setFont('helvetica');
  
  let currentY = PDF_CONFIG.margin;

  // 1. Header Section
  currentY = addHeader(pdf, currentY);
  
  // 2. Project Information Section
  currentY = addProjectInfo(pdf, input, currentY);
  
  // 3. Executive Summary Section
  currentY = addExecutiveSummary(pdf, result, currentY);
  
  // 4. Cost Breakdown Section
  currentY = addCostBreakdown(pdf, result, currentY, includeDetailedBreakdown);
  
  // Check if we need a new page
  if (currentY > PDF_CONFIG.pageHeight - 60) {
    pdf.addPage();
    currentY = PDF_CONFIG.margin;
  }
  
  // 5. Materials List Section (if enabled)
  if (includeMaterials && result.materials.length > 0) {
    currentY = addMaterialsList(pdf, result.materials, currentY);
  }
  
  // 6. Timeline Section (if enabled)
  if (includeTimeline && result.timeline.length > 0) {
    currentY = addTimeline(pdf, result.timeline, currentY);
  }
  
  // 7. Regional Information Section
  currentY = addRegionalInfo(pdf, input, result, currentY);
  
  // 8. Footer Section
  addFooter(pdf);
  
  // Save the PDF
  pdf.save(filename);
}

/**
 * Add header with company branding
 */
function addHeader(pdf: jsPDF, startY: number): number {
  const { margin, colors, fonts } = PDF_CONFIG;
  
  // Company logo placeholder (can be replaced with actual logo)
  pdf.setFillColor(colors.primary);
  pdf.rect(margin, startY, 40, 12, 'F');
  
  // Company name
  pdf.setTextColor(255, 255, 255);
  pdf.setFontSize(fonts.heading);
  pdf.setFont('helvetica', 'bold');
  pdf.text('CLARITY ENGINE', margin + 2, startY + 8);
  
  // Tagline
  pdf.setTextColor(colors.text);
  pdf.setFontSize(fonts.small);
  pdf.setFont('helvetica', 'normal');
  pdf.text('Construction Intelligence Platform', margin + 45, startY + 6);
  
  // Report title
  pdf.setFontSize(fonts.title);
  pdf.setFont('helvetica', 'bold');
  pdf.text('Construction Cost Analysis Report', margin + 45, startY + 12);
  
  // Divider line
  pdf.setDrawColor(colors.border);
  pdf.line(margin, startY + 20, PDF_CONFIG.pageWidth - margin, startY + 20);
  
  return startY + 30;
}

/**
 * Add project information section
 */
function addProjectInfo(pdf: jsPDF, input: CalculationInput, startY: number): number {
  const { margin, colors, fonts, spacing } = PDF_CONFIG;
  let currentY = startY;
  
  // Section heading
  currentY = addSectionHeading(pdf, 'Project Information', currentY);
  
  // Project details in two columns
  const leftColumn = margin;
  const rightColumn = margin + 95;
  
  pdf.setFontSize(fonts.body);
  pdf.setFont('helvetica', 'normal');
  pdf.setTextColor(colors.text);
  
  // Left column
  const leftItems = [
    { label: 'Built-up Area:', value: `${input.builtUpArea.toLocaleString()} sq ft` },
    { label: 'Quality Tier:', value: capitalizeFirst(input.qualityTier) },
    { label: 'Location:', value: capitalizeFirst(input.location) },
  ];
  
  // Right column
  const rightItems = [
    { label: 'Floors:', value: `G+${input.floors}` },
    { label: 'Plot Area:', value: input.plotArea ? `${input.plotArea.toLocaleString()} sq ft` : 'Not specified' },
    { label: 'Basement:', value: input.hasBasement ? 'Yes' : 'No' },
  ];
  
  // Render left column
  leftItems.forEach((item, index) => {
    const y = currentY + (index * spacing.line);
    pdf.setFont('helvetica', 'bold');
    pdf.text(item.label, leftColumn, y);
    pdf.setFont('helvetica', 'normal');
    pdf.text(item.value, leftColumn + 30, y);
  });
  
  // Render right column
  rightItems.forEach((item, index) => {
    const y = currentY + (index * spacing.line);
    pdf.setFont('helvetica', 'bold');
    pdf.text(item.label, rightColumn, y);
    pdf.setFont('helvetica', 'normal');
    pdf.text(item.value, rightColumn + 25, y);
  });
  
  return currentY + (Math.max(leftItems.length, rightItems.length) * spacing.line) + spacing.section;
}

/**
 * Add executive summary section
 */
function addExecutiveSummary(pdf: jsPDF, result: CalculationResult, startY: number): number {
  const { margin, colors, fonts, spacing } = PDF_CONFIG;
  let currentY = startY;
  
  // Section heading
  currentY = addSectionHeading(pdf, 'Executive Summary', currentY);
  
  // Hero metrics in boxes
  const boxWidth = 50;
  const boxHeight = 20;
  const boxSpacing = 10;
  
  const metrics = [
    { label: 'Total Cost', value: `₹${(result.totalCost / 10000000).toFixed(2)} Cr`, color: colors.primary },
    { label: 'Cost per Sq Ft', value: `₹${result.costPerSqft.toLocaleString()}`, color: colors.accent },
    { label: 'Timeline', value: `${result.summary.constructionDuration} months`, color: colors.secondary },
  ];
  
  metrics.forEach((metric, index) => {
    const x = margin + (index * (boxWidth + boxSpacing));
    
    // Box background
    pdf.setFillColor(metric.color);
    pdf.rect(x, currentY, boxWidth, boxHeight, 'F');
    
    // Value (white text)
    pdf.setTextColor(255, 255, 255);
    pdf.setFontSize(fonts.heading);
    pdf.setFont('helvetica', 'bold');
    const valueWidth = pdf.getTextWidth(metric.value);
    pdf.text(metric.value, x + (boxWidth - valueWidth) / 2, currentY + 10);
    
    // Label (below box)
    pdf.setTextColor(colors.text);
    pdf.setFontSize(fonts.small);
    pdf.setFont('helvetica', 'normal');
    const labelWidth = pdf.getTextWidth(metric.label);
    pdf.text(metric.label, x + (boxWidth - labelWidth) / 2, currentY + boxHeight + 5);
  });
  
  currentY += boxHeight + 15;
  
  // Project summary text
  pdf.setTextColor(colors.text);
  pdf.setFontSize(fonts.body);
  pdf.setFont('helvetica', 'normal');
  
  const summaryText = [
    `This comprehensive analysis covers a ${result.summary.totalBuiltUpArea.toLocaleString()} sq ft construction project`,
    `with ${capitalizeFirst(result.summary.qualityTier)} quality specifications in ${capitalizeFirst(result.summary.location)}.`,
    `The estimated construction duration is ${result.summary.constructionDuration} months with an accuracy of ${result.summary.estimateAccuracy}.`
  ];
  
  summaryText.forEach((text, index) => {
    pdf.text(text, margin, currentY + (index * spacing.line));
  });
  
  return currentY + (summaryText.length * spacing.line) + spacing.section;
}

/**
 * Add cost breakdown section
 */
function addCostBreakdown(pdf: jsPDF, result: CalculationResult, startY: number, includeDetails: boolean): number {
  const { margin, colors, fonts, spacing } = PDF_CONFIG;
  let currentY = startY;
  
  // Section heading
  currentY = addSectionHeading(pdf, 'Cost Breakdown Analysis', currentY);
  
  // Main categories
  const categories = [
    { name: 'Structure', data: result.breakdown.structure },
    { name: 'Finishing', data: result.breakdown.finishing },
    { name: 'MEP Systems', data: result.breakdown.mep },
    { name: 'External Works', data: result.breakdown.external },
    { name: 'Other Costs', data: result.breakdown.other },
  ];
  
  // Table header
  pdf.setFillColor(colors.background);
  pdf.rect(margin, currentY, PDF_CONFIG.pageWidth - (2 * margin), 8, 'F');
  pdf.setTextColor(colors.text);
  pdf.setFontSize(fonts.small);
  pdf.setFont('helvetica', 'bold');
  
  pdf.text('Category', margin + 2, currentY + 5);
  pdf.text('Amount (₹)', margin + 80, currentY + 5);
  pdf.text('Percentage', margin + 130, currentY + 5);
  
  currentY += 10;
  
  // Category rows
  categories.forEach((category, index) => {
    const rowY = currentY + (index * 8);
    
    // Alternating row background
    if (index % 2 === 0) {
      pdf.setFillColor(248, 250, 252); // Very light gray
      pdf.rect(margin, rowY - 2, PDF_CONFIG.pageWidth - (2 * margin), 8, 'F');
    }
    
    pdf.setTextColor(colors.text);
    pdf.setFontSize(fonts.body);
    pdf.setFont('helvetica', 'normal');
    
    pdf.text(category.name, margin + 2, rowY + 3);
    pdf.text(`₹${category.data.amount.toLocaleString()}`, margin + 80, rowY + 3);
    pdf.text(`${category.data.percentage.toFixed(1)}%`, margin + 130, rowY + 3);
    
    // Progress bar
    const barWidth = 30;
    const barHeight = 3;
    const barX = margin + 150;
    const barY = rowY + 1;
    
    // Background bar
    pdf.setFillColor(colors.border);
    pdf.rect(barX, barY, barWidth, barHeight, 'F');
    
    // Progress bar
    const progressWidth = (category.data.percentage / 100) * barWidth;
    pdf.setFillColor(colors.primary);
    pdf.rect(barX, barY, progressWidth, barHeight, 'F');
  });
  
  currentY += categories.length * 8 + spacing.paragraph;
  
  // Total row
  pdf.setFillColor(colors.primary);
  pdf.rect(margin, currentY, PDF_CONFIG.pageWidth - (2 * margin), 8, 'F');
  pdf.setTextColor(255, 255, 255);
  pdf.setFont('helvetica', 'bold');
  pdf.text('TOTAL', margin + 2, currentY + 5);
  pdf.text(`₹${result.breakdown.total.toLocaleString()}`, margin + 80, currentY + 5);
  pdf.text('100%', margin + 130, currentY + 5);
  
  currentY += 15;
  
  // Detailed subcategories (if enabled)
  if (includeDetails) {
    currentY = addSubcategoriesBreakdown(pdf, result.breakdown, currentY);
  }
  
  return currentY;
}

/**
 * Add subcategories breakdown
 */
function addSubcategoriesBreakdown(pdf: jsPDF, breakdown: any, startY: number): number {
  const { margin, colors, fonts, spacing } = PDF_CONFIG;
  let currentY = startY;
  
  // Check if we need a new page
  if (currentY > PDF_CONFIG.pageHeight - 100) {
    pdf.addPage();
    currentY = PDF_CONFIG.margin;
  }
  
  currentY = addSectionHeading(pdf, 'Detailed Cost Components', currentY);
  
  const categories = [
    { name: 'Structure', data: breakdown.structure },
    { name: 'Finishing', data: breakdown.finishing },
    { name: 'MEP Systems', data: breakdown.mep },
    { name: 'External Works', data: breakdown.external },
  ];
  
  categories.forEach((category) => {
    if (category.data.subCategories && category.data.subCategories.length > 0) {
      // Category name
      pdf.setTextColor(colors.primary);
      pdf.setFontSize(fonts.subheading);
      pdf.setFont('helvetica', 'bold');
      pdf.text(category.name, margin, currentY);
      currentY += spacing.line;
      
      // Subcategories
      category.data.subCategories.forEach((sub: any) => {
        pdf.setTextColor(colors.text);
        pdf.setFontSize(fonts.small);
        pdf.setFont('helvetica', 'normal');
        
        pdf.text(`• ${sub.name}`, margin + 5, currentY);
        pdf.text(`₹${sub.amount.toLocaleString()}`, margin + 120, currentY);
        if (sub.description) {
          pdf.setTextColor(colors.lightText);
          pdf.text(sub.description, margin + 10, currentY + 3);
          currentY += 3;
        }
        currentY += spacing.line;
      });
      
      currentY += spacing.paragraph;
    }
  });
  
  return currentY;
}

/**
 * Add materials list section
 */
function addMaterialsList(pdf: jsPDF, materials: any[], startY: number): number {
  const { margin, colors, fonts, spacing } = PDF_CONFIG;
  let currentY = startY;
  
  // Check if we need a new page
  if (currentY > PDF_CONFIG.pageHeight - 80) {
    pdf.addPage();
    currentY = PDF_CONFIG.margin;
  }
  
  currentY = addSectionHeading(pdf, 'Materials Requirement', currentY);
  
  // Group materials by category
  const groupedMaterials = materials.reduce((acc, material) => {
    if (!acc[material.category]) {
      acc[material.category] = [];
    }
    acc[material.category].push(material);
    return acc;
  }, {} as Record<string, any[]>);
  
  Object.entries(groupedMaterials).forEach(([category, categoryMaterials]) => {
    // Category heading
    pdf.setTextColor(colors.accent);
    pdf.setFontSize(fonts.subheading);
    pdf.setFont('helvetica', 'bold');
    pdf.text(capitalizeFirst(category), margin, currentY);
    currentY += spacing.line;
    
    // Materials table
    (categoryMaterials as any[]).forEach((material: any) => {
      pdf.setTextColor(colors.text);
      pdf.setFontSize(fonts.small);
      pdf.setFont('helvetica', 'normal');
      
      pdf.text(`• ${material.name}`, margin + 5, currentY);
      pdf.text(`${material.quantity.toLocaleString()} ${material.unit}`, margin + 80, currentY);
      if (material.totalCost) {
        pdf.text(`₹${material.totalCost.toLocaleString()}`, margin + 130, currentY);
      }
      
      currentY += spacing.line;
    });
    
    currentY += spacing.paragraph;
  });
  
  return currentY;
}

/**
 * Add timeline section
 */
function addTimeline(pdf: jsPDF, timeline: any[], startY: number): number {
  const { margin, colors, fonts, spacing } = PDF_CONFIG;
  let currentY = startY;
  
  // Check if we need a new page
  if (currentY > PDF_CONFIG.pageHeight - 60) {
    pdf.addPage();
    currentY = PDF_CONFIG.margin;
  }
  
  currentY = addSectionHeading(pdf, 'Construction Timeline', currentY);
  
  timeline.forEach((phase, index) => {
    // Phase number circle
    pdf.setFillColor(colors.primary);
    pdf.circle(margin + 5, currentY + 2, 3, 'F');
    pdf.setTextColor(255, 255, 255);
    pdf.setFontSize(fonts.small);
    pdf.setFont('helvetica', 'bold');
    pdf.text((index + 1).toString(), margin + 3.5, currentY + 3);
    
    // Phase details
    pdf.setTextColor(colors.text);
    pdf.setFontSize(fonts.body);
    pdf.setFont('helvetica', 'bold');
    pdf.text(phase.name, margin + 15, currentY + 3);
    
    pdf.setFont('helvetica', 'normal');
    pdf.setFontSize(fonts.small);
    pdf.text(`Duration: ${phase.duration} weeks`, margin + 15, currentY + 8);
    
    if (phase.description) {
      pdf.setTextColor(colors.lightText);
      pdf.text(phase.description, margin + 15, currentY + 12);
      currentY += 4;
    }
    
    currentY += 15;
  });
  
  return currentY;
}

/**
 * Add regional information section
 */
function addRegionalInfo(pdf: jsPDF, input: CalculationInput, result: CalculationResult, startY: number): number {
  const { margin, colors, fonts, spacing } = PDF_CONFIG;
  let currentY = startY;
  
  // Check if we need a new page
  if (currentY > PDF_CONFIG.pageHeight - 50) {
    pdf.addPage();
    currentY = PDF_CONFIG.margin;
  }
  
  currentY = addSectionHeading(pdf, 'Regional Considerations', currentY);
  
  const regionalInfo = [
    `Location: ${capitalizeFirst(input.location)}`,
    `Quality Tier: ${capitalizeFirst(input.qualityTier)} grade specifications`,
    `Cost Accuracy: ${result.summary.estimateAccuracy} based on current market rates`,
    'Pricing includes: Material costs, labor charges, equipment rental, and overheads',
    'Regional factors: Local material availability, labor rates, and transportation costs',
  ];
  
  pdf.setTextColor(colors.text);
  pdf.setFontSize(fonts.small);
  pdf.setFont('helvetica', 'normal');
  
  regionalInfo.forEach((info, index) => {
    pdf.text(`• ${info}`, margin, currentY + (index * spacing.line));
  });
  
  return currentY + (regionalInfo.length * spacing.line) + spacing.section;
}

/**
 * Add footer with disclaimer and contact info
 */
function addFooter(pdf: jsPDF): void {
  const { margin, colors, fonts } = PDF_CONFIG;
  const footerY = PDF_CONFIG.pageHeight - 25;
  
  // Divider line
  pdf.setDrawColor(colors.border);
  pdf.line(margin, footerY - 5, PDF_CONFIG.pageWidth - margin, footerY - 5);
  
  // Disclaimer
  pdf.setTextColor(colors.lightText);
  pdf.setFontSize(fonts.small);
  pdf.setFont('helvetica', 'normal');
  
  const disclaimer = [
    'DISCLAIMER: This estimate is based on current market rates and standard specifications. Actual costs may vary based on',
    'site conditions, material quality, design changes, and market fluctuations. Please consult with construction professionals for final planning.',
  ];
  
  disclaimer.forEach((line, index) => {
    pdf.text(line, margin, footerY + (index * 4));
  });
  
  // Contact info
  pdf.setTextColor(colors.primary);
  pdf.setFont('helvetica', 'bold');
  pdf.text('Clarity Engine - Construction Intelligence Platform', margin, footerY + 12);
  
  pdf.setTextColor(colors.lightText);
  pdf.setFont('helvetica', 'normal');
  pdf.text('Generated on: ' + new Date().toLocaleDateString('en-IN'), PDF_CONFIG.pageWidth - margin - 40, footerY + 12);
}

/**
 * Add section heading
 */
function addSectionHeading(pdf: jsPDF, title: string, y: number): number {
  const { margin, colors, fonts, spacing } = PDF_CONFIG;
  
  pdf.setTextColor(colors.primary);
  pdf.setFontSize(fonts.heading);
  pdf.setFont('helvetica', 'bold');
  pdf.text(title, margin, y);
  
  // Underline
  const textWidth = pdf.getTextWidth(title);
  pdf.setDrawColor(colors.primary);
  pdf.line(margin, y + 2, margin + textWidth, y + 2);
  
  return y + spacing.section;
}

/**
 * Generate default filename
 */
function generateDefaultFilename(input: CalculationInput): string {
  const date = new Date().toISOString().split('T')[0];
  const location = input.location.charAt(0).toUpperCase() + input.location.slice(1);
  const tier = input.qualityTier.charAt(0).toUpperCase() + input.qualityTier.slice(1);
  
  return `Clarity-Engine-Report-${location}-${tier}-${input.builtUpArea}sqft-${date}.pdf`;
}

/**
 * Capitalize first letter
 */
function capitalizeFirst(str: string): string {
  return str.charAt(0).toUpperCase() + str.slice(1);
}