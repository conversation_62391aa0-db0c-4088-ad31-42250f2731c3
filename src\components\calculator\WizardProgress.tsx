/**
 * Wizard Progress Component
 * Professional progress indicator with step visualization
 */

import React from 'react';
import { motion, AnimatePresence, type Variants } from 'framer-motion';
import { CheckCircle, Circle, AlertCircle, Clock } from 'lucide-react';
import { cn } from '@/lib/utils';
import { EnhancedProgress } from '@/components/ui/enhanced-progress';
import { WizardState } from './types/wizard';

interface WizardProgressProps {
  state: WizardState;
  steps: Array<{
    id: string;
    title: string;
    description?: string;
    estimatedTime?: string;
    optional?: boolean;
  }>;
  variant?: 'linear' | 'steps' | 'compact';
  showLabels?: boolean;
  showDescription?: boolean;
  showTime?: boolean;
  className?: string;
}

// Animation variants
const progressVariants: Variants = {
  hidden: { opacity: 0, y: -20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.3,
      staggerChildren: 0.1,
    },
  },
};

const stepVariants: Variants = {
  hidden: { opacity: 0, scale: 0.8 },
  visible: { opacity: 1, scale: 1 },
  completed: {
    scale: [1, 1.1, 1],
    transition: { duration: 0.3 },
  },
};

const lineVariants: Variants = {
  initial: { scaleX: 0 },
  animate: { scaleX: 1 },
};

export function WizardProgress({
  state,
  steps,
  variant = 'steps',
  showLabels = true,
  showDescription = false,
  showTime = false,
  className,
}: WizardProgressProps) {
  const { currentStep, completedSteps, skippedSteps, progress } = state;

  // Helper functions
  const getStepStatus = (index: number) => {
    if (completedSteps.has(index)) return 'completed';
    if (skippedSteps.has(index)) return 'skipped';
    if (index === currentStep) return 'current';
    if (index < currentStep) return 'completed';
    return 'pending';
  };

  const getStepIcon = (index: number, status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5" />;
      case 'current':
        return <Circle className="h-5 w-5 animate-pulse" />;
      case 'skipped':
        return <AlertCircle className="h-5 w-5" />;
      default:
        return <Circle className="h-5 w-5 opacity-50" />;
    }
  };

  const getStepColors = (status: string) => {
    switch (status) {
      case 'completed':
        return {
          bg: 'bg-green-500',
          border: 'border-green-500',
          text: 'text-green-700',
          icon: 'text-white',
        };
      case 'current':
        return {
          bg: 'bg-primary-500',
          border: 'border-primary-500',
          text: 'text-primary-700',
          icon: 'text-white',
        };
      case 'skipped':
        return {
          bg: 'bg-yellow-500',
          border: 'border-yellow-500',
          text: 'text-yellow-700',
          icon: 'text-white',
        };
      default:
        return {
          bg: 'bg-secondary-200',
          border: 'border-secondary-300',
          text: 'text-secondary-500',
          icon: 'text-secondary-400',
        };
    }
  };

  // Render linear progress
  if (variant === 'linear') {
    return (
      <motion.div
        className={cn('space-y-4', className)}
        variants={progressVariants}
        initial="hidden"
        animate="visible"
      >
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-secondary-900">
            Step {currentStep + 1} of {steps.length}
          </h3>
          <span className="text-sm text-secondary-600">
            {Math.round(progress)}% Complete
          </span>
        </div>
        
        <EnhancedProgress
          value={progress}
          variant="gradient"
          size="lg"
          animated
          showPercentage={false}
        />
        
        <div className="text-center">
          <h4 className="font-medium text-secondary-900">
            {steps[currentStep]?.title}
          </h4>
          {showDescription && steps[currentStep]?.description && (
            <p className="text-sm text-secondary-600 mt-1">
              {steps[currentStep].description}
            </p>
          )}
          {showTime && steps[currentStep]?.estimatedTime && (
            <div className="flex items-center justify-center gap-1 mt-2 text-xs text-secondary-500">
              <Clock className="h-3 w-3" />
              {steps[currentStep].estimatedTime}
            </div>
          )}
        </div>
      </motion.div>
    );
  }

  // Render compact progress
  if (variant === 'compact') {
    return (
      <motion.div
        className={cn('flex items-center gap-2', className)}
        variants={progressVariants}
        initial="hidden"
        animate="visible"
      >
        {steps.map((step, index) => {
          const status = getStepStatus(index);
          const colors = getStepColors(status);
          
          return (
            <motion.div
              key={step.id}
              className={cn(
                'flex-1 h-2 rounded-full transition-all duration-300',
                colors.bg
              )}
              variants={stepVariants}
              initial="hidden"
              animate="visible"
              whileHover={{ scale: 1.05 }}
            />
          );
        })}
      </motion.div>
    );
  }

  // Render step-by-step progress (default)
  return (
    <motion.div
      className={cn('space-y-6', className)}
      variants={progressVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Overall Progress Bar */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium text-secondary-700">
            Progress
          </span>
          <span className="text-sm text-secondary-600">
            {Math.round(progress)}%
          </span>
        </div>
        <EnhancedProgress
          value={progress}
          variant="gradient"
          size="md"
          animated
          showPercentage={false}
        />
      </div>

      {/* Step Indicators */}
      <div className="relative">
        {/* Connecting Lines */}
        <div className="absolute top-6 left-6 right-6 h-0.5 bg-secondary-200">
          {steps.map((_, index) => {
            if (index === steps.length - 1) return null;
            
            const isCompleted = completedSteps.has(index) || index < currentStep;
            
            return (
              <motion.div
                key={index}
                className={cn(
                  'absolute top-0 h-full',
                  isCompleted ? 'bg-primary-500' : 'bg-secondary-200'
                )}
                style={{
                  left: `${(index * 100) / (steps.length - 1)}%`,
                  width: `${100 / (steps.length - 1)}%`,
                }}
                variants={lineVariants}
                initial="initial"
                animate={isCompleted ? "animate" : "initial"}
                transition={{ duration: 0.3, delay: index * 0.1 }}
              />
            );
          })}
        </div>

        {/* Step Circles */}
        <div className="relative flex justify-between">
          {steps.map((step, index) => {
            const status = getStepStatus(index);
            const colors = getStepColors(status);
            
            return (
              <motion.div
                key={step.id}
                className="flex flex-col items-center space-y-2"
                variants={stepVariants}
                initial="hidden"
                animate="visible"
                whileHover={{ y: -2 }}
              >
                {/* Step Circle */}
                <motion.div
                  className={cn(
                    'relative flex items-center justify-center',
                    'h-12 w-12 rounded-full border-2 transition-all duration-300',
                    colors.bg,
                    colors.border,
                    'shadow-lg'
                  )}
                  variants={stepVariants}
                  animate={status === 'completed' ? 'completed' : 'visible'}
                >
                  <div className={colors.icon}>
                    {getStepIcon(index, status)}
                  </div>
                  
                  {/* Step Number */}
                  {status === 'pending' && (
                    <span className={cn('text-sm font-semibold', colors.text)}>
                      {index + 1}
                    </span>
                  )}

                  {/* Pulse Animation for Current Step */}
                  {status === 'current' && (
                    <motion.div
                      className="absolute inset-0 rounded-full border-2 border-primary-500"
                      animate={{
                        scale: [1, 1.2, 1],
                        opacity: [1, 0, 1],
                      }}
                      transition={{
                        duration: 2,
                        repeat: Infinity,
                        ease: "easeInOut",
                      }}
                    />
                  )}
                </motion.div>

                {/* Step Labels */}
                {showLabels && (
                  <div className="text-center space-y-1 max-w-[100px]">
                    <h4
                      className={cn(
                        'text-xs font-medium transition-colors duration-200',
                        status === 'current' ? colors.text : 'text-secondary-700'
                      )}
                    >
                      {step.title}
                    </h4>
                    
                    {showDescription && step.description && (
                      <p className="text-xs text-secondary-500 leading-tight">
                        {step.description}
                      </p>
                    )}
                    
                    {showTime && step.estimatedTime && (
                      <div className="flex items-center justify-center gap-1 text-xs text-secondary-400">
                        <Clock className="h-3 w-3" />
                        {step.estimatedTime}
                      </div>
                    )}
                    
                    {step.optional && (
                      <span className="text-xs text-yellow-600 italic">
                        Optional
                      </span>
                    )}
                  </div>
                )}
              </motion.div>
            );
          })}
        </div>
      </div>

      {/* Current Step Info */}
      <AnimatePresence mode="wait">
        <motion.div
          key={currentStep}
          className="text-center p-4 bg-primary-50 rounded-lg border border-primary-200"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.3 }}
        >
          <h3 className="font-semibold text-primary-900">
            {steps[currentStep]?.title}
          </h3>
          {steps[currentStep]?.description && (
            <p className="text-sm text-primary-700 mt-1">
              {steps[currentStep].description}
            </p>
          )}
        </motion.div>
      </AnimatePresence>
    </motion.div>
  );
}

export default WizardProgress;