import { NextRequest, NextResponse } from 'next/server';
// import { withPerformanceMonitoring } from '@/lib/performance/monitoring';

interface MetricData {
  name: string;
  value: number;
  rating?: 'good' | 'needs-improvement' | 'poor';
  timestamp: number;
  url: string;
  userAgent: string;
  sessionId?: string;
  userId?: string;
  deviceType?: string;
  connectionType?: string;
  route?: string;
  referrer?: string;
  buildVersion?: string;
  page?: string;
  viewport?: {
    width: number;
    height: number;
  };
}

// In-memory storage for development (use database in production)
const metricsStore: MetricData[] = [];
const MAX_METRICS = 10000;

export async function POST(request: NextRequest) {
  try {
      const body = await request.json();
      const { metric } = body;

      if (!metric || !metric.name || typeof metric.value !== 'number') {
        return NextResponse.json(
          { error: 'Invalid metric data' },
          { status: 400 }
        );
      }

      const metricData: MetricData = {
        name: metric.name,
        value: metric.value,
        rating: metric.rating,
        timestamp: metric.timestamp || Date.now(),
        url: metric.url || request.url,
        userAgent: metric.userAgent || request.headers.get('user-agent') || '',
        sessionId: metric.sessionId,
        userId: metric.userId,
        deviceType: metric.deviceType,
        connectionType: metric.connectionType,
        route: metric.route,
        referrer: metric.referrer,
        buildVersion: metric.buildVersion,
        page: body.page,
        viewport: body.viewport,
      };

      // Store metric
      metricsStore.push(metricData);

      // Keep only recent metrics
      if (metricsStore.length > MAX_METRICS) {
        metricsStore.splice(0, metricsStore.length - MAX_METRICS);
      }

      // Log in development
      if (process.env.NODE_ENV === 'development') {
        console.log(`[Performance Metric] ${metric.name}: ${metric.value}${getMetricUnit(metric.name)}`);
      }

      // In production, send to monitoring service
      if (process.env.NODE_ENV === 'production') {
        await sendToMonitoringService(metricData);
      }

      // Check for alerts
      const alerts = checkForAlerts(metricData);
      if (alerts.length > 0) {
        await processAlerts(alerts);
      }

      return NextResponse.json(
        { status: 'received', alerts: alerts.length },
        { status: 200 }
      );
    } catch (error) {
      console.error('Performance metrics error:', error);
      return NextResponse.json(
        { error: 'Failed to process metrics' },
        { status: 500 }
      );
    }
}

export async function GET(request: NextRequest) {
  try {
      const { searchParams } = new URL(request.url);
      const metric = searchParams.get('metric');
      const startTime = searchParams.get('start') ? parseInt(searchParams.get('start')!) : Date.now() - 3600000; // 1 hour ago
      const endTime = searchParams.get('end') ? parseInt(searchParams.get('end')!) : Date.now();
      const limit = searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : 100;

      // Filter metrics
      let filteredMetrics = metricsStore.filter(m => 
        m.timestamp >= startTime && m.timestamp <= endTime
      );

      if (metric) {
        filteredMetrics = filteredMetrics.filter(m => m.name === metric);
      }

      // Sort by timestamp (newest first)
      filteredMetrics.sort((a, b) => b.timestamp - a.timestamp);

      // Limit results
      filteredMetrics = filteredMetrics.slice(0, limit);

      // Calculate aggregated statistics
      const stats = calculateMetricStats(filteredMetrics);

      return NextResponse.json({
        metrics: filteredMetrics,
        stats,
        totalCount: filteredMetrics.length,
        timestamp: Date.now()
      });
    } catch (error) {
      console.error('Performance metrics retrieval error:', error);
      return NextResponse.json(
        { error: 'Failed to retrieve metrics' },
        { status: 500 }
      );
    }
}

function getMetricUnit(metricName: string): string {
  const units: Record<string, string> = {
    'LCP': 'ms',
    'FID': 'ms',
    'CLS': '',
    'FCP': 'ms',
    'TTFB': 'ms',
    'INP': 'ms',
  };
  return units[metricName] || '';
}

function checkForAlerts(metric: MetricData): Array<{
  type: 'warning' | 'error';
  message: string;
  threshold: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
}> {
  const alerts: Array<{
    type: 'warning' | 'error';
    message: string;
    threshold: number;
    severity: 'low' | 'medium' | 'high' | 'critical';
  }> = [];

  const thresholds = {
    'LCP': { warning: 2500, error: 4000 },
    'FID': { warning: 100, error: 300 },
    'CLS': { warning: 0.1, error: 0.25 },
    'FCP': { warning: 1800, error: 3000 },
    'TTFB': { warning: 800, error: 1800 },
    'INP': { warning: 200, error: 500 },
  };

  const threshold = thresholds[metric.name as keyof typeof thresholds];
  if (threshold) {
    if (metric.value > threshold.error) {
      alerts.push({
        type: 'error',
        message: `${metric.name} is critically slow: ${metric.value}${getMetricUnit(metric.name)}`,
        threshold: threshold.error,
        severity: 'critical'
      });
    } else if (metric.value > threshold.warning) {
      alerts.push({
        type: 'warning',
        message: `${metric.name} needs improvement: ${metric.value}${getMetricUnit(metric.name)}`,
        threshold: threshold.warning,
        severity: 'medium'
      });
    }
  }

  return alerts;
}

async function processAlerts(alerts: Array<{
  type: 'warning' | 'error';
  message: string;
  threshold: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
}>) {
  // In production, send alerts to monitoring service, Slack, etc.
  for (const alert of alerts) {
    console.warn(`[Performance Alert] ${alert.type.toUpperCase()}: ${alert.message}`);
    
    // Send to monitoring service
    if (process.env.NODE_ENV === 'production') {
      try {
        // Example: Send to Slack, Discord, email, etc.
        await sendAlert(alert);
      } catch (error) {
        console.error('Failed to send alert:', error);
      }
    }
  }
}

async function sendAlert(alert: {
  type: 'warning' | 'error';
  message: string;
  threshold: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
}) {
  // Implementation depends on your alerting system
  // Example: Slack webhook, email service, etc.
  console.log('Sending alert:', alert);
}

async function sendToMonitoringService(metric: MetricData) {
  // Send to external monitoring service
  // Example: DataDog, New Relic, Custom analytics
  try {
    // Implementation depends on your monitoring service
    console.log('Sending to monitoring service:', metric);
  } catch (error) {
    console.error('Failed to send to monitoring service:', error);
  }
}

function calculateMetricStats(metrics: MetricData[]) {
  if (metrics.length === 0) {
    return {
      count: 0,
      average: 0,
      median: 0,
      p95: 0,
      p99: 0,
      min: 0,
      max: 0
    };
  }

  const values = metrics.map(m => m.value).sort((a, b) => a - b);
  const count = values.length;
  const sum = values.reduce((a, b) => a + b, 0);
  const average = sum / count;
  const median = values[Math.floor(count / 2)];
  const p95 = values[Math.floor(count * 0.95)];
  const p99 = values[Math.floor(count * 0.99)];
  const min = values[0];
  const max = values[count - 1];

  return {
    count,
    average: Math.round(average * 100) / 100,
    median,
    p95,
    p99,
    min,
    max
  };
}