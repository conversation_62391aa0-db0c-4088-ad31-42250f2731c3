@import 'tailwindcss';
@import 'tw-animate-css';

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Enhanced Skeleton Animations */
@layer components {
  /* Wave animation for skeleton shimmer */
  @keyframes wave {
    0% {
      background-position: -200px 0;
    }
    100% {
      background-position: calc(200px + 100%) 0;
    }
  }
  
  /* Pulse animation with better performance */
  @keyframes skeleton-pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }
  
  /* Shimmer gradient animation */
  @keyframes shimmer {
    0% {
      background-position: -468px 0;
    }
    100% {
      background-position: 468px 0;
    }
  }
  
  /* Skeleton glow effect */
  @keyframes skeleton-glow {
    0%, 100% {
      box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
    }
    50% {
      box-shadow: 0 0 20px rgba(0, 0, 0, 0.15);
    }
  }
  
  /* Progressive loading fade */
  @keyframes progressive-fade {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  /* Skeleton classes */
  .skeleton {
    @apply bg-gray-200 animate-pulse rounded;
  }
  
  .skeleton-shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite linear;
  }
  
  .skeleton-wave {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: wave 1.5s infinite ease-in-out;
  }
  
  .skeleton-glow {
    animation: skeleton-glow 2s infinite ease-in-out;
  }
  
  /* Responsive skeleton heights */
  .skeleton-text {
    @apply h-3 sm:h-4;
  }
  
  .skeleton-title {
    @apply h-5 sm:h-6;
  }
  
  .skeleton-subtitle {
    @apply h-4 sm:h-5;
  }
  
  /* Loading states for accessibility */
  .loading-skeleton[aria-hidden="true"] {
    @apply sr-only;
  }
  
  .loading-content[aria-busy="true"] {
    @apply pointer-events-none opacity-75;
  }
  
  /* Mobile optimized skeleton */
  @media (max-width: 640px) {
    .skeleton-mobile {
      @apply h-3 rounded-sm;
    }
    
    .skeleton-mobile-card {
      @apply h-24 rounded-lg;
    }
    
    .skeleton-mobile-avatar {
      @apply w-8 h-8 rounded-full;
    }
  }
  
  /* Dark mode skeleton support */
  .dark .skeleton {
    @apply bg-gray-800;
  }
  
  .dark .skeleton-shimmer {
    background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
    background-size: 200px 100%;
  }
  
  .dark .skeleton-wave {
    background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
    background-size: 200px 100%;
  }
  
  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    .skeleton-shimmer,
    .skeleton-wave,
    .skeleton-glow {
      animation: none;
      @apply bg-gray-200;
    }
    
    .dark .skeleton-shimmer,
    .dark .skeleton-wave,
    .dark .skeleton-glow {
      @apply bg-gray-800;
    }
    
    .skeleton {
      animation: none;
    }
  }
  
  /* Performance optimizations */
  .skeleton,
  .skeleton-shimmer,
  .skeleton-wave {
    will-change: auto;
    backface-visibility: hidden;
    perspective: 1000px;
  }
  
  /* Loading indicator positions */
  .loading-indicator {
    @apply fixed top-4 right-4 z-50;
  }
  
  .loading-overlay {
    @apply fixed inset-0 bg-black/20 backdrop-blur-sm z-40;
  }
  
  /* Skeleton layout spacing */
  .skeleton-layout > * + * {
    @apply mt-2 sm:mt-3;
  }
  
  .skeleton-grid {
    @apply grid gap-3 sm:gap-4;
  }
  
  .skeleton-flex {
    @apply flex items-center gap-3 sm:gap-4;
  }
  
  /* Progressive disclosure */
  .skeleton-progressive {
    animation: progressive-fade 0.3s ease-out forwards;
  }
  
  .skeleton-progressive:nth-child(1) { animation-delay: 0ms; }
  .skeleton-progressive:nth-child(2) { animation-delay: 100ms; }
  .skeleton-progressive:nth-child(3) { animation-delay: 200ms; }
  .skeleton-progressive:nth-child(4) { animation-delay: 300ms; }
  .skeleton-progressive:nth-child(5) { animation-delay: 400ms; }
  .skeleton-progressive:nth-child(6) { animation-delay: 500ms; }
}

/* Enhanced Accessibility Styles */
@layer utilities {
  /* Screen reader only content */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  /* Focus visible improvements */
  .focus-visible {
    outline: 3px solid var(--a11y-focus, #3b82f6);
    outline-offset: 2px;
    border-radius: 2px;
  }

  /* High contrast support */
  @media (prefers-contrast: high) {
    .high-contrast {
      background: var(--a11y-bg, #ffffff);
      color: var(--a11y-fg, #000000);
      border: 2px solid var(--a11y-border, #000000);
    }
    
    button, input, select, textarea {
      background: var(--a11y-bg, #ffffff) !important;
      color: var(--a11y-fg, #000000) !important;
      border: 2px solid var(--a11y-border, #000000) !important;
    }
    
    /* Remove subtle backgrounds in high contrast */
    .bg-gray-50, .bg-gray-100, .bg-slate-50, .bg-slate-100 {
      background: var(--a11y-bg, #ffffff) !important;
    }
    
    /* Ensure text is high contrast */
    .text-gray-500, .text-gray-600, .text-slate-500, .text-slate-600 {
      color: var(--a11y-fg, #000000) !important;
    }
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    *, *::before, *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
      scroll-behavior: auto !important;
    }
    
    /* Disable problematic animations */
    .animate-spin,
    .animate-pulse,
    .animate-bounce {
      animation: none !important;
    }
  }

  /* Touch accessibility */
  @media (pointer: coarse) {
    /* Ensure minimum touch targets */
    button, input, select, textarea, a {
      min-height: 44px;
      min-width: 44px;
    }
    
    /* Larger touch targets for small elements */
    .touch-target {
      padding: 12px;
    }
  }

  /* Font size scaling support */
  .text-scalable {
    font-size: var(--a11y-text-base, 1rem);
  }
  
  .text-scalable-sm {
    font-size: var(--a11y-text-sm, 0.875rem);
  }
  
  .text-scalable-lg {
    font-size: var(--a11y-text-lg, 1.125rem);
  }

  /* Reading mode support */
  body.reading-mode {
    font-size: 1.2em !important;
    line-height: 1.6 !important;
    letter-spacing: 0.1em !important;
  }
  
  body.reading-mode * {
    font-family: serif !important;
  }
  
  body.reading-mode p {
    margin-bottom: 1em !important;
  }

  /* Touch accessibility enabled */
  body.touch-accessibility-enabled {
    /* Touch feedback improvements */
    button, .touch-target {
      transition: transform 0.15s ease, opacity 0.15s ease;
    }
    
    button:active, .touch-target:active {
      transform: scale(0.98);
      opacity: 0.8;
    }
  }

  /* Error state accessibility */
  [aria-invalid="true"] {
    border-color: var(--a11y-error, #dc2626) !important;
    box-shadow: 0 0 0 2px color-mix(in srgb, var(--a11y-error, #dc2626) 20%, transparent);
  }

  /* Required field indicators */
  [aria-required="true"] {
    border-left: 4px solid var(--a11y-accent, #3b82f6);
  }

  /* Loading state accessibility */
  [aria-busy="true"] {
    position: relative;
    cursor: wait;
  }

  [aria-busy="true"]::after {
    content: "";
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid currentColor;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
  }

  @media (prefers-reduced-motion: reduce) {
    [aria-busy="true"]::after {
      animation: none;
      border: 2px solid currentColor;
    }
  }

  /* Focus management */
  .focus-trap {
    /* Focus trap container styling */
  }

  /* Live region styling */
  [aria-live] {
    /* Ensure live regions are positioned appropriately */
    position: absolute;
    left: -10000px;
    width: 1px;
    height: 1px;
    overflow: hidden;
  }

  /* Skip link for keyboard users */
  .skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--a11y-primary, #000000);
    color: var(--a11y-primary-foreground, #ffffff);
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 1000;
    transition: top 0.3s;
  }

  .skip-link:focus {
    top: 6px;
  }

  /* Keyboard navigation indicators */
  .keyboard-user *:focus {
    outline: 3px solid var(--a11y-focus, #3b82f6);
    outline-offset: 2px;
  }

  /* Voice navigation feedback */
  .voice-navigation-active {
    border: 2px dashed var(--a11y-accent, #3b82f6);
    animation: pulse 2s infinite;
  }

  @keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
  }

  /* Mobile accessibility controls */
  .touch-accessibility-controls {
    position: fixed;
    bottom: 1rem;
    right: 1rem;
    z-index: 50;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  @media (min-width: 768px) {
    .touch-accessibility-controls {
      display: none;
    }
  }

  /* Confirmation states */
  .confirm-action {
    border: 2px solid var(--a11y-warning, #f59e0b);
    box-shadow: 0 0 0 2px color-mix(in srgb, var(--a11y-warning, #f59e0b) 20%, transparent);
  }
}
