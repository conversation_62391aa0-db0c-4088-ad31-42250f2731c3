@import 'tailwindcss';
@import 'tw-animate-css';

/* Enhanced MVP Typography - Inter + Poppins */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&family=Poppins:wght@400;500;600;700;800;900&display=swap');

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-inter);
  --font-display: var(--font-poppins);
  --font-mono: var(--font-geist-mono);
  
  /* Typography Scale */
  --text-xs: var(--typography-xs);
  --text-sm: var(--typography-sm);
  --text-base: var(--typography-base);
  --text-lg: var(--typography-lg);
  --text-xl: var(--typography-xl);
  --text-2xl: var(--typography-2xl);
  --text-3xl: var(--typography-3xl);
  --text-4xl: var(--typography-4xl);
  --text-5xl: var(--typography-5xl);
  --text-6xl: var(--typography-6xl);
  
  /* Color System */
  --color-primary-50: var(--primary-50);
  --color-primary-100: var(--primary-100);
  --color-primary-200: var(--primary-200);
  --color-primary-300: var(--primary-300);
  --color-primary-400: var(--primary-400);
  --color-primary-500: var(--primary-500);
  --color-primary-600: var(--primary-600);
  --color-primary-700: var(--primary-700);
  --color-primary-800: var(--primary-800);
  --color-primary-900: var(--primary-900);
  --color-primary-950: var(--primary-950);
  
  --color-secondary-50: var(--secondary-50);
  --color-secondary-100: var(--secondary-100);
  --color-secondary-200: var(--secondary-200);
  --color-secondary-300: var(--secondary-300);
  --color-secondary-400: var(--secondary-400);
  --color-secondary-500: var(--secondary-500);
  --color-secondary-600: var(--secondary-600);
  --color-secondary-700: var(--secondary-700);
  --color-secondary-800: var(--secondary-800);
  --color-secondary-900: var(--secondary-900);
  --color-secondary-950: var(--secondary-950);
  
  /* Semantic Colors */
  --color-success-50: var(--success-50);
  --color-success-500: var(--success-500);
  --color-success-600: var(--success-600);
  --color-warning-50: var(--warning-50);
  --color-warning-500: var(--warning-500);
  --color-warning-600: var(--warning-600);
  --color-error-50: var(--error-50);
  --color-error-500: var(--error-500);
  --color-error-600: var(--error-600);
  --color-info-50: var(--info-50);
  --color-info-500: var(--info-500);
  --color-info-600: var(--info-600);
  
  /* Legacy Color Mappings */
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  
  /* Radius Scale */
  --radius-xs: calc(var(--radius) - 6px);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --radius-2xl: calc(var(--radius) + 8px);
  --radius-3xl: calc(var(--radius) + 12px);
}

:root {
  /* ===== ENHANCED MVP DESIGN SYSTEM ===== */
  
  /* Typography System */
  --font-inter: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --font-poppins: 'Poppins', 'Inter', system-ui, sans-serif;
  --font-mono: 'JetBrains Mono', 'Fira Code', monospace;
  
  /* Typography Scale - Mobile First with Fluid Scaling */
  --typography-xs: clamp(0.75rem, 0.7rem + 0.25vw, 0.875rem);      /* 12-14px */
  --typography-sm: clamp(0.875rem, 0.825rem + 0.25vw, 1rem);       /* 14-16px */
  --typography-base: clamp(1rem, 0.95rem + 0.25vw, 1.125rem);      /* 16-18px */
  --typography-lg: clamp(1.125rem, 1.05rem + 0.375vw, 1.25rem);    /* 18-20px */
  --typography-xl: clamp(1.25rem, 1.15rem + 0.5vw, 1.5rem);        /* 20-24px */
  --typography-2xl: clamp(1.5rem, 1.35rem + 0.75vw, 1.875rem);     /* 24-30px */
  --typography-3xl: clamp(1.875rem, 1.65rem + 1.125vw, 2.25rem);   /* 30-36px */
  --typography-4xl: clamp(2.25rem, 1.95rem + 1.5vw, 3rem);         /* 36-48px */
  --typography-5xl: clamp(3rem, 2.55rem + 2.25vw, 4rem);           /* 48-64px */
  --typography-6xl: clamp(4rem, 3.4rem + 3vw, 5.5rem);             /* 64-88px */
  
  /* Font Weight Scale */
  --font-thin: 100;
  --font-extralight: 200;
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  --font-extrabold: 800;
  --font-black: 900;
  
  /* Line Height Scale */
  --leading-none: 1;
  --leading-tight: 1.25;
  --leading-snug: 1.375;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;
  --leading-loose: 2;
  
  /* Letter Spacing Scale */
  --tracking-tighter: -0.05em;
  --tracking-tight: -0.025em;
  --tracking-normal: 0em;
  --tracking-wide: 0.025em;
  --tracking-wider: 0.05em;
  --tracking-widest: 0.1em;
  
  /* ===== COMPREHENSIVE COLOR SYSTEM ===== */
  
  /* Primary Brand Colors - Professional Blue */
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-300: #93c5fd;
  --primary-400: #60a5fa;
  --primary-500: #3b82f6;    /* Main brand color */
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-800: #1e40af;
  --primary-900: #1e3a8a;
  --primary-950: #172554;
  
  /* Secondary/Neutral Colors - Professional Gray */
  --secondary-50: #f8fafc;
  --secondary-100: #f1f5f9;
  --secondary-200: #e2e8f0;
  --secondary-300: #cbd5e1;
  --secondary-400: #94a3b8;
  --secondary-500: #64748b;
  --secondary-600: #475569;
  --secondary-700: #334155;
  --secondary-800: #1e293b;
  --secondary-900: #0f172a;
  --secondary-950: #020617;
  
  /* Semantic Color System */
  --success-50: #ecfdf5;
  --success-100: #d1fae5;
  --success-200: #a7f3d0;
  --success-300: #6ee7b7;
  --success-400: #34d399;
  --success-500: #10b981;
  --success-600: #059669;
  --success-700: #047857;
  --success-800: #065f46;
  --success-900: #064e3b;
  --success-950: #022c22;
  
  --warning-50: #fffbeb;
  --warning-100: #fef3c7;
  --warning-200: #fde68a;
  --warning-300: #fcd34d;
  --warning-400: #fbbf24;
  --warning-500: #f59e0b;
  --warning-600: #d97706;
  --warning-700: #b45309;
  --warning-800: #92400e;
  --warning-900: #78350f;
  --warning-950: #451a03;
  
  --error-50: #fef2f2;
  --error-100: #fee2e2;
  --error-200: #fecaca;
  --error-300: #fca5a5;
  --error-400: #f87171;
  --error-500: #ef4444;
  --error-600: #dc2626;
  --error-700: #b91c1c;
  --error-800: #991b1b;
  --error-900: #7f1d1d;
  --error-950: #450a0a;
  
  --info-50: #eff6ff;
  --info-100: #dbeafe;
  --info-200: #bfdbfe;
  --info-300: #93c5fd;
  --info-400: #60a5fa;
  --info-500: #3b82f6;
  --info-600: #2563eb;
  --info-700: #1d4ed8;
  --info-800: #1e40af;
  --info-900: #1e3a8a;
  --info-950: #172554;
  
  /* Accent Colors for Premium Feel */
  --accent-50: #faf5ff;
  --accent-100: #f3e8ff;
  --accent-200: #e9d5ff;
  --accent-300: #d8b4fe;
  --accent-400: #c084fc;
  --accent-500: #a855f7;
  --accent-600: #9333ea;
  --accent-700: #7c3aed;
  --accent-800: #6b21a8;
  --accent-900: #581c87;
  --accent-950: #3b0764;
  
  /* Spacing Scale */
  --spacing-0: 0;
  --spacing-px: 1px;
  --spacing-0-5: 0.125rem;    /* 2px */
  --spacing-1: 0.25rem;       /* 4px */
  --spacing-1-5: 0.375rem;    /* 6px */
  --spacing-2: 0.5rem;        /* 8px */
  --spacing-2-5: 0.625rem;    /* 10px */
  --spacing-3: 0.75rem;       /* 12px */
  --spacing-3-5: 0.875rem;    /* 14px */
  --spacing-4: 1rem;          /* 16px */
  --spacing-5: 1.25rem;       /* 20px */
  --spacing-6: 1.5rem;        /* 24px */
  --spacing-7: 1.75rem;       /* 28px */
  --spacing-8: 2rem;          /* 32px */
  --spacing-9: 2.25rem;       /* 36px */
  --spacing-10: 2.5rem;       /* 40px */
  --spacing-11: 2.75rem;      /* 44px */
  --spacing-12: 3rem;         /* 48px */
  --spacing-14: 3.5rem;       /* 56px */
  --spacing-16: 4rem;         /* 64px */
  --spacing-20: 5rem;         /* 80px */
  --spacing-24: 6rem;         /* 96px */
  --spacing-28: 7rem;         /* 112px */
  --spacing-32: 8rem;         /* 128px */
  --spacing-36: 9rem;         /* 144px */
  --spacing-40: 10rem;        /* 160px */
  --spacing-44: 11rem;        /* 176px */
  --spacing-48: 12rem;        /* 192px */
  --spacing-52: 13rem;        /* 208px */
  --spacing-56: 14rem;        /* 224px */
  --spacing-60: 15rem;        /* 240px */
  --spacing-64: 16rem;        /* 256px */
  --spacing-72: 18rem;        /* 288px */
  --spacing-80: 20rem;        /* 320px */
  --spacing-96: 24rem;        /* 384px */
  
  /* Professional Shadow System */
  --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgb(0 0 0 / 0.05);
  
  /* Animation Variables */
  --animation-fast: 150ms ease-out;
  --animation-normal: 300ms ease-out;
  --animation-slow: 500ms ease-out;
  --animation-bounce: 600ms cubic-bezier(0.68, -0.55, 0.265, 1.55);
  
  /* Border Radius Scale */
  --radius: 0.625rem;
  --radius-none: 0;
  --radius-sm: 0.25rem;       /* 4px */
  --radius-base: 0.375rem;    /* 6px */
  --radius-md: 0.5rem;        /* 8px */
  --radius-lg: 0.625rem;      /* 10px */
  --radius-xl: 0.75rem;       /* 12px */
  --radius-2xl: 1rem;         /* 16px */
  --radius-3xl: 1.5rem;       /* 24px */
  --radius-full: 9999px;
  
  /* Z-Index Scale */
  --z-0: 0;
  --z-10: 10;
  --z-20: 20;
  --z-30: 30;
  --z-40: 40;
  --z-50: 50;
  --z-auto: auto;
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
  
  /* ===== LIGHT THEME COLORS ===== */
  --background: var(--secondary-50);
  --foreground: var(--secondary-900);
  --card: #ffffff;
  --card-foreground: var(--secondary-900);
  --popover: #ffffff;
  --popover-foreground: var(--secondary-900);
  --primary: var(--primary-600);
  --primary-foreground: #ffffff;
  --secondary: var(--secondary-100);
  --secondary-foreground: var(--secondary-900);
  --muted: var(--secondary-100);
  --muted-foreground: var(--secondary-500);
  --accent: var(--accent-500);
  --accent-foreground: #ffffff;
  --destructive: var(--error-500);
  --destructive-foreground: #ffffff;
  --border: var(--secondary-200);
  --input: var(--secondary-200);
  --ring: var(--primary-500);
  
  /* Chart Colors */
  --chart-1: var(--primary-500);
  --chart-2: var(--success-500);
  --chart-3: var(--warning-500);
  --chart-4: var(--accent-500);
  --chart-5: var(--error-500);
  
  /* Sidebar Colors */
  --sidebar: #ffffff;
  --sidebar-foreground: var(--secondary-900);
  --sidebar-primary: var(--primary-600);
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: var(--secondary-100);
  --sidebar-accent-foreground: var(--secondary-900);
  --sidebar-border: var(--secondary-200);
  --sidebar-ring: var(--primary-500);
}

/* ===== DARK THEME ===== */
.dark {
  --background: var(--secondary-950);
  --foreground: var(--secondary-50);
  --card: var(--secondary-900);
  --card-foreground: var(--secondary-50);
  --popover: var(--secondary-900);
  --popover-foreground: var(--secondary-50);
  --primary: var(--primary-400);
  --primary-foreground: var(--secondary-900);
  --secondary: var(--secondary-800);
  --secondary-foreground: var(--secondary-50);
  --muted: var(--secondary-800);
  --muted-foreground: var(--secondary-400);
  --accent: var(--accent-400);
  --accent-foreground: var(--secondary-900);
  --destructive: var(--error-400);
  --destructive-foreground: var(--secondary-900);
  --border: var(--secondary-800);
  --input: var(--secondary-800);
  --ring: var(--primary-400);
  
  /* Dark Chart Colors */
  --chart-1: var(--primary-400);
  --chart-2: var(--success-400);
  --chart-3: var(--warning-400);
  --chart-4: var(--accent-400);
  --chart-5: var(--error-400);
  
  /* Dark Sidebar Colors */
  --sidebar: var(--secondary-900);
  --sidebar-foreground: var(--secondary-50);
  --sidebar-primary: var(--primary-400);
  --sidebar-primary-foreground: var(--secondary-900);
  --sidebar-accent: var(--secondary-800);
  --sidebar-accent-foreground: var(--secondary-50);
  --sidebar-border: var(--secondary-800);
  --sidebar-ring: var(--primary-400);
}

/* ===== BASE LAYER STYLES ===== */
@layer base {
  * {
    @apply border-border;
    box-sizing: border-box;
  }
  
  *::before,
  *::after {
    box-sizing: border-box;
  }
  
  /* Root HTML Element */
  html {
    line-height: 1.15;
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: transparent;
    scroll-behavior: smooth;
  }
  
  /* Body Styling */
  body {
    margin: 0;
    font-family: var(--font-inter);
    font-size: var(--typography-base);
    line-height: var(--leading-normal);
    font-weight: var(--font-normal);
    color: var(--foreground);
    background-color: var(--background);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    font-feature-settings: 'kern' 1, 'liga' 1, 'calt' 1;
  }
  
  /* Typography Hierarchy */
  h1, h2, h3, h4, h5, h6 {
    margin: 0;
    font-family: var(--font-poppins);
    font-weight: var(--font-semibold);
    line-height: var(--leading-tight);
    letter-spacing: var(--tracking-tight);
    color: var(--foreground);
  }
  
  h1 {
    font-size: var(--typography-4xl);
    font-weight: var(--font-bold);
    line-height: var(--leading-none);
  }
  
  h2 {
    font-size: var(--typography-3xl);
    font-weight: var(--font-semibold);
  }
  
  h3 {
    font-size: var(--typography-2xl);
    font-weight: var(--font-semibold);
  }
  
  h4 {
    font-size: var(--typography-xl);
    font-weight: var(--font-medium);
  }
  
  h5 {
    font-size: var(--typography-lg);
    font-weight: var(--font-medium);
  }
  
  h6 {
    font-size: var(--typography-base);
    font-weight: var(--font-medium);
  }
  
  /* Paragraph and Text Elements */
  p {
    margin: 0 0 var(--spacing-4) 0;
    font-size: var(--typography-base);
    line-height: var(--leading-relaxed);
    color: var(--muted-foreground);
  }
  
  .lead {
    font-size: var(--typography-xl);
    line-height: var(--leading-relaxed);
    color: var(--muted-foreground);
  }
  
  .small {
    font-size: var(--typography-sm);
    line-height: var(--leading-normal);
    color: var(--muted-foreground);
  }
  
  .muted {
    font-size: var(--typography-sm);
    color: var(--muted-foreground);
  }
  
  /* Interactive Elements */
  a {
    color: var(--primary);
    text-decoration: none;
    transition: color var(--animation-fast);
  }
  
  a:hover {
    color: var(--primary-600);
    text-decoration: underline;
  }
  
  a:focus-visible {
    outline: 2px solid var(--ring);
    outline-offset: 2px;
    border-radius: var(--radius-sm);
  }
  
  /* Form Elements */
  button,
  input,
  optgroup,
  select,
  textarea {
    font-family: inherit;
    font-size: 100%;
    line-height: 1.15;
    margin: 0;
  }
  
  button,
  [type="button"],
  [type="reset"],
  [type="submit"] {
    -webkit-appearance: button;
    cursor: pointer;
  }
  
  button:disabled,
  [type="button"]:disabled,
  [type="reset"]:disabled,
  [type="submit"]:disabled {
    cursor: default;
    opacity: 0.6;
  }
  
  input,
  textarea {
    background-color: var(--background);
    border: 1px solid var(--border);
    border-radius: var(--radius-md);
    padding: var(--spacing-2) var(--spacing-3);
    font-size: var(--typography-sm);
    line-height: var(--leading-normal);
    color: var(--foreground);
    transition: border-color var(--animation-fast), box-shadow var(--animation-fast);
  }
  
  input:focus,
  textarea:focus {
    outline: none;
    border-color: var(--ring);
    box-shadow: 0 0 0 3px rgb(from var(--ring) r g b / 0.1);
  }
  
  /* Lists */
  ul, ol {
    margin: 0 0 var(--spacing-4) 0;
    padding-left: var(--spacing-6);
  }
  
  li {
    margin-bottom: var(--spacing-1);
  }
  
  /* Code Elements */
  code {
    font-family: var(--font-mono);
    font-size: var(--typography-sm);
    background-color: var(--muted);
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--radius-sm);
    color: var(--foreground);
  }
  
  pre {
    font-family: var(--font-mono);
    background-color: var(--muted);
    padding: var(--spacing-4);
    border-radius: var(--radius-lg);
    overflow-x: auto;
    font-size: var(--typography-sm);
    line-height: var(--leading-relaxed);
  }
  
  pre code {
    background-color: transparent;
    padding: 0;
    border-radius: 0;
  }
  
  /* Tables */
  table {
    border-collapse: collapse;
    width: 100%;
    margin-bottom: var(--spacing-4);
  }
  
  th,
  td {
    text-align: left;
    padding: var(--spacing-3);
    border-bottom: 1px solid var(--border);
  }
  
  th {
    font-weight: var(--font-semibold);
    color: var(--foreground);
    background-color: var(--muted);
  }
  
  /* Media Elements */
  img,
  svg {
    max-width: 100%;
    height: auto;
  }
  
  /* Selection */
  ::selection {
    background-color: rgb(from var(--primary) r g b / 0.2);
    color: var(--foreground);
  }
  
  /* Scrollbars */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  ::-webkit-scrollbar-track {
    background: var(--muted);
  }
  
  ::-webkit-scrollbar-thumb {
    background: var(--muted-foreground);
    border-radius: var(--radius-full);
  }
  
  ::-webkit-scrollbar-thumb:hover {
    background: var(--foreground);
  }
  
  /* Smooth Transitions */
  *, *::before, *::after {
    transition: 
      color var(--animation-fast),
      background-color var(--animation-fast),
      border-color var(--animation-fast),
      box-shadow var(--animation-fast),
      transform var(--animation-fast),
      opacity var(--animation-fast);
  }
}

/* ===== COMPONENT UTILITIES ===== */
@layer components {
  /* Typography Utilities */
  .heading-1 {
    font-family: var(--font-poppins);
    font-size: var(--typography-5xl);
    font-weight: var(--font-bold);
    line-height: var(--leading-none);
    letter-spacing: var(--tracking-tight);
    color: var(--foreground);
  }
  
  .heading-2 {
    font-family: var(--font-poppins);
    font-size: var(--typography-4xl);
    font-weight: var(--font-bold);
    line-height: var(--leading-tight);
    letter-spacing: var(--tracking-tight);
    color: var(--foreground);
  }
  
  .heading-3 {
    font-family: var(--font-poppins);
    font-size: var(--typography-3xl);
    font-weight: var(--font-semibold);
    line-height: var(--leading-tight);
    color: var(--foreground);
  }
  
  .heading-4 {
    font-family: var(--font-poppins);
    font-size: var(--typography-2xl);
    font-weight: var(--font-semibold);
    line-height: var(--leading-snug);
    color: var(--foreground);
  }
  
  .heading-5 {
    font-family: var(--font-poppins);
    font-size: var(--typography-xl);
    font-weight: var(--font-medium);
    line-height: var(--leading-snug);
    color: var(--foreground);
  }
  
  .heading-6 {
    font-family: var(--font-poppins);
    font-size: var(--typography-lg);
    font-weight: var(--font-medium);
    line-height: var(--leading-normal);
    color: var(--foreground);
  }
  
  .body-large {
    font-size: var(--typography-lg);
    line-height: var(--leading-relaxed);
    color: var(--foreground);
  }
  
  .body-normal {
    font-size: var(--typography-base);
    line-height: var(--leading-relaxed);
    color: var(--foreground);
  }
  
  .body-small {
    font-size: var(--typography-sm);
    line-height: var(--leading-normal);
    color: var(--muted-foreground);
  }
  
  .caption {
    font-size: var(--typography-xs);
    line-height: var(--leading-normal);
    color: var(--muted-foreground);
    letter-spacing: var(--tracking-wide);
  }
  
  /* Text Color Utilities */
  .text-primary { color: var(--primary); }
  .text-secondary { color: var(--secondary-500); }
  .text-success { color: var(--success-500); }
  .text-warning { color: var(--warning-500); }
  .text-error { color: var(--error-500); }
  .text-info { color: var(--info-500); }
  .text-accent { color: var(--accent-500); }
  .text-muted { color: var(--muted-foreground); }
  .text-foreground { color: var(--foreground); }
  
  /* Background Color Utilities */
  .bg-primary { background-color: var(--primary); }
  .bg-primary-50 { background-color: var(--primary-50); }
  .bg-primary-100 { background-color: var(--primary-100); }
  .bg-secondary { background-color: var(--secondary); }
  .bg-success { background-color: var(--success-500); }
  .bg-success-50 { background-color: var(--success-50); }
  .bg-warning { background-color: var(--warning-500); }
  .bg-warning-50 { background-color: var(--warning-50); }
  .bg-error { background-color: var(--error-500); }
  .bg-error-50 { background-color: var(--error-50); }
  .bg-accent { background-color: var(--accent-500); }
  .bg-accent-50 { background-color: var(--accent-50); }
  
  /* Border Color Utilities */
  .border-primary { border-color: var(--primary); }
  .border-secondary { border-color: var(--secondary-200); }
  .border-success { border-color: var(--success-500); }
  .border-warning { border-color: var(--warning-500); }
  .border-error { border-color: var(--error-500); }
  .border-accent { border-color: var(--accent-500); }
  
  /* Professional Gradients */
  .gradient-primary {
    background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
  }
  
  .gradient-secondary {
    background: linear-gradient(135deg, var(--secondary-100) 0%, var(--secondary-200) 100%);
  }
  
  .gradient-success {
    background: linear-gradient(135deg, var(--success-500) 0%, var(--success-600) 100%);
  }
  
  .gradient-accent {
    background: linear-gradient(135deg, var(--accent-500) 0%, var(--accent-600) 100%);
  }
  
  .text-gradient-primary {
    background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-800) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .text-gradient-accent {
    background: linear-gradient(135deg, var(--accent-500) 0%, var(--accent-700) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  /* Glass Morphism */
  .glass {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }
  
  .glass-dark {
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  /* Interactive Elements */
  .interactive {
    cursor: pointer;
    transition: all var(--animation-fast);
  }
  
  .interactive:hover {
    transform: translateY(-1px);
  }
  
  .interactive:active {
    transform: translateY(0);
  }
  
  /* Focus Ring */
  .focus-ring {
    outline: 2px solid var(--ring);
    outline-offset: 2px;
  }
  
  /* Elevation Shadows */
  .elevation-1 { box-shadow: var(--shadow-sm); }
  .elevation-2 { box-shadow: var(--shadow-md); }
  .elevation-3 { box-shadow: var(--shadow-lg); }
  .elevation-4 { box-shadow: var(--shadow-xl); }
  .elevation-5 { box-shadow: var(--shadow-2xl); }
}

/* ===== ACCESSIBILITY UTILITIES ===== */
@layer utilities {
  /* Screen Reader Only */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }
  
  /* Focus Visible */
  .focus-visible:focus-visible {
    outline: 2px solid var(--ring);
    outline-offset: 2px;
    border-radius: var(--radius-sm);
  }
  
  /* High Contrast Mode Support */
  @media (prefers-contrast: high) {
    .high-contrast {
      background: var(--background);
      color: var(--foreground);
      border: 2px solid var(--foreground);
    }
    
    button, input, select, textarea {
      border: 2px solid var(--foreground) !important;
    }
    
    .text-muted {
      color: var(--foreground) !important;
    }
  }
  
  /* Reduced Motion Support */
  @media (prefers-reduced-motion: reduce) {
    *, *::before, *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
      scroll-behavior: auto !important;
    }
    
    .interactive:hover {
      transform: none;
    }
    
    .interactive:active {
      transform: none;
    }
  }
  
  /* Touch Accessibility */
  @media (pointer: coarse) {
    button, input, select, textarea, a {
      min-height: 44px;
      min-width: 44px;
    }
    
    .touch-target {
      padding: var(--spacing-3);
    }
  }
  
  /* Skip Link */
  .skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--foreground);
    color: var(--background);
    padding: var(--spacing-2) var(--spacing-3);
    text-decoration: none;
    border-radius: var(--radius-md);
    z-index: var(--z-modal);
    transition: top var(--animation-normal);
  }
  
  .skip-link:focus {
    top: 6px;
  }
  
  /* Loading States */
  [aria-busy="true"] {
    cursor: wait;
    opacity: 0.7;
  }
  
  /* Required Fields */
  [aria-required="true"] {
    border-left: 4px solid var(--primary);
  }
  
  /* Error States */
  [aria-invalid="true"] {
    border-color: var(--error-500);
    box-shadow: 0 0 0 2px rgb(from var(--error-500) r g b / 0.2);
  }
}

/* ===== RESPONSIVE TYPOGRAPHY ===== */
@media (max-width: 640px) {
  :root {
    --typography-4xl: 2rem;
    --typography-3xl: 1.75rem;
    --typography-2xl: 1.5rem;
  }
}

@media (min-width: 1024px) {
  :root {
    --typography-6xl: 6rem;
    --typography-5xl: 4.5rem;
    --typography-4xl: 3.5rem;
  }
}

/* ===== PRINT STYLES ===== */
@media print {
  * {
    -webkit-print-color-adjust: exact !important;
    color-adjust: exact !important;
  }
  
  body {
    font-size: 12pt;
    line-height: 1.5;
  }
  
  h1, h2, h3, h4, h5, h6 {
    page-break-after: avoid;
  }
  
  p, li {
    page-break-inside: avoid;
  }
  
  .no-print {
    display: none !important;
  }
}