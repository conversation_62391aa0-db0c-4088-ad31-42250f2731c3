# API Testing Guide - Nirmaan AI Construction Calculator

## Quick Start Testing

### 1. Health Check (No Auth Required)
```bash
curl -X GET "http://localhost:3000/api/health" | jq
```

**Expected Response:**
```json
{
  "status": "healthy",
  "timestamp": "2025-01-15T10:30:00.000Z",
  "version": "1.0.0",
  "environment": "development",
  "uptime": 1234,
  "checks": {
    "database": true,
    "api": true,
    "calculations": true,
    "memory": {
      "used": 128,
      "total": 512,
      "percentage": 25
    }
  }
}
```

### 2. Basic Cost Calculation
```bash
curl -X POST "http://localhost:3000/api/calculate" \
  -H "Content-Type: application/json" \
  -d '{
    "builtUpArea": 1200,
    "floors": 1,
    "qualityTier": "premium",
    "location": "bangalore"
  }' | jq
```

### 3. Advanced Calculation with All Features
```bash
curl -X POST "http://localhost:3000/api/calculate" \
  -H "Content-Type: application/json" \
  -d '{
    "builtUpArea": 2000,
    "floors": 2,
    "qualityTier": "luxury",
    "location": "mumbai",
    "plotArea": 2500,
    "hasStilt": true,
    "parkingType": "covered",
    "hasBasement": true,
    "specialFeatures": [
      {
        "name": "Swimming Pool",
        "cost": 500000,
        "description": "Premium swimming pool with filtration"
      },
      {
        "name": "Home Theater",
        "cost": 300000,
        "description": "7.1 surround sound system"
      }
    ]
  }' | jq
```

## Complete Test Suite

### Test Script (Bash)
```bash
#!/bin/bash

# API Testing Script for Nirmaan AI Calculator
BASE_URL="http://localhost:3000/api"

echo "🚀 Starting API Test Suite..."
echo "================================"

# Test 1: Health Check
echo "1. Testing Health Check..."
response=$(curl -s -w "%{http_code}" -o response.json "$BASE_URL/health")
if [ "$response" = "200" ]; then
    echo "✅ Health check passed"
    cat response.json | jq .status
else
    echo "❌ Health check failed with status: $response"
fi
echo ""

# Test 2: API Documentation
echo "2. Testing API Documentation..."
response=$(curl -s -w "%{http_code}" -o response.json "$BASE_URL/calculate")
if [ "$response" = "200" ]; then
    echo "✅ API documentation accessible"
    cat response.json | jq .name
else
    echo "❌ API documentation failed with status: $response"
fi
echo ""

# Test 3: Basic Calculation
echo "3. Testing Basic Calculation..."
response=$(curl -s -w "%{http_code}" -o response.json -X POST "$BASE_URL/calculate" \
  -H "Content-Type: application/json" \
  -d '{
    "builtUpArea": 1000,
    "floors": 1,
    "qualityTier": "smart",
    "location": "bangalore"
  }')
if [ "$response" = "200" ]; then
    echo "✅ Basic calculation successful"
    cat response.json | jq '.data.totalCost, .data.costPerSqft'
else
    echo "❌ Basic calculation failed with status: $response"
    cat response.json | jq .error
fi
echo ""

# Test 4: Premium Calculation
echo "4. Testing Premium Calculation..."
response=$(curl -s -w "%{http_code}" -o response.json -X POST "$BASE_URL/calculate" \
  -H "Content-Type: application/json" \
  -d '{
    "builtUpArea": 1500,
    "floors": 2,
    "qualityTier": "premium",
    "location": "mumbai",
    "hasStilt": true,
    "parkingType": "covered"
  }')
if [ "$response" = "200" ]; then
    echo "✅ Premium calculation successful"
    cat response.json | jq '.data.totalCost, .data.costPerSqft'
else
    echo "❌ Premium calculation failed with status: $response"
    cat response.json | jq .error
fi
echo ""

# Test 5: Luxury with Special Features
echo "5. Testing Luxury with Special Features..."
response=$(curl -s -w "%{http_code}" -o response.json -X POST "$BASE_URL/calculate" \
  -H "Content-Type: application/json" \
  -d '{
    "builtUpArea": 2500,
    "floors": 3,
    "qualityTier": "luxury",
    "location": "delhi",
    "hasBasement": true,
    "specialFeatures": [
      {
        "name": "Swimming Pool",
        "cost": 800000,
        "description": "Olympic size pool"
      }
    ]
  }')
if [ "$response" = "200" ]; then
    echo "✅ Luxury calculation successful"
    cat response.json | jq '.data.totalCost, .data.costPerSqft'
else
    echo "❌ Luxury calculation failed with status: $response"
    cat response.json | jq .error
fi
echo ""

# Test 6: Validation Error
echo "6. Testing Validation Error..."
response=$(curl -s -w "%{http_code}" -o response.json -X POST "$BASE_URL/calculate" \
  -H "Content-Type: application/json" \
  -d '{
    "builtUpArea": "invalid",
    "qualityTier": "premium"
  }')
if [ "$response" = "400" ]; then
    echo "✅ Validation error handled correctly"
    cat response.json | jq '.error.message'
else
    echo "❌ Validation error not handled correctly: $response"
fi
echo ""

# Test 7: Rate Limiting Test
echo "7. Testing Rate Limiting..."
for i in {1..5}; do
    response=$(curl -s -w "%{http_code}" -o /dev/null -X POST "$BASE_URL/calculate" \
      -H "Content-Type: application/json" \
      -d '{
        "builtUpArea": 1000,
        "floors": 1,
        "qualityTier": "smart",
        "location": "bangalore"
      }')
    echo "Request $i: HTTP $response"
done
echo ""

# Test 8: Monitoring Endpoint
echo "8. Testing Monitoring Endpoint..."
response=$(curl -s -w "%{http_code}" -o response.json "$BASE_URL/monitoring")
if [ "$response" = "200" ]; then
    echo "✅ Monitoring endpoint accessible"
    cat response.json | jq '.metrics.requests.total, .metrics.performance.responseTime.avg'
else
    echo "❌ Monitoring endpoint failed with status: $response"
fi
echo ""

# Test 9: Performance Metrics
echo "9. Testing Performance Metrics..."
response=$(curl -s -w "%{http_code}" -o response.json -X POST "$BASE_URL/performance/metrics" \
  -H "Content-Type: application/json" \
  -d '{
    "metric": {
      "name": "LCP",
      "value": 1234,
      "rating": "good",
      "url": "http://localhost:3000/calculator"
    }
  }')
if [ "$response" = "200" ]; then
    echo "✅ Performance metrics submission successful"
    cat response.json | jq .status
else
    echo "❌ Performance metrics failed with status: $response"
fi
echo ""

# Test 10: Web Vitals Analytics
echo "10. Testing Web Vitals Analytics..."
response=$(curl -s -w "%{http_code}" -o response.json -X POST "$BASE_URL/analytics/web-vitals" \
  -H "Content-Type: application/json" \
  -d '{
    "metrics": [
      {
        "metric": "LCP",
        "value": 1500,
        "rating": "good",
        "url": "http://localhost:3000/calculator",
        "timestamp": '$(date +%s000)',
        "deviceType": "desktop"
      }
    ]
  }')
if [ "$response" = "200" ]; then
    echo "✅ Web vitals analytics submission successful"
    cat response.json | jq .status
else
    echo "❌ Web vitals analytics failed with status: $response"
fi
echo ""

echo "🎉 Test Suite Complete!"
echo "========================"

# Cleanup
rm -f response.json
```

### Python Test Suite
```python
#!/usr/bin/env python3

import requests
import json
import time
from typing import Dict, Any

class APITester:
    def __init__(self, base_url: str = "http://localhost:3000/api"):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'API-Tester/1.0'
        })
        
    def test_health_check(self) -> bool:
        """Test health check endpoint"""
        try:
            response = self.session.get(f"{self.base_url}/health")
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Health Check: {data['status']}")
                return True
            else:
                print(f"❌ Health Check failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Health Check error: {e}")
            return False
    
    def test_calculation(self, test_data: Dict[str, Any], test_name: str) -> bool:
        """Test calculation endpoint"""
        try:
            response = self.session.post(
                f"{self.base_url}/calculate",
                json=test_data
            )
            
            if response.status_code == 200:
                data = response.json()
                if data['success']:
                    print(f"✅ {test_name}: ₹{data['data']['totalCost']:,} (₹{data['data']['costPerSqft']}/sqft)")
                    return True
                else:
                    print(f"❌ {test_name}: {data['error']['message']}")
                    return False
            else:
                print(f"❌ {test_name} failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ {test_name} error: {e}")
            return False
    
    def test_monitoring(self) -> bool:
        """Test monitoring endpoint"""
        try:
            response = self.session.get(f"{self.base_url}/monitoring")
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Monitoring: {data['metrics']['requests']['total']} requests")
                return True
            else:
                print(f"❌ Monitoring failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Monitoring error: {e}")
            return False
    
    def test_performance_metrics(self) -> bool:
        """Test performance metrics endpoint"""
        try:
            response = self.session.post(
                f"{self.base_url}/performance/metrics",
                json={
                    "metric": {
                        "name": "LCP",
                        "value": 1234,
                        "rating": "good",
                        "url": "http://localhost:3000/calculator"
                    }
                }
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Performance Metrics: {data['status']}")
                return True
            else:
                print(f"❌ Performance Metrics failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Performance Metrics error: {e}")
            return False
    
    def test_rate_limiting(self) -> bool:
        """Test rate limiting"""
        print("Testing rate limiting (may take a moment)...")
        
        test_data = {
            "builtUpArea": 1000,
            "floors": 1,
            "qualityTier": "smart",
            "location": "bangalore"
        }
        
        success_count = 0
        rate_limited = False
        
        for i in range(10):
            try:
                response = self.session.post(
                    f"{self.base_url}/calculate",
                    json=test_data
                )
                
                if response.status_code == 200:
                    success_count += 1
                elif response.status_code == 429:
                    rate_limited = True
                    print(f"✅ Rate limiting triggered at request {i+1}")
                    break
                    
                time.sleep(0.1)  # Small delay between requests
                
            except Exception as e:
                print(f"Request {i+1} error: {e}")
        
        if rate_limited:
            return True
        else:
            print(f"⚠️  Rate limiting not triggered ({success_count} successful requests)")
            return False
    
    def run_all_tests(self):
        """Run all tests"""
        print("🚀 Starting API Test Suite...")
        print("=" * 50)
        
        tests = [
            ("Health Check", self.test_health_check),
            ("Smart Calculation", lambda: self.test_calculation({
                "builtUpArea": 1000,
                "floors": 1,
                "qualityTier": "smart",
                "location": "bangalore"
            }, "Smart Calculation")),
            ("Premium Calculation", lambda: self.test_calculation({
                "builtUpArea": 1500,
                "floors": 2,
                "qualityTier": "premium",
                "location": "mumbai",
                "hasStilt": True,
                "parkingType": "covered"
            }, "Premium Calculation")),
            ("Luxury with Features", lambda: self.test_calculation({
                "builtUpArea": 2500,
                "floors": 3,
                "qualityTier": "luxury",
                "location": "delhi",
                "hasBasement": True,
                "specialFeatures": [
                    {
                        "name": "Swimming Pool",
                        "cost": 800000,
                        "description": "Olympic size pool"
                    }
                ]
            }, "Luxury with Features")),
            ("Monitoring", self.test_monitoring),
            ("Performance Metrics", self.test_performance_metrics),
            ("Rate Limiting", self.test_rate_limiting)
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            print(f"\n{test_name}:")
            if test_func():
                passed += 1
        
        print(f"\n🎉 Test Suite Complete!")
        print(f"Results: {passed}/{total} tests passed")
        print("=" * 50)

if __name__ == "__main__":
    tester = APITester()
    tester.run_all_tests()
```

### JavaScript/Node.js Test Suite
```javascript
const axios = require('axios');

class APITester {
    constructor(baseURL = 'http://localhost:3000/api') {
        this.baseURL = baseURL;
        this.client = axios.create({
            baseURL: this.baseURL,
            timeout: 10000,
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'API-Tester/1.0'
            }
        });
    }

    async testHealthCheck() {
        try {
            const response = await this.client.get('/health');
            console.log(`✅ Health Check: ${response.data.status}`);
            return true;
        } catch (error) {
            console.log(`❌ Health Check failed: ${error.message}`);
            return false;
        }
    }

    async testCalculation(testData, testName) {
        try {
            const response = await this.client.post('/calculate', testData);
            
            if (response.data.success) {
                const { totalCost, costPerSqft } = response.data.data;
                console.log(`✅ ${testName}: ₹${totalCost.toLocaleString()} (₹${costPerSqft}/sqft)`);
                return true;
            } else {
                console.log(`❌ ${testName}: ${response.data.error.message}`);
                return false;
            }
        } catch (error) {
            console.log(`❌ ${testName} error: ${error.message}`);
            return false;
        }
    }

    async testMonitoring() {
        try {
            const response = await this.client.get('/monitoring');
            console.log(`✅ Monitoring: ${response.data.metrics.requests.total} requests`);
            return true;
        } catch (error) {
            console.log(`❌ Monitoring failed: ${error.message}`);
            return false;
        }
    }

    async testPerformanceMetrics() {
        try {
            const response = await this.client.post('/performance/metrics', {
                metric: {
                    name: 'LCP',
                    value: 1234,
                    rating: 'good',
                    url: 'http://localhost:3000/calculator'
                }
            });
            
            console.log(`✅ Performance Metrics: ${response.data.status}`);
            return true;
        } catch (error) {
            console.log(`❌ Performance Metrics failed: ${error.message}`);
            return false;
        }
    }

    async testRateLimiting() {
        console.log('Testing rate limiting...');
        
        const testData = {
            builtUpArea: 1000,
            floors: 1,
            qualityTier: 'smart',
            location: 'bangalore'
        };

        let successCount = 0;
        let rateLimited = false;

        for (let i = 0; i < 10; i++) {
            try {
                const response = await this.client.post('/calculate', testData);
                if (response.status === 200) {
                    successCount++;
                }
            } catch (error) {
                if (error.response?.status === 429) {
                    rateLimited = true;
                    console.log(`✅ Rate limiting triggered at request ${i + 1}`);
                    break;
                }
            }
            
            await new Promise(resolve => setTimeout(resolve, 100));
        }

        if (rateLimited) {
            return true;
        } else {
            console.log(`⚠️  Rate limiting not triggered (${successCount} successful requests)`);
            return false;
        }
    }

    async runAllTests() {
        console.log('🚀 Starting API Test Suite...');
        console.log('='.repeat(50));

        const tests = [
            ['Health Check', () => this.testHealthCheck()],
            ['Smart Calculation', () => this.testCalculation({
                builtUpArea: 1000,
                floors: 1,
                qualityTier: 'smart',
                location: 'bangalore'
            }, 'Smart Calculation')],
            ['Premium Calculation', () => this.testCalculation({
                builtUpArea: 1500,
                floors: 2,
                qualityTier: 'premium',
                location: 'mumbai',
                hasStilt: true,
                parkingType: 'covered'
            }, 'Premium Calculation')],
            ['Luxury with Features', () => this.testCalculation({
                builtUpArea: 2500,
                floors: 3,
                qualityTier: 'luxury',
                location: 'delhi',
                hasBasement: true,
                specialFeatures: [
                    {
                        name: 'Swimming Pool',
                        cost: 800000,
                        description: 'Olympic size pool'
                    }
                ]
            }, 'Luxury with Features')],
            ['Monitoring', () => this.testMonitoring()],
            ['Performance Metrics', () => this.testPerformanceMetrics()],
            ['Rate Limiting', () => this.testRateLimiting()]
        ];

        let passed = 0;
        const total = tests.length;

        for (const [testName, testFunc] of tests) {
            console.log(`\n${testName}:`);
            if (await testFunc()) {
                passed++;
            }
        }

        console.log(`\n🎉 Test Suite Complete!`);
        console.log(`Results: ${passed}/${total} tests passed`);
        console.log('='.repeat(50));
    }
}

// Run tests
const tester = new APITester();
tester.runAllTests().catch(console.error);
```

### Postman Collection

```json
{
  "info": {
    "name": "Nirmaan AI Calculator API",
    "description": "Complete API testing collection",
    "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
  },
  "item": [
    {
      "name": "Health Check",
      "request": {
        "method": "GET",
        "header": [],
        "url": {
          "raw": "{{base_url}}/health",
          "host": ["{{base_url}}"],
          "path": ["health"]
        }
      }
    },
    {
      "name": "Smart Calculation",
      "request": {
        "method": "POST",
        "header": [
          {
            "key": "Content-Type",
            "value": "application/json"
          }
        ],
        "body": {
          "mode": "raw",
          "raw": "{\n  \"builtUpArea\": 1000,\n  \"floors\": 1,\n  \"qualityTier\": \"smart\",\n  \"location\": \"bangalore\"\n}"
        },
        "url": {
          "raw": "{{base_url}}/calculate",
          "host": ["{{base_url}}"],
          "path": ["calculate"]
        }
      }
    },
    {
      "name": "Premium Calculation",
      "request": {
        "method": "POST",
        "header": [
          {
            "key": "Content-Type",
            "value": "application/json"
          }
        ],
        "body": {
          "mode": "raw",
          "raw": "{\n  \"builtUpArea\": 1500,\n  \"floors\": 2,\n  \"qualityTier\": \"premium\",\n  \"location\": \"mumbai\",\n  \"hasStilt\": true,\n  \"parkingType\": \"covered\"\n}"
        },
        "url": {
          "raw": "{{base_url}}/calculate",
          "host": ["{{base_url}}"],
          "path": ["calculate"]
        }
      }
    },
    {
      "name": "Luxury with Features",
      "request": {
        "method": "POST",
        "header": [
          {
            "key": "Content-Type",
            "value": "application/json"
          }
        ],
        "body": {
          "mode": "raw",
          "raw": "{\n  \"builtUpArea\": 2500,\n  \"floors\": 3,\n  \"qualityTier\": \"luxury\",\n  \"location\": \"delhi\",\n  \"hasBasement\": true,\n  \"specialFeatures\": [\n    {\n      \"name\": \"Swimming Pool\",\n      \"cost\": 800000,\n      \"description\": \"Olympic size pool\"\n    }\n  ]\n}"
        },
        "url": {
          "raw": "{{base_url}}/calculate",
          "host": ["{{base_url}}"],
          "path": ["calculate"]
        }
      }
    },
    {
      "name": "Monitoring",
      "request": {
        "method": "GET",
        "header": [],
        "url": {
          "raw": "{{base_url}}/monitoring",
          "host": ["{{base_url}}"],
          "path": ["monitoring"]
        }
      }
    },
    {
      "name": "Performance Metrics",
      "request": {
        "method": "POST",
        "header": [
          {
            "key": "Content-Type",
            "value": "application/json"
          }
        ],
        "body": {
          "mode": "raw",
          "raw": "{\n  \"metric\": {\n    \"name\": \"LCP\",\n    \"value\": 1234,\n    \"rating\": \"good\",\n    \"url\": \"http://localhost:3000/calculator\"\n  }\n}"
        },
        "url": {
          "raw": "{{base_url}}/performance/metrics",
          "host": ["{{base_url}}"],
          "path": ["performance", "metrics"]
        }
      }
    }
  ],
  "variable": [
    {
      "key": "base_url",
      "value": "http://localhost:3000/api",
      "type": "string"
    }
  ]
}
```

## Performance Testing

### Load Testing with Apache Bench
```bash
# Test calculation endpoint with 100 concurrent requests
ab -n 1000 -c 100 -T "application/json" -p calculation_payload.json http://localhost:3000/api/calculate
```

### Load Testing with wrk
```bash
# Install wrk first
# Test with 12 threads, 400 connections for 30 seconds
wrk -t12 -c400 -d30s -s calculation_script.lua http://localhost:3000/api/calculate
```

### Stress Testing Script
```bash
#!/bin/bash

# Stress test the API
echo "Starting stress test..."

# Function to make requests
make_request() {
    curl -s -X POST "http://localhost:3000/api/calculate" \
        -H "Content-Type: application/json" \
        -d '{
            "builtUpArea": '$((RANDOM % 2000 + 500))',
            "floors": '$((RANDOM % 3 + 1))',
            "qualityTier": "premium",
            "location": "bangalore"
        }' > /dev/null 2>&1
}

# Run concurrent requests
for i in {1..100}; do
    make_request &
done

wait
echo "Stress test completed"
```

## Error Scenario Testing

### 1. Invalid Input Testing
```bash
# Test with invalid JSON
curl -X POST "http://localhost:3000/api/calculate" \
  -H "Content-Type: application/json" \
  -d 'invalid json'

# Test with missing required fields
curl -X POST "http://localhost:3000/api/calculate" \
  -H "Content-Type: application/json" \
  -d '{
    "builtUpArea": 1000
  }'

# Test with invalid data types
curl -X POST "http://localhost:3000/api/calculate" \
  -H "Content-Type: application/json" \
  -d '{
    "builtUpArea": "not a number",
    "floors": 1,
    "qualityTier": "premium",
    "location": "bangalore"
  }'
```

### 2. Edge Cases
```bash
# Test with minimum values
curl -X POST "http://localhost:3000/api/calculate" \
  -H "Content-Type: application/json" \
  -d '{
    "builtUpArea": 1,
    "floors": 0,
    "qualityTier": "smart",
    "location": "bangalore"
  }'

# Test with maximum values
curl -X POST "http://localhost:3000/api/calculate" \
  -H "Content-Type: application/json" \
  -d '{
    "builtUpArea": 100000,
    "floors": 10,
    "qualityTier": "luxury",
    "location": "mumbai"
  }'
```

## Monitoring and Debugging

### Enable Debug Mode
```bash
# Set environment variables for debugging
export DEBUG=true
export LOG_LEVEL=debug
npm run dev
```

### Check Logs
```bash
# Watch logs in real-time
tail -f logs/api.log

# Filter for errors
grep "ERROR" logs/api.log

# Check performance metrics
grep "Performance" logs/api.log
```

---

## Troubleshooting Common Issues

### 1. Connection Refused
```bash
# Check if server is running
netstat -tulpn | grep :3000

# Start the development server
npm run dev
```

### 2. Rate Limiting Issues
```bash
# Check rate limit headers
curl -I -X POST "http://localhost:3000/api/calculate" \
  -H "Content-Type: application/json" \
  -d '{...}'
```

### 3. Memory Issues
```bash
# Check memory usage
curl -X GET "http://localhost:3000/api/health" | jq .checks.memory
```

### 4. Performance Issues
```bash
# Check performance metrics
curl -X GET "http://localhost:3000/api/monitoring?detail=detailed" | jq .metrics.performance
```

---

*This testing guide provides comprehensive coverage for all API endpoints and scenarios. Use it to ensure your API implementation is robust and production-ready.*