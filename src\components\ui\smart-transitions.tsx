/**
 * Smart Loading Transitions with Staggered Animations
 * Provides intelligent loading sequences and transition management
 */

'use client';

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { motion, AnimatePresence, useAnimation, useInView } from 'framer-motion';
import { cn } from '@/lib/utils';
import { 
  fadeIn, 
  staggerContainer, 
  staggerItem, 
  slideInLeft, 
  slideInRight, 
  scaleIn,
  prefersReducedMotion 
} from '@/lib/animations';

// Smart stagger controller
interface StaggerControllerProps {
  children: React.ReactNode;
  staggerDelay?: number;
  initialDelay?: number;
  direction?: 'up' | 'down' | 'left' | 'right' | 'scale' | 'fade';
  threshold?: number;
  triggerOnce?: boolean;
  className?: string;
  onAnimationStart?: () => void;
  onAnimationComplete?: () => void;
}

export function StaggerController({
  children,
  staggerDelay = 0.1,
  initialDelay = 0,
  direction = 'up',
  threshold = 0.1,
  triggerOnce = true,
  className,
  onAnimationStart,
  onAnimationComplete
}: StaggerControllerProps) {
  const controls = useAnimation();
  const ref = useRef(null);
  const isInView = useInView(ref, { 
    threshold, 
    once: triggerOnce 
  });

  const getDirectionVariants = () => {
    switch (direction) {
      case 'left':
        return slideInLeft;
      case 'right':
        return slideInRight;
      case 'scale':
        return scaleIn;
      case 'fade':
        return fadeIn;
      case 'up':
      default:
        return {
          initial: { opacity: 0, y: prefersReducedMotion ? 0 : 20 },
          animate: { opacity: 1, y: 0 },
          exit: { opacity: 0, y: prefersReducedMotion ? 0 : -10 }
        };
    }
  };

  const containerVariants = {
    initial: {},
    animate: {
      transition: {
        staggerChildren: prefersReducedMotion ? 0 : staggerDelay,
        delayChildren: prefersReducedMotion ? 0 : initialDelay
      }
    }
  };

  useEffect(() => {
    if (isInView) {
      onAnimationStart?.();
      controls.start('animate').then(() => {
        onAnimationComplete?.();
      });
    } else if (!triggerOnce) {
      controls.start('initial');
    }
  }, [isInView, controls, triggerOnce, onAnimationStart, onAnimationComplete]);

  return (
    <motion.div
      ref={ref}
      className={className}
      variants={containerVariants}
      initial="initial"
      animate={controls}
    >
      {React.Children.map(children, (child, index) => (
        <motion.div
          key={index}
          variants={getDirectionVariants()}
          custom={index}
        >
          {child}
        </motion.div>
      ))}
    </motion.div>
  );
}

// Progressive reveal controller
interface ProgressiveRevealProps {
  items: React.ReactNode[];
  interval?: number;
  autoStart?: boolean;
  direction?: 'sequential' | 'batch' | 'cascade';
  batchSize?: number;
  className?: string;
  itemClassName?: string;
  onItemReveal?: (index: number) => void;
  onComplete?: () => void;
}

export function ProgressiveReveal({
  items,
  interval = 200,
  autoStart = true,
  direction = 'sequential',
  batchSize = 3,
  className,
  itemClassName,
  onItemReveal,
  onComplete
}: ProgressiveRevealProps) {
  const [visibleItems, setVisibleItems] = useState<Set<number>>(new Set());
  const [isActive, setIsActive] = useState(autoStart);
  const timeoutRef = useRef<NodeJS.Timeout>();

  const revealNext = useCallback(() => {
    setVisibleItems(prev => {
      const newSet = new Set(prev);
      
      if (direction === 'sequential') {
        // Reveal one item at a time
        const nextIndex = newSet.size;
        if (nextIndex < items.length) {
          newSet.add(nextIndex);
          onItemReveal?.(nextIndex);
        }
      } else if (direction === 'batch') {
        // Reveal items in batches
        const currentBatch = Math.floor(newSet.size / batchSize);
        const startIndex = currentBatch * batchSize;
        const endIndex = Math.min(startIndex + batchSize, items.length);
        
        for (let i = startIndex; i < endIndex; i++) {
          if (!newSet.has(i)) {
            newSet.add(i);
            onItemReveal?.(i);
          }
        }
      } else if (direction === 'cascade') {
        // Reveal with cascading effect
        const nextIndex = newSet.size;
        if (nextIndex < items.length) {
          newSet.add(nextIndex);
          onItemReveal?.(nextIndex);
          
          // Trigger multiple reveals with shorter delays
          if (nextIndex + 1 < items.length) {
            setTimeout(() => {
              setVisibleItems(prev => {
                const cascadeSet = new Set(prev);
                if (nextIndex + 1 < items.length) {
                  cascadeSet.add(nextIndex + 1);
                  onItemReveal?.(nextIndex + 1);
                }
                return cascadeSet;
              });
            }, interval / 2);
          }
        }
      }
      
      if (newSet.size >= items.length) {
        setIsActive(false);
        onComplete?.();
      }
      
      return newSet;
    });
  }, [items.length, direction, batchSize, interval, onItemReveal, onComplete]);

  useEffect(() => {
    if (!isActive) return;

    timeoutRef.current = setTimeout(revealNext, interval);
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [isActive, revealNext, interval, visibleItems.size]);

  const start = useCallback(() => {
    setIsActive(true);
    setVisibleItems(new Set());
  }, []);

  const pause = useCallback(() => {
    setIsActive(false);
  }, []);

  const reset = useCallback(() => {
    setIsActive(false);
    setVisibleItems(new Set());
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
  }, []);

  return (
    <div className={className}>
      <AnimatePresence>
        {items.map((item, index) => (
          visibleItems.has(index) && (
            <motion.div
              key={index}
              variants={fadeIn}
              initial="initial"
              animate="animate"
              exit="exit"
              className={itemClassName}
              custom={index}
            >
              {item}
            </motion.div>
          )
        ))}
      </AnimatePresence>
      
      {/* Control buttons for manual control */}
      {!autoStart && (
        <div className="flex gap-2 mt-4">
          <button
            onClick={start}
            disabled={isActive}
            className="px-3 py-1 text-sm bg-blue-500 text-white rounded disabled:opacity-50"
          >
            Start
          </button>
          <button
            onClick={pause}
            disabled={!isActive}
            className="px-3 py-1 text-sm bg-yellow-500 text-white rounded disabled:opacity-50"
          >
            Pause
          </button>
          <button
            onClick={reset}
            className="px-3 py-1 text-sm bg-red-500 text-white rounded"
          >
            Reset
          </button>
        </div>
      )}
    </div>
  );
}

// Loading sequence manager
interface LoadingSequenceProps {
  steps: Array<{
    id: string;
    component: React.ReactNode;
    duration?: number;
    delay?: number;
    skipable?: boolean;
  }>;
  onStepComplete?: (stepId: string, stepIndex: number) => void;
  onSequenceComplete?: () => void;
  className?: string;
  allowSkip?: boolean;
  showProgress?: boolean;
}

export function LoadingSequence({
  steps,
  onStepComplete,
  onSequenceComplete,
  className,
  allowSkip = false,
  showProgress = true
}: LoadingSequenceProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<Set<number>>(new Set());
  const timeoutRef = useRef<NodeJS.Timeout>();

  const nextStep = useCallback(() => {
    const step = steps[currentStep];
    if (step) {
      setCompletedSteps(prev => new Set([...prev, currentStep]));
      onStepComplete?.(step.id, currentStep);
      
      if (currentStep < steps.length - 1) {
        const nextStepDelay = steps[currentStep + 1]?.delay || 0;
        timeoutRef.current = setTimeout(() => {
          setCurrentStep(prev => prev + 1);
        }, nextStepDelay);
      } else {
        onSequenceComplete?.();
      }
    }
  }, [currentStep, steps, onStepComplete, onSequenceComplete]);

  const skipStep = useCallback(() => {
    if (allowSkip && steps[currentStep]?.skipable) {
      nextStep();
    }
  }, [allowSkip, currentStep, steps, nextStep]);

  useEffect(() => {
    const step = steps[currentStep];
    if (step && step.duration) {
      timeoutRef.current = setTimeout(nextStep, step.duration);
    }

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [currentStep, nextStep, steps]);

  const currentStepData = steps[currentStep];
  const progress = ((currentStep + 1) / steps.length) * 100;

  return (
    <div className={cn('space-y-4', className)}>
      {/* Progress indicator */}
      {showProgress && (
        <div className="space-y-2">
          <div className="flex justify-between text-sm text-gray-600">
            <span>Step {currentStep + 1} of {steps.length}</span>
            <span>{Math.round(progress)}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <motion.div
              className="bg-blue-500 h-2 rounded-full"
              initial={{ width: 0 }}
              animate={{ width: `${progress}%` }}
              transition={{ duration: 0.3 }}
            />
          </div>
        </div>
      )}

      {/* Current step content */}
      <AnimatePresence mode="wait">
        {currentStepData && (
          <motion.div
            key={currentStep}
            variants={fadeIn}
            initial="initial"
            animate="animate"
            exit="exit"
            className="min-h-[200px] flex items-center justify-center"
          >
            {currentStepData.component}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Skip button */}
      {allowSkip && currentStepData?.skipable && (
        <div className="text-center">
          <button
            onClick={skipStep}
            className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 underline"
          >
            Skip this step
          </button>
        </div>
      )}
    </div>
  );
}

// Intelligent content loader
interface IntelligentLoaderProps {
  content: React.ReactNode;
  skeleton: React.ReactNode;
  isLoading: boolean;
  error?: string | null;
  errorComponent?: React.ReactNode;
  retryComponent?: React.ReactNode;
  transition?: 'fade' | 'slide' | 'scale';
  loadingDelay?: number;
  minimumLoadTime?: number;
  onRetry?: () => void;
  className?: string;
}

export function IntelligentLoader({
  content,
  skeleton,
  isLoading: externalLoading,
  error,
  errorComponent,
  retryComponent,
  transition = 'fade',
  loadingDelay = 0,
  minimumLoadTime = 500,
  onRetry,
  className
}: IntelligentLoaderProps) {
  const [isLoading, setIsLoading] = useState(externalLoading);
  const [showContent, setShowContent] = useState(false);
  const loadStartTime = useRef<number>();

  useEffect(() => {
    if (externalLoading && !loadStartTime.current) {
      loadStartTime.current = Date.now();
      
      if (loadingDelay > 0) {
        setTimeout(() => setIsLoading(true), loadingDelay);
      } else {
        setIsLoading(true);
      }
    } else if (!externalLoading && loadStartTime.current) {
      const elapsedTime = Date.now() - loadStartTime.current;
      const remainingTime = Math.max(0, minimumLoadTime - elapsedTime);
      
      setTimeout(() => {
        setIsLoading(false);
        setShowContent(true);
        loadStartTime.current = undefined;
      }, remainingTime);
    }
  }, [externalLoading, loadingDelay, minimumLoadTime]);

  const transitions = {
    fade: fadeIn,
    slide: slideInLeft,
    scale: scaleIn
  };

  const currentTransition = transitions[transition];

  return (
    <div className={className}>
      <AnimatePresence mode="wait">
        {error ? (
          <motion.div
            key="error"
            variants={currentTransition}
            initial="initial"
            animate="animate"
            exit="exit"
          >
            {errorComponent || (
              <div className="text-center py-8">
                <div className="text-red-600 mb-4">
                  {error}
                </div>
                {onRetry && (
                  retryComponent || (
                    <button
                      onClick={onRetry}
                      className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
                    >
                      Try Again
                    </button>
                  )
                )}
              </div>
            )}
          </motion.div>
        ) : isLoading ? (
          <motion.div
            key="loading"
            variants={currentTransition}
            initial="initial"
            animate="animate"
            exit="exit"
          >
            {skeleton}
          </motion.div>
        ) : showContent ? (
          <motion.div
            key="content"
            variants={currentTransition}
            initial="initial"
            animate="animate"
            exit="exit"
          >
            {content}
          </motion.div>
        ) : null}
      </AnimatePresence>
    </div>
  );
}

// Batch loader for multiple items
interface BatchLoaderProps<T> {
  items: T[];
  renderItem: (item: T, index: number) => React.ReactNode;
  renderSkeleton: (index: number) => React.ReactNode;
  batchSize?: number;
  batchDelay?: number;
  isLoading?: boolean;
  className?: string;
  itemClassName?: string;
  onBatchLoad?: (batchIndex: number, items: T[]) => void;
}

export function BatchLoader<T>({
  items,
  renderItem,
  renderSkeleton,
  batchSize = 5,
  batchDelay = 300,
  isLoading = false,
  className,
  itemClassName,
  onBatchLoad
}: BatchLoaderProps<T>) {
  const [loadedBatches, setLoadedBatches] = useState<Set<number>>(new Set());
  const [currentBatch, setCurrentBatch] = useState(0);

  const totalBatches = Math.ceil(items.length / batchSize);

  useEffect(() => {
    if (isLoading) {
      setLoadedBatches(new Set());
      setCurrentBatch(0);
      return;
    }

    const timer = setTimeout(() => {
      if (currentBatch < totalBatches) {
        setLoadedBatches(prev => new Set([...prev, currentBatch]));
        
        const batchStart = currentBatch * batchSize;
        const batchEnd = Math.min(batchStart + batchSize, items.length);
        const batchItems = items.slice(batchStart, batchEnd);
        
        onBatchLoad?.(currentBatch, batchItems);
        setCurrentBatch(prev => prev + 1);
      }
    }, batchDelay);

    return () => clearTimeout(timer);
  }, [currentBatch, totalBatches, batchSize, batchDelay, isLoading, items, onBatchLoad]);

  return (
    <div className={className}>
      {Array.from({ length: totalBatches }).map((_, batchIndex) => {
        const batchStart = batchIndex * batchSize;
        const batchEnd = Math.min(batchStart + batchSize, items.length);
        const batchItems = items.slice(batchStart, batchEnd);
        const isLoaded = loadedBatches.has(batchIndex);

        return (
          <StaggerController
            key={batchIndex}
            staggerDelay={0.05}
            className={cn('grid gap-4', itemClassName)}
          >
            {batchItems.map((item, itemIndex) => {
              const globalIndex = batchStart + itemIndex;
              
              return (
                <div key={globalIndex}>
                  {isLoaded ? renderItem(item, globalIndex) : renderSkeleton(globalIndex)}
                </div>
              );
            })}
          </StaggerController>
        );
      })}
    </div>
  );
}