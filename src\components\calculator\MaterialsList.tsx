'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import {
  ArrowUpDown,
  Search,
  Filter,
  Package,
  TrendingUp,
  AlertCircle,
} from 'lucide-react';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

import { MaterialQuantity } from '@/core/calculator/types';
import { formatCurrency, formatNumber } from '@/lib/validation/calculator';

interface MaterialsListProps {
  materials: MaterialQuantity[];
}

type SortField = 'name' | 'quantity' | 'totalCost' | 'category';
type SortDirection = 'asc' | 'desc';

// Sample materials data if none provided
const sampleMaterials: MaterialQuantity[] = [
  {
    category: 'Structure',
    name: 'Cement (OPC 53 Grade)',
    quantity: 380,
    unit: 'bags (50kg)',
    rate: 350,
    totalCost: 133000,
    purpose: 'Foundation, columns, beams, slabs',
    specifications: 'IS:12269-2013, OPC 53 Grade',
  },
  {
    category: 'Structure',
    name: 'Steel (TMT Bars)',
    quantity: 4000,
    unit: 'kg',
    rate: 65,
    totalCost: 260000,
    purpose: 'Reinforcement for concrete structures',
    specifications: 'Fe 415, IS:1786-2008',
  },
  {
    category: 'Structure',
    name: 'Bricks (Clay)',
    quantity: 8000,
    unit: 'pieces',
    rate: 8,
    totalCost: 64000,
    purpose: 'Masonry walls, partitions',
    specifications: 'Class A, 230x110x70mm',
  },
  {
    category: 'Structure',
    name: 'Sand (River Sand)',
    quantity: 816,
    unit: 'cft',
    rate: 45,
    totalCost: 36720,
    purpose: 'Concrete mixing, plastering',
    specifications: 'Washed river sand, FM 2.6-3.0',
  },
  {
    category: 'Structure',
    name: 'Aggregate (20mm)',
    quantity: 608,
    unit: 'cft',
    rate: 55,
    totalCost: 33440,
    purpose: 'Concrete mixing',
    specifications: 'Crushed aggregate, 20mm nominal size',
  },
  {
    category: 'Finishing',
    name: 'Vitrified Tiles',
    quantity: 1000,
    unit: 'sqft',
    rate: 85,
    totalCost: 85000,
    purpose: 'Floor finishing',
    specifications: '600x600mm, Grade A quality',
  },
  {
    category: 'MEP',
    name: 'Electrical Wiring',
    quantity: 2000,
    unit: 'meters',
    rate: 25,
    totalCost: 50000,
    purpose: 'Electrical installation',
    specifications: '2.5 sq mm copper wire, ISI mark',
  },
];

export function MaterialsList({ materials = sampleMaterials }: MaterialsListProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortField, setSortField] = useState<SortField>('totalCost');
  const [sortDirection, setSortDirection] = useState<SortDirection>('desc');
  const [filterCategory, setFilterCategory] = useState<string>('all');

  // Get unique categories
  const categories = Array.from(new Set(materials.map(m => m.category)));

  // Filter and sort materials
  const filteredAndSortedMaterials = materials
    .filter(material => {
      const matchesSearch = material.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           material.category.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesCategory = filterCategory === 'all' || material.category === filterCategory;
      return matchesSearch && matchesCategory;
    })
    .sort((a, b) => {
      let aValue: any = a[sortField];
      let bValue: any = b[sortField];

      if (sortField === 'name' || sortField === 'category') {
        aValue = aValue?.toLowerCase() || '';
        bValue = bValue?.toLowerCase() || '';
      }

      if (sortDirection === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };

  const totalMaterialsCost = materials.reduce((sum, material) =>
    sum + (material.totalCost || 0), 0
  );

  const getSortIcon = (field: SortField) => {
    if (sortField !== field) {
      return <ArrowUpDown className="h-4 w-4 text-gray-400" />;
    }
    return (
      <ArrowUpDown
        className={`h-4 w-4 ${sortDirection === 'asc' ? 'rotate-180' : ''} text-blue-600`}
      />
    );
  };

  if (materials.length === 0) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Materials List Not Available
          </h3>
          <p className="text-gray-600">
            Detailed materials list will be available in the comprehensive report.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Package className="h-8 w-8 text-blue-600" />
              <div>
                <p className="text-sm text-gray-600">Total Materials</p>
                <p className="text-2xl font-bold text-gray-900">
                  {materials.length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <TrendingUp className="h-8 w-8 text-green-600" />
              <div>
                <p className="text-sm text-gray-600">Materials Cost</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatCurrency(totalMaterialsCost)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <AlertCircle className="h-8 w-8 text-amber-600" />
              <div>
                <p className="text-sm text-gray-600">Categories</p>
                <p className="text-2xl font-bold text-gray-900">
                  {categories.length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and search */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Materials & Quantities
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search materials..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <Select value={filterCategory} onValueChange={setFilterCategory}>
              <SelectTrigger className="w-full md:w-48">
                <Filter className="h-4 w-4 mr-2" />
                <SelectValue placeholder="Filter by category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {categories.map(category => (
                  <SelectItem key={category} value={category}>
                    {category}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Materials table */}
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left py-3 px-4">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleSort('category')}
                      className="font-semibold text-gray-700 hover:text-gray-900"
                    >
                      Category
                      {getSortIcon('category')}
                    </Button>
                  </th>
                  <th className="text-left py-3 px-4">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleSort('name')}
                      className="font-semibold text-gray-700 hover:text-gray-900"
                    >
                      Material
                      {getSortIcon('name')}
                    </Button>
                  </th>
                  <th className="text-right py-3 px-4">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleSort('quantity')}
                      className="font-semibold text-gray-700 hover:text-gray-900"
                    >
                      Quantity
                      {getSortIcon('quantity')}
                    </Button>
                  </th>
                  <th className="text-right py-3 px-4">
                    <span className="font-semibold text-gray-700">Rate</span>
                  </th>
                  <th className="text-right py-3 px-4">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleSort('totalCost')}
                      className="font-semibold text-gray-700 hover:text-gray-900"
                    >
                      Total Cost
                      {getSortIcon('totalCost')}
                    </Button>
                  </th>
                </tr>
              </thead>
              <tbody>
                {filteredAndSortedMaterials.map((material, index) => (
                  <motion.tr
                    key={`${material.category}-${material.name}-${index}`}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.05 }}
                    className="border-b border-gray-100 hover:bg-gray-50"
                  >
                    <td className="py-4 px-4">
                      <span className="inline-flex px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded">
                        {material.category}
                      </span>
                    </td>
                    <td className="py-4 px-4">
                      <div>
                        <div className="font-medium text-gray-900">
                          {material.name}
                        </div>
                        <div className="text-sm text-gray-600">
                          {material.purpose}
                        </div>
                        {material.specifications && (
                          <div className="text-xs text-gray-500 mt-1">
                            {material.specifications}
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="py-4 px-4 text-right">
                      <div className="font-medium">
                        {formatNumber(material.quantity)}
                      </div>
                      <div className="text-sm text-gray-600">
                        {material.unit}
                      </div>
                    </td>
                    <td className="py-4 px-4 text-right">
                      {material.rate ? (
                        <div className="text-sm">
                          ₹{formatNumber(material.rate)}
                          <div className="text-xs text-gray-500">
                            per {material.unit}
                          </div>
                        </div>
                      ) : (
                        <span className="text-gray-400">-</span>
                      )}
                    </td>
                    <td className="py-4 px-4 text-right">
                      {material.totalCost ? (
                        <div className="font-semibold text-gray-900">
                          {formatCurrency(material.totalCost)}
                        </div>
                      ) : (
                        <span className="text-gray-400">-</span>
                      )}
                    </td>
                  </motion.tr>
                ))}
              </tbody>
            </table>
          </div>

          {filteredAndSortedMaterials.length === 0 && (
            <div className="text-center py-8">
              <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">
                No materials found matching your search criteria.
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Note */}
      <Card className="bg-blue-50 border-blue-200">
        <CardContent className="p-4">
          <p className="text-sm text-blue-800">
            <strong>Note:</strong> Material quantities include standard wastage factors.
            Actual requirements may vary based on site conditions and construction practices.
            Rates are indicative and subject to market fluctuations.
          </p>
        </CardContent>
      </Card>
    </div>
  );
}