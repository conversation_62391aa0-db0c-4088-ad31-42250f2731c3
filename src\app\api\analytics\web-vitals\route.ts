import { NextRequest, NextResponse } from 'next/server';
// import { withPerformanceMonitoring } from '@/lib/performance/monitoring';

interface WebVitalsMetric {
  metric: string;
  value: number;
  rating: string;
  url: string;
  timestamp: number;
  userAgent: string;
  connectionType?: string;
  deviceType?: string;
  sessionId?: string;
  userId?: string;
  buildVersion?: string;
  route?: string;
  referrer?: string;
  viewport?: { width: number; height: number };
}

interface AnalyticsBatch {
  metrics: WebVitalsMetric[];
  timestamp: number;
  batchId: string;
}

// In-memory storage for development
const analyticsStore: WebVitalsMetric[] = [];
const MAX_ANALYTICS_ENTRIES = 10000;

// Aggregated stats cache
let aggregatedStats: any = null;
let lastStatsUpdate = 0;
const STATS_CACHE_DURATION = 60000; // 1 minute

export async function POST(request: NextRequest) {
  try {
      const body = await request.json();
      const { metrics } = body;

      if (!metrics || !Array.isArray(metrics)) {
        return NextResponse.json(
          { error: 'Invalid analytics data' },
          { status: 400 }
        );
      }

      // Process each metric
      for (const metric of metrics) {
        if (!metric.metric || typeof metric.value !== 'number') {
          continue;
        }

        const webVitalsMetric: WebVitalsMetric = {
          metric: metric.metric,
          value: metric.value,
          rating: metric.rating || 'unknown',
          url: metric.url || request.url,
          timestamp: metric.timestamp || Date.now(),
          userAgent: metric.userAgent || request.headers.get('user-agent') || '',
          connectionType: metric.connectionType,
          deviceType: metric.deviceType,
          sessionId: metric.sessionId,
          userId: metric.userId,
          buildVersion: metric.buildVersion,
          route: metric.route,
          referrer: metric.referrer,
          viewport: metric.viewport
        };

        // Store metric
        analyticsStore.push(webVitalsMetric);

        // Keep only recent metrics
        if (analyticsStore.length > MAX_ANALYTICS_ENTRIES) {
          analyticsStore.splice(0, analyticsStore.length - MAX_ANALYTICS_ENTRIES);
        }

        // Process metric for real-time analysis
        await processWebVitalsMetric(webVitalsMetric);
      }

      // Invalidate stats cache
      aggregatedStats = null;

      // Log in development
      if (process.env.NODE_ENV === 'development') {
        console.log(`[Web Vitals Analytics] Processed ${metrics.length} metrics`);
      }

      return NextResponse.json(
        { 
          status: 'received', 
          processed: metrics.length,
          timestamp: Date.now()
        },
        { status: 200 }
      );
    } catch (error) {
      console.error('Web vitals analytics error:', error);
      return NextResponse.json(
        { error: 'Failed to process analytics' },
        { status: 500 }
      );
    }
}

export async function GET(request: NextRequest) {
  try {
      const { searchParams } = new URL(request.url);
      const metric = searchParams.get('metric');
      const timeRange = searchParams.get('timeRange') || '1h';
      const deviceType = searchParams.get('deviceType');
      const route = searchParams.get('route');
      const aggregated = searchParams.get('aggregated') === 'true';

      // Calculate time range
      const now = Date.now();
      const timeRanges = {
        '1h': now - 3600000,
        '24h': now - 86400000,
        '7d': now - 604800000,
        '30d': now - 2592000000
      };
      const startTime = timeRanges[timeRange as keyof typeof timeRanges] || timeRanges['1h'];

      // Filter metrics
      let filteredMetrics = analyticsStore.filter(m => m.timestamp >= startTime);

      if (metric) {
        filteredMetrics = filteredMetrics.filter(m => m.metric === metric);
      }

      if (deviceType) {
        filteredMetrics = filteredMetrics.filter(m => m.deviceType === deviceType);
      }

      if (route) {
        filteredMetrics = filteredMetrics.filter(m => m.route === route);
      }

      if (aggregated) {
        // Return aggregated statistics
        const stats = await getAggregatedStats(filteredMetrics, timeRange);
        return NextResponse.json({
          stats,
          timeRange,
          totalMetrics: filteredMetrics.length,
          timestamp: Date.now()
        });
      } else {
        // Return raw metrics
        return NextResponse.json({
          metrics: filteredMetrics.slice(0, 1000), // Limit to 1000 recent metrics
          totalCount: filteredMetrics.length,
          timeRange,
          timestamp: Date.now()
        });
      }
    } catch (error) {
      console.error('Web vitals analytics retrieval error:', error);
      return NextResponse.json(
        { error: 'Failed to retrieve analytics' },
        { status: 500 }
      );
    }
}

async function processWebVitalsMetric(metric: WebVitalsMetric) {
  // Check for performance issues
  const alerts = checkPerformanceThresholds(metric);
  
  if (alerts.length > 0) {
    await processPerformanceAlerts(alerts, metric);
  }

  // Update real-time dashboard if connected
  await updateRealTimeDashboard(metric);

  // Send to external analytics services
  if (process.env.NODE_ENV === 'production') {
    await sendToExternalAnalytics(metric);
  }
}

function checkPerformanceThresholds(metric: WebVitalsMetric): Array<{
  type: 'warning' | 'critical';
  message: string;
  threshold: number;
}> {
  const alerts: Array<{
    type: 'warning' | 'critical';
    message: string;
    threshold: number;
  }> = [];

  const thresholds = {
    'LCP': { warning: 2500, critical: 4000 },
    'FID': { warning: 100, critical: 300 },
    'CLS': { warning: 0.1, critical: 0.25 },
    'FCP': { warning: 1800, critical: 3000 },
    'TTFB': { warning: 800, critical: 1800 },
    'INP': { warning: 200, critical: 500 }
  };

  const threshold = thresholds[metric.metric as keyof typeof thresholds];
  if (threshold) {
    if (metric.value > threshold.critical) {
      alerts.push({
        type: 'critical',
        message: `${metric.metric} is critically slow: ${metric.value}${getMetricUnit(metric.metric)}`,
        threshold: threshold.critical
      });
    } else if (metric.value > threshold.warning) {
      alerts.push({
        type: 'warning',
        message: `${metric.metric} needs improvement: ${metric.value}${getMetricUnit(metric.metric)}`,
        threshold: threshold.warning
      });
    }
  }

  return alerts;
}

async function processPerformanceAlerts(alerts: Array<{
  type: 'warning' | 'critical';
  message: string;
  threshold: number;
}>, metric: WebVitalsMetric) {
  for (const alert of alerts) {
    console.warn(`[Performance Alert] ${alert.type.toUpperCase()}: ${alert.message}`);
    
    // In production, send to monitoring service
    if (process.env.NODE_ENV === 'production') {
      await sendAlertToMonitoring(alert, metric);
    }
  }
}

async function updateRealTimeDashboard(metric: WebVitalsMetric) {
  // Update real-time dashboard via WebSocket or Server-Sent Events
  // Implementation depends on your real-time solution
  console.log('Updating real-time dashboard:', metric);
}

async function sendToExternalAnalytics(metric: WebVitalsMetric) {
  // Send to Google Analytics, DataDog, etc.
  try {
    // Example implementation
    console.log('Sending to external analytics:', metric);
  } catch (error) {
    console.error('Failed to send to external analytics:', error);
  }
}

async function sendAlertToMonitoring(alert: {
  type: 'warning' | 'critical';
  message: string;
  threshold: number;
}, metric: WebVitalsMetric) {
  // Send alert to monitoring service
  console.log('Sending alert to monitoring:', alert, metric);
}

async function getAggregatedStats(metrics: WebVitalsMetric[], timeRange: string) {
  const now = Date.now();
  
  // Check cache
  if (aggregatedStats && (now - lastStatsUpdate) < STATS_CACHE_DURATION) {
    return aggregatedStats;
  }

  // Calculate aggregated statistics
  const stats = {
    overview: calculateOverviewStats(metrics),
    byMetric: calculateStatsByMetric(metrics),
    byDevice: calculateStatsByDevice(metrics),
    byRoute: calculateStatsByRoute(metrics),
    timeline: calculateTimelineStats(metrics, timeRange),
    performanceScore: calculatePerformanceScore(metrics),
    trends: calculateTrends(metrics)
  };

  // Cache results
  aggregatedStats = stats;
  lastStatsUpdate = now;

  return stats;
}

function calculateOverviewStats(metrics: WebVitalsMetric[]) {
  const totalMetrics = metrics.length;
  const uniqueUsers = new Set(metrics.map(m => m.userId || m.sessionId)).size;
  const uniquePages = new Set(metrics.map(m => m.route)).size;
  
  // Calculate rating distribution
  const ratings = metrics.reduce((acc, m) => {
    acc[m.rating] = (acc[m.rating] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  return {
    totalMetrics,
    uniqueUsers,
    uniquePages,
    ratings,
    ratingPercentages: {
      good: Math.round((ratings.good || 0) / totalMetrics * 100),
      'needs-improvement': Math.round((ratings['needs-improvement'] || 0) / totalMetrics * 100),
      poor: Math.round((ratings.poor || 0) / totalMetrics * 100)
    }
  };
}

function calculateStatsByMetric(metrics: WebVitalsMetric[]) {
  const metricGroups = metrics.reduce((acc, m) => {
    if (!acc[m.metric]) {
      acc[m.metric] = [];
    }
    acc[m.metric].push(m.value);
    return acc;
  }, {} as Record<string, number[]>);

  const result: Record<string, any> = {};
  for (const [metric, values] of Object.entries(metricGroups)) {
    values.sort((a, b) => a - b);
    const count = values.length;
    const sum = values.reduce((a, b) => a + b, 0);
    
    result[metric] = {
      count,
      average: Math.round((sum / count) * 100) / 100,
      median: values[Math.floor(count / 2)],
      p75: values[Math.floor(count * 0.75)],
      p90: values[Math.floor(count * 0.90)],
      p95: values[Math.floor(count * 0.95)],
      p99: values[Math.floor(count * 0.99)],
      min: values[0],
      max: values[count - 1]
    };
  }

  return result;
}

function calculateStatsByDevice(metrics: WebVitalsMetric[]) {
  const deviceGroups = metrics.reduce((acc, m) => {
    const device = m.deviceType || 'unknown';
    if (!acc[device]) {
      acc[device] = [];
    }
    acc[device].push(m);
    return acc;
  }, {} as Record<string, WebVitalsMetric[]>);

  const result: Record<string, any> = {};
  for (const [device, deviceMetrics] of Object.entries(deviceGroups)) {
    result[device] = calculateOverviewStats(deviceMetrics);
  }

  return result;
}

function calculateStatsByRoute(metrics: WebVitalsMetric[]) {
  const routeGroups = metrics.reduce((acc, m) => {
    const route = m.route || 'unknown';
    if (!acc[route]) {
      acc[route] = [];
    }
    acc[route].push(m);
    return acc;
  }, {} as Record<string, WebVitalsMetric[]>);

  const result: Record<string, any> = {};
  for (const [route, routeMetrics] of Object.entries(routeGroups)) {
    result[route] = calculateOverviewStats(routeMetrics);
  }

  return result;
}

function calculateTimelineStats(metrics: WebVitalsMetric[], timeRange: string) {
  // Group metrics by time intervals
  const intervalMs = getIntervalMs(timeRange);
  const now = Date.now();
  const intervals: Record<string, WebVitalsMetric[]> = {};

  for (const metric of metrics) {
    const intervalStart = Math.floor(metric.timestamp / intervalMs) * intervalMs;
    const intervalKey = new Date(intervalStart).toISOString();
    
    if (!intervals[intervalKey]) {
      intervals[intervalKey] = [];
    }
    intervals[intervalKey].push(metric);
  }

  const timeline = Object.entries(intervals).map(([time, intervalMetrics]) => ({
    time,
    timestamp: new Date(time).getTime(),
    metrics: intervalMetrics.length,
    ...calculateOverviewStats(intervalMetrics)
  }));

  return timeline.sort((a, b) => a.timestamp - b.timestamp);
}

function getIntervalMs(timeRange: string): number {
  switch (timeRange) {
    case '1h': return 5 * 60 * 1000; // 5 minutes
    case '24h': return 60 * 60 * 1000; // 1 hour
    case '7d': return 6 * 60 * 60 * 1000; // 6 hours
    case '30d': return 24 * 60 * 60 * 1000; // 1 day
    default: return 60 * 60 * 1000; // 1 hour
  }
}

function calculatePerformanceScore(metrics: WebVitalsMetric[]) {
  const metricWeights = {
    'LCP': 0.25,
    'FID': 0.25,
    'CLS': 0.25,
    'FCP': 0.15,
    'TTFB': 0.05,
    'INP': 0.25
  };

  let totalScore = 0;
  let totalWeight = 0;

  for (const [metricName, weight] of Object.entries(metricWeights)) {
    const metricData = metrics.filter(m => m.metric === metricName);
    if (metricData.length > 0) {
      const goodCount = metricData.filter(m => m.rating === 'good').length;
      const score = (goodCount / metricData.length) * 100;
      totalScore += score * weight;
      totalWeight += weight;
    }
  }

  return totalWeight > 0 ? Math.round(totalScore / totalWeight) : 0;
}

function calculateTrends(metrics: WebVitalsMetric[]) {
  // Calculate trends over time
  const now = Date.now();
  const oneHourAgo = now - 3600000;
  const twoHoursAgo = now - 7200000;

  const recentMetrics = metrics.filter(m => m.timestamp > oneHourAgo);
  const previousMetrics = metrics.filter(m => m.timestamp > twoHoursAgo && m.timestamp <= oneHourAgo);

  const recentScore = calculatePerformanceScore(recentMetrics);
  const previousScore = calculatePerformanceScore(previousMetrics);

  return {
    performanceScore: {
      current: recentScore,
      previous: previousScore,
      change: recentScore - previousScore,
      trend: recentScore > previousScore ? 'up' : recentScore < previousScore ? 'down' : 'stable'
    }
  };
}

function getMetricUnit(metricName: string): string {
  const units: Record<string, string> = {
    'LCP': 'ms',
    'FID': 'ms',
    'CLS': '',
    'FCP': 'ms',
    'TTFB': 'ms',
    'INP': 'ms'
  };
  return units[metricName] || '';
}