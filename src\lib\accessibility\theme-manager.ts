/**
 * Theme and Visual Accessibility Manager
 * Provides high contrast themes and visual accessibility features for WCAG 2.1 AA compliance
 */

import { screenReader } from './screen-reader';

export type ThemeMode = 'light' | 'dark' | 'high-contrast' | 'high-contrast-dark';
export type MotionPreference = 'auto' | 'reduced' | 'none';
export type FontSizeScale = 'small' | 'normal' | 'large' | 'extra-large';

export interface AccessibilityThemeConfig {
  mode: ThemeMode;
  motion: MotionPreference;
  fontSize: FontSizeScale;
  focusVisible: boolean;
  underlineLinks: boolean;
  reducedTransparency: boolean;
  increasedContrast: boolean;
  colorBlindnessSupport: boolean;
}

export interface ColorContrastData {
  foreground: string;
  background: string;
  ratio: number;
  passes: {
    aa: boolean;
    aaa: boolean;
    large: boolean;
  };
}

/**
 * Accessibility Theme Manager
 */
export class AccessibilityThemeManager {
  private static instance: AccessibilityThemeManager;
  private config: AccessibilityThemeConfig;
  private styleElement: HTMLStyleElement;
  private observers: Set<(config: AccessibilityThemeConfig) => void> = new Set();

  constructor() {
    this.config = this.getDefaultConfig();
    this.styleElement = this.createStyleElement();
    this.loadSavedConfig();
    this.detectSystemPreferences();
    this.applyTheme();
    this.setupListeners();
  }

  static getInstance(): AccessibilityThemeManager {
    if (!AccessibilityThemeManager.instance) {
      AccessibilityThemeManager.instance = new AccessibilityThemeManager();
    }
    return AccessibilityThemeManager.instance;
  }

  /**
   * Get current theme configuration
   */
  getConfig(): AccessibilityThemeConfig {
    return { ...this.config };
  }

  /**
   * Update theme configuration
   */
  updateConfig(updates: Partial<AccessibilityThemeConfig>): void {
    const oldConfig = { ...this.config };
    this.config = { ...this.config, ...updates };
    
    this.applyTheme();
    this.saveConfig();
    this.notifyObservers();
    
    // Announce theme changes
    this.announceChanges(oldConfig, this.config);
  }

  /**
   * Set theme mode
   */
  setThemeMode(mode: ThemeMode): void {
    this.updateConfig({ mode });
  }

  /**
   * Set motion preference
   */
  setMotionPreference(motion: MotionPreference): void {
    this.updateConfig({ motion });
  }

  /**
   * Set font size scale
   */
  setFontSize(fontSize: FontSizeScale): void {
    this.updateConfig({ fontSize });
  }

  /**
   * Toggle high contrast mode
   */
  toggleHighContrast(): void {
    const newMode = this.config.mode === 'high-contrast' 
      ? 'light' 
      : this.config.mode === 'high-contrast-dark'
      ? 'dark'
      : this.config.mode === 'dark'
      ? 'high-contrast-dark'
      : 'high-contrast';
    
    this.setThemeMode(newMode);
  }

  /**
   * Toggle focus indicators
   */
  toggleFocusIndicators(): void {
    this.updateConfig({ focusVisible: !this.config.focusVisible });
  }

  /**
   * Subscribe to theme changes
   */
  subscribe(callback: (config: AccessibilityThemeConfig) => void): () => void {
    this.observers.add(callback);
    return () => this.observers.delete(callback);
  }

  /**
   * Get high contrast colors
   */
  getHighContrastColors(): Record<string, string> {
    const isDark = this.config.mode.includes('dark');
    
    if (isDark) {
      return {
        background: '#000000',
        foreground: '#ffffff',
        primary: '#ffffff',
        secondary: '#cccccc',
        accent: '#ffff00',
        border: '#ffffff',
        focus: '#00ffff',
        error: '#ff6b6b',
        success: '#51cf66',
        warning: '#ffd43b'
      };
    }
    
    return {
      background: '#ffffff',
      foreground: '#000000',
      primary: '#000000',
      secondary: '#333333',
      accent: '#0000ff',
      border: '#000000',
      focus: '#ff0000',
      error: '#d63031',
      success: '#00b894',
      warning: '#fdcb6e'
    };
  }

  /**
   * Check color contrast ratio
   */
  checkContrastRatio(foreground: string, background: string): ColorContrastData {
    const ratio = this.calculateContrastRatio(foreground, background);
    
    return {
      foreground,
      background,
      ratio,
      passes: {
        aa: ratio >= 4.5,
        aaa: ratio >= 7,
        large: ratio >= 3
      }
    };
  }

  /**
   * Get accessible color pair
   */
  getAccessibleColorPair(baseColor: string, preferredBackground?: string): {
    foreground: string;
    background: string;
    ratio: number;
  } {
    const backgrounds = preferredBackground 
      ? [preferredBackground]
      : ['#ffffff', '#000000', '#f8f9fa', '#212529'];
    
    const colors = this.config.mode.includes('high-contrast')
      ? this.getHighContrastColors()
      : {};
    
    for (const bg of backgrounds) {
      const ratio = this.calculateContrastRatio(baseColor, bg);
      if (ratio >= 4.5) {
        return { foreground: baseColor, background: bg, ratio };
      }
    }
    
    // Fallback to high contrast
    return {
      foreground: colors.foreground || '#000000',
      background: colors.background || '#ffffff',
      ratio: 21
    };
  }

  /**
   * Generate theme CSS
   */
  generateThemeCSS(): string {
    const colors = this.config.mode.includes('high-contrast')
      ? this.getHighContrastColors()
      : this.getStandardColors();
    
    const fontSizes = this.getFontSizeScale();
    const motionCSS = this.getMotionCSS();
    
    return `
      :root {
        /* Color tokens */
        --a11y-bg: ${colors.background};
        --a11y-fg: ${colors.foreground};
        --a11y-primary: ${colors.primary};
        --a11y-secondary: ${colors.secondary};
        --a11y-accent: ${colors.accent};
        --a11y-border: ${colors.border};
        --a11y-focus: ${colors.focus};
        --a11y-error: ${colors.error};
        --a11y-success: ${colors.success};
        --a11y-warning: ${colors.warning};
        
        /* Font size tokens */
        --a11y-text-xs: ${fontSizes.xs};
        --a11y-text-sm: ${fontSizes.sm};
        --a11y-text-base: ${fontSizes.base};
        --a11y-text-lg: ${fontSizes.lg};
        --a11y-text-xl: ${fontSizes.xl};
        --a11y-text-2xl: ${fontSizes['2xl']};
        
        /* Accessibility features */
        --a11y-focus-width: ${this.config.focusVisible ? '3px' : '2px'};
        --a11y-focus-style: ${this.config.focusVisible ? 'solid' : 'solid'};
        --a11y-underline-links: ${this.config.underlineLinks ? 'underline' : 'none'};
        --a11y-transparency: ${this.config.reducedTransparency ? '1' : '0.9'};
      }
      
      /* High contrast mode overrides */
      ${this.config.mode.includes('high-contrast') ? this.getHighContrastCSS() : ''}
      
      /* Motion preferences */
      ${motionCSS}
      
      /* Focus improvements */
      ${this.getFocusCSS()}
      
      /* Link improvements */
      ${this.getLinkCSS()}
      
      /* Form improvements */
      ${this.getFormCSS()}
      
      /* Button improvements */
      ${this.getButtonCSS()}
      
      /* Typography improvements */
      ${this.getTypographyCSS()}
    `;
  }

  private getDefaultConfig(): AccessibilityThemeConfig {
    return {
      mode: 'light',
      motion: 'auto',
      fontSize: 'normal',
      focusVisible: true,
      underlineLinks: false,
      reducedTransparency: false,
      increasedContrast: false,
      colorBlindnessSupport: false
    };
  }

  private createStyleElement(): HTMLStyleElement {
    const style = document.createElement('style');
    style.id = 'accessibility-theme';
    document.head.appendChild(style);
    return style;
  }

  private loadSavedConfig(): void {
    try {
      const saved = localStorage.getItem('accessibility-theme-config');
      if (saved) {
        this.config = { ...this.config, ...JSON.parse(saved) };
      }
    } catch (error) {
      console.warn('Failed to load saved accessibility config:', error);
    }
  }

  private saveConfig(): void {
    try {
      localStorage.setItem('accessibility-theme-config', JSON.stringify(this.config));
    } catch (error) {
      console.warn('Failed to save accessibility config:', error);
    }
  }

  private detectSystemPreferences(): void {
    // Detect dark mode preference
    if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
      this.config.mode = 'dark';
    }
    
    // Detect motion preference
    if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
      this.config.motion = 'reduced';
    }
    
    // Detect high contrast preference
    if (window.matchMedia('(prefers-contrast: high)').matches) {
      this.config.mode = this.config.mode === 'dark' ? 'high-contrast-dark' : 'high-contrast';
    }
    
    // Detect font size preference
    if (window.matchMedia('(prefers-reduced-motion: no-preference)').matches) {
      // Default is fine
    }
  }

  private setupListeners(): void {
    // Listen for system preference changes
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
      if (e.matches) {
        this.setThemeMode(this.config.mode.includes('high-contrast') ? 'high-contrast-dark' : 'dark');
      } else {
        this.setThemeMode(this.config.mode.includes('high-contrast') ? 'high-contrast' : 'light');
      }
    });
    
    window.matchMedia('(prefers-reduced-motion: reduce)').addEventListener('change', (e) => {
      this.setMotionPreference(e.matches ? 'reduced' : 'auto');
    });
    
    window.matchMedia('(prefers-contrast: high)').addEventListener('change', (e) => {
      if (e.matches) {
        this.toggleHighContrast();
      }
    });
  }

  private applyTheme(): void {
    const css = this.generateThemeCSS();
    this.styleElement.textContent = css;
    
    // Apply theme class to body
    document.body.className = document.body.className.replace(/theme-\w+/g, '');
    document.body.classList.add(`theme-${this.config.mode}`);
    
    // Apply motion class
    document.body.classList.toggle('reduce-motion', this.config.motion === 'reduced');
    document.body.classList.toggle('no-motion', this.config.motion === 'none');
    
    // Apply font size class
    document.body.classList.toggle('font-large', this.config.fontSize === 'large');
    document.body.classList.toggle('font-extra-large', this.config.fontSize === 'extra-large');
    document.body.classList.toggle('font-small', this.config.fontSize === 'small');
  }

  private notifyObservers(): void {
    this.observers.forEach(callback => {
      try {
        callback(this.config);
      } catch (error) {
        console.error('Error in theme observer:', error);
      }
    });
  }

  private announceChanges(oldConfig: AccessibilityThemeConfig, newConfig: AccessibilityThemeConfig): void {
    const changes: string[] = [];
    
    if (oldConfig.mode !== newConfig.mode) {
      changes.push(`Theme changed to ${newConfig.mode.replace('-', ' ')}`);
    }
    
    if (oldConfig.fontSize !== newConfig.fontSize) {
      changes.push(`Font size changed to ${newConfig.fontSize}`);
    }
    
    if (oldConfig.motion !== newConfig.motion) {
      changes.push(`Motion preferences set to ${newConfig.motion}`);
    }
    
    if (oldConfig.focusVisible !== newConfig.focusVisible) {
      changes.push(`Focus indicators ${newConfig.focusVisible ? 'enabled' : 'disabled'}`);
    }
    
    if (changes.length > 0) {
      screenReader.announce(changes.join(', '));
    }
  }

  private getStandardColors(): Record<string, string> {
    const isDark = this.config.mode === 'dark';
    
    if (isDark) {
      return {
        background: '#1a1a1a',
        foreground: '#ffffff',
        primary: '#3b82f6',
        secondary: '#6b7280',
        accent: '#8b5cf6',
        border: '#374151',
        focus: '#3b82f6',
        error: '#ef4444',
        success: '#10b981',
        warning: '#f59e0b'
      };
    }
    
    return {
      background: '#ffffff',
      foreground: '#111827',
      primary: '#2563eb',
      secondary: '#6b7280',
      accent: '#7c3aed',
      border: '#d1d5db',
      focus: '#2563eb',
      error: '#dc2626',
      success: '#059669',
      warning: '#d97706'
    };
  }

  private getFontSizeScale(): Record<string, string> {
    const baseMultiplier = {
      'small': 0.875,
      'normal': 1,
      'large': 1.125,
      'extra-large': 1.25
    }[this.config.fontSize];
    
    return {
      xs: `${0.75 * baseMultiplier}rem`,
      sm: `${0.875 * baseMultiplier}rem`,
      base: `${1 * baseMultiplier}rem`,
      lg: `${1.125 * baseMultiplier}rem`,
      xl: `${1.25 * baseMultiplier}rem`,
      '2xl': `${1.5 * baseMultiplier}rem`
    };
  }

  private getMotionCSS(): string {
    if (this.config.motion === 'none') {
      return `
        *, *::before, *::after {
          animation-duration: 0.01ms !important;
          animation-iteration-count: 1 !important;
          transition-duration: 0.01ms !important;
          scroll-behavior: auto !important;
        }
      `;
    }
    
    if (this.config.motion === 'reduced') {
      return `
        @media (prefers-reduced-motion: reduce) {
          *, *::before, *::after {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
            scroll-behavior: auto !important;
          }
        }
      `;
    }
    
    return '';
  }

  private getHighContrastCSS(): string {
    return `
      /* Force high contrast for all elements */
      * {
        border-color: var(--a11y-border) !important;
      }
      
      /* Ensure sufficient contrast for text */
      p, span, div, label, h1, h2, h3, h4, h5, h6 {
        color: var(--a11y-fg) !important;
        background-color: transparent !important;
      }
      
      /* High contrast buttons */
      button, input[type="button"], input[type="submit"] {
        background-color: var(--a11y-bg) !important;
        color: var(--a11y-fg) !important;
        border: 2px solid var(--a11y-border) !important;
      }
      
      /* High contrast form elements */
      input, select, textarea {
        background-color: var(--a11y-bg) !important;
        color: var(--a11y-fg) !important;
        border: 2px solid var(--a11y-border) !important;
      }
      
      /* Remove subtle backgrounds */
      .bg-gray-50, .bg-gray-100, .bg-slate-50, .bg-slate-100 {
        background-color: var(--a11y-bg) !important;
      }
    `;
  }

  private getFocusCSS(): string {
    return `
      /* Enhanced focus indicators */
      *:focus-visible {
        outline: var(--a11y-focus-width) var(--a11y-focus-style) var(--a11y-focus) !important;
        outline-offset: 2px !important;
        box-shadow: 0 0 0 2px var(--a11y-focus) !important;
      }
      
      /* Ensure focus is visible on all interactive elements */
      button:focus-visible,
      input:focus-visible,
      select:focus-visible,
      textarea:focus-visible,
      a:focus-visible,
      [tabindex]:focus-visible {
        outline: var(--a11y-focus-width) var(--a11y-focus-style) var(--a11y-focus) !important;
        outline-offset: 2px !important;
      }
    `;
  }

  private getLinkCSS(): string {
    return `
      /* Link improvements */
      a {
        text-decoration: var(--a11y-underline-links);
        text-decoration-thickness: 2px;
        text-underline-offset: 2px;
      }
      
      a:hover {
        text-decoration: underline;
        text-decoration-thickness: 2px;
      }
      
      /* Visited link distinction */
      a:visited {
        color: var(--a11y-secondary);
      }
    `;
  }

  private getFormCSS(): string {
    return `
      /* Form improvements */
      input, select, textarea {
        font-size: var(--a11y-text-base);
        padding: 0.75rem;
        border-width: 2px;
        border-radius: 0.375rem;
      }
      
      /* Error states */
      input[aria-invalid="true"],
      select[aria-invalid="true"],
      textarea[aria-invalid="true"] {
        border-color: var(--a11y-error) !important;
        background-color: color-mix(in srgb, var(--a11y-error) 5%, var(--a11y-bg));
      }
      
      /* Required field indicators */
      [aria-required="true"] {
        border-left: 4px solid var(--a11y-accent);
      }
    `;
  }

  private getButtonCSS(): string {
    return `
      /* Button improvements */
      button {
        font-size: var(--a11y-text-base);
        padding: 0.75rem 1.5rem;
        border-radius: 0.375rem;
        border-width: 2px;
        font-weight: 600;
        min-height: 44px;
        min-width: 44px;
      }
      
      /* Disabled state */
      button:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }
      
      /* Loading state */
      button[aria-busy="true"] {
        position: relative;
        color: transparent;
      }
      
      button[aria-busy="true"]::after {
        content: "";
        position: absolute;
        width: 16px;
        height: 16px;
        top: 50%;
        left: 50%;
        margin-left: -8px;
        margin-top: -8px;
        border: 2px solid var(--a11y-fg);
        border-radius: 50%;
        border-top-color: transparent;
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        to { transform: rotate(360deg); }
      }
    `;
  }

  private getTypographyCSS(): string {
    return `
      /* Typography improvements */
      body {
        font-size: var(--a11y-text-base);
        line-height: 1.6;
      }
      
      h1 { font-size: var(--a11y-text-2xl); line-height: 1.2; }
      h2 { font-size: var(--a11y-text-xl); line-height: 1.3; }
      h3 { font-size: var(--a11y-text-lg); line-height: 1.4; }
      h4, h5, h6 { font-size: var(--a11y-text-base); line-height: 1.5; }
      
      /* Improved text spacing */
      p + p { margin-top: 1rem; }
      h1 + *, h2 + *, h3 + * { margin-top: 0.5rem; }
      
      /* Better readability */
      .text-sm { font-size: var(--a11y-text-sm); }
      .text-lg { font-size: var(--a11y-text-lg); }
      .text-xl { font-size: var(--a11y-text-xl); }
    `;
  }

  private calculateContrastRatio(color1: string, color2: string): number {
    const lum1 = this.getLuminance(color1);
    const lum2 = this.getLuminance(color2);
    
    const brightest = Math.max(lum1, lum2);
    const darkest = Math.min(lum1, lum2);
    
    return (brightest + 0.05) / (darkest + 0.05);
  }

  private getLuminance(color: string): number {
    const rgb = this.hexToRgb(color);
    if (!rgb) return 0;
    
    const [r, g, b] = [rgb.r, rgb.g, rgb.b].map(c => {
      c = c / 255;
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    });
    
    return 0.2126 * r + 0.7152 * g + 0.0722 * b;
  }

  private hexToRgb(hex: string): { r: number; g: number; b: number } | null {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null;
  }
}

// Export singleton instance
export const themeManager = AccessibilityThemeManager.getInstance();

/**
 * React hook for theme management
 */
export function useAccessibilityTheme() {
  return {
    getConfig: themeManager.getConfig.bind(themeManager),
    updateConfig: themeManager.updateConfig.bind(themeManager),
    setThemeMode: themeManager.setThemeMode.bind(themeManager),
    setMotionPreference: themeManager.setMotionPreference.bind(themeManager),
    setFontSize: themeManager.setFontSize.bind(themeManager),
    toggleHighContrast: themeManager.toggleHighContrast.bind(themeManager),
    toggleFocusIndicators: themeManager.toggleFocusIndicators.bind(themeManager),
    subscribe: themeManager.subscribe.bind(themeManager),
    checkContrastRatio: themeManager.checkContrastRatio.bind(themeManager),
    getAccessibleColorPair: themeManager.getAccessibleColorPair.bind(themeManager)
  };
}