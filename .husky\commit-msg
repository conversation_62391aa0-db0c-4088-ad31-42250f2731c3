#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

# Conventional Commits validation
# Pattern: type(scope?): description
# Types: feat, fix, docs, style, refactor, test, chore, perf, ci, build

commit_regex='^(feat|fix|docs|style|refactor|test|chore|perf|ci|build)(\(.+\))?: .{1,50}'

if ! grep -qE "$commit_regex" "$1"; then
  echo "❌ Invalid commit message format!"
  echo ""
  echo "Format: type(scope): description"
  echo "Types: feat, fix, docs, style, refactor, test, chore, perf, ci, build"
  echo ""
  echo "Examples:"
  echo "  feat(calculator): add premium tier calculations"
  echo "  fix(api): resolve validation error handling"
  echo "  docs: update README with installation steps"
  echo ""
  exit 1
fi

echo "✅ Commit message format is valid"