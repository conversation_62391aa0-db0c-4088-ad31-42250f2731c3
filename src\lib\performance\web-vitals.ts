import { onCLS, onFCP, onLCP, onTTFB, onINP, type Metric } from 'web-vitals';

export interface WebVitalsData {
  id: string;
  name: string;
  value: number;
  rating: 'good' | 'needs-improvement' | 'poor';
  delta: number;
  entries: PerformanceEntry[];
  timestamp: number;
  url: string;
  userAgent: string;
  connectionType?: string;
  deviceType?: 'mobile' | 'tablet' | 'desktop';
  viewport?: { width: number; height: number };
  sessionId?: string;
  userId?: string;
  buildVersion?: string;
  route?: string;
  referrer?: string;
}

export interface PerformanceReport {
  cls: WebVitalsData | null;
  fcp: WebVitalsData | null;
  lcp: WebVitalsData | null;
  ttfb: WebVitalsData | null;
  inp: WebVitalsData | null;
  timestamp: number;
  url: string;
  route: string;
  loadTime: number;
  domContentLoadedTime: number;
  resourceCount: number;
  transferSize: number;
  cacheHitRate: number;
  criticalResourcesLoadTime: number;
  memoryUsage?: {
    usedJSHeapSize: number;
    totalJSHeapSize: number;
    jsHeapSizeLimit: number;
  };
  networkInfo?: {
    type: string;
    effectiveType: string;
    downlink: number;
    rtt: number;
    saveData: boolean;
  };
  deviceInfo?: {
    type: 'mobile' | 'tablet' | 'desktop';
    screen: { width: number; height: number };
    viewport: { width: number; height: number };
    pixelRatio: number;
  };
  performanceScore: number;
  alerts: PerformanceAlert[];
}

export interface PerformanceAlert {
  type: 'warning' | 'error' | 'info';
  metric: string;
  message: string;
  threshold: number;
  actualValue: number;
  timestamp: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

class WebVitalsMonitor {
  private metrics: Map<string, WebVitalsData> = new Map();
  private isInitialized = false;
  private reportCallback?: (report: PerformanceReport) => void;
  private alertCallback?: (alert: PerformanceAlert) => void;
  private sessionId: string = this.generateSessionId();
  private userId?: string;
  private buildVersion: string = process.env.npm_package_version || '1.0.0';
  private performanceObserver?: PerformanceObserver;
  private longTaskObserver?: PerformanceObserver;
  private alerts: PerformanceAlert[] = [];

  constructor() {
    this.initializeMonitoring();
  }

  private generateSessionId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  private initializeMonitoring() {
    if (this.isInitialized || typeof window === 'undefined') return;

    this.isInitialized = true;

    // Monitor Core Web Vitals
    onCLS(this.handleMetric.bind(this), { reportAllChanges: true });
    onFCP(this.handleMetric.bind(this));
    onLCP(this.handleMetric.bind(this), { reportAllChanges: true });
    onTTFB(this.handleMetric.bind(this));
    onINP(this.handleMetric.bind(this), { reportAllChanges: true });

    // Monitor page load events
    window.addEventListener('load', this.handlePageLoad.bind(this));
    window.addEventListener('beforeunload', this.handlePageUnload.bind(this));
    window.addEventListener('visibilitychange', this.handleVisibilityChange.bind(this));

    // Initialize performance observers
    this.initializePerformanceObservers();

    // Start continuous monitoring
    this.startContinuousMonitoring();
  }

  private initializePerformanceObservers() {
    if (!('PerformanceObserver' in window)) return;

    // Long task observer
    try {
      this.longTaskObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.duration > 50) { // Tasks longer than 50ms
            this.handleLongTask(entry);
          }
        }
      });
      this.longTaskObserver.observe({ entryTypes: ['longtask'] });
    } catch (e) {
      console.warn('Long task observer not supported');
    }

    // Resource observer
    try {
      this.performanceObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'resource') {
            this.handleResourceTiming(entry as PerformanceResourceTiming);
          }
        }
      });
      this.performanceObserver.observe({ entryTypes: ['resource'] });
    } catch (e) {
      console.warn('Resource observer not supported');
    }
  }

  private handleLongTask(entry: PerformanceEntry) {
    const alert: PerformanceAlert = {
      type: 'warning',
      metric: 'LongTask',
      message: `Long task detected: ${entry.duration.toFixed(2)}ms`,
      threshold: 50,
      actualValue: entry.duration,
      timestamp: Date.now(),
      severity: entry.duration > 100 ? 'high' : 'medium'
    };

    this.alerts.push(alert);
    this.alertCallback?.(alert);
  }

  private handleResourceTiming(entry: PerformanceResourceTiming) {
    // Monitor slow resources
    if (entry.duration > 1000) {
      const alert: PerformanceAlert = {
        type: 'warning',
        metric: 'SlowResource',
        message: `Slow resource: ${entry.name} (${entry.duration.toFixed(2)}ms)`,
        threshold: 1000,
        actualValue: entry.duration,
        timestamp: Date.now(),
        severity: entry.duration > 3000 ? 'high' : 'medium'
      };

      this.alerts.push(alert);
      this.alertCallback?.(alert);
    }
  }

  private handleVisibilityChange() {
    if (document.visibilityState === 'hidden') {
      this.sendMetrics();
    }
  }

  private startContinuousMonitoring() {
    // Monitor memory usage every 30 seconds
    setInterval(() => {
      this.checkMemoryUsage();
    }, 30000);

    // Monitor performance every 10 seconds
    setInterval(() => {
      this.checkPerformanceThresholds();
    }, 10000);
  }

  private checkMemoryUsage() {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      const usagePercent = (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100;
      
      if (usagePercent > 80) {
        const alert: PerformanceAlert = {
          type: 'warning',
          metric: 'MemoryUsage',
          message: `High memory usage: ${usagePercent.toFixed(1)}%`,
          threshold: 80,
          actualValue: usagePercent,
          timestamp: Date.now(),
          severity: usagePercent > 90 ? 'critical' : 'high'
        };

        this.alerts.push(alert);
        this.alertCallback?.(alert);
      }
    }
  }

  private checkPerformanceThresholds() {
    const lcp = this.metrics.get('LCP');
    const cls = this.metrics.get('CLS');
    const inp = this.metrics.get('INP');

    // Check LCP threshold
    if (lcp && lcp.value > 4000) {
      const alert: PerformanceAlert = {
        type: 'error',
        metric: 'LCP',
        message: `Poor LCP: ${lcp.value.toFixed(2)}ms`,
        threshold: 2500,
        actualValue: lcp.value,
        timestamp: Date.now(),
        severity: 'high'
      };

      this.alerts.push(alert);
      this.alertCallback?.(alert);
    }

    // Check CLS threshold
    if (cls && cls.value > 0.25) {
      const alert: PerformanceAlert = {
        type: 'error',
        metric: 'CLS',
        message: `Poor CLS: ${cls.value.toFixed(3)}`,
        threshold: 0.1,
        actualValue: cls.value,
        timestamp: Date.now(),
        severity: 'high'
      };

      this.alerts.push(alert);
      this.alertCallback?.(alert);
    }

    // Check INP threshold
    if (inp && inp.value > 500) {
      const alert: PerformanceAlert = {
        type: 'error',
        metric: 'INP',
        message: `Poor INP: ${inp.value.toFixed(2)}ms`,
        threshold: 200,
        actualValue: inp.value,
        timestamp: Date.now(),
        severity: 'high'
      };

      this.alerts.push(alert);
      this.alertCallback?.(alert);
    }
  }

  private handleMetric(metric: Metric) {
    const vitalsData: WebVitalsData = {
      id: metric.id,
      name: metric.name,
      value: metric.value,
      rating: this.getMetricRating(metric.name, metric.value),
      delta: metric.delta,
      entries: metric.entries,
      timestamp: Date.now(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      connectionType: this.getConnectionType(),
      deviceType: this.getDeviceType(),
      viewport: this.getViewportInfo(),
      sessionId: this.sessionId,
      userId: this.userId,
      buildVersion: this.buildVersion,
      route: this.getCurrentRoute(),
      referrer: document.referrer,
    };

    this.metrics.set(metric.name, vitalsData);
    this.reportMetric(vitalsData);
  }

  private getMetricRating(name: string, value: number): 'good' | 'needs-improvement' | 'poor' {
    const thresholds = {
      CLS: [0.1, 0.25],
      FCP: [1800, 3000],
      LCP: [2500, 4000],
      TTFB: [800, 1800],
      INP: [200, 500],
    };

    const [good, poor] = thresholds[name as keyof typeof thresholds] || [0, 0];

    if (value <= good) return 'good';
    if (value <= poor) return 'needs-improvement';
    return 'poor';
  }

  private getDeviceType(): 'mobile' | 'tablet' | 'desktop' {
    const width = window.innerWidth;
    if (width < 768) return 'mobile';
    if (width < 1024) return 'tablet';
    return 'desktop';
  }

  private getViewportInfo(): { width: number; height: number } {
    return {
      width: window.innerWidth,
      height: window.innerHeight,
    };
  }

  private getCurrentRoute(): string {
    return window.location.pathname + window.location.search;
  }

  private getNetworkInfo(): any {
    const connection = (navigator as any).connection;
    if (!connection) return null;

    return {
      type: connection.type || 'unknown',
      effectiveType: connection.effectiveType || 'unknown',
      downlink: connection.downlink || 0,
      rtt: connection.rtt || 0,
      saveData: connection.saveData || false,
    };
  }

  private getDeviceInfo(): any {
    return {
      type: this.getDeviceType(),
      screen: {
        width: window.screen.width,
        height: window.screen.height,
      },
      viewport: this.getViewportInfo(),
      pixelRatio: window.devicePixelRatio || 1,
    };
  }

  private getConnectionType(): string | undefined {
    const connection = (navigator as any).connection;
    return connection ? connection.effectiveType : undefined;
  }

  private handlePageLoad() {
    // Generate comprehensive performance report
    const report = this.generatePerformanceReport();
    if (this.reportCallback) {
      this.reportCallback(report);
    }
  }

  private handlePageUnload() {
    // Send final metrics before page unload
    this.sendMetrics();
  }

  private generatePerformanceReport(): PerformanceReport {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    const resources = performance.getEntriesByType('resource');

    // Calculate additional metrics
    const transferSize = resources.reduce((sum, resource) => {
      return sum + ((resource as any).transferSize || 0);
    }, 0);

    const cachedResources = resources.filter(resource => 
      (resource as any).transferSize === 0 && (resource as any).decodedBodySize > 0
    );
    const cacheHitRate = resources.length > 0 ? (cachedResources.length / resources.length) * 100 : 0;

    // Calculate critical resources load time
    const criticalResources = resources.filter(resource => 
      resource.name.includes('.css') || resource.name.includes('.js') || resource.name.includes('font')
    );
    const criticalResourcesLoadTime = criticalResources.reduce((max, resource) => 
      Math.max(max, resource.responseEnd - resource.startTime), 0
    );

    const report: PerformanceReport = {
      cls: this.metrics.get('CLS') || null,
      fcp: this.metrics.get('FCP') || null,
      lcp: this.metrics.get('LCP') || null,
      ttfb: this.metrics.get('TTFB') || null,
      inp: this.metrics.get('INP') || null,
      timestamp: Date.now(),
      url: window.location.href,
      route: this.getCurrentRoute(),
      loadTime: navigation ? navigation.loadEventEnd - navigation.fetchStart : 0,
      domContentLoadedTime: navigation ? navigation.domContentLoadedEventEnd - navigation.fetchStart : 0,
      resourceCount: resources.length,
      transferSize,
      cacheHitRate,
      criticalResourcesLoadTime,
      networkInfo: this.getNetworkInfo(),
      deviceInfo: this.getDeviceInfo(),
      performanceScore: this.calculatePerformanceScore(),
      alerts: [...this.alerts],
    };

    // Add memory usage if available
    if ('memory' in performance) {
      report.memoryUsage = (performance as any).memory;
    }

    return report;
  }

  private calculatePerformanceScore(): number {
    let score = 100;
    const metrics = this.metrics;

    // LCP scoring (40% weight)
    const lcp = metrics.get('LCP');
    if (lcp) {
      if (lcp.value > 4000) score -= 40;
      else if (lcp.value > 2500) score -= 20;
    }

    // CLS scoring (25% weight)
    const cls = metrics.get('CLS');
    if (cls) {
      if (cls.value > 0.25) score -= 25;
      else if (cls.value > 0.1) score -= 12;
    }

    // INP scoring (25% weight)
    const inp = metrics.get('INP');
    if (inp) {
      const threshold = 500;
      const goodThreshold = 200;
      if (inp.value > threshold) score -= 25;
      else if (inp.value > goodThreshold) score -= 12;
    }

    // FCP scoring (10% weight)
    const fcp = metrics.get('FCP');
    if (fcp) {
      if (fcp.value > 3000) score -= 10;
      else if (fcp.value > 1800) score -= 5;
    }

    return Math.max(0, Math.min(100, score));
  }

  private reportMetric(metric: WebVitalsData) {
    // Report to analytics service
    this.sendToAnalytics(metric);
    
    // Report to monitoring service
    this.sendToMonitoring(metric);
    
    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`[Web Vitals] ${metric.name}:`, metric);
    }
  }

  private sendToAnalytics(metric: WebVitalsData) {
    // Send to Google Analytics 4
    if (typeof window !== 'undefined' && 'gtag' in window) {
      (window as any).gtag('event', metric.name, {
        event_category: 'Web Vitals',
        event_label: metric.id,
        value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
        custom_parameter_1: metric.rating,
        custom_parameter_2: metric.url,
      });
    }

    // Send to other analytics services
    this.sendToCustomAnalytics(metric);
  }

  private sendToMonitoring(metric: WebVitalsData) {
    // Send to application monitoring service
    if (typeof window !== 'undefined' && typeof window.fetch === 'function') {
      fetch('/api/performance/metrics', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          metric,
          timestamp: Date.now(),
          page: window.location.pathname,
          referrer: document.referrer,
          viewport: {
            width: window.innerWidth,
            height: window.innerHeight,
          },
        }),
      }).catch(error => {
        console.error('Failed to send metric to monitoring service:', error);
      });
    }
  }

  private sendToCustomAnalytics(metric: WebVitalsData) {
    // Send to custom analytics endpoint
    const analyticsData = {
      metric: metric.name,
      value: metric.value,
      rating: metric.rating,
      url: metric.url,
      timestamp: metric.timestamp,
      userAgent: metric.userAgent,
      connectionType: metric.connectionType,
    };

    // Queue for batch sending
    this.queueAnalytics(analyticsData);
  }

  private analyticsQueue: any[] = [];
  private queueAnalytics(data: any) {
    this.analyticsQueue.push(data);
    
    // Send batch every 5 seconds or when queue reaches 10 items
    if (this.analyticsQueue.length >= 10) {
      this.sendAnalyticsBatch();
    } else {
      setTimeout(() => this.sendAnalyticsBatch(), 5000);
    }
  }

  private sendAnalyticsBatch() {
    if (this.analyticsQueue.length === 0) return;

    const batch = [...this.analyticsQueue];
    this.analyticsQueue = [];

    if (typeof window !== 'undefined' && typeof window.fetch === 'function') {
      fetch('/api/analytics/web-vitals', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ metrics: batch }),
      }).catch(error => {
        console.error('Failed to send analytics batch:', error);
        // Re-queue failed items
        this.analyticsQueue.unshift(...batch);
      });
    }
  }

  private sendMetrics() {
    // Send any remaining metrics
    this.sendAnalyticsBatch();
  }

  public onReport(callback: (report: PerformanceReport) => void) {
    this.reportCallback = callback;
  }

  public onAlert(callback: (alert: PerformanceAlert) => void) {
    this.alertCallback = callback;
  }

  public setUserId(userId: string) {
    this.userId = userId;
  }

  public getAlerts(): PerformanceAlert[] {
    return [...this.alerts];
  }

  public clearAlerts() {
    this.alerts = [];
  }

  public getPerformanceScore(): number {
    return this.calculatePerformanceScore();
  }

  public getPublicDeviceInfo() {
    return this.getDeviceInfo();
  }

  public getPublicNetworkInfo() {
    return this.getNetworkInfo();
  }

  public startRealTimeMonitoring() {
    // Start real-time monitoring with WebSocket if available
    if ('WebSocket' in window) {
      try {
        const ws = new WebSocket(`ws://${window.location.host}/api/performance/realtime`);
        ws.onopen = () => {
          console.log('Real-time performance monitoring connected');
        };
        ws.onmessage = (event) => {
          const data = JSON.parse(event.data);
          if (data.type === 'performance-alert') {
            this.alertCallback?.(data.alert);
          }
        };
      } catch (error) {
        console.warn('Real-time monitoring not available:', error);
      }
    }
  }

  public getMetrics(): Map<string, WebVitalsData> {
    return new Map(this.metrics);
  }

  public getMetric(name: string): WebVitalsData | undefined {
    return this.metrics.get(name);
  }

  public clearMetrics() {
    this.metrics.clear();
  }

  public generateReport(): PerformanceReport {
    return this.generatePerformanceReport();
  }
}

// Performance budget checker
export class PerformanceBudget {
  private budgets = {
    LCP: 2500, // 2.5 seconds
    CLS: 0.1,  // 0.1 cumulative layout shift
    FCP: 1800, // 1.8 seconds
    TTFB: 800, // 800 milliseconds
    INP: 200, // 200 milliseconds
  };

  public checkBudget(metrics: Map<string, WebVitalsData>): {
    passed: boolean;
    violations: Array<{
      metric: string;
      value: number;
      budget: number;
      severity: 'warning' | 'error';
    }>;
  } {
    const violations: Array<{
      metric: string;
      value: number;
      budget: number;
      severity: 'warning' | 'error';
    }> = [];

    for (const [name, budget] of Object.entries(this.budgets)) {
      const metric = metrics.get(name);
      if (metric && metric.value > budget) {
        violations.push({
          metric: name,
          value: metric.value,
          budget,
          severity: metric.rating === 'poor' ? 'error' : 'warning',
        });
      }
    }

    return {
      passed: violations.length === 0,
      violations,
    };
  }

  public setBudget(metric: string, value: number) {
    this.budgets[metric as keyof typeof this.budgets] = value;
  }
}

// React component for monitoring
export class ReactPerformanceMonitor {
  private renderTimes: Map<string, number> = new Map();
  private componentCounts: Map<string, number> = new Map();

  public startRender(componentName: string) {
    this.renderTimes.set(componentName, performance.now());
  }

  public endRender(componentName: string) {
    const startTime = this.renderTimes.get(componentName);
    if (startTime) {
      const renderTime = performance.now() - startTime;
      this.reportRenderTime(componentName, renderTime);
      this.renderTimes.delete(componentName);
    }
  }

  public incrementComponentCount(componentName: string) {
    const current = this.componentCounts.get(componentName) || 0;
    this.componentCounts.set(componentName, current + 1);
  }

  public reportRenderTime(componentName: string, renderTime: number) {
    if (renderTime > 16) { // Longer than one frame
      console.warn(`[React Performance] ${componentName} took ${renderTime.toFixed(2)}ms to render`);
    }

    // Send to monitoring service
    if (typeof window !== 'undefined' && typeof window.fetch === 'function') {
      fetch('/api/performance/react-metrics', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          component: componentName,
          renderTime,
          timestamp: Date.now(),
          page: window.location.pathname,
        }),
      }).catch(error => {
        console.error('Failed to send React metric:', error);
      });
    }
  }

  public getMetrics() {
    return {
      renderTimes: new Map(this.renderTimes),
      componentCounts: new Map(this.componentCounts),
    };
  }
}

// Singleton instances
export const webVitalsMonitor = new WebVitalsMonitor();
export const performanceBudget = new PerformanceBudget();
export const reactPerformanceMonitor = new ReactPerformanceMonitor();

// Utility functions
export function measurePageLoadTime(): number {
  const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
  return navigation ? navigation.loadEventEnd - navigation.fetchStart : 0;
}

export function measureDOMContentLoadedTime(): number {
  const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
  return navigation ? navigation.domContentLoadedEventEnd - navigation.fetchStart : 0;
}

export function measureResourceLoadTime(resourceName: string): number {
  const resources = performance.getEntriesByName(resourceName);
  if (resources.length > 0) {
    const resource = resources[0] as PerformanceResourceTiming;
    return resource.responseEnd - resource.startTime;
  }
  return 0;
}

export function getMemoryUsage(): any {
  if ('memory' in performance) {
    return (performance as any).memory;
  }
  return null;
}

export function clearPerformanceMetrics() {
  if (performance.clearMarks) {
    performance.clearMarks();
  }
  if (performance.clearMeasures) {
    performance.clearMeasures();
  }
}

// Performance Hook for React
export function usePerformanceMonitoring(componentName: string) {
  const renderStart = performance.now();
  
  return {
    endRender: () => {
      const renderTime = performance.now() - renderStart;
      reactPerformanceMonitor.reportRenderTime(componentName, renderTime);
    },
    recordMetric: (metricName: string, value: number) => {
      performance.mark(`${componentName}-${metricName}`);
      performance.measure(`${componentName}-${metricName}`, `${componentName}-${metricName}`);
    },
  };
}

// Initialize monitoring when module is loaded
if (typeof window !== 'undefined') {
  webVitalsMonitor.onReport((report) => {
    const budgetCheck = performanceBudget.checkBudget(
      new Map(Object.entries(report).filter(([key, value]) => value !== null))
    );
    
    if (!budgetCheck.passed) {
      console.warn('[Performance Budget] Violations detected:', budgetCheck.violations);
    }
  });
}