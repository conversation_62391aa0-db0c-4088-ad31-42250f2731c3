/**
 * API Integration Test Utilities
 * Shared test data and validation functions
 */

/**
 * Test Data Sets for Different Scenarios
 */
export const testScenarios = {
  smartTierBangalore: {
    builtUpArea: 1000,
    floors: 0,
    qualityTier: 'smart',
    location: 'bangalore',
    hasStilt: false,
    parkingType: 'open',
    hasBasement: false,
    specialFeatures: [],
    expectedRange: { min: 1600000, max: 2000000 }
  },
  premiumTierMumbai: {
    builtUpArea: 2500,
    floors: 1,
    qualityTier: 'premium',
    location: 'mumbai',
    hasStilt: true,
    parkingType: 'covered',
    hasBasement: false,
    specialFeatures: [],
    expectedRange: { min: 5500000, max: 7000000 }
  },
  luxuryTierDelhi: {
    builtUpArea: 5000,
    floors: 2,
    qualityTier: 'luxury',
    location: 'delhi',
    hasStilt: false,
    parkingType: 'covered',
    hasBasement: true,
    specialFeatures: [],
    expectedRange: { min: 15000000, max: 20000000 }
  },
  edgeCaseMinimum: {
    builtUpArea: 300,
    floors: 0,
    qualityTier: 'smart',
    location: 'bhubaneswar',
    hasStilt: false,
    parkingType: 'none',
    hasBasement: false,
    specialFeatures: [],
    expectedRange: { min: 480000, max: 600000 }
  },
  edgeCaseMaximum: {
    builtUpArea: 10000,
    floors: 3,
    qualityTier: 'luxury',
    location: 'mumbai',
    hasStilt: true,
    parkingType: 'covered',
    hasBasement: true,
    specialFeatures: [],
    expectedRange: { min: 30000000, max: 50000000 }
  }
};

/**
 * Validation Error Test Cases
 */
export const validationTestCases = {
  missingRequiredFields: {
    // Missing builtUpArea
    floors: 1,
    qualityTier: 'smart',
    location: 'bangalore'
  },
  invalidBuiltUpArea: {
    builtUpArea: -100,
    floors: 1,
    qualityTier: 'smart',
    location: 'bangalore'
  },
  invalidQualityTier: {
    builtUpArea: 1000,
    floors: 1,
    qualityTier: 'invalid_tier',
    location: 'bangalore'
  },
  invalidLocation: {
    builtUpArea: 1000,
    floors: 1,
    qualityTier: 'smart',
    location: 'invalid_city'
  },
  tooLargeArea: {
    builtUpArea: 100000,
    floors: 1,
    qualityTier: 'smart',
    location: 'bangalore'
  }
};

/**
 * Validation Functions
 */
export function validateCalculationResult(result, expectedRange, inputData) {
  const errors = [];

  // Basic structure validation
  if (!result) {
    errors.push('Result is null or undefined');
    return errors;
  }

  // Required fields validation
  const requiredFields = ['totalCost', 'costPerSqft', 'breakdown', 'materials', 'timeline', 'summary'];
  for (const field of requiredFields) {
    if (!(field in result)) {
      errors.push(`Missing required field: ${field}`);
    }
  }

  // Cost validation
  if (result.totalCost < expectedRange.min || result.totalCost > expectedRange.max) {
    errors.push(`Total cost ${result.totalCost} outside expected range ${expectedRange.min}-${expectedRange.max}`);
  }

  // Cost per sqft validation
  const expectedCostPerSqft = result.totalCost / (inputData.builtUpArea * (inputData.floors + 1));
  const actualCostPerSqft = result.costPerSqft;
  const tolerance = expectedCostPerSqft * 0.05; // 5% tolerance

  if (Math.abs(actualCostPerSqft - expectedCostPerSqft) > tolerance) {
    errors.push(`Cost per sqft calculation mismatch: expected ~${Math.round(expectedCostPerSqft)}, got ${actualCostPerSqft}`);
  }

  // Breakdown validation
  if (result.breakdown) {
    const breakdownTotal =
      result.breakdown.structure.amount +
      result.breakdown.finishing.amount +
      result.breakdown.mep.amount +
      result.breakdown.external.amount +
      result.breakdown.other.amount;

    const tolerance = result.totalCost * 0.01; // 1% tolerance
    if (Math.abs(breakdownTotal - result.totalCost) > tolerance) {
      errors.push(`Breakdown total ${breakdownTotal} doesn't match total cost ${result.totalCost}`);
    }

    // Percentage validation
    const totalPercentage =
      result.breakdown.structure.percentage +
      result.breakdown.finishing.percentage +
      result.breakdown.mep.percentage +
      result.breakdown.external.percentage +
      result.breakdown.other.percentage;

    if (Math.abs(totalPercentage - 100) > 1) {
      errors.push(`Breakdown percentages total ${totalPercentage}%, should be ~100%`);
    }
  }

  // Summary validation
  if (result.summary) {
    if (result.summary.totalCost !== result.totalCost) {
      errors.push('Summary total cost mismatch');
    }
    if (result.summary.costPerSqft !== result.costPerSqft) {
      errors.push('Summary cost per sqft mismatch');
    }
    if (result.summary.qualityTier !== inputData.qualityTier) {
      errors.push('Summary quality tier mismatch');
    }
    if (result.summary.location !== inputData.location) {
      errors.push('Summary location mismatch');
    }
  }

  return errors;
}

export function measurePerformance(startTime, endTime) {
  const duration = endTime - startTime;
  return {
    duration,
    withinSLA: duration < 500, // 500ms SLA
    performance: duration < 200 ? 'excellent' : duration < 500 ? 'good' : 'poor'
  };
}