# 🔌 API Documentation

*Comprehensive guide to Nirmaan AI Construction Calculator API*

## 🎯 Overview

The Nirmaan AI API provides programmatic access to our construction cost calculation engine, material database, and project management features. Built with RESTful principles and designed for developers, it enables seamless integration with your existing systems.

### Base URL
```
https://api.nirmaan-ai.com/v1
```

### Authentication
All API requests require authentication using API keys:
```bash
Authorization: Bearer YOUR_API_KEY
```

## 🚀 Getting Started

### API Key Generation

1. **Sign up** for a Nirmaan AI account
2. **Navigate** to Dashboard → API Keys
3. **Generate** a new API key
4. **Copy** and securely store your key

### Quick Start Example

```javascript
// Calculate construction cost
const response = await fetch('https://api.nirmaan-ai.com/v1/calculate', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer YOUR_API_KEY',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    location: 'bangalore',
    area: 1500,
    quality_tier: 'premium'
  })
});

const result = await response.json();
console.log(result.total_cost); // ₹37,50,000
```

## 📊 Core Endpoints

### 🧮 Cost Calculation

**POST /calculate**

Calculate construction costs for a project.

**Request Body:**
```json
{
  "location": "bangalore",
  "area": 1500,
  "quality_tier": "premium",
  "project_type": "residential",
  "floors": 2,
  "additional_features": ["parking", "compound_wall"]
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "total_cost": 3750000,
    "cost_per_sqft": 2500,
    "breakdown": {
      "structure": {
        "amount": 1312500,
        "percentage": 35
      },
      "finishing": {
        "amount": 1125000,
        "percentage": 30
      },
      "mep": {
        "amount": 750000,
        "percentage": 20
      },
      "external": {
        "amount": 375000,
        "percentage": 10
      },
      "other": {
        "amount": 187500,
        "percentage": 5
      }
    },
    "materials": [
      {
        "name": "Cement",
        "quantity": 750,
        "unit": "bags",
        "rate": 350,
        "amount": 262500
      }
    ],
    "timeline": {
      "total_months": 12,
      "phases": [
        {
          "name": "Foundation",
          "duration": 2,
          "cost": 562500
        }
      ]
    }
  }
}
```

**Parameters:**
- `location` (required): City name or code
- `area` (required): Built-up area in square feet
- `quality_tier` (required): `smart`, `premium`, or `luxury`
- `project_type` (optional): `residential`, `commercial`, `industrial`
- `floors` (optional): Number of floors (default: 1)
- `additional_features` (optional): Array of additional features

### 📋 Project Management

**GET /projects**

Retrieve all projects for authenticated user.

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "proj_123",
      "name": "Mumbai Apartment",
      "location": "mumbai",
      "area": 1200,
      "quality_tier": "premium",
      "total_cost": 3000000,
      "created_at": "2024-01-15T10:30:00Z",
      "updated_at": "2024-01-15T10:30:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "per_page": 20,
    "total": 5,
    "total_pages": 1
  }
}
```

**POST /projects**

Create a new project.

**Request Body:**
```json
{
  "name": "Delhi Villa",
  "location": "delhi",
  "area": 2500,
  "quality_tier": "luxury",
  "description": "3BHK luxury villa with modern amenities"
}
```

**GET /projects/{id}**

Get specific project details.

**PUT /projects/{id}**

Update project information.

**DELETE /projects/{id}**

Delete a project.

### 🏗️ Materials Database

**GET /materials**

Retrieve materials catalog.

**Query Parameters:**
- `category` (optional): Filter by category
- `location` (optional): Location-specific pricing
- `search` (optional): Search by material name

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "mat_001",
      "name": "Portland Cement",
      "category": "cement",
      "unit": "bag",
      "specifications": {
        "grade": "53",
        "weight": "50kg",
        "standard": "IS 12269"
      },
      "pricing": {
        "mumbai": {
          "retail": 400,
          "wholesale": 350,
          "bulk": 320
        },
        "delhi": {
          "retail": 380,
          "wholesale": 330,
          "bulk": 300
        }
      },
      "updated_at": "2024-01-15T10:30:00Z"
    }
  ]
}
```

**GET /materials/{id}**

Get specific material details.

**GET /materials/categories**

Get all material categories.

### 🌍 Location Data

**GET /locations**

Get supported locations.

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "code": "mumbai",
      "name": "Mumbai",
      "state": "Maharashtra",
      "tier": "metro",
      "cost_multiplier": 1.2,
      "labor_rate": 500,
      "supported_features": ["all"]
    }
  ]
}
```

**GET /locations/{code}/pricing**

Get location-specific pricing factors.

### 📈 Market Data

**GET /market/trends**

Get market trends and predictions.

**Response:**
```json
{
  "success": true,
  "data": {
    "current_month": {
      "average_cost_per_sqft": 2200,
      "growth_rate": 3.5,
      "inflation_factor": 1.05
    },
    "trends": [
      {
        "month": "2024-01",
        "average_cost": 2100,
        "cement_price": 350,
        "steel_price": 60
      }
    ],
    "predictions": {
      "next_quarter": {
        "expected_growth": 2.8,
        "price_range": {
          "min": 2150,
          "max": 2300
        }
      }
    }
  }
}
```

**GET /market/materials/{material_id}/history**

Get price history for specific material.

## 🔧 Advanced Features

### 📊 Batch Processing

**POST /batch/calculate**

Calculate multiple projects in one request.

**Request Body:**
```json
{
  "projects": [
    {
      "id": "calc_1",
      "location": "mumbai",
      "area": 1000,
      "quality_tier": "smart"
    },
    {
      "id": "calc_2", 
      "location": "delhi",
      "area": 1500,
      "quality_tier": "premium"
    }
  ]
}
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "calc_1",
      "total_cost": 1800000,
      "cost_per_sqft": 1800
    },
    {
      "id": "calc_2",
      "total_cost": 3750000,
      "cost_per_sqft": 2500
    }
  ]
}
```

### 📄 Report Generation

**POST /reports/generate**

Generate PDF reports.

**Request Body:**
```json
{
  "project_id": "proj_123",
  "format": "pdf",
  "sections": ["summary", "breakdown", "materials", "timeline"],
  "branding": {
    "company_name": "XYZ Constructions",
    "logo_url": "https://example.com/logo.png"
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "report_id": "rpt_456",
    "download_url": "https://api.nirmaan-ai.com/v1/reports/rpt_456/download",
    "expires_at": "2024-01-16T10:30:00Z"
  }
}
```

### 🔍 Search & Filtering

**GET /search**

Global search across projects and materials.

**Query Parameters:**
- `q` (required): Search query
- `type` (optional): `projects`, `materials`, `locations`
- `limit` (optional): Number of results (default: 10)

### 📊 Analytics

**GET /analytics/usage**

Get API usage statistics.

**Response:**
```json
{
  "success": true,
  "data": {
    "current_month": {
      "requests": 1500,
      "quota": 5000,
      "remaining": 3500
    },
    "daily_usage": [
      {
        "date": "2024-01-15",
        "requests": 50,
        "calculations": 35
      }
    ]
  }
}
```

## 📝 Webhooks

### Setting up Webhooks

Configure webhooks to receive real-time notifications.

**POST /webhooks**

Create a webhook endpoint.

**Request Body:**
```json
{
  "url": "https://your-app.com/webhook",
  "events": ["calculation.completed", "project.created"],
  "secret": "your_webhook_secret"
}
```

### Webhook Events

**calculation.completed**
```json
{
  "event": "calculation.completed",
  "data": {
    "calculation_id": "calc_123",
    "project_id": "proj_456",
    "total_cost": 2500000,
    "timestamp": "2024-01-15T10:30:00Z"
  }
}
```

**project.created**
```json
{
  "event": "project.created",
  "data": {
    "project_id": "proj_789",
    "name": "New Project",
    "location": "bangalore",
    "timestamp": "2024-01-15T10:30:00Z"
  }
}
```

## 🔒 Authentication & Security

### API Key Types

**Free Tier**
- 100 requests/month
- Basic calculation endpoints
- No webhooks

**Premium Tier**
- 5,000 requests/month
- All endpoints access
- Webhook support
- Priority support

**Enterprise Tier**
- Unlimited requests
- Custom rate limits
- Dedicated support
- SLA guarantees

### Security Best Practices

1. **Store API keys securely**
2. **Use HTTPS only**
3. **Implement rate limiting**
4. **Validate webhook signatures**
5. **Monitor API usage**

### Rate Limiting

**Headers:**
```
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1609459200
```

**Rate Limit Exceeded:**
```json
{
  "error": {
    "code": "rate_limit_exceeded",
    "message": "Rate limit exceeded. Try again later.",
    "retry_after": 60
  }
}
```

## 📚 SDKs & Libraries

### JavaScript/Node.js

```javascript
npm install @nirmaan-ai/sdk

const NirmaanAI = require('@nirmaan-ai/sdk');
const client = new NirmaanAI('YOUR_API_KEY');

// Calculate cost
const result = await client.calculate({
  location: 'bangalore',
  area: 1500,
  quality_tier: 'premium'
});
```

### Python

```python
pip install nirmaan-ai-python

from nirmaan_ai import NirmaanAI

client = NirmaanAI('YOUR_API_KEY')

# Calculate cost
result = client.calculate(
    location='bangalore',
    area=1500,
    quality_tier='premium'
)
```

### PHP

```php
composer require nirmaan-ai/php-sdk

use NirmaanAI\Client;

$client = new Client('YOUR_API_KEY');

// Calculate cost
$result = $client->calculate([
    'location' => 'bangalore',
    'area' => 1500,
    'quality_tier' => 'premium'
]);
```

## 🚨 Error Handling

### Error Response Format

```json
{
  "success": false,
  "error": {
    "code": "invalid_location",
    "message": "The specified location is not supported",
    "details": {
      "supported_locations": ["mumbai", "delhi", "bangalore"]
    }
  }
}
```

### Common Error Codes

| Code | HTTP Status | Description |
|------|-------------|-------------|
| `invalid_api_key` | 401 | Invalid or missing API key |
| `rate_limit_exceeded` | 429 | Rate limit exceeded |
| `invalid_location` | 400 | Unsupported location |
| `invalid_quality_tier` | 400 | Invalid quality tier |
| `insufficient_quota` | 402 | API quota exceeded |
| `server_error` | 500 | Internal server error |

## 📈 Usage Examples

### Integration Examples

**1. Cost Comparison Tool**
```javascript
// Compare costs across quality tiers
const tiers = ['smart', 'premium', 'luxury'];
const results = await Promise.all(
  tiers.map(tier => 
    client.calculate({
      location: 'mumbai',
      area: 1000,
      quality_tier: tier
    })
  )
);
```

**2. Project Dashboard**
```javascript
// Get project overview
const projects = await client.getProjects();
const totalValue = projects.reduce((sum, p) => sum + p.total_cost, 0);
```

**3. Material Price Monitoring**
```javascript
// Monitor cement prices
const cement = await client.getMaterial('cement');
const priceHistory = await client.getMaterialHistory('cement');
```

## 🔧 Testing

### Sandbox Environment

Base URL: `https://api-sandbox.nirmaan-ai.com/v1`

Use sandbox for testing without affecting production data.

### Test API Keys

```
Test Key: test_sk_123456789
Webhook Test: https://webhook.site/your-unique-url
```

### Mock Data

Sandbox returns consistent mock data for testing:
- Mumbai 1000 sqft premium: ₹25,00,000
- Delhi 1500 sqft luxury: ₹52,50,000
- Bangalore 800 sqft smart: ₹14,40,000

## 📞 Support

### Developer Support

- **Email**: <EMAIL>
- **Discord**: [Join our developer community](https://discord.gg/nirmaan-ai)
- **GitHub**: [API Issues](https://github.com/nirmaan-ai/api-issues)

### Response Times

- **Free Tier**: 48 hours
- **Premium**: 24 hours
- **Enterprise**: 4 hours

### Documentation Updates

API documentation is versioned and updated regularly:
- **Changelog**: Track API changes
- **Migration Guides**: Upgrade assistance
- **Deprecation Notices**: Advance warning

---

## 🎯 API Roadmap

### Q3 2025
- **GraphQL Support**: Alternative query language
- **Real-time Subscriptions**: WebSocket connections
- **Bulk Operations**: Enhanced batch processing
- **Advanced Analytics**: Machine learning insights

### Q4 2025
- **Multi-language SDKs**: Java, Go, Ruby support
- **API Gateway**: Enhanced routing and caching
- **Custom Webhooks**: User-defined event triggers
- **Data Export**: Bulk data export capabilities

---

*This API documentation is regularly updated. For the latest version, visit our [developer portal](https://developers.nirmaan-ai.com)*