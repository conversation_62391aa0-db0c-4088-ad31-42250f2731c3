/**
 * Comprehensive Security Headers Configuration
 * Production-ready security headers for Nirmaan AI Construction Calculator
 */

interface SecurityHeadersConfig {
  environment: 'development' | 'production' | 'staging';
  domain: string;
  enableHSTS: boolean;
  enableCSP: boolean;
  reportUri?: string;
  nonce?: string;
}

interface CSPDirectives {
  'default-src': string[];
  'script-src': string[];
  'style-src': string[];
  'img-src': string[];
  'font-src': string[];
  'connect-src': string[];
  'media-src': string[];
  'object-src': string[];
  'child-src': string[];
  'worker-src': string[];
  'frame-src': string[];
  'form-action': string[];
  'base-uri': string[];
  'manifest-src': string[];
  'frame-ancestors': string[];
  'report-uri'?: string[];
  'report-to'?: string;
  'upgrade-insecure-requests'?: boolean;
  'block-all-mixed-content'?: boolean;
}

export class SecurityHeaders {
  private config: SecurityHeadersConfig;
  private nonce: string | null = null;

  constructor(config: Partial<SecurityHeadersConfig> = {}) {
    this.config = {
      environment: process.env.NODE_ENV === 'production' ? 'production' : 'development',
      domain: process.env.NEXT_PUBLIC_DOMAIN || 'localhost:3000',
      enableHSTS: process.env.NODE_ENV === 'production',
      enableCSP: true,
      reportUri: process.env.CSP_REPORT_URI,
      ...config,
    };
  }

  /**
   * Generate a cryptographically secure nonce for inline scripts/styles
   */
  generateNonce(): string {
    if (typeof crypto !== 'undefined' && crypto.getRandomValues) {
      const array = new Uint8Array(16);
      crypto.getRandomValues(array);
      this.nonce = Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
    } else {
      // Fallback for environments without crypto.getRandomValues
      this.nonce = Math.random().toString(36).substring(2, 15) + 
                   Math.random().toString(36).substring(2, 15);
    }
    return this.nonce;
  }

  /**
   * Get current nonce
   */
  getNonce(): string | null {
    return this.nonce;
  }

  /**
   * Build Content Security Policy header value
   */
  private buildCSP(): string {
    const directives: CSPDirectives = {
      'default-src': ["'self'"],
      'script-src': [
        "'self'",
        "'unsafe-inline'", // Required for Next.js in development
        "'unsafe-eval'", // Required for Next.js hot reload
        'https://js.stripe.com',
        'https://checkout.stripe.com',
        'https://www.google-analytics.com',
        'https://www.googletagmanager.com',
        'https://connect.facebook.net',
        'https://snap.licdn.com',
        ...(this.nonce ? [`'nonce-${this.nonce}'`] : []),
      ],
      'style-src': [
        "'self'",
        "'unsafe-inline'", // Required for styled-components and CSS-in-JS
        'https://fonts.googleapis.com',
        'https://cdn.jsdelivr.net',
        ...(this.nonce ? [`'nonce-${this.nonce}'`] : []),
      ],
      'img-src': [
        "'self'",
        'data:',
        'blob:',
        'https:',
        'https://images.unsplash.com',
        'https://res.cloudinary.com',
        'https://www.google-analytics.com',
        'https://www.facebook.com',
        'https://px.ads.linkedin.com',
      ],
      'font-src': [
        "'self'",
        'https://fonts.gstatic.com',
        'https://cdn.jsdelivr.net',
        'data:',
      ],
      'connect-src': [
        "'self'",
        'https://*.supabase.co',
        'wss://*.supabase.co',
        'https://api.stripe.com',
        'https://checkout.stripe.com',
        'https://www.google-analytics.com',
        'https://analytics.google.com',
        'https://stats.g.doubleclick.net',
        'https://www.facebook.com',
        'https://connect.facebook.net',
        'https://api.linkedin.com',
        'wss://localhost:*', // For development hot reload
      ],
      'media-src': [
        "'self'",
        'https:',
        'data:',
        'blob:',
      ],
      'object-src': ["'none'"],
      'child-src': [
        "'self'",
        'https://js.stripe.com',
        'https://checkout.stripe.com',
      ],
      'worker-src': [
        "'self'",
        'blob:',
      ],
      'frame-src': [
        "'self'",
        'https://js.stripe.com',
        'https://checkout.stripe.com',
        'https://www.youtube.com',
        'https://player.vimeo.com',
      ],
      'form-action': [
        "'self'",
        'https://checkout.stripe.com',
      ],
      'base-uri': ["'self'"],
      'manifest-src': ["'self'"],
      'frame-ancestors': ["'none'"],
    };

    // Add report-uri if configured
    if (this.config.reportUri) {
      directives['report-uri'] = [this.config.reportUri];
      directives['report-to'] = 'csp-endpoint';
    }

    // Add upgrade-insecure-requests for production
    if (this.config.environment === 'production') {
      directives['upgrade-insecure-requests'] = true;
      directives['block-all-mixed-content'] = true;
    }

    // Build CSP string
    const cspParts: string[] = [];
    
    Object.entries(directives).forEach(([directive, values]) => {
      if (typeof values === 'boolean' && values) {
        cspParts.push(directive);
      } else if (Array.isArray(values) && values.length > 0) {
        cspParts.push(`${directive} ${values.join(' ')}`);
      } else if (typeof values === 'string') {
        cspParts.push(`${directive} ${values}`);
      }
    });

    return cspParts.join('; ');
  }

  /**
   * Get comprehensive security headers
   */
  getSecurityHeaders(): Record<string, string> {
    const headers: Record<string, string> = {
      // Content Security Policy
      'Content-Security-Policy': this.buildCSP(),
      
      // Prevent MIME type sniffing
      'X-Content-Type-Options': 'nosniff',
      
      // Prevent clickjacking
      'X-Frame-Options': 'DENY',
      
      // XSS Protection (legacy browsers)
      'X-XSS-Protection': '1; mode=block',
      
      // Referrer Policy
      'Referrer-Policy': 'strict-origin-when-cross-origin',
      
      // Permissions Policy (Feature Policy)
      'Permissions-Policy': [
        'camera=()',
        'microphone=()',
        'geolocation=(self)',
        'payment=(self)',
        'usb=()',
        'magnetometer=()',
        'gyroscope=()',
        'accelerometer=()',
        'ambient-light-sensor=()',
        'autoplay=(self)',
        'battery=()',
        'bluetooth=()',
        'display-capture=()',
        'document-domain=()',
        'encrypted-media=(self)',
        'fullscreen=(self)',
        'gamepad=()',
        'midi=()',
        'nfc=()',
        'notifications=(self)',
        'persistent-storage=()',
        'picture-in-picture=(self)',
        'publickey-credentials-get=(self)',
        'screen-wake-lock=()',
        'serial=()',
        'speaker-selection=()',
        'sync-xhr=()',
        'web-share=(self)',
        'xr-spatial-tracking=()',
      ].join(', '),
      
      // Cross-Origin Policies
      'Cross-Origin-Embedder-Policy': 'credentialless',
      'Cross-Origin-Opener-Policy': 'same-origin',
      'Cross-Origin-Resource-Policy': 'same-origin',
      
      // Disable DNS prefetching for external domains
      'X-DNS-Prefetch-Control': 'off',
      
      // Prevent IE from executing downloads
      'X-Download-Options': 'noopen',
      
      // Prevent Adobe Flash and PDF plugins
      'X-Permitted-Cross-Domain-Policies': 'none',
      
      // Server information disclosure
      'Server': 'Nirmaan-Security/1.0',
      
      // Cache control for sensitive content
      'Cache-Control': 'private, no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0',
    };

    // Add HSTS for production with HTTPS
    if (this.config.enableHSTS && this.config.environment === 'production') {
      headers['Strict-Transport-Security'] = 'max-age=63072000; includeSubDomains; preload';
    }

    // Add Network Error Logging
    headers['NEL'] = JSON.stringify({
      report_to: 'network-errors',
      max_age: 86400,
      include_subdomains: true,
      success_fraction: 0.01,
      failure_fraction: 1.0,
    });

    // Add Clear Site Data header for logout endpoints
    if (this.config.environment === 'production') {
      headers['Clear-Site-Data'] = '"cache", "cookies", "storage", "executionContexts"';
    }

    // Add Report-To header for CSP reporting
    if (this.config.reportUri) {
      headers['Report-To'] = JSON.stringify({
        group: 'csp-endpoint',
        max_age: 10886400,
        endpoints: [{ url: this.config.reportUri }],
        include_subdomains: true,
      });
    }

    // Add timing control headers
    headers['Timing-Allow-Origin'] = "'self'";
    
    // Add origin trial headers for new web features
    headers['Origin-Trial'] = '';

    return headers;
  }

  /**
   * Get security headers optimized for API endpoints
   */
  getAPISecurityHeaders(): Record<string, string> {
    return {
      'Content-Security-Policy': "default-src 'none'",
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'DENY',
      'X-XSS-Protection': '1; mode=block',
      'Referrer-Policy': 'no-referrer',
      'Cross-Origin-Resource-Policy': 'same-origin',
      'Access-Control-Allow-Origin': this.config.environment === 'production' 
        ? `https://${this.config.domain}` 
        : '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With, X-CSRF-Token',
      'Access-Control-Allow-Credentials': 'true',
      'Access-Control-Max-Age': '86400',
      'Vary': 'Origin, Access-Control-Request-Method, Access-Control-Request-Headers',
    };
  }

  /**
   * Get development-specific headers
   */
  getDevelopmentHeaders(): Record<string, string> {
    if (this.config.environment !== 'development') {
      return {};
    }

    return {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': '*',
      'Access-Control-Allow-Headers': '*',
    };
  }

  /**
   * Apply security headers to a Response object
   */
  applyHeaders(response: Response, isAPI: boolean = false): Response {
    const headers = isAPI 
      ? { ...this.getAPISecurityHeaders(), ...this.getDevelopmentHeaders() }
      : { ...this.getSecurityHeaders(), ...this.getDevelopmentHeaders() };

    Object.entries(headers).forEach(([key, value]) => {
      if (value) {
        response.headers.set(key, value);
      }
    });

    return response;
  }

  /**
   * Validate CSP compliance for inline content
   */
  validateInlineContent(content: string, type: 'script' | 'style'): {
    isValid: boolean;
    issues: string[];
    recommendations: string[];
  } {
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Check for dangerous patterns
    const dangerousPatterns = {
      script: [
        /eval\s*\(/i,
        /Function\s*\(/i,
        /setTimeout\s*\(\s*["']/i,
        /setInterval\s*\(\s*["']/i,
        /innerHTML\s*=/i,
        /outerHTML\s*=/i,
        /document\.write/i,
        /document\.writeln/i,
      ],
      style: [
        /javascript\s*:/i,
        /expression\s*\(/i,
        /url\s*\(\s*["']?javascript:/i,
        /@import.*javascript:/i,
      ],
    };

    const patterns = dangerousPatterns[type] || [];
    patterns.forEach((pattern, index) => {
      if (pattern.test(content)) {
        issues.push(`Detected potentially unsafe ${type} pattern #${index + 1}`);
      }
    });

    // Recommendations
    if (type === 'script') {
      recommendations.push('Use external script files with integrity hashes');
      recommendations.push('Implement nonce-based CSP for unavoidable inline scripts');
      recommendations.push('Consider using Web Workers for complex computations');
    } else {
      recommendations.push('Use external CSS files or CSS-in-JS with nonce');
      recommendations.push('Avoid inline styles for dynamic content');
    }

    return {
      isValid: issues.length === 0,
      issues,
      recommendations,
    };
  }
}

// Default instance
export const securityHeaders = new SecurityHeaders();

// Helper function for middleware
export function getSecurityHeadersForEnvironment(): Record<string, string> {
  const headers = new SecurityHeaders();
  return headers.getSecurityHeaders();
}

// Export types
export type { SecurityHeadersConfig, CSPDirectives };