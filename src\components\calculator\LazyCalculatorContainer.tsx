'use client';

import { Suspense, lazy } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';

// Lazy load the heavy calculator components
const EnhancedCalculatorContainer = lazy(() => 
  import('./EnhancedCalculatorContainer').then(module => ({
    default: module.EnhancedCalculatorContainer
  }))
);

const ResultsDisplay = lazy(() => 
  import('./ResultsDisplay').then(module => ({
    default: module.ResultsDisplay
  }))
);

const QualityTierSelector = lazy(() => 
  import('./QualityTierSelector').then(module => ({
    default: module.QualityTierSelector
  }))
);

const LocationSelector = lazy(() => 
  import('./LocationSelector').then(module => ({
    default: module.LocationSelector
  }))
);

// Lightweight loading skeleton
const CalculatorSkeleton = () => (
  <div className="max-w-4xl mx-auto space-y-8">
    <Card>
      <CardContent className="pt-6">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <Skeleton className="h-6 w-32" />
            <Skeleton className="h-6 w-24" />
          </div>
          <div className="space-y-2">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
          </div>
          <div className="flex justify-between">
            <Skeleton className="h-10 w-20" />
            <Skeleton className="h-10 w-20" />
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
);

// Error boundary for lazy loading
const LazyErrorBoundary = ({ children }: { children: React.ReactNode }) => (
  <Suspense fallback={<CalculatorSkeleton />}>
    {children}
  </Suspense>
);

export function LazyCalculatorContainer() {
  return (
    <LazyErrorBoundary>
      <EnhancedCalculatorContainer />
    </LazyErrorBoundary>
  );
}

// Export individual lazy components for advanced usage
export const LazyQualityTierSelector = ({ ...props }) => (
  <Suspense fallback={<Skeleton className="h-20 w-full" />}>
    <QualityTierSelector {...props} />
  </Suspense>
);

export const LazyLocationSelector = ({ ...props }) => (
  <Suspense fallback={<Skeleton className="h-12 w-full" />}>
    <LocationSelector {...props} />
  </Suspense>
);

export const LazyResultsDisplay = ({ ...props }) => (
  <Suspense fallback={<CalculatorSkeleton />}>
    <ResultsDisplay {...props} />
  </Suspense>
);

export default LazyCalculatorContainer;