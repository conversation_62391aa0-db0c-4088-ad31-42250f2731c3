import { NextRequest, NextResponse } from 'next/server';
import { withPerformanceMonitoring } from '@/lib/performance/monitoring';

interface CustomMetric {
  name: string;
  value: number;
  unit?: string;
  timestamp: number;
  url: string;
  route: string;
  userAgent: string;
  sessionId?: string;
  userId?: string;
  metadata?: any;
}

// In-memory storage for development
const customMetricsStore: CustomMetric[] = [];
const MAX_CUSTOM_METRICS = 5000;

export async function POST(request: NextRequest) {
  return withPerformanceMonitoring(async () => {
    try {
      const body = await request.json();
      const { name, value, unit, timestamp, url, route, userAgent, sessionId, userId, metadata } = body;

      if (!name || typeof value !== 'number') {
        return NextResponse.json(
          { error: 'Invalid custom metric data' },
          { status: 400 }
        );
      }

      const customMetric: CustomMetric = {
        name,
        value,
        unit,
        timestamp: timestamp || Date.now(),
        url: url || request.url,
        route: route || new URL(request.url).pathname,
        userAgent: userAgent || request.headers.get('user-agent') || '',
        sessionId,
        userId,
        metadata
      };

      // Store custom metric
      customMetricsStore.push(customMetric);

      // Keep only recent metrics
      if (customMetricsStore.length > MAX_CUSTOM_METRICS) {
        customMetricsStore.splice(0, customMetricsStore.length - MAX_CUSTOM_METRICS);
      }

      // Log in development
      if (process.env.NODE_ENV === 'development') {
        console.log(`[Custom Metric] ${name}: ${value}${unit || ''}`);
      }

      // Process metric-specific logic
      await processCustomMetric(customMetric);

      return NextResponse.json(
        { status: 'received', timestamp: Date.now() },
        { status: 200 }
      );
    } catch (error) {
      console.error('Custom metrics error:', error);
      return NextResponse.json(
        { error: 'Failed to process custom metric' },
        { status: 500 }
      );
    }
  });
}

export async function GET(request: NextRequest) {
  return withPerformanceMonitoring(async () => {
    try {
      const { searchParams } = new URL(request.url);
      const name = searchParams.get('name');
      const startTime = searchParams.get('start') ? parseInt(searchParams.get('start')!) : Date.now() - 3600000;
      const endTime = searchParams.get('end') ? parseInt(searchParams.get('end')!) : Date.now();
      const limit = searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : 100;
      const groupBy = searchParams.get('groupBy') || 'none';

      // Filter metrics
      let filteredMetrics = customMetricsStore.filter(m => 
        m.timestamp >= startTime && m.timestamp <= endTime
      );

      if (name) {
        filteredMetrics = filteredMetrics.filter(m => m.name === name);
      }

      // Sort by timestamp (newest first)
      filteredMetrics.sort((a, b) => b.timestamp - a.timestamp);

      // Limit results
      filteredMetrics = filteredMetrics.slice(0, limit);

      // Group metrics if requested
      const groupedMetrics = groupBy !== 'none' ? groupMetrics(filteredMetrics, groupBy) : null;

      // Calculate statistics
      const stats = calculateCustomMetricStats(filteredMetrics);

      return NextResponse.json({
        metrics: filteredMetrics,
        groupedMetrics,
        stats,
        totalCount: filteredMetrics.length,
        timestamp: Date.now()
      });
    } catch (error) {
      console.error('Custom metrics retrieval error:', error);
      return NextResponse.json(
        { error: 'Failed to retrieve custom metrics' },
        { status: 500 }
      );
    }
  });
}

async function processCustomMetric(metric: CustomMetric) {
  // Process specific metric types
  switch (metric.name) {
    case 'calculation-time':
      await processCalculationTime(metric);
      break;
    case 'page-load-time':
      await processPageLoadTime(metric);
      break;
    case 'user-interaction-time':
      await processUserInteractionTime(metric);
      break;
    case 'api-response-time':
      await processApiResponseTime(metric);
      break;
    case 'render-time':
      await processRenderTime(metric);
      break;
    default:
      // Generic processing
      await processGenericMetric(metric);
  }
}

async function processCalculationTime(metric: CustomMetric) {
  // Alert if calculation takes too long
  if (metric.value > 5000) { // 5 seconds
    console.warn(`[Performance Alert] Slow calculation: ${metric.value}ms`);
    
    // In production, send alert
    if (process.env.NODE_ENV === 'production') {
      await sendSlowCalculationAlert(metric);
    }
  }
}

async function processPageLoadTime(metric: CustomMetric) {
  // Alert if page load is too slow
  if (metric.value > 10000) { // 10 seconds
    console.warn(`[Performance Alert] Slow page load: ${metric.value}ms`);
    
    // In production, send alert
    if (process.env.NODE_ENV === 'production') {
      await sendSlowPageLoadAlert(metric);
    }
  }
}

async function processUserInteractionTime(metric: CustomMetric) {
  // Alert if user interaction is too slow
  if (metric.value > 1000) { // 1 second
    console.warn(`[Performance Alert] Slow user interaction: ${metric.value}ms`);
  }
}

async function processApiResponseTime(metric: CustomMetric) {
  // Alert if API response is too slow
  if (metric.value > 2000) { // 2 seconds
    console.warn(`[Performance Alert] Slow API response: ${metric.value}ms`);
    
    // In production, send alert
    if (process.env.NODE_ENV === 'production') {
      await sendSlowApiAlert(metric);
    }
  }
}

async function processRenderTime(metric: CustomMetric) {
  // Alert if render time is too slow
  if (metric.value > 50) { // 50ms
    console.warn(`[Performance Alert] Slow render: ${metric.value}ms`);
  }
}

async function processGenericMetric(metric: CustomMetric) {
  // Generic processing for unknown metrics
  console.log(`[Custom Metric] ${metric.name}: ${metric.value}${metric.unit || ''}`);
}

async function sendSlowCalculationAlert(metric: CustomMetric) {
  // Send alert for slow calculations
  console.log('Sending slow calculation alert:', metric);
}

async function sendSlowPageLoadAlert(metric: CustomMetric) {
  // Send alert for slow page loads
  console.log('Sending slow page load alert:', metric);
}

async function sendSlowApiAlert(metric: CustomMetric) {
  // Send alert for slow API responses
  console.log('Sending slow API alert:', metric);
}

function groupMetrics(metrics: CustomMetric[], groupBy: string): Record<string, CustomMetric[]> {
  const grouped: Record<string, CustomMetric[]> = {};

  for (const metric of metrics) {
    let key: string;
    
    switch (groupBy) {
      case 'name':
        key = metric.name;
        break;
      case 'route':
        key = metric.route;
        break;
      case 'hour':
        key = new Date(metric.timestamp).toISOString().slice(0, 13);
        break;
      case 'day':
        key = new Date(metric.timestamp).toISOString().slice(0, 10);
        break;
      case 'userId':
        key = metric.userId || 'anonymous';
        break;
      default:
        key = 'all';
    }

    if (!grouped[key]) {
      grouped[key] = [];
    }
    grouped[key].push(metric);
  }

  return grouped;
}

function calculateCustomMetricStats(metrics: CustomMetric[]) {
  if (metrics.length === 0) {
    return {
      count: 0,
      average: 0,
      median: 0,
      p95: 0,
      p99: 0,
      min: 0,
      max: 0,
      byName: {}
    };
  }

  const values = metrics.map(m => m.value).sort((a, b) => a - b);
  const count = values.length;
  const sum = values.reduce((a, b) => a + b, 0);
  const average = sum / count;
  const median = values[Math.floor(count / 2)];
  const p95 = values[Math.floor(count * 0.95)];
  const p99 = values[Math.floor(count * 0.99)];
  const min = values[0];
  const max = values[count - 1];

  // Calculate stats by metric name
  const byName: Record<string, any> = {};
  const metricsByName = groupMetrics(metrics, 'name');
  
  for (const [name, nameMetrics] of Object.entries(metricsByName)) {
    const nameValues = nameMetrics.map(m => m.value).sort((a, b) => a - b);
    const nameCount = nameValues.length;
    const nameSum = nameValues.reduce((a, b) => a + b, 0);
    
    byName[name] = {
      count: nameCount,
      average: Math.round((nameSum / nameCount) * 100) / 100,
      median: nameValues[Math.floor(nameCount / 2)],
      p95: nameValues[Math.floor(nameCount * 0.95)],
      p99: nameValues[Math.floor(nameCount * 0.99)],
      min: nameValues[0],
      max: nameValues[nameCount - 1]
    };
  }

  return {
    count,
    average: Math.round(average * 100) / 100,
    median,
    p95,
    p99,
    min,
    max,
    byName
  };
}