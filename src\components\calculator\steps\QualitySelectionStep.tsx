/**
 * Quality Selection Step Component - Enhanced Visual Interface
 * Comprehensive material and quality selection with visual cards, brand options, and real-time pricing
 */

import React, { useEffect, useState, useMemo } from 'react';
import { motion, type Variants } from 'framer-motion';
import { 
  Crown, Star, Sparkles, Check, Info, Palette, Home, 
  Zap, ChefHat, Droplets, Paintbrush, Package, TrendingUp,
  Grid3x3, LayoutGrid, ChevronRight, Award, Heart,
  BadgeCheck, Cpu, Lightbulb, Wrench, Settings
} from 'lucide-react';
import { EnhancedCard } from '@/components/ui/enhanced-card';
import { EnhancedButton } from '@/components/ui/enhanced-button';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { isMobileViewport } from '@/lib/mobile';
import { 
  StepComponentProps, 
  StepValidation,
  QUALITY_OPTIONS
} from '../types/wizard';

// Animation variants
const containerVariants: Variants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.4,
      staggerChildren: 0.1,
    },
  },
  exit: {
    opacity: 0,
    y: -20,
    transition: { duration: 0.3 },
  },
};

const cardVariants: Variants = {
  hidden: { opacity: 0, scale: 0.95 },
  visible: { 
    opacity: 1, 
    scale: 1,
    transition: { duration: 0.3 }
  },
};

const fieldVariants: Variants = {
  hidden: { opacity: 0, x: -20 },
  visible: { opacity: 1, x: 0 },
};

export function QualitySelectionStep({
  data,
  updateData,
  errors,
  onValidation,
  isActive,
}: StepComponentProps) {
  const [localErrors, setLocalErrors] = useState<Record<string, string>>({});
  const [isMobile, setIsMobile] = useState(false);

  // Initialize mobile detection
  useEffect(() => {
    setIsMobile(isMobileViewport());
    const handleResize = () => setIsMobile(isMobileViewport());
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Validation function
  const validateStep = (): StepValidation => {
    const stepErrors: Record<string, string> = {};
    
    // Quality validation
    if (!data.quality) {
      stepErrors.quality = 'Please select a quality tier';
    }

    setLocalErrors(stepErrors);
    
    const validation: StepValidation = {
      isValid: Object.keys(stepErrors).length === 0,
      errors: stepErrors,
    };

    if (onValidation) {
      onValidation(validation);
    }

    return validation;
  };

  // Trigger validation when data changes
  useEffect(() => {
    if (isActive) {
      validateStep();
    }
  }, [data, isActive]);

  // Helper function to handle input changes
  const handleInputChange = (field: string, value: string) => {
    updateData({ [field]: value });
    
    // Clear error when user starts typing
    if (localErrors[field]) {
      setLocalErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  // Material and quality data for enhanced selection
  const qualityTiers = [
    {
      id: 'smart',
      title: 'Smart Choice',
      price: '₹1,800/sqft',
      priceRange: '₹1,600-2,000/sqft',
      value: 'smart',
      badge: 'Best Value',
      icon: Star,
      color: 'blue',
      features: [
        'M20 concrete grade',
        'Standard finishes',
        'Cera/Parryware fixtures',
        'Basic electrical fittings',
        'Standard tiles & paint',
        '2-year warranty'
      ],
      savings: '20% cost efficient',
    },
    {
      id: 'premium',
      title: 'Premium Selection',
      price: '₹2,500/sqft',
      priceRange: '₹2,200-2,800/sqft',
      value: 'premium',
      popular: true,
      badge: 'Most Popular',
      icon: Sparkles,
      color: 'emerald',
      features: [
        'M25 concrete grade',
        'Branded materials',
        'Kohler/Grohe fixtures',
        'Premium electrical fittings',
        'Designer tiles & textures',
        '5-year warranty'
      ],
      savings: 'Perfect balance',
    },
    {
      id: 'luxury',
      title: 'Luxury Collection',
      price: '₹3,500/sqft',
      priceRange: '₹3,000+/sqft',
      value: 'luxury',
      badge: 'Premium Quality',
      icon: Crown,
      color: 'violet',
      features: [
        'M30+ concrete grade',
        'International brands',
        'Imported fixtures',
        'Home automation ready',
        'Premium finishes & materials',
        '10-year warranty'
      ],
      savings: 'Lifetime value',
    },
  ];

  // Flooring options with visual representations
  const flooringOptions = [
    {
      id: 'ceramic',
      name: 'Ceramic Tiles',
      price: '₹45-80/sqft',
      image: '🏺',
      description: 'Durable and affordable',
      pros: ['Water resistant', 'Easy maintenance', 'Budget-friendly'],
      quality: ['smart', 'premium'],
      popular: true,
    },
    {
      id: 'vitrified',
      name: 'Vitrified Tiles',
      price: '₹60-120/sqft',
      image: '✨',
      description: 'Premium finish with durability',
      pros: ['Scratch resistant', 'Low absorption', 'Elegant look'],
      quality: ['premium', 'luxury'],
    },
    {
      id: 'marble',
      name: 'Marble Flooring',
      price: '₹150-400/sqft',
      image: '🏛️',
      description: 'Luxurious natural stone',
      pros: ['Premium appearance', 'Natural patterns', 'Heat resistant'],
      quality: ['luxury'],
    },
    {
      id: 'granite',
      name: 'Granite Flooring',
      price: '₹120-300/sqft',
      image: '🪨',
      description: 'Strong and elegant',
      pros: ['Extremely durable', 'Stain resistant', 'Polished finish'],
      quality: ['premium', 'luxury'],
    },
    {
      id: 'wooden',
      name: 'Wooden Flooring',
      price: '₹200-800/sqft',
      image: '🪵',
      description: 'Warm and premium feel',
      pros: ['Natural warmth', 'Sound insulation', 'Premium appeal'],
      quality: ['luxury'],
    },
  ];

  // Bathroom fixture packages
  const bathroomPackages = [
    {
      id: 'cera',
      brand: 'Cera',
      price: '₹25,000-40,000',
      image: '🚿',
      includes: ['Wash basin', 'Toilet seat', 'Shower set', 'Faucets'],
      warranty: '2 years',
      quality: ['smart'],
      rating: 4.2,
    },
    {
      id: 'kohler',
      brand: 'Kohler',
      price: '₹45,000-70,000',
      image: '🏺',
      includes: ['Premium wash basin', 'Smart toilet seat', 'Rain shower', 'Designer faucets'],
      warranty: '5 years',
      quality: ['premium'],
      popular: true,
      rating: 4.6,
    },
    {
      id: 'grohe',
      brand: 'Grohe',
      price: '₹80,000-150,000',
      image: '💎',
      includes: ['Designer basin', 'Smart toilet', 'Thermostatic shower', 'Premium fixtures'],
      warranty: '10 years',
      quality: ['luxury'],
      rating: 4.8,
    },
  ];

  // Kitchen layout options
  const kitchenLayouts = [
    {
      id: 'straight',
      name: 'Straight Kitchen',
      price: '₹1.5-3L',
      image: '📏',
      description: 'Ideal for compact spaces',
      pros: ['Space efficient', 'Cost effective', 'Easy workflow'],
      suitableFor: ['1-2 BHK', 'Small families'],
    },
    {
      id: 'l-shaped',
      name: 'L-Shaped Kitchen',
      price: '₹2-4L',
      image: '📐',
      description: 'Corner utilization with good storage',
      pros: ['Better storage', 'Corner utilization', 'Open feel'],
      suitableFor: ['2-3 BHK', 'Medium families'],
      popular: true,
    },
    {
      id: 'u-shaped',
      name: 'U-Shaped Kitchen',
      price: '₹3-6L',
      image: '🅿️',
      description: 'Maximum storage and counter space',
      pros: ['Maximum storage', 'Multiple work zones', 'Efficient workflow'],
      suitableFor: ['3+ BHK', 'Large families'],
    },
    {
      id: 'island',
      name: 'Island Kitchen',
      price: '₹5-12L',
      image: '🏝️',
      description: 'Luxury kitchen with central island',
      pros: ['Premium appeal', 'Entertainment space', 'Additional storage'],
      suitableFor: ['4+ BHK', 'Large spaces'],
    },
  ];

  // Paint and finish options
  const paintBrands = [
    {
      id: 'asian-paints',
      name: 'Asian Paints',
      price: '₹180-350/L',
      image: '🎨',
      quality: 'Premium',
      warranty: '7 years',
      popular: true,
    },
    {
      id: 'berger',
      name: 'Berger Paints',
      price: '₹150-300/L',
      image: '🌈',
      quality: 'Good',
      warranty: '5 years',
    },
    {
      id: 'dulux',
      name: 'Dulux',
      price: '₹200-400/L',
      image: '✨',
      quality: 'Premium',
      warranty: '8 years',
    },
  ];

  // Electrical packages
  const electricalPackages = [
    {
      id: 'basic',
      name: 'Basic Package',
      price: '₹800-1,200/point',
      image: '💡',
      includes: ['Standard switches', 'Basic wiring', 'LED fittings'],
      brands: ['Anchor', 'Havells'],
      warranty: '2 years',
    },
    {
      id: 'premium',
      name: 'Premium Package',
      price: '₹1,200-2,000/point',
      image: '⚡',
      includes: ['Premium switches', 'Modular fittings', 'Designer lights'],
      brands: ['Legrand', 'Schneider'],
      warranty: '5 years',
      popular: true,
    },
    {
      id: 'smart',
      name: 'Smart Home Package',
      price: '₹2,500-5,000/point',
      image: '🏡',
      includes: ['Smart switches', 'Automation ready', 'App control', 'Voice control'],
      brands: ['Philips Hue', 'Xiaomi'],
      warranty: '3 years',
    },
  ];

  // Calculate estimated costs based on selections
  const calculateEstimatedCost = useMemo(() => {
    const baseArea = parseInt(data.builtUpArea || '1000');
    const qualityMultiplier = data.quality === 'smart' ? 1800 : data.quality === 'premium' ? 2500 : 3500;
    const baseCost = baseArea * qualityMultiplier;
    
    // Add material upgrades cost
    let upgradeCost = 0;
    if (data.flooringType === 'marble') upgradeCost += baseArea * 100;
    if (data.flooringType === 'wooden') upgradeCost += baseArea * 150;
    if (data.bathroomFixtures === 'grohe') upgradeCost += 50000;
    
    return {
      baseCost,
      upgradeCost,
      totalCost: baseCost + upgradeCost,
      perSqft: Math.round((baseCost + upgradeCost) / baseArea),
    };
  }, [data.quality, data.builtUpArea, data.flooringType, data.bathroomFixtures]);

  // State for active selection categories
  const [activeCategory, setActiveCategory] = useState<string>('quality');
  const [selectedRoomForFlooring, setSelectedRoomForFlooring] = useState<string>('all');
  const [kitchenBudget, setKitchenBudget] = useState<number[]>([300000]);

  if (!isActive) return null;

  return (
    <motion.div
      className="space-y-8"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      exit="exit"
    >
      {/* Header */}
      <motion.div 
        className="text-center space-y-4"
        variants={fieldVariants}
      >
        <div className="flex items-center justify-center gap-2 text-primary-600">
          <Crown className="h-6 w-6" />
          <h2 className="text-2xl font-bold">Quality & Material Selection</h2>
        </div>
        <p className="text-secondary-600 max-w-2xl mx-auto">
          Choose premium materials and finishes that match your vision and budget. 
          Visual galleries help you make informed decisions.
        </p>
        
        {/* Cost Preview */}
        <div className="flex items-center justify-center gap-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-200">
          <div className="text-center">
            <p className="text-sm text-secondary-600">Estimated Cost</p>
            <p className="text-2xl font-bold text-primary-600">
              ₹{(calculateEstimatedCost.totalCost / 100000).toFixed(1)}L
            </p>
          </div>
          <div className="text-center">
            <p className="text-sm text-secondary-600">Per Sq.Ft</p>
            <p className="text-xl font-semibold text-secondary-700">
              ₹{calculateEstimatedCost.perSqft}
            </p>
          </div>
          <div className="text-center">
            <p className="text-sm text-secondary-600">Quality</p>
            <Badge variant="secondary" className="capitalize">
              {data.quality || 'Smart'}
            </Badge>
          </div>
        </div>

        {/* Category Navigation */}
        <div className="flex flex-wrap justify-center gap-2">
          {[
            { id: 'quality', label: 'Quality Tier', icon: Crown },
            { id: 'flooring', label: 'Flooring', icon: Grid3x3 },
            { id: 'bathroom', label: 'Bathroom', icon: Droplets },
            { id: 'kitchen', label: 'Kitchen', icon: ChefHat },
            { id: 'paint', label: 'Paint & Finish', icon: Paintbrush },
            { id: 'electrical', label: 'Electrical', icon: Zap },
          ].map((category) => {
            const Icon = category.icon;
            const isActive = activeCategory === category.id;
            return (
              <EnhancedButton
                key={category.id}
                variant={isActive ? 'primary' : 'ghost'}
                size="sm"
                leftIcon={<Icon className="h-4 w-4" />}
                onClick={() => setActiveCategory(category.id)}
                className={cn(
                  'transition-all duration-200',
                  !isActive && 'hover:bg-primary-50'
                )}
              >
                {category.label}
              </EnhancedButton>
            );
          })}
        </div>
      </motion.div>

      {/* Quality Tier Selection */}
      {activeCategory === 'quality' && (
        <motion.div 
          variants={cardVariants}
          initial="hidden"
          animate="visible"
          exit="hidden"
        >
          <EnhancedCard variant="outlined" size="lg">
            <div className="space-y-6">
              <div className="text-center">
                <h3 className="text-lg font-semibold mb-2 flex items-center justify-center gap-2">
                  <Crown className="h-5 w-5 text-primary-600" />
                  Select Quality Tier *
                </h3>
                <p className="text-sm text-secondary-600">
                  Compare features and pricing across different quality levels
                </p>
              </div>

              {/* Quality Grid */}
              <div className={cn(
                'grid gap-6',
                isMobile ? 'grid-cols-1' : 'grid-cols-3'
              )}>
                {qualityTiers.map((tier) => {
                  const isSelected = data.quality === tier.value;
                  const Icon = tier.icon;
                  
                  return (
                    <motion.div
                      key={tier.value}
                      className={cn(
                        'relative p-6 rounded-xl border-2 cursor-pointer transition-all duration-300',
                        isSelected 
                          ? 'border-primary-500 bg-primary-50 shadow-lg' 
                          : 'border-secondary-200 hover:border-primary-300 hover:shadow-md',
                        'group overflow-hidden'
                      )}
                      variants={fieldVariants}
                      whileHover={{ scale: 1.02, y: -4 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => handleInputChange('quality', tier.value)}
                    >
                      {/* Badge */}
                      <div className="absolute -top-2 left-1/2 transform -translate-x-1/2">
                        <Badge 
                          variant={tier.popular ? 'default' : 'secondary'}
                          className={cn(
                            'text-xs px-3 py-1',
                            tier.popular && 'bg-emerald-500 text-white'
                          )}
                        >
                          {tier.badge}
                        </Badge>
                      </div>

                      {/* Selected Indicator */}
                      {isSelected && (
                        <motion.div 
                          className="absolute top-4 right-4"
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          transition={{ type: "spring", duration: 0.3 }}
                        >
                          <div className="w-6 h-6 bg-primary-500 rounded-full flex items-center justify-center">
                            <Check className="h-4 w-4 text-white" />
                          </div>
                        </motion.div>
                      )}

                      <div className="space-y-4 mt-4">
                        {/* Header */}
                        <div className="text-center">
                          <Icon className={cn(
                            'h-10 w-10 mx-auto mb-3',
                            isSelected ? 'text-primary-600' : 'text-secondary-400'
                          )} />
                          <h4 className="font-bold text-xl mb-1">{tier.title}</h4>
                          <div className="space-y-1">
                            <p className="text-2xl font-bold text-primary-600">{tier.price}</p>
                            <p className="text-xs text-secondary-500">{tier.priceRange}</p>
                            <p className="text-sm text-secondary-600 font-medium">{tier.savings}</p>
                          </div>
                        </div>

                        {/* Features */}
                        <div className="space-y-2">
                          {tier.features.map((feature, idx) => (
                            <div key={idx} className="flex items-start gap-2 text-sm">
                              <Check className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                              <span className="text-secondary-700">{feature}</span>
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* Hover Gradient */}
                      <motion.div
                        className="absolute inset-0 bg-gradient-to-br from-primary-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                      />
                    </motion.div>
                  );
                })}
              </div>

              {(localErrors.quality || errors.quality) && (
                <p className="text-sm text-red-600 text-center">
                  {localErrors.quality || errors.quality}
                </p>
              )}
            </div>
          </EnhancedCard>
        </motion.div>
      )}

      {/* Flooring Selection */}
      {activeCategory === 'flooring' && (
        <motion.div 
          variants={cardVariants}
          initial="hidden"
          animate="visible"
          exit="hidden"
        >
          <EnhancedCard variant="outlined" size="lg">
            <div className="space-y-6">
              <div className="text-center">
                <h3 className="text-lg font-semibold mb-2 flex items-center justify-center gap-2">
                  <Grid3x3 className="h-5 w-5 text-primary-600" />
                  Flooring Selection
                </h3>
                <p className="text-sm text-secondary-600">
                  Choose flooring materials that match your quality tier and room requirements
                </p>
              </div>

              {/* Room Filter */}
              <div className="flex flex-wrap justify-center gap-2 p-3 bg-secondary-50 rounded-lg">
                <Label className="text-sm font-medium">Apply to:</Label>
                {['all', 'bedrooms', 'living', 'kitchen', 'bathrooms'].map((room) => (
                  <EnhancedButton
                    key={room}
                    variant={selectedRoomForFlooring === room ? 'primary' : 'ghost'}
                    size="sm"
                    onClick={() => setSelectedRoomForFlooring(room)}
                    className="capitalize"
                  >
                    {room === 'all' ? 'All Rooms' : room}
                  </EnhancedButton>
                ))}
              </div>

              {/* Flooring Options Grid */}
              <div className={cn(
                'grid gap-4',
                isMobile ? 'grid-cols-1' : 'grid-cols-2 lg:grid-cols-3'
              )}>
                {flooringOptions
                  .filter(option => 
                    !data.quality || option.quality.includes(data.quality)
                  )
                  .map((option) => {
                    const isSelected = data.flooringType === option.id;
                    
                    return (
                      <motion.div
                        key={option.id}
                        className={cn(
                          'relative p-4 rounded-lg border-2 cursor-pointer transition-all duration-200',
                          isSelected 
                            ? 'border-primary-500 bg-primary-50 shadow-md' 
                            : 'border-secondary-200 hover:border-primary-300 hover:shadow-sm',
                          'group'
                        )}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        onClick={() => handleInputChange('flooringType', option.id)}
                      >
                        {/* Popular Badge */}
                        {option.popular && (
                          <Badge className="absolute -top-2 -right-2 bg-green-500">
                            Popular
                          </Badge>
                        )}

                        {/* Selected Indicator */}
                        {isSelected && (
                          <div className="absolute top-2 right-2">
                            <div className="w-5 h-5 bg-primary-500 rounded-full flex items-center justify-center">
                              <Check className="h-3 w-3 text-white" />
                            </div>
                          </div>
                        )}

                        <div className="space-y-3">
                          {/* Material Preview */}
                          <div className="text-center">
                            <div className="text-4xl mb-2">{option.image}</div>
                            <h4 className="font-semibold">{option.name}</h4>
                            <p className="text-primary-600 font-medium text-sm">{option.price}</p>
                          </div>

                          {/* Description */}
                          <p className="text-xs text-secondary-600 text-center">
                            {option.description}
                          </p>

                          {/* Pros */}
                          <div className="space-y-1">
                            {option.pros.slice(0, 2).map((pro, idx) => (
                              <div key={idx} className="flex items-center gap-2 text-xs">
                                <Check className="h-3 w-3 text-green-500 flex-shrink-0" />
                                <span className="text-secondary-700">{pro}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      </motion.div>
                    );
                  })
                }
              </div>
            </div>
          </EnhancedCard>
        </motion.div>
      )}

      {/* Bathroom Selection */}
      {activeCategory === 'bathroom' && (
        <motion.div 
          variants={cardVariants}
          initial="hidden"
          animate="visible"
          exit="hidden"
        >
          <EnhancedCard variant="outlined" size="lg">
            <div className="space-y-6">
              <div className="text-center">
                <h3 className="text-lg font-semibold mb-2 flex items-center justify-center gap-2">
                  <Droplets className="h-5 w-5 text-primary-600" />
                  Bathroom Fixtures
                </h3>
                <p className="text-sm text-secondary-600">
                  Premium bathroom fixture packages from trusted brands
                </p>
              </div>

              {/* Bathroom Packages */}
              <div className={cn(
                'grid gap-6',
                isMobile ? 'grid-cols-1' : 'grid-cols-3'
              )}>
                {bathroomPackages.map((pkg) => {
                  const isSelected = data.bathroomFixtures === pkg.id;
                  const isCompatible = !data.quality || pkg.quality.includes(data.quality);
                  
                  return (
                    <motion.div
                      key={pkg.id}
                      className={cn(
                        'relative p-6 rounded-xl border-2 cursor-pointer transition-all duration-200',
                        isSelected 
                          ? 'border-primary-500 bg-primary-50 shadow-lg' 
                          : 'border-secondary-200 hover:border-primary-300 hover:shadow-md',
                        !isCompatible && 'opacity-50 cursor-not-allowed'
                      )}
                      whileHover={isCompatible ? { scale: 1.02, y: -2 } : {}}
                      whileTap={isCompatible ? { scale: 0.98 } : {}}
                      onClick={() => isCompatible && handleInputChange('bathroomFixtures', pkg.id)}
                    >
                      {/* Popular Badge */}
                      {pkg.popular && (
                        <Badge className="absolute -top-2 left-1/2 transform -translate-x-1/2 bg-blue-500">
                          Most Popular
                        </Badge>
                      )}

                      {/* Selected Indicator */}
                      {isSelected && (
                        <div className="absolute top-4 right-4">
                          <div className="w-6 h-6 bg-primary-500 rounded-full flex items-center justify-center">
                            <Check className="h-4 w-4 text-white" />
                          </div>
                        </div>
                      )}

                      <div className="space-y-4">
                        {/* Brand Header */}
                        <div className="text-center">
                          <div className="text-3xl mb-2">{pkg.image}</div>
                          <h4 className="font-bold text-xl">{pkg.brand}</h4>
                          <p className="text-primary-600 font-semibold">{pkg.price}</p>
                          <div className="flex items-center justify-center gap-1 mt-1">
                            <Star className="h-4 w-4 text-yellow-400 fill-current" />
                            <span className="text-sm text-secondary-600">{pkg.rating}</span>
                          </div>
                        </div>

                        {/* Includes */}
                        <div className="space-y-2">
                          <p className="text-sm font-medium text-secondary-700">Package Includes:</p>
                          {pkg.includes.map((item, idx) => (
                            <div key={idx} className="flex items-center gap-2 text-sm">
                              <Package className="h-3 w-3 text-green-500 flex-shrink-0" />
                              <span className="text-secondary-700">{item}</span>
                            </div>
                          ))}
                        </div>

                        {/* Warranty */}
                        <div className="flex items-center justify-center gap-2 text-sm">
                          <Award className="h-4 w-4 text-blue-500" />
                          <span className="text-secondary-600">{pkg.warranty} warranty</span>
                        </div>
                      </div>
                    </motion.div>
                  );
                })}
              </div>
            </div>
          </EnhancedCard>
        </motion.div>
      )}

      {/* Kitchen Selection */}
      {activeCategory === 'kitchen' && (
        <motion.div 
          variants={cardVariants}
          initial="hidden"
          animate="visible"
          exit="hidden"
        >
          <EnhancedCard variant="outlined" size="lg">
            <div className="space-y-6">
              <div className="text-center">
                <h3 className="text-lg font-semibold mb-2 flex items-center justify-center gap-2">
                  <ChefHat className="h-5 w-5 text-primary-600" />
                  Kitchen Configuration
                </h3>
                <p className="text-sm text-secondary-600">
                  Choose kitchen layout and set your budget for modular solutions
                </p>
              </div>

              {/* Budget Slider */}
              <div className="p-6 bg-gradient-to-r from-orange-50 to-red-50 rounded-xl border border-orange-200">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label className="text-lg font-semibold">Kitchen Budget</Label>
                    <div className="text-right">
                      <p className="text-2xl font-bold text-primary-600">
                        ₹{(kitchenBudget[0] / 100000).toFixed(1)}L
                      </p>
                      <p className="text-sm text-secondary-600">Total Investment</p>
                    </div>
                  </div>
                  <Slider
                    value={kitchenBudget}
                    onValueChange={setKitchenBudget}
                    max={1200000}
                    min={100000}
                    step={50000}
                    className="w-full"
                  />
                  <div className="flex justify-between text-sm text-secondary-600">
                    <span>₹1L</span>
                    <span>₹12L</span>
                  </div>
                </div>
              </div>

              {/* Kitchen Layouts */}
              <div className={cn(
                'grid gap-4',
                isMobile ? 'grid-cols-1' : 'grid-cols-2'
              )}>
                {kitchenLayouts.map((layout) => {
                  const isSelected = data.kitchenType === layout.id;
                  const isAffordable = kitchenBudget[0] >= parseInt(layout.price.split('-')[0].replace('₹', '').replace('L', '')) * 100000;
                  
                  return (
                    <motion.div
                      key={layout.id}
                      className={cn(
                        'relative p-6 rounded-xl border-2 cursor-pointer transition-all duration-200',
                        isSelected 
                          ? 'border-primary-500 bg-primary-50 shadow-lg' 
                          : 'border-secondary-200 hover:border-primary-300 hover:shadow-md',
                        !isAffordable && 'opacity-50 cursor-not-allowed'
                      )}
                      whileHover={isAffordable ? { scale: 1.02 } : {}}
                      whileTap={isAffordable ? { scale: 0.98 } : {}}
                      onClick={() => isAffordable && handleInputChange('kitchenType', layout.id)}
                    >
                      {/* Popular Badge */}
                      {layout.popular && (
                        <Badge className="absolute -top-2 right-4 bg-orange-500">
                          Popular
                        </Badge>
                      )}

                      {/* Selected Indicator */}
                      {isSelected && (
                        <div className="absolute top-4 right-4">
                          <div className="w-6 h-6 bg-primary-500 rounded-full flex items-center justify-center">
                            <Check className="h-4 w-4 text-white" />
                          </div>
                        </div>
                      )}

                      <div className="space-y-4">
                        {/* Layout Header */}
                        <div className="text-center">
                          <div className="text-4xl mb-3">{layout.image}</div>
                          <h4 className="font-bold text-lg">{layout.name}</h4>
                          <p className="text-primary-600 font-semibold">{layout.price}</p>
                          <p className="text-sm text-secondary-600 mt-1">
                            {layout.description}
                          </p>
                        </div>

                        {/* Pros */}
                        <div className="space-y-2">
                          {layout.pros.map((pro, idx) => (
                            <div key={idx} className="flex items-center gap-2 text-sm">
                              <Check className="h-3 w-3 text-green-500 flex-shrink-0" />
                              <span className="text-secondary-700">{pro}</span>
                            </div>
                          ))}
                        </div>

                        {/* Suitable For */}
                        <div className="pt-2 border-t border-secondary-200">
                          <p className="text-xs text-secondary-600 mb-1">Suitable for:</p>
                          <div className="flex flex-wrap gap-1">
                            {layout.suitableFor.map((suit, idx) => (
                              <Badge key={idx} variant="outline" className="text-xs">
                                {suit}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  );
                })}
              </div>
            </div>
          </EnhancedCard>
        </motion.div>
      )}

      {/* Paint & Finishes Selection */}
      {activeCategory === 'paint' && (
        <motion.div 
          variants={cardVariants}
          initial="hidden"
          animate="visible"
          exit="hidden"
        >
          <EnhancedCard variant="outlined" size="lg">
            <div className="space-y-6">
              <div className="text-center">
                <h3 className="text-lg font-semibold mb-2 flex items-center justify-center gap-2">
                  <Paintbrush className="h-5 w-5 text-primary-600" />
                  Paint & Finishes
                </h3>
                <p className="text-sm text-secondary-600">
                  Premium paint brands and color options for interior and exterior
                </p>
              </div>

              {/* Paint Brands */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {paintBrands.map((brand) => {
                  const isSelected = data.wallFinish === brand.id;
                  
                  return (
                    <motion.div
                      key={brand.id}
                      className={cn(
                        'relative p-4 rounded-lg border-2 cursor-pointer transition-all duration-200',
                        isSelected 
                          ? 'border-primary-500 bg-primary-50 shadow-md' 
                          : 'border-secondary-200 hover:border-primary-300 hover:shadow-sm'
                      )}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => handleInputChange('wallFinish', brand.id)}
                    >
                      {/* Popular Badge */}
                      {brand.popular && (
                        <Badge className="absolute -top-2 right-2 bg-purple-500">
                          Popular
                        </Badge>
                      )}

                      {/* Selected Indicator */}
                      {isSelected && (
                        <div className="absolute top-2 right-2">
                          <div className="w-5 h-5 bg-primary-500 rounded-full flex items-center justify-center">
                            <Check className="h-3 w-3 text-white" />
                          </div>
                        </div>
                      )}

                      <div className="space-y-3 text-center">
                        <div className="text-3xl">{brand.image}</div>
                        <h4 className="font-semibold">{brand.name}</h4>
                        <p className="text-primary-600 font-medium text-sm">{brand.price}</p>
                        <div className="space-y-1">
                          <Badge variant="outline" className="text-xs">
                            {brand.quality} Quality
                          </Badge>
                          <p className="text-xs text-secondary-600">{brand.warranty} warranty</p>
                        </div>
                      </div>
                    </motion.div>
                  );
                })}
              </div>

              {/* Color Palette Preview */}
              <div className="p-6 bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl border border-purple-200">
                <h4 className="font-semibold mb-4 text-center">Popular Color Combinations</h4>
                <div className="grid grid-cols-3 md:grid-cols-6 gap-3">
                  {[
                    { name: 'Classic White', color: '#FFFFFF', border: true },
                    { name: 'Warm Beige', color: '#F5F5DC' },
                    { name: 'Soft Grey', color: '#D3D3D3' },
                    { name: 'Ocean Blue', color: '#4682B4' },
                    { name: 'Sage Green', color: '#9CAF88' },
                    { name: 'Sunset Orange', color: '#FF6347' },
                  ].map((color, idx) => (
                    <div key={idx} className="text-center">
                      <div 
                        className={cn(
                          'w-12 h-12 rounded-lg mx-auto mb-2 shadow-sm',
                          color.border && 'border-2 border-secondary-300'
                        )}
                        style={{ backgroundColor: color.color }}
                      />
                      <p className="text-xs text-secondary-600">{color.name}</p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </EnhancedCard>
        </motion.div>
      )}

      {/* Electrical & Smart Features */}
      {activeCategory === 'electrical' && (
        <motion.div 
          variants={cardVariants}
          initial="hidden"
          animate="visible"
          exit="hidden"
        >
          <EnhancedCard variant="outlined" size="lg">
            <div className="space-y-6">
              <div className="text-center">
                <h3 className="text-lg font-semibold mb-2 flex items-center justify-center gap-2">
                  <Zap className="h-5 w-5 text-primary-600" />
                  Electrical & Smart Features
                </h3>
                <p className="text-sm text-secondary-600">
                  Choose electrical packages from basic to smart home automation
                </p>
              </div>

              {/* Electrical Packages */}
              <div className={cn(
                'grid gap-6',
                isMobile ? 'grid-cols-1' : 'grid-cols-3'
              )}>
                {electricalPackages.map((pkg) => {
                  const isSelected = data.electricalFittings === pkg.id;
                  
                  return (
                    <motion.div
                      key={pkg.id}
                      className={cn(
                        'relative p-6 rounded-xl border-2 cursor-pointer transition-all duration-200',
                        isSelected 
                          ? 'border-primary-500 bg-primary-50 shadow-lg' 
                          : 'border-secondary-200 hover:border-primary-300 hover:shadow-md'
                      )}
                      whileHover={{ scale: 1.02, y: -2 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => handleInputChange('electricalFittings', pkg.id)}
                    >
                      {/* Popular Badge */}
                      {pkg.popular && (
                        <Badge className="absolute -top-2 left-1/2 transform -translate-x-1/2 bg-yellow-500">
                          Recommended
                        </Badge>
                      )}

                      {/* Selected Indicator */}
                      {isSelected && (
                        <div className="absolute top-4 right-4">
                          <div className="w-6 h-6 bg-primary-500 rounded-full flex items-center justify-center">
                            <Check className="h-4 w-4 text-white" />
                          </div>
                        </div>
                      )}

                      <div className="space-y-4">
                        {/* Package Header */}
                        <div className="text-center">
                          <div className="text-4xl mb-3">{pkg.image}</div>
                          <h4 className="font-bold text-lg">{pkg.name}</h4>
                          <p className="text-primary-600 font-semibold">{pkg.price}</p>
                        </div>

                        {/* Includes */}
                        <div className="space-y-2">
                          {pkg.includes.map((item, idx) => (
                            <div key={idx} className="flex items-center gap-2 text-sm">
                              <Lightbulb className="h-3 w-3 text-yellow-500 flex-shrink-0" />
                              <span className="text-secondary-700">{item}</span>
                            </div>
                          ))}
                        </div>

                        {/* Brands */}
                        <div className="space-y-2">
                          <p className="text-sm font-medium text-secondary-700">Brands:</p>
                          <div className="flex flex-wrap gap-1">
                            {pkg.brands.map((brand, idx) => (
                              <Badge key={idx} variant="outline" className="text-xs">
                                {brand}
                              </Badge>
                            ))}
                          </div>
                        </div>

                        {/* Warranty */}
                        <div className="flex items-center justify-center gap-2 text-sm pt-2 border-t border-secondary-200">
                          <Settings className="h-4 w-4 text-blue-500" />
                          <span className="text-secondary-600">{pkg.warranty} warranty</span>
                        </div>
                      </div>
                    </motion.div>
                  );
                })}
              </div>

              {/* Smart Home ROI Calculator */}
              {data.electricalFittings === 'smart' && (
                <motion.div
                  className="p-6 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-200"
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  transition={{ duration: 0.3 }}
                >
                  <div className="text-center space-y-4">
                    <h4 className="font-semibold text-lg flex items-center justify-center gap-2">
                      <TrendingUp className="h-5 w-5 text-blue-600" />
                      Smart Home ROI Calculator
                    </h4>
                    <div className="grid grid-cols-3 gap-4">
                      <div className="text-center">
                        <p className="text-2xl font-bold text-green-600">25%</p>
                        <p className="text-sm text-secondary-600">Energy Savings</p>
                      </div>
                      <div className="text-center">
                        <p className="text-2xl font-bold text-blue-600">15%</p>
                        <p className="text-sm text-secondary-600">Property Value</p>
                      </div>
                      <div className="text-center">
                        <p className="text-2xl font-bold text-purple-600">3 Yrs</p>
                        <p className="text-sm text-secondary-600">Payback Period</p>
                      </div>
                    </div>
                  </div>
                </motion.div>
              )}
            </div>
          </EnhancedCard>
        </motion.div>
      )}
    </motion.div>
  );
}

export default QualitySelectionStep;