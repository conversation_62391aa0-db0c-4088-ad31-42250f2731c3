/**
 * Quality Selection Step Component - PLACEHOLDER
 * This component will be replaced by the Quality Selection Agent
 */

import React, { useEffect, useState } from 'react';
import { motion, type Variants } from 'framer-motion';
import { Crown, Star, Sparkles, Check, Info } from 'lucide-react';
import { EnhancedCard } from '@/components/ui/enhanced-card';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { cn } from '@/lib/utils';
import { isMobileViewport } from '@/lib/mobile';
import { QualityTierSelector } from '@/components/ui/swipeable-cards';
import { 
  StepComponentProps, 
  StepValidation,
  QUALITY_OPTIONS
} from '../types/wizard';

// Animation variants
const containerVariants: Variants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.4,
      staggerChildren: 0.1,
    },
  },
  exit: {
    opacity: 0,
    y: -20,
    transition: { duration: 0.3 },
  },
};

const cardVariants: Variants = {
  hidden: { opacity: 0, scale: 0.95 },
  visible: { 
    opacity: 1, 
    scale: 1,
    transition: { duration: 0.3 }
  },
};

const fieldVariants: Variants = {
  hidden: { opacity: 0, x: -20 },
  visible: { opacity: 1, x: 0 },
};

export function QualitySelectionStep({
  data,
  updateData,
  errors,
  onValidation,
  isActive,
}: StepComponentProps) {
  const [localErrors, setLocalErrors] = useState<Record<string, string>>({});
  const [isMobile, setIsMobile] = useState(false);

  // Initialize mobile detection
  useEffect(() => {
    setIsMobile(isMobileViewport());
    const handleResize = () => setIsMobile(isMobileViewport());
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Validation function
  const validateStep = (): StepValidation => {
    const stepErrors: Record<string, string> = {};
    
    // Quality validation
    if (!data.quality) {
      stepErrors.quality = 'Please select a quality tier';
    }

    setLocalErrors(stepErrors);
    
    const validation: StepValidation = {
      isValid: Object.keys(stepErrors).length === 0,
      errors: stepErrors,
    };

    if (onValidation) {
      onValidation(validation);
    }

    return validation;
  };

  // Trigger validation when data changes
  useEffect(() => {
    if (isActive) {
      validateStep();
    }
  }, [data, isActive]);

  // Helper function to handle input changes
  const handleInputChange = (field: string, value: string) => {
    updateData({ [field]: value });
    
    // Clear error when user starts typing
    if (localErrors[field]) {
      setLocalErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  // Quality tier data for mobile selector
  const qualityTiers = [
    {
      id: 'smart',
      title: 'Smart Choice',
      price: '₹1,600-2,000/sqft',
      value: 'smart',
      features: [
        'M20 concrete grade',
        'Standard finishes',
        'Cera/Parryware fixtures',
        'Basic electrical fittings',
        'Standard tiles & paint'
      ],
    },
    {
      id: 'premium',
      title: 'Premium Selection',
      price: '₹2,200-2,800/sqft',
      value: 'premium',
      popular: true,
      features: [
        'M25 concrete grade',
        'Branded materials',
        'Kohler/Grohe fixtures',
        'Premium electrical fittings',
        'Designer tiles & textures'
      ],
    },
    {
      id: 'luxury',
      title: 'Luxury Collection',
      price: '₹3,000+/sqft',
      value: 'luxury',
      features: [
        'M30+ concrete grade',
        'International brands',
        'Imported fixtures',
        'Home automation ready',
        'Premium finishes & materials'
      ],
    },
  ];

  if (!isActive) return null;

  return (
    <motion.div
      className="space-y-8"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      exit="exit"
    >
      {/* Header */}
      <motion.div 
        className="text-center space-y-2"
        variants={fieldVariants}
      >
        <div className="flex items-center justify-center gap-2 text-primary-600">
          <Crown className="h-6 w-6" />
          <h2 className="text-2xl font-bold">Quality & Finishes</h2>
        </div>
        <p className="text-secondary-600 max-w-2xl mx-auto">
          Choose the quality tier that matches your vision and budget. Each tier includes specific materials, finishes, and fixtures.
        </p>
        <div className="inline-flex items-center gap-2 px-3 py-1 bg-yellow-100 text-yellow-800 rounded-full text-sm">
          <span className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse"></span>
          PLACEHOLDER - Will be enhanced by Quality Selection Agent
        </div>
      </motion.div>

      {/* Quality Tier Selection */}
      <motion.div variants={cardVariants}>
        <EnhancedCard variant="outlined" size="lg">
          <div className="space-y-6">
            <div className="text-center">
              <h3 className="text-lg font-semibold mb-2">Select Quality Tier *</h3>
              <p className="text-sm text-secondary-600">
                Compare features and pricing across different quality levels
              </p>
            </div>

            {/* Mobile Quality Selector */}
            {isMobile ? (
              <QualityTierSelector
                tiers={qualityTiers}
                selectedTier={data.quality || ''}
                onTierSelect={(value) => handleInputChange('quality', value)}
              />
            ) : (
              /* Desktop Quality Grid */
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {QUALITY_OPTIONS.map((option, index) => {
                  const isSelected = data.quality === option.value;
                  const Icon = index === 0 ? Star : index === 1 ? Sparkles : Crown;
                  
                  return (
                    <motion.div
                      key={option.value}
                      className={cn(
                        'relative p-6 rounded-lg border-2 cursor-pointer transition-all duration-200',
                        isSelected 
                          ? 'border-primary-500 bg-primary-50 shadow-lg' 
                          : 'border-secondary-200 hover:border-primary-300 hover:shadow-md',
                        option.popular && 'ring-2 ring-blue-500 ring-opacity-50'
                      )}
                      variants={fieldVariants}
                      whileHover={{ scale: 1.02, y: -2 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => handleInputChange('quality', option.value)}
                    >
                      {/* Popular Badge */}
                      {option.popular && (
                        <div className="absolute -top-2 left-1/2 transform -translate-x-1/2">
                          <span className="bg-blue-500 text-white text-xs px-3 py-1 rounded-full">
                            Most Popular
                          </span>
                        </div>
                      )}

                      {/* Selected Indicator */}
                      {isSelected && (
                        <div className="absolute top-4 right-4">
                          <div className="w-6 h-6 bg-primary-500 rounded-full flex items-center justify-center">
                            <Check className="h-4 w-4 text-white" />
                          </div>
                        </div>
                      )}

                      <div className="space-y-4">
                        {/* Header */}
                        <div className="text-center">
                          <Icon className={cn(
                            'h-8 w-8 mx-auto mb-2',
                            isSelected ? 'text-primary-600' : 'text-secondary-400'
                          )} />
                          <h4 className="font-semibold text-lg">{option.label}</h4>
                          <p className="text-primary-600 font-medium">{option.price}</p>
                          <p className="text-sm text-secondary-600 mt-1">
                            {option.description}
                          </p>
                        </div>

                        {/* Features */}
                        <div className="space-y-2">
                          {qualityTiers.find(t => t.value === option.value)?.features.map((feature, idx) => (
                            <div key={idx} className="flex items-start gap-2 text-sm">
                              <Check className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                              <span className="text-secondary-700">{feature}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </motion.div>
                  );
                })}
              </div>
            )}

            {(localErrors.quality || errors.quality) && (
              <p className="text-sm text-red-600 text-center">
                {localErrors.quality || errors.quality}
              </p>
            )}
          </div>
        </EnhancedCard>
      </motion.div>

      {/* Additional Quality Options - Placeholder */}
      <motion.div variants={cardVariants}>
        <EnhancedCard 
          variant="outlined" 
          size="lg"
          header={
            <div className="flex items-center gap-2">
              <Sparkles className="h-5 w-5 text-primary-600" />
              <h3 className="text-lg font-semibold">Detailed Specifications</h3>
              <span className="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded">
                Coming Soon
              </span>
            </div>
          }
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Placeholder for detailed options */}
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="flooringType">Flooring Type</Label>
                <Select
                  value={data.flooringType || 'ceramic'}
                  onValueChange={(value) => handleInputChange('flooringType', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select flooring" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ceramic">Ceramic Tiles</SelectItem>
                    <SelectItem value="vitrified">Vitrified Tiles</SelectItem>
                    <SelectItem value="marble">Marble</SelectItem>
                    <SelectItem value="granite">Granite</SelectItem>
                    <SelectItem value="wooden">Wooden Flooring</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="wallFinish">Wall Finish</Label>
                <Select
                  value={data.wallFinish || 'paint'}
                  onValueChange={(value) => handleInputChange('wallFinish', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select wall finish" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="paint">Premium Paint</SelectItem>
                    <SelectItem value="wallpaper">Wallpaper</SelectItem>
                    <SelectItem value="texture">Texture Paint</SelectItem>
                    <SelectItem value="stones">Natural Stones</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="ceilingType">Ceiling Type</Label>
                <Select
                  value={data.ceilingType || 'false'}
                  onValueChange={(value) => handleInputChange('ceilingType', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select ceiling" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="false">False Ceiling</SelectItem>
                    <SelectItem value="gypsum">Gypsum Board</SelectItem>
                    <SelectItem value="wooden">Wooden Ceiling</SelectItem>
                    <SelectItem value="concrete">Exposed Concrete</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="kitchenType">Kitchen Type</Label>
                <Select
                  value={data.kitchenType || 'modular'}
                  onValueChange={(value) => handleInputChange('kitchenType', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select kitchen type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="modular">Modular Kitchen</SelectItem>
                    <SelectItem value="semi-modular">Semi-Modular</SelectItem>
                    <SelectItem value="traditional">Traditional</SelectItem>
                    <SelectItem value="island">Island Kitchen</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="bathroomFixtures">Bathroom Fixtures</Label>
                <Select
                  value={data.bathroomFixtures || 'standard'}
                  onValueChange={(value) => handleInputChange('bathroomFixtures', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select fixtures" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="standard">Standard Fixtures</SelectItem>
                    <SelectItem value="premium">Premium Fixtures</SelectItem>
                    <SelectItem value="luxury">Luxury Fixtures</SelectItem>
                    <SelectItem value="imported">Imported Fixtures</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="electricalFittings">Electrical Fittings</Label>
                <Select
                  value={data.electricalFittings || 'standard'}
                  onValueChange={(value) => handleInputChange('electricalFittings', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select electrical fittings" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="standard">Standard Fittings</SelectItem>
                    <SelectItem value="premium">Premium Fittings</SelectItem>
                    <SelectItem value="smart">Smart Home Ready</SelectItem>
                    <SelectItem value="automation">Full Automation</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
            <div className="flex items-start gap-3">
              <Info className="h-5 w-5 text-blue-600 mt-0.5" />
              <div className="text-sm">
                <p className="font-medium text-blue-900 mb-1">Coming Soon: Enhanced Quality Selection</p>
                <p className="text-blue-800">
                  The Quality Selection Agent will provide detailed material specifications, 
                  brand comparisons, and visual galleries to help you make informed choices.
                </p>
              </div>
            </div>
          </div>
        </EnhancedCard>
      </motion.div>
    </motion.div>
  );
}

export default QualitySelectionStep;