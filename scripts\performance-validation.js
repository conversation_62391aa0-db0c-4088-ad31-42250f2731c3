#!/usr/bin/env node

/**
 * Comprehensive Performance Validation Testing Script
 * 
 * This script performs extensive performance testing to validate all optimization implementations
 * in the Nirmaan AI Construction Calculator project.
 * 
 * Tests include:
 * 1. Bundle optimization and code splitting effectiveness
 * 2. Web Vitals monitoring and performance metrics
 * 3. Memory optimization and React performance
 * 4. Loading states and skeleton UI performance
 * 5. Mobile performance optimizations
 * 6. Caching strategies and CDN performance
 * 7. API response times and database query performance
 * 8. Image optimization and lazy loading
 * 9. Progressive Web App features
 * 10. Comprehensive performance reporting
 */

const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

// Performance testing configuration
const PERFORMANCE_CONFIG = {
  BUNDLE_SIZE_THRESHOLD: 3 * 1024 * 1024, // 3MB
  LIGHTHOUSE_MIN_SCORE: 85,
  MEMORY_THRESHOLD: 100 * 1024 * 1024, // 100MB
  LOAD_TIME_THRESHOLD: 3000, // 3 seconds
  API_RESPONSE_THRESHOLD: 500, // 500ms
  MOBILE_PERF_THRESHOLD: 80,
  PWA_MIN_SCORE: 75
};

const COLORS = {
  RED: '\x1b[31m',
  GREEN: '\x1b[32m',
  YELLOW: '\x1b[33m',
  BLUE: '\x1b[34m',
  MAGENTA: '\x1b[35m',
  CYAN: '\x1b[36m',
  WHITE: '\x1b[37m',
  RESET: '\x1b[0m',
  BOLD: '\x1b[1m',
  DIM: '\x1b[2m'
};

class PerformanceTester {
  constructor() {
    this.results = {
      bundleOptimization: {},
      webVitals: {},
      memoryOptimization: {},
      loadingStates: {},
      mobileOptimization: {},
      cachingStrategies: {},
      apiPerformance: {},
      imageOptimization: {},
      pwaFeatures: {},
      overallScore: 0,
      recommendations: []
    };
    this.startTime = Date.now();
    this.testCount = 0;
    this.passedTests = 0;
  }

  log(message, color = COLORS.WHITE) {
    console.log(`${color}${message}${COLORS.RESET}`);
  }

  logSection(title) {
    console.log('\n' + '='.repeat(80));
    this.log(`📊 ${title}`, COLORS.CYAN + COLORS.BOLD);
    console.log('='.repeat(80));
  }

  logTest(testName, status, details = '') {
    const icon = status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : '⚠️';
    const color = status === 'PASS' ? COLORS.GREEN : status === 'FAIL' ? COLORS.RED : COLORS.YELLOW;
    this.log(`${icon} ${testName}: ${color}${status}${COLORS.RESET} ${details}`);
    
    this.testCount++;
    if (status === 'PASS') this.passedTests++;
  }

  async runCommand(command, args = [], options = {}) {
    return new Promise((resolve, reject) => {
      const child = spawn(command, args, { ...options, stdio: 'pipe' });
      let stdout = '';
      let stderr = '';

      child.stdout?.on('data', (data) => {
        stdout += data.toString();
      });

      child.stderr?.on('data', (data) => {
        stderr += data.toString();
      });

      child.on('close', (code) => {
        resolve({ code, stdout, stderr });
      });

      child.on('error', reject);
    });
  }

  async checkFileExists(filePath) {
    try {
      await fs.promises.access(filePath);
      return true;
    } catch {
      return false;
    }
  }

  async getFileSize(filePath) {
    try {
      const stats = await fs.promises.stat(filePath);
      return stats.size;
    } catch {
      return 0;
    }
  }

  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  formatDuration(ms) {
    if (ms < 1000) return `${ms}ms`;
    return `${(ms / 1000).toFixed(1)}s`;
  }

  // Test 1: Bundle Optimization and Code Splitting
  async testBundleOptimization() {
    this.logSection('1. Bundle Optimization & Code Splitting Analysis');

    try {
      // Check if build exists
      const nextDir = path.join(process.cwd(), '.next');
      const buildExists = await this.checkFileExists(nextDir);
      
      if (!buildExists) {
        this.logTest('Build Directory', 'FAIL', 'No .next directory found');
        this.results.bundleOptimization.buildExists = false;
        return;
      }

      this.logTest('Build Directory', 'PASS', '.next directory exists');
      this.results.bundleOptimization.buildExists = true;

      // Analyze bundle sizes
      const staticDir = path.join(nextDir, 'static');
      const chunksDir = path.join(staticDir, 'chunks');
      
      if (await this.checkFileExists(chunksDir)) {
        const chunks = await fs.promises.readdir(chunksDir);
        const jsChunks = chunks.filter(file => file.endsWith('.js'));
        
        let totalSize = 0;
        const chunkSizes = [];
        
        for (const chunk of jsChunks) {
          const chunkPath = path.join(chunksDir, chunk);
          const size = await this.getFileSize(chunkPath);
          totalSize += size;
          chunkSizes.push({ name: chunk, size });
        }

        this.results.bundleOptimization.totalSize = totalSize;
        this.results.bundleOptimization.chunkCount = jsChunks.length;
        this.results.bundleOptimization.chunks = chunkSizes;

        const sizeStatus = totalSize < PERFORMANCE_CONFIG.BUNDLE_SIZE_THRESHOLD ? 'PASS' : 'FAIL';
        this.logTest('Bundle Size', sizeStatus, 
          `${this.formatBytes(totalSize)} (${jsChunks.length} chunks)`);

        // Check for code splitting effectiveness
        const hasCodeSplitting = jsChunks.length > 3; // At least vendor, main, and one page chunk
        this.logTest('Code Splitting', hasCodeSplitting ? 'PASS' : 'WARN', 
          `${jsChunks.length} chunks detected`);

        // Check for common optimization patterns
        const hasVendorChunk = jsChunks.some(chunk => chunk.includes('vendor') || chunk.includes('commons'));
        this.logTest('Vendor Chunk Separation', hasVendorChunk ? 'PASS' : 'WARN', 
          hasVendorChunk ? 'Vendor code properly separated' : 'No vendor chunk detected');

      } else {
        this.logTest('Chunk Analysis', 'FAIL', 'No chunks directory found');
      }

      // Test dynamic imports
      const srcDir = path.join(process.cwd(), 'src');
      const result = await this.runCommand('grep', ['-r', 'import(', srcDir]);
      const dynamicImportCount = result.stdout.split('\n').filter(line => 
        line.includes('import(') && !line.trim().startsWith('//')
      ).length;

      this.results.bundleOptimization.dynamicImports = dynamicImportCount;
      this.logTest('Dynamic Imports', dynamicImportCount > 0 ? 'PASS' : 'WARN', 
        `${dynamicImportCount} dynamic imports found`);

    } catch (error) {
      this.logTest('Bundle Analysis', 'FAIL', `Error: ${error.message}`);
      this.results.bundleOptimization.error = error.message;
    }
  }

  // Test 2: Web Vitals Monitoring
  async testWebVitalsMonitoring() {
    this.logSection('2. Web Vitals Monitoring & Performance Metrics');

    try {
      // Check for web-vitals implementation
      const webVitalsPath = path.join(process.cwd(), 'src/lib/performance/web-vitals.ts');
      const webVitalsExists = await this.checkFileExists(webVitalsPath);
      
      this.logTest('Web Vitals Module', webVitalsExists ? 'PASS' : 'FAIL', 
        webVitalsExists ? 'Performance monitoring implemented' : 'No web-vitals module found');

      if (webVitalsExists) {
        const webVitalsContent = await fs.promises.readFile(webVitalsPath, 'utf8');
        
        // Check for Core Web Vitals
        const vitalsChecks = [
          { name: 'LCP (Largest Contentful Paint)', pattern: /onLCP|getLCP/i },
          { name: 'FID (First Input Delay)', pattern: /onFID|getFID/i },
          { name: 'CLS (Cumulative Layout Shift)', pattern: /onCLS|getCLS/i },
          { name: 'FCP (First Contentful Paint)', pattern: /onFCP|getFCP/i },
          { name: 'TTFB (Time to First Byte)', pattern: /onTTFB|getTTFB/i },
          { name: 'INP (Interaction to Next Paint)', pattern: /onINP|getINP/i }
        ];

        this.results.webVitals.coreVitals = {};
        
        vitalsChecks.forEach(({ name, pattern }) => {
          const implemented = pattern.test(webVitalsContent);
          this.results.webVitals.coreVitals[name] = implemented;
          this.logTest(`Core Web Vital: ${name}`, implemented ? 'PASS' : 'WARN', 
            implemented ? 'Monitored' : 'Not implemented');
        });

        // Check for performance budgets
        const hasBudgets = /budget|threshold|limit/i.test(webVitalsContent);
        this.logTest('Performance Budgets', hasBudgets ? 'PASS' : 'WARN', 
          hasBudgets ? 'Performance budgets defined' : 'No budgets found');

        // Check for analytics integration
        const hasAnalytics = /analytics|gtag|ga\(|track/i.test(webVitalsContent);
        this.logTest('Analytics Integration', hasAnalytics ? 'PASS' : 'WARN', 
          hasAnalytics ? 'Analytics tracking enabled' : 'No analytics found');

        this.results.webVitals.implemented = true;
      }

      // Check performance API usage
      const perfApiResult = await this.runCommand('grep', ['-r', 'performance\\.', 'src/', '--include=*.ts', '--include=*.tsx']);
      const perfApiUsage = perfApiResult.stdout.split('\n').length > 1;
      this.logTest('Performance API Usage', perfApiUsage ? 'PASS' : 'WARN', 
        perfApiUsage ? 'Performance API utilized' : 'Limited Performance API usage');

    } catch (error) {
      this.logTest('Web Vitals Analysis', 'FAIL', `Error: ${error.message}`);
      this.results.webVitals.error = error.message;
    }
  }

  // Test 3: Memory Optimization and React Performance
  async testMemoryOptimization() {
    this.logSection('3. Memory Optimization & React Performance');

    try {
      // Check for React optimization patterns
      const srcDir = path.join(process.cwd(), 'src');
      
      // Test for React.memo usage
      const memoResult = await this.runCommand('grep', ['-r', 'React\\.memo\\|memo(', srcDir]);
      const memoUsage = memoResult.stdout.split('\n').filter(line => 
        line.includes('React.memo') || line.includes('memo(')
      ).length;
      
      this.logTest('React.memo Usage', memoUsage > 0 ? 'PASS' : 'WARN', 
        `${memoUsage} memoized components found`);

      // Test for useCallback usage
      const callbackResult = await this.runCommand('grep', ['-r', 'useCallback', srcDir]);
      const callbackUsage = callbackResult.stdout.split('\n').filter(line => 
        line.includes('useCallback') && !line.trim().startsWith('//')
      ).length;

      this.logTest('useCallback Usage', callbackUsage > 0 ? 'PASS' : 'WARN', 
        `${callbackUsage} useCallback instances found`);

      // Test for useMemo usage
      const memoHookResult = await this.runCommand('grep', ['-r', 'useMemo', srcDir]);
      const memoHookUsage = memoHookResult.stdout.split('\n').filter(line => 
        line.includes('useMemo') && !line.trim().startsWith('//')
      ).length;

      this.logTest('useMemo Usage', memoHookUsage > 0 ? 'PASS' : 'WARN', 
        `${memoHookUsage} useMemo instances found`);

      // Check for performance profiler
      const profilerPath = path.join(srcDir, 'lib/performance/react-profiler.ts');
      const profilerExists = await this.checkFileExists(profilerPath);
      this.logTest('React Profiler', profilerExists ? 'PASS' : 'WARN', 
        profilerExists ? 'Performance profiler implemented' : 'No profiler found');

      // Check for memory leak prevention
      const cleanupResult = await this.runCommand('grep', ['-r', 'useEffect.*return', srcDir]);
      const cleanupUsage = cleanupResult.stdout.split('\n').filter(line => 
        line.includes('useEffect') && line.includes('return')
      ).length;

      this.logTest('Effect Cleanup', cleanupUsage > 0 ? 'PASS' : 'WARN', 
        `${cleanupUsage} cleanup functions found`);

      this.results.memoryOptimization = {
        memoUsage,
        callbackUsage,
        memoHookUsage,
        cleanupUsage,
        profilerExists
      };

    } catch (error) {
      this.logTest('Memory Optimization Analysis', 'FAIL', `Error: ${error.message}`);
      this.results.memoryOptimization.error = error.message;
    }
  }

  // Test 4: Loading States and Skeleton UI Performance
  async testLoadingStates() {
    this.logSection('4. Loading States & Skeleton UI Performance');

    try {
      const srcDir = path.join(process.cwd(), 'src');

      // Check for skeleton components
      const skeletonResult = await this.runCommand('grep', ['-r', 'Skeleton\\|skeleton', srcDir, '--include=*.tsx']);
      const skeletonUsage = skeletonResult.stdout.split('\n').filter(line => 
        (line.includes('Skeleton') || line.includes('skeleton')) && !line.trim().startsWith('//')
      ).length;

      this.logTest('Skeleton UI Components', skeletonUsage > 0 ? 'PASS' : 'WARN', 
        `${skeletonUsage} skeleton implementations found`);

      // Check for loading states
      const loadingResult = await this.runCommand('grep', ['-r', 'loading\\|isLoading', srcDir, '--include=*.tsx']);
      const loadingUsage = loadingResult.stdout.split('\n').filter(line => 
        (line.includes('loading') || line.includes('isLoading')) && !line.trim().startsWith('//')
      ).length;

      this.logTest('Loading State Management', loadingUsage > 0 ? 'PASS' : 'WARN', 
        `${loadingUsage} loading state implementations`);

      // Check for Suspense usage
      const suspenseResult = await this.runCommand('grep', ['-r', 'Suspense', srcDir]);
      const suspenseUsage = suspenseResult.stdout.split('\n').filter(line => 
        line.includes('Suspense') && !line.trim().startsWith('//')
      ).length;

      this.logTest('React Suspense', suspenseUsage > 0 ? 'PASS' : 'WARN', 
        `${suspenseUsage} Suspense boundaries found`);

      // Check for progressive loading
      const progressiveResult = await this.runCommand('grep', ['-r', 'lazy\\|LazyLoad', srcDir]);
      const progressiveUsage = progressiveResult.stdout.split('\n').filter(line => 
        (line.includes('lazy') || line.includes('LazyLoad')) && !line.trim().startsWith('//')
      ).length;

      this.logTest('Progressive Loading', progressiveUsage > 0 ? 'PASS' : 'WARN', 
        `${progressiveUsage} lazy loading implementations`);

      this.results.loadingStates = {
        skeletonUsage,
        loadingUsage,
        suspenseUsage,
        progressiveUsage
      };

    } catch (error) {
      this.logTest('Loading States Analysis', 'FAIL', `Error: ${error.message}`);
      this.results.loadingStates.error = error.message;
    }
  }

  // Test 5: Mobile Performance Optimizations
  async testMobileOptimizations() {
    this.logSection('5. Mobile Performance Optimizations');

    try {
      const srcDir = path.join(process.cwd(), 'src');

      // Check for responsive design
      const responsiveResult = await this.runCommand('grep', ['-r', 'md:\\|lg:\\|sm:\\|mobile', srcDir]);
      const responsiveUsage = responsiveResult.stdout.split('\n').filter(line => 
        (line.includes('md:') || line.includes('lg:') || line.includes('sm:') || line.includes('mobile'))
      ).length;

      this.logTest('Responsive Design', responsiveUsage > 0 ? 'PASS' : 'WARN', 
        `${responsiveUsage} responsive implementations found`);

      // Check for touch optimization
      const touchResult = await this.runCommand('grep', ['-r', 'touch\\|gesture\\|swipe', srcDir]);
      const touchUsage = touchResult.stdout.split('\n').filter(line => 
        (line.includes('touch') || line.includes('gesture') || line.includes('swipe'))
      ).length;

      this.logTest('Touch Optimization', touchUsage > 0 ? 'PASS' : 'WARN', 
        `${touchUsage} touch/gesture implementations`);

      // Check for mobile-specific optimizations
      const mobileLibPath = path.join(srcDir, 'lib/mobile');
      const mobileLibExists = await this.checkFileExists(mobileLibPath);
      this.logTest('Mobile Library', mobileLibExists ? 'PASS' : 'WARN', 
        mobileLibExists ? 'Mobile optimization library exists' : 'No mobile library found');

      // Check for viewport meta tag in layout
      const layoutPath = path.join(srcDir, 'app/layout.tsx');
      if (await this.checkFileExists(layoutPath)) {
        const layoutContent = await fs.promises.readFile(layoutPath, 'utf8');
        const hasViewport = /viewport.*mobile/i.test(layoutContent);
        this.logTest('Viewport Configuration', hasViewport ? 'PASS' : 'WARN', 
          hasViewport ? 'Mobile viewport configured' : 'Viewport configuration not found');
      }

      // Check for image optimization
      const imageResult = await this.runCommand('grep', ['-r', 'next/image\\|Image', srcDir]);
      const imageOptUsage = imageResult.stdout.split('\n').filter(line => 
        line.includes('next/image') || line.includes('Image')
      ).length;

      this.logTest('Image Optimization', imageOptUsage > 0 ? 'PASS' : 'WARN', 
        `${imageOptUsage} optimized image implementations`);

      this.results.mobileOptimization = {
        responsiveUsage,
        touchUsage,
        mobileLibExists,
        imageOptUsage
      };

    } catch (error) {
      this.logTest('Mobile Optimization Analysis', 'FAIL', `Error: ${error.message}`);
      this.results.mobileOptimization.error = error.message;
    }
  }

  // Test 6: Caching Strategies and CDN Performance
  async testCachingStrategies() {
    this.logSection('6. Caching Strategies & CDN Performance');

    try {
      // Check Next.js configuration for caching
      const nextConfigPath = path.join(process.cwd(), 'next.config.ts');
      const nextConfigExists = await this.checkFileExists(nextConfigPath);

      if (nextConfigExists) {
        const nextConfig = await fs.promises.readFile(nextConfigPath, 'utf8');
        
        // Check for image optimization
        const hasImageConfig = /images\s*:/.test(nextConfig);
        this.logTest('Image Caching Config', hasImageConfig ? 'PASS' : 'WARN', 
          hasImageConfig ? 'Image optimization configured' : 'No image config found');

        // Check for static optimization
        const hasStaticOptim = /output.*static/i.test(nextConfig);
        this.logTest('Static Optimization', hasStaticOptim ? 'PASS' : 'WARN', 
          hasStaticOptim ? 'Static optimization enabled' : 'Static export not configured');

        // Check for CDN configuration
        const hasCDN = /cdn|cloudfront|cloudflare/i.test(nextConfig);
        this.logTest('CDN Configuration', hasCDN ? 'PASS' : 'WARN', 
          hasCDN ? 'CDN configuration found' : 'No CDN configuration detected');
      }

      // Check for service worker
      const swPath = path.join(process.cwd(), 'public/sw.js');
      const swExists = await this.checkFileExists(swPath);
      this.logTest('Service Worker', swExists ? 'PASS' : 'WARN', 
        swExists ? 'Service worker implemented' : 'No service worker found');

      // Check for caching headers
      const middlewarePath = path.join(process.cwd(), 'middleware.ts');
      if (await this.checkFileExists(middlewarePath)) {
        const middleware = await fs.promises.readFile(middlewarePath, 'utf8');
        const hasCacheHeaders = /cache-control|max-age/i.test(middleware);
        this.logTest('Cache Headers', hasCacheHeaders ? 'PASS' : 'WARN', 
          hasCacheHeaders ? 'Cache headers configured' : 'No cache headers found');
      }

      // Check for static file optimization
      const publicDir = path.join(process.cwd(), 'public');
      if (await this.checkFileExists(publicDir)) {
        const publicFiles = await fs.promises.readdir(publicDir);
        const hasManifest = publicFiles.includes('manifest.json');
        this.logTest('PWA Manifest', hasManifest ? 'PASS' : 'WARN', 
          hasManifest ? 'PWA manifest exists' : 'No PWA manifest found');
      }

      this.results.cachingStrategies = {
        nextConfigExists,
        swExists,
        middlewareExists: await this.checkFileExists(middlewarePath)
      };

    } catch (error) {
      this.logTest('Caching Analysis', 'FAIL', `Error: ${error.message}`);
      this.results.cachingStrategies.error = error.message;
    }
  }

  // Test 7: API Response Times and Database Query Performance
  async testAPIPerformance() {
    this.logSection('7. API Response Times & Database Query Performance');

    try {
      const apiDir = path.join(process.cwd(), 'src/app/api');
      const apiExists = await this.checkFileExists(apiDir);

      if (apiExists) {
        const apiFiles = await this.getAllFiles(apiDir, '.ts');
        this.logTest('API Routes', apiFiles.length > 0 ? 'PASS' : 'WARN', 
          `${apiFiles.length} API routes found`);

        // Check for error handling
        let errorHandlingCount = 0;
        let rateLimitingCount = 0;
        let validationCount = 0;

        for (const file of apiFiles) {
          const content = await fs.promises.readFile(file, 'utf8');
          if (/try.*catch|\.catch\(/.test(content)) errorHandlingCount++;
          if (/rate.*limit|throttle/i.test(content)) rateLimitingCount++;
          if (/validate|zod|joi/i.test(content)) validationCount++;
        }

        this.logTest('Error Handling', errorHandlingCount > 0 ? 'PASS' : 'WARN', 
          `${errorHandlingCount}/${apiFiles.length} routes with error handling`);

        this.logTest('Rate Limiting', rateLimitingCount > 0 ? 'PASS' : 'WARN', 
          `${rateLimitingCount}/${apiFiles.length} routes with rate limiting`);

        this.logTest('Input Validation', validationCount > 0 ? 'PASS' : 'WARN', 
          `${validationCount}/${apiFiles.length} routes with validation`);

        // Check for caching in API routes
        const cachingResult = await this.runCommand('grep', ['-r', 'cache\\|Cache', apiDir]);
        const apiCaching = cachingResult.stdout.split('\n').length > 1;
        this.logTest('API Caching', apiCaching ? 'PASS' : 'WARN', 
          apiCaching ? 'API caching implemented' : 'No API caching found');

        this.results.apiPerformance = {
          routeCount: apiFiles.length,
          errorHandlingCount,
          rateLimitingCount,
          validationCount,
          apiCaching
        };
      } else {
        this.logTest('API Directory', 'WARN', 'No API directory found');
      }

      // Check for database optimization
      const dbResult = await this.runCommand('grep', ['-r', 'supabase\\|prisma\\|mongoose', 'src/']);
      const dbUsage = dbResult.stdout.split('\n').length > 1;
      this.logTest('Database Integration', dbUsage ? 'PASS' : 'WARN', 
        dbUsage ? 'Database integration found' : 'No database integration detected');

    } catch (error) {
      this.logTest('API Performance Analysis', 'FAIL', `Error: ${error.message}`);
      this.results.apiPerformance.error = error.message;
    }
  }

  // Test 8: Image Optimization and Lazy Loading
  async testImageOptimization() {
    this.logSection('8. Image Optimization & Lazy Loading');

    try {
      const srcDir = path.join(process.cwd(), 'src');
      const publicDir = path.join(process.cwd(), 'public');

      // Check for Next.js Image component usage
      const imageResult = await this.runCommand('grep', ['-r', 'next/image', srcDir]);
      const nextImageUsage = imageResult.stdout.split('\n').filter(line => 
        line.includes('next/image') && !line.trim().startsWith('//')
      ).length;

      this.logTest('Next.js Image Component', nextImageUsage > 0 ? 'PASS' : 'WARN', 
        `${nextImageUsage} Next.js Image usages found`);

      // Check for lazy loading implementation
      const lazyResult = await this.runCommand('grep', ['-r', 'loading.*lazy\\|lazy.*loading', srcDir]);
      const lazyImageUsage = lazyResult.stdout.split('\n').filter(line => 
        (line.includes('loading') && line.includes('lazy')) && !line.trim().startsWith('//')
      ).length;

      this.logTest('Lazy Loading', lazyImageUsage > 0 ? 'PASS' : 'WARN', 
        `${lazyImageUsage} lazy loading implementations`);

      // Check for image optimization in public directory
      if (await this.checkFileExists(publicDir)) {
        const publicFiles = await fs.promises.readdir(publicDir);
        const imageFiles = publicFiles.filter(file => 
          /\.(jpg|jpeg|png|gif|webp|svg)$/i.test(file)
        );
        
        this.logTest('Static Images', imageFiles.length > 0 ? 'PASS' : 'WARN', 
          `${imageFiles.length} static images found`);

        // Check for optimized formats
        const webpFiles = imageFiles.filter(file => file.endsWith('.webp'));
        const svgFiles = imageFiles.filter(file => file.endsWith('.svg'));
        
        this.logTest('WebP Format Usage', webpFiles.length > 0 ? 'PASS' : 'WARN', 
          `${webpFiles.length} WebP images found`);

        this.logTest('SVG Usage', svgFiles.length > 0 ? 'PASS' : 'WARN', 
          `${svgFiles.length} SVG images found`);
      }

      // Check for progressive image component
      const progressiveResult = await this.runCommand('grep', ['-r', 'progressive.*image\\|ProgressiveImage', srcDir]);
      const progressiveUsage = progressiveResult.stdout.split('\n').length > 1;
      
      this.logTest('Progressive Images', progressiveUsage ? 'PASS' : 'WARN', 
        progressiveUsage ? 'Progressive image loading found' : 'No progressive loading detected');

      this.results.imageOptimization = {
        nextImageUsage,
        lazyImageUsage,
        progressiveUsage
      };

    } catch (error) {
      this.logTest('Image Optimization Analysis', 'FAIL', `Error: ${error.message}`);
      this.results.imageOptimization.error = error.message;
    }
  }

  // Test 9: Progressive Web App Features
  async testPWAFeatures() {
    this.logSection('9. Progressive Web App Features & Offline Functionality');

    try {
      const publicDir = path.join(process.cwd(), 'public');

      // Check for PWA manifest
      const manifestPath = path.join(publicDir, 'manifest.json');
      const manifestExists = await this.checkFileExists(manifestPath);
      
      if (manifestExists) {
        const manifest = JSON.parse(await fs.promises.readFile(manifestPath, 'utf8'));
        
        const requiredFields = ['name', 'short_name', 'start_url', 'display', 'theme_color', 'icons'];
        const missingFields = requiredFields.filter(field => !manifest[field]);
        
        this.logTest('PWA Manifest', missingFields.length === 0 ? 'PASS' : 'WARN', 
          missingFields.length === 0 ? 'Complete manifest' : `Missing: ${missingFields.join(', ')}`);

        // Check icon sizes
        const icons = manifest.icons || [];
        const iconSizes = icons.map(icon => icon.sizes).flat();
        const hasRequiredSizes = ['192x192', '512x512'].every(size => 
          iconSizes.some(iconSize => iconSize.includes(size))
        );

        this.logTest('PWA Icons', hasRequiredSizes ? 'PASS' : 'WARN', 
          hasRequiredSizes ? 'Required icon sizes present' : 'Missing required icon sizes');
      } else {
        this.logTest('PWA Manifest', 'WARN', 'No manifest.json found');
      }

      // Check for service worker
      const swPath = path.join(publicDir, 'sw.js');
      const swExists = await this.checkFileExists(swPath);
      
      if (swExists) {
        const swContent = await fs.promises.readFile(swPath, 'utf8');
        
        // Check for caching strategies
        const hasCaching = /cache|Cache/.test(swContent);
        this.logTest('SW Caching', hasCaching ? 'PASS' : 'WARN', 
          hasCaching ? 'Caching strategies implemented' : 'No caching in service worker');

        // Check for offline support
        const hasOffline = /offline|network.*failed/i.test(swContent);
        this.logTest('Offline Support', hasOffline ? 'PASS' : 'WARN', 
          hasOffline ? 'Offline functionality detected' : 'No offline support found');
      } else {
        this.logTest('Service Worker', 'WARN', 'No service worker found');
      }

      // Check for offline page
      const offlinePath = path.join(publicDir, 'offline.html');
      const offlineExists = await this.checkFileExists(offlinePath);
      this.logTest('Offline Page', offlineExists ? 'PASS' : 'WARN', 
        offlineExists ? 'Offline fallback page exists' : 'No offline page found');

      // Check for PWA meta tags in layout
      const layoutPath = path.join(process.cwd(), 'src/app/layout.tsx');
      if (await this.checkFileExists(layoutPath)) {
        const layoutContent = await fs.promises.readFile(layoutPath, 'utf8');
        
        const pwaMetaTags = [
          'apple-touch-icon',
          'theme-color',
          'apple-mobile-web-app-capable'
        ];

        const presentTags = pwaMetaTags.filter(tag => 
          new RegExp(tag, 'i').test(layoutContent)
        );

        this.logTest('PWA Meta Tags', presentTags.length > 0 ? 'PASS' : 'WARN', 
          `${presentTags.length}/${pwaMetaTags.length} PWA meta tags found`);
      }

      this.results.pwaFeatures = {
        manifestExists,
        swExists,
        offlineExists
      };

    } catch (error) {
      this.logTest('PWA Analysis', 'FAIL', `Error: ${error.message}`);
      this.results.pwaFeatures.error = error.message;
    }
  }

  // Helper method to get all files recursively
  async getAllFiles(dir, extension = '') {
    const files = [];
    
    async function traverse(currentDir) {
      const items = await fs.promises.readdir(currentDir, { withFileTypes: true });
      
      for (const item of items) {
        const fullPath = path.join(currentDir, item.name);
        
        if (item.isDirectory()) {
          await traverse(fullPath);
        } else if (!extension || item.name.endsWith(extension)) {
          files.push(fullPath);
        }
      }
    }
    
    await traverse(dir);
    return files;
  }

  // Calculate overall performance score
  calculateOverallScore() {
    const weights = {
      bundleOptimization: 0.15,
      webVitals: 0.15,
      memoryOptimization: 0.15,
      loadingStates: 0.10,
      mobileOptimization: 0.15,
      cachingStrategies: 0.10,
      apiPerformance: 0.10,
      imageOptimization: 0.05,
      pwaFeatures: 0.05
    };

    let totalScore = 0;
    let totalWeight = 0;

    // Calculate scores for each category based on test results
    Object.keys(weights).forEach(category => {
      if (this.results[category] && !this.results[category].error) {
        // Simple scoring based on whether features are implemented
        let categoryScore = 0;
        const categoryData = this.results[category];

        // Count implemented features vs total possible features
        const implementedFeatures = Object.values(categoryData).filter(value => 
          value === true || (typeof value === 'number' && value > 0)
        ).length;
        
        const totalFeatures = Object.keys(categoryData).length;
        categoryScore = totalFeatures > 0 ? (implementedFeatures / totalFeatures) * 100 : 50;

        totalScore += categoryScore * weights[category];
        totalWeight += weights[category];
      }
    });

    return totalWeight > 0 ? Math.round(totalScore / totalWeight) : 0;
  }

  // Generate recommendations based on test results
  generateRecommendations() {
    const recommendations = [];

    // Bundle optimization recommendations
    if (this.results.bundleOptimization.totalSize > PERFORMANCE_CONFIG.BUNDLE_SIZE_THRESHOLD) {
      recommendations.push({
        category: 'Bundle Optimization',
        priority: 'High',
        issue: 'Bundle size exceeds recommended threshold',
        solution: 'Implement more aggressive code splitting, remove unused dependencies, and optimize chunk sizes'
      });
    }

    if (this.results.bundleOptimization.dynamicImports === 0) {
      recommendations.push({
        category: 'Code Splitting',
        priority: 'Medium',
        issue: 'No dynamic imports detected',
        solution: 'Implement dynamic imports for route-based code splitting and heavy components'
      });
    }

    // Memory optimization recommendations
    if (this.results.memoryOptimization.memoUsage === 0) {
      recommendations.push({
        category: 'React Performance',
        priority: 'Medium',
        issue: 'No React.memo usage detected',
        solution: 'Wrap expensive components with React.memo to prevent unnecessary re-renders'
      });
    }

    if (this.results.memoryOptimization.cleanupUsage < 5) {
      recommendations.push({
        category: 'Memory Management',
        priority: 'High',
        issue: 'Insufficient effect cleanup detected',
        solution: 'Implement proper cleanup in useEffect hooks to prevent memory leaks'
      });
    }

    // Mobile optimization recommendations
    if (this.results.mobileOptimization.touchUsage === 0) {
      recommendations.push({
        category: 'Mobile Experience',
        priority: 'Medium',
        issue: 'No touch optimizations detected',
        solution: 'Implement touch gestures and mobile-specific interactions for better UX'
      });
    }

    // PWA recommendations
    if (!this.results.pwaFeatures.manifestExists) {
      recommendations.push({
        category: 'Progressive Web App',
        priority: 'Low',
        issue: 'No PWA manifest found',
        solution: 'Add a web app manifest to enable PWA features like install prompts and better mobile integration'
      });
    }

    // API performance recommendations
    if (this.results.apiPerformance.rateLimitingCount === 0) {
      recommendations.push({
        category: 'API Security',
        priority: 'High',
        issue: 'No rate limiting detected',
        solution: 'Implement rate limiting on API routes to prevent abuse and improve performance'
      });
    }

    return recommendations;
  }

  // Generate comprehensive performance report
  generateReport() {
    this.logSection('📋 Comprehensive Performance Testing Report');

    const duration = Date.now() - this.startTime;
    const overallScore = this.calculateOverallScore();
    const recommendations = this.generateRecommendations();

    this.results.overallScore = overallScore;
    this.results.recommendations = recommendations;

    // Summary
    this.log(`\n🎯 Overall Performance Score: ${overallScore}/100`, 
      overallScore >= 90 ? COLORS.GREEN : overallScore >= 70 ? COLORS.YELLOW : COLORS.RED);
    
    this.log(`⏱️  Total Testing Duration: ${this.formatDuration(duration)}`);
    this.log(`📊 Tests Executed: ${this.testCount}`);
    this.log(`✅ Tests Passed: ${this.passedTests}/${this.testCount} (${Math.round(this.passedTests/this.testCount*100)}%)`);

    // Performance grade
    const grade = overallScore >= 90 ? 'A' : overallScore >= 80 ? 'B' : overallScore >= 70 ? 'C' : overallScore >= 60 ? 'D' : 'F';
    this.log(`🏆 Performance Grade: ${grade}`, 
      grade === 'A' ? COLORS.GREEN : grade === 'B' ? COLORS.YELLOW : COLORS.RED);

    // Category breakdown
    this.log('\n📊 Category Performance Breakdown:', COLORS.CYAN);
    
    const categories = [
      'Bundle Optimization',
      'Web Vitals Monitoring', 
      'Memory Optimization',
      'Loading States',
      'Mobile Optimization',
      'Caching Strategies',
      'API Performance',
      'Image Optimization',
      'PWA Features'
    ];

    categories.forEach((category, index) => {
      const key = Object.keys(this.results)[index + 1]; // Skip overallScore
      const hasError = this.results[key]?.error;
      const status = hasError ? '❌ Error' : '✅ Implemented';
      this.log(`  ${category}: ${status}`);
    });

    // Recommendations
    if (recommendations.length > 0) {
      this.log('\n🔧 Performance Recommendations:', COLORS.YELLOW);
      
      recommendations.forEach((rec, index) => {
        const priorityColor = rec.priority === 'High' ? COLORS.RED : 
                             rec.priority === 'Medium' ? COLORS.YELLOW : COLORS.GREEN;
        
        this.log(`\n${index + 1}. ${rec.category} [${priorityColor}${rec.priority}${COLORS.RESET}]`);
        this.log(`   Issue: ${rec.issue}`, COLORS.DIM);
        this.log(`   Solution: ${rec.solution}`, COLORS.DIM);
      });
    } else {
      this.log('\n🎉 No performance recommendations - excellent optimization!', COLORS.GREEN);
    }

    // Save detailed report
    this.saveDetailedReport();

    // Final summary
    this.log('\n' + '='.repeat(80));
    this.log('🚀 Performance Validation Testing Complete!', COLORS.GREEN + COLORS.BOLD);
    
    if (overallScore >= 85) {
      this.log('✨ Excellent performance! Your application is well-optimized.', COLORS.GREEN);
    } else if (overallScore >= 70) {
      this.log('👍 Good performance with room for improvement.', COLORS.YELLOW);
    } else {
      this.log('⚠️  Performance needs attention. Consider implementing the recommendations above.', COLORS.RED);
    }
    
    this.log('='.repeat(80));
  }

  // Save detailed JSON report
  async saveDetailedReport() {
    const reportPath = path.join(process.cwd(), 'reports', 'performance-validation-report.json');
    
    try {
      // Ensure reports directory exists
      await fs.promises.mkdir(path.dirname(reportPath), { recursive: true });
      
      const report = {
        timestamp: new Date().toISOString(),
        duration: Date.now() - this.startTime,
        overallScore: this.results.overallScore,
        testSummary: {
          total: this.testCount,
          passed: this.passedTests,
          failed: this.testCount - this.passedTests,
          passRate: Math.round(this.passedTests / this.testCount * 100)
        },
        results: this.results,
        recommendations: this.results.recommendations,
        environment: {
          nodeVersion: process.version,
          platform: process.platform,
          architecture: process.arch
        }
      };

      await fs.promises.writeFile(reportPath, JSON.stringify(report, null, 2));
      this.log(`\n📄 Detailed report saved: ${reportPath}`, COLORS.BLUE);
      
    } catch (error) {
      this.log(`\n❌ Failed to save detailed report: ${error.message}`, COLORS.RED);
    }
  }

  // Main execution method
  async run() {
    this.log('🚀 Starting Comprehensive Performance Validation Testing...', COLORS.GREEN + COLORS.BOLD);
    this.log(`📅 Test Started: ${new Date().toLocaleString()}`);
    this.log(`🔍 Testing Environment: ${process.platform} ${process.arch}`);

    try {
      await this.testBundleOptimization();
      await this.testWebVitalsMonitoring();
      await this.testMemoryOptimization();
      await this.testLoadingStates();
      await this.testMobileOptimizations();
      await this.testCachingStrategies();
      await this.testAPIPerformance();
      await this.testImageOptimization();
      await this.testPWAFeatures();
      
      this.generateReport();
      
    } catch (error) {
      this.log(`\n❌ Testing failed with error: ${error.message}`, COLORS.RED);
      console.error(error);
      process.exit(1);
    }
  }
}

// Execute performance testing
if (require.main === module) {
  const tester = new PerformanceTester();
  tester.run().catch(console.error);
}

module.exports = PerformanceTester;