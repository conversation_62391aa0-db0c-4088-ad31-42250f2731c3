import { calculate } from '@/core/calculator/engine';
import type { CalculationInput } from '@/core/calculator/types';

// Mock calculation input data
const mockCalculationInput: CalculationInput = {
  plotArea: 1000,
  builtUpArea: 800,
  floors: 1,
  qualityTier: 'premium',
  location: 'delhi',
  hasBasement: false,
  parkingType: 'open'
};

describe('Calculation Performance Tests', () => {
  const performanceThresholds = {
    singleCalculation: 100, // ms
  };

  describe('Single Calculation Performance', () => {
    it('should complete calculation within performance threshold', () => {
      const startTime = performance.now();
      
      const result = calculate(mockCalculationInput);
      
      const endTime = performance.now();
      const executionTime = endTime - startTime;
      
      expect(result.totalCost).toBeDefined();
      expect(result.totalCost).toBeGreaterThan(0);
      expect(executionTime).toBeLessThan(performanceThresholds.singleCalculation);
      
      console.log(`Single calculation completed in ${executionTime.toFixed(2)}ms`);
    });

    it('should handle different quality tiers with consistent performance', () => {
      const tiers = ['smart', 'premium', 'luxury'] as const;
      const executionTimes: number[] = [];
      
      for (const tier of tiers) {
        const startTime = performance.now();
        
        const result = calculate({
          ...mockCalculationInput,
          qualityTier: tier,
        });
        
        const endTime = performance.now();
        const executionTime = endTime - startTime;
        
        expect(result.totalCost).toBeGreaterThan(0);
        expect(executionTime).toBeLessThan(performanceThresholds.singleCalculation);
        
        executionTimes.push(executionTime);
      }
      
      // Check consistency (no tier should be significantly slower)
      const maxTime = Math.max(...executionTimes);
      const minTime = Math.min(...executionTimes);
      const varianceRatio = maxTime / minTime;
      
      expect(varianceRatio).toBeLessThan(2); // Max 2x variance
      
      console.log(`Tier execution times:`, executionTimes.map(t => `${t.toFixed(2)}ms`));
    });
  });
});