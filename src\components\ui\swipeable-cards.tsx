/**
 * Swipeable Cards Component
 * Provides touch-friendly swipeable card interface for mobile devices
 */

'use client';

import { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence, PanInfo } from 'framer-motion';
import { ChevronLeft, ChevronRight } from 'lucide-react';

import { cn } from '@/lib/utils';
import { 
  hapticFeedback, 
  isMobileViewport, 
  mobileClasses,
  MOBILE_DESIGN 
} from '@/lib/mobile';

interface SwipeableCard {
  id: string;
  title: string;
  subtitle?: string;
  content: React.ReactNode;
  value: string;
  selected?: boolean;
}

interface SwipeableCardsProps {
  cards: SwipeableCard[];
  onCardSelect: (value: string) => void;
  selectedValue?: string;
  className?: string;
  showIndicators?: boolean;
  showArrows?: boolean;
  autoSwipe?: boolean;
  swipeThreshold?: number;
}

export function SwipeableCards({
  cards,
  onCardSelect,
  selectedValue,
  className,
  showIndicators = true,
  showArrows = true,
  autoSwipe = false,
  swipeThreshold = MOBILE_DESIGN.SWIPE_THRESHOLD
}: SwipeableCardsProps) {
  const [currentIndex, setCurrentIndex] = useState(
    selectedValue ? cards.findIndex(card => card.value === selectedValue) : 0
  );
  const [isDragging, setIsDragging] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  const isMobile = isMobileViewport();

  // Update current index when selectedValue changes
  useEffect(() => {
    if (selectedValue) {
      const index = cards.findIndex(card => card.value === selectedValue);
      if (index !== -1) {
        setCurrentIndex(index);
      }
    }
  }, [selectedValue, cards]);

  // Auto-swipe functionality
  useEffect(() => {
    if (!autoSwipe || isDragging) return;

    const interval = setInterval(() => {
      setCurrentIndex(prev => (prev + 1) % cards.length);
    }, 3000);

    return () => clearInterval(interval);
  }, [autoSwipe, isDragging, cards.length]);

  const goToNext = () => {
    const nextIndex = (currentIndex + 1) % cards.length;
    setCurrentIndex(nextIndex);
    onCardSelect(cards[nextIndex].value);
    hapticFeedback.light();
  };

  const goToPrevious = () => {
    const prevIndex = currentIndex === 0 ? cards.length - 1 : currentIndex - 1;
    setCurrentIndex(prevIndex);
    onCardSelect(cards[prevIndex].value);
    hapticFeedback.light();
  };

  const goToIndex = (index: number) => {
    setCurrentIndex(index);
    onCardSelect(cards[index].value);
    hapticFeedback.light();
  };

  const handleDragStart = () => {
    setIsDragging(true);
  };

  const handleDragEnd = (event: MouseEvent | TouchEvent | PointerEvent, info: PanInfo) => {
    setIsDragging(false);

    const { offset, velocity } = info;
    const swipeDistance = Math.abs(offset.x);
    const swipeVelocity = Math.abs(velocity.x);

    if (swipeDistance > swipeThreshold || swipeVelocity > 500) {
      if (offset.x > 0) {
        goToPrevious();
      } else {
        goToNext();
      }
    }
  };

  const handleCardClick = (value: string) => {
    onCardSelect(value);
    hapticFeedback.light();
  };

  const slideVariants = {
    enter: (direction: number) => ({
      x: direction > 0 ? 1000 : -1000,
      opacity: 0,
      scale: 0.8,
    }),
    center: {
      zIndex: 1,
      x: 0,
      opacity: 1,
      scale: 1,
    },
    exit: (direction: number) => ({
      zIndex: 0,
      x: direction < 0 ? 1000 : -1000,
      opacity: 0,
      scale: 0.8,
    }),
  };

  const cardVariants = {
    selected: {
      scale: 1,
      opacity: 1,
      y: 0,
      transition: {
        type: 'spring',
        damping: 20,
        stiffness: 300,
      },
    },
    unselected: {
      scale: 0.95,
      opacity: 0.7,
      y: 10,
      transition: {
        type: 'spring',
        damping: 20,
        stiffness: 300,
      },
    },
  };

  if (!isMobile) {
    // Desktop grid layout
    return (
      <div className={cn('grid grid-cols-1 md:grid-cols-3 gap-4', className)}>
        {cards.map((card) => (
          <motion.div
            key={card.id}
            className={cn(
              'p-6 rounded-xl border-2 cursor-pointer transition-all duration-200',
              'hover:shadow-lg hover:scale-105',
              selectedValue === card.value
                ? 'border-blue-500 bg-blue-50 shadow-md'
                : 'border-gray-200 bg-white hover:border-gray-300'
            )}
            variants={cardVariants}
            animate={selectedValue === card.value ? 'selected' : 'unselected'}
            onClick={() => handleCardClick(card.value)}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <div className="text-center">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {card.title}
              </h3>
              {card.subtitle && (
                <p className="text-sm text-gray-600 mb-4">{card.subtitle}</p>
              )}
              <div className="text-gray-700">{card.content}</div>
            </div>
          </motion.div>
        ))}
      </div>
    );
  }

  // Mobile swipeable layout
  return (
    <div className={cn('relative', className)}>
      <div className="relative overflow-hidden">
        <div 
          ref={containerRef}
          className="flex touch-pan-x"
          style={{ height: 'auto' }}
        >
          <AnimatePresence initial={false} custom={currentIndex}>
            <motion.div
              key={currentIndex}
              custom={currentIndex}
              variants={slideVariants}
              initial="enter"
              animate="center"
              exit="exit"
              transition={{
                x: { type: 'spring', stiffness: 300, damping: 30 },
                opacity: { duration: 0.2 },
                scale: { duration: 0.2 },
              }}
              drag="x"
              dragConstraints={{ left: 0, right: 0 }}
              dragElastic={0.2}
              onDragStart={handleDragStart}
              onDragEnd={handleDragEnd}
              className="w-full flex-shrink-0"
            >
              <div
                className={cn(
                  'p-6 mx-4 rounded-xl border-2 cursor-pointer',
                  'transition-all duration-200',
                  selectedValue === cards[currentIndex].value
                    ? 'border-blue-500 bg-blue-50 shadow-lg'
                    : 'border-gray-200 bg-white shadow-md',
                  mobileClasses.touchTarget
                )}
                onClick={() => handleCardClick(cards[currentIndex].value)}
              >
                <div className="text-center">
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    {cards[currentIndex].title}
                  </h3>
                  {cards[currentIndex].subtitle && (
                    <p className="text-sm text-gray-600 mb-4">
                      {cards[currentIndex].subtitle}
                    </p>
                  )}
                  <div className="text-gray-700">
                    {cards[currentIndex].content}
                  </div>
                </div>
              </div>
            </motion.div>
          </AnimatePresence>
        </div>

        {/* Navigation arrows */}
        {showArrows && cards.length > 1 && (
          <>
            <button
              onClick={goToPrevious}
              className={cn(
                'absolute left-2 top-1/2 transform -translate-y-1/2',
                'bg-white rounded-full shadow-lg p-2',
                'text-gray-600 hover:text-gray-900',
                'transition-all duration-200',
                mobileClasses.touchTarget
              )}
              disabled={currentIndex === 0}
            >
              <ChevronLeft className="h-5 w-5" />
            </button>

            <button
              onClick={goToNext}
              className={cn(
                'absolute right-2 top-1/2 transform -translate-y-1/2',
                'bg-white rounded-full shadow-lg p-2',
                'text-gray-600 hover:text-gray-900',
                'transition-all duration-200',
                mobileClasses.touchTarget
              )}
              disabled={currentIndex === cards.length - 1}
            >
              <ChevronRight className="h-5 w-5" />
            </button>
          </>
        )}
      </div>

      {/* Indicators */}
      {showIndicators && cards.length > 1 && (
        <div className="flex justify-center mt-4 space-x-2">
          {cards.map((_, index) => (
            <button
              key={index}
              onClick={() => goToIndex(index)}
              className={cn(
                'w-2 h-2 rounded-full transition-all duration-200',
                mobileClasses.touchTarget,
                index === currentIndex
                  ? 'bg-blue-500 w-6'
                  : 'bg-gray-300 hover:bg-gray-400'
              )}
            />
          ))}
        </div>
      )}

      {/* Swipe hint */}
      {isMobile && (
        <div className="text-center mt-2">
          <p className="text-xs text-gray-500">
            Swipe left or right to browse options
          </p>
        </div>
      )}
    </div>
  );
}

interface QualityTierCard {
  id: string;
  title: string;
  price: string;
  features: string[];
  value: string;
  popular?: boolean;
}

interface QualityTierSelectorProps {
  tiers: QualityTierCard[];
  selectedTier: string;
  onTierSelect: (tier: string) => void;
  className?: string;
}

export function QualityTierSelector({
  tiers,
  selectedTier,
  onTierSelect,
  className
}: QualityTierSelectorProps) {
  const swipeableCards: SwipeableCard[] = tiers.map(tier => ({
    id: tier.id,
    title: tier.title,
    subtitle: tier.price,
    value: tier.value,
    content: (
      <div className="space-y-3">
        {tier.popular && (
          <div className="inline-block px-3 py-1 bg-blue-100 text-blue-800 text-xs font-semibold rounded-full">
            Most Popular
          </div>
        )}
        <div className="text-2xl font-bold text-blue-600">{tier.price}</div>
        <ul className="space-y-2 text-sm text-gray-600">
          {tier.features.map((feature, index) => (
            <li key={index} className="flex items-center">
              <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mr-2 flex-shrink-0" />
              {feature}
            </li>
          ))}
        </ul>
      </div>
    ),
  }));

  return (
    <SwipeableCards
      cards={swipeableCards}
      onCardSelect={onTierSelect}
      selectedValue={selectedTier}
      className={className}
      showIndicators={true}
      showArrows={false}
    />
  );
}