/**
 * Pull-to-Refresh Component
 * Provides native mobile pull-to-refresh functionality
 */

'use client';

import { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { RefreshCw, ChevronDown } from 'lucide-react';

import { cn } from '@/lib/utils';
import { 
  hapticFeedback, 
  isMobileViewport, 
  MOBILE_DESIGN 
} from '@/lib/mobile';

interface PullToRefreshProps {
  onRefresh: () => Promise<void> | void;
  children: React.ReactNode;
  threshold?: number;
  className?: string;
  disabled?: boolean;
}

export function PullToRefresh({
  onRefresh,
  children,
  threshold = MOBILE_DESIGN.PULL_TO_REFRESH_THRESHOLD,
  className,
  disabled = false
}: PullToRefreshProps) {
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [pullDistance, setPullDistance] = useState(0);
  const [isPulling, setIsPulling] = useState(false);
  const [shouldRefresh, setShouldRefresh] = useState(false);

  const containerRef = useRef<HTMLDivElement>(null);
  const startYRef = useRef<number>(0);
  const currentYRef = useRef<number>(0);

  const isMobile = isMobileViewport();

  const handleTouchStart = (e: TouchEvent) => {
    if (disabled || !isMobile) return;

    // Only allow pull-to-refresh when at the top of the page
    if (window.scrollY !== 0) return;

    startYRef.current = e.touches[0].clientY;
    setIsPulling(false);
  };

  const handleTouchMove = (e: TouchEvent) => {
    if (disabled || !isMobile) return;

    currentYRef.current = e.touches[0].clientY;
    const deltaY = currentYRef.current - startYRef.current;

    // Only allow downward pull at the top of the page
    if (window.scrollY === 0 && deltaY > 0) {
      e.preventDefault();
      setIsPulling(true);
      
      // Calculate pull distance with resistance
      const resistance = 0.5;
      const distance = Math.min(deltaY * resistance, threshold * 1.5);
      setPullDistance(distance);

      // Determine if we should refresh
      const shouldRefreshNow = distance >= threshold;
      if (shouldRefreshNow !== shouldRefresh) {
        setShouldRefresh(shouldRefreshNow);
        if (shouldRefreshNow) {
          hapticFeedback.light();
        }
      }
    }
  };

  const handleTouchEnd = async () => {
    if (disabled || !isMobile) return;

    if (isPulling) {
      if (shouldRefresh && !isRefreshing) {
        setIsRefreshing(true);
        hapticFeedback.medium();
        
        try {
          await onRefresh();
        } catch (error) {
          console.error('Refresh failed:', error);
        } finally {
          setIsRefreshing(false);
        }
      }
      
      setIsPulling(false);
      setPullDistance(0);
      setShouldRefresh(false);
    }
  };

  useEffect(() => {
    const container = containerRef.current;
    if (!container || !isMobile) return;

    container.addEventListener('touchstart', handleTouchStart, { passive: false });
    container.addEventListener('touchmove', handleTouchMove, { passive: false });
    container.addEventListener('touchend', handleTouchEnd, { passive: false });

    return () => {
      container.removeEventListener('touchstart', handleTouchStart);
      container.removeEventListener('touchmove', handleTouchMove);
      container.removeEventListener('touchend', handleTouchEnd);
    };
  }, [disabled, isMobile, shouldRefresh, isRefreshing, threshold]);

  const refreshIndicatorVariants = {
    hidden: { opacity: 0, scale: 0.8, y: -20 },
    visible: { opacity: 1, scale: 1, y: 0 },
    exit: { opacity: 0, scale: 0.8, y: -20 }
  };

  const getRefreshState = () => {
    if (isRefreshing) return 'refreshing';
    if (shouldRefresh) return 'release';
    if (isPulling) return 'pulling';
    return 'idle';
  };

  const getRefreshIcon = () => {
    const state = getRefreshState();
    
    switch (state) {
      case 'refreshing':
        return (
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
          >
            <RefreshCw className="h-5 w-5 text-blue-600" />
          </motion.div>
        );
      case 'release':
        return <ChevronDown className="h-5 w-5 text-green-600" />;
      case 'pulling':
        return (
          <motion.div
            animate={{ 
              rotate: Math.min((pullDistance / threshold) * 180, 180)
            }}
            transition={{ type: 'spring', stiffness: 300, damping: 30 }}
          >
            <ChevronDown className="h-5 w-5 text-gray-600" />
          </motion.div>
        );
      default:
        return null;
    }
  };

  const getRefreshText = () => {
    const state = getRefreshState();
    
    switch (state) {
      case 'refreshing':
        return 'Refreshing...';
      case 'release':
        return 'Release to refresh';
      case 'pulling':
        return 'Pull to refresh';
      default:
        return '';
    }
  };

  return (
    <div
      ref={containerRef}
      className={cn('relative overflow-hidden', className)}
      style={{
        transform: `translateY(${Math.min(pullDistance, threshold)})px)`,
        transition: isPulling ? 'none' : 'transform 0.3s ease-out',
      }}
    >
      {/* Refresh Indicator */}
      <AnimatePresence>
        {(isPulling || isRefreshing) && (
          <motion.div
            className="absolute top-0 left-0 right-0 flex items-center justify-center py-4 bg-white/95 backdrop-blur-sm border-b border-gray-200"
            variants={refreshIndicatorVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            style={{
              transform: `translateY(-${Math.max(0, threshold - pullDistance)}px)`,
            }}
          >
            <div className="flex items-center space-x-2">
              {getRefreshIcon()}
              <span className="text-sm font-medium text-gray-700">
                {getRefreshText()}
              </span>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Content */}
      <div className="relative z-10">
        {children}
      </div>
    </div>
  );
}

interface RefreshableContainerProps {
  onRefresh: () => Promise<void> | void;
  children: React.ReactNode;
  className?: string;
  disabled?: boolean;
  showRefreshButton?: boolean;
}

export function RefreshableContainer({
  onRefresh,
  children,
  className,
  disabled = false,
  showRefreshButton = true
}: RefreshableContainerProps) {
  const [isRefreshing, setIsRefreshing] = useState(false);
  const isMobile = isMobileViewport();

  const handleRefresh = async () => {
    if (disabled || isRefreshing) return;

    setIsRefreshing(true);
    hapticFeedback.medium();

    try {
      await onRefresh();
    } catch (error) {
      console.error('Refresh failed:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  return (
    <div className={cn('relative', className)}>
      {/* Desktop refresh button */}
      {!isMobile && showRefreshButton && (
        <div className="absolute top-4 right-4 z-10">
          <button
            onClick={handleRefresh}
            disabled={disabled || isRefreshing}
            className={cn(
              'p-2 rounded-full bg-white shadow-lg border',
              'text-gray-600 hover:text-gray-900 hover:bg-gray-50',
              'transition-all duration-200',
              'disabled:opacity-50 disabled:cursor-not-allowed',
              isRefreshing && 'animate-spin'
            )}
          >
            <RefreshCw className="h-5 w-5" />
          </button>
        </div>
      )}

      {/* Mobile pull-to-refresh */}
      {isMobile ? (
        <PullToRefresh
          onRefresh={handleRefresh}
          disabled={disabled}
          className={className}
        >
          {children}
        </PullToRefresh>
      ) : (
        children
      )}
    </div>
  );
}

interface SwipeToRefreshProps {
  onRefresh: () => Promise<void> | void;
  children: React.ReactNode;
  className?: string;
  disabled?: boolean;
  direction?: 'horizontal' | 'vertical';
}

export function SwipeToRefresh({
  onRefresh,
  children,
  className,
  disabled = false,
  direction = 'vertical'
}: SwipeToRefreshProps) {
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [swipeDistance, setSwipeDistance] = useState(0);
  const [isScrolling, setIsScrolling] = useState(false);

  const containerRef = useRef<HTMLDivElement>(null);
  const startPosRef = useRef<{ x: number; y: number }>({ x: 0, y: 0 });
  const currentPosRef = useRef<{ x: number; y: number }>({ x: 0, y: 0 });

  const isMobile = isMobileViewport();
  const threshold = MOBILE_DESIGN.SWIPE_THRESHOLD;

  const handleTouchStart = (e: TouchEvent) => {
    if (disabled || !isMobile) return;

    const touch = e.touches[0];
    startPosRef.current = { x: touch.clientX, y: touch.clientY };
    setIsScrolling(false);
  };

  const handleTouchMove = (e: TouchEvent) => {
    if (disabled || !isMobile) return;

    const touch = e.touches[0];
    currentPosRef.current = { x: touch.clientX, y: touch.clientY };

    const deltaX = currentPosRef.current.x - startPosRef.current.x;
    const deltaY = currentPosRef.current.y - startPosRef.current.y;

    if (direction === 'horizontal') {
      // Horizontal swipe (right to refresh)
      if (deltaX > 0 && Math.abs(deltaX) > Math.abs(deltaY)) {
        e.preventDefault();
        setIsScrolling(true);
        setSwipeDistance(Math.min(deltaX, threshold * 1.5));
      }
    } else {
      // Vertical swipe (down to refresh)
      if (deltaY > 0 && Math.abs(deltaY) > Math.abs(deltaX) && window.scrollY === 0) {
        e.preventDefault();
        setIsScrolling(true);
        setSwipeDistance(Math.min(deltaY, threshold * 1.5));
      }
    }
  };

  const handleTouchEnd = async () => {
    if (disabled || !isMobile) return;

    if (isScrolling && swipeDistance >= threshold && !isRefreshing) {
      setIsRefreshing(true);
      hapticFeedback.medium();

      try {
        await onRefresh();
      } catch (error) {
        console.error('Refresh failed:', error);
      } finally {
        setIsRefreshing(false);
      }
    }

    setIsScrolling(false);
    setSwipeDistance(0);
  };

  useEffect(() => {
    const container = containerRef.current;
    if (!container || !isMobile) return;

    container.addEventListener('touchstart', handleTouchStart, { passive: false });
    container.addEventListener('touchmove', handleTouchMove, { passive: false });
    container.addEventListener('touchend', handleTouchEnd, { passive: false });

    return () => {
      container.removeEventListener('touchstart', handleTouchStart);
      container.removeEventListener('touchmove', handleTouchMove);
      container.removeEventListener('touchend', handleTouchEnd);
    };
  }, [disabled, isMobile, direction, isRefreshing, swipeDistance]);

  return (
    <div
      ref={containerRef}
      className={cn('relative touch-pan-y', className)}
      style={{
        transform: direction === 'horizontal' 
          ? `translateX(${Math.min(swipeDistance * 0.3, threshold * 0.3)}px)`
          : `translateY(${Math.min(swipeDistance * 0.3, threshold * 0.3)}px)`,
        transition: isScrolling ? 'none' : 'transform 0.3s ease-out',
      }}
    >
      {/* Refresh indicator */}
      <AnimatePresence>
        {(isScrolling || isRefreshing) && (
          <motion.div
            className={cn(
              'absolute flex items-center justify-center',
              'bg-white/95 backdrop-blur-sm border border-gray-200 rounded-full',
              'shadow-lg z-10',
              direction === 'horizontal' 
                ? 'left-0 top-1/2 transform -translate-y-1/2 -translate-x-full p-3'
                : 'top-0 left-1/2 transform -translate-x-1/2 -translate-y-full p-3'
            )}
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
          >
            <motion.div
              animate={isRefreshing ? { rotate: 360 } : {}}
              transition={isRefreshing ? { duration: 1, repeat: Infinity, ease: 'linear' } : {}}
            >
              <RefreshCw className="h-5 w-5 text-blue-600" />
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {children}
    </div>
  );
}