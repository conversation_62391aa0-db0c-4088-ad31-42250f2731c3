/**
 * Mobile optimization utilities and helpers
 * Provides touch-friendly design patterns and mobile-specific features
 */

// Touch target size constants (iOS/Android guidelines)
export const TOUCH_TARGET = {
  MIN_SIZE: 44, // 44px minimum for accessibility
  COMFORTABLE_SIZE: 48, // Comfortable touch target
  LARGE_SIZE: 56, // Large touch target for primary actions
} as const;

// Viewport breakpoints for mobile optimization
export const MOBILE_BREAKPOINTS = {
  XS: 320, // iPhone SE
  SM: 375, // iPhone 12 Pro
  MD: 414, // iPhone 12 Pro Max
  LG: 768, // iPad Mini
} as const;

// Mobile-specific design tokens
export const MOBILE_DESIGN = {
  // Spacing
  SAFE_AREA_INSET: 'env(safe-area-inset-bottom)', // iOS safe area
  BOTTOM_SHEET_BORDER_RADIUS: '16px 16px 0 0',
  
  // Typography
  MOBILE_FONT_SCALE: {
    xs: '0.875rem', // 14px
    sm: '1rem',     // 16px
    md: '1.125rem', // 18px
    lg: '1.25rem',  // 20px
    xl: '1.5rem',   // 24px
  },
  
  // Gestures
  SWIPE_THRESHOLD: 50, // px
  PULL_TO_REFRESH_THRESHOLD: 100, // px
} as const;

// Device detection utilities
export const detectDevice = () => {
  if (typeof window === 'undefined') return null;
  
  const userAgent = window.navigator.userAgent;
  return {
    isMobile: /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent),
    isIOS: /iPad|iPhone|iPod/.test(userAgent),
    isAndroid: /Android/.test(userAgent),
    isTouchDevice: 'ontouchstart' in window,
    hasHaptics: 'vibrate' in navigator,
    supportsShare: 'share' in navigator,
  };
};

// Haptic feedback utilities
export const hapticFeedback = {
  light: () => {
    const device = detectDevice();
    if (device?.hasHaptics) {
      navigator.vibrate?.(50);
    }
  },
  medium: () => {
    const device = detectDevice();
    if (device?.hasHaptics) {
      navigator.vibrate?.(100);
    }
  },
  heavy: () => {
    const device = detectDevice();
    if (device?.hasHaptics) {
      navigator.vibrate?.(200);
    }
  },
  success: () => {
    const device = detectDevice();
    if (device?.hasHaptics) {
      navigator.vibrate?.([100, 50, 100]);
    }
  },
  error: () => {
    const device = detectDevice();
    if (device?.hasHaptics) {
      navigator.vibrate?.([200, 100, 200]);
    }
  },
};

// Native share API wrapper
export const shareContent = async (content: {
  title?: string;
  text?: string;
  url?: string;
}) => {
  const device = detectDevice();
  
  if (device?.supportsShare) {
    try {
      await navigator.share(content);
      return { success: true };
    } catch (error) {
      console.warn('Native share failed:', error);
      return { success: false, error };
    }
  }
  
  // Fallback: copy to clipboard
  if (content.url && 'clipboard' in navigator) {
    try {
      await navigator.clipboard.writeText(content.url);
      return { success: true, method: 'clipboard' };
    } catch (error) {
      return { success: false, error, method: 'clipboard' };
    }
  }
  
  return { success: false, error: 'No sharing method available' };
};

// Swipe gesture detection
export const useSwipeGesture = (
  onSwipeLeft?: () => void,
  onSwipeRight?: () => void,
  threshold = MOBILE_DESIGN.SWIPE_THRESHOLD
) => {
  let startX = 0;
  let startY = 0;
  let endX = 0;
  let endY = 0;

  const handleTouchStart = (e: TouchEvent) => {
    const touch = e.touches[0];
    startX = touch.clientX;
    startY = touch.clientY;
  };

  const handleTouchMove = (e: TouchEvent) => {
    const touch = e.touches[0];
    endX = touch.clientX;
    endY = touch.clientY;
  };

  const handleTouchEnd = () => {
    const deltaX = endX - startX;
    const deltaY = endY - startY;
    
    // Check if horizontal swipe is more significant than vertical
    if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > threshold) {
      if (deltaX > 0 && onSwipeRight) {
        onSwipeRight();
        hapticFeedback.light();
      } else if (deltaX < 0 && onSwipeLeft) {
        onSwipeLeft();
        hapticFeedback.light();
      }
    }
  };

  return {
    onTouchStart: handleTouchStart,
    onTouchMove: handleTouchMove,
    onTouchEnd: handleTouchEnd,
  };
};

// Mobile viewport utilities
export const isMobileViewport = () => {
  if (typeof window === 'undefined') return false;
  return window.innerWidth < MOBILE_BREAKPOINTS.LG;
};

export const isSmallMobile = () => {
  if (typeof window === 'undefined') return false;
  return window.innerWidth < MOBILE_BREAKPOINTS.SM;
};

// Bottom sheet utilities
export const bottomSheetVariants = {
  hidden: {
    y: '100%',
    opacity: 0,
  },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      type: 'spring',
      damping: 30,
      stiffness: 400,
    },
  },
  exit: {
    y: '100%',
    opacity: 0,
    transition: {
      duration: 0.2,
    },
  },
};

// Pull-to-refresh utilities
export const usePullToRefresh = (onRefresh: () => void | Promise<void>) => {
  let startY = 0;
  let currentY = 0;
  let isPulling = false;

  const handleTouchStart = (e: TouchEvent) => {
    startY = e.touches[0].clientY;
  };

  const handleTouchMove = (e: TouchEvent) => {
    currentY = e.touches[0].clientY;
    const pullDistance = currentY - startY;
    
    // Only allow pull when at top of page
    if (window.scrollY === 0 && pullDistance > 0) {
      isPulling = true;
      
      // Add visual feedback for pull distance
      if (pullDistance > MOBILE_DESIGN.PULL_TO_REFRESH_THRESHOLD) {
        hapticFeedback.light();
      }
    }
  };

  const handleTouchEnd = async () => {
    if (isPulling) {
      const pullDistance = currentY - startY;
      
      if (pullDistance > MOBILE_DESIGN.PULL_TO_REFRESH_THRESHOLD) {
        hapticFeedback.medium();
        await onRefresh();
      }
      
      isPulling = false;
    }
  };

  return {
    onTouchStart: handleTouchStart,
    onTouchMove: handleTouchMove,
    onTouchEnd: handleTouchEnd,
  };
};

// Mobile-optimized input types
export const getMobileInputType = (fieldType: string) => {
  switch (fieldType) {
    case 'number':
    case 'area':
    case 'cost':
      return 'number'; // Shows numeric keypad
    case 'email':
      return 'email'; // Shows email keyboard
    case 'phone':
      return 'tel'; // Shows phone keypad
    case 'url':
      return 'url'; // Shows URL keyboard
    default:
      return 'text';
  }
};

// Mobile-optimized class names
export const mobileClasses = {
  // Touch targets
  touchTarget: 'min-h-[44px] min-w-[44px]',
  largeTouchTarget: 'min-h-[56px] min-w-[56px]',
  
  // Typography
  mobileFriendlyText: 'text-base leading-relaxed', // 16px minimum for iOS
  
  // Spacing
  mobilePadding: 'px-4 py-3 sm:px-6 sm:py-4',
  mobileMargin: 'mx-4 my-3 sm:mx-6 sm:my-4',
  
  // Safe areas
  safeAreaPadding: 'pb-[env(safe-area-inset-bottom)]',
  
  // Scrolling
  mobileScroll: 'overflow-x-hidden overflow-y-auto',
  horizontalScroll: 'overflow-x-auto scrollbar-hide',
  
  // Cards and containers
  mobileCard: 'rounded-t-2xl sm:rounded-lg',
  mobileModal: 'fixed inset-x-0 bottom-0 sm:relative sm:inset-auto',
};

// Prevent zoom on input focus (iOS Safari)
export const preventZoomOnFocus = () => {
  if (typeof document === 'undefined') return;
  
  const viewport = document.querySelector('meta[name="viewport"]');
  if (viewport) {
    const content = viewport.getAttribute('content');
    if (content && !content.includes('maximum-scale')) {
      viewport.setAttribute('content', `${content}, maximum-scale=1, user-scalable=no`);
    }
  }
};

// Restore zoom capability
export const restoreZoom = () => {
  if (typeof document === 'undefined') return;
  
  const viewport = document.querySelector('meta[name="viewport"]');
  if (viewport) {
    const content = viewport.getAttribute('content');
    if (content) {
      const cleanContent = content
        .replace(/, maximum-scale=1/g, '')
        .replace(/, user-scalable=no/g, '');
      viewport.setAttribute('content', cleanContent);
    }
  }
};