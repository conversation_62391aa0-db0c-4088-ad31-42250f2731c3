/**
 * Mobile Utility Functions
 * Provides mobile-specific functionality and optimizations
 */

/**
 * Mobile design constants
 */
export const MOBILE_DESIGN = {
  // Breakpoints
  breakpoints: {
    mobile: 768,
    tablet: 1024,
    desktop: 1200
  },
  
  // Touch targets
  touchTarget: {
    minSize: 44,
    recommendedSize: 48
  },
  
  // Gestures
  gestures: {
    swipeThreshold: 50,
    pullThreshold: 80,
    velocityThreshold: 0.3
  },
  
  // Timing
  animations: {
    fast: 200,
    normal: 300,
    slow: 500
  },
  
  // Safe areas
  safeArea: {
    top: 'env(safe-area-inset-top)',
    bottom: 'env(safe-area-inset-bottom)',
    left: 'env(safe-area-inset-left)',
    right: 'env(safe-area-inset-right)'
  }
};

export interface DeviceInfo {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  isAndroid: boolean;
  isIOS: boolean;
  hasTouch: boolean;
  userAgent: string;
}

export interface ShareData {
  title?: string;
  text?: string;
  url?: string;
}

/**
 * Detect device type and capabilities
 */
export const detectDevice = (): DeviceInfo => {
  if (typeof window === 'undefined') {
    return {
      isMobile: false,
      isTablet: false,
      isDesktop: true,
      isAndroid: false,
      isIOS: false,
      hasTouch: false,
      userAgent: ''
    };
  }

  const userAgent = navigator.userAgent;
  const hasTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
  
  const isMobile = /iPhone|iPod|Android|webOS|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
  const isTablet = /iPad|Android.*(?!Mobile)/i.test(userAgent) && window.innerWidth >= 768;
  const isAndroid = /Android/i.test(userAgent);
  const isIOS = /iPhone|iPad|iPod/i.test(userAgent);

  return {
    isMobile: isMobile && !isTablet,
    isTablet,
    isDesktop: !isMobile && !isTablet,
    isAndroid,
    isIOS,
    hasTouch,
    userAgent
  };
};

/**
 * Check if current viewport is mobile-sized
 */
export const isMobileViewport = (): boolean => {
  if (typeof window === 'undefined') return false;
  return window.innerWidth < 768;
};

/**
 * Share content using native share API or fallback
 */
export const shareContent = async (data: ShareData): Promise<boolean> => {
  if (typeof window === 'undefined') return false;

  // Try native share API first
  if (navigator.share && navigator.canShare && navigator.canShare(data)) {
    try {
      await navigator.share(data);
      return true;
    } catch (error) {
      console.warn('Native share failed:', error);
    }
  }

  // Fallback to clipboard
  if (data.url && navigator.clipboard) {
    try {
      await navigator.clipboard.writeText(data.url);
      return true;
    } catch (error) {
      console.warn('Clipboard write failed:', error);
    }
  }

  return false;
};

/**
 * Prevent zoom on input focus (iOS Safari)
 */
export const preventZoomOnFocus = (element: HTMLElement): void => {
  if (typeof window === 'undefined') return;
  
  const viewport = document.querySelector('meta[name="viewport"]');
  if (viewport && detectDevice().isIOS) {
    const originalContent = viewport.getAttribute('content') || '';
    
    element.addEventListener('focus', () => {
      viewport.setAttribute('content', `${originalContent}, user-scalable=no`);
    });
    
    element.addEventListener('blur', () => {
      viewport.setAttribute('content', originalContent);
    });
  }
};

/**
 * Restore zoom capability
 */
export const restoreZoom = (): void => {
  if (typeof window === 'undefined') return;
  
  const viewport = document.querySelector('meta[name="viewport"]');
  if (viewport) {
    const content = viewport.getAttribute('content') || '';
    const cleanContent = content.replace(/, user-scalable=no/g, '');
    viewport.setAttribute('content', cleanContent);
  }
};

/**
 * Get appropriate input type for mobile keyboards
 */
export const getMobileInputType = (fieldType: string): string => {
  const typeMap: Record<string, string> = {
    number: 'number',
    phone: 'tel',
    email: 'email',
    url: 'url',
    area: 'number',
    cost: 'number',
    text: 'text'
  };
  
  return typeMap[fieldType] || 'text';
};

/**
 * Trigger haptic feedback on supported devices
 */
export const hapticFeedback = (type: 'light' | 'medium' | 'heavy' = 'light'): void => {
  if (typeof window === 'undefined') return;
  
  // @ts-ignore - Haptic feedback API
  if (window.navigator && window.navigator.vibrate) {
    const patterns: Record<string, number[]> = {
      light: [10],
      medium: [50],
      heavy: [100]
    };
    
    window.navigator.vibrate(patterns[type]);
  }
};

/**
 * Mobile-optimized CSS classes
 */
export const mobileClasses = {
  // Touch-friendly sizing
  touchTarget: 'min-h-[44px] min-w-[44px]',
  touchButton: 'min-h-[48px] px-6 py-3',
  touchInput: 'min-h-[48px] px-4 py-3 text-base',
  
  // Safe areas
  safeTop: 'pt-safe-top',
  safeBottom: 'pb-safe-bottom',
  safeLeft: 'pl-safe-left',
  safeRight: 'pr-safe-right',
  
  // Scrolling
  scrollContainer: 'overflow-auto -webkit-overflow-scrolling-touch',
  hideScrollbar: 'scrollbar-hide',
  
  // Responsive spacing
  mobileSpacing: 'p-4 md:p-6 lg:p-8',
  mobileText: 'text-sm md:text-base',
  mobileHeading: 'text-lg md:text-xl lg:text-2xl',
  
  // Mobile layouts
  mobileStack: 'flex flex-col space-y-4',
  mobileGrid: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4',
  mobileCard: 'bg-white rounded-lg shadow-sm border p-4',
  
  // Touch interactions
  touchActive: 'active:scale-95 transition-transform',
  touchHover: 'hover:bg-gray-50 active:bg-gray-100',
  
  // Performance optimizations
  willChange: 'will-change-transform',
  gpu: 'transform-gpu',
  
  // Accessibility
  focusVisible: 'focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:outline-none',
  srOnly: 'sr-only'
};

/**
 * Check if device supports modern web features
 */
export const supportsModernFeatures = (): {
  webp: boolean;
  avif: boolean;
  webgl: boolean;
  intersectionObserver: boolean;
  passiveEventListeners: boolean;
} => {
  if (typeof window === 'undefined') {
    return {
      webp: false,
      avif: false,
      webgl: false,
      intersectionObserver: false,
      passiveEventListeners: false
    };
  }

  // Create a canvas element to test WebP/AVIF support
  const canvas = document.createElement('canvas');
  canvas.width = 1;
  canvas.height = 1;

  return {
    webp: canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0,
    avif: canvas.toDataURL('image/avif').indexOf('data:image/avif') === 0,
    webgl: !!(window.WebGLRenderingContext && canvas.getContext('webgl')),
    intersectionObserver: 'IntersectionObserver' in window,
    passiveEventListeners: (() => {
      let supportsPassive = false;
      try {
        const opts = Object.defineProperty({}, 'passive', {
          get() {
            supportsPassive = true;
            return false;
          }
        });
        window.addEventListener('test', () => {}, opts);
        window.removeEventListener('test', () => {}, opts);
      } catch (e) {
        supportsPassive = false;
      }
      return supportsPassive;
    })()
  };
};

/**
 * Optimize image loading for mobile
 */
export const getOptimizedImageSrc = (
  src: string,
  width: number,
  quality = 75
): string => {
  if (typeof window === 'undefined') return src;
  
  const features = supportsModernFeatures();
  const device = detectDevice();
  
  // Use device pixel ratio for retina displays
  const dpr = window.devicePixelRatio || 1;
  const optimizedWidth = Math.round(width * dpr);
  
  // Choose format based on support
  let format = 'webp';
  if (features.avif && device.isAndroid) {
    format = 'avif';
  } else if (!features.webp) {
    format = 'jpeg';
  }
  
  // Return optimized URL (would integrate with image optimization service)
  return `${src}?w=${optimizedWidth}&q=${quality}&f=${format}`;
};

/**
 * Mobile performance monitoring
 */
export const measurePerformance = () => {
  if (typeof window === 'undefined' || !('performance' in window)) {
    return null;
  }

  const timing = performance.timing;
  const navigation = performance.navigation;

  return {
    // Network timing
    dns: timing.domainLookupEnd - timing.domainLookupStart,
    tcp: timing.connectEnd - timing.connectStart,
    request: timing.responseStart - timing.requestStart,
    response: timing.responseEnd - timing.responseStart,
    
    // Page timing
    domContentLoaded: timing.domContentLoadedEventEnd - timing.navigationStart,
    loadComplete: timing.loadEventEnd - timing.navigationStart,
    
    // Navigation type
    navigationType: navigation.type,
    redirectCount: navigation.redirectCount,
    
    // Memory (if available)
    memory: (performance as any).memory ? {
      usedJSHeapSize: (performance as any).memory.usedJSHeapSize,
      totalJSHeapSize: (performance as any).memory.totalJSHeapSize,
      jsHeapSizeLimit: (performance as any).memory.jsHeapSizeLimit
    } : null
  };
};

/**
 * Smooth scroll to element with mobile optimizations
 */
export const scrollToElement = (
  element: HTMLElement | string,
  options: {
    offset?: number;
    behavior?: 'smooth' | 'auto';
    block?: 'start' | 'center' | 'end';
  } = {}
): void => {
  if (typeof window === 'undefined') return;
  
  const target = typeof element === 'string' 
    ? document.querySelector(element) as HTMLElement
    : element;
    
  if (!target) return;
  
  const { offset = 0, behavior = 'smooth', block = 'start' } = options;
  
  const targetPosition = target.getBoundingClientRect().top + window.pageYOffset - offset;
  
  // Use native smooth scrolling if supported
  if ('scrollBehavior' in document.documentElement.style) {
    window.scrollTo({
      top: targetPosition,
      behavior
    });
  } else {
    // Fallback for older browsers
    window.scrollTo(0, targetPosition);
  }
};

/**
 * React hook for swipe gesture detection
 */
export const useSwipeGesture = (
  onSwipeLeft?: () => void,
  onSwipeRight?: () => void,
  threshold = 50
) => {
  if (typeof window === 'undefined') {
    return {
      onTouchStart: () => {},
      onTouchMove: () => {},
      onTouchEnd: () => {}
    };
  }

  let startX = 0;
  let startY = 0;
  let currentX = 0;
  let currentY = 0;

  const handleTouchStart = (e: React.TouchEvent) => {
    startX = e.touches[0].clientX;
    startY = e.touches[0].clientY;
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    currentX = e.touches[0].clientX;
    currentY = e.touches[0].clientY;
  };

  const handleTouchEnd = () => {
    const deltaX = currentX - startX;
    const deltaY = Math.abs(currentY - startY);
    
    // Only trigger swipe if horizontal movement is greater than vertical
    if (Math.abs(deltaX) > threshold && Math.abs(deltaX) > deltaY) {
      if (deltaX > 0 && onSwipeRight) {
        onSwipeRight();
      } else if (deltaX < 0 && onSwipeLeft) {
        onSwipeLeft();
      }
    }
  };

  return {
    onTouchStart: handleTouchStart,
    onTouchMove: handleTouchMove,
    onTouchEnd: handleTouchEnd
  };
};