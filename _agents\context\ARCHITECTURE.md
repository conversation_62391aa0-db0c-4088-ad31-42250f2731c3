# Clarity Engine - System Architecture Decisions

Last Updated: 2025-01-12 20:35 IST

## Core Technology Stack

### Frontend Architecture
- **Framework**: Next.js 14+ with App Router
- **Language**: TypeScript 5+
- **Styling**: Tailwind CSS 3+ with shadcn/ui
- **State Management**: <PERSON>ust<PERSON> (global) + TanStack Query (server state)
- **Forms**: React Hook Form 7+

### Backend & Database
- **Database**: Supabase PostgreSQL 15+
- **API**: Supabase PostgREST + Next.js API routes
- **Authentication**: Supabase Auth
- **Storage**: Supabase Storage
- **Cache**: Redis via Upstash
- **Queue**: pg-boss for background jobs

### Deployment & Infrastructure  
- **Platform**: Vercel with Edge Network
- **Monitoring**: Sentry + PostHog + BetterStack
- **Testing**: Jest + React Testing Library + Playwright

## Project Structure Decisions

```
clarity-engine/
├── _agents/           # AI agent workspace and tracking
├── src/               # Application source code
│   ├── app/          # Next.js 14 app directory
│   ├── components/   # Reusable UI components
│   ├── core/         # Business logic and calculations
│   ├── lib/          # Utilities and configurations
│   ├── types/        # TypeScript definitions
│   └── styles/       # Global styles
├── data/             # Static data files
├── tests/            # Test suites
└── docs/             # Project documentation
```

## Database Schema Design

### Core Tables
- `projects` - User calculation projects
- `materials` - Material catalog with pricing
- `users` - User management (via Supabase auth)

### Key Design Patterns
- Row-Level Security for multi-tenant isolation
- JSONB for flexible calculation data storage
- Temporal pricing with validity periods
- Hierarchical material categories

## API Design Principles

### RESTful Routes
- `POST /api/calculate` - Main calculation endpoint
- `GET /api/materials` - Material catalog
- `POST /api/projects/save` - Save calculations
- `GET /api/projects` - User's saved projects

### Response Format
```typescript
interface ApiResponse<T> {
  data?: T;
  error?: string;
  success: boolean;
}
```

## Quality Standards

### Performance Targets
- Page load: < 3 seconds
- Calculation time: < 500ms  
- Bundle size: < 500KB gzipped
- Lighthouse score: > 90

### Security Requirements
- Input validation on all endpoints
- Rate limiting on calculation APIs
- CSP headers configured
- XSS prevention measures

## Integration Points

### Supabase MCP Verified
- Project URL: https://fsppzopxkxdnyqleuhpl.supabase.co
- Anonymous key available
- Database connection established
- Ready for schema creation