'use client';

import { useState, useRef } from 'react';
import { motion, PanInfo } from 'framer-motion';
import { Check, Crown, Gem, Home, ChevronLeft, ChevronRight } from 'lucide-react';
import { cn } from '@/lib/utils';
import { QUALITY_TIER_SPECS } from '@/core/calculator/constants';
import { 
  hapticFeedback, 
  isMobileViewport, 
  mobileClasses,
  useSwipeGesture 
} from '@/lib/mobile';

interface QualityTierSelectorProps {
  value: 'smart' | 'premium' | 'luxury';
  onChange: (value: 'smart' | 'premium' | 'luxury') => void;
  error?: string;
}

const tierIcons = {
  smart: Home,
  premium: Gem,
  luxury: Crown,
};

const tierColors = {
  smart: 'border-blue-500 bg-blue-50 hover:bg-blue-100',
  premium: 'border-purple-500 bg-purple-50 hover:bg-purple-100',
  luxury: 'border-amber-500 bg-amber-50 hover:bg-amber-100',
};

const selectedColors = {
  smart: 'border-blue-600 bg-blue-100 ring-2 ring-blue-500',
  premium: 'border-purple-600 bg-purple-100 ring-2 ring-purple-500',
  luxury: 'border-amber-600 bg-amber-100 ring-2 ring-amber-500',
};

export function QualityTierSelector({
  value,
  onChange,
  error,
}: QualityTierSelectorProps) {
  const tiers: Array<'smart' | 'premium' | 'luxury'> = ['smart', 'premium', 'luxury'];
  const [currentIndex, setCurrentIndex] = useState(tiers.indexOf(value));
  const [isMobile, setIsMobile] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  // Check if mobile on component mount
  useState(() => {
    setIsMobile(isMobileViewport());
    const handleResize = () => setIsMobile(isMobileViewport());
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  });

  // Swipe gesture handling for mobile
  const handleSwipeLeft = () => {
    if (currentIndex < tiers.length - 1) {
      const newIndex = currentIndex + 1;
      setCurrentIndex(newIndex);
      onChange(tiers[newIndex]);
    }
  };

  const handleSwipeRight = () => {
    if (currentIndex > 0) {
      const newIndex = currentIndex - 1;
      setCurrentIndex(newIndex);
      onChange(tiers[newIndex]);
    }
  };

  const swipeHandlers = useSwipeGesture(handleSwipeLeft, handleSwipeRight);

  // Convert touch handlers for React
  const reactSwipeHandlers = {
    onTouchStart: (e: React.TouchEvent) => swipeHandlers.onTouchStart(e.nativeEvent),
    onTouchMove: (e: React.TouchEvent) => swipeHandlers.onTouchMove(e.nativeEvent),
    onTouchEnd: (e: React.TouchEvent) => swipeHandlers.onTouchEnd(),
  };

  const handleTierSelect = (tier: 'smart' | 'premium' | 'luxury') => {
    const newIndex = tiers.indexOf(tier);
    setCurrentIndex(newIndex);
    onChange(tier);
    hapticFeedback.light();
  };

  const handleNavigation = (direction: 'prev' | 'next') => {
    if (direction === 'prev' && currentIndex > 0) {
      handleSwipeRight();
    } else if (direction === 'next' && currentIndex < tiers.length - 1) {
      handleSwipeLeft();
    }
    hapticFeedback.medium();
  };

  // Mobile swipeable layout
  if (isMobile) {
    return (
      <div className="space-y-4">
        <label className="text-sm font-medium text-gray-700">
          Select Quality Tier
        </label>

        {/* Mobile swipeable container */}
        <div className="relative">
          {/* Navigation arrows */}
          <div className="absolute inset-y-0 left-0 z-10 flex items-center">
            <button
              type="button"
              onClick={() => handleNavigation('prev')}
              disabled={currentIndex === 0}
              className={cn(
                'p-2 rounded-full bg-white shadow-lg border',
                'disabled:opacity-50 disabled:cursor-not-allowed',
                mobileClasses.touchTarget,
                'transition-all duration-200'
              )}
            >
              <ChevronLeft className="h-5 w-5 text-gray-600" />
            </button>
          </div>

          <div className="absolute inset-y-0 right-0 z-10 flex items-center">
            <button
              type="button"
              onClick={() => handleNavigation('next')}
              disabled={currentIndex === tiers.length - 1}
              className={cn(
                'p-2 rounded-full bg-white shadow-lg border',
                'disabled:opacity-50 disabled:cursor-not-allowed',
                mobileClasses.touchTarget,
                'transition-all duration-200'
              )}
            >
              <ChevronRight className="h-5 w-5 text-gray-600" />
            </button>
          </div>

          {/* Swipeable cards container */}
          <div
            ref={containerRef}
            className="mx-12 overflow-hidden"
            {...reactSwipeHandlers}
          >
            <motion.div
              className="flex"
              animate={{
                x: `-${currentIndex * 100}%`,
              }}
              transition={{
                type: 'spring',
                stiffness: 300,
                damping: 30,
              }}
            >
              {tiers.map((tier, index) => {
                const Icon = tierIcons[tier];
                const spec = QUALITY_TIER_SPECS[tier];
                const isSelected = value === tier;
                const isCurrent = index === currentIndex;

                return (
                  <motion.div
                    key={tier}
                    className="w-full flex-shrink-0 px-2"
                  >
                    <motion.button
                      type="button"
                      onClick={() => handleTierSelect(tier)}
                      className={cn(
                        'relative w-full p-6 border-2 rounded-xl transition-all duration-200',
                        'focus:outline-none focus:ring-2 focus:ring-offset-2',
                        mobileClasses.touchTarget,
                        isSelected ? selectedColors[tier] : tierColors[tier]
                      )}
                      whileTap={{ scale: 0.95 }}
                      animate={{
                        scale: isCurrent ? 1 : 0.95,
                        opacity: isCurrent ? 1 : 0.7,
                      }}
                    >
                      {/* Selection indicator */}
                      {isSelected && (
                        <motion.div
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          className="absolute top-3 right-3"
                        >
                          <div className="h-8 w-8 rounded-full bg-white shadow-md flex items-center justify-center">
                            <Check className="h-5 w-5 text-green-600" />
                          </div>
                        </motion.div>
                      )}

                      {/* Icon */}
                      <div className="flex justify-center mb-4">
                        <Icon className={cn(
                          'h-12 w-12',
                          isSelected
                            ? tier === 'smart'
                              ? 'text-blue-600'
                              : tier === 'premium'
                              ? 'text-purple-600'
                              : 'text-amber-600'
                            : 'text-gray-500'
                        )} />
                      </div>

                      {/* Content */}
                      <div className="text-center space-y-3">
                        <h3 className="text-lg font-semibold text-gray-900">{spec.name}</h3>
                        <p className="text-sm text-gray-600 leading-relaxed">{spec.description}</p>
                        <p className="text-base font-semibold text-gray-900">
                          {spec.priceRange}
                        </p>

                        {/* Key features */}
                        <div className="pt-3 space-y-2 text-left">
                          <p className="text-sm text-gray-600">
                            <span className="font-medium">Concrete:</span> {spec.concreteGrade}
                          </p>
                          <p className="text-sm text-gray-600">
                            <span className="font-medium">Fixtures:</span> {spec.fixtures}
                          </p>
                          <p className="text-sm text-gray-600">
                            <span className="font-medium">Flooring:</span> {spec.flooringOptions[0]}
                          </p>
                        </div>
                      </div>
                    </motion.button>
                  </motion.div>
                );
              })}
            </motion.div>
          </div>

          {/* Swipe indicator dots */}
          <div className="flex justify-center mt-4 space-x-2">
            {tiers.map((_, index) => (
              <button
                key={index}
                type="button"
                onClick={() => {
                  setCurrentIndex(index);
                  onChange(tiers[index]);
                  hapticFeedback.light();
                }}
                className={cn(
                  'w-2 h-2 rounded-full transition-all duration-200',
                  index === currentIndex ? 'bg-blue-600 w-6' : 'bg-gray-300'
                )}
              />
            ))}
          </div>

          {/* Swipe hint */}
          <p className="text-xs text-gray-500 text-center mt-2">
            Swipe left or right to explore options
          </p>
        </div>

        {/* Error message */}
        {error && (
          <motion.p
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-sm text-red-600 mt-1"
          >
            {error}
          </motion.p>
        )}
      </div>
    );
  }

  // Desktop grid layout
  return (
    <div className="space-y-2">
      <label className="text-sm font-medium text-gray-700">
        Select Quality Tier
      </label>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        {tiers.map((tier) => {
          const Icon = tierIcons[tier];
          const spec = QUALITY_TIER_SPECS[tier];
          const isSelected = value === tier;

          return (
            <motion.button
              key={tier}
              type="button"
              onClick={() => handleTierSelect(tier)}
              className={cn(
                'relative p-4 border-2 rounded-lg transition-all duration-200',
                'focus:outline-none focus:ring-2 focus:ring-offset-2',
                isSelected ? selectedColors[tier] : tierColors[tier]
              )}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              {/* Selection indicator */}
              {isSelected && (
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  className="absolute top-2 right-2"
                >
                  <div className="h-6 w-6 rounded-full bg-white shadow-md flex items-center justify-center">
                    <Check className="h-4 w-4 text-green-600" />
                  </div>
                </motion.div>
              )}

              {/* Icon */}
              <div className="flex justify-center mb-3">
                <Icon className={cn(
                  'h-10 w-10',
                  isSelected
                    ? tier === 'smart'
                      ? 'text-blue-600'
                      : tier === 'premium'
                      ? 'text-purple-600'
                      : 'text-amber-600'
                    : 'text-gray-500'
                )} />
              </div>

              {/* Content */}
              <div className="text-left space-y-2">
                <h3 className="font-semibold text-gray-900">{spec.name}</h3>
                <p className="text-sm text-gray-600">{spec.description}</p>
                <p className="text-sm font-medium text-gray-900">
                  {spec.priceRange}
                </p>

                {/* Key features */}
                <div className="pt-2 space-y-1">
                  <p className="text-xs text-gray-500">
                    <span className="font-medium">Concrete:</span> {spec.concreteGrade}
                  </p>
                  <p className="text-xs text-gray-500">
                    <span className="font-medium">Fixtures:</span> {spec.fixtures}
                  </p>
                  <p className="text-xs text-gray-500">
                    <span className="font-medium">Flooring:</span> {spec.flooringOptions[0]}
                  </p>
                </div>
              </div>
            </motion.button>
          );
        })}
      </div>

      {/* Error message */}
      {error && (
        <motion.p
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-sm text-red-600 mt-1"
        >
          {error}
        </motion.p>
      )}
    </div>
  );
}

// Default export for backward compatibility
export default QualityTierSelector;