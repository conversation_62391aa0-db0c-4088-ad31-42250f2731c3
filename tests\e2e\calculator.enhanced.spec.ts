import { test, expect, Page } from '@playwright/test';

// Test data
const testData = {
  validInput: {
    plotSize: '1200',
    floors: '2',
    quality: 'smart',
    location: 'bangalore',
    buildingType: 'residential',
  },
  invalidInput: {
    plotSize: '0',
    floors: '0',
    quality: '',
    location: '',
    buildingType: 'residential',
  },
  largeInput: {
    plotSize: '10000',
    floors: '4',
    quality: 'luxury',
    location: 'mumbai',
    buildingType: 'commercial',
  },
};

// Page Object Model
class CalculatorPage {
  constructor(private page: Page) {}

  // Locators
  get plotSizeInput() {
    return this.page.locator('[data-testid="plot-size-input"]');
  }

  get floorsSelect() {
    return this.page.locator('[data-testid="floors-select"]');
  }

  get qualitySelect() {
    return this.page.locator('[data-testid="quality-select"]');
  }

  get locationSelect() {
    return this.page.locator('[data-testid="location-select"]');
  }

  get buildingTypeSelect() {
    return this.page.locator('[data-testid="building-type-select"]');
  }

  get calculateButton() {
    return this.page.locator('[data-testid="calculate-button"]');
  }

  get resetButton() {
    return this.page.locator('[data-testid="reset-button"]');
  }

  get resultsContainer() {
    return this.page.locator('[data-testid="results-container"]');
  }

  get totalCost() {
    return this.page.locator('[data-testid="total-cost"]');
  }

  get costPerSqft() {
    return this.page.locator('[data-testid="cost-per-sqft"]');
  }

  get loadingSpinner() {
    return this.page.locator('[data-testid="loading-spinner"]');
  }

  get errorMessage() {
    return this.page.locator('[data-testid="error-message"]');
  }

  get validationError() {
    return this.page.locator('[data-testid="validation-error"]');
  }

  // Actions
  async navigate() {
    await this.page.goto('/calculator');
  }

  async fillForm(data: typeof testData.validInput) {
    await this.plotSizeInput.fill(data.plotSize);
    await this.floorsSelect.selectOption(data.floors);
    await this.qualitySelect.selectOption(data.quality);
    await this.locationSelect.selectOption(data.location);
    await this.buildingTypeSelect.selectOption(data.buildingType);
  }

  async submitForm() {
    await this.calculateButton.click();
  }

  async resetForm() {
    await this.resetButton.click();
  }

  async waitForResults() {
    await expect(this.resultsContainer).toBeVisible();
  }

  async waitForLoading() {
    await expect(this.loadingSpinner).toBeVisible();
  }

  async waitForLoadingToFinish() {
    await expect(this.loadingSpinner).not.toBeVisible();
  }
}

// Test setup
test.describe('Calculator E2E Tests', () => {
  let calculatorPage: CalculatorPage;

  test.beforeEach(async ({ page }) => {
    calculatorPage = new CalculatorPage(page);
    await calculatorPage.navigate();
  });

  test.describe('Basic Functionality', () => {
    test('should display calculator form on page load', async ({ page }) => {
      await expect(page.locator('h1')).toContainText('Construction Calculator');
      await expect(calculatorPage.plotSizeInput).toBeVisible();
      await expect(calculatorPage.floorsSelect).toBeVisible();
      await expect(calculatorPage.qualitySelect).toBeVisible();
      await expect(calculatorPage.locationSelect).toBeVisible();
      await expect(calculatorPage.calculateButton).toBeVisible();
    });

    test('should calculate construction cost with valid inputs', async ({ page }) => {
      await calculatorPage.fillForm(testData.validInput);
      await calculatorPage.submitForm();
      
      await calculatorPage.waitForLoading();
      await calculatorPage.waitForLoadingToFinish();
      await calculatorPage.waitForResults();

      await expect(calculatorPage.totalCost).toBeVisible();
      await expect(calculatorPage.costPerSqft).toBeVisible();
      
      // Verify cost values are displayed
      const totalCostText = await calculatorPage.totalCost.textContent();
      const costPerSqftText = await calculatorPage.costPerSqft.textContent();
      
      expect(totalCostText).toMatch(/₹[\d,]+/);
      expect(costPerSqftText).toMatch(/₹[\d,]+/);
    });

    test('should show validation errors for invalid inputs', async ({ page }) => {
      await calculatorPage.fillForm(testData.invalidInput);
      await calculatorPage.submitForm();

      await expect(calculatorPage.validationError).toBeVisible();
      await expect(calculatorPage.validationError).toContainText('Please enter a valid plot size');
    });

    test('should reset form when reset button is clicked', async ({ page }) => {
      await calculatorPage.fillForm(testData.validInput);
      await calculatorPage.submitForm();
      await calculatorPage.waitForResults();

      await calculatorPage.resetForm();

      await expect(calculatorPage.plotSizeInput).toHaveValue('');
      await expect(calculatorPage.resultsContainer).not.toBeVisible();
    });
  });

  test.describe('Form Validation', () => {
    test('should validate plot size input', async ({ page }) => {
      await calculatorPage.plotSizeInput.fill('0');
      await calculatorPage.plotSizeInput.blur();

      await expect(calculatorPage.validationError).toBeVisible();
      await expect(calculatorPage.validationError).toContainText('must be greater than 0');
    });

    test('should validate negative plot size', async ({ page }) => {
      await calculatorPage.plotSizeInput.fill('-100');
      await calculatorPage.plotSizeInput.blur();

      await expect(calculatorPage.validationError).toBeVisible();
      await expect(calculatorPage.validationError).toContainText('must be greater than 0');
    });

    test('should validate extremely large plot size', async ({ page }) => {
      await calculatorPage.plotSizeInput.fill('1000000');
      await calculatorPage.plotSizeInput.blur();

      await expect(calculatorPage.validationError).toBeVisible();
      await expect(calculatorPage.validationError).toContainText('cannot exceed');
    });

    test('should validate all required fields', async ({ page }) => {
      await calculatorPage.submitForm();

      const errorMessages = await page.locator('[data-testid="validation-error"]').all();
      expect(errorMessages.length).toBeGreaterThan(0);
    });
  });

  test.describe('Loading States', () => {
    test('should show loading spinner during calculation', async ({ page }) => {
      await calculatorPage.fillForm(testData.validInput);
      await calculatorPage.submitForm();

      await expect(calculatorPage.loadingSpinner).toBeVisible();
      await expect(calculatorPage.calculateButton).toBeDisabled();
    });

    test('should hide loading spinner after calculation', async ({ page }) => {
      await calculatorPage.fillForm(testData.validInput);
      await calculatorPage.submitForm();

      await calculatorPage.waitForLoadingToFinish();
      
      await expect(calculatorPage.loadingSpinner).not.toBeVisible();
      await expect(calculatorPage.calculateButton).toBeEnabled();
    });
  });

  test.describe('Error Handling', () => {
    test('should handle network errors gracefully', async ({ page }) => {
      // Mock network failure
      await page.route('**/api/calculate', (route) => {
        route.abort('failed');
      });

      await calculatorPage.fillForm(testData.validInput);
      await calculatorPage.submitForm();

      await expect(calculatorPage.errorMessage).toBeVisible();
      await expect(calculatorPage.errorMessage).toContainText('network error');
    });

    test('should handle API errors', async ({ page }) => {
      // Mock API error
      await page.route('**/api/calculate', (route) => {
        route.fulfill({
          status: 500,
          body: JSON.stringify({ error: 'Internal server error' }),
        });
      });

      await calculatorPage.fillForm(testData.validInput);
      await calculatorPage.submitForm();

      await expect(calculatorPage.errorMessage).toBeVisible();
    });
  });

  test.describe('Different Input Scenarios', () => {
    test('should handle large construction projects', async ({ page }) => {
      await calculatorPage.fillForm(testData.largeInput);
      await calculatorPage.submitForm();

      await calculatorPage.waitForResults();
      
      const totalCostText = await calculatorPage.totalCost.textContent();
      const costValue = parseInt(totalCostText?.replace(/[^\d]/g, '') || '0');
      
      expect(costValue).toBeGreaterThan(10000000); // Should be > 1 crore for large projects
    });

    test('should calculate different costs for different quality tiers', async ({ page }) => {
      // Test smart quality
      await calculatorPage.fillForm({ ...testData.validInput, quality: 'smart' });
      await calculatorPage.submitForm();
      await calculatorPage.waitForResults();
      
      const smartCost = await calculatorPage.totalCost.textContent();
      await calculatorPage.resetForm();

      // Test luxury quality
      await calculatorPage.fillForm({ ...testData.validInput, quality: 'luxury' });
      await calculatorPage.submitForm();
      await calculatorPage.waitForResults();
      
      const luxuryCost = await calculatorPage.totalCost.textContent();
      
      const smartValue = parseInt(smartCost?.replace(/[^\d]/g, '') || '0');
      const luxuryValue = parseInt(luxuryCost?.replace(/[^\d]/g, '') || '0');
      
      expect(luxuryValue).toBeGreaterThan(smartValue);
    });

    test('should calculate different costs for different locations', async ({ page }) => {
      // Test Bangalore
      await calculatorPage.fillForm({ ...testData.validInput, location: 'bangalore' });
      await calculatorPage.submitForm();
      await calculatorPage.waitForResults();
      
      const bangaloreCost = await calculatorPage.totalCost.textContent();
      await calculatorPage.resetForm();

      // Test Mumbai
      await calculatorPage.fillForm({ ...testData.validInput, location: 'mumbai' });
      await calculatorPage.submitForm();
      await calculatorPage.waitForResults();
      
      const mumbaiCost = await calculatorPage.totalCost.textContent();
      
      const bangaloreValue = parseInt(bangaloreCost?.replace(/[^\d]/g, '') || '0');
      const mumbaiValue = parseInt(mumbaiCost?.replace(/[^\d]/g, '') || '0');
      
      expect(mumbaiValue).toBeGreaterThan(bangaloreValue); // Mumbai should be more expensive
    });
  });

  test.describe('Responsive Design', () => {
    test('should work on mobile devices', async ({ page, isMobile }) => {
      if (isMobile) {
        await calculatorPage.fillForm(testData.validInput);
        await calculatorPage.submitForm();
        await calculatorPage.waitForResults();

        await expect(calculatorPage.totalCost).toBeVisible();
        
        // Check if mobile-specific UI elements are present
        await expect(page.locator('[data-testid="mobile-results"]')).toBeVisible();
      }
    });

    test('should adapt to tablet viewport', async ({ page, browserName }) => {
      await page.setViewportSize({ width: 768, height: 1024 });
      
      await calculatorPage.fillForm(testData.validInput);
      await calculatorPage.submitForm();
      await calculatorPage.waitForResults();

      await expect(calculatorPage.totalCost).toBeVisible();
      
      // Verify layout adapts to tablet size
      const formContainer = page.locator('[data-testid="form-container"]');
      await expect(formContainer).toHaveClass(/tablet-layout/);
    });
  });

  test.describe('Accessibility', () => {
    test('should have proper ARIA labels', async ({ page }) => {
      await expect(calculatorPage.plotSizeInput).toHaveAttribute('aria-label');
      await expect(calculatorPage.floorsSelect).toHaveAttribute('aria-label');
      await expect(calculatorPage.qualitySelect).toHaveAttribute('aria-label');
      await expect(calculatorPage.locationSelect).toHaveAttribute('aria-label');
      await expect(calculatorPage.calculateButton).toHaveAttribute('aria-label');
    });

    test('should support keyboard navigation', async ({ page }) => {
      await page.keyboard.press('Tab');
      await expect(calculatorPage.plotSizeInput).toBeFocused();
      
      await page.keyboard.press('Tab');
      await expect(calculatorPage.floorsSelect).toBeFocused();
      
      await page.keyboard.press('Tab');
      await expect(calculatorPage.qualitySelect).toBeFocused();
      
      await page.keyboard.press('Tab');
      await expect(calculatorPage.locationSelect).toBeFocused();
      
      await page.keyboard.press('Tab');
      await expect(calculatorPage.buildingTypeSelect).toBeFocused();
      
      await page.keyboard.press('Tab');
      await expect(calculatorPage.calculateButton).toBeFocused();
    });

    test('should submit form with Enter key', async ({ page }) => {
      await calculatorPage.fillForm(testData.validInput);
      await calculatorPage.plotSizeInput.focus();
      await page.keyboard.press('Enter');

      await calculatorPage.waitForLoading();
      await calculatorPage.waitForResults();

      await expect(calculatorPage.totalCost).toBeVisible();
    });
  });

  test.describe('Performance', () => {
    test('should load page within acceptable time', async ({ page }) => {
      const startTime = Date.now();
      await calculatorPage.navigate();
      const loadTime = Date.now() - startTime;

      expect(loadTime).toBeLessThan(3000); // Should load within 3 seconds
    });

    test('should calculate results within acceptable time', async ({ page }) => {
      await calculatorPage.fillForm(testData.validInput);
      
      const startTime = Date.now();
      await calculatorPage.submitForm();
      await calculatorPage.waitForResults();
      const calculationTime = Date.now() - startTime;

      expect(calculationTime).toBeLessThan(5000); // Should calculate within 5 seconds
    });
  });

  test.describe('Edge Cases', () => {
    test('should handle concurrent calculations', async ({ page }) => {
      const promises = [];
      
      for (let i = 0; i < 3; i++) {
        promises.push(
          (async () => {
            await calculatorPage.fillForm(testData.validInput);
            await calculatorPage.submitForm();
            await calculatorPage.waitForResults();
          })()
        );
      }

      await Promise.all(promises);
      await expect(calculatorPage.totalCost).toBeVisible();
    });

    test('should handle page refresh during calculation', async ({ page }) => {
      await calculatorPage.fillForm(testData.validInput);
      await calculatorPage.submitForm();
      
      // Refresh page during calculation
      await page.reload();
      
      // Form should be reset
      await expect(calculatorPage.plotSizeInput).toHaveValue('');
      await expect(calculatorPage.resultsContainer).not.toBeVisible();
    });

    test('should handle browser back/forward navigation', async ({ page }) => {
      await calculatorPage.fillForm(testData.validInput);
      await calculatorPage.submitForm();
      await calculatorPage.waitForResults();

      // Navigate to another page
      await page.goto('/about');
      
      // Navigate back
      await page.goBack();
      
      // Form should maintain state
      await expect(calculatorPage.plotSizeInput).toHaveValue(testData.validInput.plotSize);
    });
  });
});

// Performance-specific tests
test.describe('Performance Tests', () => {
  test('should meet Core Web Vitals thresholds', async ({ page }) => {
    await page.goto('/calculator');
    
    // Measure LCP (Largest Contentful Paint)
    const lcp = await page.evaluate(() => {
      return new Promise((resolve) => {
        new PerformanceObserver((entryList) => {
          const entries = entryList.getEntries();
          const lastEntry = entries[entries.length - 1];
          resolve(lastEntry.startTime);
        }).observe({ entryTypes: ['largest-contentful-paint'] });
      });
    });
    
    expect(lcp).toBeLessThan(2500); // LCP should be under 2.5 seconds
  });

  test('should have minimal memory usage', async ({ page }) => {
    await page.goto('/calculator');
    
    const memoryUsage = await page.evaluate(() => {
      return (performance as any).memory?.usedJSHeapSize || 0;
    });
    
    expect(memoryUsage).toBeLessThan(50 * 1024 * 1024); // Should use less than 50MB
  });
});

// Accessibility-specific tests
test.describe('Accessibility Tests', () => {
  test('should pass automated accessibility checks', async ({ page }) => {
    await page.goto('/calculator');
    
    // This would require additional setup with axe-playwright
    // const results = await page.axe();
    // expect(results.violations).toHaveLength(0);
  });

  test('should support screen reader navigation', async ({ page }) => {
    await page.goto('/calculator');
    
    // Check for proper heading structure
    const headings = await page.locator('h1, h2, h3, h4, h5, h6').all();
    expect(headings.length).toBeGreaterThan(0);
    
    // Check for proper form labels
    const labels = await page.locator('label').all();
    expect(labels.length).toBeGreaterThan(0);
  });
});