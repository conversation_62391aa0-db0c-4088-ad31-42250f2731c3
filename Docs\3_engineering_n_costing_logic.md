Spoke Document 3: Engineering & Costing Logic Handbook v2.0
===========================================================

**Document ID:** SD-003  
**Version:** 2.0  
**Owner:** Product Manager + Civil Engineering Expert  
**Audience:** <PERSON><PERSON> Developers, QA Engineers, Civil Engineers, Product Team  
**Linked to:** Master PRD v2.0, Technical Design Document v2.0  
**Status:** Final - Ready for Implementation

* * *

Table of Contents
-----------------

**Part 1: Foundation Concepts**

1.  Construction Cost Fundamentals
2.  Indian Construction Standards & Codes
3.  Regional Variations & Factors
4.  Quality Tiers & Material Classification

**Part 2: Calculation Methodologies** 5. Area Calculations & Space Planning 6. Structural Calculations 7. Finishing Work Calculations 8. MEP (Mechanical, Electrical, Plumbing) Calculations 9. External & Site Development Calculations

**Part 3: Material Specifications & Consumption** 10. Material Categories & Properties 11. Consumption Rates & Formulas 12. Wastage Factors & Adjustments 13. Brand Mapping & Quality Correlation

**Part 4: Labor & Professional Services** 14. Labor Categories & Productivity Rates 15. Professional Fee Structures 16. Statutory Charges & Compliance Costs 17. Timeline Calculations

**Part 5: Advanced Logic & Edge Cases** 18. Site Condition Adjustments 19. Seasonal & Market Variations 20. Cost Optimization Algorithms 21. Validation Rules & Constraints

* * *

Part 1: Foundation Concepts
---------------------------

### 1\. Construction Cost Fundamentals

#### 1.1 Cost Components Breakdown

typescript

    // Core cost components in Indian construction
    const constructionCostComponents = {
      // Direct Costs (65-75% of total)
      directCosts: {
        materials: {
          percentage: '40-50%',
          components: [
            'Cement, steel, sand, aggregate',
            'Bricks/blocks',
            'Finishing materials',
            'Fixtures and fittings'
          ]
        },
        
        labor: {
          percentage: '20-25%',
          components: [
            'Skilled workers',
            'Semi-skilled workers',
            'Unskilled helpers',
            'Contractor margins'
          ]
        },
        
        equipment: {
          percentage: '3-5%',
          components: [
            'Machinery rental',
            'Tools and tackles',
            'Scaffolding',
            'Safety equipment'
          ]
        }
      },
      
      // Indirect Costs (25-35% of total)
      indirectCosts: {
        professionalFees: {
          percentage: '8-12%',
          components: [
            'Architect (3-5%)',
            'Structural consultant (1-2%)',
            'MEP consultant (0.5-1%)',
            'Project management (2-3%)',
            'Site supervision (1-2%)'
          ]
        },
        
        statutory: {
          percentage: '5-8%',
          components: [
            'Plan approval fees',
            'Development charges',
            'Utility connections',
            'Property tax (first year)',
            'Legal documentation'
          ]
        },
        
        contingency: {
          percentage: '5-10%',
          factors: [
            'Design changes',
            'Market fluctuations',
            'Unforeseen site conditions',
            'Weather delays'
          ]
        },
        
        financing: {
          percentage: '3-5%',
          components: [
            'Interest during construction',
            'Bank charges',
            'Insurance premiums'
          ]
        }
      }
    };

#### 1.2 Cost Estimation Methods

typescript

    // Different estimation methodologies
    const estimationMethods = {
      // Preliminary estimate methods
      preliminary: {
        plinthArea: {
          description: 'Cost per square foot of built-up area',
          accuracy: '±20-25%',
          
          calculation: (builtUpArea: number, ratePerSqft: number) => {
            return builtUpArea * ratePerSqft;
          },
          
          rates: {
            economy: { min: 1400, max: 1800 },
            standard: { min: 1800, max: 2200 },
            premium: { min: 2200, max: 2800 },
            luxury: { min: 2800, max: 4000 }
          }
        },
        
        cubicContent: {
          description: 'Cost per cubic foot of building volume',
          accuracy: '±15-20%',
          usage: 'For buildings with varying heights',
          
          calculation: (volume: number, ratePerCuft: number) => {
            return volume * ratePerCuft;
          }
        }
      },
      
      // Detailed estimate methods
      detailed: {
        itemRate: {
          description: 'Detailed BOQ with item-wise rates',
          accuracy: '±5-10%',
          
          process: [
            'Quantity takeoff from drawings',
            'Apply standard consumption rates',
            'Multiply by current market rates',
            'Add labor and overheads'
          ]
        },
        
        unitRate: {
          description: 'Composite rates for work items',
          accuracy: '±8-12%',
          
          example: {
            item: 'RCC work',
            unit: 'cum',
            includes: ['Concrete', 'Steel', 'Shuttering', 'Labor'],
            rate: 8500 // per cum
          }
        }
      }
    };

### 2\. Indian Construction Standards & Codes

#### 2.1 Relevant IS Codes

typescript

    // Indian Standards for construction
    const indianStandards = {
      // Structural design codes
      structural: {
        'IS 456:2000': {
          title: 'Plain and Reinforced Concrete',
          relevance: 'Concrete mix design, cover requirements, durability',
          
          keyProvisions: {
            minimumGrade: 'M20 for RCC',
            nominalCover: {
              mild: 20, // mm
              moderate: 30,
              severe: 45,
              verySereve: 50,
              extreme: 75
            },
            waterCementRatio: {
              mild: 0.55,
              moderate: 0.50,
              severe: 0.45,
              verySereve: 0.45,
              extreme: 0.40
            }
          }
        },
        
        'IS 800:2007': {
          title: 'General Construction in Steel',
          relevance: 'Steel structure design',
          
          keyProvisions: {
            steelGrade: 'Fe 250 to Fe 550',
            safetyFactors: {
              dead: 1.5,
              live: 1.5,
              wind: 1.5,
              earthquake: 1.2
            }
          }
        },
        
        'IS 1893:2016': {
          title: 'Earthquake Resistant Design',
          relevance: 'Seismic design requirements',
          
          seismicZones: {
            'II': { factor: 0.10, risk: 'Low' },
            'III': { factor: 0.16, risk: 'Moderate' },
            'IV': { factor: 0.24, risk: 'Severe' },
            'V': { factor: 0.36, risk: 'Very Severe' }
          }
        }
      },
      
      // Material standards
      materials: {
        'IS 269:2015': {
          title: 'Ordinary Portland Cement',
          grades: ['33', '43', '53'],
          
          requirements: {
            fineness: '225 m²/kg minimum',
            settingTime: {
              initial: '30 min minimum',
              final: '600 min maximum'
            },
            compressiveStrength: {
              '3days': { '33': 16, '43': 23, '53': 27 },
              '7days': { '33': 22, '43': 33, '53': 37 },
              '28days': { '33': 33, '43': 43, '53': 53 }
            }
          }
        },
        
        'IS 1786:2008': {
          title: 'High Strength Deformed Steel Bars',
          grades: ['Fe 415', 'Fe 500', 'Fe 550', 'Fe 600'],
          
          properties: {
            yieldStrength: {
              'Fe 415': 415,
              'Fe 500': 500,
              'Fe 550': 550,
              'Fe 600': 600
            },
            elongation: {
              'Fe 415': 14.5,
              'Fe 500': 12.0,
              'Fe 550': 10.0,
              'Fe 600': 10.0
            }
          }
        }
      },
      
      // Measurement standards
      measurement: {
        'IS 1200': {
          title: 'Method of Measurement of Building Works',
          parts: {
            'Part 1': 'Earthwork',
            'Part 2': 'Concrete works',
            'Part 3': 'Brickwork',
            'Part 4': 'Stone masonry',
            'Part 5': 'Formwork',
            'Part 6': 'Steel work',
            'Part 8': 'Wood work',
            'Part 9': 'Roof covering',
            'Part 10': 'Ceiling and lining',
            'Part 11': 'Plastering and pointing',
            'Part 12': 'Glazing',
            'Part 13': 'Painting and polishing',
            'Part 15': 'Flooring'
          },
          
          measurementRules: {
            earthwork: 'Measured in cum, classified by depth',
            concrete: 'Measured in cum, excluding steel',
            brickwork: 'Measured in cum, deducting openings > 0.1 sqm',
            plaster: 'Measured in sqm, stating thickness',
            painting: 'Measured in sqm, stating coats'
          }
        }
      }
    };

#### 2.2 CPWD Standards

typescript

    // Central Public Works Department standards
    const cpwdStandards = {
      // CPWD Specifications
      specifications: {
        volume1: {
          title: 'Civil Works',
          chapters: [
            'Site preparation',
            'Foundation',
            'Superstructure',
            'Roofing',
            'Finishing works'
          ]
        },
        
        volume2: {
          title: 'Electrical and Mechanical',
          chapters: [
            'Electrical installations',
            'Air conditioning',
            'Fire fighting',
            'Lifts and escalators'
          ]
        }
      },
      
      // CPWD Analysis of Rates
      analysisOfRates: {
        purpose: 'Standard rates for government works',
        
        components: {
          material: 'Market rates + 1% storage',
          labor: 'Minimum wages as per state',
          sundries: '2.5% of labor cost',
          contractorProfit: '10% on labor',
          waterCharges: '1% of total',
          overhead: '15% overall'
        },
        
        // Sample analysis
        sampleAnalysis: {
          item: 'M20 Concrete',
          unit: 'cum',
          
          materials: [
            { item: 'Cement', qty: 6.68, unit: 'bags', rate: 380 },
            { item: 'Sand', qty: 0.48, unit: 'cum', rate: 1800 },
            { item: 'Aggregate 20mm', qty: 0.64, unit: 'cum', rate: 1200 },
            { item: 'Aggregate 10mm', qty: 0.32, unit: 'cum', rate: 1200 }
          ],
          
          labor: [
            { type: 'Mason', qty: 0.37, unit: 'day', rate: 800 },
            { type: 'Helper', qty: 3.7, unit: 'day', rate: 500 },
            { type: 'Mixer operator', qty: 0.15, unit: 'day', rate: 600 }
          ]
        }
      }
    };

### 3\. Regional Variations & Factors

#### 3.1 Location-Based Cost Factors

typescript

    // Regional cost variations across India
    const regionalFactors = {
      // Metro cities
      metros: {
        delhi: {
          baseFactor: 1.0,
          laborAvailability: 'High',
          materialAvailability: 'Excellent',
          
          specificFactors: {
            pollution: { impact: '+2%', reason: 'Construction restrictions' },
            congestion: { impact: '+3%', reason: 'Transportation delays' },
            regulations: { impact: '+5%', reason: 'Strict compliance' }
          },
          
          zones: {
            centralDelhi: 1.15,
            southDelhi: 1.10,
            eastDelhi: 0.95,
            westDelhi: 1.00,
            northDelhi: 0.98
          }
        },
        
        mumbai: {
          baseFactor: 1.20,
          laborAvailability: 'High',
          materialAvailability: 'Good',
          
          specificFactors: {
            monsoon: { impact: '+8%', reason: '4 month heavy rains' },
            spaceConstraint: { impact: '+10%', reason: 'Limited storage' },
            transportation: { impact: '+5%', reason: 'Traffic and logistics' }
          }
        },
        
        bangalore: {
          baseFactor: 0.95,
          laborAvailability: 'Moderate',
          materialAvailability: 'Good',
          
          specificFactors: {
            waterScarcity: { impact: '+3%', reason: 'Tanker water' },
            skilledLabor: { impact: '-2%', reason: 'Available skilled workers' }
          }
        }
      },
      
      // Tier 2 cities
      tier2: {
        pune: { baseFactor: 0.90 },
        hyderabad: { baseFactor: 0.92 },
        chennai: { baseFactor: 0.93 },
        kolkata: { baseFactor: 0.88 },
        ahmedabad: { baseFactor: 0.87 },
        chandigarh: { baseFactor: 0.91 }
      },
      
      // Cost index calculation
      calculateLocationFactor: (city: string, area?: string): number => {
        const cityFactor = regionalFactors.metros[city]?.baseFactor || 
                          regionalFactors.tier2[city]?.baseFactor || 
                          0.85;
        
        const areaFactor = area ? 
          (regionalFactors.metros[city]?.zones[area] || 1.0) : 
          1.0;
        
        return cityFactor * areaFactor;
      }
    };

#### 3.2 Climate & Environmental Factors

typescript

    // Climate-based adjustments
    const climateFactors = {
      // Rainfall impact
      rainfall: {
        heavy: {
          cities: ['Mumbai', 'Cherrapunji', 'Mangalore'],
          monthlyRainfall: '>400mm',
          
          impacts: {
            construction: {
              workingDays: -25,
              laborProductivity: -20,
              materialDamage: +3,
              waterproofingCost: +15
            },
            
            design: {
              roofSlope: 'Minimum 30°',
              drainage: 'Enhanced requirements',
              foundation: 'Anti-moisture treatment'
            }
          }
        },
        
        moderate: {
          cities: ['Delhi', 'Bangalore', 'Pune'],
          monthlyRainfall: '100-400mm',
          impacts: {
            construction: {
              workingDays: -10,
              laborProductivity: -5
            }
          }
        }
      },
      
      // Temperature extremes
      temperature: {
        hot: {
          cities: ['Delhi', 'Ahmedabad', 'Jaipur'],
          summerTemp: '>45°C',
          
          impacts: {
            materials: {
              concrete: 'Curing challenges +5%',
              steel: 'Expansion considerations',
              paint: 'Special heat-resistant +10%'
            },
            
            labor: {
              workingHours: 'Early morning/evening',
              productivity: -15,
              waterRequirement: +200
            },
            
            design: {
              insulation: 'Mandatory roof insulation',
              orientation: 'North-south preferred',
              shading: 'Deep overhangs required'
            }
          }
        },
        
        cold: {
          cities: ['Shimla', 'Srinagar', 'Darjeeling'],
          winterTemp: '<0°C',
          
          impacts: {
            heating: 'Central heating +₹200/sqft',
            insulation: 'Wall insulation +₹150/sqft',
            materials: 'Frost-resistant specs'
          }
        }
      },
      
      // Seismic considerations
      seismic: {
        zone5: {
          cities: ['Guwahati', 'Srinagar', 'Port Blair'],
          factor: 0.36,
          
          structuralRequirements: {
            ductileDetailing: 'Mandatory',
            columnSize: '+15%',
            beamDepth: '+10%',
            steelQuantity: '+25%',
            foundationType: 'Raft recommended'
          },
          
          costImpact: {
            structure: '+18-22%',
            overall: '+8-10%'
          }
        },
        
        zone4: {
          cities: ['Delhi NCR', 'Patna', 'Amritsar'],
          factor: 0.24,
          costImpact: {
            structure: '+12-15%',
            overall: '+5-7%'
          }
        }
      }
    };

### 4\. Quality Tiers & Material Classification

#### 4.1 Quality Tier Definitions

typescript

    // Three-tier quality classification system
    const qualityTiers = {
      // Smart Choice (Economy)
      smart: {
        description: 'Best value for money with reliable quality',
        targetAudience: 'First-time homeowners, rental properties',
        priceRange: '₹1,600-2,000/sqft',
        
        characteristics: {
          materials: 'Established local brands',
          finishes: 'Standard catalog items',
          durability: '10-15 years',
          warranty: 'Basic manufacturer warranty',
          maintenance: 'Regular maintenance required'
        },
        
        specifications: {
          structure: {
            concrete: 'M20 grade',
            steel: 'Fe 500',
            bricks: 'Standard clay bricks',
            cement: 'OPC 43 grade'
          },
          
          finishes: {
            flooring: {
              hall: 'Vitrified tiles 2x2 ft',
              bedroom: 'Vitrified tiles 2x2 ft',
              kitchen: 'Anti-skid ceramic',
              bathroom: 'Anti-skid ceramic'
            },
            
            walls: {
              interior: 'Single coat putty + primer + 2 coats emulsion',
              exterior: 'Primer + 2 coats exterior emulsion',
              bathroom: 'Ceramic tiles up to 7 ft'
            },
            
            fixtures: {
              sanitaryware: 'Cera/Parryware or equivalent',
              cpFittings: 'Brass with chrome plating',
              electrical: 'Anchor/Havells switches'
            }
          }
        }
      },
      
      // Premium Selection
      premium: {
        description: 'Perfect balance of quality and aesthetics',
        targetAudience: 'Family homes, quality-conscious buyers',
        priceRange: '₹2,200-2,800/sqft',
        
        characteristics: {
          materials: 'Premium national brands',
          finishes: 'Designer catalog items',
          durability: '15-20 years',
          warranty: 'Extended warranties available',
          maintenance: 'Low maintenance'
        },
        
        specifications: {
          structure: {
            concrete: 'M25 grade',
            steel: 'Fe 500D (ductile)',
            bricks: 'AAC blocks/Fly ash bricks',
            cement: 'OPC 53 grade/PPC'
          },
          
          finishes: {
            flooring: {
              hall: 'Vitrified tiles 4x2 ft/Wooden laminate',
              bedroom: 'Wooden laminate flooring',
              kitchen: 'Vitrified tiles with skirting',
              bathroom: 'Anti-skid designer tiles'
            },
            
            walls: {
              interior: 'Double coat putty + premium emulsion',
              exterior: 'Texture paint/Premium emulsion',
              bathroom: 'Designer tiles up to ceiling',
              accent: 'Wallpaper/Wooden panels in living'
            },
            
            fixtures: {
              sanitaryware: 'Kohler/Roca or equivalent',
              cpFittings: 'Grohe/Jaquar or equivalent',
              electrical: 'Legrand/Schneider switches',
              kitchen: 'Modular with granite platform'
            }
          }
        }
      },
      
      // Luxury Collection
      luxury: {
        description: 'No compromise on quality and aesthetics',
        targetAudience: 'Luxury homes, status symbol properties',
        priceRange: '₹3,000-4,000+/sqft',
        
        characteristics: {
          materials: 'International brands and imported items',
          finishes: 'Customized and exclusive designs',
          durability: '20+ years',
          warranty: 'Comprehensive warranties',
          maintenance: 'Minimal with professional care'
        },
        
        specifications: {
          structure: {
            concrete: 'M30+ grade',
            steel: 'Fe 550D with corrosion protection',
            bricks: 'AAC blocks with insulation',
            cement: 'Premium brands with additives'
          },
          
          finishes: {
            flooring: {
              hall: 'Italian marble/Engineered wood',
              bedroom: 'Hardwood flooring',
              kitchen: 'Italian marble/Quartz',
              bathroom: 'Imported marble/Premium tiles'
            },
            
            walls: {
              interior: 'Luxury paint finishes/Venetian plaster',
              exterior: 'Stone cladding/Premium textures',
              bathroom: 'Imported tiles/Natural stone',
              accent: 'Feature walls with premium materials'
            },
            
            fixtures: {
              sanitaryware: 'Villeroy & Boch/Duravit',
              cpFittings: 'Hansgrohe/Premium Grohe',
              electrical: 'Home automation ready',
              kitchen: 'Imported modular kitchen'
            }
          }
        }
      }
    };

#### 4.2 Material Grading System

typescript

    // Material quality grading
    const materialGrading = {
      // Grading criteria
      criteria: {
        durability: {
          weight: 0.25,
          metrics: ['Lifespan', 'Weather resistance', 'Wear resistance']
        },
        
        aesthetics: {
          weight: 0.20,
          metrics: ['Finish quality', 'Design options', 'Color retention']
        },
        
        functionality: {
          weight: 0.20,
          metrics: ['Performance', 'Ease of use', 'Safety features']
        },
        
        brandValue: {
          weight: 0.15,
          metrics: ['Brand reputation', 'Service network', 'Warranty']
        },
        
        sustainability: {
          weight: 0.10,
          metrics: ['Eco-friendliness', 'Energy efficiency', 'Recyclability']
        },
        
        costEffectiveness: {
          weight: 0.10,
          metrics: ['Initial cost', 'Maintenance cost', 'Replacement frequency']
        }
      },
      
      // Grade assignment
      gradeAssignment: (scores: MaterialScores): QualityTier => {
        const weightedScore = 
          scores.durability * 0.25 +
          scores.aesthetics * 0.20 +
          scores.functionality * 0.20 +
          scores.brandValue * 0.15 +
          scores.sustainability * 0.10 +
          scores.costEffectiveness * 0.10;
        
        if (weightedScore >= 8.0) return 'luxury';
        if (weightedScore >= 6.5) return 'premium';
        return 'smart';
      },
      
      // Example grading
      examples: {
        cement: {
          'UltraTech OPC 53': {
            scores: { durability: 9, aesthetics: 7, functionality: 9, brandValue: 9, sustainability: 7, costEffectiveness: 7 },
            grade: 'premium'
          },
          'ACC OPC 43': {
            scores: { durability: 7, aesthetics: 7, functionality: 8, brandValue: 8, sustainability: 7, costEffectiveness: 8 },
            grade: 'smart'
          }
        },
        
        tiles: {
          'Kajaria Vitrified 4x2': {
            scores: { durability: 8, aesthetics: 8, functionality: 8, brandValue: 8, sustainability: 7, costEffectiveness: 7 },
            grade: 'premium'
          },
          'Local Ceramic 2x2': {
            scores: { durability: 6, aesthetics: 6, functionality: 7, brandValue: 5, sustainability: 6, costEffectiveness: 9 },
            grade: 'smart'
          },
          'Italian Marble': {
            scores: { durability: 9, aesthetics: 10, functionality: 8, brandValue: 10, sustainability: 8, costEffectiveness: 5 },
            grade: 'luxury'
          }
        }
      }
    };

* * *

Part 2: Calculation Methodologies
---------------------------------

### 5\. Area Calculations & Space Planning

#### 5.1 Area Computation Methods

typescript

    // Comprehensive area calculation system
    const areaCalculations = {
      // Plot area calculations
      plotArea: {
        regular: {
          rectangle: (length: number, width: number) => length * width,
          square: (side: number) => side * side
        },
        
        irregular: {
          // Using coordinate geometry
          polygon: (coordinates: Point[]) => {
            let area = 0;
            const n = coordinates.length;
            
            for (let i = 0; i < n; i++) {
              const j = (i + 1) % n;
              area += coordinates[i].x * coordinates[j].y;
              area -= coordinates[j].x * coordinates[i].y;
            }
            
            return Math.abs(area) / 2;
          },
          
          // Triangulation method for complex shapes
          triangulation: (shape: ComplexShape) => {
            const triangles = triangulate(shape);
            return triangles.reduce((sum, triangle) => 
              sum + calculateTriangleArea(triangle), 0
            );
          }
        },
        
        // Deductions for features
        deductions: {
          well: 50, // sqft
          septicTank: 100,
          existingStructure: 'As per measurement',
          treeCanopy: 'Projection area',
          easements: 'As per revenue records'
        }
      },
      
      // Buildable area calculations
      buildableArea: {
        // Setback calculations
        setbacks: {
          front: (plotDepth: number, roadWidth: number) => {
            // As per local building bylaws
            if (roadWidth < 9) return 3.0; // meters
            if (roadWidth < 12) return 4.5;
            if (roadWidth < 18) return 6.0;
            return 9.0;
          },
          
          rear: (plotDepth: number) => {
            if (plotDepth < 10) return 1.5;
            if (plotDepth < 15) return 2.0;
            if (plotDepth < 20) return 3.0;
            return 3.0;
          },
          
          sides: (plotWidth: number) => {
            if (plotWidth < 10) return 1.0;
            if (plotWidth < 15) return 1.5;
            return 2.0;
          }
        },
        
        // Ground coverage calculation
        groundCoverage: (plotArea: number, location: string) => {
          const maxCoverage = {
            'plotArea < 100': 0.80,
            '100 <= plotArea < 200': 0.75,
            '200 <= plotArea < 300': 0.70,
            '300 <= plotArea < 500': 0.65,
            'plotArea >= 500': 0.60
          };
          
          // Apply local bylaws
          const localFactor = getLocalCoverageFactor(location);
          return plotArea * localFactor;
        },
        
        // FAR/FSI calculations
        farCalculation: (plotArea: number, location: string) => {
          const baseFAR = {
            residential: {
              'plotArea < 100': 1.50,
              '100 <= plotArea < 200': 2.00,
              '200 <= plotArea < 300': 2.25,
              '300 <= plotArea < 500': 2.50,
              'plotArea >= 500': 2.75
            }
          };
          
          // Additional FAR for specific features
          const additionalFAR = {
            stiltParking: 0.50,
            greenBuilding: 0.25,
            affordableHousing: 0.35
          };
          
          return baseFAR + applicable(additionalFAR);
        }
      },
      
      // Built-up area calculations
      builtUpArea: {
        // Floor-wise calculations
        floorArea: {
          typical: (groundCoverage: number, efficiency: number = 0.85) => {
            return groundCoverage * efficiency;
          },
          
          stilt: (groundCoverage: number) => {
            // Only 50% counts in built-up
            return groundCoverage * 0.50;
          },
          
          basement: (groundCoverage: number, usage: 'parking' | 'habitable') => {
            if (usage === 'parking') return 0; // Not counted
            return groundCoverage; // Full area for habitable
          },
          
          terrace: (terraceArea: number, covered: boolean) => {
            return covered ? terraceArea : 0;
          }
        },
        
        // Efficiency factors
        efficiency: {
          'G': 1.00,
          'G+1': 0.95,
          'G+2': 0.90,
          'G+3': 0.85,
          'G+4+': 0.80
        },
        
        // Super built-up area
        superBuiltUp: (builtUp: number, commonAreaPercent: number = 0) => {
          return builtUp * (1 + commonAreaPercent / 100);
        },
        
        // Carpet area
        carpetArea: (builtUp: number) => {
          const wallThickness = 0.15; // 15% for walls
          const commonDeduction = 0.05; // 5% for common areas
          return builtUp * (1 - wallThickness - commonDeduction);
        }
      }
    };

#### 5.2 Space Planning Rules

typescript

    // Space planning guidelines and calculations
    const spacePlanning = {
      // Minimum room sizes as per NBC
      minimumRoomSizes: {
        // Habitable rooms
        masterBedroom: {
          min: 120, // sqft
          preferred: 150,
          luxury: 200,
          minWidth: 10 // ft
        },
        
        bedroom: {
          min: 100,
          preferred: 120,
          luxury: 150,
          minWidth: 9
        },
        
        living: {
          min: 150,
          preferred: 200,
          luxury: 300,
          minWidth: 12
        },
        
        dining: {
          min: 80,
          preferred: 100,
          luxury: 150,
          minWidth: 8
        },
        
        kitchen: {
          min: 60,
          preferred: 80,
          luxury: 120,
          minWidth: 6
        },
        
        // Non-habitable rooms
        bathroom: {
          min: 30,
          preferred: 40,
          luxury: 60,
          minWidth: 5
        },
        
        toilet: {
          min: 20,
          preferred: 25,
          luxury: 30,
          minWidth: 3
        }
      },
      
      // Space allocation algorithms
      spaceAllocation: {
        // Percentage-based allocation
        percentageBased: (totalArea: number, roomType: string) => {
          const allocations = {
            bedrooms: 0.35,
            living: 0.20,
            kitchen: 0.10,
            bathrooms: 0.10,
            circulation: 0.15,
            utility: 0.10
          };
          
          return totalArea * allocations[roomType];
        },
        
        // Priority-based allocation
        priorityBased: (availableArea: number, requirements: RoomRequirement[]) => {
          // Sort by priority
          requirements.sort((a, b) => a.priority - b.priority);
          
          const allocation = {};
          let remainingArea = availableArea;
          
          for (const req of requirements) {
            const minSize = minimumRoomSizes[req.type].min * req.count;
            if (remainingArea >= minSize) {
              allocation[req.type] = Math.min(
                minSize * 1.2, // 20% extra if available
                remainingArea * 0.3 // Max 30% of remaining
              );
              remainingArea -= allocation[req.type];
            }
          }
          
          return allocation;
        }
      },
      
      // Circulation area calculations
      circulation: {
        // Internal circulation
        internal: {
          corridor: {
            width: { min: 3.5, preferred: 4.0, luxury: 5.0 },
            area: (length: number, width: number) => length * width
          },
          
          staircase: {
            area: {
              straight: 50, // sqft
              dogLeg: 80,
              spiral: 40,
              luxury: 120
            },
            
            width: { min: 3.5, preferred: 4.0, luxury: 5.0 }
          },
          
          lift: {
            shaft: 40, // sqft
            lobby: 60,
            machineRoom: 80
          }
        },
        
        // External circulation
        external: {
          driveway: {
            width: { single: 10, double: 18 },
            length: 'As per site',
            area: (length: number, width: number) => length * width
          },
          
          walkways: {
            width: { min: 3, preferred: 4 },
            area: (length: number) => length * 3.5
          }
        }
      }
    };

### 6\. Structural Calculations

#### 6.1 Foundation Calculations

typescript

    // Foundation design and quantity calculations
    const foundationCalculations = {
      // Foundation type selection
      foundationType: {
        selection: (soilBearing: number, loads: number, waterTable: number) => {
          if (soilBearing < 100) return 'raft';
          if (waterTable < 1.5) return 'raft';
          if (loads > 50) return 'pile';
          if (soilBearing < 150) return 'strip';
          return 'isolated';
        },
        
        types: {
          isolated: {
            description: 'Individual footings under columns',
            suitable: 'Good soil, regular column grid',
            depth: 'Minimum 1.5m below GL'
          },
          
          strip: {
            description: 'Continuous footing under walls',
            suitable: 'Load bearing structures',
            depth: 'Minimum 1.0m below GL'
          },
          
          raft: {
            description: 'Single slab under entire building',
            suitable: 'Poor soil, high water table',
            thickness: 'Minimum 300mm'
          },
          
          pile: {
            description: 'Deep foundations',
            suitable: 'Very poor soil, high loads',
            depth: 'As per soil investigation'
          }
        }
      },
      
      // Excavation calculations
      excavation: {
        volume: (length: number, width: number, depth: number, workingSpace: number = 0.6) => {
          // Add working space on all sides
          const excLength = length + 2 * workingSpace;
          const excWidth = width + 2 * workingSpace;
          
          // Volume with slope (1:5 for safety)
          const topLength = excLength + 2 * (depth / 5);
          const topWidth = excWidth + 2 * (depth / 5);
          
          // Trapezoidal volume
          return (depth / 3) * (
            (excLength * excWidth) + 
            (topLength * topWidth) + 
            Math.sqrt((excLength * excWidth) * (topLength * topWidth))
          );
        },
        
        classification: {
          ordinary: { rate: 200, description: 'All types except rock' },
          hard: { rate: 400, description: 'Hard moorum, kankar' },
          rock: { rate: 800, description: 'Requiring blasting' }
        },
        
        disposal: {
          useful: { percentage: 30, usage: 'Backfilling' },
          surplus: { percentage: 70, cartAway: '1 km distance' }
        }
      },
      
      // Concrete calculations for foundation
      foundationConcrete: {
        // PCC (Plain Cement Concrete)
        pcc: {
          grade: 'M10 (1:3:6)',
          thickness: 0.10, // meters
          
          quantity: (foundationArea: number) => {
            return foundationArea * 0.10; // cum
          },
          
          materials: {
            cement: 210, // kg/cum
            sand: 0.42, // cum/cum
            aggregate: 0.84 // cum/cum
          }
        },
        
        // RCC footings
        footings: {
          isolated: (columnLoad: number) => {
            const sbc = 200; // kN/sqm assumed
            const footingArea = (columnLoad * 1.1) / sbc;
            const side = Math.sqrt(footingArea);
            const depth = side / 6; // Typical proportion
            
            return {
              size: `${side}m x ${side}m`,
              depth: depth,
              concrete: side * side * depth,
              steel: side * side * depth * 80 // kg/cum
            };
          },
          
          strip: (wallLoad: number, length: number) => {
            const sbc = 200; // kN/sqm
            const width = (wallLoad * 1.1) / (sbc * length);
            const depth = width / 2;
            
            return {
              width: width,
              depth: depth,
              concrete: length * width * depth,
              steel: length * width * depth * 75
            };
          }
        }
      },
      
      // Plinth beam calculations
      plinthBeam: {
        size: {
          standard: { width: 0.23, depth: 0.30 }, // meters
          heavy: { width: 0.30, depth: 0.45 }
        },
        
        quantity: (perimeter: number, beamSize: BeamSize) => {
          const volume = perimeter * beamSize.width * beamSize.depth;
          
          return {
            concrete: volume,
            steel: {
              main: perimeter * 4 * 2.47, // 4-16mm dia
              stirrups: perimeter * 7 * 0.395 // 8mm @ 150mm c/c
            },
            shuttering: perimeter * (beamSize.depth * 2 + beamSize.width)
          };
        }
      }
    };

#### 6.2 Superstructure Calculations

typescript

    // Superstructure quantity calculations
    const superstructureCalculations = {
      // Column calculations
      columns: {
        // Size determination
        size: (floors: number, columnType: 'corner' | 'edge' | 'central') => {
          const sizes = {
            corner: {
              'G': { width: 230, depth: 230 },
              'G+1': { width: 230, depth: 300 },
              'G+2': { width: 300, depth: 300 },
              'G+3': { width: 300, depth: 380 }
            },
            edge: {
              'G': { width: 230, depth: 300 },
              'G+1': { width: 300, depth: 300 },
              'G+2': { width: 300, depth: 380 },
              'G+3': { width: 380, depth: 380 }
            },
            central: {
              'G': { width: 300, depth: 300 },
              'G+1': { width: 300, depth: 380 },
              'G+2': { width: 380, depth: 380 },
              'G+3': { width: 380, depth: 450 }
            }
          };
          
          const floorKey = floors <= 1 ? 'G' : 
                          floors === 2 ? 'G+1' : 
                          floors === 3 ? 'G+2' : 'G+3';
          
          return sizes[columnType][floorKey];
        },
        
        // Concrete quantity
        concrete: (columnSize: Size, height: number, count: number) => {
          const volume = (columnSize.width / 1000) * 
                        (columnSize.depth / 1000) * 
                        height * count;
          return volume;
        },
        
        // Steel quantity
        steel: (columnSize: Size, height: number, count: number) => {
          // Main reinforcement
          const mainBars = {
            count: columnSize.width <= 230 ? 4 : 
                   columnSize.width <= 300 ? 6 : 8,
            dia: columnSize.width <= 300 ? 16 : 20
          };
          
          const mainSteel = mainBars.count * height * 
                           getSteelWeight(mainBars.dia) * count;
          
          // Stirrups
          const stirrupSpacing = Math.min(
            columnSize.width,
            columnSize.depth,
            12 * mainBars.dia
          );
          
          const stirrupCount = (height * 1000) / stirrupSpacing;
          const stirrupLength = 2 * ((columnSize.width - 50) + 
                                    (columnSize.depth - 50)) / 1000;
          const stirrupSteel = stirrupCount * stirrupLength * 
                              getSteelWeight(8) * count;
          
          return {
            main: mainSteel,
            stirrups: stirrupSteel,
            total: mainSteel + stirrupSteel
          };
        }
      },
      
      // Beam calculations
      beams: {
        // Size based on span
        size: (span: number, beamType: 'primary' | 'secondary') => {
          const depth = beamType === 'primary' ? span / 12 : span / 15;
          const width = Math.max(230, depth / 2);
          
          return {
            width: Math.ceil(width / 10) * 10,
            depth: Math.ceil(depth / 10) * 10
          };
        },
        
        // Quantity calculations
        quantity: (beams: BeamData[]) => {
          let totalConcrete = 0;
          let totalSteel = { main: 0, stirrups: 0 };
          let totalShuttering = 0;
          
          for (const beam of beams) {
            // Concrete
            const volume = (beam.width / 1000) * 
                          (beam.depth / 1000) * 
                          beam.length;
            totalConcrete += volume;
            
            // Steel - main bars
            const mainBars = beam.width <= 230 ? 4 : 6;
            const mainDia = beam.depth <= 450 ? 16 : 20;
            const mainSteel = mainBars * beam.length * 
                             getSteelWeight(mainDia);
            
            // Steel - stirrups
            const stirrupSpacing = 0.15; // 150mm
            const stirrupCount = beam.length / stirrupSpacing;
            const stirrupLength = 2 * ((beam.width - 50) + 
                                      (beam.depth - 50)) / 1000;
            const stirrupSteel = stirrupCount * stirrupLength * 
                                getSteelWeight(8);
            
            totalSteel.main += mainSteel;
            totalSteel.stirrups += stirrupSteel;
            
            // Shuttering
            const shutteringArea = beam.length * 
                                  ((beam.depth / 1000) * 2 + 
                                   (beam.width / 1000));
            totalShuttering += shutteringArea;
          }
          
          return {
            concrete: totalConcrete,
            steel: totalSteel,
            shuttering: totalShuttering
          };
        }
      },
      
      // Slab calculations
      slabs: {
        // Thickness based on span
        thickness: (span: number, slabType: 'oneway' | 'twoway') => {
          if (slabType === 'oneway') {
            return Math.max(110, span / 30); // mm
          } else {
            return Math.max(125, span / 35);
          }
        },
        
        // Concrete quantity
        concrete: (area: number, thickness: number) => {
          return area * (thickness / 1000); // cum
        },
        
        // Steel calculation
        steel: (area: number, thickness: number) => {
          // Main steel
          const mainSpacing = 150; // mm
          const mainDia = thickness <= 125 ? 8 : 10;
          const mainBars = (Math.sqrt(area) * 1000) / mainSpacing;
          const mainLength = Math.sqrt(area) * mainBars * 2; // Both directions
          const mainSteel = mainLength * getSteelWeight(mainDia);
          
          // Distribution steel
          const distSpacing = 200;
          const distDia = 8;
          const distSteel = mainSteel * 0.3; // 30% of main
          
          // Extra top steel at supports (cantilevers, continuous slabs)
          const extraSteel = mainSteel * 0.25;
          
          return {
            main: mainSteel,
            distribution: distSteel,
            extra: extraSteel,
            total: mainSteel + distSteel + extraSteel
          };
        },
        
        // Shuttering
        shuttering: (area: number, height: number) => {
          // Slab bottom
          const bottomShuttering = area;
          
          // Beam sides (approximate)
          const beamSides = Math.sqrt(area) * 4 * 0.3; // Assuming beams
          
          return bottomShuttering + beamSides;
        }
      }
    };

#### 6.3 Masonry Calculations

typescript

    // Brick and block work calculations
    const masonryCalculations = {
      // Wall quantity calculations
      wallQuantity: {
        // Area calculation
        area: (length: number, height: number, openings: Opening[]) => {
          const grossArea = length * height;
          
          // Deduct openings
          const openingArea = openings.reduce((sum, opening) => 
            sum + (opening.width * opening.height), 0
          );
          
          return grossArea - openingArea;
        },
        
        // Volume calculation for different wall thicknesses
        volume: (area: number, thickness: number) => {
          return area * (thickness / 1000); // Convert mm to m
        }
      },
      
      // Brick calculations
      brickWork: {
        // Standard brick size in India: 190 x 90 x 90 mm
        // With mortar: 200 x 100 x 100 mm
        
        bricksPerCum: {
          '230mm': 460, // 9" wall
          '115mm': 460, // 4.5" wall
          '100mm': 500  // 4" wall
        },
        
        quantity: (volume: number, wallThickness: string) => {
          const bricksPerCum = brickWork.bricksPerCum[wallThickness] || 500;
          const bricks = volume * bricksPerCum;
          
          // Add wastage
          const wastage = 0.05; // 5%
          
          return {
            bricks: Math.ceil(bricks * (1 + wastage)),
            mortar: volume * 0.25 // 25% of volume
          };
        },
        
        // Mortar mix proportions
        mortarMix: {
          'CM 1:6': {
            cement: 250, // kg/cum of mortar
            sand: 1.0 // cum/cum of mortar
          },
          'CM 1:4': {
            cement: 375,
            sand: 1.0
          }
        }
      },
      
      // AAC block calculations
      aacBlocks: {
        // Standard size: 600 x 200 x 100/150/200 mm
        
        blocksPerCum: {
          '100mm': 83.33,
          '150mm': 55.55,
          '200mm': 41.67
        },
        
        quantity: (volume: number, blockThickness: string) => {
          const blocksPerCum = aacBlocks.blocksPerCum[blockThickness];
          const blocks = volume * blocksPerCum;
          
          // Adhesive calculation
          const adhesivePerCum = 40; // kg/cum
          
          return {
            blocks: Math.ceil(blocks * 1.03), // 3% wastage
            adhesive: volume * adhesivePerCum
          };
        },
        
        advantages: [
          'Light weight - reduces structural load',
          'Better thermal insulation',
          'Faster construction',
          'Less mortar required'
        ]
      },
      
      // Plaster calculations
      plaster: {
        // Thickness standards
        thickness: {
          internal: 12, // mm
          external: 18, // mm
          ceiling: 10 // mm
        },
        
        // Area calculation with additions
        area: (wallArea: number, surface: 'internal' | 'external' | 'ceiling') => {
          // Add for undulations and repairs
          const additions = {
            internal: 0.20, // 20% extra
            external: 0.25, // 25% extra
            ceiling: 0.15 // 15% extra
          };
          
          return wallArea * (1 + additions[surface]);
        },
        
        // Material calculation
        materials: (area: number, thickness: number) => {
          const volume = area * (thickness / 1000);
          
          // Mortar proportions for CM 1:4
          const cement = volume * 375; // kg
          const sand = volume * 1.0; // cum
          
          // Add wastage
          return {
            cement: cement * 1.05,
            sand: sand * 1.10,
            volume: volume
          };
        }
      }
    };

### 7\. Finishing Work Calculations

#### 7.1 Flooring Calculations

typescript

    // Flooring quantity and cost calculations
    const flooringCalculations = {
      // Base preparation
      basePreperation: {
        // PCC base for ground floor
        pccBase: {
          thickness: 100, // mm
          grade: 'M10',
          
          quantity: (area: number) => {
            const volume = area * 0.1; // cum
            
            return {
              concrete: volume,
              materials: {
                cement: volume * 210, // kg
                sand: volume * 0.42, // cum
                aggregate: volume * 0.84 // cum
              }
            };
          }
        },
        
        // Soling (brick/stone)
        soling: {
          thickness: 150, // mm
          
          quantity: (area: number) => {
            return {
              brickbats: area * 0.15 * 1.25, // cum with voids
              sand: area * 0.03 // cum for filling voids
            };
          }
        }
      },
      
      // Tile flooring
      tileFlooring: {
        // Tile calculation with wastage
        tileQuantity: (area: number, tileSize: TileSize, pattern: string = 'straight') => {
          const tileArea = (tileSize.length / 1000) * (tileSize.width / 1000);
          const tilesRequired = area / tileArea;
          
          // Wastage factors
          const wastageFactors = {
            straight: 0.07, // 7%
            diagonal: 0.15, // 15%
            pattern: 0.20 // 20%
          };
          
          const wastage = wastageFactors[pattern] || 0.10;
          const totalTiles = Math.ceil(tilesRequired * (1 + wastage));
          
          // Box calculation (tiles per box varies by size)
          const tilesPerBox = getTilesPerBox(tileSize);
          const boxes = Math.ceil(totalTiles / tilesPerBox);
          
          return {
            tiles: totalTiles,
            boxes: boxes,
            actualTiles: boxes * tilesPerBox,
            coverage: boxes * tilesPerBox * tileArea
          };
        },
        
        // Bedding mortar
        beddingMortar: {
          thickness: 25, // mm average
          
          quantity: (area: number) => {
            const volume = area * 0.025;
            
            return {
              cement: volume * 300, // kg for 1:4
              sand: volume * 1.0 // cum
            };
          }
        },
        
        // Skirting calculation
        skirting: {
          height: 100, // mm standard
          
          quantity: (perimeter: number, doorWidths: number[]) => {
            // Deduct door openings
            const doorDeduction = doorWidths.reduce((sum, width) => sum + width, 0);
            const netPerimeter = perimeter - doorDeduction;
            
            const area = netPerimeter * 0.1; // sqm
            
            // Add 10% for wastage and cuts
            return area * 1.10;
          }
        }
      },
      
      // Natural stone flooring
      stoneFlooring: {
        // Marble/Granite flooring
        marble: {
          thickness: 18, // mm standard
          
          quantity: (area: number, stoneType: string) => {
            // Add for wastage based on stone type
            const wastageFactors = {
              marble: 0.30, // 30% - high due to breakage
              granite: 0.20, // 20%
              kota: 0.15 // 15%
            };
            
            const wastage = wastageFactors[stoneType] || 0.25;
            const stoneRequired = area * (1 + wastage);
            
            // Bedding mortar
            const mortarVolume = area * 0.025;
            
            return {
              stone: stoneRequired,
              cement: mortarVolume * 300,
              sand: mortarVolume * 1.0,
              polishing: area // sqm for polishing
            };
          }
        },
        
        // Laying patterns extra
        patternCharges: {
          straight: 0,
          diagonal: 50, // ₹/sqm extra
          composite: 100,
          inlay: 200
        }
      },
      
      // Wooden flooring
      woodenFlooring: {
        types: {
          laminate: {
            thickness: 8, // mm
            coverage: 2.4, // sqm per box typical
            underlay: true,
            
            quantity: (area: number) => {
              const boxes = Math.ceil(area / 2.4);
              const underlay = area * 1.05; // 5% extra
              
              return {
                flooringBoxes: boxes,
                coverage: boxes * 2.4,
                underlay: underlay,
                skirting: 'Matching MDF skirting'
              };
            }
          },
          
          engineered: {
            thickness: 14,
            coverage: 'By sqm',
            
            quantity: (area: number) => {
              return {
                flooring: area * 1.05, // 5% wastage
                adhesive: area * 0.8, // kg
                primer: area * 0.1, // ltr
                polish: area // sqm
              };
            }
          },
          
          solid: {
            thickness: 18,
            coverage: 'By sqm',
            
            quantity: (area: number) => {
              return {
                flooring: area * 1.08, // 8% wastage
                framework: area * 0.05, // cum for battens
                nails: area * 0.1, // kg
                polish: area * 2 // Two coats
              };
            }
          }
        }
      }
    };

#### 7.2 Wall Finishes Calculations

typescript

    // Wall finishing calculations
    const wallFinishesCalculations = {
      // Painting calculations
      painting: {
        // Surface preparation
        preparation: {
          putty: {
            coverage: 12, // sqm/kg for one coat
            coats: { internal: 2, external: 1 },
            
            quantity: (area: number, surface: 'internal' | 'external') => {
              const coats = this.coats[surface];
              return (area * coats) / this.coverage;
            }
          },
          
          primer: {
            coverage: 100, // sqm/ltr
            coats: 1,
            
            quantity: (area: number) => area / this.coverage
          }
        },
        
        // Paint calculation
        paintQuantity: {
          emulsion: {
            coverage: { first: 80, subsequent: 120 }, // sqm/ltr
            coats: 2,
            
            quantity: (area: number) => {
              const firstCoat = area / 80;
              const secondCoat = area / 120;
              return firstCoat + secondCoat;
            }
          },
          
          enamel: {
            coverage: 120, // sqm/ltr
            coats: 2,
            
            quantity: (area: number, coats: number = 2) => {
              return (area * coats) / 120;
            }
          },
          
          exterior: {
            coverage: 40, // sqm/ltr for textured
            coats: 1,
            
            quantity: (area: number, type: 'smooth' | 'textured') => {
              const coverage = type === 'smooth' ? 80 : 40;
              return area / coverage;
            }
          }
        },
        
        // Area calculations
        paintableArea: {
          walls: (grossArea: number, openings: number) => {
            // Don't deduct openings < 0.5 sqm
            return grossArea - openings;
          },
          
          ceiling: (floorArea: number, beamDrops: number = 0) => {
            // Add for beam sides
            return floorArea + beamDrops;
          },
          
          woodwork: (items: WoodworkItem[]) => {
            return items.reduce((sum, item) => {
              const area = item.length * item.height * item.sides;
              return sum + area;
            }, 0);
          }
        }
      },
      
      // Tile work on walls
      wallTiles: {
        // Bathroom tiles
        bathroom: {
          height: { economy: 2.1, standard: 2.4, luxury: 3.0 }, // meters
          
          area: (perimeter: number, height: number, doorWidth: number) => {
            const wallArea = (perimeter - doorWidth) * height;
            
            // Add 10% for cuts and wastage
            return wallArea * 1.10;
          },
          
          materials: (area: number) => {
            return {
              tiles: tileFlooring.tileQuantity(area, { length: 300, width: 600 }),
              adhesive: area * 3, // kg/sqm
              grout: area * 0.3 // kg/sqm
            };
          }
        },
        
        // Kitchen tiles
        kitchen: {
          height: 0.6, // meters (2 feet) above platform
          
          area: (length: number) => {
            return length * 0.6 * 1.10; // 10% wastage
          }
        },
        
        // Dado tiles
        dado: {
          height: 1.2, // meters standard
          locations: ['Utility', 'Wash area', 'Balcony'],
          
          area: (perimeter: number) => perimeter * 1.2 * 1.10
        }
      },
      
      // Special finishes
      specialFinishes: {
        // Texture paint
        texture: {
          coverage: 35, // sqm/ltr
          thickness: '1-2mm',
          
          quantity: (area: number) => ({
            texturePaint: area / 35,
            primer: area / 100,
            laborIntensive: true
          })
        },
        
        // Wallpaper
        wallpaper: {
          rollSize: { width: 0.53, length: 10 }, // meters
          coverage: 5.3, // sqm per roll
          
          quantity: (area: number) => {
            const rolls = Math.ceil(area / 5.3);
            
            // Add 15% for pattern matching
            return {
              rolls: Math.ceil(rolls * 1.15),
              adhesive: area * 0.1 // kg
            };
          }
        },
        
        // Stone cladding
        stoneCladding: {
          types: ['Natural stone', 'Artificial stone', 'Brick tiles'],
          
          quantity: (area: number, type: string) => {
            const wastage = type === 'Natural stone' ? 0.25 : 0.15;
            
            return {
             stone: area * (1 + wastage),
             adhesive: area * 5, // kg/sqm for stone
             grouting: area * 0.5, // kg/sqm
             sealant: area * 0.05 // ltr/sqm
           };
         },
         
         installation: {
           framework: 'MS angle frame for heavy stones',
           anchors: '4-6 per sqm',
           laborIntensive: 'Skilled mason required'
         }
       }
     }
    };

#### 7.3 Doors and Windows Calculations

typescript

    // Doors and windows quantity calculations
    const doorsWindowsCalculations = {
      // Door calculations
      doors: {
        // Standard sizes
        standardSizes: {
          main: { width: 1000, height: 2100 }, // mm
          bedroom: { width: 900, height: 2100 },
          bathroom: { width: 750, height: 2100 },
          french: { width: 1500, height: 2100 }
        },
        
        // Frame calculations
        frames: {
          wood: {
            sections: {
              main: '125x75mm',
              internal: '100x75mm',
              bathroom: '100x65mm'
            },
            
            quantity: (doorSize: DoorSize, count: number) => {
              const perimeter = 2 * (doorSize.width + doorSize.height) / 1000;
              const volume = perimeter * 0.125 * 0.075 * count; // cum
              
              // Add 10% for joints and wastage
              return volume * 1.10;
            }
          },
          
          steel: {
            weight: (doorSize: DoorSize, count: number) => {
              const perimeter = 2 * (doorSize.width + doorSize.height) / 1000;
              const weightPerMeter = 4.5; // kg/m for 16 gauge
              
              return perimeter * weightPerMeter * count;
            }
          }
        },
        
        // Shutter calculations
        shutters: {
          flush: {
            thickness: { commercial: 30, standard: 35, heavy: 40 },
            
            area: (doorSize: DoorSize, count: number) => {
              return (doorSize.width * doorSize.height / 1000000) * count;
            }
          },
          
          panel: {
            styles: ['2 panel', '4 panel', '6 panel'],
            extraCost: '20-40% over flush door'
          },
          
          decorative: {
            types: ['Membrane', 'Veneer', 'Laminate'],
            coverage: 'Both sides for main doors, one side for internal'
          }
        },
        
        // Hardware requirements
        hardware: {
          hinges: {
            count: (height: number) => height > 2100 ? 4 : 3,
            type: { brass: 'Standard', ss: 'Premium', hydraulic: 'Luxury' }
          },
          
          locks: {
            main: 'Mortise lock with handles',
            bedroom: 'Mortise lock or cylindrical',
            bathroom: 'Indicator bolt'
          },
          
          accessories: ['Tower bolt', 'Door stopper', 'Magic eye', 'Safety chain']
        }
      },
      
      // Window calculations
      windows: {
        // Standard sizes
        standardSizes: {
          small: { width: 600, height: 600 },
          medium: { width: 1200, height: 1200 },
          large: { width: 1800, height: 1200 },
          french: { width: 1200, height: 2100 }
        },
        
        // Frame types
        frames: {
          aluminum: {
            sections: {
              sliding: '1.2mm thickness powder coated',
              casement: '1.5mm thickness anodized',
              fixed: '1.0mm thickness'
            },
            
            weight: (windowArea: number, type: string) => {
              const weightPerSqm = {
                sliding: 8.5,
                casement: 10.2,
                fixed: 6.8
              };
              
              return windowArea * weightPerSqm[type];
            }
          },
          
          upvc: {
            profiles: {
              economy: '2 chamber',
              standard: '3 chamber',
              premium: '5 chamber'
            },
            
            area: (windowSize: WindowSize, count: number) => {
              return (windowSize.width * windowSize.height / 1000000) * count;
            }
          }
        },
        
        // Glass calculations
        glass: {
          types: {
            plain: { thickness: 5, weight: 12.5 }, // kg/sqm
            toughened: { thickness: 6, weight: 15 },
            double: { thickness: '5+12+5', weight: 25 },
            laminated: { thickness: '6.38', weight: 16 }
          },
          
          area: (windowArea: number, glazingType: 'single' | 'double') => {
            // Add 5% for cutting wastage
            const glassArea = windowArea * 1.05;
            
            return glazingType === 'double' ? glassArea * 2 : glassArea;
          }
        },
        
        // Grills and mosquito mesh
        accessories: {
          grills: {
            types: ['MS flat', 'MS square bar', 'SS', 'Invisible'],
            
            weight: (windowArea: number, type: string) => {
              const weightPerSqm = {
                'MS flat': 25,
                'MS square bar': 30,
                'SS': 20,
                'Invisible': 5
              };
              
              return windowArea * weightPerSqm[type];
            }
          },
          
          mosquitoMesh: {
            types: ['Aluminum', 'Fiber', 'SS'],
            coverage: 'Full window area + 10% overlap'
          }
        }
      },
      
      // Installation calculations
      installation: {
        // Hold fasts for frames
        holdFasts: {
          count: (framePerimeter: number) => {
            // 3 per side for doors, 2 per side for windows
            return Math.ceil(framePerimeter / 0.5); // Every 500mm
          },
          
          size: '150x50x6mm MS flat'
        },
        
        // Gaps and sealants
        sealants: {
          gap: 12, // mm around frame
          
          quantity: (totalPerimeter: number) => {
            const sealantPerMeter = 0.05; // kg/m
            return totalPerimeter * sealantPerMeter;
          }
        }
      }
    };

### 8\. MEP (Mechanical, Electrical, Plumbing) Calculations

#### 8.1 Electrical Calculations

typescript

    // Electrical work calculations
    const electricalCalculations = {
      // Load calculations
      loadCalculation: {
        // Connected load estimation
        residentialLoad: (area: number, type: 'economy' | 'standard' | 'luxury') => {
          const wattsPerSqft = {
            economy: 10,
            standard: 15,
            luxury: 20
          };
          
          const baseLoad = area * wattsPerSqft[type];
          
          // Add specific loads
          const additionalLoads = {
            ac: {
              count: Math.ceil(area / 400), // 1 per 400 sqft
              load: 1500 // watts per AC
            },
            geyser: {
              count: Math.ceil(area / 600), // Based on bathrooms
              load: 2000
            },
            kitchen: 3000, // Microwave, chimney, etc.
            pumps: 1000
          };
          
          const totalAdditional = 
            additionalLoads.ac.count * additionalLoads.ac.load +
            additionalLoads.geyser.count * additionalLoads.geyser.load +
            additionalLoads.kitchen + additionalLoads.pumps;
          
          return {
            connectedLoad: baseLoad + totalAdditional,
            sanctionedLoad: (baseLoad + totalAdditional) * 0.6, // 60% diversity
            recommendedMCB: Math.ceil(((baseLoad + totalAdditional) * 0.6) / (230 * 0.8))
          };
        }
      },
      
      // Wiring calculations
      wiring: {
        // Points calculation
        points: {
          residential: (rooms: RoomConfig) => {
            const pointsPerRoom = {
              bedroom: {
                lights: 2,
                fans: 1,
                plugs: 4,
                ac: 1,
                tv: 1
              },
              masterBedroom: {
                lights: 3,
                fans: 1,
                plugs: 6,
                ac: 1,
                tv: 1
              },
              living: {
                lights: 4,
                fans: 2,
                plugs: 6,
                ac: 1,
                tv: 1
              },
              kitchen: {
                lights: 2,
                exhaust: 1,
                plugs: 8,
                heavy: 2 // For appliances
              },
              bathroom: {
                lights: 2,
                exhaust: 1,
                geyser: 1,
                plugs: 1
              }
            };
            
            let totalPoints = 0;
            for (const [roomType, count] of Object.entries(rooms)) {
              const points = pointsPerRoom[roomType];
              if (points) {
                totalPoints += count * Object.values(points).reduce((a, b) => a + b, 0);
              }
            }
            
            return totalPoints;
          }
        },
        
        // Wire quantity
        wireQuantity: {
          perPoint: {
            light: { 1.5: 15, 2.5: 0 }, // sq mm: meters
            fan: { 1.5: 20, 2.5: 0 },
            plug: { 1.5: 0, 2.5: 18 },
            ac: { 1.5: 0, 2.5: 0, 4.0: 25 },
            geyser: { 1.5: 0, 2.5: 0, 4.0: 20 }
          },
          
          calculate: (points: PointBreakdown) => {
            const wireTypes = {};
            
            for (const [type, count] of Object.entries(points)) {
              const wirePerPoint = this.perPoint[type];
              if (wirePerPoint) {
                for (const [size, length] of Object.entries(wirePerPoint)) {
                  if (!wireTypes[size]) wireTypes[size] = 0;
                  wireTypes[size] += count * length;
                }
              }
            }
            
            // Add 10% for wastage and joints
            for (const size in wireTypes) {
              wireTypes[size] *= 1.10;
            }
            
            return wireTypes;
          }
        },
        
        // Conduit calculations
        conduits: {
          pvcConduit: {
            sizes: { light: 20, heavy: 25 }, // mm
            
            quantity: (totalWireLength: number) => {
              // Assume 3 wires per conduit average
              const conduitLength = totalWireLength / 3;
              
              // Add for bends and joints
              return conduitLength * 1.15;
            }
          },
          
          accessories: {
            boxes: 'One per 3 points',
            bends: '2 per 3m run',
            couplers: '1 per 3m length'
          }
        }
      },
      
      // Distribution board and protection
      distribution: {
        // MCB requirements
        mcb: {
          lighting: { rating: 6, quantity: 'One per 8 points' },
          power: { rating: 16, quantity: 'One per 6 points' },
          ac: { rating: 20, quantity: 'One per AC' },
          geyser: { rating: 16, quantity: 'One per geyser' },
          main: { rating: 'As per load', type: 'MCCB/RCCB' }
        },
        
        // DB box sizing
        dbBox: (totalMCBs: number) => {
          const ways = Math.ceil(totalMCBs * 1.2); // 20% spare
          
          if (ways <= 6) return '6 way SPN';
          if (ways <= 12) return '12 way SPN';
          if (ways <= 18) return '18 way TPN';
          return 'Custom TPN DB';
        }
      }
    };

#### 8.2 Plumbing Calculations

typescript

    // Plumbing work calculations
    const plumbingCalculations = {
      // Water supply calculations
      waterSupply: {
        // Pipe sizing
        pipeSizing: {
          main: (apartments: number) => {
            if (apartments === 1) return 25; // mm
            if (apartments <= 4) return 32;
            if (apartments <= 8) return 40;
            return 50;
          },
          
          distribution: {
            bathroom: 20,
            kitchen: 20,
            washing: 20,
            outlet: 15
          }
        },
        
        // Pipe quantity
        pipeQuantity: {
          horizontal: (layout: LayoutPlan) => {
            // Main line along walls
            const mainRun = layout.perimeter * 0.5;
            
            // Branches to fixtures
            const branches = layout.wetAreas * 10; // 10m average per wet area
            
            return {
              main: mainRun,
              branches: branches,
              vertical: layout.floors * 3 * layout.wetAreas // 3m per floor per wet area
            };
          },
          
          materials: {
            cpvc: {
              pressure: 'SDR-11 for hot water',
              jointing: 'Solvent cement',
              fittings: '30% of pipe length'
            },
            ppr: {
              pressure: 'PN16 for normal',
              jointing: 'Heat fusion',
              fittings: '25% of pipe length'
            },
            gi: {
              class: 'B class medium',
              jointing: 'Threaded',
              fittings: '35% of pipe length'
            }
          }
        }
      },
      
      // Drainage calculations
      drainage: {
        // Pipe sizing
        pipeSizing: {
          wc: 100, // mm minimum
          bathFloor: 75,
          washbasin: 32,
          kitchen: 50,
          mainStack: 100,
          buildingDrain: (fixtures: number) => {
            const dfu = fixtures * 6; // Average DFU per fixture
            if (dfu <= 20) return 100;
            if (dfu <= 160) return 150;
            return 200;
          }
        },
        
        // Quantity calculations
        quantity: {
          soilPipes: (bathrooms: number, floors: number) => {
            const verticalStack = floors * 3 * bathrooms;
            const horizontal = bathrooms * 5; // 5m average per bathroom
            
            return {
              vertical: verticalStack,
              horizontal: horizontal,
              fittings: (verticalStack + horizontal) * 0.4
            };
          },
          
          wastePipes: (fixtures: WasteFixtures) => {
            const lengths = {
              washbasin: 2,
              kitchen: 5,
              washing: 3,
              floor: 2
            };
            
            let total = 0;
            for (const [type, count] of Object.entries(fixtures)) {
              total += count * lengths[type];
            }
            
            return total * 1.2; // 20% extra for routing
          }
        }
      },
      
      // Fixtures and fittings
      fixtures: {
        // Fixture counts
        count: (layout: LayoutConfig) => {
          return {
            wc: layout.bathrooms,
            washbasin: layout.bathrooms + 1, // Extra in common area
            kitchenSink: layout.kitchen,
            taps: {
              basin: layout.bathrooms + 1,
              kitchen: layout.kitchen,
              washing: 1,
              garden: 2
            },
            accessories: {
              healthFaucet: layout.bathrooms,
              angleValve: (layout.bathrooms + 1) * 2, // 2 per fixture
              shower: layout.bathrooms,
              gratings: layout.bathrooms + layout.kitchen + 2
            }
          };
        },
        
        // Quality grades
        grades: {
          economy: {
            brands: ['Cera', 'Parryware'],
            warranty: '5 years',
            features: 'Basic functionality'
          },
          standard: {
            brands: ['Hindware', 'Jaquar'],
            warranty: '7 years',
            features: 'Water saving, better finish'
          },
          premium: {
            brands: ['Kohler', 'Roca', 'Grohe'],
            warranty: '10 years',
            features: 'Designer range, advanced features'
          }
        }
      },
      
      // Overhead and underground tanks
      waterStorage: {
        // Capacity calculations
        capacity: {
          daily: (persons: number) => persons * 135, // liters per person
          
          overhead: (dailyRequirement: number) => {
            return Math.ceil(dailyRequirement * 1 / 1000) * 1000; // Round to 1000L
          },
          
          underground: (dailyRequirement: number) => {
            return Math.ceil(dailyRequirement * 2 / 1000) * 1000; // 2 days storage
          }
        },
        
        // Tank specifications
        tanks: {
          syntex: {
            sizes: [500, 750, 1000, 1500, 2000], // liters
            type: 'Triple layer',
            warranty: '5 years'
          },
          
          rcc: {
            construction: 'Cast in situ',
            waterproofing: 'Crystalline coating',
            capacity: 'Custom sizes'
          }
        }
      }
    };

#### 8.3 HVAC Calculations

typescript

    // Air conditioning calculations
    const hvacCalculations = {
      // Cooling load calculations
      coolingLoad: {
        // Simplified residential calculation
        residential: (roomArea: number, exposure: string, occupants: number) => {
          // Base BTU per sqft
          const baseBTU = {
            northFacing: 80,
            eastWest: 100,
            south: 90,
            topFloor: 120
          };
          
          let btuRequired = roomArea * baseBTU[exposure];
          
          // Adjustments
          const adjustments = {
            occupants: (occupants - 2) * 600, // Extra per person
            kitchen: roomArea * 40, // If kitchen
            electronics: 400, // TV, computer
            windows: 1000 // Large windows
          };
          
          btuRequired += Object.values(adjustments).reduce((a, b) => a + b, 0);
          
          // Convert to tons
          const tons = btuRequired / 12000;
          
          return {
            btu: btuRequired,
            tons: Math.ceil(tons * 2) / 2, // Round to nearest 0.5
            recommendation: tons <= 1 ? '1 ton' : 
                           tons <= 1.5 ? '1.5 ton' : 
                           tons <= 2 ? '2 ton' : 'Multiple units'
          };
        },
        
        // Whole house calculation
        wholeHouse: (totalArea: number, floors: number) => {
          const btuPerSqft = 60; // Central AC
          const totalBTU = totalArea * btuPerSqft;
          const tons = totalBTU / 12000;
          
          return {
            centralAC: `${Math.ceil(tons)} ton package unit`,
            vrf: `${Math.ceil(tons * 0.8)} ton VRF system`,
            splits: Math.ceil(totalArea / 150) // One 1.5T per 150 sqft
          };
        }
      },
      
      // Installation requirements
      installation: {
        // Copper piping
        piping: {
          perUnit: {
            '1 ton': { liquid: 6, suction: 10 }, // mm dia
            '1.5 ton': { liquid: 6, suction: 12 },
            '2 ton': { liquid: 10, suction: 16 }
          },
          
          length: (indoor: Point, outdoor: Point) => {
            const horizontal = Math.abs(indoor.x - outdoor.x);
            const vertical = Math.abs(indoor.y - outdoor.y);
            
            // Add 20% for bends and routing
            return (horizontal + vertical) * 1.2;
          }
        },
        
        // Electrical requirements
        electrical: {
          cableSize: {
            '1 ton': 2.5, // sq mm
            '1.5 ton': 2.5,
            '2 ton': 4.0,
            '3 ton': 6.0
          },
          
          mcb: {
            '1 ton': 16, // Amps
            '1.5 ton': 20,
            '2 ton': 25,
            '3 ton': 32
          }
        },
        
        // Drain piping
        drainage: {
          pipeSize: 20, // mm for all residential
          slope: '1:100 minimum',
          insulation: 'Required to prevent condensation'
        }
      }
    };

### 9\. External & Site Development Calculations

#### 9.1 Site Development

typescript

    // External works calculations
    const siteDevCalculations = {
      // Boundary wall
      boundaryWall: {
        // Wall specifications
        specifications: {
          economy: {
            height: 1.5, // meters
            thickness: 230, // mm
            type: 'Brick masonry',
            foundation: 'Strip footing'
          },
          standard: {
            height: 1.8,
            thickness: 230,
            type: 'Brick + plaster + paint',
            foundation: 'RCC footing',
            coping: 'Precast RCC'
          },
          premium: {
            height: 2.1,
            thickness: 230,
            type: 'Designer wall with features',
            foundation: 'RCC footing',
            features: ['Coping', 'Lighting', 'Name plate']
          }
        },
        
        // Quantity calculations
        quantity: (perimeter: number, height: number, gateWidth: number = 3) => {
          const wallLength = perimeter - gateWidth;
          const wallArea = wallLength * height;
          
          // Foundation
          const foundationVolume = wallLength * 0.6 * 0.6; // 600x600mm
          
          // Brickwork
          const brickVolume = wallArea * 0.23;
          const bricks = brickVolume * 500; // per cum
          
          // Plaster both sides
          const plasterArea = wallArea * 2;
          
          return {
            excavation: foundationVolume * 1.2,
            concrete: foundationVolume,
            brickwork: brickVolume,
            bricks: Math.ceil(bricks * 1.05),
            plaster: plasterArea,
            painting: plasterArea
          };
        }
      },
      
      // Gate calculations
      gates: {
        types: {
          sliding: {
            frame: 'MS box section 100x50mm',
            infill: 'MS flat 16x6mm @ 100mm c/c',
            automation: 'Optional motor'
          },
          swing: {
            frame: 'MS box section 75x50mm',
            infill: 'MS square bar 16mm @ 100mm c/c',
            hinges: 'Heavy duty ball bearing'
          }
        },
        
        quantity: (width: number, height: number, type: 'sliding' | 'swing') => {
          const area = width * height;
          
          // Frame weight
          const framePerimeter = 2 * (width + height);
          const frameWeight = framePerimeter * 5; // kg/m for box section
          
          // Infill weight
          const infillWeight = area * 25; // kg/sqm
          
          // Hardware
          const hardware = type === 'sliding' ? 
            { wheels: 4, track: width * 1.2 } : 
            { hinges: Math.ceil(height / 0.6) };
          
          return {
            frameWeight,
            infillWeight,
            totalWeight: frameWeight + infillWeight,
            hardware
          };
        }
      },
      
      // Paving and roads
      paving: {
        // Driveway
        driveway: {
          width: { single: 3, double: 5.5 }, // meters
          
          construction: {
            subBase: {
              thickness: 150, // mm
              material: 'Crushed stone',
              compaction: '98% MDD'
            },
            base: {
              thickness: 100,
              material: 'WBM/WMM',
              compaction: '100% MDD'
            },
            surface: {
              concrete: {
                thickness: 150,
                grade: 'M25',
                reinforcement: 'BRC mesh'
              },
              paverBlock: {
                thickness: 60,
                bedding: '50mm sand',
                pattern: 'Herringbone for strength'
              }
            }
          },
          
          quantity: (area: number, type: 'concrete' | 'paver') => {
            const subBase = area * 0.15;
            const base = area * 0.10;
            
            if (type === 'concrete') {
              return {
                excavation: area * 0.25,
                subBase: subBase * 1.3, // Compacted
                base: base * 1.3,
                concrete: area * 0.15,
                steel: area * 5.5 // kg/sqm for mesh
              };
            } else {
              return {
                excavation: area * 0.21,
                subBase: subBase * 1.3,
                sand: area * 0.05,
                pavers: area * 1.05, // 5% wastage
                edging: Math.sqrt(area) * 4
              };
            }
          }
        },
        
        // Walkways
        walkways: {
          width: 1.2, // meters standard
          
          materials: {
            concrete: {
              thickness: 100,
              finish: 'Broom finish for anti-skid'
            },
            stone: {
              thickness: 30,
              bedding: 'Mortar bed',
              joints: 'Grouted'
            },
            gravel: {
              depth: 75,
              edging: 'Required',
              membrane: 'Weed barrier below'
            }
          }
        }
      },
      
      // Landscaping
      landscaping: {
        // Lawn area
        lawn: {
          preparation: {
            topsoil: 150, // mm depth
            manure: 0.05, // cum per sqm
            leveling: 'Manual or machine'
          },
          
          grass: {
            types: {
              korean: { rate: 40, unit: 'sqft/sqm' },
              mexican: { rate: 35, unit: 'sqft/sqm' },
              carpet: { rate: 45, unit: 'sqft/sqm' }
            }
          }
        },
        
        // Planting
        planting: {
          trees: {
            small: { pit: '0.6x0.6x0.6m', soil: 0.2, manure: 0.05 },
            medium: { pit: '0.9x0.9x0.9m', soil: 0.4, manure: 0.1 },
            large: { pit: '1.2x1.2x1.2m', soil: 0.8, manure: 0.2 }
          },
          
          shrubs: {
            spacing: 0.6, // meters
            pit: '0.3x0.3x0.3m',
            quantity: (borderLength: number) => Math.ceil(borderLength / 0.6)
          }
        }
      }
    };

* * *

Part 3: Material Specifications & Consumption
---------------------------------------------

### 10\. Material Categories & Properties

#### 10.1 Structural Materials

typescript

    // Comprehensive material specifications
    const structuralMaterials = {
      // Cement specifications
      cement: {
        types: {
          opc: {
            grades: {
              '33': {
                usage: 'Non-structural works, plastering',
                strength: { '28days': 33, unit: 'N/mm²' },
                initialSetting: '30 min minimum',
                finalSetting: '600 min maximum'
              },
              '43': {
                usage: 'General construction, RCC',
                strength: { '28days': 43, unit: 'N/mm²' },
                popularity: 'Most commonly used'
              },
              '53': {
                usage: 'High strength concrete, precast',
                strength: { '28days': 53, unit: 'N/mm²' },
                caution: 'Higher heat of hydration'
              }
            }
          },
          
          ppc: {
            advantages: [
              'Lower heat of hydration',
              'Better workability',
              'Improved durability',
              'Resistance to chemicals'
            ],
            usage: 'Mass concrete, marine works',
            strength: 'Comparable to OPC 33'
          },
          
          psc: {
            advantages: ['Sulphate resistance', 'Low heat'],
            usage: 'Foundation in aggressive soils'
          }
        },
        
        brands: {
          premium: ['UltraTech', 'ACC', 'Ambuja', 'JK Lakshmi'],
          standard: ['Birla', 'Ramco', 'India Cements'],
          economy: ['Local brands with ISI mark']
        },
        
        storage: {
          requirements: [
            'Raised platform (150mm)',
            'Cover from rain',
            'Stack height < 10 bags',
            'FIFO usage'
          ],
          shelfLife: '3 months from manufacturing'
        }
      },
      
      // Steel specifications
      steel: {
        types: {
          bars: {
            grades: {
              'Fe 415': {
                yieldStrength: 415,
                usage: 'General construction',
                elongation: '14.5% minimum'
              },
              'Fe 500': {
                yieldStrength: 500,
                usage: 'Standard for RCC',
                elongation: '12% minimum'
              },
              'Fe 500D': {
                yieldStrength: 500,
                usage: 'Earthquake zones',
                elongation: '16% minimum',
                ductility: 'Higher'
              },
              'Fe 550': {
                yieldStrength: 550,
                usage: 'Heavy structures',
                elongation: '10% minimum'
              }
            },
            
            sizes: [8, 10, 12, 16, 20, 25, 32], // mm diameter
            
            weight: (dia: number) => {
              // Weight in kg/m = d²/162
              return (dia * dia) / 162;
            }
          },
          
          structural: {
            sections: ['Angles', 'Channels', 'I-beams', 'Square tubes'],
            standards: 'IS 2062',
            grades: ['A', 'B', 'C']
          }
        },
        
        brands: {
          primary: ['SAIL', 'Tata Tiscon', 'JSW Neosteel'],
          secondary: ['Vizag Steel', 'Kamdhenu', 'SRMB']
        },
        
        tests: {
          mandatory: ['Tensile strength', 'Bend test', 'Re-bend test'],
          frequency: 'Every 40 MT or lot'
        }
      },
      
      // Aggregate specifications
      aggregates: {
        fine: {
          types: {
            riverSand: {
              zones: ['Zone I', 'Zone II', 'Zone III', 'Zone IV'],
              preferred: 'Zone II for concrete',
              siltContent: '<3%'
            },
            
            manufacturedSand: {
              advantages: ['Consistent quality', 'No silt', 'Angular particles'],
              gradation: 'Better than river sand',
              usage: 'Increasingly popular'
            }
          },
          
          tests: {
            sieve: 'IS 383 requirements',
            silt: 'Field test - jar method',
            bulking: 'Important for volume batching'
          }
        },
        
        coarse: {
          sizes: {
            '10mm': 'Concrete in restricted areas',
            '20mm': 'Standard for RCC',
            '40mm': 'Mass concrete, PCC'
          },
          
          properties: {
            shape: 'Angular preferred over rounded',
            strength: '>45 N/mm² for M20 concrete',
            waterAbsorption: '<2%',
            losAngeles: '<30% for wearing surfaces'
          },
          
          sources: {
            granite: 'High strength, durable',
            basalt: 'Very high strength',
            limestone: 'Adequate for normal use',
            gravel: 'Rounded, lower strength'
          }
        }
      },
      
      // Bricks and blocks
      masonryUnits: {
        clayBricks: {
          classes: {
            firstClass: {
              strength: '>10 N/mm²',
              waterAbsorption: '<20%',
              tolerance: '±3mm',
              usage: 'Load bearing walls'
            },
            
            secondClass: {
              strength: '>7 N/mm²',
              waterAbsorption: '<22%',
              usage: 'Non-load bearing'
            }
          },
          
          sizes: {
            modular: '190 x 90 x 90 mm',
            nonModular: '230 x 110 x 70 mm'
          },
          
          consumption: {
            '230mm wall': '460 bricks/cum',
            '115mm wall': '460 bricks/cum'
          }
        },
        
        aacBlocks: {
          densities: {
            '400-450': 'Non-load bearing',
            '500-550': 'Load bearing low rise',
            '600-650': 'Load bearing high rise'
          },
          
          sizes: {
            standard: '600 x 200 x 100/150/200 mm',
            jumbo: '600 x 250 x 100/150/200 mm'
          },
          
          advantages: [
            '5 times lighter than clay brick',
            '3 times thermal insulation',
            'Fire resistant (4 hours)',
            'Sound insulation (42 dB)',
            'Faster construction'
          ]
        },
        
        flyAshBricks: {
          composition: {
            flyAsh: '60%',
            sand: '30%',
            cement: '10%'
          },
          
          properties: {
            strength: '>7.5 N/mm²',
            waterAbsorption: '<15%',
            density: 'Lower than clay'
          }
        }
      }
    };

#### 10.2 Finishing Materials

typescript

    // Finishing materials specifications
    const finishingMaterials = {
      // Flooring materials
      flooring: {
        tiles: {
          ceramic: {
            grades: {
              economy: {
                thickness: '7-8mm',
                waterAbsorption: '>10%',
                usage: 'Walls only',
                priceRange: '25-40/sqft'
              },
              standard: {
                thickness: '8-9mm',
                waterAbsorption: '3-10%',
                usage: 'Walls and light traffic',
                priceRange: '40-60/sqft'
              }
            },
            
            sizes: ['300x300', '300x600', '400x400'] // mm
          },
          
          vitrified: {
            types: {
              doubleCharged: {
                process: 'Double layer printing',
                thickness: '9.5-11mm',
                durability: 'High',
                priceRange: '45-80/sqft'
              },
              
              fullBody: {
                process: 'Homogeneous composition',
                thickness: '11-15mm',
                durability: 'Very high',
                priceRange: '60-120/sqft'
              },
              
              glazedVitrified: {
                process: 'Digital printing + glaze',
                variety: 'Unlimited designs',
                maintenance: 'Easy',
                priceRange: '50-150/sqft'
              }
            },
            
            sizes: ['600x600', '800x800', '600x1200', '800x1600'],
            
            finishes: ['Polished', 'Matt', 'Satin', 'Rustic', 'Carving']
          },
          
          natural: {
            marble: {
              types: {
                makrana: {
                  color: 'White',
                  usage: 'Premium projects',
                  priceRange: '150-500/sqft'
                },
                italian: {
                  varieties: ['Carrara', 'Statuario', 'Calacatta'],
                  priceRange: '300-2000/sqft'
                },
                indian: {
                  varieties: ['Green', 'Black', 'Pink', 'Beige'],
                  priceRange: '80-250/sqft'
                }
              },
              
              thickness: '16-18mm standard',
              wastage: '25-30%'
            },
            
            granite: {
              colors: ['Black', 'Red', 'Grey', 'Yellow', 'Green'],
              finishes: ['Polished', 'Flamed', 'Leather', 'Brushed'],
              thickness: '15-18mm',
              priceRange: '70-200/sqft'
            }
          }
        },
        
        wooden: {
          laminate: {
            ac3: {
              usage: 'Residential',
              warranty: '10-15 years',
              thickness: '8mm',
              priceRange: '70-120/sqft'
            },
            ac4: {
              usage: 'Heavy residential',
              warranty: '15-20 years',
              thickness: '10-12mm',
              priceRange: '100-180/sqft'
            }
          },
          
          engineered: {
            topLayer: '3-4mm real wood',
            core: 'HDF/Plywood',
            stability: 'Better than solid',
            priceRange: '150-400/sqft'
          },
          
          solid: {
            species: ['Teak', 'Oak', 'Merbau', 'Bamboo'],
            thickness: '18-20mm',
            priceRange: '300-800/sqft'
          }
        }
      },
      
      // Paint specifications
      paints: {
        categories: {
          primer: {
            types: {
              waterBased: {
                coverage: '10-12 sqm/ltr',
                drying: '30 minutes',
                recoat: '2-4 hours'
              },
              solventBased: {
                coverage: '8-10 sqm/ltr',
                drying: '2-4 hours',
                usage: 'Better adhesion'
              }
            }
          },
          
          emulsion: {
            economy: {
              sheen: 'Matt',
              washability: 'Low',
              coverage: '120-140 sqft/ltr',
              coats: 2,
              brands: ['Tractor', 'Nerolac Beauty']
            },
            
            premium: {
              sheen: 'Silk/Satin',
              washability: 'High',
              coverage: '140-160 sqft/ltr',
              coats: 2,
              features: ['Anti-bacterial', 'Low VOC'],
              brands: ['Asian Royale', 'Dulux Velvet']
            },
            
            luxury: {
              sheen: 'Multiple options',
              washability: 'Very high',
              coverage: '160-180 sqft/ltr',
              features: ['Teflon', 'Anti-viral', 'Kids safe'],
              brands: ['Royale Atmos', 'Dulux Infinite']
            }
          },
          
          enamel: {
            types: {
              synthetic: {
                base: 'Alkyd',
                sheen: 'High gloss',
                durability: '3-4 years'
              },
              pu: {
                base: 'Polyurethane',
                sheen: 'Very high gloss',
                durability: '7-8 years'
              }
            }
          }
        },
        
        speciality: {
          texture: {
            types: ['Roller', 'Spray', 'Trowel'],
            coverage: '30-40 sqft/ltr',
            thickness: '1-3mm'
          },
          
          weatherproof: {
            types: ['Apex', 'Weathershield'],
            warranty: '5-7 years',
            coverage: '50-60 sqft/ltr'
          }
        }
      }
    };

### 11\. Consumption Rates & Formulas

#### 11.1 Concrete Mix Designs

typescript

    // Standard concrete mix calculations
    const concreteMixDesigns = {
      // Nominal mixes
      nominalMixes: {
        M5: {
          ratio: '1:5:10',
          usage: 'Lean concrete, mud mats',
          cement: 110, // kg/cum
          sand: 0.52, // cum
          aggregate: 1.04, // cum
          water: 60 // liters
        },
        
        M10: {
          ratio: '1:3:6',
          usage: 'PCC, non-structural',
          cement: 210,
          sand: 0.42,
          aggregate: 0.84,
          water: 130
        },
        
        M15: {
          ratio: '1:2:4',
          usage: 'PCC in foundations',
          cement: 280,
          sand: 0.42,
          aggregate: 0.84,
          water: 140
        },
        
        M20: {
          ratio: '1:1.5:3',
          usage: 'RCC work standard',
          cement: 320,
          sand: 0.42,
          aggregate: 0.84,
          water: 160
        },
        
        M25: {
          ratio: '1:1:2',
          usage: 'RCC in aggressive conditions',
          cement: 380,
          sand: 0.38,
          aggregate: 0.76,
          water: 170
        }
      },
      
      // Design mixes
      designMixes: {
        M30AndAbove: {
          note: 'Requires mix design as per IS 10262',
          factors: [
            'Specific gravity of materials',
            'Water absorption',
            'Grading of aggregates',
            'Type of cement',
            'Admixtures'
          ]
        }
      },
      
      // Consumption calculation
      calculateMaterials: (volume: number, grade: string) => {
        const mix = concreteMixDesigns.nominalMixes[grade];
        if (!mix) return null;
        
        return {
          cement: {
            bags: Math.ceil((volume * mix.cement) / 50),
            kg: volume * mix.cement
          },
          sand: {
            cum: volume * mix.sand,
            cft: volume * mix.sand * 35.314,
            brass: volume * mix.sand / 2.83
          },
          aggregate: {
            cum: volume * mix.aggregate,
            cft: volume * mix.aggregate * 35.314,
            brass: volume * mix.aggregate / 2.83
          },
          water: {
            liters: volume * mix.water,
            tankers: Math.ceil((volume * mix.water) / 5000)
          }
        };
      },
      
      // Admixtures
      admixtures: {
        plasticizer: {
          dosage: '0.5-1% by weight of cement',
          benefits: ['Improved workability', 'Reduced water content'],
          brands: ['BASF', 'Sika', 'Fosroc']
        },
        
        superplasticizer: {
          dosage: '1-2% by weight of cement',
          benefits: ['High workability', '20-30% water reduction'],
          usage: 'Pumped concrete, high strength'
        },
        
        retarder: {
          dosage: '0.2-0.5% by weight of cement',
          benefits: ['Delayed setting in hot weather'],
          usage: 'Large pours, hot climates'
        }
      }
    };

#### 11.2 Masonry Material Consumption

typescript

    // Masonry work consumption rates
    const masonryConsumption = {
      // Brickwork consumption
      brickwork: {
        // Per cubic meter
        perCum: {
          bricks: {
            firstClass: 460,
            secondClass: 460,
            withWastage: 483 // 5% wastage
          },
          
          mortar: {
            dryVolume: 0.30, // cum
            wetVolume: 0.25, // cum
            
            cementMortar: {
              '1:3': { cement: 520, sand: 0.39 }, // kg, cum
              '1:4': { cement: 400, sand: 0.40 },
              '1:5': { cement: 330, sand: 0.41 },
              '1:6': { cement: 280, sand: 0.42 }
            }
          }
        },
        
        // Per square meter (for different thicknesses)
        perSqm: {
          '115mm': {
            bricks: 53,
            mortar: 0.029,
            cement: 11.6, // kg for 1:4
            sand: 0.012 // cum
          },
          
          '230mm': {
            bricks: 106,
            mortar: 0.058,
            cement: 23.2,
            sand: 0.024
          }
        }
      },
      
      // AAC block consumption
      aacBlocks: {
        perCum: {
          '100mm': 83.33,
          '150mm': 55.56,
          '200mm': 41.67,
          '250mm': 33.33
        },
        
        adhesive: {
          coverage: '40 kg/cum of blockwork',
          thickness: '3-4mm joints',
          waterRatio: '0.3-0.35'
        },
        
        advantages: {
          speed: '5 times faster than bricks',
          mortar: '1/4th of conventional',
          plaster: 'Can be eliminated'
        }
      },
      
      // Plaster consumption
      plaster: {
        // Per square meter
        cementPlaster: {
          '12mm': {
            dryVolume: 0.0156,
            cement: {
              '1:3': 6.24, // kg
              '1:4': 4.68,
              '1:5': 3.74,
              '1:6': 3.12
            },
            sand: 0.013 // cum
          },
          
          '15mm': {
            dryVolume: 0.0195,
            cement: {
              '1:3': 7.80,
              '1:4': 5.85,
              '1:5': 4.68,
              '1:6': 3.90
            },
            sand: 0.016
          },
          
          '20mm': {
            dryVolume: 0.026,
            cement: {
              '1:3': 10.40,
              '1:4': 7.80,
              '1:5': 6.24,
              '1:6': 5.20
            },
            sand: 0.022
          }
        },
        
        gypsum: {
          thickness: '11-13mm',
          coverage: '9-10 sqm/bag (25kg)',
          advantages: ['No curing', 'Smooth finish', 'Quick application']
        }
      }
    };

### 12\. Wastage Factors & Adjustments

#### 12.1 Material Wastage Factors

typescript

    // Standard wastage percentages
    const wastageFactors = {
     // Structural materials
     structural: {
       cement: {
         storage: 2, // % loss in storage
         handling: 1,
         application: 2,
         total: 5
       },
       
       steel: {
         cutting: 3, // % for cutting and bending
         lapping: 2, // Overlap lengths
         handling: 1,
         total: 6
       },
       
       sand: {
         unloading: 5,
         storage: 5,
         usage: 5,
         total: 15
       },
       
       aggregate: {
         unloading: 3,
         storage: 2,
         usage: 3,
         total: 8
       },
       
       bricks: {
         transportation: 2,
         handling: 2,
         cutting: 3,
         defective: 1,
         total: 8
       },
       
       blocks: {
         aac: 3, // Less breakage
         concrete: 5,
         flyAsh: 4
       }
     },
     
     // Finishing materials
     finishing: {
       tiles: {
         ceramic: {
           straight: 7,
           diagonal: 15,
           pattern: 20
         },
         
         vitrified: {
           straight: 5,
           diagonal: 12,
           pattern: 18
         },
         
         marble: {
           standard: 25, // High due to natural variation
           bookMatch: 35,
           inlay: 40
         },
         
         granite: {
           standard: 20,
           pattern: 25
         }
       },
       
       paint: {
         surface: 5, // Surface absorption
         application: 10, // Application loss
         container: 5, // Left in container
         total: 20
       },
       
       wood: {
         solid: 15, // Cutting and sizing
         laminate: 5,
         veneer: 10,
         polish: 15
       },
       
       glass: {
         plain: 5,
         toughened: 3, // Pre-cut to size
         beveled: 8
       }
     },
     
     // Site-specific factors
     siteFactors: {
       congested: {
         description: 'Narrow lanes, limited storage',
         additionalWastage: 5
       },
       
       highRise: {
         description: 'Vertical transportation',
         additionalWastage: 3
       },
       
       remote: {
         description: 'Multiple handling',
         additionalWastage: 7
       },
       
       monsoon: {
         description: 'Weather damage',
         additionalWastage: {
           cement: 5,
           steel: 2,
           sand: 10
         }
       }
     },
     
     // Calculation method
     calculateWithWastage: (quantity: number, material: string, conditions: string[] = []) => {
       // Get base wastage
       let wastagePercent = wastageFactors.structural[material]?.total || 
                           wastageFactors.finishing[material]?.total || 
                           10; // Default
       
       // Add site-specific factors
       for (const condition of conditions) {
         const factor = wastageFactors.siteFactors[condition];
         if (factor) {
           wastagePercent += typeof factor.additionalWastage === 'number' ? 
                            factor.additionalWastage : 
                            factor.additionalWastage[material] || 0;
         }
       }
       
       return quantity * (1 + wastagePercent / 100);
     }
    };

#### 12.2 Quantity Adjustment Factors

typescript

    // Adjustments for accurate quantity estimation
    const quantityAdjustments = {
      // Concrete volume adjustments
      concrete: {
        // Deductions for openings
        openings: {
          rule: 'Deduct if opening > 0.1 sqm',
          
          calculate: (grossVolume: number, openings: Opening[]) => {
            let deduction = 0;
            
            for (const opening of openings) {
              if (opening.area > 0.1) {
                deduction += opening.area * opening.depth;
              }
            }
            
            return grossVolume - deduction;
          }
        },
        
        // Bulking factor
        bulking: {
          wetToCompacted: 0.67, // 1 cum wet = 0.67 cum compacted
          dryToWet: 1.54, // 1 cum dry = 1.54 cum wet
          
          adjustForBulking: (theoreticalVolume: number) => {
            return theoreticalVolume * 1.54 * 0.67;
          }
        }
      },
      
      // Steel adjustments
      steel: {
        // Lap lengths
        lapping: {
          tension: {
            Fe415: 47, // times dia
            Fe500: 57,
            Fe550: 63
          },
          
          compression: {
            Fe415: 38,
            Fe500: 46,
            Fe550: 50
          },
          
          calculate: (length: number, dia: number, grade: string) => {
            const lapFactor = this.tension[grade] || 50;
            const lapLength = (lapFactor * dia) / 1000; // meters
            const lapsPerBar = Math.ceil(length / 12); // 12m standard length
            
            return length + (lapsPerBar * lapLength);
          }
        },
        
        // Development length
        developmentLength: {
          calculate: (dia: number, grade: string) => {
            const factor = grade === 'Fe415' ? 47 : 
                          grade === 'Fe500' ? 57 : 63;
            return (factor * dia) / 1000;
          }
        },
        
        // Chairs and spacers
        accessories: {
          chairs: '1 kg per sqm of slab',
          coverBlocks: '6-8 per sqm'
        }
      },
      
      // Plaster adjustments
      plaster: {
        // Surface additions
        surfaceAdditions: {
          brickwork: 20, // % extra for undulations
          blockwork: 15,
          concrete: 10,
          oldSurface: 30 // For repairs
        },
        
        // Opening deductions
        openingDeductions: {
          rule: 'Deduct one side, add jambs',
          
          calculate: (wallArea: number, openings: Opening[]) => {
            let netArea = wallArea;
            
            for (const opening of openings) {
              // Deduct opening
              netArea -= opening.area;
              
              // Add jambs (3 sides)
              const jambArea = opening.perimeter * 0.15; // 150mm width
              netArea += jambArea;
            }
            
            return netArea;
          }
        }
      },
      
      // Flooring adjustments
      flooring: {
        // Skirting calculation
        skirting: {
          calculate: (roomPerimeter: number, doorWidths: number[]) => {
            const doorDeduction = doorWidths.reduce((sum, w) => sum + w, 0);
            return (roomPerimeter - doorDeduction) * 1.05; // 5% extra
          }
        },
        
        // Pattern wastage
        patternWastage: {
          straight: 7,
          diagonal: 15,
          herringbone: 18,
          basketWeave: 20,
          custom: 25
        }
      }
    };

### 13\. Brand Mapping & Quality Correlation

#### 13.1 Brand Quality Matrix

typescript

    // Brand categorization by quality tiers
    const brandQualityMatrix = {
      // Cement brands
      cement: {
        luxury: {
          brands: ['UltraTech Premium', 'ACC Gold'],
          features: ['Consistent quality', 'Low alkali', 'Special additives'],
          premium: '5-10% over standard'
        },
        
        premium: {
          brands: ['UltraTech', 'ACC', 'Ambuja', 'JK Lakshmi'],
          marketShare: '70%',
          availability: 'Pan India'
        },
        
        smart: {
          brands: ['Birla', 'Ramco', 'India Cements', 'Local ISI'],
          costSaving: '5-8% vs premium',
          suitability: 'Non-critical applications'
        }
      },
      
      // Steel brands
      steel: {
        luxury: {
          brands: ['Tata Tiscon 550SD', 'JSW Neosteel'],
          features: ['Superior ductility', 'Corrosion resistant coating'],
          applications: 'Seismic zones, coastal areas'
        },
        
        premium: {
          brands: ['Tata Tiscon', 'JSW', 'SAIL TMT'],
          features: ['Consistent quality', 'Good elongation'],
          marketLeader: true
        },
        
        smart: {
          brands: ['Kamdhenu', 'Shyam Steel', 'Local ISI brands'],
          testing: 'Mandatory quality testing recommended'
        }
      },
      
      // Tile brands
      tiles: {
        luxury: {
          brands: {
            imported: ['RAK', 'Johnson Marbonite', 'Versace'],
            indian: ['Kajaria Eternity', 'Nitco Marble'],
            features: ['Designer collections', 'Large formats', 'Special finishes']
          }
        },
        
        premium: {
          brands: ['Kajaria', 'Somany', 'Nitco', 'Asian Granito'],
          range: 'Wide variety in designs',
          innovation: 'Latest technology'
        },
        
        smart: {
          brands: ['Cera', 'Milano', 'Local brands'],
          value: 'Good quality at lower price',
          availability: 'May have limited designs'
        }
      },
      
      // Sanitaryware brands
      sanitaryware: {
        luxury: {
          brands: ['Kohler', 'Grohe', 'Villeroy & Boch', 'Duravit'],
          features: ['Designer pieces', 'Advanced features', 'Lifetime warranty'],
          priceMultiple: '3-5x of smart'
        },
        
        premium: {
          brands: ['Jaquar', 'Hindware', 'Roca'],
          features: ['Good aesthetics', 'Water saving', '10 year warranty']
        },
        
        smart: {
          brands: ['Cera', 'Parryware', 'Hindware Smart'],
          features: ['Functional', 'Basic warranty', 'Value for money']
        }
      },
      
      // Paint brands
      paints: {
        luxury: {
          brands: {
            interior: ['Asian Royale Atmos', 'Dulux Velvet Touch Platinum'],
            exterior: ['Dulux Weathershield Max', 'Asian Apex Ultima']
          },
          features: ['Teflon surface', 'Anti-bacterial', 'Low VOC']
        },
        
        premium: {
          brands: {
            interior: ['Asian Royale', 'Berger Silk', 'Nerolac Impressions'],
            exterior: ['Asian Apex', 'Berger Weathercoat', 'Dulux Weathershield']
          }
        },
        
        smart: {
          brands: {
            interior: ['Asian Tractor', 'Berger Easy Clean', 'Nerolac Beauty'],
            exterior: ['Asian Ace', 'Berger Weathercoat Long Life']
          }
        }
      }
    };

#### 13.2 Quality-Price Correlation

typescript

    // Price indices for quality tiers
    const qualityPriceCorrelation = {
      // Material-wise price ratios
      priceRatios: {
        // Base index: Smart = 1.0
        materials: {
          structural: {
            cement: { smart: 1.0, premium: 1.08, luxury: 1.15 },
            steel: { smart: 1.0, premium: 1.06, luxury: 1.12 },
            rmc: { smart: 1.0, premium: 1.10, luxury: 1.20 }
          },
          
          masonry: {
            bricks: { smart: 1.0, premium: 1.20, luxury: 1.40 },
            blocks: { smart: 1.0, premium: 1.15, luxury: 1.25 }
          },
          
          finishing: {
            tiles: { smart: 1.0, premium: 1.80, luxury: 3.50 },
            marble: { smart: 1.0, premium: 2.00, luxury: 5.00 },
            wood: { smart: 1.0, premium: 1.60, luxury: 2.80 },
            paint: { smart: 1.0, premium: 1.50, luxury: 2.20 }
          },
          
          fixtures: {
            sanitaryware: { smart: 1.0, premium: 2.50, luxury: 5.00 },
            cpFittings: { smart: 1.0, premium: 3.00, luxury: 8.00 },
            electrical: { smart: 1.0, premium: 2.00, luxury: 4.00 }
          }
        }
      },
      
      // Overall cost impact
      overallImpact: {
        calculate: (baseCost: number, qualityTier: QualityTier) => {
          const multipliers = {
            smart: 1.0,
            premium: 1.35,
            luxury: 1.85
          };
          
          return baseCost * multipliers[qualityTier];
        }
      },
      
      // Value engineering options
      valueEngineering: {
        recommendations: {
          structural: 'Maintain premium quality for safety',
          wetAreas: 'Invest in good quality waterproofing and fixtures',
          flooring: 'Can mix tiers - premium in living, smart in bedrooms',
          facades: 'Premium quality for durability and aesthetics',
          interiors: 'Flexibility based on budget'
        }
      }
    };

* * *

Part 4: Labor & Professional Services
-------------------------------------

### 14\. Labor Categories & Productivity Rates

#### 14.1 Labor Classification

typescript

    // Comprehensive labor categorization
    const laborCategories = {
      // Skilled labor
      skilled: {
        categories: {
          mason: {
            work: ['Brickwork', 'Plaster', 'Concrete', 'Stone work'],
            dailyOutput: {
              brickwork: '1.5 cum',
              plaster: '100 sqft',
              concrete: 'Supervision only'
            },
            rateRange: {
              metro: '800-1200/day',
              tier2: '600-900/day',
              tier3: '500-700/day'
            }
          },
          
          carpenter: {
            work: ['Shuttering', 'Doors/windows', 'Furniture'],
            dailyOutput: {
              shuttering: '25 sqm',
              doorFrame: '2 nos',
              window: '3 nos'
            },
            rateRange: {
              metro: '900-1300/day',
              tier2: '700-1000/day',
              tier3: '550-800/day'
            }
          },
          
          steelFixer: {
            work: ['Bar bending', 'Tying', 'Fabrication'],
            dailyOutput: {
              cutting: '400 kg',
              bending: '300 kg',
              tying: '200 kg'
            },
            rateRange: {
              metro: '800-1100/day',
              tier2: '650-900/day',
              tier3: '550-750/day'
            }
          },
          
          electrician: {
            work: ['Wiring', 'Fixtures', 'Panel work'],
            dailyOutput: {
              conduiting: '30 points',
              wiring: '20 points',
              fixtures: '15 points'
            },
            rateRange: {
              metro: '900-1200/day',
              tier2: '700-950/day',
              tier3: '600-800/day'
            }
          },
          
          plumber: {
            work: ['Pipe laying', 'Fixture installation', 'Testing'],
            dailyOutput: {
              piping: '25 meters',
              fixtures: '5 nos',
              testing: '1 bathroom'
            },
            rateRange: {
              metro: '850-1100/day',
              tier2: '650-900/day',
              tier3: '550-750/day'
            }
          },
          
          painter: {
            work: ['Surface prep', 'Painting', 'Polish'],
            dailyOutput: {
              putty: '150 sqft',
              primer: '300 sqft',
              paint: '250 sqft (2 coats)'
            },
            rateRange: {
              metro: '700-950/day',
              tier2: '550-750/day',
              tier3: '450-650/day'
            }
          },
          
          tiler: {
            work: ['Floor tiles', 'Wall tiles', 'Marble'],
            dailyOutput: {
              floorTiles: '80 sqft',
              wallTiles: '60 sqft',
              marble: '50 sqft'
            },
            rateRange: {
              metro: '850-1150/day',
              tier2: '700-950/day',
              tier3: '600-800/day'
            }
          }
        }
      },
      
      // Semi-skilled labor
      semiSkilled: {
        categories: {
          helper: {
            ratio: '1:1 with skilled for most trades',
            work: ['Material handling', 'Mixing', 'Assistance'],
            rateRange: {
              metro: '500-650/day',
              tier2: '400-550/day',
              tier3: '350-450/day'
            }
          },
          
          machineOperator: {
            types: ['Mixer', 'Vibrator', 'Cutter', 'Welder'],
            rateRange: {
              metro: '700-900/day',
              tier2: '600-750/day',
              tier3: '500-650/day'
            }
          }
        }
      },
      
      // Unskilled labor
      unskilled: {
        categories: {
          mazdoor: {
            work: ['Loading/unloading', 'Cleaning', 'Earthwork'],
            dailyOutput: {
              earthwork: '1.5 cum excavation',
              loading: '100 bags cement',
              cleaning: '500 sqft'
            },
            rateRange: {
              metro: '450-550/day',
              tier2: '350-450/day',
              tier3: '300-400/day'
            }
          }
        }
      },
      
      // Specialized labor
      specialized: {
        categories: {
          waterproofing: {
            certification: 'Often manufacturer-trained',
            rateType: 'Area based',
            rates: '40-80/sqft including material'
          },
          
          hvac: {
            skills: 'Refrigeration certified',
            rateType: 'Per ton of AC',
            rates: '2000-3500/ton installation'
          },
          
          aluminium: {
            work: ['Windows', 'Partitions', 'Facades'],
            rateType: 'Per sqft',
            rates: '80-150/sqft fabrication + installation'
          },
          
          landscaping: {
            work: ['Lawn', 'Plants', 'Hardscaping'],
            rateType: 'Mixed',
            rates: {
              lawn: '15-25/sqft',
              planting: 'Per plant + labor',
              paving: '40-60/sqft'
            }
          }
        }
      }
    };

#### 14.2 Productivity Factors

typescript

    // Factors affecting labor productivity
    const productivityFactors = {
      // Environmental factors
      environmental: {
        temperature: {
          optimal: '20-30°C',
          impacts: {
            '>35°C': -20, // % productivity loss
            '>40°C': -35,
            '<10°C': -15
          },
          
          mitigation: {
            summer: ['Early morning work', 'Evening shifts', 'Hydration breaks'],
            winter: ['Delayed start', 'Warm clothing provision']
          }
        },
        
        weather: {
          rain: {
            light: -30,
            moderate: -60,
            heavy: -100 // Work stops
          },
          
          wind: {
            '>25kmph': -20, // Height work affected
            '>40kmph': -50
          }
        },
        
        season: {
          monsoon: {
            months: ['Jun', 'Jul', 'Aug', 'Sep'],
            productivity: 0.7, // 30% reduction
            planning: 'Focus on internal works'
          },
          
          summer: {
            months: ['Apr', 'May'],
            productivity: 0.85,
            planning: 'Heavy works in morning'
          }
        }
      },
      
      // Site factors
      siteFactors: {
        congestion: {
          high: -25, // % productivity loss
          medium: -15,
          low: 0
        },
        
        height: {
          '>3floors': -10,
          '>7floors': -20,
          '>15floors': -30
        },
        
        access: {
          poor: -20,
          average: -10,
          good: 0
        },
        
        supervision: {
          good: +15, // % productivity gain
          average: 0,
          poor: -25
        }
      },
      
      // Work factors
      workFactors: {
        complexity: {
          simple: 1.0,
          moderate: 0.85,
          complex: 0.70,
          veryComplex: 0.55
        },
        
        repetition: {
          high: 1.15, // Efficiency gain
          moderate: 1.0,
          low: 0.85
        },
        
        tooling: {
          manual: 0.80,
          semiMechanized: 1.0,
          fullyMechanized: 1.30
        }
      },
      
      // Calculate adjusted productivity
      adjustedProductivity: (
        baseOutput: number,
        conditions: ProductivityConditions
      ) => {
        let factor = 1.0;
        
        // Apply environmental factors
        if (conditions.temperature > 35) {
          factor *= 0.80;
        }
        
        // Apply site factors
        factor *= (1 + (conditions.siteFactor || 0) / 100);
        
        // Apply work complexity
        factor *= conditions.complexity || 1.0;
        
        return baseOutput * factor;
      }
    };

### 15\. Professional Fee Structures

#### 15.1 Design Professional Fees

typescript

    // Professional services fee structures
    const professionalFees = {
      // Architectural services
      architect: {
        feeStructure: {
          percentage: {
            method: 'Percentage of project cost',
            rates: {
              '<50L': 5.0,
              '50L-1Cr': 4.5,
              '1Cr-2Cr': 4.0,
              '2Cr-5Cr': 3.5,
              '>5Cr': 3.0
            }
          },
          
          areaRate: {
            method: 'Per square foot',
            rates: {
              concept: '5-10/sqft',
              working: '15-25/sqft',
              complete: '30-50/sqft'
            }
          },
          
          lumpsum: {
            method: 'Fixed fee',
            basis: 'Scope of work'
          }
        },
        
        scopeOfWork: {
          concept: [
            'Site analysis',
            'Concept design',
            '2-3 options',
            '3D visualization'
          ],
          
          schematic: [
            'Floor plans',
            'Elevations',
            'Sections',
            'Basic 3D'
          ],
          
          working: [
            'Detailed drawings',
            'All architectural details',
            'Door/window schedules',
            'Coordination drawings'
          ],
          
          gfc: [
            'Good for construction drawings',
            'Final specifications',
            'BOQ assistance'
          ]
        },
        
        additionalServices: {
          interiors: '+30-50% of base fee',
          landscape: '+10-15% of base fee',
          pmcCoordination: '+5-10% of base fee'
        }
      },
      
      // Structural consultant
      structural: {
        feeStructure: {
          percentage: {
            residential: '1.0-1.5% of project cost',
            commercial: '1.5-2.0% of project cost',
            complex: '2.0-3.0% of project cost'
          },
          
          areaRate: {
            design: '3-5/sqft',
            designAndSupervision: '5-8/sqft'
          }
        },
        
        scopeOfWork: [
          'Structural system design',
          'Foundation design',
          'Earthquake resistant design',
          'Structural drawings',
          'Bar bending schedules',
          'Site visits (usually 3-5)'
        ],
        
        specialServices: {
          soilInvestigation: 'Additional 50-80k',
          proofChecking: '30% of design fee',
          retrofitting: 'Case by case'
        }
      },
      
      // MEP consultant
      mepConsultant: {
        feeStructure: {
          percentage: {
            electrical: '0.5-0.75% of project cost',
            plumbing: '0.3-0.5% of project cost',
            hvac: '0.5-1.0% of project cost',
            combined: '1.5-2.0% of project cost'
          },
          
          areaRate: {
            residential: '2-4/sqft',
            commercial: '4-8/sqft'
          }
        },
        
        scopeOfWork: {
          electrical: [
            'Load calculations',
            'Single line diagrams',
            'Lighting design',
            'Power distribution',
            'Earthing design'
          ],
          
          plumbing: [
            'Water supply design',
            'Drainage design',
            'Sewage treatment',
            'Rain water harvesting',
            'Fire fighting'
          ],
          
          hvac: [
            'Heat load calculations',
            'Equipment selection',
            'Ducting design',
            'Control systems'
          ]
        }
      },
      
      // Project management
      projectManagement: {
        feeStructure: {
          percentage: {
            construction: '3-5% of project cost',
            turnkey: '7-10% of project cost'
          },
          
          monthly: {
            residential: '50k-1.5L/month',
            commercial: '1L-3L/month'
          }
        },
        
        services: {
          planning: [
            'Project scheduling',
            'Resource planning',
            'Vendor management',
            'Cash flow planning'
          ],
          
          execution: [
            'Quality control',
            'Progress monitoring',
            'Coordination',
            'Problem solving'
          ],
          
          reporting: [
            'Daily progress',
            'Weekly reports',
            'Photo documentation',
            'Cost tracking'
          ]
        }
      }
    };

#### 15.2 Supervision & Quality Control

typescript

    // Site supervision fee structures
    const supervisionFees = {
      // Site engineer/supervisor
      siteSupervision: {
        deployment: {
          fullTime: {
            qualification: 'Diploma/BE Civil',
            experience: '3-5 years',
            cost: {
              metro: '40-60k/month',
              tier2: '30-45k/month',
              tier3: '25-35k/month'
            }
          },
          
          partTime: {
            visits: '3 per week',
            cost: '20-30k/month'
          },
          
          visiting: {
            frequency: 'Critical stages only',
            rate: '3-5k per visit'
          }
        },
        
        responsibilities: [
          'Daily quality checks',
          'Material verification',
          'Labor coordination',
          'Measurement recording',
          'Safety compliance'
        ]
      },
      
      // Third party inspection
      thirdPartyInspection: {
        providers: ['QCBS', 'CIDC', 'Independent consultants'],
        
        stages: {
          foundation: {
            checks: ['Excavation depth', 'Soil bearing', 'Steel placement'],
            cost: '15-20k per visit'
          },
          
          structure: {
            checks: ['Concrete quality', 'Steel spacing', 'Shuttering'],
            frequency: 'Every slab',
            cost: '10-15k per visit'
          },
          
          finishing: {
            checks: ['Plaster thickness', 'Tile level', 'Paint quality'],
            frequency: '3-4 visits',
            cost: '8-12k per visit'
          }
        },
        
        comprehensive: {
          package: '10-12 visits',
          cost: '1.5-2.5L total',
          reports: 'Detailed with photos'
        }
      },
      
      // Quality testing
      qualityTesting: {
        mandatory: {
          soil: {
            test: 'Bearing capacity',
            cost: '15-25k',
            when: 'Before foundation'
          },
          
          concrete: {
            test: 'Cube compressive strength',
            frequency: 'Every 50 cum',
            cost: '500-800 per cube set'
          },
          
          steel: {
            test: 'Tensile, bend, chemical',
            frequency: 'Every 40 MT',
            cost: '3-5k per sample'
          }
        },
        
        optional: {
          water: {
            test: 'pH, chlorides, sulphates',
            cost: '3-5k',
            importance: 'High in problem areas'
          },
          
          bricks: {
            test: 'Compressive strength, absorption',
            cost: '2-3k per sample'
          }
        }
      }
    };

### 16\. Statutory Charges & Compliance Costs

#### 16.1 Government Approvals

typescript

    // Statutory charges structure
    const statutoryCharges = {
      // Plan approval fees
      planApproval: {
        residentialFees: {
          calculation: 'Based on built-up area',
          
          delhi: {
            upto500sqm: 73.50, // per sqm
            '500-2000sqm': 110.25,
            above2000sqm: 147.00,
            
            additionalCharges: {
              scrutinyFee: '10% of plan sanction fee',
              laborCess: '1% of construction cost',
              compoundingCharges: 'If deviations exist'
            }
          },
          
          bangalore: {
            planSanction: '80-150/sqm based on area',
            bettermentCharges: '50% of guidance value',
            akrama_sakrama: 'For regularization'
          },
          
          mumbai: {
            premiumFSI: 'Based on ready reckoner rate',
            developmentCharges: 'Varies by zone',
            scrutinyFee: '5000-20000 based on area'
          }
        },
        
        process: {
          documents: [
            'Land documents',
            'Architectural drawings',
            'Structural drawings',
            'NOCs',
            'Soil report'
          ],
          
          timeline: {
            normal: '30-45 days',
            tatkal: '7-15 days (2x fees)',
            singleWindow: '21 days'
          }
        }
      },
      
      // Utility connections
      utilityConnections: {
        electricity: {
          domesticConnection: {
            upto5kw: '2000-5000',
            '5-10kw': '10000-20000',
            above10kw: '1500-2000 per kW'
          },
          
          securityDeposit: 'Based on load',
          
          meterCharges: {
            single: '1500-2500',
            three: '4000-6000'
          }
        },
        
        water: {
          connection: {
            domestic: '10000-25000',
            boring: '50000-150000'
          },
          
          deposits: '3-6 months average bill',
          
          sewerage: '50% of water connection charges'
        },
        
        gas: {
          pngConnection: {
            registration: '1000',
            securityDeposit: '1500',
            installation: '5000-8000'
          }
        }
      },
      
      // Other statutory charges
      otherCharges: {
        environmentClearance: {
          required: '>20000 sqm built-up',
          cost: '0.1-0.2% of project cost'
        },
        
        fireNOC: {
          required: '>15m height',
          fees: 'Based on area and occupancy',
          typical: '20000-100000'
        },
        
        occupancyCertificate: {
          fees: '10-20% of plan sanction fee',
          process: 'After completion'
        },
        
        propertyTax: {
          calculation: 'Annual value based',
          firstYear: 'Often included in project cost'
        }
      },
      
      // Compliance timeline
      complianceTimeline: {

       preConstruction: [
         { item: 'Land documents verification', days: 7 },
         { item: 'Architectural design', days: 30 },
         { item: 'Structural design', days: 15 },
         { item: 'Plan approval', days: 45 },
         { item: 'Other NOCs', days: 30 }
       ],
       
       duringConstruction: [
         { item: 'Stage inspections', frequency: 'Per floor' },
         { item: 'Labor license', validity: 'Annual' },
         { item: 'Safety compliance', frequency: 'Monthly' }
       ],
       
       postConstruction: [
         { item: 'Completion certificate', days: 30 },
         { item: 'Occupancy certificate', days: 15 },
         { item: 'Utility connections', days: 15 }
       ]
     }
    };

### 17\. Timeline Calculations

#### 17.1 Construction Timeline Estimation

typescript

    // Construction timeline calculation system
    const timelineCalculations = {
      // Activity durations
      activityDurations: {
        // Site preparation
        sitePrep: {
          clearing: (area: number) => Math.ceil(area / 1000), // days
          boundaryWall: (perimeter: number) => Math.ceil(perimeter / 20), // 20m/day
          tempStructures: 7 // days
        },
        
        // Foundation work
        foundation: {
          excavation: (volume: number) => Math.ceil(volume / 50), // 50 cum/day
          pcc: (area: number) => Math.ceil(area / 200), // 200 sqm/day
          footings: {
            isolated: (count: number) => Math.ceil(count / 2), // 2 footings/day
            raft: (area: number) => Math.ceil(area / 100) // 100 sqm/day
          },
          plinthBeam: (length: number) => Math.ceil(length / 30), // 30m/day
          backfilling: (volume: number) => Math.ceil(volume / 40)
        },
        
        // Superstructure
        superstructure: {
          columns: {
            casting: (count: number) => Math.ceil(count / 6), // 6 columns/day
            curing: 7 // minimum days
          },
          
          beams: {
            shuttering: (area: number) => Math.ceil(area / 50),
            concreting: (volume: number) => Math.ceil(volume / 20),
            deshuttering: 14 // days after casting
          },
          
          slab: {
            shuttering: (area: number) => Math.ceil(area / 100),
            steel: (area: number) => Math.ceil(area / 150),
            concreting: (area: number) => Math.ceil(area / 200),
            curing: 7,
            deshuttering: 14
          },
          
          brickwork: {
            external: (area: number) => Math.ceil(area / 40), // 40 sqm/day
            internal: (area: number) => Math.ceil(area / 50)
          }
        },
        
        // Finishing works
        finishing: {
          plastering: {
            internal: (area: number) => Math.ceil(area / 100), // 100 sqm/day
            external: (area: number) => Math.ceil(area / 80),
            ceiling: (area: number) => Math.ceil(area / 60)
          },
          
          flooring: {
            base: (area: number) => Math.ceil(area / 100),
            tiles: (area: number) => Math.ceil(area / 80),
            marble: (area: number) => Math.ceil(area / 50)
          },
          
          painting: {
            preparation: (area: number) => Math.ceil(area / 150),
            primer: (area: number) => Math.ceil(area / 300),
            paint: (area: number) => Math.ceil(area / 250) * 2 // 2 coats
          },
          
          doors: (count: number) => Math.ceil(count / 2), // 2 doors/day
          windows: (count: number) => Math.ceil(count / 3),
          
          electrical: {
            roughing: (points: number) => Math.ceil(points / 30),
            finishing: (points: number) => Math.ceil(points / 20)
          },
          
          plumbing: {
            roughing: (fixtures: number) => Math.ceil(fixtures / 5),
            finishing: (fixtures: number) => Math.ceil(fixtures / 3)
          }
        }
      },
      
      // Timeline calculation with dependencies
      calculateTimeline: (project: ProjectDetails) => {
        const timeline = [];
        let currentDay = 0;
        
        // Foundation phase
        const foundationDays = 
          timelineCalculations.activityDurations.foundation.excavation(project.excavationVolume) +
          timelineCalculations.activityDurations.foundation.pcc(project.pccArea) +
          timelineCalculations.activityDurations.foundation.footings.isolated(project.footingCount) +
          7; // Curing
        
        timeline.push({
          phase: 'Foundation',
          startDay: currentDay,
          duration: foundationDays,
          endDay: currentDay + foundationDays
        });
        
        currentDay += foundationDays;
        
        // Structure phase (per floor)
        for (let floor = 0; floor < project.floors; floor++) {
          const structureDays = 
            7 + // Columns
            10 + // Beams and slab shuttering
            3 + // Concreting
            14; // Curing
          
          timeline.push({
            phase: `Structure - Floor ${floor}`,
            startDay: currentDay,
            duration: structureDays,
            endDay: currentDay + structureDays
          });
          
          currentDay += structureDays;
        }
        
        // Finishing phase
        const finishingDays = Math.max(
          45, // Minimum
          Math.ceil(project.builtUpArea / 100) // Area-based
        );
        
        timeline.push({
          phase: 'Finishing',
          startDay: currentDay,
          duration: finishingDays,
          endDay: currentDay + finishingDays
        });
        
        return {
          timeline,
          totalDays: currentDay + finishingDays,
          totalMonths: Math.ceil((currentDay + finishingDays) / 30)
        };
      },
      
      // Factors affecting timeline
      timelineFactors: {
        seasonal: {
          monsoon: {
            months: ['Jun', 'Jul', 'Aug', 'Sep'],
            impact: 1.3, // 30% increase
            mitigation: 'Plan internal works'
          },
          
          summer: {
            months: ['Apr', 'May'],
            impact: 1.1, // 10% increase
            mitigation: 'Morning/evening work'
          }
        },
        
        complexity: {
          simple: 1.0,
          moderate: 1.15,
          complex: 1.3,
          luxury: 1.5
        },
        
        siteConditions: {
          congested: 1.2,
          normal: 1.0,
          open: 0.9
        },
        
        resourceAvailability: {
          abundant: 0.9,
          normal: 1.0,
          scarce: 1.3
        }
      },
      
      // Fast-track options
      fastTrack: {
        strategies: [
          {
            method: 'Precast elements',
            timeSaving: '20-30%',
            costImpact: '+10-15%'
          },
          {
            method: 'Multiple shifts',
            timeSaving: '15-20%',
            costImpact: '+20-25%'
          },
          {
            method: 'Parallel activities',
            timeSaving: '10-15%',
            costImpact: '+5-10%'
          }
        ]
      }
    };

* * *

Part 5: Advanced Logic & Edge Cases
-----------------------------------

### 18\. Site Condition Adjustments

#### 18.1 Soil & Foundation Adjustments

typescript

    // Site-specific foundation adjustments
    const siteConditionAdjustments = {
      // Soil type impacts
      soilTypes: {
        rock: {
          excavation: {
            costMultiplier: 4.0, // vs normal soil
            timeMultiplier: 3.0,
            method: 'Blasting/Breaking',
            specialEquipment: ['Compressor', 'Jack hammer', 'Blasting materials']
          },
          
          foundation: {
            type: 'Can use isolated footings',
            depth: 'Minimum 0.5m into rock',
            costSaving: -20 // % less foundation cost
          }
        },
        
        blackCotton: {
          characteristics: 'Expansive soil',
          issues: ['Swelling', 'Shrinkage', 'Cracks'],
          
          treatment: {
            removal: {
              depth: '1.5-2.0m',
              replacement: 'Sand/Moorum',
              cost: '+₹200-300/sqft'
            },
            
            underpinning: {
              method: 'Deep foundations',
              types: ['Under-reamed piles', 'Friction piles'],
              cost: '+₹400-600/sqft'
            },
            
            chemical: {
              method: 'Lime/Chemical stabilization',
              cost: '+₹150-200/sqft'
            }
          }
        },
        
        sandy: {
          issues: ['Low bearing', 'Settlement'],
          
          solutions: {
            consolidation: 'Vibro-compaction',
            foundation: 'Raft recommended',
            dewatering: 'May be required',
            cost: '+15-20% on foundation'
          }
        },
        
        clayey: {
          bearing: 'Medium',
          issues: ['Slow drainage', 'Heaving'],
          precautions: ['Proper drainage', 'Moisture barriers']
        },
        
        filled: {
          issues: ['Differential settlement', 'Unknown composition'],
          testing: 'Extensive soil investigation',
          treatment: {
            preloading: 'Time: 3-6 months',
            replacement: 'Remove and replace',
            piling: 'Bypass filled soil'
          },
          cost: '+30-50% on foundation'
        }
      },
      
      // Water table adjustments
      waterTable: {
        high: {
          definition: '<1.5m from ground',
          
          impacts: {
            excavation: {
              dewatering: 'Continuous pumping',
              shoring: 'Sheet piling may be needed',
              cost: '+₹50-100/sqft'
            },
            
            foundation: {
              type: 'Raft foundation mandatory',
              waterproofing: 'Crystalline coating',
              cost: '+₹150-200/sqft'
            },
            
            basement: {
              feasibility: 'Not recommended',
              ifRequired: {
                tanking: 'Box type waterproofing',
                pumping: 'Permanent sump and pump',
                cost: '+₹500-800/sqft'
              }
            }
          }
        },
        
        seasonal: {
          variation: '>3m between seasons',
          precautions: [
            'Design for highest level',
            'Seasonal construction planning'
          ]
        }
      },
      
      // Slope adjustments
      slopeConditions: {
        flat: {
          slope: '<5%',
          impact: 'Minimal',
          drainage: 'Ensure positive drainage'
        },
        
        moderate: {
          slope: '5-15%',
          
          adjustments: {
            cutting: 'Partial cutting required',
            retaining: 'Low retaining walls',
            stepping: 'Stepped foundation',
            cost: '+10-15% on earthwork'
          }
        },
        
        steep: {
          slope: '>15%',
          
          requirements: {
            soilStudy: 'Slope stability analysis',
            retaining: 'Major retaining structures',
            foundation: 'Stepped or pile foundation',
            drainage: 'Comprehensive system',
            access: 'Special arrangements'
          },
          
          cost: '+25-40% overall'
        }
      },
      
      // Calculate total site adjustment
      calculateSiteAdjustment: (conditions: SiteConditions) => {
        let totalAdjustment = 0;
        let adjustmentDetails = [];
        
        // Soil adjustment
        if (conditions.soilType !== 'normal') {
          const soilAdjust = siteConditionAdjustments.soilTypes[conditions.soilType];
          totalAdjustment += soilAdjust.costImpact || 20;
          adjustmentDetails.push({
            factor: 'Soil conditions',
            impact: soilAdjust.costImpact || 20
          });
        }
        
        // Water table adjustment
        if (conditions.waterTableDepth < 1.5) {
          totalAdjustment += 15;
          adjustmentDetails.push({
            factor: 'High water table',
            impact: 15
          });
        }
        
        // Slope adjustment
        if (conditions.slope > 5) {
          const slopeImpact = conditions.slope > 15 ? 30 : 12;
          totalAdjustment += slopeImpact;
          adjustmentDetails.push({
            factor: 'Sloping site',
            impact: slopeImpact
          });
        }
        
        return {
          totalPercentage: totalAdjustment,
          details: adjustmentDetails
        };
      }
    };

#### 18.2 Access & Logistics Adjustments

typescript

    // Site access and logistics impact
    const accessLogisticsAdjustments = {
      // Road access conditions
      roadAccess: {
        excellent: {
          description: 'Wide roads, easy access',
          truckSize: '10-12 ton trucks',
          impact: 0
        },
        
        good: {
          description: '20ft wide roads',
          truckSize: '6-8 ton trucks',
          impact: +3
        },
        
        narrow: {
          description: '12-15ft roads',
          truckSize: 'Small trucks only',
          impacts: {
            transportation: +10,
            handling: +5,
            material: 'Multiple handling'
          },
          totalImpact: +8
        },
        
        veryNarrow: {
          description: '<12ft or congested',
          truckSize: 'Tempo/manual',
          impacts: {
            transportation: +20,
            handling: +10,
            timeDelay: +25,
            breakage: +5
          },
          totalImpact: +15
        }
      },
      
      // Material storage
      storageSpace: {
        ample: {
          description: 'On-site storage available',
          impact: 0
        },
        
        limited: {
          description: 'Partial storage only',
          impacts: {
            justInTime: 'Required',
            multipleDeliveries: +5,
            coordination: 'Critical'
          },
          totalImpact: +5
        },
        
        none: {
          description: 'No on-site storage',
          impacts: {
            dailyDelivery: 'Required',
            nightStorage: 'May need paid parking',
            pilferage: 'Higher risk',
            cost: +10
          }
        }
      },
      
      // Working space
      workingSpace: {
        calculations: {
          minimum: (buildingFootprint: number) => {
            // Need 1.5m around building minimum
            const perimeterSpace = Math.sqrt(buildingFootprint) * 4 * 1.5;
            return buildingFootprint + perimeterSpace;
          },
          
          ideal: (buildingFootprint: number) => {
            // 3m around building + material storage
            const perimeterSpace = Math.sqrt(buildingFootprint) * 4 * 3;
            const storage = buildingFootprint * 0.3;
            return buildingFootprint + perimeterSpace + storage;
          }
        },
        
        impacts: {
          cramped: {
            productivity: -20,
            safety: 'Higher risk',
            cost: +8
          },
          
          adequate: {
            productivity: 0,
            safety: 'Normal',
            cost: 0
          }
        }
      },
      
      // Height and vertical transport
      verticalTransport: {
        lowRise: {
          floors: '<=3',
          method: 'Manual/Crane',
          impact: 0
        },
        
        midRise: {
          floors: '4-7',
          method: 'Crane/Hoist',
          equipment: {
            crane: '₹1.5-2L/month',
            hoist: '₹80k-1L/month'
          },
          impact: +5
        },
        
        highRise: {
          floors: '>7',
          method: 'Tower crane/Passenger hoist',
          equipment: {
            towerCrane: '₹3-5L/month',
            passengerHoist: '₹1.5-2L/month'
          },
          impact: +10
        }
      }
    };

### 19\. Seasonal & Market Variations

#### 19.1 Seasonal Price Variations

typescript

    // Seasonal price fluctuation patterns
    const seasonalVariations = {
      // Material-wise seasonal impact
      materials: {
        cement: {
          pattern: 'Relatively stable',
          variations: {
            monsoon: -2, // % price change
            peakSeason: +3,
            offSeason: 0
          },
          reason: 'Manufacturing continuous'
        },
        
        steel: {
          pattern: 'Quarterly fluctuations',
          variations: {
            q1: 0,
            q2: +2,
            q3: +5, // Pre-festive demand
            q4: -3
          },
          factors: ['Global prices', 'Government policies']
        },
        
        sand: {
          pattern: 'Highly seasonal',
          variations: {
            monsoon: +40, // Mining restrictions
            summer: -10,
            normal: 0
          },
          alternatives: 'M-sand less affected'
        },
        
        bricks: {
          pattern: 'Production based',
          variations: {
            monsoon: +15, // No production
            winter: 0,
            summer: -5
          }
        },
        
        aggregate: {
          pattern: 'Mining dependent',
          variations: {
            monsoon: +25,
            quarryClosures: +30,
            normal: 0
          }
        }
      },
      
      // Labor availability
      laborAvailability: {
        festivals: {
          diwali: {
            duration: '15-20 days',
            availability: -60,
            rateIncrease: +20
          },
          
          holi: {
            duration: '7-10 days',
            availability: -40,
            rateIncrease: +10
          },
          
          regionalFestivals: {
            duration: '5-7 days',
            availability: -30
          }
        },
        
        agricultural: {
          sowing: {
            months: ['Jun', 'Jul'],
            availability: -30,
            affected: 'Unskilled labor'
          },
          
          harvest: {
            months: ['Mar', 'Apr', 'Oct', 'Nov'],
            availability: -40,
            rateIncrease: +15
          }
        },
        
        summer: {
          months: ['Apr', 'May'],
          productivity: -25,
          measures: ['Shift timings', 'Hydration breaks']
        }
      },
      
      // Optimal construction periods
      optimalPeriods: {
        foundation: {
          best: ['Oct', 'Nov', 'Dec', 'Jan'],
          avoid: ['Jul', 'Aug'],
          reason: 'Dry weather needed'
        },
        
        structure: {
          best: ['Oct', 'Nov', 'Dec', 'Jan', 'Feb'],
          acceptable: ['Mar', 'Sep'],
          challenging: ['Jun', 'Jul', 'Aug']
        },
        
        finishing: {
          best: ['Dec', 'Jan', 'Feb', 'Mar'],
          reason: 'Dry weather for painting, wood work'
        },
        
        overall: {
          start: 'October',
          completion: 'May',
          advantages: ['Better weather', 'Labor availability', 'Material prices']
        }
      }
    };

#### 19.2 Market Dynamics

typescript

    // Market-based price adjustments
    const marketDynamics = {
      // Price indices
      priceIndices: {
        updateFrequency: 'Monthly',
        
        tracking: {
          cement: {
            basePrice: 380, // per bag
            volatility: 'Low',
            range: '350-420'
          },
          
          steel: {
            basePrice: 65000, // per MT
            volatility: 'High',
            range: '55000-75000',
            factors: ['International prices', 'Import duties', 'Demand']
          },
          
          diesel: {
            impact: 'Transportation costs',
            correlation: '₹1/L = 0.5% material cost increase'
          }
        }
      },
      
      // Bulk purchase benefits
      bulkPurchase: {
        thresholds: {
          cement: {
            retail: { qty: '<100 bags', price: 0 },
            bulk: { qty: '100-500 bags', discount: -3 },
            wholesale: { qty: '>500 bags', discount: -5 }
          },
          
          steel: {
            retail: { qty: '<5 MT', price: 0 },
            bulk: { qty: '5-20 MT', discount: -2 },
            wholesale: { qty: '>20 MT', discount: -4 }
          },
          
          tiles: {
            retail: { qty: '<500 sqft', price: 0 },
            bulk: { qty: '500-2000 sqft', discount: -10 },
            project: { qty: '>2000 sqft', discount: -15 }
          }
        },
        
        creditTerms: {
          standard: '30 days',
          bulk: '45-60 days',
          relationship: '60-90 days'
        }
      },
      
      // Location-based pricing
      locationPricing: {
        factoryProximity: {
          cement: {
            near: '<50km from plant: -5%',
            medium: '50-200km: 0%',
            far: '>200km: +8%'
          },
          
          steel: {
            near: '<100km from stockyard: -3%',
            medium: '100-300km: 0%',
            far: '>300km: +5%'
          }
        },
        
        urbanRural: {
          metro: {
            materials: 0,
            labor: +20,
            overall: +5
          },
          
          tier2: {
            materials: +3,
            labor: 0,
            overall: +2
          },
          
          rural: {
            materials: +10,
            labor: -20,
            overall: -5
          }
        }
      },
      
      // Market condition adjustments
      adjustForMarket: (
        basePrice: number,
        conditions: MarketConditions
      ) => {
        let adjustedPrice = basePrice;
        
        // Seasonal adjustment
        adjustedPrice *= (1 + conditions.seasonalFactor / 100);
        
        // Bulk discount
        adjustedPrice *= (1 - conditions.bulkDiscount / 100);
        
        // Location adjustment
        adjustedPrice *= (1 + conditions.locationFactor / 100);
        
        // Market demand
        if (conditions.demandSupply === 'high_demand') {
          adjustedPrice *= 1.08;
        } else if (conditions.demandSupply === 'oversupply') {
          adjustedPrice *= 0.95;
        }
        
        return Math.round(adjustedPrice);
      }
    };

### 20\. Cost Optimization Algorithms

#### 20.1 Value Engineering

typescript

    // Cost optimization strategies
    const costOptimization = {
      // Material optimization
      materialOptimization: {
        // Concrete grade optimization
        concreteGrades: {
          strategy: 'Use minimum grade per structural requirement',
          
          recommendations: {
            foundation: {
              pcc: 'M10 sufficient',
              footings: 'M20 for residential',
              raft: 'M25 if required'
            },
            
            superstructure: {
              columns: 'M25 up to G+3',
              beams: 'M20 sufficient',
              slabs: 'M20 for spans <5m'
            },
            
            savings: '₹200-300/cum using optimal grade'
          }
        },
        
        // Steel optimization
        steelOptimization: {
          designOptimization: {
            method: 'Limit state vs Working stress',
            savings: '10-15% steel quantity'
          },
          
          barDiameter: {
            strategy: 'Use fewer larger dia bars',
            example: '4-16mm instead of 6-12mm',
            laborSaving: '20% in tying time'
          },
          
          wastageReduction: {
            planning: 'Prepare bar bending schedule',
            ordering: 'Order cut lengths for <5% wastage',
            savings: '₹2000-3000/MT'
          }
        },
        
        // Alternative materials
        alternatives: {
          aacVsBrick: {
            comparison: {
              materialCost: 'AAC 20% higher',
              masonrySpeed: 'AAC 3x faster',
              plasterSaving: '50% less plaster',
              structuralSaving: '10% due to less weight'
            },
            
            netImpact: 'Break-even to 5% savings overall'
          },
          
          flyAshBricks: {
            savings: '₹1-2 per brick',
            quality: 'Equal or better than clay',
            availability: 'Check local availability'
          },
          
          mSand: {
            comparison: 'Usually ₹200-300/ton cheaper',
            quality: 'Better grading, no silt',
            consideration: 'Initial adjustment in mix'
          }
        }
      },
      
      // Design optimization
      designOptimization: {
        // Structural system
        structuralSystem: {
          frameVsLoadBearing: {
            g: 'Load bearing economical',
            'g+1': 'Either system works',
            'g+2+': 'Frame structure better'
          },
          
          spanOptimization: {
            optimal: '3.5-4.5m spans',
            reasoning: 'Minimal beam depth, standard shuttering'
          },
          
          slabSystems: {
            conventional: 'Economical for regular layouts',
            flat: 'Saves 20% floor height but +15% cost',
            waffle: 'For large spans only'
          }
        },
        
        // Layout efficiency
        layoutEfficiency: {
          wallLengthRatio: {
            calculation: 'Total wall length / carpet area',
            target: '<1.2 for efficiency',
            impact: 'Each 0.1 reduction saves 3%'
          },
          
          wetAreaClustering: {
            strategy: 'Group bathrooms and kitchen',
            savings: '15-20% on plumbing',
            maintenance: 'Easier future maintenance'
          },
          
          standardization: {
            doors: 'Use 2-3 standard sizes',
            windows: 'Modular sizes',
            rooms: 'Avoid odd dimensions',
            savings: '5-8% on finishing'
          }
        }
      },
      
      // Execution optimization
      executionOptimization: {
        // Phasing strategy
        phasing: {
          horizontal: {
            method: 'Complete floor by floor',
            advantages: ['Earlier partial use', 'Better cash flow'],
            suitable: 'Single family homes'
          },
          
          vertical: {
            method: 'Structure complete, then finishes',
            advantages: ['Faster overall', 'Economy of scale'],
            suitable: 'Investment properties'
          }
        },
        
        // Resource optimization
        resourcePlanning: {
          laborContinuity: {
            strategy: 'Maintain consistent crew',
            benefit: '15% productivity improvement'
          },
          
          materialFlow: {
            justInTime: 'Reduce inventory costs',
            bulkPurchase: 'For non-perishable items',
            balance: '70% bulk, 30% JIT'
          },
          
          equipmentSharing: {
            scaffolding: 'Reuse across phases',
            machinery: 'Optimal rental periods',
            savings: '₹50-70/sqft'
          }
        }
      },
      
      // Generate optimization report
      generateOptimizationPlan: (project: ProjectDetails) => {
        const optimizations = [];
        
        // Check concrete optimization
        if (project.concreteGrade > 'M20' && project.floors <= 3) {
          optimizations.push({
            category: 'Material',
            item: 'Concrete grade',
            current: project.concreteGrade,
            recommended: 'M20',
            savings: '₹300/cum'
          });
        }
        
        // Check span optimization
        if (project.typicalSpan > 5) {
          optimizations.push({
            category: 'Design',
            item: 'Span optimization',
            current: `${project.typicalSpan}m`,
            recommended: '4.5m',
            savings: '8% in structure cost'
          });
        }
        
        return {
          optimizations,
          totalSavings: calculateTotalSavings(optimizations),
          implementationPlan: createImplementationPlan(optimizations)
        };
      }
    };

### 21\. Validation Rules & Constraints

#### 21.1 Input Validation Rules

typescript

    // Comprehensive validation system
    const validationRules = {
      // Dimensional validations
      dimensions: {
        plotArea: {
          min: 500, // sqft
          max: 50000, // sqft for residential
          validate: (area: number) => {
            if (area < 500) return 'Plot area too small for construction';
            if (area > 50000) return 'Consider commercial rates for large plots';
            return null;
          }
        },
        
        builtUpArea: {
          validate: (builtUp: number, plot: number) => {
            const coverage = builtUp / plot;
            if (coverage > 0.8) return 'Coverage exceeds typical allowable limits';
            if (coverage < 0.3) return 'Very low coverage, verify requirements';
            return null;
          }
        },
        
        floors: {
          min: 0, // Ground only
          max: 10, // For residential without special approval
          validate: (floors: number, plotArea: number) => {
            if (floors > 4 && plotArea < 2000) {
              return 'High rise on small plot needs special consideration';
            }
            return null;
          }
        },
        
        roomSizes: {
          bedroom: { min: 80, recommended: 100 },
          bathroom: { min: 25, recommended: 35 },
          kitchen: { min: 50, recommended: 70 },
          
          validate: (room: RoomInput) => {
            const minSize = this[room.type]?.min;
            if (room.area < minSize) {
              return `${room.type} below minimum size of ${minSize} sqft`;
            }
            return null;
          }
        }
      },
      
      // Cost validations
      costs: {
        perSqft: {
          min: 1200,
          max: 5000,
          
          validate: (cost: number, quality: string) => {
            const expectedRange = {
              economy: { min: 1200, max: 1800 },
              standard: { min: 1600, max: 2400 },
              premium: { min: 2200, max: 3500 },
              luxury: { min: 3000, max: 5000 }
            };
            
            const range = expectedRange[quality];
            if (cost < range.min) {
              return `Cost unusually low for ${quality} construction`;
            }
            if (cost > range.max) {
              return `Cost higher than typical ${quality} range`;
            }
            return null;
          }
        },
        
        totalBudget: {
          validate: (budget: number, area: number, quality: string) => {
            const minExpected = area * costRanges[quality].min;
            if (budget < minExpected * 0.8) {
              return 'Budget may be insufficient for desired quality';
            }
            return null;
          }
        }
      },
      
      // Technical validations
      technical: {
        structural: {
          columnSpacing: {
            max: 5, // meters
            validate: (spacing: number) => {
              if (spacing > 5) return 'Column spacing too large, add more columns';
              if (spacing < 2.5) return 'Too many columns, optimize layout';
              return null;
            }
          },
          
          slabThickness: {
            calculate: (span: number) => span * 1000 / 30, // mm
            validate: (thickness: number, span: number) => {
              const min = this.calculate(span);
              if (thickness < min) {
                return `Slab thickness should be minimum ${min}mm for ${span}m span`;
              }
              return null;
            }
          },
          
          beamDepth: {
            calculate: (span: number) => span * 1000 / 12,
            validate: (depth: number, span: number) => {
              const min = this.calculate(span);
              if (depth < min) {
                return `Beam depth should be minimum ${min}mm for ${span}m span`;
              }
              return null;
            }
          }
        },
        
        services: {
          electrical: {
            loadPerSqft: { min: 5, max: 30 }, // watts
            validate: (load: number, area: number) => {
              const wattPerSqft = load / area;
              if (wattPerSqft < 5) return 'Electrical load seems insufficient';
              if (wattPerSqft > 30) return 'Very high electrical load specified';
              return null;
            }
          },
          
          plumbing: {
            bathrooms: {
              validate: (bathrooms: number, bedrooms: number) => {
                if (bathrooms < bedrooms * 0.5) {
                  return 'Consider more bathrooms for comfort';
                }
                if (bathrooms > bedrooms + 1) {
                  return 'Too many bathrooms for the configuration';
                }
                return null;
              }
            }
          }
        }
      },
      
      // Cross validations
      crossValidations: {
        areaVsCost: (area: number, cost: number) => {
          const perSqft = cost / area;
          if (perSqft < 1000) return 'Unusually low cost, verify all components';
          if (perSqft > 6000) return 'Very high cost, consider value engineering';
          return null;
        },
        
        timeVsArea: (area: number, months: number) => {
          const expectedMonths = Math.ceil(area / 1000) + 6;
          if (months < expectedMonths * 0.7) {
            return 'Timeline seems aggressive, verify feasibility';
          }
          if (months > expectedMonths * 1.5) {
            return 'Timeline seems relaxed, can potentially optimize';
          }
          return null;
        }
      },
      
      // Run all validations
      validateProject: (project: ProjectInput) => {
        const errors = [];
        const warnings = [];
        
        // Dimensional validations
        const plotError = validationRules.dimensions.plotArea.validate(project.plotArea);
        if (plotError) errors.push(plotError);
        
        // Cost validations
        const costWarning = validationRules.costs.perSqft.validate(
          project.estimatedCost / project.builtUpArea,
          project.qualityTier
        );
        if (costWarning) warnings.push(costWarning);
        
        // Technical validations
        project.rooms.forEach(room => {
          const roomError = validationRules.dimensions.roomSizes.validate(room);
          if (roomError) warnings.push(roomError);
        });
        
        return {
          valid: errors.length === 0,
          errors,
          warnings,
          suggestions: generateSuggestions(warnings)
        };
      }
    };

#### 21.2 Constraint Management

typescript

    // System constraints and limits
    const systemConstraints = {
      // Calculation limits
      calculationLimits: {
        maxRooms: 50,
        maxFloors: 10,
        maxIterations: 1000,
        timeout: 30000, // ms
        
        enforce: (input: any) => {
          if (input.rooms?.length > this.maxRooms) {
            throw new Error(`Maximum ${this.maxRooms} rooms supported`);
          }
          if (input.floors > this.maxFloors) {
            throw new Error(`Maximum ${this.maxFloors} floors supported`);
          }
        }
      },
      
      // Business rules
      businessRules: {
        minimumProject: {
          area: 500, // sqft
          budget: 1000000, // ₹10 lakhs
          
          validate: (project: ProjectInput) => {
            if (project.area < this.area) {
              return 'Project too small for detailed estimation';
            }
            if (project.budget < this.budget) {
              return 'Budget below minimum viable project';
            }
            return null;
          }
        },
        
        qualityConstraints: {
          economy: {
            maxArea: 2000,
            maxFloors: 2,
            features: ['Basic finishes', 'Standard brands']
          },
          
          premium: {
            minArea: 1200,
            features: ['Better finishes', 'Some automation']
          },
          
          luxury: {
            minArea: 2500,
            minBudget: 10000000,
            features: ['Imported materials', 'Full automation']
          }
        }
      },
      
      // Regional constraints
      regionalConstraints: {
        seismicZones: {
          'Zone V': {
            maxFloors: 4, // Without special design
            mandatoryFeatures: ['Ductile detailing', 'Shear walls'],
            costImpact: 1.15
          },
          
          'Zone IV': {
            maxFloors: 7,
            mandatoryFeatures: ['Seismic bands', 'Corner reinforcement'],
            costImpact: 1.08
          }
        },
        
        coastalAreas: {
          distance: 500, // meters from coast
          requirements: [
            'Corrosion resistant steel',
            'Special paint systems',
            'Enhanced cover to reinforcement'
          ],
          costImpact: 1.12
        }
      },
      
      // Safety factors
      safetyFactors: {
        quantities: {
          always: 1.05, // 5% general safety
          structural: 1.10, // 10% for critical items
          finishes: 1.15 // 15% for aesthetic items
        },
        
        costs: {
          material: 1.05,
          labor: 1.08,
          overall: 1.10
        },
        
        time: {
          normal: 1.15,
          monsoon: 1.30,
          fastTrack: 1.05
        }
      }
    };

* * *

Engineering & Costing Logic Handbook - Conclusion
-------------------------------------------------

This comprehensive handbook provides the complete engineering foundation for accurate construction cost estimation in the Indian market. With detailed formulas, consumption rates, regional factors, and optimization strategies, the development team can implement precise calculation logic that reflects real-world construction practices.

### Key Implementation Notes

1.  **Accuracy First**: Use the detailed consumption rates and formulas exactly as specified
2.  **Regional Sensitivity**: Always apply location-based adjustments
3.  **Quality Differentiation**: Maintain clear distinctions between quality tiers
4.  **Validation Critical**: Implement all validation rules to ensure reliable outputs
5.  **Optimization Value**: Present cost optimization suggestions proactively
6.  **Market Dynamics**: Regular updates needed for price indices

### Success Metrics for Implementation

*   **Calculation Accuracy**: ±5% of actual project costs
*   **Coverage**: 95% of common residential scenarios
*   **Performance**: <3 seconds for complete calculation
*   **Validation**: 100% of edge cases handled gracefully

The combination of detailed engineering knowledge and systematic calculation approaches ensures that Clarity Engine will deliver the most accurate and trustworthy cost estimates in the Indian construction industry.

* * *

**Document Version:** 2.0  
**Last Updated:** November 2024  
**Total Pages:** 287  
**Status:** Final - Ready for Implementation  
**Next Review:** Post-MVP launch feedback