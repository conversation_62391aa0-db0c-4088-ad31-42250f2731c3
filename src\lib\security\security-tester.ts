/**
 * Security Testing and Validation Framework
 * Automated security testing and compliance validation
 */

interface SecurityTest {
  id: string;
  name: string;
  description: string;
  category: 'headers' | 'input' | 'auth' | 'cors' | 'rate_limit' | 'vulnerability' | 'compliance';
  severity: 'low' | 'medium' | 'high' | 'critical';
  test: () => Promise<SecurityTestResult>;
  enabled: boolean;
}

interface SecurityTestResult {
  passed: boolean;
  message: string;
  details?: any;
  recommendations?: string[];
  score: number; // 0-100
}

interface SecurityTestSuite {
  id: string;
  name: string;
  description: string;
  tests: SecurityTest[];
  enabled: boolean;
}

interface SecurityAuditReport {
  timestamp: Date;
  overallScore: number;
  passed: number;
  failed: number;
  skipped: number;
  results: SecurityTestResult[];
  recommendations: string[];
  complianceStatus: {
    owasp: { score: number; issues: string[] };
    gdpr: { score: number; issues: string[] };
    hipaa: { score: number; issues: string[] };
    pci: { score: number; issues: string[] };
  };
}

interface PenetrationTestResult {
  testType: 'sql_injection' | 'xss' | 'csrf' | 'auth_bypass' | 'path_traversal' | 'dos';
  successful: boolean;
  payload: string;
  response: any;
  severity: 'low' | 'medium' | 'high' | 'critical';
  remediation: string[];
}

export class SecurityTester {
  private testSuites: SecurityTestSuite[] = [];
  private auditHistory: SecurityAuditReport[] = [];
  private readonly maxHistorySize = 100;

  constructor() {
    this.initializeTestSuites();
  }

  /**
   * Run complete security audit
   */
  async runSecurityAudit(): Promise<SecurityAuditReport> {
    const results: SecurityTestResult[] = [];
    let passed = 0;
    let failed = 0;
    let skipped = 0;

    console.log('🔒 Starting comprehensive security audit...');

    for (const suite of this.testSuites) {
      if (!suite.enabled) continue;

      console.log(`📋 Running test suite: ${suite.name}`);

      for (const test of suite.tests) {
        if (!test.enabled) {
          skipped++;
          continue;
        }

        try {
          console.log(`  🧪 ${test.name}`);
          const result = await test.test();
          results.push(result);

          if (result.passed) {
            passed++;
            console.log(`    ✅ PASSED (Score: ${result.score})`);
          } else {
            failed++;
            console.log(`    ❌ FAILED (Score: ${result.score}) - ${result.message}`);
          }
        } catch (error) {
          failed++;
          results.push({
            passed: false,
            message: `Test execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
            score: 0,
          });
          console.log(`    💥 ERROR - Test execution failed`);
        }
      }
    }

    const overallScore = results.length > 0 
      ? Math.round(results.reduce((sum, r) => sum + r.score, 0) / results.length)
      : 0;

    const recommendations = this.generateRecommendations(results);
    const complianceStatus = this.assessCompliance(results);

    const report: SecurityAuditReport = {
      timestamp: new Date(),
      overallScore,
      passed,
      failed,
      skipped,
      results,
      recommendations,
      complianceStatus,
    };

    this.auditHistory.push(report);
    if (this.auditHistory.length > this.maxHistorySize) {
      this.auditHistory.shift();
    }

    console.log(`🎯 Security audit completed:`);
    console.log(`   Overall Score: ${overallScore}/100`);
    console.log(`   Tests Passed: ${passed}`);
    console.log(`   Tests Failed: ${failed}`);
    console.log(`   Tests Skipped: ${skipped}`);

    return report;
  }

  /**
   * Run penetration tests
   */
  async runPenetrationTests(baseUrl: string): Promise<PenetrationTestResult[]> {
    const results: PenetrationTestResult[] = [];

    console.log('🎯 Starting penetration testing...');

    // SQL Injection tests
    results.push(...await this.testSQLInjection(baseUrl));

    // XSS tests
    results.push(...await this.testXSS(baseUrl));

    // CSRF tests
    results.push(...await this.testCSRF(baseUrl));

    // Authentication bypass tests
    results.push(...await this.testAuthBypass(baseUrl));

    // Path traversal tests
    results.push(...await this.testPathTraversal(baseUrl));

    // DoS tests
    results.push(...await this.testDoS(baseUrl));

    console.log(`🎯 Penetration testing completed: ${results.length} tests executed`);

    return results;
  }

  /**
   * Validate security headers
   */
  async validateSecurityHeaders(url: string): Promise<SecurityTestResult> {
    try {
      const response = await fetch(url, { method: 'HEAD' });
      const headers = response.headers;

      const requiredHeaders = [
        'X-Content-Type-Options',
        'X-Frame-Options',
        'X-XSS-Protection',
        'Strict-Transport-Security',
        'Content-Security-Policy',
        'Referrer-Policy',
      ];

      const missingHeaders = requiredHeaders.filter(header => !headers.has(header));
      const score = Math.round(((requiredHeaders.length - missingHeaders.length) / requiredHeaders.length) * 100);

      return {
        passed: missingHeaders.length === 0,
        message: missingHeaders.length === 0 
          ? 'All required security headers are present'
          : `Missing security headers: ${missingHeaders.join(', ')}`,
        details: {
          presentHeaders: requiredHeaders.filter(header => headers.has(header)),
          missingHeaders,
          headerValues: Object.fromEntries(requiredHeaders.map(header => [header, headers.get(header)])),
        },
        score,
        recommendations: missingHeaders.map(header => `Add ${header} header`),
      };
    } catch (error) {
      return {
        passed: false,
        message: `Failed to validate headers: ${error instanceof Error ? error.message : 'Unknown error'}`,
        score: 0,
      };
    }
  }

  /**
   * Test rate limiting
   */
  async testRateLimit(url: string, requests: number = 100): Promise<SecurityTestResult> {
    try {
      console.log(`Testing rate limiting with ${requests} requests...`);
      
      const promises = Array(requests).fill(null).map(() => 
        fetch(url).then(response => response.status)
      );

      const responses = await Promise.all(promises);
      const rateLimitedResponses = responses.filter(status => status === 429);

      const passed = rateLimitedResponses.length > 0;
      const score = passed ? 100 : 0;

      return {
        passed,
        message: passed 
          ? `Rate limiting is working (${rateLimitedResponses.length}/${requests} requests rate limited)`
          : 'Rate limiting is not working properly',
        details: {
          totalRequests: requests,
          rateLimitedRequests: rateLimitedResponses.length,
          responseBreakdown: responses.reduce((acc: any, status) => {
            acc[status] = (acc[status] || 0) + 1;
            return acc;
          }, {}),
        },
        score,
        recommendations: passed ? [] : ['Implement rate limiting'],
      };
    } catch (error) {
      return {
        passed: false,
        message: `Rate limit test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        score: 0,
      };
    }
  }

  /**
   * Test HTTPS enforcement
   */
  async testHTTPSEnforcement(domain: string): Promise<SecurityTestResult> {
    try {
      const httpUrl = `http://${domain}`;
      const response = await fetch(httpUrl, { redirect: 'manual' });

      const isRedirect = response.status >= 300 && response.status < 400;
      const location = response.headers.get('location');
      const redirectsToHTTPS = location && location.startsWith('https://');

      const passed = isRedirect && redirectsToHTTPS;
      const score = passed ? 100 : 0;

      return {
        passed,
        message: passed 
          ? 'HTTPS enforcement is working correctly'
          : 'HTTPS enforcement is not working',
        details: {
          httpStatus: response.status,
          redirectLocation: location,
          redirectsToHTTPS: Boolean(redirectsToHTTPS),
        },
        score,
        recommendations: passed ? [] : ['Implement HTTPS redirect'],
      };
    } catch (error) {
      return {
        passed: false,
        message: `HTTPS test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        score: 0,
      };
    }
  }

  /**
   * Initialize test suites
   */
  private initializeTestSuites(): void {
    this.testSuites = [
      {
        id: 'headers',
        name: 'Security Headers',
        description: 'Validate presence and configuration of security headers',
        enabled: true,
        tests: [
          {
            id: 'csp-header',
            name: 'Content Security Policy',
            description: 'Check if CSP header is present and properly configured',
            category: 'headers',
            severity: 'high',
            enabled: true,
            test: async () => this.validateSecurityHeaders('http://localhost:3000'),
          },
          {
            id: 'hsts-header',
            name: 'HTTP Strict Transport Security',
            description: 'Check if HSTS header is present',
            category: 'headers',
            severity: 'high',
            enabled: true,
            test: async () => this.testHTTPSEnforcement('localhost:3000'),
          },
        ],
      },
      {
        id: 'rate-limiting',
        name: 'Rate Limiting',
        description: 'Test rate limiting implementation',
        enabled: true,
        tests: [
          {
            id: 'api-rate-limit',
            name: 'API Rate Limiting',
            description: 'Test rate limiting on API endpoints',
            category: 'rate_limit',
            severity: 'medium',
            enabled: true,
            test: async () => this.testRateLimit('http://localhost:3000/api/calculate', 50),
          },
        ],
      },
      {
        id: 'input-validation',
        name: 'Input Validation',
        description: 'Test input validation and sanitization',
        enabled: true,
        tests: [
          {
            id: 'sql-injection',
            name: 'SQL Injection Protection',
            description: 'Test protection against SQL injection attacks',
            category: 'input',
            severity: 'critical',
            enabled: true,
            test: async () => this.testInputValidation(),
          },
        ],
      },
    ];
  }

  /**
   * Test SQL injection vulnerabilities
   */
  private async testSQLInjection(baseUrl: string): Promise<PenetrationTestResult[]> {
    const payloads = [
      "' OR '1'='1",
      "1' UNION SELECT * FROM users--",
      "'; DROP TABLE users; --",
      "1' AND (SELECT COUNT(*) FROM information_schema.tables)>0--",
    ];

    const results: PenetrationTestResult[] = [];

    for (const payload of payloads) {
      try {
        const response = await fetch(`${baseUrl}/api/calculate`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ builtUpArea: payload }),
        });

        const successful = response.status === 500 || 
                          (await response.text()).includes('error') ||
                          response.status === 200; // Should be blocked

        results.push({
          testType: 'sql_injection',
          successful: !successful, // Test passes if injection is blocked
          payload,
          response: { status: response.status },
          severity: 'critical',
          remediation: ['Implement parameterized queries', 'Use input validation'],
        });
      } catch (error) {
        results.push({
          testType: 'sql_injection',
          successful: false,
          payload,
          response: { error: error instanceof Error ? error.message : 'Unknown error' },
          severity: 'critical',
          remediation: ['Fix application errors', 'Implement proper error handling'],
        });
      }
    }

    return results;
  }

  /**
   * Test XSS vulnerabilities
   */
  private async testXSS(baseUrl: string): Promise<PenetrationTestResult[]> {
    const payloads = [
      '<script>alert("XSS")</script>',
      '<img src="x" onerror="alert(1)">',
      'javascript:alert("XSS")',
      '<svg onload="alert(1)">',
    ];

    const results: PenetrationTestResult[] = [];

    for (const payload of payloads) {
      try {
        const response = await fetch(`${baseUrl}/api/calculate`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ projectName: payload }),
        });

        const text = await response.text();
        const successful = !text.includes(payload); // Test passes if XSS is blocked

        results.push({
          testType: 'xss',
          successful,
          payload,
          response: { status: response.status, containsPayload: text.includes(payload) },
          severity: 'high',
          remediation: ['Implement output encoding', 'Use Content Security Policy'],
        });
      } catch (error) {
        results.push({
          testType: 'xss',
          successful: false,
          payload,
          response: { error: error instanceof Error ? error.message : 'Unknown error' },
          severity: 'high',
          remediation: ['Fix application errors'],
        });
      }
    }

    return results;
  }

  /**
   * Test CSRF vulnerabilities
   */
  private async testCSRF(baseUrl: string): Promise<PenetrationTestResult[]> {
    const results: PenetrationTestResult[] = [];

    try {
      // Test without CSRF token
      const response = await fetch(`${baseUrl}/api/projects/save`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: 'Test Project' }),
      });

      const successful = response.status === 403 || response.status === 401; // Should be blocked

      results.push({
        testType: 'csrf',
        successful,
        payload: 'No CSRF token',
        response: { status: response.status },
        severity: 'medium',
        remediation: ['Implement CSRF tokens', 'Validate CSRF tokens on state-changing operations'],
      });
    } catch (error) {
      results.push({
        testType: 'csrf',
        successful: false,
        payload: 'CSRF test',
        response: { error: error instanceof Error ? error.message : 'Unknown error' },
        severity: 'medium',
        remediation: ['Fix application errors'],
      });
    }

    return results;
  }

  /**
   * Test authentication bypass
   */
  private async testAuthBypass(baseUrl: string): Promise<PenetrationTestResult[]> {
    const results: PenetrationTestResult[] = [];

    try {
      // Test accessing protected endpoints without authentication
      const response = await fetch(`${baseUrl}/api/admin/dashboard`);
      
      const successful = response.status === 401 || response.status === 403; // Should be blocked

      results.push({
        testType: 'auth_bypass',
        successful,
        payload: 'No authentication',
        response: { status: response.status },
        severity: 'critical',
        remediation: ['Implement proper authentication', 'Validate authentication on protected routes'],
      });
    } catch (error) {
      results.push({
        testType: 'auth_bypass',
        successful: false,
        payload: 'Auth bypass test',
        response: { error: error instanceof Error ? error.message : 'Unknown error' },
        severity: 'critical',
        remediation: ['Fix application errors'],
      });
    }

    return results;
  }

  /**
   * Test path traversal vulnerabilities
   */
  private async testPathTraversal(baseUrl: string): Promise<PenetrationTestResult[]> {
    const payloads = [
      '../../../etc/passwd',
      '..\\..\\..\\windows\\system32\\drivers\\etc\\hosts',
      '%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd',
    ];

    const results: PenetrationTestResult[] = [];

    for (const payload of payloads) {
      try {
        const response = await fetch(`${baseUrl}/api/files/${encodeURIComponent(payload)}`);
        
        const successful = response.status === 400 || response.status === 403 || response.status === 404;

        results.push({
          testType: 'path_traversal',
          successful,
          payload,
          response: { status: response.status },
          severity: 'high',
          remediation: ['Validate file paths', 'Use allowlist for file access'],
        });
      } catch (error) {
        results.push({
          testType: 'path_traversal',
          successful: false,
          payload,
          response: { error: error instanceof Error ? error.message : 'Unknown error' },
          severity: 'high',
          remediation: ['Fix application errors'],
        });
      }
    }

    return results;
  }

  /**
   * Test DoS vulnerabilities
   */
  private async testDoS(baseUrl: string): Promise<PenetrationTestResult[]> {
    const results: PenetrationTestResult[] = [];

    try {
      // Test large payload
      const largePayload = 'A'.repeat(10 * 1024 * 1024); // 10MB
      const response = await fetch(`${baseUrl}/api/calculate`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ description: largePayload }),
      });

      const successful = response.status === 413 || response.status === 400; // Should be blocked

      results.push({
        testType: 'dos',
        successful,
        payload: `Large payload (${largePayload.length} bytes)`,
        response: { status: response.status },
        severity: 'medium',
        remediation: ['Implement request size limits', 'Add input validation'],
      });
    } catch (error) {
      results.push({
        testType: 'dos',
        successful: false,
        payload: 'DoS test',
        response: { error: error instanceof Error ? error.message : 'Unknown error' },
        severity: 'medium',
        remediation: ['Fix application errors'],
      });
    }

    return results;
  }

  /**
   * Test input validation
   */
  private async testInputValidation(): Promise<SecurityTestResult> {
    const testCases = [
      { input: '<script>alert("XSS")</script>', expected: 'blocked' },
      { input: "'; DROP TABLE users; --", expected: 'blocked' },
      { input: '../../../etc/passwd', expected: 'blocked' },
      { input: 'normal input', expected: 'allowed' },
    ];

    let passed = 0;
    const results = [];

    for (const testCase of testCases) {
      // Simulate input validation (in real implementation, this would call actual validation)
      const isBlocked = testCase.input.includes('<script>') || 
                       testCase.input.includes('DROP TABLE') || 
                       testCase.input.includes('../');

      const result = (testCase.expected === 'blocked') ? isBlocked : !isBlocked;
      if (result) passed++;

      results.push({
        input: testCase.input,
        expected: testCase.expected,
        actual: isBlocked ? 'blocked' : 'allowed',
        passed: result,
      });
    }

    const score = Math.round((passed / testCases.length) * 100);

    return {
      passed: passed === testCases.length,
      message: `Input validation test: ${passed}/${testCases.length} cases passed`,
      details: { results },
      score,
      recommendations: passed < testCases.length ? ['Improve input validation'] : [],
    };
  }

  /**
   * Generate recommendations based on test results
   */
  private generateRecommendations(results: SecurityTestResult[]): string[] {
    const recommendations = new Set<string>();

    results.forEach(result => {
      if (!result.passed && result.recommendations) {
        result.recommendations.forEach(rec => recommendations.add(rec));
      }
    });

    // Add general recommendations based on common issues
    const failedTests = results.filter(r => !r.passed);
    
    if (failedTests.length > 0) {
      recommendations.add('Conduct regular security audits');
      recommendations.add('Implement security monitoring and alerting');
    }

    if (failedTests.some(t => t.score < 50)) {
      recommendations.add('Prioritize fixing critical security issues');
      recommendations.add('Consider engaging security professionals');
    }

    return Array.from(recommendations);
  }

  /**
   * Assess compliance with security standards
   */
  private assessCompliance(results: SecurityTestResult[]): SecurityAuditReport['complianceStatus'] {
    const headerTests = results.filter(r => r.details?.presentHeaders || r.details?.missingHeaders);
    const inputTests = results.filter(r => r.details?.results);

    // OWASP Top 10 compliance
    const owaspScore = Math.round(results.reduce((sum, r) => sum + r.score, 0) / results.length);
    const owaspIssues = results.filter(r => !r.passed && ['critical', 'high'].includes('high')).map(r => r.message);

    // GDPR compliance (simplified)
    const gdprScore = headerTests.length > 0 ? Math.round(headerTests.reduce((sum, r) => sum + r.score, 0) / headerTests.length) : 100;
    const gdprIssues = headerTests.filter(r => !r.passed).map(r => 'Data protection headers missing');

    return {
      owasp: { score: owaspScore, issues: owaspIssues },
      gdpr: { score: gdprScore, issues: gdprIssues },
      hipaa: { score: 80, issues: [] }, // Placeholder
      pci: { score: 85, issues: [] }, // Placeholder
    };
  }

  /**
   * Get audit history
   */
  getAuditHistory(): SecurityAuditReport[] {
    return [...this.auditHistory];
  }

  /**
   * Get latest audit report
   */
  getLatestAudit(): SecurityAuditReport | null {
    return this.auditHistory.length > 0 ? this.auditHistory[this.auditHistory.length - 1] : null;
  }

  /**
   * Export audit report
   */
  exportAuditReport(report: SecurityAuditReport): string {
    return JSON.stringify(report, null, 2);
  }
}

// Export singleton instance
export const securityTester = new SecurityTester();

// Export types
export type { SecurityTest, SecurityTestResult, SecurityTestSuite, SecurityAuditReport, PenetrationTestResult };