/**
 * AI-Powered Recommendation System
 * Provides intelligent suggestions and cost optimization recommendations
 */

import { CalculatorFormData } from '@/components/calculator/types/wizard';
import { REGIONAL_PREFERENCES } from './smart-defaults';

export interface Recommendation {
  id: string;
  type: 'cost-optimization' | 'feature-suggestion' | 'quality-upgrade' | 'warning' | 'tip';
  priority: 'high' | 'medium' | 'low';
  title: string;
  description: string;
  action?: {
    label: string;
    field: keyof CalculatorFormData;
    value: string | boolean;
  };
  savings?: number; // Cost savings in INR
  additionalCost?: number; // Additional cost in INR
  confidence: number; // 0-1 scale
  category: 'budget' | 'design' | 'sustainability' | 'functionality' | 'resale';
  tags: string[];
}

export interface CostOptimization {
  currentCost: number;
  optimizedCost: number;
  savings: number;
  recommendations: Recommendation[];
  alternativeConfigurations: Array<{
    changes: Partial<CalculatorFormData>;
    costImpact: number;
    description: string;
    pros: string[];
    cons: string[];
  }>;
}

export interface UserProfile {
  location: string;
  budgetRange: 'budget' | 'mid-range' | 'premium' | 'luxury';
  familySize: number;
  priorities: Array<'cost' | 'quality' | 'sustainability' | 'resale' | 'comfort'>;
  timeframe: 'immediate' | 'next-6-months' | 'next-year' | 'flexible';
  experienceLevel: 'first-time' | 'experienced' | 'investor';
}

export class AIRecommendationEngine {
  private userProfile: UserProfile | null = null;
  private marketData: Record<string, any> = {}; // Simulated market data

  constructor() {
    this.initializeMarketData();
  }

  /**
   * Set user profile for personalized recommendations
   */
  setUserProfile(profile: UserProfile): void {
    this.userProfile = profile;
  }

  /**
   * Generate recommendations based on current form data
   */
  generateRecommendations(data: Partial<CalculatorFormData>): Recommendation[] {
    const recommendations: Recommendation[] = [];

    // Cost optimization recommendations
    recommendations.push(...this.getCostOptimizationRecommendations(data));

    // Feature suggestions
    recommendations.push(...this.getFeatureSuggestions(data));

    // Quality recommendations
    recommendations.push(...this.getQualityRecommendations(data));

    // Sustainability recommendations
    recommendations.push(...this.getSustainabilityRecommendations(data));

    // Regional recommendations
    recommendations.push(...this.getRegionalRecommendations(data));

    // Validation warnings
    recommendations.push(...this.getValidationWarnings(data));

    // Market insights
    recommendations.push(...this.getMarketInsights(data));

    // Sort by priority and confidence
    return recommendations
      .sort((a, b) => {
        const priorityOrder = { high: 3, medium: 2, low: 1 };
        return (priorityOrder[b.priority] - priorityOrder[a.priority]) || (b.confidence - a.confidence);
      })
      .slice(0, 12); // Limit to top 12 recommendations
  }

  /**
   * Cost optimization recommendations
   */
  private getCostOptimizationRecommendations(data: Partial<CalculatorFormData>): Recommendation[] {
    const recommendations: Recommendation[] = [];

    // Material cost optimizations
    if (data.quality === 'luxury' && data.builtUpArea) {
      const area = parseFloat(data.builtUpArea);
      const potentialSavings = area * 500; // ₹500/sqft savings

      recommendations.push({
        id: 'opt-quality-downgrade',
        type: 'cost-optimization',
        priority: 'medium',
        title: 'Consider Premium Quality Instead',
        description: `Switch to Premium quality for ₹${(potentialSavings / 100000).toFixed(1)}L savings while maintaining excellent finishes`,
        action: {
          label: 'Switch to Premium',
          field: 'quality',
          value: 'premium',
        },
        savings: potentialSavings,
        confidence: 0.8,
        category: 'budget',
        tags: ['cost-saving', 'quality', 'value'],
      });
    }

    // Room optimization
    if (data.bedrooms && data.bathrooms) {
      const bedrooms = parseInt(data.bedrooms);
      const bathrooms = parseInt(data.bathrooms);

      if (bathrooms > bedrooms) {
        recommendations.push({
          id: 'opt-bathroom-reduction',
          type: 'cost-optimization',
          priority: 'medium',
          title: 'Optimize Bathroom Count',
          description: `Consider ${bedrooms} bathrooms instead of ${bathrooms} to save on plumbing and finishing costs`,
          action: {
            label: `Set to ${bedrooms} bathrooms`,
            field: 'bathrooms',
            value: bedrooms.toString(),
          },
          savings: (bathrooms - bedrooms) * 150000,
          confidence: 0.7,
          category: 'budget',
          tags: ['cost-saving', 'practical'],
        });
      }
    }

    // Feature cost optimization
    if (data.swimmingPool) {
      recommendations.push({
        id: 'opt-pool-alternative',
        type: 'cost-optimization',
        priority: 'low',
        title: 'Pool Alternative',
        description: 'Consider a smaller plunge pool or jacuzzi to reduce costs by ₹6-8L while maintaining luxury appeal',
        savings: 600000,
        confidence: 0.6,
        category: 'budget',
        tags: ['luxury', 'alternative', 'cost-saving'],
      });
    }

    return recommendations;
  }

  /**
   * Feature suggestions based on current configuration
   */
  private getFeatureSuggestions(data: Partial<CalculatorFormData>): Recommendation[] {
    const recommendations: Recommendation[] = [];

    // Solar panel suggestions
    if (!data.solarPanels && data.builtUpArea) {
      const area = parseFloat(data.builtUpArea);
      if (area > 1500) {
        recommendations.push({
          id: 'feat-solar-panels',
          type: 'feature-suggestion',
          priority: 'high',
          title: 'Solar Panels Recommended',
          description: `For ${area} sq ft, solar panels can save ₹15-20K annually on electricity bills. Payback in 4-5 years.`,
          action: {
            label: 'Add Solar Panels',
            field: 'solarPanels',
            value: true,
          },
          additionalCost: 550000,
          confidence: 0.9,
          category: 'sustainability',
          tags: ['eco-friendly', 'cost-saving', 'long-term'],
        });
      }
    }

    // Home automation for premium/luxury
    if (!data.homeAutomation && (data.quality === 'premium' || data.quality === 'luxury')) {
      recommendations.push({
        id: 'feat-home-automation',
        type: 'feature-suggestion',
        priority: 'medium',
        title: 'Smart Home Features',
        description: 'Home automation enhances convenience and adds 8-12% to resale value in premium homes',
        action: {
          label: 'Add Home Automation',
          field: 'homeAutomation',
          value: true,
        },
        additionalCost: 400000,
        confidence: 0.8,
        category: 'functionality',
        tags: ['smart-home', 'resale-value', 'convenience'],
      });
    }

    // Security system for all configurations
    if (!data.securitySystem) {
      recommendations.push({
        id: 'feat-security-system',
        type: 'feature-suggestion',
        priority: 'high',
        title: 'Security System Essential',
        description: 'CCTV and security systems are crucial for safety and insurance benefits',
        action: {
          label: 'Add Security System',
          field: 'securitySystem',
          value: true,
        },
        additionalCost: 250000,
        confidence: 0.95,
        category: 'functionality',
        tags: ['safety', 'essential', 'insurance'],
      });
    }

    // Rainwater harvesting for sustainability
    if (!data.rainwaterHarvesting && data.plotSize) {
      const plotSize = parseFloat(data.plotSize);
      if (plotSize > 1000) {
        recommendations.push({
          id: 'feat-rainwater-harvesting',
          type: 'feature-suggestion',
          priority: 'medium',
          title: 'Rainwater Harvesting',
          description: 'Mandatory in many cities and provides water security. Can reduce water bills by 30-40%',
          action: {
            label: 'Add Rainwater Harvesting',
            field: 'rainwaterHarvesting',
            value: true,
          },
          additionalCost: 200000,
          confidence: 0.8,
          category: 'sustainability',
          tags: ['eco-friendly', 'compliance', 'water-security'],
        });
      }
    }

    return recommendations;
  }

  /**
   * Quality-related recommendations
   */
  private getQualityRecommendations(data: Partial<CalculatorFormData>): Recommendation[] {
    const recommendations: Recommendation[] = [];

    // Quality upgrade suggestions based on budget
    if (data.quality === 'smart' && data.builtUpArea) {
      const area = parseFloat(data.builtUpArea);
      const currentCost = area * 1800;
      const premiumCost = area * 2500;
      const additionalCost = premiumCost - currentCost;

      if (area < 2000) { // Smaller homes benefit more from quality upgrades
        recommendations.push({
          id: 'qual-upgrade-premium',
          type: 'quality-upgrade',
          priority: 'medium',
          title: 'Consider Premium Quality',
          description: `Premium finishes add only ₹${(additionalCost / 100000).toFixed(1)}L but significantly improve durability and resale value`,
          action: {
            label: 'Upgrade to Premium',
            field: 'quality',
            value: 'premium',
          },
          additionalCost,
          confidence: 0.7,
          category: 'design',
          tags: ['upgrade', 'durability', 'resale-value'],
        });
      }
    }

    // Material-specific recommendations
    if (data.quality && data.flooringType === 'ceramic' && data.quality !== 'smart') {
      recommendations.push({
        id: 'qual-flooring-upgrade',
        type: 'quality-upgrade',
        priority: 'low',
        title: 'Upgrade Flooring',
        description: 'Consider vitrified tiles for better durability and aesthetics with your quality selection',
        action: {
          label: 'Switch to Vitrified Tiles',
          field: 'flooringType',
          value: 'vitrified',
        },
        additionalCost: 50000,
        confidence: 0.6,
        category: 'quality',
        tags: ['flooring', 'durability', 'aesthetics'],
      });
    }

    return recommendations;
  }

  /**
   * Sustainability recommendations
   */
  private getSustainabilityRecommendations(data: Partial<CalculatorFormData>): Recommendation[] {
    const recommendations: Recommendation[] = [];

    const sustainabilityScore = this.calculateSustainabilityScore(data);

    if (sustainabilityScore < 0.5) {
      recommendations.push({
        id: 'sust-improve-score',
        type: 'tip',
        priority: 'medium',
        title: 'Improve Sustainability',
        description: 'Consider adding solar panels, rainwater harvesting, and LED lighting for a greener home',
        confidence: 0.8,
        category: 'sustainability',
        tags: ['eco-friendly', 'green-building', 'future-ready'],
      });
    }

    return recommendations;
  }

  /**
   * Regional recommendations based on location
   */
  private getRegionalRecommendations(data: Partial<CalculatorFormData>): Recommendation[] {
    const recommendations: Recommendation[] = [];

    if (!data.location) return recommendations;

    const regionalPrefs = REGIONAL_PREFERENCES[data.location];
    if (!regionalPrefs) return recommendations;

    // Location-specific tips
    const locationTips = {
      mumbai: 'In Mumbai, focus on space optimization and ventilation due to compact living',
      delhi: 'Delhi construction benefits from good insulation and air purification systems',
      bangalore: 'Bangalore\'s climate is ideal for passive cooling and large windows',
      chennai: 'In Chennai, prioritize ventilation, waterproofing, and heat-resistant materials',
      hyderabad: 'Hyderabad offers good material availability - consider local stone finishes',
    };

    const tip = locationTips[data.location as keyof typeof locationTips];
    if (tip) {
      recommendations.push({
        id: `regional-tip-${data.location}`,
        type: 'tip',
        priority: 'low',
        title: `${regionalPrefs.city} Construction Tip`,
        description: tip,
        confidence: 0.9,
        category: 'design',
        tags: ['regional', 'local-expertise', 'climate'],
      });
    }

    return recommendations;
  }

  /**
   * Validation warnings for unusual configurations
   */
  private getValidationWarnings(data: Partial<CalculatorFormData>): Recommendation[] {
    const recommendations: Recommendation[] = [];

    // Unusual bathroom to bedroom ratio
    if (data.bedrooms && data.bathrooms) {
      const bedrooms = parseInt(data.bedrooms);
      const bathrooms = parseInt(data.bathrooms);

      if (bedrooms >= 4 && bathrooms === 1) {
        recommendations.push({
          id: 'warn-insufficient-bathrooms',
          type: 'warning',
          priority: 'high',
          title: 'Insufficient Bathrooms',
          description: `${bedrooms} bedrooms typically need at least 2-3 bathrooms for comfortable living`,
          action: {
            label: 'Add Another Bathroom',
            field: 'bathrooms',
            value: '2',
          },
          confidence: 0.9,
          category: 'functionality',
          tags: ['comfort', 'practical', 'resale'],
        });
      }
    }

    // Budget reality check
    if (data.builtUpArea && data.quality) {
      const area = parseFloat(data.builtUpArea);
      const qualityCosts = { smart: 1800, premium: 2500, luxury: 3500 };
      const estimatedCost = area * qualityCosts[data.quality as keyof typeof qualityCosts];

      if (area > 3000 && data.quality === 'luxury') {
        recommendations.push({
          id: 'warn-high-budget',
          type: 'warning',
          priority: 'medium',
          title: 'High Budget Project',
          description: `Estimated cost: ₹${(estimatedCost / 10000000).toFixed(1)} Cr. Consider phased construction or premium quality`,
          confidence: 0.8,
          category: 'budget',
          tags: ['budget', 'planning', 'phased-construction'],
        });
      }
    }

    return recommendations;
  }

  /**
   * Market insights and trends
   */
  private getMarketInsights(data: Partial<CalculatorFormData>): Recommendation[] {
    const recommendations: Recommendation[] = [];

    // Construction timing insights
    recommendations.push({
      id: 'market-timing',
      type: 'tip',
      priority: 'low',
      title: 'Optimal Construction Timing',
      description: 'Start construction in Oct-Nov for completion before monsoon. Material prices are typically lower in winter',
      confidence: 0.7,
      category: 'budget',
      tags: ['timing', 'market', 'weather'],
    });

    // Resale value insights
    if (data.quality && data.location) {
      recommendations.push({
        id: 'market-resale',
        type: 'tip',
        priority: 'medium',
        title: 'Resale Value Insight',
        description: `${data.quality} quality homes in ${data.location} typically appreciate 6-8% annually`,
        confidence: 0.6,
        category: 'resale',
        tags: ['investment', 'appreciation', 'market-trends'],
      });
    }

    return recommendations;
  }

  /**
   * Generate cost optimization analysis
   */
  generateCostOptimization(data: Partial<CalculatorFormData>): CostOptimization | null {
    if (!data.builtUpArea || !data.quality) return null;

    const area = parseFloat(data.builtUpArea);
    const qualityCosts = { smart: 1800, premium: 2500, luxury: 3500 };
    const currentCost = area * qualityCosts[data.quality as keyof typeof qualityCosts];

    const optimizations: CostOptimization = {
      currentCost,
      optimizedCost: currentCost,
      savings: 0,
      recommendations: [],
      alternativeConfigurations: [],
    };

    // Quality downgrades
    if (data.quality === 'luxury') {
      const premiumCost = area * qualityCosts.premium;
      optimizations.alternativeConfigurations.push({
        changes: { quality: 'premium' },
        costImpact: premiumCost - currentCost,
        description: 'Switch to Premium Quality',
        pros: ['₹5L+ savings', 'Still excellent finishes', 'Faster completion'],
        cons: ['Slightly lower resale premium', 'Basic automation only'],
      });
    }

    // Feature optimizations
    const featureOptimizations = this.getFeatureOptimizations(data);
    optimizations.alternativeConfigurations.push(...featureOptimizations);

    return optimizations;
  }

  /**
   * Get "People like you also chose" recommendations
   */
  getPeopleAlsoChose(data: Partial<CalculatorFormData>): Array<{
    feature: string;
    percentage: number;
    description: string;
  }> {
    const profile = this.createUserProfile(data);
    
    // Simulated collaborative filtering data
    const recommendations = [
      { feature: 'Solar Panels', percentage: 78, description: 'Users with similar projects' },
      { feature: 'Home Automation', percentage: 65, description: 'Premium quality builders' },
      { feature: 'Security System', percentage: 92, description: 'All project types' },
      { feature: 'Modular Kitchen', percentage: 85, description: 'Similar budget range' },
    ];

    return recommendations.slice(0, 3);
  }

  /**
   * Private helper methods
   */
  private calculateSustainabilityScore(data: Partial<CalculatorFormData>): number {
    let score = 0;
    let maxScore = 5;

    if (data.solarPanels) score += 1;
    if (data.rainwaterHarvesting) score += 1;
    if (data.garden) score += 0.5;
    if (data.electricalFittings === 'automation') score += 1;
    if (!data.swimmingPool) score += 0.5; // Water conservation
    if (data.quality === 'smart') score += 1; // Efficient use of materials

    return score / maxScore;
  }

  private getFeatureOptimizations(data: Partial<CalculatorFormData>) {
    const optimizations = [];

    if (data.swimmingPool) {
      optimizations.push({
        changes: { swimmingPool: false },
        costImpact: -1150000,
        description: 'Remove Swimming Pool',
        pros: ['₹11.5L savings', 'Lower maintenance', 'More garden space'],
        cons: ['Reduced luxury appeal', 'Lower resale premium'],
      });
    }

    if (data.elevator) {
      optimizations.push({
        changes: { elevator: false },
        costImpact: -1600000,
        description: 'Remove Elevator',
        pros: ['₹16L savings', 'More usable space', 'Lower maintenance'],
        cons: ['Accessibility issues', 'Required for 3+ floors'],
      });
    }

    return optimizations;
  }

  private createUserProfile(data: Partial<CalculatorFormData>): UserProfile {
    // Infer user profile from form data
    const area = parseFloat(data.builtUpArea || '1000');
    const quality = data.quality || 'smart';
    
    let budgetRange: UserProfile['budgetRange'] = 'budget';
    if (area > 2000 || quality === 'premium') budgetRange = 'mid-range';
    if (area > 3000 || quality === 'luxury') budgetRange = 'luxury';

    return {
      location: data.location || 'delhi',
      budgetRange,
      familySize: parseInt(data.bedrooms || '2') + 1,
      priorities: ['cost', 'quality'],
      timeframe: 'next-6-months',
      experienceLevel: 'first-time',
    };
  }

  private initializeMarketData(): void {
    // Initialize with market trends, pricing data, etc.
    this.marketData = {
      trends: {
        'solar-adoption': 0.45,
        'automation-adoption': 0.32,
        'security-adoption': 0.78,
      },
      pricing: {
        'material-inflation': 0.08,
        'labor-inflation': 0.12,
      },
    };
  }
}

// Export singleton instance
export const aiRecommendations = new AIRecommendationEngine();

// Helper functions
export function getQuickRecommendations(data: Partial<CalculatorFormData>): Recommendation[] {
  return aiRecommendations.generateRecommendations(data);
}

export function getCostOptimizations(data: Partial<CalculatorFormData>): CostOptimization | null {
  return aiRecommendations.generateCostOptimization(data);
}