import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { loadMaterials, getMaterialPrice, getMaterialConsumption } from '../loader';
import { createMockMaterialDatabase } from '../../../test/factories';

// Mock the core materials data
vi.mock('../../../data/materials/core-materials.json', () => ({
  default: createMockMaterialDatabase(),
}));

describe('Material Loader', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('loadMaterials', () => {
    it('should load materials successfully', async () => {
      const materials = await loadMaterials();

      expect(materials).toHaveProperty('materials');
      expect(materials).toHaveProperty('locations');
      expect(materials).toHaveProperty('qualityTiers');
      expect(materials).toHaveProperty('categories');

      expect(Array.isArray(materials.materials)).toBe(true);
      expect(Array.isArray(materials.locations)).toBe(true);
      expect(Array.isArray(materials.qualityTiers)).toBe(true);
      expect(Array.isArray(materials.categories)).toBe(true);
    });

    it('should load materials with correct structure', async () => {
      const materials = await loadMaterials();

      const firstMaterial = materials.materials[0];
      expect(firstMaterial).toHaveProperty('id');
      expect(firstMaterial).toHaveProperty('name');
      expect(firstMaterial).toHaveProperty('category');
      expect(firstMaterial).toHaveProperty('unit');
      expect(firstMaterial).toHaveProperty('consumption');
      expect(firstMaterial).toHaveProperty('pricing');
      expect(firstMaterial).toHaveProperty('wastage');
    });

    it('should load locations with correct structure', async () => {
      const materials = await loadMaterials();

      const firstLocation = materials.locations[0];
      expect(firstLocation).toHaveProperty('id');
      expect(firstLocation).toHaveProperty('name');
      expect(firstLocation).toHaveProperty('multiplier');
    });

    it('should include all required quality tiers', async () => {
      const materials = await loadMaterials();

      expect(materials.qualityTiers).toContain('smart');
      expect(materials.qualityTiers).toContain('premium');
      expect(materials.qualityTiers).toContain('luxury');
    });

    it('should include all required categories', async () => {
      const materials = await loadMaterials();

      expect(materials.categories).toContain('structural');
      expect(materials.categories).toContain('finishing');
      expect(materials.categories).toContain('electrical');
      expect(materials.categories).toContain('plumbing');
      expect(materials.categories).toContain('external');
    });
  });

  describe('getMaterialPrice', () => {
    it('should return correct price for valid material, location, and quality tier', () => {
      const price = getMaterialPrice('cement', 'bangalore', 'smart');
      expect(typeof price).toBe('number');
      expect(price).toBeGreaterThan(0);
    });

    it('should return different prices for different quality tiers', () => {
      const smartPrice = getMaterialPrice('cement', 'bangalore', 'smart');
      const premiumPrice = getMaterialPrice('cement', 'bangalore', 'premium');
      const luxuryPrice = getMaterialPrice('cement', 'bangalore', 'luxury');

      expect(smartPrice).toBeLessThan(premiumPrice);
      expect(premiumPrice).toBeLessThan(luxuryPrice);
    });

    it('should return different prices for different locations', () => {
      const bangalorePrice = getMaterialPrice('cement', 'bangalore', 'smart');
      const mumbaiPrice = getMaterialPrice('cement', 'mumbai', 'smart');

      // Mumbai should be more expensive than Bangalore
      expect(mumbaiPrice).toBeGreaterThan(bangalorePrice);
    });

    it('should handle invalid material gracefully', () => {
      const price = getMaterialPrice('invalid_material', 'bangalore', 'smart');
      expect(price).toBe(0);
    });

    it('should handle invalid location gracefully', () => {
      const price = getMaterialPrice('cement', 'invalid_location', 'smart');
      expect(price).toBe(0);
    });

    it('should handle invalid quality tier gracefully', () => {
      const price = getMaterialPrice('cement', 'bangalore', 'invalid_tier' as any);
      expect(price).toBe(0);
    });

    it('should return consistent prices for repeated calls', () => {
      const price1 = getMaterialPrice('cement', 'bangalore', 'smart');
      const price2 = getMaterialPrice('cement', 'bangalore', 'smart');
      expect(price1).toBe(price2);
    });
  });

  describe('getMaterialConsumption', () => {
    it('should return correct consumption for valid material and quality tier', () => {
      const consumption = getMaterialConsumption('cement', 'smart');
      expect(typeof consumption).toBe('number');
      expect(consumption).toBeGreaterThan(0);
    });

    it('should return different consumption for different quality tiers', () => {
      const smartConsumption = getMaterialConsumption('cement', 'smart');
      const premiumConsumption = getMaterialConsumption('cement', 'premium');
      const luxuryConsumption = getMaterialConsumption('cement', 'luxury');

      expect(smartConsumption).toBeLessThan(premiumConsumption);
      expect(premiumConsumption).toBeLessThan(luxuryConsumption);
    });

    it('should handle invalid material gracefully', () => {
      const consumption = getMaterialConsumption('invalid_material', 'smart');
      expect(consumption).toBe(0);
    });

    it('should handle invalid quality tier gracefully', () => {
      const consumption = getMaterialConsumption('cement', 'invalid_tier' as any);
      expect(consumption).toBe(0);
    });

    it('should return consistent consumption for repeated calls', () => {
      const consumption1 = getMaterialConsumption('cement', 'smart');
      const consumption2 = getMaterialConsumption('cement', 'smart');
      expect(consumption1).toBe(consumption2);
    });
  });

  describe('Performance Tests', () => {
    it('should load materials within acceptable time', async () => {
      const start = performance.now();
      await loadMaterials();
      const end = performance.now();

      // Should load within 50ms
      expect(end - start).toBeLessThan(50);
    });

    it('should handle multiple concurrent material loads', async () => {
      const promises = Array.from({ length: 10 }, () => loadMaterials());

      const start = performance.now();
      const results = await Promise.all(promises);
      const end = performance.now();

      expect(results).toHaveLength(10);
      expect(results.every(r => r.materials.length > 0)).toBe(true);
      
      // All loads should complete within 200ms
      expect(end - start).toBeLessThan(200);
    });

    it('should perform price lookups efficiently', () => {
      const materials = ['cement', 'steel', 'sand', 'aggregate', 'bricks'];
      const locations = ['bangalore', 'mumbai', 'delhi'];
      const qualityTiers = ['smart', 'premium', 'luxury'];

      const start = performance.now();
      
      // Perform 1000 price lookups
      for (let i = 0; i < 1000; i++) {
        const material = materials[i % materials.length];
        const location = locations[i % locations.length];
        const quality = qualityTiers[i % qualityTiers.length];
        getMaterialPrice(material, location, quality as any);
      }
      
      const end = performance.now();

      // Should complete within 100ms
      expect(end - start).toBeLessThan(100);
    });
  });

  describe('Data Validation', () => {
    it('should validate material data structure', async () => {
      const materials = await loadMaterials();

      materials.materials.forEach(material => {
        expect(material).toHaveProperty('id');
        expect(material).toHaveProperty('name');
        expect(material).toHaveProperty('category');
        expect(material).toHaveProperty('unit');
        expect(material).toHaveProperty('consumption');
        expect(material).toHaveProperty('pricing');
        expect(material).toHaveProperty('wastage');

        expect(typeof material.id).toBe('string');
        expect(typeof material.name).toBe('string');
        expect(typeof material.category).toBe('string');
        expect(typeof material.unit).toBe('string');
        expect(typeof material.consumption).toBe('object');
        expect(typeof material.pricing).toBe('object');
        expect(typeof material.wastage).toBe('number');
      });
    });

    it('should validate location data structure', async () => {
      const materials = await loadMaterials();

      materials.locations.forEach(location => {
        expect(location).toHaveProperty('id');
        expect(location).toHaveProperty('name');
        expect(location).toHaveProperty('multiplier');

        expect(typeof location.id).toBe('string');
        expect(typeof location.name).toBe('string');
        expect(typeof location.multiplier).toBe('number');
        expect(location.multiplier).toBeGreaterThan(0);
      });
    });

    it('should validate consumption data for all quality tiers', async () => {
      const materials = await loadMaterials();

      materials.materials.forEach(material => {
        expect(material.consumption).toHaveProperty('smart');
        expect(material.consumption).toHaveProperty('premium');
        expect(material.consumption).toHaveProperty('luxury');

        expect(typeof material.consumption.smart).toBe('number');
        expect(typeof material.consumption.premium).toBe('number');
        expect(typeof material.consumption.luxury).toBe('number');

        expect(material.consumption.smart).toBeGreaterThan(0);
        expect(material.consumption.premium).toBeGreaterThan(0);
        expect(material.consumption.luxury).toBeGreaterThan(0);
      });
    });

    it('should validate pricing data for all locations and quality tiers', async () => {
      const materials = await loadMaterials();

      materials.materials.forEach(material => {
        materials.locations.forEach(location => {
          expect(material.pricing).toHaveProperty(location.id);
          
          const locationPricing = material.pricing[location.id];
          expect(locationPricing).toHaveProperty('smart');
          expect(locationPricing).toHaveProperty('premium');
          expect(locationPricing).toHaveProperty('luxury');

          expect(typeof locationPricing.smart).toBe('number');
          expect(typeof locationPricing.premium).toBe('number');
          expect(typeof locationPricing.luxury).toBe('number');

          expect(locationPricing.smart).toBeGreaterThan(0);
          expect(locationPricing.premium).toBeGreaterThan(0);
          expect(locationPricing.luxury).toBeGreaterThan(0);
        });
      });
    });

    it('should validate wastage factors are reasonable', async () => {
      const materials = await loadMaterials();

      materials.materials.forEach(material => {
        expect(material.wastage).toBeGreaterThan(0);
        expect(material.wastage).toBeLessThan(0.5); // Max 50% wastage
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle corrupted material data', async () => {
      // Mock corrupted data
      vi.doMock('../../../data/materials/core-materials.json', () => ({
        default: {
          materials: [{ id: 'invalid' }], // Missing required fields
        },
      }));

      await expect(loadMaterials()).rejects.toThrow();
    });

    it('should handle missing material data file', async () => {
      vi.doMock('../../../data/materials/core-materials.json', () => {
        throw new Error('Module not found');
      });

      await expect(loadMaterials()).rejects.toThrow();
    });

    it('should handle empty material data', async () => {
      vi.doMock('../../../data/materials/core-materials.json', () => ({
        default: {
          materials: [],
          locations: [],
          qualityTiers: [],
          categories: [],
        },
      }));

      const materials = await loadMaterials();
      expect(materials.materials).toHaveLength(0);
      expect(materials.locations).toHaveLength(0);
    });
  });
});