# Performance Optimization Guide

## Overview

This guide provides comprehensive documentation for the React component performance optimization system implemented in the Nirmaan AI Construction Calculator project. The system includes memory profiling, performance monitoring, budget tracking, and automated optimization recommendations.

## Architecture

### Core Components

1. **React Profiler** (`/src/lib/performance/react-profiler.ts`)
   - Component render time tracking
   - Memory usage monitoring
   - Performance budget enforcement
   - Automatic optimization recommendations

2. **Memory Optimizer** (`/src/lib/performance/memory-optimizer.ts`)
   - Memory leak detection
   - Object pooling
   - Weak reference management
   - Memory usage optimization

3. **Performance Budget Monitor** (`/src/lib/performance/performance-budget.ts`)
   - Budget violation tracking
   - Performance threshold enforcement
   - Multi-metric monitoring
   - Severity-based alerting

4. **Performance Recommendations Engine** (`/src/lib/performance/performance-recommendations.ts`)
   - Automated analysis
   - Optimization suggestions
   - Implementation guidance
   - Impact assessment

## Features

### React Component Optimization

#### React.memo Implementation
```typescript
const OptimizedComponent = React.memo(({ data, callback }) => {
  const memoizedValue = useMemo(() => {
    return expensiveCalculation(data);
  }, [data]);

  const memoizedCallback = useCallback(() => {
    callback(memoizedValue);
  }, [callback, memoizedValue]);

  return <div>{memoizedValue}</div>;
});
```

#### useMemo for Expensive Calculations
```typescript
const ExpensiveComponent = ({ items, filter }) => {
  const filteredItems = useMemo(() => {
    return items.filter(item => item.category === filter);
  }, [items, filter]);

  return (
    <div>
      {filteredItems.map(item => (
        <Item key={item.id} {...item} />
      ))}
    </div>
  );
};
```

#### useCallback for Event Handlers
```typescript
const InteractiveComponent = ({ onUpdate }) => {
  const [value, setValue] = useState('');

  const handleChange = useCallback((e) => {
    setValue(e.target.value);
  }, []);

  const handleSubmit = useCallback(() => {
    onUpdate(value);
  }, [onUpdate, value]);

  return (
    <form onSubmit={handleSubmit}>
      <input value={value} onChange={handleChange} />
    </form>
  );
};
```

### Memory Management

#### Memory Leak Detection
```typescript
import { useMemoryOptimization } from '@/lib/performance/memory-optimizer';

const ComponentWithMemoryMonitoring = () => {
  const { recordMemory } = useMemoryOptimization('ComponentName');

  useEffect(() => {
    recordMemory();
    
    const interval = setInterval(() => {
      recordMemory();
    }, 1000);

    return () => clearInterval(interval);
  }, [recordMemory]);

  return <div>Component content</div>;
};
```

#### Object Pooling
```typescript
import { useObjectPool } from '@/lib/performance/memory-optimizer';

const ComponentWithObjectPool = () => {
  const { getObject, returnObject } = useObjectPool('MyPool', () => ({
    id: Math.random(),
    data: new Array(1000).fill(0),
  }));

  const handleOperation = () => {
    const obj = getObject();
    // Use object...
    returnObject(obj);
  };

  return <button onClick={handleOperation}>Use Pooled Object</button>;
};
```

### Performance Budget Monitoring

#### Component Budget Configuration
```typescript
import { usePerformanceBudget } from '@/lib/performance/performance-budget';

const MonitoredComponent = () => {
  const { budget, violations, updateBudget, recordMetric } = usePerformanceBudget(
    'MonitoredComponent',
    'calculator'
  );

  // Custom budget
  updateBudget({
    maxRenderTime: 30,
    maxMemoryUsage: 25 * 1024 * 1024, // 25MB
  });

  const handleExpensiveOperation = async () => {
    const start = performance.now();
    await expensiveOperation();
    const duration = performance.now() - start;
    
    recordMetric('operation', 'time', duration);
  };

  return (
    <div>
      <div>Budget violations: {violations.length}</div>
      <button onClick={handleExpensiveOperation}>
        Expensive Operation
      </button>
    </div>
  );
};
```

#### Render Time Monitoring
```typescript
import { useRenderTimeMonitoring } from '@/lib/performance/performance-budget';

const RenderMonitoredComponent = () => {
  const { renderCount } = useRenderTimeMonitoring('RenderMonitoredComponent');

  return (
    <div>
      <div>Render count: {renderCount}</div>
      <div>Component content</div>
    </div>
  );
};
```

### Performance Profiling

#### Using the Profiler Wrapper
```typescript
import { ProfilerWrapper } from '@/lib/performance/react-profiler';

const ProfiledComponent = () => {
  return (
    <ProfilerWrapper id="ProfiledComponent">
      <div>Component content</div>
    </ProfilerWrapper>
  );
};
```

#### Performance Monitoring Hook
```typescript
import { usePerformanceMonitoring } from '@/lib/performance/react-profiler';

const MonitoredComponent = () => {
  const { metrics, profile } = usePerformanceMonitoring('MonitoredComponent');

  return (
    <div>
      {metrics && (
        <div>
          <div>Render count: {metrics.renderCount}</div>
          <div>Average render time: {metrics.averageRenderTime.toFixed(2)}ms</div>
          <div>Memory usage: {(metrics.memorySizeEstimate / 1024 / 1024).toFixed(2)}MB</div>
        </div>
      )}
      <div>Component content</div>
    </div>
  );
};
```

## Performance Dashboard

### Accessing the Dashboard
The performance dashboard is available at `/performance-dashboard` and provides:

- Real-time component performance metrics
- Memory usage tracking
- Budget violation monitoring
- Performance recommendations
- Historical analysis

### Dashboard Features

1. **Overview Tab**
   - Component render performance chart
   - Memory leak distribution
   - Overall performance score

2. **Components Tab**
   - Detailed component metrics
   - Optimization status
   - Specific recommendations

3. **Memory Tab**
   - Memory leak detection
   - Growth pattern analysis
   - Severity classification

4. **Timeline Tab**
   - Render time trends
   - Memory usage over time
   - Performance degradation tracking

## Best Practices

### Component Optimization

1. **Use React.memo wisely**
   ```typescript
   // Good: Custom comparison for complex props
   const MyComponent = React.memo(({ data }) => {
     return <div>{data.value}</div>;
   }, (prevProps, nextProps) => {
     return prevProps.data.value === nextProps.data.value;
   });
   ```

2. **Optimize expensive calculations**
   ```typescript
   // Good: Memoize expensive operations
   const ExpensiveComponent = ({ items, filter }) => {
     const processedItems = useMemo(() => {
       return items.map(item => ({
         ...item,
         processed: expensiveProcessing(item)
       }));
     }, [items]);

     return <div>{processedItems.length} items</div>;
   };
   ```

3. **Minimize re-renders**
   ```typescript
   // Good: Stable callback references
   const ParentComponent = ({ data }) => {
     const [count, setCount] = useState(0);
     
     const handleIncrement = useCallback(() => {
       setCount(c => c + 1);
     }, []);

     return <ChildComponent onIncrement={handleIncrement} />;
   };
   ```

### Memory Management

1. **Clean up effects**
   ```typescript
   useEffect(() => {
     const subscription = subscribe();
     const timer = setInterval(() => {}, 1000);
     
     return () => {
       subscription.unsubscribe();
       clearInterval(timer);
     };
   }, []);
   ```

2. **Use object pooling for frequent allocations**
   ```typescript
   const { getObject, returnObject } = useObjectPool('calculations', () => ({
     result: 0,
     intermediate: new Array(1000),
   }));
   ```

3. **Monitor memory usage**
   ```typescript
   const { recordMemory } = useMemoryOptimization('ComponentName');
   
   useEffect(() => {
     recordMemory();
   });
   ```

### Performance Budget

1. **Set realistic budgets**
   ```typescript
   const { updateBudget } = usePerformanceBudget('Component', 'form');
   
   updateBudget({
     maxRenderTime: 16, // 60fps
     maxMemoryUsage: 10 * 1024 * 1024, // 10MB
   });
   ```

2. **Monitor violations**
   ```typescript
   const { violations } = usePerformanceBudget('Component');
   
   if (violations.length > 0) {
     console.warn('Performance budget violations:', violations);
   }
   ```

## Performance Monitoring Integration

### Automatic Monitoring
The system automatically monitors:
- Component render times
- Memory usage patterns
- Budget violations
- Performance degradation

### Manual Monitoring
```typescript
import { reactProfiler } from '@/lib/performance/react-profiler';
import { memoryOptimizer } from '@/lib/performance/memory-optimizer';

// Start monitoring
reactProfiler.enableProfiling();
memoryOptimizer.startMonitoring();

// Get performance data
const summary = reactProfiler.getPerformanceSummary();
const memoryStatus = memoryOptimizer.getMemoryStatus();

// Generate recommendations
const analysis = performanceRecommendationsEngine.generateRecommendations();
```

## Testing

### Performance Tests
```typescript
import { describe, it, expect } from 'vitest';
import { reactProfiler } from '@/lib/performance/react-profiler';

describe('Component Performance', () => {
  it('should render within budget', () => {
    const componentId = 'TestComponent';
    
    reactProfiler.onRenderCallback(
      componentId,
      'update',
      15, // Within 16ms budget
      15,
      1000,
      1015,
      new Set()
    );

    const profile = reactProfiler.getComponentProfile(componentId);
    expect(profile?.violations.length).toBe(0);
  });
});
```

### Memory Tests
```typescript
import { memoryOptimizer } from '@/lib/performance/memory-optimizer';

describe('Memory Management', () => {
  it('should detect memory leaks', () => {
    const componentId = 'TestComponent';
    
    memoryOptimizer.registerComponent(componentId);
    
    // Simulate memory growth
    for (let i = 0; i < 10; i++) {
      memoryOptimizer.recordComponentMemory(componentId);
    }
    
    const leakReport = memoryOptimizer.getMemoryLeakReport();
    expect(leakReport.totalLeaks).toBe(0);
  });
});
```

## Troubleshooting

### Common Issues

1. **High render times**
   - Check for unnecessary re-renders
   - Implement React.memo
   - Use useMemo for expensive calculations

2. **Memory leaks**
   - Ensure proper cleanup in useEffect
   - Remove event listeners
   - Clear timers and intervals

3. **Budget violations**
   - Review component complexity
   - Implement lazy loading
   - Use virtualization for large lists

### Performance Debugging

1. **Enable profiling**
   ```typescript
   reactProfiler.enableProfiling();
   ```

2. **Monitor specific components**
   ```typescript
   const profile = reactProfiler.getComponentProfile('ComponentName');
   console.log(profile);
   ```

3. **Generate recommendations**
   ```typescript
   const analysis = performanceRecommendationsEngine.generateRecommendations();
   console.log(analysis.recommendations);
   ```

## Configuration

### Environment Variables
```env
NODE_ENV=development # Enable detailed logging
REACT_APP_PERFORMANCE_MONITORING=true # Enable monitoring
REACT_APP_MEMORY_PROFILING=true # Enable memory profiling
```

### Build Configuration
```typescript
// next.config.js
module.exports = {
  experimental: {
    profiling: true, // Enable React profiling
  },
  webpack: (config) => {
    if (process.env.NODE_ENV === 'development') {
      config.optimization.usedExports = false;
    }
    return config;
  },
};
```

## Deployment Considerations

### Production Optimization
1. Disable detailed profiling in production
2. Use service workers for caching
3. Implement code splitting
4. Enable compression

### Monitoring in Production
```typescript
// Production monitoring
if (process.env.NODE_ENV === 'production') {
  reactProfiler.enableProfiling();
  memoryOptimizer.startMonitoring(5000); // Less frequent
}
```

## Resources

### Documentation
- [React Performance](https://react.dev/learn/render-and-commit)
- [Memory Management](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Memory_Management)
- [Web Performance](https://web.dev/performance/)

### Tools
- React DevTools Profiler
- Chrome DevTools Performance
- Web Vitals Extension

## Support

For issues or questions about the performance optimization system:
1. Check the performance dashboard for insights
2. Review the recommendations engine output
3. Examine component profiles for specific issues
4. Consult the test suite for expected behavior

---

*This guide is part of the Nirmaan AI Construction Calculator performance optimization system. For updates and additional resources, refer to the project documentation.*