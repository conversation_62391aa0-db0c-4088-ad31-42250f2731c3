name: 🔒 Security Scanning

on:
  push:
    branches: [ main, master, develop ]
  pull_request:
    branches: [ main, master, develop ]
  schedule:
    # Run security scans daily at 3 AM UTC
    - cron: '0 3 * * *'
  workflow_dispatch:

env:
  NODE_VERSION: '18'
  SECURITY_SCAN_TIMEOUT: 30

jobs:
  # Dependency Vulnerability Scanning
  dependency-scan:
    name: 🔍 Dependency Vulnerability Scan
    runs-on: ubuntu-latest
    timeout-minutes: 15
    
    outputs:
      critical-vulns: ${{ steps.vulnerability-assessment.outputs.critical-vulns }}
      high-vulns: ${{ steps.vulnerability-assessment.outputs.high-vulns }}
      medium-vulns: ${{ steps.vulnerability-assessment.outputs.medium-vulns }}
      security-score: ${{ steps.vulnerability-assessment.outputs.security-score }}
    
    steps:
      - name: 📦 Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          
      - name: 🔧 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 📥 Install dependencies
        run: npm ci --prefer-offline --no-audit --progress=false
        
      - name: 🔒 NPM Audit (JSON output)
        id: npm-audit
        run: |
          npm audit --audit-level=low --json > npm-audit-report.json || true
          echo "npm-audit-complete=true" >> $GITHUB_OUTPUT
        continue-on-error: true
        
      - name: 🔍 Snyk vulnerability scan
        uses: snyk/actions/node@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: --severity-threshold=low --json-file-output=snyk-report.json
        continue-on-error: true
        
      - name: 🛡️ OSV Scanner
        uses: google/osv-scanner-action@v1.7.4
        with:
          scan-args: |-
            --output=osv-report.json
            --format=json
            ./
        continue-on-error: true
        
      - name: 📊 Vulnerability assessment
        id: vulnerability-assessment
        run: |
          CRITICAL_VULNS=0
          HIGH_VULNS=0
          MEDIUM_VULNS=0
          
          # Parse npm audit report
          if [ -f npm-audit-report.json ]; then
            CRITICAL_VULNS=$(jq '.vulnerabilities | to_entries | map(select(.value.severity == "critical")) | length' npm-audit-report.json || echo "0")
            HIGH_VULNS=$(jq '.vulnerabilities | to_entries | map(select(.value.severity == "high")) | length' npm-audit-report.json || echo "0")
            MEDIUM_VULNS=$(jq '.vulnerabilities | to_entries | map(select(.value.severity == "moderate")) | length' npm-audit-report.json || echo "0")
          fi
          
          # Calculate security score
          SECURITY_SCORE=$((100 - CRITICAL_VULNS * 30 - HIGH_VULNS * 20 - MEDIUM_VULNS * 5))
          if [ $SECURITY_SCORE -lt 0 ]; then
            SECURITY_SCORE=0
          fi
          
          echo "critical-vulns=$CRITICAL_VULNS" >> $GITHUB_OUTPUT
          echo "high-vulns=$HIGH_VULNS" >> $GITHUB_OUTPUT
          echo "medium-vulns=$MEDIUM_VULNS" >> $GITHUB_OUTPUT
          echo "security-score=$SECURITY_SCORE" >> $GITHUB_OUTPUT
          
          echo "Security Assessment:"
          echo "- Critical: $CRITICAL_VULNS"
          echo "- High: $HIGH_VULNS"
          echo "- Medium: $MEDIUM_VULNS"
          echo "- Score: $SECURITY_SCORE/100"
          
      - name: 📤 Upload security reports
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: vulnerability-reports
          path: |
            npm-audit-report.json
            snyk-report.json
            osv-report.json
          retention-days: 30
          
      - name: 🚨 Security alert
        if: steps.vulnerability-assessment.outputs.critical-vulns > 0
        run: |
          echo "🚨 CRITICAL SECURITY ALERT: ${{ steps.vulnerability-assessment.outputs.critical-vulns }} critical vulnerabilities found!"
          exit 1

  # Code Security Analysis
  code-security:
    name: 🛡️ Code Security Analysis
    runs-on: ubuntu-latest
    timeout-minutes: 20
    
    permissions:
      security-events: write
      contents: read
      actions: read
    
    steps:
      - name: 📦 Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          
      - name: 🛡️ Initialize CodeQL
        uses: github/codeql-action/init@v3
        with:
          languages: javascript,typescript
          queries: +security-extended,security-and-quality
          
      - name: 🔧 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 📥 Install dependencies
        run: npm ci --prefer-offline --no-audit --progress=false
        
      - name: 🏗️ Build for analysis
        run: npm run build
        
      - name: 🛡️ Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v3
        with:
          category: "/language:javascript"
          upload: true
          
      - name: 🔍 ESLint security scan
        run: |
          npm run lint -- --format=json --output-file=eslint-security-report.json || true
          # Check for security-related ESLint rules
          if [ -f eslint-security-report.json ]; then
            SECURITY_ISSUES=$(jq '[.[] | .messages[] | select(.ruleId | contains("security"))] | length' eslint-security-report.json || echo "0")
            echo "Security ESLint issues found: $SECURITY_ISSUES"
          fi
          
      - name: 🔒 Semgrep security scan
        uses: returntocorp/semgrep-action@v1
        with:
          config: >-
            p/security-audit
            p/secrets
            p/javascript
            p/typescript
            p/react
          generateSarif: "1"
        continue-on-error: true
        
      - name: 📤 Upload Semgrep results
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: semgrep.sarif
          category: semgrep
        continue-on-error: true

  # Container Security (if applicable)
  container-security:
    name: 🐳 Container Security
    runs-on: ubuntu-latest
    timeout-minutes: 15
    if: github.event_name == 'push' || github.event_name == 'schedule'
    
    steps:
      - name: 📦 Checkout code
        uses: actions/checkout@v4
        
      - name: 🔍 Scan Dockerfile (if exists)
        run: |
          if [ -f Dockerfile ]; then
            echo "Dockerfile found, scanning..."
            docker run --rm -v "$PWD":/app -w /app hadolint/hadolint hadolint Dockerfile
          else
            echo "No Dockerfile found, skipping container security scan"
          fi
        continue-on-error: true
        
      - name: 🔒 Trivy filesystem scan
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'
        continue-on-error: true
        
      - name: 📤 Upload Trivy results
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'
          category: 'trivy'
        continue-on-error: true

  # License Compliance Check
  license-check:
    name: 📋 License Compliance
    runs-on: ubuntu-latest
    timeout-minutes: 10
    
    outputs:
      license-issues: ${{ steps.license-scan.outputs.license-issues }}
      
    steps:
      - name: 📦 Checkout code
        uses: actions/checkout@v4
        
      - name: 🔧 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 📥 Install dependencies
        run: npm ci --prefer-offline --no-audit --progress=false
        
      - name: 📋 License scan
        id: license-scan
        run: |
          npm install -g license-checker
          
          # Generate license report
          license-checker --json > license-report.json || true
          license-checker --csv > license-report.csv || true
          
          # Check for problematic licenses
          PROBLEMATIC_LICENSES=("GPL-2.0" "GPL-3.0" "AGPL-1.0" "AGPL-3.0" "LGPL-2.1" "LGPL-3.0")
          LICENSE_ISSUES=0
          
          for license in "${PROBLEMATIC_LICENSES[@]}"; do
            if license-checker --onlyAllow "MIT;Apache-2.0;BSD-2-Clause;BSD-3-Clause;ISC;0BSD;Unlicense" --excludePrivatePackages > /dev/null 2>&1; then
              echo "✅ No problematic licenses found"
            else
              echo "⚠️ Potentially problematic license found: $license"
              LICENSE_ISSUES=$((LICENSE_ISSUES + 1))
            fi
          done
          
          echo "license-issues=$LICENSE_ISSUES" >> $GITHUB_OUTPUT
          
      - name: 📤 Upload license reports
        uses: actions/upload-artifact@v4
        with:
          name: license-reports
          path: |
            license-report.json
            license-report.csv
          retention-days: 30

  # Security Configuration Check
  security-config:
    name: ⚙️ Security Configuration
    runs-on: ubuntu-latest
    timeout-minutes: 10
    
    steps:
      - name: 📦 Checkout code
        uses: actions/checkout@v4
        
      - name: 🔍 Security headers check
        run: |
          # Check for security-related configurations
          echo "Checking security configurations..."
          
          # Check for security headers in Next.js config
          if [ -f next.config.ts ] || [ -f next.config.js ]; then
            echo "✅ Next.js config found"
            if grep -q "headers" next.config.* 2>/dev/null; then
              echo "✅ Security headers configuration detected"
            else
              echo "⚠️ No security headers configuration found"
            fi
          fi
          
          # Check for environment variable security
          if [ -f .env.example ]; then
            echo "✅ Environment example file found"
          else
            echo "⚠️ No .env.example file found"
          fi
          
          # Check for secrets in repository
          if command -v git-secrets &> /dev/null; then
            git-secrets --scan
          else
            echo "⚠️ git-secrets not available, skipping secret scan"
          fi
          
      - name: 🔒 Content Security Policy check
        run: |
          # Check for CSP configuration
          if grep -r "Content-Security-Policy" . --include="*.ts" --include="*.js" --include="*.tsx" --include="*.jsx" 2>/dev/null; then
            echo "✅ Content Security Policy found"
          else
            echo "⚠️ No Content Security Policy detected"
          fi

  # Security Summary
  security-summary:
    name: 📊 Security Summary
    runs-on: ubuntu-latest
    needs: [dependency-scan, code-security, license-check, security-config]
    if: always()
    
    steps:
      - name: 📊 Generate security summary
        run: |
          echo "# 🔒 Security Scan Summary" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "## 🔍 Vulnerability Assessment" >> $GITHUB_STEP_SUMMARY
          echo "- Critical: ${{ needs.dependency-scan.outputs.critical-vulns || 0 }}" >> $GITHUB_STEP_SUMMARY
          echo "- High: ${{ needs.dependency-scan.outputs.high-vulns || 0 }}" >> $GITHUB_STEP_SUMMARY
          echo "- Medium: ${{ needs.dependency-scan.outputs.medium-vulns || 0 }}" >> $GITHUB_STEP_SUMMARY
          echo "- Security Score: ${{ needs.dependency-scan.outputs.security-score || 'N/A' }}/100" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "## 📋 License Compliance" >> $GITHUB_STEP_SUMMARY
          echo "- License Issues: ${{ needs.license-check.outputs.license-issues || 0 }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "## 🛡️ Security Checks" >> $GITHUB_STEP_SUMMARY
          echo "| Check | Status |" >> $GITHUB_STEP_SUMMARY
          echo "|-------|--------|" >> $GITHUB_STEP_SUMMARY
          echo "| Dependency Scan | ${{ needs.dependency-scan.result == 'success' && '✅' || '❌' }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Code Security | ${{ needs.code-security.result == 'success' && '✅' || '❌' }} |" >> $GITHUB_STEP_SUMMARY
          echo "| License Check | ${{ needs.license-check.result == 'success' && '✅' || '❌' }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Security Config | ${{ needs.security-config.result == 'success' && '✅' || '❌' }} |" >> $GITHUB_STEP_SUMMARY
          
      - name: 🚨 Security alert
        if: |
          needs.dependency-scan.outputs.critical-vulns > 0 ||
          needs.dependency-scan.outputs.high-vulns > 5 ||
          needs.dependency-scan.outputs.security-score < 60
        run: |
          echo "🚨 SECURITY ALERT: Critical security issues detected!"
          echo "Critical vulnerabilities: ${{ needs.dependency-scan.outputs.critical-vulns || 0 }}"
          echo "High vulnerabilities: ${{ needs.dependency-scan.outputs.high-vulns || 0 }}"
          echo "Security score: ${{ needs.dependency-scan.outputs.security-score || 'N/A' }}/100"
          exit 1