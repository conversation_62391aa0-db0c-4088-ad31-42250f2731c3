/**
 * Rate Limiter Implementation
 * Simple in-memory rate limiting for API endpoints
 */

interface RateLimitResult {
  allowed: boolean;
  remaining: number;
  retryAfter: number;
  resetTime: number;
}

interface RateLimitStore {
  [key: string]: {
    count: number;
    resetTime: number;
  };
}

class InMemoryRateLimiter {
  private store: RateLimitStore = {};
  private max: number;
  private window: number;

  constructor(max: number = 100, window: number = 60000) {
    this.max = max;
    this.window = window;
  }

  async check(identifier: string): Promise<RateLimitResult> {
    const now = Date.now();
    const key = identifier;

    // Clean up expired entries
    if (this.store[key] && this.store[key].resetTime <= now) {
      delete this.store[key];
    }

    // Initialize if not exists
    if (!this.store[key]) {
      this.store[key] = {
        count: 1,
        resetTime: now + this.window,
      };

      return {
        allowed: true,
        remaining: this.max - 1,
        retryAfter: 0,
        resetTime: this.store[key].resetTime,
      };
    }

    // Check if limit exceeded
    if (this.store[key].count >= this.max) {
      const retryAfter = Math.ceil((this.store[key].resetTime - now) / 1000);
      
      return {
        allowed: false,
        remaining: 0,
        retryAfter,
        resetTime: this.store[key].resetTime,
      };
    }

    // Increment count
    this.store[key].count += 1;

    return {
      allowed: true,
      remaining: this.max - this.store[key].count,
      retryAfter: 0,
      resetTime: this.store[key].resetTime,
    };
  }

  // Reset all rate limit data
  reset(): void {
    this.store = {};
  }

  // Get current status for an identifier
  getStatus(identifier: string): RateLimitResult | null {
    const now = Date.now();
    const entry = this.store[identifier];

    if (!entry || entry.resetTime <= now) {
      return null;
    }

    return {
      allowed: entry.count < this.max,
      remaining: this.max - entry.count,
      retryAfter: entry.resetTime <= now ? 0 : Math.ceil((entry.resetTime - now) / 1000),
      resetTime: entry.resetTime,
    };
  }
}

// Default rate limiter instance
export const rateLimiter = new InMemoryRateLimiter(100, 60000);

// Export class for custom instances
export { InMemoryRateLimiter };

// Export types
export type { RateLimitResult };