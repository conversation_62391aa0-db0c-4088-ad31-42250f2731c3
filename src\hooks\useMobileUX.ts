"use client";

import { useEffect, useState, useCallback, useRef } from 'react';
import <PERSON><PERSON><PERSON><PERSON>, { TouchEventData, SwipeGesture } from '@/lib/mobile/touch-handler';
import GestureNavigation from '@/lib/mobile/gesture-navigation';
import hapticFeedback from '@/lib/mobile/haptic-feedback';
import pwaManager from '@/lib/mobile/pwa-manager';
import offlineManager from '@/lib/mobile/offline-manager';

interface MobileUXConfig {
  enableHaptic?: boolean;
  enableGestures?: boolean;
  enablePWA?: boolean;
  enableOffline?: boolean;
  touchOptimization?: boolean;
}

interface MobileUXState {
  isTouch: boolean;
  isMobile: boolean;
  isOnline: boolean;
  isPWAInstalled: boolean;
  canInstallPWA: boolean;
  orientation: 'portrait' | 'landscape';
  viewportHeight: number;
  safeAreaInsets: {
    top: number;
    bottom: number;
    left: number;
    right: number;
  };
}

interface MobileUXMethods {
  triggerHaptic: (pattern: string) => void;
  installPWA: () => Promise<boolean>;
  saveOffline: (data: any, type: string) => Promise<string>;
  syncOfflineData: () => Promise<void>;
  enableTouchOptimization: (element: HTMLElement) => void;
  setupGestureNavigation: (element: HTMLElement) => GestureNavigation;
  optimizeForMobile: () => void;
}

type UseMobileUXReturn = {
  state: MobileUXState;
  methods: MobileUXMethods;
};

export const useMobileUX = (config: MobileUXConfig = {}): UseMobileUXReturn => {
  const {
    enableHaptic = true,
    enableGestures = true,
    enablePWA = true,
    enableOffline = true,
    touchOptimization = true
  } = config;

  const [state, setState] = useState<MobileUXState>({
    isTouch: false,
    isMobile: false,
    isOnline: navigator.onLine,
    isPWAInstalled: false,
    canInstallPWA: false,
    orientation: 'portrait',
    viewportHeight: window.innerHeight,
    safeAreaInsets: {
      top: 0,
      bottom: 0,
      left: 0,
      right: 0
    }
  });

  const gestureNavigationRef = useRef<GestureNavigation | null>(null);
  const touchHandlersRef = useRef<Map<HTMLElement, TouchHandler>>(new Map());

  // Detect device capabilities
  const detectDeviceCapabilities = useCallback(() => {
    const isTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
      navigator.userAgent
    );
    
    const orientation = window.innerHeight > window.innerWidth ? 'portrait' : 'landscape';
    
    setState(prev => ({
      ...prev,
      isTouch,
      isMobile,
      orientation,
      viewportHeight: window.innerHeight
    }));
  }, []);

  // Get safe area insets
  const updateSafeAreaInsets = useCallback(() => {
    const computedStyle = getComputedStyle(document.documentElement);
    
    const safeAreaInsets = {
      top: parseInt(computedStyle.getPropertyValue('env(safe-area-inset-top)') || '0'),
      bottom: parseInt(computedStyle.getPropertyValue('env(safe-area-inset-bottom)') || '0'),
      left: parseInt(computedStyle.getPropertyValue('env(safe-area-inset-left)') || '0'),
      right: parseInt(computedStyle.getPropertyValue('env(safe-area-inset-right)') || '0')
    };
    
    setState(prev => ({ ...prev, safeAreaInsets }));
  }, []);

  // Handle viewport height changes (for mobile keyboards)
  const handleViewportChange = useCallback(() => {
    setState(prev => ({
      ...prev,
      viewportHeight: window.innerHeight
    }));
  }, []);

  // Handle orientation changes
  const handleOrientationChange = useCallback(() => {
    setTimeout(() => {
      const orientation = window.innerHeight > window.innerWidth ? 'portrait' : 'landscape';
      setState(prev => ({
        ...prev,
        orientation,
        viewportHeight: window.innerHeight
      }));
    }, 100); // Delay to ensure accurate measurements
  }, []);

  // PWA event handlers
  const handlePWAEvents = useCallback(() => {
    if (!enablePWA) return;

    pwaManager.on('installPromptAvailable', () => {
      setState(prev => ({ ...prev, canInstallPWA: true }));
    });

    pwaManager.on('installed', () => {
      setState(prev => ({ ...prev, isPWAInstalled: true, canInstallPWA: false }));
    });
  }, [enablePWA]);

  // Offline event handlers
  const handleOfflineEvents = useCallback(() => {
    if (!enableOffline) return;

    const handleOnline = () => setState(prev => ({ ...prev, isOnline: true }));
    const handleOffline = () => setState(prev => ({ ...prev, isOnline: false }));

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [enableOffline]);

  // Initialize mobile optimizations
  const initializeMobileOptimizations = useCallback(() => {
    if (!touchOptimization) return;

    // Prevent iOS bounce scroll
    document.body.style.overscrollBehavior = 'none';
    
    // Optimize touch scrolling
    document.body.style.webkitOverflowScrolling = 'touch';
    
    // Prevent zoom on input focus (iOS)
    const viewport = document.querySelector('meta[name="viewport"]');
    if (viewport && state.isMobile) {
      viewport.setAttribute('content', 
        'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no'
      );
    }
    
    // Add mobile-specific CSS classes
    document.documentElement.classList.add(
      state.isTouch ? 'touch-device' : 'no-touch',
      state.isMobile ? 'mobile-device' : 'desktop-device'
    );
    
    // Handle safe area support
    updateSafeAreaInsets();
  }, [touchOptimization, state.isMobile, state.isTouch, updateSafeAreaInsets]);

  // Methods object
  const methods: MobileUXMethods = {
    triggerHaptic: useCallback((pattern: string) => {
      if (enableHaptic) {
        hapticFeedback.trigger(pattern);
      }
    }, [enableHaptic]),

    installPWA: useCallback(async () => {
      if (enablePWA) {
        return await pwaManager.promptInstall();
      }
      return false;
    }, [enablePWA]),

    saveOffline: useCallback(async (data: any, type: string) => {
      if (enableOffline) {
        return await offlineManager.saveData(type as any, data);
      }
      return '';
    }, [enableOffline]),

    syncOfflineData: useCallback(async () => {
      if (enableOffline) {
        await offlineManager.syncPendingData();
      }
    }, [enableOffline]),

    enableTouchOptimization: useCallback((element: HTMLElement) => {
      if (!touchOptimization) return;

      // Create touch handler if it doesn't exist
      if (!touchHandlersRef.current.has(element)) {
        const touchHandler = new TouchHandler(element, {
          enableHaptic,
          minTouchTarget: 44
        });
        touchHandlersRef.current.set(element, touchHandler);
      }

      // Apply touch-friendly styles
      element.style.minHeight = '44px';
      element.style.minWidth = '44px';
      element.style.touchAction = 'manipulation';
      element.classList.add('touch-optimized');
    }, [touchOptimization, enableHaptic]),

    setupGestureNavigation: useCallback((element: HTMLElement) => {
      if (!enableGestures) {
        throw new Error('Gestures not enabled');
      }

      if (gestureNavigationRef.current) {
        gestureNavigationRef.current.destroy();
      }

      const gestureNav = new GestureNavigation(element, {
        enableSwipeBack: true,
        enableSwipeForward: true,
        enablePullToRefresh: true
      });

      gestureNavigationRef.current = gestureNav;
      return gestureNav;
    }, [enableGestures]),

    optimizeForMobile: useCallback(() => {
      // Auto-enhance form inputs
      const inputs = document.querySelectorAll('input, textarea, select');
      inputs.forEach(input => {
        const htmlInput = input as HTMLElement;
        methods.enableTouchOptimization(htmlInput);
      });

      // Auto-enhance buttons
      const buttons = document.querySelectorAll('button, [role="button"]');
      buttons.forEach(button => {
        const htmlButton = button as HTMLElement;
        methods.enableTouchOptimization(htmlButton);
        
        if (enableHaptic) {
          hapticFeedback.enhanceElement(htmlButton);
        }
      });

      // Setup gesture navigation for main container
      const mainContainer = document.querySelector('main') || document.body;
      if (enableGestures && mainContainer) {
        methods.setupGestureNavigation(mainContainer as HTMLElement);
      }
    }, [enableHaptic, enableGestures])
  };

  // Effect to initialize everything
  useEffect(() => {
    detectDeviceCapabilities();
    handlePWAEvents();
    const cleanupOffline = handleOfflineEvents();
    
    // Event listeners
    window.addEventListener('resize', handleViewportChange);
    window.addEventListener('orientationchange', handleOrientationChange);
    
    // Initial setup
    setTimeout(() => {
      initializeMobileOptimizations();
    }, 100);

    return () => {
      window.removeEventListener('resize', handleViewportChange);
      window.removeEventListener('orientationchange', handleOrientationChange);
      cleanupOffline?.();
      
      // Cleanup touch handlers
      touchHandlersRef.current.forEach(handler => handler.destroy());
      touchHandlersRef.current.clear();
      
      // Cleanup gesture navigation
      if (gestureNavigationRef.current) {
        gestureNavigationRef.current.destroy();
      }
    };
  }, []);

  // Update PWA state
  useEffect(() => {
    if (enablePWA) {
      setState(prev => ({
        ...prev,
        isPWAInstalled: pwaManager.isAppInstalled(),
        canInstallPWA: pwaManager.isInstallPromptAvailable()
      }));
    }
  }, [enablePWA]);

  return {
    state,
    methods
  };
};

export type { MobileUXConfig, MobileUXState, MobileUXMethods, UseMobileUXReturn };