/**
 * Enhanced UI Components Index
 * Exports all enhanced MVP components with their types
 */

// Enhanced Button
export {
  EnhancedButton,
  type ButtonProps,
  type ButtonVariant,
  type ButtonSize,
} from "./enhanced-button";

// Enhanced Input
export {
  EnhancedInput,
  type InputProps,
  type InputVariant,
  type InputSize,
} from "./enhanced-input";

// Enhanced Card
export {
  EnhancedCard,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
  type CardProps,
  type CardVariant,
  type CardSize,
} from "./enhanced-card";

// Enhanced Select
export {
  EnhancedSelect,
  type SelectProps,
  type SelectOption,
  type SelectVariant,
  type SelectSize,
} from "./enhanced-select";

// Enhanced Progress
export {
  EnhancedProgress,
  CircularProgress,
  type ProgressProps,
  type CircularProgressProps,
  type ProgressVariant,
  type ProgressSize,
} from "./enhanced-progress";

// Enhanced Modal
export {
  EnhancedModal,
  ConfirmModal,
  ModalTrigger,
  ModalClose,
  type ModalProps,
  type ModalVariant,
  type ModalSize,
  type ConfirmModalProps,
} from "./enhanced-modal";

// Component library info
export const ENHANCED_COMPONENT_VERSION = "1.0.0";
export const ENHANCED_COMPONENTS = [
  "EnhancedButton",
  "EnhancedInput", 
  "EnhancedCard",
  "EnhancedSelect",
  "EnhancedProgress",
  "EnhancedModal",
] as const;

export type EnhancedComponentName = typeof ENHANCED_COMPONENTS[number];