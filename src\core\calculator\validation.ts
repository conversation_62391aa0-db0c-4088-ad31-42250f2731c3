/**
 * Input validation for calculation engine
 * Ensures data integrity and prevents calculation errors
 */

import type { CalculationInput, ValidationError } from './types';
import { VALIDATION_LIMITS, REGIONAL_MULTIPLIERS } from './constants';

/**
 * Validate calculation input and return array of errors
 */
export function validateInput(input: CalculationInput): ValidationError[] {
  const errors: ValidationError[] = [];

  // Validate built-up area
  if (!input.builtUpArea || input.builtUpArea < VALIDATION_LIMITS.minArea) {
    errors.push({
      field: 'builtUpArea',
      message: `Built-up area must be at least ${VALIDATION_LIMITS.minArea} sqft`,
      code: 'MIN_AREA_REQUIRED',
    });
  }

  if (input.builtUpArea > VALIDATION_LIMITS.maxArea) {
    errors.push({
      field: 'builtUpArea',
      message: `Built-up area cannot exceed ${VALIDATION_LIMITS.maxArea} sqft`,
      code: 'MAX_AREA_EXCEEDED',
    });
  }

  // Validate floors
  if (input.floors < VALIDATION_LIMITS.minFloors) {
    errors.push({
      field: 'floors',
      message: 'Floors cannot be negative',
      code: 'INVALID_FLOORS',
    });
  }

  if (input.floors > VALIDATION_LIMITS.maxFloors) {
    errors.push({
      field: 'floors',
      message: `Maximum ${VALIDATION_LIMITS.maxFloors} floors allowed`,
      code: 'MAX_FLOORS_EXCEEDED',
    });
  }

  // Validate quality tier
  const validQualityTiers = ['smart', 'premium', 'luxury'];
  if (!validQualityTiers.includes(input.qualityTier)) {
    errors.push({
      field: 'qualityTier',
      message: 'Quality tier must be smart, premium, or luxury',
      code: 'INVALID_QUALITY_TIER',
    });
  }

  // Validate location
  if (!input.location || input.location.trim().length === 0) {
    errors.push({
      field: 'location',
      message: 'Location is required',
      code: 'LOCATION_REQUIRED',
    });
  }

  // Validate plot area (if provided)
  if (input.plotArea && input.plotArea < input.builtUpArea) {
    errors.push({
      field: 'plotArea',
      message: 'Plot area cannot be smaller than built-up area',
      code: 'PLOT_AREA_TOO_SMALL',
    });
  }

  // Validate special features (if provided)
  if (input.specialFeatures) {
    input.specialFeatures.forEach((feature, index) => {
      if (!feature.name || feature.name.trim().length === 0) {
        errors.push({
          field: `specialFeatures[${index}].name`,
          message: 'Special feature name is required',
          code: 'FEATURE_NAME_REQUIRED',
        });
      }

      if (feature.cost < 0) {
        errors.push({
          field: `specialFeatures[${index}].cost`,
          message: 'Special feature cost cannot be negative',
          code: 'FEATURE_COST_INVALID',
        });
      }
    });
  }

  return errors;
}

/**
 * Sanitize and normalize input data
 */
export function sanitizeInput(input: CalculationInput): CalculationInput {
  return {
    ...input,
    builtUpArea: Math.round(input.builtUpArea),
    floors: Math.floor(input.floors),
    location: input.location.toLowerCase().trim(),
    plotArea: input.plotArea ? Math.round(input.plotArea) : undefined,
    qualityTier: input.qualityTier.toLowerCase() as
      | 'smart'
      | 'premium'
      | 'luxury',
  };
}

/**
 * Check if location is supported
 */
export function isLocationSupported(location: string): boolean {
  const normalizedLocation = location.toLowerCase().trim();
  return Object.keys(REGIONAL_MULTIPLIERS).includes(normalizedLocation);
}

/**
 * Get suggested locations for autocomplete
 */
export function getSuggestedLocations(query: string): string[] {
  const normalizedQuery = query.toLowerCase().trim();
  const locations = Object.keys(REGIONAL_MULTIPLIERS).filter(
    loc => loc !== 'default' && loc.includes(normalizedQuery)
  );

  // Sort by relevance (exact match first, then contains)
  return locations.sort((a, b) => {
    if (a === normalizedQuery) return -1;
    if (b === normalizedQuery) return 1;
    if (a.startsWith(normalizedQuery) && !b.startsWith(normalizedQuery)) {
      return -1;
    }
    if (b.startsWith(normalizedQuery) && !a.startsWith(normalizedQuery)) {
      return 1;
    }
    return a.localeCompare(b);
  });
}

/**
 * Validate cost reasonableness based on market standards
 */
export function validateCostReasonableness(
  totalCost: number,
  builtUpArea: number,
  qualityTier: 'smart' | 'premium' | 'luxury'
): ValidationError[] {
  const errors: ValidationError[] = [];
  const costPerSqft = totalCost / builtUpArea;

  // Define reasonable cost ranges per sqft by quality tier
  const costRanges = {
    smart: { min: 1200, max: 2500 },
    premium: { min: 2000, max: 3500 },
    luxury: { min: 2800, max: 5000 },
  };

  const range = costRanges[qualityTier];

  if (costPerSqft < range.min) {
    errors.push({
      field: 'totalCost',
      message: `Cost per sqft (₹${Math.round(costPerSqft)}) seems unusually low for ${qualityTier} quality. Expected range: ₹${range.min}-${range.max}`,
      code: 'COST_TOO_LOW',
    });
  }

  if (costPerSqft > range.max) {
    errors.push({
      field: 'totalCost',
      message: `Cost per sqft (₹${Math.round(costPerSqft)}) seems unusually high for ${qualityTier} quality. Expected range: ₹${range.min}-${range.max}`,
      code: 'COST_TOO_HIGH',
    });
  }

  return errors;
}

/**
 * Validate material quantities for reasonableness
 */
export function validateMaterialQuantities(
  materials: unknown[],
  builtUpArea: number,
  floors: number
): ValidationError[] {
  const errors: ValidationError[] = [];
  const totalArea = builtUpArea * (floors + 1);

  // Define reasonable consumption ranges
  const reasonableRanges = {
    cement: { min: 0.3, max: 0.5 }, // bags per sqft
    steel: { min: 3.0, max: 5.0 }, // kg per sqft
    bricks: { min: 6, max: 12 }, // pieces per sqft
  };

  materials.forEach(materialItem => {
    const material = materialItem as { category?: string; quantity?: number; unit?: string; [key: string]: unknown };
    if (!material.category || !material.quantity) return;

    const materialType = material.category.toLowerCase();
    const range =
      reasonableRanges[materialType as keyof typeof reasonableRanges];

    if (range) {
      const consumptionPerSqft = material.quantity / totalArea;

      if (consumptionPerSqft < range.min || consumptionPerSqft > range.max) {
        errors.push({
          field: `materials.${materialType}`,
          message: `${material.category} consumption (${consumptionPerSqft.toFixed(2)} ${material.unit || 'unit'}/sqft) is outside normal range (${range.min}-${range.max})`,
          code: 'MATERIAL_CONSUMPTION_UNUSUAL',
        });
      }
    }
  });

  return errors;
}
