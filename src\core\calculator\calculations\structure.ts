/**
 * Structure Cost Calculations
 * Based on Indian construction standards and practices
 */

import type { CalculationInput, CategoryCost, SubCategory } from '../types';
import {
  STRUCTURE_BREAKDOWN,
  COST_BREAKDOWN_PERCENTAGES,
} from '../constants';

export function calculateStructureCost(input: CalculationInput): CategoryCost {
  const { builtUpArea, floors, qualityTier, hasBasement = false } = input;

  // Quality tier base structure rates (per sqft)
  const structureBaseRates = {
    smart: 600, // ₹600/sqft for Smart Choice
    premium: 750, // ₹750/sqft for Premium Selection
    luxury: 900, // ₹900/sqft for Luxury Collection
  };

  // Floor multipliers (higher floors need stronger structure per IS codes)
  const floorMultipliers = {
    0: 1.0, // Ground only
    1: 1.05, // G+1
    2: 1.1, // G+2
    3: 1.15, // G+3
    4: 1.2, // G+4
    5: 1.25, // G+5 and above
  };

  // Get base structure rate and floor multiplier
  const baseStructureRate = structureBaseRates[qualityTier];
  const floorKey = Math.min(floors, 5) as keyof typeof floorMultipliers;
  const floorMultiplier = floorMultipliers[floorKey];

  // Calculate total area including all floors and basement
  let totalArea = builtUpArea * (floors + 1); // +1 for ground floor
  if (hasBasement) {
    totalArea += builtUpArea * 1.3; // Basement is 30% more expensive
  }

  // Apply multipliers to base rate
  const adjustedStructureRate = baseStructureRate * floorMultiplier;

  // Total structure cost
  const totalStructureCost = totalArea * adjustedStructureRate;

  // Calculate sub-categories based on structure breakdown
  const subCategories: SubCategory[] = [
    {
      name: 'Foundation',
      amount: Math.round(totalStructureCost * STRUCTURE_BREAKDOWN.foundation),
      percentage: STRUCTURE_BREAKDOWN.foundation * 100,
      description:
        'Excavation, footing, grade beam, PCC, and foundation concrete as per IS 456',
    },
    {
      name: 'Columns & Beams',
      amount: Math.round(totalStructureCost * STRUCTURE_BREAKDOWN.columns),
      percentage: STRUCTURE_BREAKDOWN.columns * 100,
      description:
        'RCC columns, tie beams, main beams as per IS 13920 (seismic design)',
    },
    {
      name: 'Slabs',
      amount: Math.round(totalStructureCost * STRUCTURE_BREAKDOWN.slabs),
      percentage: STRUCTURE_BREAKDOWN.slabs * 100,
      description: 'Floor slabs, roof slab, staircase slabs as per IS 456',
    },
    {
      name: 'Walls',
      amount: Math.round(totalStructureCost * STRUCTURE_BREAKDOWN.walls),
      percentage: STRUCTURE_BREAKDOWN.walls * 100,
      description: 'Load bearing walls and partition walls as per IS 1905',
    },
    {
      name: 'Staircase',
      amount: Math.round(totalStructureCost * STRUCTURE_BREAKDOWN.staircase),
      percentage: STRUCTURE_BREAKDOWN.staircase * 100,
      description: 'RCC staircase with railings as per IS 456',
    },
  ];

  return {
    amount: Math.round(totalStructureCost),
    percentage: COST_BREAKDOWN_PERCENTAGES.structure * 100,
    subCategories,
  };
}

/**
 * Calculate foundation cost based on soil type and building height
 */
export function calculateFoundationCost(
  area: number,
  floors: number,
  soilType: 'good' | 'medium' | 'poor' = 'medium'
): number {
  const baseFoundationCost = 180; // per sqft

  // Soil type multipliers
  const soilMultipliers = {
    good: 1.0, // Normal black cotton soil
    medium: 1.2, // Medium bearing capacity
    poor: 1.5, // Poor soil, deeper foundation needed
  };

  // Floor multiplier for foundation (more floors = stronger foundation)
  const foundationFloorMultiplier = 1 + floors * 0.1;

  return (
    area *
    baseFoundationCost *
    soilMultipliers[soilType] *
    foundationFloorMultiplier
  );
}

/**
 * Calculate concrete volume and cost
 */
export function calculateConcreteCost(
  area: number,
  floors: number,
  qualityTier: 'smart' | 'premium' | 'luxury'
): { volume: number; cost: number } {
  // Concrete consumption per sqft (including all RCC work)
  const concreteConsumption = {
    smart: 0.125, // 0.125 cum per sqft (M20 grade)
    premium: 0.135, // 0.135 cum per sqft (M25 grade)
    luxury: 0.145, // 0.145 cum per sqft (M30 grade)
  };

  // Concrete rates per cum
  const concreteRates = {
    smart: 4500, // M20 grade
    premium: 5200, // M25 grade
    luxury: 6000, // M30 grade
  };

  const totalArea = area * (floors + 1);
  const volume = totalArea * concreteConsumption[qualityTier];
  const cost = volume * concreteRates[qualityTier];

  return { volume: Math.round(volume * 100) / 100, cost: Math.round(cost) };
}

/**
 * Calculate steel reinforcement cost
 */
export function calculateSteelCost(
  area: number,
  floors: number,
  qualityTier: 'smart' | 'premium' | 'luxury'
): { weight: number; cost: number } {
  // Steel consumption per sqft
  const steelConsumption = {
    smart: 3.8, // kg per sqft (Fe415)
    premium: 4.0, // kg per sqft (Fe500)
    luxury: 4.5, // kg per sqft (Fe500D/Fe550)
  };

  // Steel rates per kg
  const steelRates = {
    smart: 65, // Fe415 grade
    premium: 70, // Fe500 grade
    luxury: 75, // Fe500D/Fe550 grade
  };

  const totalArea = area * (floors + 1);
  const weight = totalArea * steelConsumption[qualityTier];
  const cost = weight * steelRates[qualityTier];

  return { weight: Math.round(weight), cost: Math.round(cost) };
}
