/**
 * Performance Budget Monitoring
 * Comprehensive budget tracking and enforcement
 */

import { useEffect, useRef, useState } from 'react';

// Performance budget configuration
export interface PerformanceBudget {
  // Timing budgets (in milliseconds)
  maxRenderTime: number;
  maxComponentLoadTime: number;
  maxAPIResponseTime: number;
  maxAnimationFrame: number;
  maxInteractionDelay: number;
  
  // Memory budgets (in bytes)
  maxMemoryUsage: number;
  maxMemoryGrowthRate: number;
  maxComponentMemory: number;
  
  // Network budgets (in bytes)
  maxBundleSize: number;
  maxImageSize: number;
  maxTotalAssets: number;
  
  // Rendering budgets
  maxReRenders: number;
  maxDOMNodes: number;
  maxEventListeners: number;
  
  // Quality budgets
  minLighthouseScore: number;
  maxCumulativeLayoutShift: number;
  maxFirstInputDelay: number;
  maxLargestContentfulPaint: number;
}

// Budget violation severity
export enum ViolationSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

// Budget violation details
export interface BudgetViolation {
  id: string;
  metric: string;
  actualValue: number;
  budgetValue: number;
  severity: ViolationSeverity;
  component?: string;
  timestamp: Date;
  description: string;
  recommendations: string[];
}

// Budget monitoring results
export interface BudgetReport {
  violations: BudgetViolation[];
  totalViolations: number;
  severityDistribution: Record<ViolationSeverity, number>;
  budgetCompliance: number; // Percentage
  recommendations: string[];
}

// Default performance budgets
const DEFAULT_BUDGETS: Record<string, PerformanceBudget> = {
  calculator: {
    maxRenderTime: 50,
    maxComponentLoadTime: 1000,
    maxAPIResponseTime: 3000,
    maxAnimationFrame: 16,
    maxInteractionDelay: 100,
    maxMemoryUsage: 100 * 1024 * 1024, // 100MB
    maxMemoryGrowthRate: 1024 * 1024, // 1MB/s
    maxComponentMemory: 50 * 1024 * 1024, // 50MB
    maxBundleSize: 2 * 1024 * 1024, // 2MB
    maxImageSize: 500 * 1024, // 500KB
    maxTotalAssets: 10 * 1024 * 1024, // 10MB
    maxReRenders: 5,
    maxDOMNodes: 1000,
    maxEventListeners: 100,
    minLighthouseScore: 90,
    maxCumulativeLayoutShift: 0.1,
    maxFirstInputDelay: 100,
    maxLargestContentfulPaint: 2500,
  },
  form: {
    maxRenderTime: 16,
    maxComponentLoadTime: 500,
    maxAPIResponseTime: 2000,
    maxAnimationFrame: 16,
    maxInteractionDelay: 50,
    maxMemoryUsage: 25 * 1024 * 1024, // 25MB
    maxMemoryGrowthRate: 512 * 1024, // 512KB/s
    maxComponentMemory: 10 * 1024 * 1024, // 10MB
    maxBundleSize: 1024 * 1024, // 1MB
    maxImageSize: 100 * 1024, // 100KB
    maxTotalAssets: 5 * 1024 * 1024, // 5MB
    maxReRenders: 3,
    maxDOMNodes: 500,
    maxEventListeners: 50,
    minLighthouseScore: 95,
    maxCumulativeLayoutShift: 0.05,
    maxFirstInputDelay: 50,
    maxLargestContentfulPaint: 1500,
  },
  list: {
    maxRenderTime: 32,
    maxComponentLoadTime: 2000,
    maxAPIResponseTime: 5000,
    maxAnimationFrame: 16,
    maxInteractionDelay: 100,
    maxMemoryUsage: 150 * 1024 * 1024, // 150MB
    maxMemoryGrowthRate: 2 * 1024 * 1024, // 2MB/s
    maxComponentMemory: 75 * 1024 * 1024, // 75MB
    maxBundleSize: 3 * 1024 * 1024, // 3MB
    maxImageSize: 200 * 1024, // 200KB
    maxTotalAssets: 15 * 1024 * 1024, // 15MB
    maxReRenders: 8,
    maxDOMNodes: 5000,
    maxEventListeners: 200,
    minLighthouseScore: 85,
    maxCumulativeLayoutShift: 0.15,
    maxFirstInputDelay: 100,
    maxLargestContentfulPaint: 3000,
  },
  default: {
    maxRenderTime: 16,
    maxComponentLoadTime: 1000,
    maxAPIResponseTime: 3000,
    maxAnimationFrame: 16,
    maxInteractionDelay: 100,
    maxMemoryUsage: 50 * 1024 * 1024, // 50MB
    maxMemoryGrowthRate: 1024 * 1024, // 1MB/s
    maxComponentMemory: 25 * 1024 * 1024, // 25MB
    maxBundleSize: 1024 * 1024, // 1MB
    maxImageSize: 200 * 1024, // 200KB
    maxTotalAssets: 5 * 1024 * 1024, // 5MB
    maxReRenders: 5,
    maxDOMNodes: 1000,
    maxEventListeners: 100,
    minLighthouseScore: 90,
    maxCumulativeLayoutShift: 0.1,
    maxFirstInputDelay: 100,
    maxLargestContentfulPaint: 2500,
  },
};

class PerformanceBudgetMonitor {
  private budgets: Map<string, PerformanceBudget> = new Map();
  private violations: BudgetViolation[] = [];
  private metrics: Map<string, any> = new Map();
  private monitoring: boolean = false;
  private observers: Map<string, PerformanceObserver> = new Map();

  constructor() {
    // Initialize default budgets
    Object.entries(DEFAULT_BUDGETS).forEach(([key, budget]) => {
      this.budgets.set(key, budget);
    });
    
    this.initializePerformanceObservers();
  }

  // Initialize performance observers
  private initializePerformanceObservers(): void {
    if (typeof window === 'undefined' || !window.PerformanceObserver) return;

    try {
      // Paint timing observer
      const paintObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          if (entry.entryType === 'paint') {
            this.recordMetric('paint', entry.name, entry.startTime);
          }
        });
      });
      paintObserver.observe({ entryTypes: ['paint'] });
      this.observers.set('paint', paintObserver);

      // Layout shift observer
      const layoutObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry: any) => {
          if (entry.entryType === 'layout-shift' && !entry.hadRecentInput) {
            this.recordMetric('layout-shift', 'cumulative-layout-shift', entry.value);
          }
        });
      });
      layoutObserver.observe({ entryTypes: ['layout-shift'] });
      this.observers.set('layout-shift', layoutObserver);

      // First input delay observer
      const fidObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry: any) => {
          if (entry.entryType === 'first-input') {
            const delay = entry.processingStart - entry.startTime;
            this.recordMetric('first-input', 'delay', delay);
          }
        });
      });
      fidObserver.observe({ entryTypes: ['first-input'] });
      this.observers.set('first-input', fidObserver);

      // Largest contentful paint observer
      const lcpObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          if (entry.entryType === 'largest-contentful-paint') {
            this.recordMetric('largest-contentful-paint', 'time', entry.startTime);
          }
        });
      });
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
      this.observers.set('largest-contentful-paint', lcpObserver);

    } catch (error) {
      console.warn('Failed to initialize performance observers:', error);
    }
  }

  // Set budget for component type
  public setBudget(componentType: string, budget: Partial<PerformanceBudget>): void {
    const existingBudget = this.budgets.get(componentType) || DEFAULT_BUDGETS.default;
    this.budgets.set(componentType, { ...existingBudget, ...budget });
  }

  // Get budget for component type
  public getBudget(componentType: string): PerformanceBudget {
    return this.budgets.get(componentType) || DEFAULT_BUDGETS.default;
  }

  // Record performance metric
  public recordMetric(category: string, metric: string, value: number, componentId?: string): void {
    const key = `${category}.${metric}`;
    const timestamp = Date.now();
    
    this.metrics.set(key, {
      value,
      timestamp,
      componentId,
    });

    // Check for budget violations
    this.checkBudgetViolation(category, metric, value, componentId);
  }

  // Check for budget violation
  private checkBudgetViolation(category: string, metric: string, value: number, componentId?: string): void {
    const componentType = this.getComponentType(componentId);
    const budget = this.getBudget(componentType);
    
    let budgetValue: number | undefined;
    let description: string;
    let recommendations: string[] = [];

    // Map metrics to budget values
    switch (`${category}.${metric}`) {
      case 'render.time':
        budgetValue = budget.maxRenderTime;
        description = 'Render time exceeds budget';
        recommendations = [
          'Use React.memo() for expensive components',
          'Implement useMemo() for expensive calculations',
          'Consider component splitting',
        ];
        break;
      case 'memory.usage':
        budgetValue = budget.maxMemoryUsage;
        description = 'Memory usage exceeds budget';
        recommendations = [
          'Check for memory leaks',
          'Implement object pooling',
          'Use WeakMap/WeakSet for references',
        ];
        break;
      case 'api.response-time':
        budgetValue = budget.maxAPIResponseTime;
        description = 'API response time exceeds budget';
        recommendations = [
          'Implement request caching',
          'Optimize API queries',
          'Add loading states',
        ];
        break;
      case 'layout-shift.cumulative-layout-shift':
        budgetValue = budget.maxCumulativeLayoutShift;
        description = 'Cumulative layout shift exceeds budget';
        recommendations = [
          'Set dimensions for images and videos',
          'Reserve space for dynamic content',
          'Avoid inserting content above existing content',
        ];
        break;
      case 'first-input.delay':
        budgetValue = budget.maxFirstInputDelay;
        description = 'First input delay exceeds budget';
        recommendations = [
          'Break up long tasks',
          'Optimize JavaScript execution',
          'Use web workers for heavy computations',
        ];
        break;
      case 'largest-contentful-paint.time':
        budgetValue = budget.maxLargestContentfulPaint;
        description = 'Largest contentful paint exceeds budget';
        recommendations = [
          'Optimize image loading',
          'Implement lazy loading',
          'Reduce render-blocking resources',
        ];
        break;
      default:
        return; // Unknown metric
    }

    if (budgetValue !== undefined && value > budgetValue) {
      const severity = this.calculateViolationSeverity(value, budgetValue);
      
      const violation: BudgetViolation = {
        id: `${category}-${metric}-${Date.now()}`,
        metric: `${category}.${metric}`,
        actualValue: value,
        budgetValue,
        severity,
        component: componentId,
        timestamp: new Date(),
        description,
        recommendations,
      };

      this.violations.push(violation);
      
      // Keep only last 100 violations
      if (this.violations.length > 100) {
        this.violations.shift();
      }

      // Log violation
      console.warn(`Budget violation: ${description}`, violation);
    }
  }

  // Calculate violation severity
  private calculateViolationSeverity(actual: number, budget: number): ViolationSeverity {
    const ratio = actual / budget;
    
    if (ratio > 3) return ViolationSeverity.CRITICAL;
    if (ratio > 2) return ViolationSeverity.HIGH;
    if (ratio > 1.5) return ViolationSeverity.MEDIUM;
    return ViolationSeverity.LOW;
  }

  // Get component type from ID
  private getComponentType(componentId?: string): string {
    if (!componentId) return 'default';
    
    const id = componentId.toLowerCase();
    if (id.includes('calculator')) return 'calculator';
    if (id.includes('form')) return 'form';
    if (id.includes('list')) return 'list';
    return 'default';
  }

  // Get budget report
  public getBudgetReport(): BudgetReport {
    const severityDistribution = this.violations.reduce((acc, violation) => {
      acc[violation.severity] = (acc[violation.severity] || 0) + 1;
      return acc;
    }, {} as Record<ViolationSeverity, number>);

    const totalMetrics = this.metrics.size;
    const totalViolations = this.violations.length;
    const budgetCompliance = totalMetrics > 0 ? ((totalMetrics - totalViolations) / totalMetrics) * 100 : 100;

    const recommendations = this.generateRecommendations();

    return {
      violations: this.violations,
      totalViolations,
      severityDistribution,
      budgetCompliance,
      recommendations,
    };
  }

  // Generate recommendations based on violations
  private generateRecommendations(): string[] {
    const recommendations: string[] = [];
    const violationsByMetric = this.violations.reduce((acc, violation) => {
      acc[violation.metric] = (acc[violation.metric] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Sort by frequency
    const sortedViolations = Object.entries(violationsByMetric)
      .sort(([, a], [, b]) => b - a);

    sortedViolations.forEach(([metric, count]) => {
      if (count > 3) {
        recommendations.push(`High frequency violations in ${metric}: Consider implementing specific optimizations`);
      }
    });

    // Memory-specific recommendations
    const memoryViolations = this.violations.filter(v => v.metric.includes('memory'));
    if (memoryViolations.length > 0) {
      recommendations.push('Memory issues detected: Implement memory monitoring and cleanup');
    }

    // Performance-specific recommendations
    const performanceViolations = this.violations.filter(v => v.metric.includes('render'));
    if (performanceViolations.length > 0) {
      recommendations.push('Render performance issues: Consider React optimization techniques');
    }

    return recommendations;
  }

  // Clear violations
  public clearViolations(): void {
    this.violations = [];
    this.metrics.clear();
  }

  // Get violations by component
  public getViolationsByComponent(componentId: string): BudgetViolation[] {
    return this.violations.filter(v => v.component === componentId);
  }

  // Get violations by severity
  public getViolationsBySeverity(severity: ViolationSeverity): BudgetViolation[] {
    return this.violations.filter(v => v.severity === severity);
  }

  // Start monitoring
  public startMonitoring(): void {
    this.monitoring = true;
  }

  // Stop monitoring
  public stopMonitoring(): void {
    this.monitoring = false;
    this.observers.forEach(observer => observer.disconnect());
    this.observers.clear();
  }

  // Check if monitoring is active
  public isMonitoring(): boolean {
    return this.monitoring;
  }
}

// Create singleton instance
export const performanceBudgetMonitor = new PerformanceBudgetMonitor();

// React hook for performance budget monitoring
export const usePerformanceBudget = (componentId: string, componentType?: string) => {
  const [violations, setViolations] = useState<BudgetViolation[]>([]);
  const [budget, setBudget] = useState<PerformanceBudget>(() => 
    performanceBudgetMonitor.getBudget(componentType || 'default')
  );

  // Update budget
  const updateBudget = (newBudget: Partial<PerformanceBudget>) => {
    const type = componentType || 'default';
    performanceBudgetMonitor.setBudget(type, newBudget);
    setBudget(performanceBudgetMonitor.getBudget(type));
  };

  // Record metric
  const recordMetric = (category: string, metric: string, value: number) => {
    performanceBudgetMonitor.recordMetric(category, metric, value, componentId);
  };

  // Update violations
  useEffect(() => {
    const interval = setInterval(() => {
      const componentViolations = performanceBudgetMonitor.getViolationsByComponent(componentId);
      setViolations(componentViolations);
    }, 1000);

    return () => clearInterval(interval);
  }, [componentId]);

  return {
    budget,
    violations,
    updateBudget,
    recordMetric,
  };
};

// React hook for render time monitoring
export const useRenderTimeMonitoring = (componentId: string) => {
  const renderStartTime = useRef<number>(0);
  const renderCount = useRef<number>(0);

  useEffect(() => {
    renderStartTime.current = performance.now();
    renderCount.current++;

    return () => {
      const renderTime = performance.now() - renderStartTime.current;
      performanceBudgetMonitor.recordMetric('render', 'time', renderTime, componentId);
      
      if (renderCount.current > 10) {
        performanceBudgetMonitor.recordMetric('render', 'count', renderCount.current, componentId);
      }
    };
  });

  return {
    renderCount: renderCount.current,
  };
};

// React hook for API response time monitoring
export const useAPIResponseTimeMonitoring = () => {
  const measureAPICall = async <T>(
    apiCall: () => Promise<T>,
    endpoint: string,
    componentId?: string
  ): Promise<T> => {
    const startTime = performance.now();
    
    try {
      const result = await apiCall();
      const responseTime = performance.now() - startTime;
      
      performanceBudgetMonitor.recordMetric('api', 'response-time', responseTime, componentId);
      
      return result;
    } catch (error) {
      const responseTime = performance.now() - startTime;
      performanceBudgetMonitor.recordMetric('api', 'error-time', responseTime, componentId);
      throw error;
    }
  };

  return { measureAPICall };
};

export default performanceBudgetMonitor;