/**
 * Enhanced Progress Component for MVP Design System
 * Professional progress bars with animations and variants
 */

import * as React from "react";
import * as ProgressPrimitive from "@radix-ui/react-progress";
import { motion, type Variants } from "framer-motion";
import { cn } from "@/lib/utils";

type ProgressVariant = "default" | "success" | "warning" | "error" | "gradient";
type ProgressSize = "sm" | "md" | "lg" | "xl";

interface ProgressProps {
  value?: number;
  max?: number;
  variant?: ProgressVariant;
  size?: ProgressSize;
  label?: string;
  showValue?: boolean;
  showPercentage?: boolean;
  animated?: boolean;
  striped?: boolean;
  indeterminate?: boolean;
  className?: string;
}

// Animation variants
const barVariants: Variants = {
  initial: { width: 0 },
  animate: (value: number) => ({
    width: `${value}%`,
    transition: {
      duration: 0.8,
      ease: "easeOut",
    },
  }),
};

const pulseVariants: Variants = {
  animate: {
    opacity: [0.6, 1, 0.6],
    transition: {
      duration: 2,
      repeat: Infinity,
      ease: "easeInOut",
    },
  },
};

const indeterminateVariants: Variants = {
  animate: {
    x: ["0%", "100%"],
    transition: {
      duration: 1.5,
      repeat: Infinity,
      ease: "easeInOut",
    },
  },
};

// Base styles
const rootClasses = cn(
  "relative overflow-hidden rounded-full bg-secondary-200"
);

// Variant styles
const variantClasses: Record<ProgressVariant, string> = {
  default: "bg-primary-500",
  success: "bg-green-500",
  warning: "bg-yellow-500",
  error: "bg-red-500",
  gradient: "bg-gradient-to-r from-primary-400 via-primary-500 to-primary-600",
};

// Size styles
const sizeClasses: Record<ProgressSize, string> = {
  sm: "h-2",
  md: "h-3",
  lg: "h-4",
  xl: "h-6",
};

const labelSizeClasses: Record<ProgressSize, string> = {
  sm: "text-xs",
  md: "text-sm",
  lg: "text-base",
  xl: "text-lg",
};

export const EnhancedProgress = React.forwardRef<
  React.ElementRef<typeof ProgressPrimitive.Root>,
  ProgressProps
>(
  (
    {
      value = 0,
      max = 100,
      variant = "default",
      size = "md",
      label,
      showValue = false,
      showPercentage = false,
      animated = true,
      striped = false,
      indeterminate = false,
      className,
      ...props
    },
    ref
  ) => {
    // Calculate percentage
    const percentage = Math.min(Math.max((value / max) * 100, 0), 100);
    const displayValue = indeterminate ? 0 : percentage;

    // Format display text
    const getDisplayText = () => {
      if (indeterminate) return "Loading...";
      if (showPercentage) return `${Math.round(percentage)}%`;
      if (showValue) return `${value}/${max}`;
      return null;
    };

    const displayText = getDisplayText();

    return (
      <div className="space-y-2">
        {/* Label and Value */}
        {(label || displayText) && (
          <div className="flex items-center justify-between">
            {label && (
              <span
                className={cn(
                  "font-medium text-secondary-700",
                  labelSizeClasses[size]
                )}
              >
                {label}
              </span>
            )}
            {displayText && (
              <span
                className={cn(
                  "font-medium text-secondary-600",
                  labelSizeClasses[size]
                )}
              >
                {displayText}
              </span>
            )}
          </div>
        )}

        {/* Progress Bar */}
        <ProgressPrimitive.Root
          ref={ref}
          className={cn(
            rootClasses,
            sizeClasses[size],
            className
          )}
          value={indeterminate ? undefined : value}
          max={max}
          {...props}
        >
          <ProgressPrimitive.Indicator
            className={cn(
              "h-full transition-all duration-300 ease-out relative",
              variantClasses[variant],
              striped && "bg-stripes"
            )}
            style={{
              transform: `translateX(-${100 - displayValue}%)`,
            }}
          >
            {/* Animated Progress Bar */}
            {animated && !indeterminate && (
              <motion.div
                className="absolute inset-0 bg-current"
                variants={barVariants}
                initial="initial"
                animate="animate"
                custom={displayValue}
              />
            )}

            {/* Indeterminate Animation */}
            {indeterminate && (
              <motion.div
                className={cn(
                  "absolute inset-0 w-1/3",
                  variantClasses[variant]
                )}
                variants={indeterminateVariants}
                animate="animate"
              />
            )}

            {/* Pulse Effect for Loading */}
            {indeterminate && (
              <motion.div
                className="absolute inset-0 bg-white/20"
                variants={pulseVariants}
                animate="animate"
              />
            )}

            {/* Shine Effect */}
            {animated && !indeterminate && variant === "gradient" && (
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent w-1/3"
                animate={{
                  x: ["-100%", "300%"],
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut",
                  delay: 0.5,
                }}
              />
            )}
          </ProgressPrimitive.Indicator>
        </ProgressPrimitive.Root>

        {/* Accessibility Text */}
        <div className="sr-only">
          {indeterminate
            ? "Loading in progress"
            : `Progress: ${Math.round(percentage)}% complete`}
        </div>
      </div>
    );
  }
);

EnhancedProgress.displayName = "EnhancedProgress";

// Circular Progress Component
interface CircularProgressProps extends Omit<ProgressProps, 'striped'> {
  strokeWidth?: number;
  radius?: number;
}

export const CircularProgress = React.forwardRef<HTMLDivElement, CircularProgressProps>(
  (
    {
      value = 0,
      max = 100,
      variant = "default",
      size = "md",
      label,
      showPercentage = true,
      animated = true,
      indeterminate = false,
      strokeWidth = 4,
      radius = 40,
      className,
    },
    ref
  ) => {
    const percentage = Math.min(Math.max((value / max) * 100, 0), 100);
    const circumference = 2 * Math.PI * radius;
    const strokeDasharray = circumference;
    const strokeDashoffset = indeterminate ? 0 : circumference - (percentage / 100) * circumference;

    const sizeMap = {
      sm: 48,
      md: 64,
      lg: 80,
      xl: 96,
    };

    const svgSize = sizeMap[size];

    return (
      <div
        ref={ref}
        className={cn("relative inline-flex items-center justify-center", className)}
        style={{ width: svgSize, height: svgSize }}
      >
        <svg
          width={svgSize}
          height={svgSize}
          viewBox={`0 0 ${(radius + strokeWidth) * 2} ${(radius + strokeWidth) * 2}`}
          className="transform -rotate-90"
        >
          {/* Background Circle */}
          <circle
            cx={radius + strokeWidth}
            cy={radius + strokeWidth}
            r={radius}
            stroke="currentColor"
            strokeWidth={strokeWidth}
            fill="none"
            className="text-secondary-200"
          />

          {/* Progress Circle */}
          <motion.circle
            cx={radius + strokeWidth}
            cy={radius + strokeWidth}
            r={radius}
            stroke="currentColor"
            strokeWidth={strokeWidth}
            fill="none"
            strokeLinecap="round"
            strokeDasharray={strokeDasharray}
            strokeDashoffset={strokeDashoffset}
            className={cn(variantClasses[variant])}
            initial={{ strokeDashoffset: circumference }}
            animate={{
              strokeDashoffset: indeterminate ? [circumference, 0, circumference] : strokeDashoffset,
            }}
            transition={
              indeterminate
                ? { duration: 2, repeat: Infinity, ease: "easeInOut" }
                : { duration: animated ? 0.8 : 0, ease: "easeOut" }
            }
          />
        </svg>

        {/* Center Content */}
        <div className="absolute inset-0 flex items-center justify-center">
          {label ? (
            <span className={cn("font-medium text-secondary-700", labelSizeClasses[size])}>
              {label}
            </span>
          ) : showPercentage && !indeterminate ? (
            <span className={cn("font-medium text-secondary-700", labelSizeClasses[size])}>
              {Math.round(percentage)}%
            </span>
          ) : indeterminate ? (
            <motion.div
              animate={{ rotate: 360 }}
              transition={{
                duration: 1,
                repeat: Infinity,
                ease: "linear",
              }}
              className="text-primary-500"
            >
              ⟳
            </motion.div>
          ) : null}
        </div>
      </div>
    );
  }
);

CircularProgress.displayName = "CircularProgress";

// Custom CSS for striped pattern
const stripedStyles = `
.bg-stripes {
  background-image: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.2) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0.2) 75%,
    transparent 75%,
    transparent
  );
  background-size: 1rem 1rem;
  animation: stripes 1s linear infinite;
}

@keyframes stripes {
  0% { background-position: 0 0; }
  100% { background-position: 1rem 0; }
}
`;

// Inject styles
if (typeof document !== "undefined") {
  const styleElement = document.createElement("style");
  styleElement.textContent = stripedStyles;
  document.head.appendChild(styleElement);
}

// Export types
export type { ProgressProps, CircularProgressProps, ProgressVariant, ProgressSize };