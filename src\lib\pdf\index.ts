/**
 * PDF Export Module for Clarity Engine
 * Professional PDF report generation for construction cost calculations
 */

export { generatePDFReport } from './generator';
export { 
  exportCalculationToPDFEnhanced, 
  exportCalculationToPDFWithRetry,
  validatePDFExportData,
  estimatePDFGenerationTime,
  getPDFExportAnalytics 
} from './enhanced-generator';

// Re-export types for convenience
export type { CalculationInput, CalculationResult } from '@/core/calculator/types';
export type { PDFExportOptions, PDFExportResult } from './enhanced-generator';

/**
 * PDF Generation Options Interface
 */
export interface PDFOptions {
  filename?: string;
  includeTimeline?: boolean;
  includeMaterials?: boolean;
  includeDetailedBreakdown?: boolean;
}

/**
 * Quick PDF generation function with sensible defaults
 */
export async function exportCalculationToPDF(
  input: any,
  result: any,
  options?: Partial<PDFOptions>
): Promise<void> {
  const { generatePDFReport } = await import('./generator');
  
  const defaultOptions: PDFOptions = {
    includeTimeline: true,
    includeMaterials: true,
    includeDetailedBreakdown: true,
    ...options,
  };
  
  return generatePDFReport(input, result, defaultOptions);
}