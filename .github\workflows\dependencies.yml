name: 📦 Dependency Management

on:
  push:
    branches: [ main, master, develop ]
  pull_request:
    branches: [ main, master, develop ]
  schedule:
    # Run dependency checks daily at 5 AM UTC
    - cron: '0 5 * * *'
    # Run dependency updates weekly on Mondays at 6 AM UTC
    - cron: '0 6 * * 1'
  workflow_dispatch:
    inputs:
      update_type:
        description: 'Type of dependency update'
        required: true
        default: 'patch'
        type: choice
        options:
          - patch
          - minor
          - major
          - all

env:
  NODE_VERSION: '18'

jobs:
  # Dependency Audit and Security
  dependency-audit:
    name: 🔍 Dependency Security Audit
    runs-on: ubuntu-latest
    timeout-minutes: 15
    
    outputs:
      total-deps: ${{ steps.audit.outputs.total-deps }}
      outdated-deps: ${{ steps.audit.outputs.outdated-deps }}
      vulnerable-deps: ${{ steps.audit.outputs.vulnerable-deps }}
      security-score: ${{ steps.audit.outputs.security-score }}
      
    steps:
      - name: 📦 Checkout code
        uses: actions/checkout@v4
        
      - name: 🔧 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 📥 Install dependencies
        run: npm ci --prefer-offline --no-audit --progress=false
        
      - name: 🔍 Run dependency audit
        id: audit
        run: |
          # Generate audit report
          npm audit --json > audit-report.json || true
          npm audit > audit-report.txt || true
          
          # Count dependencies
          TOTAL_DEPS=$(npm list --depth=0 --json | jq '.dependencies | length')
          
          # Check for outdated packages
          npm outdated --json > outdated-report.json || true
          OUTDATED_DEPS=$(jq 'length' outdated-report.json 2>/dev/null || echo "0")
          
          # Count vulnerabilities
          CRITICAL_VULNS=$(jq '.vulnerabilities | to_entries | map(select(.value.severity == "critical")) | length' audit-report.json 2>/dev/null || echo "0")
          HIGH_VULNS=$(jq '.vulnerabilities | to_entries | map(select(.value.severity == "high")) | length' audit-report.json 2>/dev/null || echo "0")
          MODERATE_VULNS=$(jq '.vulnerabilities | to_entries | map(select(.value.severity == "moderate")) | length' audit-report.json 2>/dev/null || echo "0")
          
          VULNERABLE_DEPS=$((CRITICAL_VULNS + HIGH_VULNS + MODERATE_VULNS))
          
          # Calculate security score
          SECURITY_SCORE=$((100 - CRITICAL_VULNS * 30 - HIGH_VULNS * 20 - MODERATE_VULNS * 5))
          if [ $SECURITY_SCORE -lt 0 ]; then
            SECURITY_SCORE=0
          fi
          
          echo "total-deps=$TOTAL_DEPS" >> $GITHUB_OUTPUT
          echo "outdated-deps=$OUTDATED_DEPS" >> $GITHUB_OUTPUT
          echo "vulnerable-deps=$VULNERABLE_DEPS" >> $GITHUB_OUTPUT
          echo "security-score=$SECURITY_SCORE" >> $GITHUB_OUTPUT
          
          echo "Dependency Summary:"
          echo "- Total Dependencies: $TOTAL_DEPS"
          echo "- Outdated Dependencies: $OUTDATED_DEPS"
          echo "- Vulnerable Dependencies: $VULNERABLE_DEPS"
          echo "- Security Score: $SECURITY_SCORE/100"
          
      - name: 📊 Dependency tree analysis
        run: |
          # Generate dependency tree
          npm list --all --json > dependency-tree.json
          
          # Check for duplicate dependencies
          npm dedupe --dry-run > dedupe-report.txt || true
          
          # Analyze package sizes
          npm install -g bundlephobia-cli
          bundlephobia --json > package-sizes.json || true
          
          echo "Dependency tree analysis completed"
          
      - name: 🔍 License compliance check
        run: |
          # Install license checker
          npm install -g license-checker
          
          # Generate license report
          license-checker --json > license-report.json
          license-checker --csv > license-report.csv
          license-checker --summary > license-summary.txt
          
          # Check for problematic licenses
          PROBLEMATIC_LICENSES=$(license-checker --json | jq -r 'to_entries[] | select(.value.licenses | type == "string" and (contains("GPL") or contains("AGPL") or contains("LGPL"))) | .key' | wc -l)
          
          echo "License compliance check:"
          echo "- Packages with problematic licenses: $PROBLEMATIC_LICENSES"
          
          if [ $PROBLEMATIC_LICENSES -gt 0 ]; then
            echo "⚠️ Found packages with potentially problematic licenses"
            license-checker --json | jq -r 'to_entries[] | select(.value.licenses | type == "string" and (contains("GPL") or contains("AGPL") or contains("LGPL"))) | "\(.key): \(.value.licenses)"'
          fi
          
      - name: 📤 Upload audit reports
        uses: actions/upload-artifact@v4
        with:
          name: dependency-audit-reports
          path: |
            audit-report.json
            audit-report.txt
            outdated-report.json
            dependency-tree.json
            license-report.json
            license-report.csv
            license-summary.txt
            package-sizes.json
          retention-days: 30
          
      - name: 🚨 Critical vulnerability alert
        if: steps.audit.outputs.vulnerable-deps > 0
        run: |
          echo "🚨 SECURITY ALERT: ${{ steps.audit.outputs.vulnerable-deps }} vulnerable dependencies found!"
          
          # Show critical vulnerabilities
          if [ -f audit-report.json ]; then
            jq -r '.vulnerabilities | to_entries[] | select(.value.severity == "critical" or .value.severity == "high") | "\(.key): \(.value.severity) - \(.value.title)"' audit-report.json
          fi

  # Dependency Updates
  dependency-updates:
    name: 🔄 Dependency Updates
    runs-on: ubuntu-latest
    if: github.event.schedule == '0 6 * * 1' || github.event_name == 'workflow_dispatch'
    timeout-minutes: 20
    
    outputs:
      updates-available: ${{ steps.check-updates.outputs.updates-available }}
      
    steps:
      - name: 📦 Checkout code
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          
      - name: 🔧 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 📥 Install dependencies
        run: npm ci --prefer-offline --no-audit --progress=false
        
      - name: 🔍 Check for updates
        id: check-updates
        run: |
          # Install npm-check-updates
          npm install -g npm-check-updates
          
          # Check for updates
          UPDATE_TYPE="${{ github.event.inputs.update_type || 'patch' }}"
          
          case $UPDATE_TYPE in
            "patch")
              ncu --target patch --jsonUpgraded > updates.json || echo "{}" > updates.json
              ;;
            "minor")
              ncu --target minor --jsonUpgraded > updates.json || echo "{}" > updates.json
              ;;
            "major")
              ncu --target latest --jsonUpgraded > updates.json || echo "{}" > updates.json
              ;;
            "all")
              ncu --target latest --jsonUpgraded > updates.json || echo "{}" > updates.json
              ;;
          esac
          
          UPDATES_COUNT=$(jq 'length' updates.json)
          echo "updates-available=$UPDATES_COUNT" >> $GITHUB_OUTPUT
          
          echo "Available updates ($UPDATE_TYPE): $UPDATES_COUNT"
          
          if [ $UPDATES_COUNT -gt 0 ]; then
            echo "Updates to apply:"
            jq -r 'to_entries[] | "\(.key): \(.value)"' updates.json
          fi
          
      - name: 🔄 Apply updates
        if: steps.check-updates.outputs.updates-available > 0
        run: |
          UPDATE_TYPE="${{ github.event.inputs.update_type || 'patch' }}"
          
          # Apply updates based on type
          case $UPDATE_TYPE in
            "patch")
              ncu --target patch -u
              ;;
            "minor")
              ncu --target minor -u
              ;;
            "major")
              ncu --target latest -u
              ;;
            "all")
              ncu --target latest -u
              ;;
          esac
          
          # Install updated dependencies
          npm install
          
      - name: 🧪 Test after updates
        if: steps.check-updates.outputs.updates-available > 0
        run: |
          # Run tests to ensure updates don't break anything
          npm run test:unit
          npm run lint
          npm run type-check
          npm run build
          
      - name: 📝 Create update PR
        if: steps.check-updates.outputs.updates-available > 0
        uses: peter-evans/create-pull-request@v5
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          commit-message: |
            chore: update dependencies (${{ github.event.inputs.update_type || 'patch' }})
            
            - Updated ${{ steps.check-updates.outputs.updates-available }} dependencies
            - Type: ${{ github.event.inputs.update_type || 'patch' }}
            - Auto-generated by dependency update workflow
          title: 'chore: dependency updates (${{ github.event.inputs.update_type || 'patch' }})'
          body: |
            ## 📦 Dependency Updates
            
            This PR contains automated dependency updates.
            
            **Update Type:** ${{ github.event.inputs.update_type || 'patch' }}
            **Dependencies Updated:** ${{ steps.check-updates.outputs.updates-available }}
            
            ### 🔍 Changes
            - Updated dependencies to latest ${{ github.event.inputs.update_type || 'patch' }} versions
            - All tests passing ✅
            - Build successful ✅
            
            ### 🧪 Testing
            - [x] Unit tests
            - [x] Linting
            - [x] Type checking
            - [x] Build
            
            Auto-generated by GitHub Actions 🤖
          branch: dependency-updates-${{ github.run_number }}
          delete-branch: true

  # Security Monitoring
  security-monitoring:
    name: 🛡️ Security Monitoring
    runs-on: ubuntu-latest
    timeout-minutes: 10
    
    steps:
      - name: 📦 Checkout code
        uses: actions/checkout@v4
        
      - name: 🔧 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 📥 Install dependencies
        run: npm ci --prefer-offline --no-audit --progress=false
        
      - name: 🔍 Snyk security monitoring
        uses: snyk/actions/node@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          command: monitor
          args: --org=${{ secrets.SNYK_ORG_ID }}
        continue-on-error: true
        
      - name: 🛡️ OSV vulnerability scanning
        uses: google/osv-scanner-action@v1.7.4
        with:
          scan-args: |-
            --output=osv-report.json
            --format=json
            ./package-lock.json
        continue-on-error: true
        
      - name: 📊 Security advisory check
        run: |
          # Check GitHub security advisories
          npm audit --audit-level=moderate --json > security-advisories.json || true
          
          # Extract advisory information
          if [ -f security-advisories.json ]; then
            ADVISORIES=$(jq '.advisories | length' security-advisories.json 2>/dev/null || echo "0")
            echo "Security advisories found: $ADVISORIES"
            
            if [ $ADVISORIES -gt 0 ]; then
              echo "Advisory details:"
              jq -r '.advisories[] | "- \(.title) (\(.severity)): \(.url)"' security-advisories.json
            fi
          fi

  # Package Maintenance
  package-maintenance:
    name: 🔧 Package Maintenance
    runs-on: ubuntu-latest
    timeout-minutes: 15
    
    steps:
      - name: 📦 Checkout code
        uses: actions/checkout@v4
        
      - name: 🔧 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 📥 Install dependencies
        run: npm ci --prefer-offline --no-audit --progress=false
        
      - name: 🧹 Clean up unused dependencies
        run: |
          # Install depcheck to find unused dependencies
          npm install -g depcheck
          
          # Check for unused dependencies
          depcheck --json > unused-deps-report.json || true
          
          UNUSED_DEPS=$(jq '.dependencies | length' unused-deps-report.json 2>/dev/null || echo "0")
          UNUSED_DEV_DEPS=$(jq '.devDependencies | length' unused-deps-report.json 2>/dev/null || echo "0")
          
          echo "Unused dependencies: $UNUSED_DEPS"
          echo "Unused dev dependencies: $UNUSED_DEV_DEPS"
          
          if [ $UNUSED_DEPS -gt 0 ]; then
            echo "Unused production dependencies:"
            jq -r '.dependencies[]' unused-deps-report.json
          fi
          
          if [ $UNUSED_DEV_DEPS -gt 0 ]; then
            echo "Unused development dependencies:"
            jq -r '.devDependencies[]' unused-deps-report.json
          fi
          
      - name: 🔍 Duplicate dependency check
        run: |
          # Check for duplicate dependencies
          npm dedupe --dry-run > dedupe-report.txt 2>&1 || true
          
          if [ -s dedupe-report.txt ]; then
            echo "Duplicate dependencies found:"
            cat dedupe-report.txt
          else
            echo "No duplicate dependencies found"
          fi
          
      - name: 📊 Package size analysis
        run: |
          # Analyze package sizes
          npm install -g cost-of-modules
          
          # Generate size report
          cost-of-modules --yarn=false > package-cost-report.txt || true
          
          echo "Package size analysis completed"
          
      - name: 📤 Upload maintenance reports
        uses: actions/upload-artifact@v4
        with:
          name: package-maintenance-reports
          path: |
            unused-deps-report.json
            dedupe-report.txt
            package-cost-report.txt
          retention-days: 7

  # Dependency Summary
  dependency-summary:
    name: 📊 Dependency Summary
    runs-on: ubuntu-latest
    needs: [dependency-audit, security-monitoring, package-maintenance]
    if: always()
    
    steps:
      - name: 📊 Generate dependency summary
        run: |
          echo "# 📦 Dependency Management Report" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "## 📊 Overview" >> $GITHUB_STEP_SUMMARY
          echo "- Total Dependencies: ${{ needs.dependency-audit.outputs.total-deps || 'N/A' }}" >> $GITHUB_STEP_SUMMARY
          echo "- Outdated Dependencies: ${{ needs.dependency-audit.outputs.outdated-deps || 'N/A' }}" >> $GITHUB_STEP_SUMMARY
          echo "- Vulnerable Dependencies: ${{ needs.dependency-audit.outputs.vulnerable-deps || 'N/A' }}" >> $GITHUB_STEP_SUMMARY
          echo "- Security Score: ${{ needs.dependency-audit.outputs.security-score || 'N/A' }}/100" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "## 🔍 Status" >> $GITHUB_STEP_SUMMARY
          echo "| Check | Status |" >> $GITHUB_STEP_SUMMARY
          echo "|-------|--------|" >> $GITHUB_STEP_SUMMARY
          echo "| Dependency Audit | ${{ needs.dependency-audit.result == 'success' && '✅' || '❌' }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Security Monitoring | ${{ needs.security-monitoring.result == 'success' && '✅' || '❌' }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Package Maintenance | ${{ needs.package-maintenance.result == 'success' && '✅' || '❌' }} |" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          if [ "${{ needs.dependency-audit.outputs.vulnerable-deps || 0 }}" -gt 0 ]; then
            echo "## 🚨 Security Alerts" >> $GITHUB_STEP_SUMMARY
            echo "- **${{ needs.dependency-audit.outputs.vulnerable-deps || 0 }}** vulnerable dependencies detected" >> $GITHUB_STEP_SUMMARY
            echo "- Review security audit reports for details" >> $GITHUB_STEP_SUMMARY
            echo "- Consider updating affected packages" >> $GITHUB_STEP_SUMMARY
          fi
          
      - name: 🚨 Dependency alert
        if: |
          needs.dependency-audit.outputs.vulnerable-deps > 5 ||
          needs.dependency-audit.outputs.security-score < 70
        run: |
          echo "🚨 DEPENDENCY ALERT: Critical dependency issues detected!"
          echo "Vulnerable dependencies: ${{ needs.dependency-audit.outputs.vulnerable-deps || 0 }}"
          echo "Security score: ${{ needs.dependency-audit.outputs.security-score || 'N/A' }}/100"
          exit 1