'use client';

import * as React from 'react';
import { motion } from 'framer-motion';
import { cva, type VariantProps } from 'class-variance-authority';

import { cn } from '@/lib/utils';
import { 
  buttonHover, 
  buttonTap, 
  buttonPulse, 
  buttonRipple, 
  loadingSpinner,
  successCheckmark,
  createHoverAnimation,
  prefersReducedMotion 
} from '@/lib/animations';

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive cursor-pointer",
  {
    variants: {
      variant: {
        default:
          'bg-primary text-primary-foreground shadow-xs hover:bg-primary/90',
        destructive:
          'bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',
        outline:
          'border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50',
        secondary:
          'bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80',
        ghost:
          'hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50',
        link: 'text-primary underline-offset-4 hover:underline',
      },
      size: {
        default: 'h-9 px-4 py-2 has-[>svg]:px-3',
        sm: 'h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5',
        lg: 'h-10 rounded-md px-6 has-[>svg]:px-4',
        icon: 'size-9',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
);

interface AnimatedButtonProps extends VariantProps<typeof buttonVariants> {
  className?: string;
  loading?: boolean;
  loadingText?: string;
  animate?: boolean;
  disabled?: boolean;
  type?: 'button' | 'submit' | 'reset';
  onClick?: (event: React.MouseEvent<HTMLButtonElement>) => void;
  children: React.ReactNode;
  pulse?: boolean;
  success?: boolean;
  ripple?: boolean;
  glow?: boolean;
}

const AnimatedButton = React.forwardRef<
  HTMLButtonElement,
  AnimatedButtonProps
>(({
  className,
  variant,
  size,
  loading = false,
  loadingText = 'Loading...',
  animate = true,
  children,
  disabled,
  onClick,
  type = 'button',
  pulse = false,
  success = false,
  ripple = false,
  glow = false,
  ...props
}, ref) => {
  const isDisabled = disabled || loading;

  const buttonContent = loading ? (
    <div className="flex items-center gap-2">
      <motion.div
        className="h-4 w-4 border-2 border-current border-t-transparent rounded-full"
        {...loadingSpinner}
      />
      {loadingText}
    </div>
  ) : success ? (
    <motion.div
      className="flex items-center gap-2"
      variants={successCheckmark}
      initial="initial"
      animate="animate"
    >
      <svg className="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
        <path d="M20 6L9 17l-5-5" />
      </svg>
      Success
    </motion.div>
  ) : children;

  if (!animate || prefersReducedMotion) {
    return (
      <button
        ref={ref}
        type={type}
        className={cn(buttonVariants({ variant, size, className }))}
        disabled={isDisabled}
        onClick={onClick}
        {...props}
      >
        {buttonContent}
      </button>
    );
  }

  const hoverAnimation = createHoverAnimation(
    1.02,
    0,
    glow ? '0 0 20px rgba(59, 130, 246, 0.5)' : '0 8px 25px rgba(0, 0, 0, 0.15)'
  );

  return (
    <motion.button
      ref={ref}
      type={type}
      className={cn(
        buttonVariants({ variant, size, className }),
        glow && 'shadow-lg',
        'relative overflow-hidden'
      )}
      disabled={isDisabled}
      onClick={onClick}
      {...(isDisabled ? {} : hoverAnimation)}
      animate={pulse ? buttonPulse.animate : {}}
      whileFocus={isDisabled ? undefined : { 
        scale: 1.01,
        transition: { duration: 0.15 }
      }}
      transition={{ duration: 0.15 }}
      {...props}
    >
      {buttonContent}
    </motion.button>
  );
});

AnimatedButton.displayName = 'AnimatedButton';

export { AnimatedButton, buttonVariants };
export type { AnimatedButtonProps };