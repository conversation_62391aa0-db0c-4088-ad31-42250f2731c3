/**
 * Enhanced Components Test Suite
 * Comprehensive tests for all enhanced MVP components
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, it, expect, vi } from 'vitest';

import {
  EnhancedButton,
  EnhancedInput,
  EnhancedCard,
  EnhancedSelect,
  EnhancedProgress,
  EnhancedModal,
  ConfirmModal,
} from '../enhanced-index';

// Mock framer-motion for tests
vi.mock('framer-motion', () => ({
  motion: {
    button: React.forwardRef<HTMLButtonElement>((props: any, ref) => (
      <button ref={ref} {...props} />
    )),
    div: React.forwardRef<HTMLDivElement>((props: any, ref) => (
      <div ref={ref} {...props} />
    )),
    input: React.forwardRef<HTMLInputElement>((props: any, ref) => (
      <input ref={ref} {...props} />
    )),
    p: React.forwardRef<HTMLParagraphElement>((props: any, ref) => (
      <p ref={ref} {...props} />
    )),
  },
  AnimatePresence: ({ children }: any) => children,
}));

// Mock Radix UI components
vi.mock('@radix-ui/react-select', () => ({
  Root: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  Trigger: React.forwardRef<HTMLButtonElement>((props: any, ref) => (
    <button ref={ref} {...props} />
  )),
  Portal: ({ children }: any) => children,
  Content: React.forwardRef<HTMLDivElement>((props: any, ref) => (
    <div ref={ref} {...props} />
  )),
  Viewport: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  Item: React.forwardRef<HTMLDivElement>((props: any, ref) => (
    <div ref={ref} {...props} />
  )),
  ItemText: ({ children }: any) => <span>{children}</span>,
  ItemIndicator: ({ children }: any) => <span>{children}</span>,
  Value: ({ placeholder }: any) => <span>{placeholder}</span>,
  Icon: ({ children }: any) => children,
}));

vi.mock('@radix-ui/react-progress', () => ({
  Root: React.forwardRef<HTMLDivElement>((props: any, ref) => (
    <div ref={ref} role="progressbar" {...props} />
  )),
  Indicator: React.forwardRef<HTMLDivElement>((props: any, ref) => (
    <div ref={ref} {...props} />
  )),
}));

vi.mock('@radix-ui/react-dialog', () => ({
  Root: ({ children, open }: any) => open ? <div>{children}</div> : null,
  Portal: ({ children }: any) => children,
  Overlay: React.forwardRef<HTMLDivElement>((props: any, ref) => (
    <div ref={ref} {...props} />
  )),
  Content: React.forwardRef<HTMLDivElement>((props: any, ref) => (
    <div ref={ref} {...props} />
  )),
  Title: ({ children }: any) => <h2>{children}</h2>,
  Description: ({ children }: any) => <p>{children}</p>,
  Close: React.forwardRef<HTMLButtonElement>((props: any, ref) => (
    <button ref={ref} {...props} />
  )),
  Trigger: React.forwardRef<HTMLButtonElement>((props: any, ref) => (
    <button ref={ref} {...props} />
  )),
}));

describe('Enhanced Components', () => {
  describe('EnhancedButton', () => {
    it('renders with default props', () => {
      render(<EnhancedButton>Click me</EnhancedButton>);
      expect(screen.getByRole('button')).toHaveTextContent('Click me');
    });

    it('handles different variants', () => {
      const { rerender } = render(
        <EnhancedButton variant="primary">Primary</EnhancedButton>
      );
      expect(screen.getByRole('button')).toBeInTheDocument();

      rerender(<EnhancedButton variant="secondary">Secondary</EnhancedButton>);
      expect(screen.getByRole('button')).toHaveTextContent('Secondary');
    });

    it('shows loading state', () => {
      render(<EnhancedButton loading>Loading</EnhancedButton>);
      expect(screen.getByRole('button')).toHaveAttribute('aria-busy', 'true');
    });

    it('handles click events', async () => {
      const handleClick = vi.fn();
      render(<EnhancedButton onClick={handleClick}>Click</EnhancedButton>);
      
      await userEvent.click(screen.getByRole('button'));
      expect(handleClick).toHaveBeenCalledTimes(1);
    });

    it('is disabled when loading', async () => {
      const handleClick = vi.fn();
      render(
        <EnhancedButton loading onClick={handleClick}>
          Loading
        </EnhancedButton>
      );
      
      await userEvent.click(screen.getByRole('button'));
      expect(handleClick).not.toHaveBeenCalled();
    });
  });

  describe('EnhancedInput', () => {
    it('renders with label', () => {
      render(<EnhancedInput label="Test Input" />);
      expect(screen.getByLabelText('Test Input')).toBeInTheDocument();
    });

    it('shows error state', () => {
      render(<EnhancedInput errorMessage="This field is required" />);
      expect(screen.getByText('This field is required')).toBeInTheDocument();
    });

    it('handles password toggle', async () => {
      render(
        <EnhancedInput
          type="password"
          showPasswordToggle
          defaultValue="secret"
        />
      );
      
      const input = screen.getByDisplayValue('secret');
      expect(input).toHaveAttribute('type', 'password');

      const toggleButton = screen.getByLabelText('Show password');
      await userEvent.click(toggleButton);
      
      await waitFor(() => {
        expect(input).toHaveAttribute('type', 'text');
      });
    });

    it('handles focus and blur events', async () => {
      const onFocus = vi.fn();
      const onBlur = vi.fn();
      
      render(<EnhancedInput onFocus={onFocus} onBlur={onBlur} />);
      
      const input = screen.getByRole('textbox');
      await userEvent.click(input);
      expect(onFocus).toHaveBeenCalledTimes(1);
      
      await userEvent.tab();
      expect(onBlur).toHaveBeenCalledTimes(1);
    });
  });

  describe('EnhancedCard', () => {
    it('renders children', () => {
      render(
        <EnhancedCard>
          <div>Card content</div>
        </EnhancedCard>
      );
      expect(screen.getByText('Card content')).toBeInTheDocument();
    });

    it('renders with header and footer', () => {
      render(
        <EnhancedCard
          header={<div>Header</div>}
          footer={<div>Footer</div>}
        >
          Content
        </EnhancedCard>
      );
      
      expect(screen.getByText('Header')).toBeInTheDocument();
      expect(screen.getByText('Content')).toBeInTheDocument();
      expect(screen.getByText('Footer')).toBeInTheDocument();
    });

    it('handles click when interactive', async () => {
      const onClick = vi.fn();
      render(
        <EnhancedCard interactive onClick={onClick}>
          Clickable card
        </EnhancedCard>
      );
      
      await userEvent.click(screen.getByText('Clickable card'));
      expect(onClick).toHaveBeenCalledTimes(1);
    });
  });

  describe('EnhancedSelect', () => {
    const options = [
      { value: 'option1', label: 'Option 1' },
      { value: 'option2', label: 'Option 2' },
      { value: 'option3', label: 'Option 3' },
    ];

    it('renders with options', () => {
      render(<EnhancedSelect options={options} />);
      expect(screen.getByRole('button')).toBeInTheDocument();
    });

    it('shows label when provided', () => {
      render(<EnhancedSelect label="Choose option" options={options} />);
      expect(screen.getByText('Choose option')).toBeInTheDocument();
    });

    it('handles value changes', () => {
      const onValueChange = vi.fn();
      render(
        <EnhancedSelect
          options={options}
          onValueChange={onValueChange}
        />
      );
      
      expect(screen.getByRole('button')).toBeInTheDocument();
    });
  });

  describe('EnhancedProgress', () => {
    it('renders with value', () => {
      render(<EnhancedProgress value={50} max={100} />);
      expect(screen.getByRole('progressbar')).toBeInTheDocument();
    });

    it('shows percentage when enabled', () => {
      render(
        <EnhancedProgress
          value={75}
          max={100}
          showPercentage
        />
      );
      expect(screen.getByText('75%')).toBeInTheDocument();
    });

    it('shows label and value', () => {
      render(
        <EnhancedProgress
          value={30}
          max={100}
          label="Loading"
          showValue
        />
      );
      expect(screen.getByText('Loading')).toBeInTheDocument();
      expect(screen.getByText('30/100')).toBeInTheDocument();
    });

    it('handles indeterminate state', () => {
      render(<EnhancedProgress indeterminate />);
      expect(screen.getByText('Loading...')).toBeInTheDocument();
    });
  });

  describe('EnhancedModal', () => {
    it('does not render when closed', () => {
      render(
        <EnhancedModal open={false}>
          <div>Modal content</div>
        </EnhancedModal>
      );
      expect(screen.queryByText('Modal content')).not.toBeInTheDocument();
    });

    it('renders when open', () => {
      render(
        <EnhancedModal open={true}>
          <div>Modal content</div>
        </EnhancedModal>
      );
      expect(screen.getByText('Modal content')).toBeInTheDocument();
    });

    it('renders with title and description', () => {
      render(
        <EnhancedModal
          open={true}
          title="Test Modal"
          description="This is a test modal"
        >
          Content
        </EnhancedModal>
      );
      
      expect(screen.getByText('Test Modal')).toBeInTheDocument();
      expect(screen.getByText('This is a test modal')).toBeInTheDocument();
    });
  });

  describe('ConfirmModal', () => {
    it('renders confirmation dialog', () => {
      render(
        <ConfirmModal
          open={true}
          title="Confirm Action"
          description="Are you sure?"
          onConfirm={vi.fn()}
        />
      );
      
      expect(screen.getByText('Confirm Action')).toBeInTheDocument();
      expect(screen.getByText('Are you sure?')).toBeInTheDocument();
      expect(screen.getByText('Confirm')).toBeInTheDocument();
      expect(screen.getByText('Cancel')).toBeInTheDocument();
    });

    it('handles confirm action', async () => {
      const onConfirm = vi.fn();
      const onOpenChange = vi.fn();
      
      render(
        <ConfirmModal
          open={true}
          title="Confirm"
          onConfirm={onConfirm}
          onOpenChange={onOpenChange}
        />
      );
      
      await userEvent.click(screen.getByText('Confirm'));
      expect(onConfirm).toHaveBeenCalledTimes(1);
    });
  });
});

// Integration test for component combinations
describe('Component Integration', () => {
  it('works together in a form', async () => {
    const onSubmit = vi.fn();
    
    render(
      <EnhancedCard>
        <form onSubmit={onSubmit}>
          <EnhancedInput
            label="Name"
            placeholder="Enter your name"
          />
          <EnhancedSelect
            label="Category"
            options={[
              { value: 'a', label: 'Category A' },
              { value: 'b', label: 'Category B' },
            ]}
          />
          <EnhancedProgress value={50} showPercentage />
          <EnhancedButton type="submit">
            Submit
          </EnhancedButton>
        </form>
      </EnhancedCard>
    );
    
    expect(screen.getByLabelText('Name')).toBeInTheDocument();
    expect(screen.getByLabelText('Category')).toBeInTheDocument();
    expect(screen.getByText('50%')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Submit' })).toBeInTheDocument();
  });
});