/**
 * Security Middleware for Production
 * Enhanced security configurations and monitoring
 */

import { NextRequest, NextResponse } from 'next/server';
import { rateLimiter } from '@/lib/rate-limiter';

interface SecurityConfig {
  rateLimit: {
    maxRequests: number;
    windowMs: number;
    skipSuccessfulRequests: boolean;
  };
  csrfProtection: boolean;
  validateInput: boolean;
  blockSuspiciousActivity: boolean;
  logSecurityEvents: boolean;
}

interface SecurityEvent {
  type: 'rate_limit' | 'suspicious_request' | 'invalid_input' | 'csrf_violation' | 'blocked_ip';
  timestamp: Date;
  ip: string;
  userAgent: string;
  path: string;
  details: any;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

class SecurityMonitor {
  private events: SecurityEvent[] = [];
  private blockedIPs: Set<string> = new Set();
  private suspiciousPatterns: RegExp[] = [
    /\<script\>/i,
    /javascript:/i,
    /eval\(/i,
    /document\.cookie/i,
    /xss/i,
    /union\s+select/i,
    /drop\s+table/i,
    /\.\.\//g,
    /\/etc\/passwd/i,
    /cmd\.exe/i,
    /powershell/i,
  ];
  private readonly maxEvents = 1000;

  public recordSecurityEvent(event: SecurityEvent): void {
    this.events.push(event);
    
    // Remove old events
    if (this.events.length > this.maxEvents) {
      this.events.shift();
    }
    
    // Auto-block IPs with high severity events
    if (event.severity === 'critical' || event.severity === 'high') {
      this.blockIP(event.ip);
    }
    
    // Log security events
    console.warn(`Security Event [${event.severity}]: ${event.type} from ${event.ip}`, event);
  }

  public isIPBlocked(ip: string): boolean {
    return this.blockedIPs.has(ip);
  }

  public blockIP(ip: string): void {
    this.blockedIPs.add(ip);
    console.error(`IP ${ip} has been blocked due to security violations`);
  }

  public unblockIP(ip: string): void {
    this.blockedIPs.delete(ip);
  }

  public isSuspiciousRequest(url: string, body?: any): boolean {
    const content = url + (body ? JSON.stringify(body) : '');
    return this.suspiciousPatterns.some(pattern => pattern.test(content));
  }

  public getSecurityEvents(): SecurityEvent[] {
    return [...this.events];
  }

  public getBlockedIPs(): string[] {
    return Array.from(this.blockedIPs);
  }

  public getSecuritySummary(): {
    totalEvents: number;
    blockedIPs: number;
    eventsByType: Record<string, number>;
    eventsBySeverity: Record<string, number>;
    recentCriticalEvents: SecurityEvent[];
  } {
    const eventsByType: Record<string, number> = {};
    const eventsBySeverity: Record<string, number> = {};
    
    this.events.forEach(event => {
      eventsByType[event.type] = (eventsByType[event.type] || 0) + 1;
      eventsBySeverity[event.severity] = (eventsBySeverity[event.severity] || 0) + 1;
    });
    
    const recentCriticalEvents = this.events
      .filter(event => event.severity === 'critical')
      .slice(-10);
    
    return {
      totalEvents: this.events.length,
      blockedIPs: this.blockedIPs.size,
      eventsByType,
      eventsBySeverity,
      recentCriticalEvents,
    };
  }
}

// Create singleton instance
export const securityMonitor = new SecurityMonitor();

// Security middleware configuration
const securityConfig: SecurityConfig = {
  rateLimit: {
    maxRequests: 100,
    windowMs: 15 * 60 * 1000, // 15 minutes
    skipSuccessfulRequests: false,
  },
  csrfProtection: true,
  validateInput: true,
  blockSuspiciousActivity: true,
  logSecurityEvents: true,
};

// Input validation utilities
export const inputValidator = {
  sanitizeString: (input: string): string => {
    return input
      .replace(/[<>\"']/g, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '')
      .trim();
  },

  validateEmail: (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email) && email.length <= 254;
  },

  validateNumber: (value: any, min?: number, max?: number): boolean => {
    const num = Number(value);
    if (isNaN(num)) return false;
    if (min !== undefined && num < min) return false;
    if (max !== undefined && num > max) return false;
    return true;
  },

  validateConstructionInput: (input: any): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];
    
    if (!input) {
      errors.push('Input is required');
      return { isValid: false, errors };
    }
    
    // Validate built-up area
    if (!inputValidator.validateNumber(input.builtUpArea, 100, 50000)) {
      errors.push('Built-up area must be between 100 and 50000 sq ft');
    }
    
    // Validate quality tier
    const validTiers = ['smart', 'premium', 'luxury'];
    if (!validTiers.includes(input.qualityTier)) {
      errors.push('Invalid quality tier');
    }
    
    // Validate location
    if (typeof input.location !== 'string' || input.location.length < 2) {
      errors.push('Invalid location');
    }
    
    // Validate floors
    if (!inputValidator.validateNumber(input.floors, 0, 20)) {
      errors.push('Floors must be between 0 and 20');
    }
    
    return { isValid: errors.length === 0, errors };
  },
};

// CSRF protection utility
export const csrfProtection = {
  generateToken: (): string => {
    return Math.random().toString(36).substring(2, 15) + 
           Math.random().toString(36).substring(2, 15);
  },

  validateToken: (token: string, sessionToken: string): boolean => {
    return token === sessionToken && token.length >= 10;
  },
};

// Main security middleware
export function withSecurity(handler: Function, config: Partial<SecurityConfig> = {}) {
  const finalConfig = { ...securityConfig, ...config };
  
  return async (req: NextRequest, ...args: any[]) => {
    const ip = (req as any).ip || req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'unknown';
    const userAgent = req.headers.get('user-agent') || 'unknown';
    const path = req.nextUrl.pathname;
    
    try {
      // Check if IP is blocked
      if (securityMonitor.isIPBlocked(ip)) {
        securityMonitor.recordSecurityEvent({
          type: 'blocked_ip',
          timestamp: new Date(),
          ip,
          userAgent,
          path,
          details: { reason: 'IP blocked due to previous violations' },
          severity: 'high',
        });
        
        return NextResponse.json(
          { error: 'Access denied' },
          { status: 403 }
        );
      }
      
      // Rate limiting
      if (finalConfig.rateLimit) {
        const rateLimitResult = await rateLimiter.check(ip);
        if (!rateLimitResult.success) {
          securityMonitor.recordSecurityEvent({
            type: 'rate_limit',
            timestamp: new Date(),
            ip,
            userAgent,
            path,
            details: { limit: rateLimitResult.limit, remaining: rateLimitResult.remaining },
            severity: 'medium',
          });
          
          return NextResponse.json(
            { error: 'Rate limit exceeded' },
            { 
              status: 429,
              headers: {
                'X-RateLimit-Limit': rateLimitResult.limit.toString(),
                'X-RateLimit-Remaining': rateLimitResult.remaining.toString(),
                'X-RateLimit-Reset': new Date(rateLimitResult.resetTime).toISOString(),
              }
            }
          );
        }
      }
      
      // Check for suspicious activity
      if (finalConfig.blockSuspiciousActivity) {
        const bodyText = req.body ? JSON.stringify(req.body) : '';
        
        if (securityMonitor.isSuspiciousRequest(path + req.nextUrl.search, bodyText)) {
          securityMonitor.recordSecurityEvent({
            type: 'suspicious_request',
            timestamp: new Date(),
            ip,
            userAgent,
            path,
            details: { url: req.nextUrl.toString(), body: bodyText },
            severity: 'high',
          });
          
          return NextResponse.json(
            { error: 'Suspicious activity detected' },
            { status: 400 }
          );
        }
      }
      
      // CSRF protection for state-changing operations
      if (finalConfig.csrfProtection && ['POST', 'PUT', 'DELETE'].includes(req.method)) {
        const csrfToken = req.headers.get('x-csrf-token');
        const sessionToken = req.headers.get('x-session-token');
        
        if (!csrfToken || !sessionToken || !csrfProtection.validateToken(csrfToken, sessionToken)) {
          securityMonitor.recordSecurityEvent({
            type: 'csrf_violation',
            timestamp: new Date(),
            ip,
            userAgent,
            path,
            details: { method: req.method, hasToken: !!csrfToken },
            severity: 'medium',
          });
          
          return NextResponse.json(
            { error: 'CSRF token validation failed' },
            { status: 403 }
          );
        }
      }
      
      // Call the original handler
      return await handler(req, ...args);
      
    } catch (error) {
      // Log security-related errors
      console.error('Security middleware error:', error);
      
      return NextResponse.json(
        { error: 'Security validation failed' },
        { status: 500 }
      );
    }
  };
}

// Security headers utility
export const securityHeaders = {
  getSecurityHeaders: (): Record<string, string> => ({
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'Permissions-Policy': 'camera=(), microphone=(), geolocation=(), payment=()',
    'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload',
    'Content-Security-Policy': [
      "default-src 'self'",
      "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
      "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
      "font-src 'self' https://fonts.gstatic.com",
      "img-src 'self' data: blob:",
      "connect-src 'self' https://*.supabase.co wss://*.supabase.co",
      "frame-ancestors 'none'",
      "base-uri 'self'",
      "form-action 'self'",
    ].join('; '),
  }),
  
  applySecurityHeaders: (response: NextResponse): NextResponse => {
    const headers = securityHeaders.getSecurityHeaders();
    Object.entries(headers).forEach(([key, value]) => {
      response.headers.set(key, value);
    });
    return response;
  },
};

// Export utilities
export {
  securityConfig,
  SecurityMonitor,
  type SecurityEvent,
  type SecurityConfig,
};