/**
 * Performance Optimization Tests
 * Comprehensive testing for performance monitoring and optimization
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { reactProfiler } from '../react-profiler';
import { memoryOptimizer } from '../memory-optimizer';
import { performanceBudgetMonitor } from '../performance-budget';
import { performanceRecommendationsEngine } from '../performance-recommendations';

// Mock performance API
const mockPerformanceMemory = {
  usedJSHeapSize: 50 * 1024 * 1024, // 50MB
  totalJSHeapSize: 100 * 1024 * 1024, // 100MB
  jsHeapSizeLimit: 2 * 1024 * 1024 * 1024, // 2GB
};

const mockPerformanceObserver = vi.fn();

// Mock window.performance
Object.defineProperty(window, 'performance', {
  value: {
    memory: mockPerformanceMemory,
    now: vi.fn(() => Date.now()),
    getEntriesByType: vi.fn(() => []),
    mark: vi.fn(),
    measure: vi.fn(),
  },
  writable: true,
});

// Mock PerformanceObserver
Object.defineProperty(window, 'PerformanceObserver', {
  value: vi.fn().mockImplementation(() => ({
    observe: mockPerformanceObserver,
    disconnect: vi.fn(),
  })),
  writable: true,
});

describe('React Profiler', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    reactProfiler.clearMetrics();
    reactProfiler.enableProfiling();
  });

  afterEach(() => {
    reactProfiler.disableProfiling();
  });

  it('should enable and disable profiling', () => {
    expect(reactProfiler.isProfilingEnabled).toBe(true);
    reactProfiler.disableProfiling();
    expect(reactProfiler.isProfilingEnabled).toBe(false);
  });

  it('should track component render metrics', () => {
    const componentId = 'TestComponent';
    const renderTime = 25;

    // Simulate profiler callback
    reactProfiler.onRenderCallback(
      componentId,
      'update',
      renderTime,
      renderTime,
      1000,
      1025,
      new Set()
    );

    const profile = reactProfiler.getComponentProfile(componentId);
    expect(profile).toBeDefined();
    expect(profile?.metrics.renderCount).toBe(1);
    expect(profile?.metrics.lastRenderTime).toBe(renderTime);
  });

  it('should detect performance budget violations', () => {
    const componentId = 'SlowComponent';
    const slowRenderTime = 100; // Exceeds 50ms budget

    reactProfiler.onRenderCallback(
      componentId,
      'update',
      slowRenderTime,
      slowRenderTime,
      1000,
      1100,
      new Set()
    );

    const profile = reactProfiler.getComponentProfile(componentId);
    expect(profile?.violations.length).toBeGreaterThan(0);
    expect(profile?.violations[0]).toContain('Render time');
  });

  it('should generate performance recommendations', () => {
    const componentId = 'TestComponent';
    
    // Create multiple slow renders
    for (let i = 0; i < 6; i++) {
      reactProfiler.onRenderCallback(
        componentId,
        'update',
        80, // Slow render
        80,
        1000 + i * 100,
        1080 + i * 100,
        new Set()
      );
    }

    const profile = reactProfiler.getComponentProfile(componentId);
    expect(profile?.recommendations.length).toBeGreaterThan(0);
    expect(profile?.recommendations[0]).toContain('React.memo');
  });

  it('should track memory usage', () => {
    const componentId = 'MemoryComponent';
    
    reactProfiler.onRenderCallback(
      componentId,
      'mount',
      20,
      20,
      1000,
      1020,
      new Set()
    );

    const profile = reactProfiler.getComponentProfile(componentId);
    expect(profile?.metrics.memorySizeEstimate).toBe(mockPerformanceMemory.usedJSHeapSize);
  });

  it('should provide performance summary', () => {
    const componentId = 'SummaryComponent';
    
    reactProfiler.onRenderCallback(
      componentId,
      'update',
      30,
      30,
      1000,
      1030,
      new Set()
    );

    const summary = reactProfiler.getPerformanceSummary();
    expect(summary.totalComponents).toBe(1);
    expect(summary.averageRenderTime).toBe(30);
    expect(summary.optimizationScore).toBeLessThan(100);
  });
});

describe('Memory Optimizer', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    memoryOptimizer.stopMonitoring();
  });

  afterEach(() => {
    memoryOptimizer.stopMonitoring();
  });

  it('should start and stop monitoring', () => {
    memoryOptimizer.startMonitoring(100);
    expect(memoryOptimizer.isMonitoring).toBe(true);
    
    memoryOptimizer.stopMonitoring();
    expect(memoryOptimizer.isMonitoring).toBe(false);
  });

  it('should register and unregister components', () => {
    const componentId = 'TestComponent';
    
    memoryOptimizer.registerComponent(componentId);
    expect(memoryOptimizer.componentDetectors.has(componentId)).toBe(true);
    
    memoryOptimizer.unregisterComponent(componentId);
    expect(memoryOptimizer.componentDetectors.has(componentId)).toBe(false);
  });

  it('should track component memory usage', () => {
    const componentId = 'MemoryComponent';
    
    memoryOptimizer.registerComponent(componentId);
    memoryOptimizer.recordComponentMemory(componentId);
    
    const detector = memoryOptimizer.componentDetectors.get(componentId);
    expect(detector?.samples.length).toBe(1);
    expect(detector?.samples[0].usedJSHeapSize).toBe(mockPerformanceMemory.usedJSHeapSize);
  });

  it('should provide memory status', () => {
    const status = memoryOptimizer.getMemoryStatus();
    
    expect(status.current).toBe(mockPerformanceMemory.usedJSHeapSize);
    expect(status.status).toBe('healthy');
    expect(status.recommendations).toBeInstanceOf(Array);
  });

  it('should support object pooling', () => {
    const poolName = 'TestPool';
    const factory = () => ({ id: Math.random() });
    
    const obj1 = memoryOptimizer.getFromPool(poolName, factory);
    memoryOptimizer.returnToPool(poolName, obj1);
    
    const obj2 = memoryOptimizer.getFromPool(poolName, factory);
    expect(obj2).toBe(obj1); // Should reuse pooled object
  });

  it('should detect memory leaks', () => {
    const componentId = 'LeakyComponent';
    
    memoryOptimizer.registerComponent(componentId, 1024 * 1024); // 1MB threshold
    
    // Simulate memory growth
    const originalUsedHeapSize = mockPerformanceMemory.usedJSHeapSize;
    for (let i = 0; i < 10; i++) {
      mockPerformanceMemory.usedJSHeapSize += 2 * 1024 * 1024; // 2MB growth per sample
      memoryOptimizer.recordComponentMemory(componentId);
    }
    
    const leakReport = memoryOptimizer.getMemoryLeakReport();
    expect(leakReport.totalLeaks).toBeGreaterThan(0);
    expect(leakReport.leakingComponents).toContain(componentId);
    
    // Restore original value
    mockPerformanceMemory.usedJSHeapSize = originalUsedHeapSize;
  });
});

describe('Performance Budget Monitor', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    performanceBudgetMonitor.clearViolations();
  });

  it('should set and get component budgets', () => {
    const componentType = 'calculator';
    const budget = { maxRenderTime: 100 };
    
    performanceBudgetMonitor.setBudget(componentType, budget);
    const retrievedBudget = performanceBudgetMonitor.getBudget(componentType);
    
    expect(retrievedBudget.maxRenderTime).toBe(100);
  });

  it('should record performance metrics', () => {
    const componentId = 'TestComponent';
    
    performanceBudgetMonitor.recordMetric('render', 'time', 75, componentId);
    
    const report = performanceBudgetMonitor.getBudgetReport();
    expect(report.totalViolations).toBeGreaterThan(0); // Should violate 50ms default budget
  });

  it('should detect budget violations', () => {
    const componentId = 'SlowComponent';
    const slowRenderTime = 200; // Well above budget
    
    performanceBudgetMonitor.recordMetric('render', 'time', slowRenderTime, componentId);
    
    const violations = performanceBudgetMonitor.getViolationsByComponent(componentId);
    expect(violations.length).toBeGreaterThan(0);
    expect(violations[0].severity).toBe('critical');
  });

  it('should provide budget compliance report', () => {
    // Record some metrics
    performanceBudgetMonitor.recordMetric('render', 'time', 10, 'FastComponent');
    performanceBudgetMonitor.recordMetric('render', 'time', 100, 'SlowComponent');
    
    const report = performanceBudgetMonitor.getBudgetReport();
    expect(report.budgetCompliance).toBeLessThan(100);
    expect(report.severityDistribution).toHaveProperty('critical');
  });

  it('should group violations by severity', () => {
    performanceBudgetMonitor.recordMetric('render', 'time', 200, 'CriticalComponent');
    performanceBudgetMonitor.recordMetric('render', 'time', 80, 'HighComponent');
    performanceBudgetMonitor.recordMetric('render', 'time', 60, 'MediumComponent');
    
    const criticalViolations = performanceBudgetMonitor.getViolationsBySeverity('critical');
    const highViolations = performanceBudgetMonitor.getViolationsBySeverity('high');
    const mediumViolations = performanceBudgetMonitor.getViolationsBySeverity('medium');
    
    expect(criticalViolations.length).toBe(1);
    expect(highViolations.length).toBe(1);
    expect(mediumViolations.length).toBe(1);
  });
});

describe('Performance Recommendations Engine', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    performanceRecommendationsEngine.clearRecommendations();
  });

  it('should generate comprehensive recommendations', () => {
    // Setup test data
    const componentId = 'TestComponent';
    
    // Create slow rendering component
    reactProfiler.onRenderCallback(
      componentId,
      'update',
      100, // Slow render
      100,
      1000,
      1100,
      new Set()
    );
    
    const analysis = performanceRecommendationsEngine.generateRecommendations();
    
    expect(analysis.recommendations.length).toBeGreaterThan(0);
    expect(analysis.score).toBeLessThan(100);
    expect(analysis.issues.high).toBeGreaterThan(0);
  });

  it('should categorize recommendations correctly', () => {
    // Generate recommendations
    const analysis = performanceRecommendationsEngine.generateRecommendations();
    
    const reactRecommendations = performanceRecommendationsEngine.getRecommendationsByCategory('react_optimization');
    const memoryRecommendations = performanceRecommendationsEngine.getRecommendationsByCategory('memory_management');
    
    expect(Array.isArray(reactRecommendations)).toBe(true);
    expect(Array.isArray(memoryRecommendations)).toBe(true);
  });

  it('should prioritize recommendations', () => {
    // Create critical performance issue
    performanceBudgetMonitor.recordMetric('render', 'time', 500, 'CriticalComponent');
    
    const analysis = performanceRecommendationsEngine.generateRecommendations();
    const criticalRecommendations = performanceRecommendationsEngine.getRecommendationsByPriority('critical');
    
    expect(criticalRecommendations.length).toBeGreaterThan(0);
    expect(analysis.issues.critical).toBeGreaterThan(0);
  });

  it('should provide component-specific recommendations', () => {
    const componentId = 'SpecificComponent';
    
    reactProfiler.onRenderCallback(
      componentId,
      'update',
      80,
      80,
      1000,
      1080,
      new Set()
    );
    
    performanceRecommendationsEngine.generateRecommendations();
    const componentRecommendations = performanceRecommendationsEngine.getRecommendationsByComponent(componentId);
    
    expect(componentRecommendations.length).toBeGreaterThan(0);
    expect(componentRecommendations[0].component).toBe(componentId);
  });

  it('should include implementation details', () => {
    const analysis = performanceRecommendationsEngine.generateRecommendations();
    
    if (analysis.recommendations.length > 0) {
      const recommendation = analysis.recommendations[0];
      expect(recommendation.implementation.steps).toBeInstanceOf(Array);
      expect(recommendation.implementation.codeExamples).toBeInstanceOf(Array);
      expect(recommendation.implementation.resources).toBeInstanceOf(Array);
    }
  });

  it('should track analysis history', () => {
    performanceRecommendationsEngine.generateRecommendations();
    performanceRecommendationsEngine.generateRecommendations();
    
    const history = performanceRecommendationsEngine.getAnalysisHistory();
    expect(history.length).toBe(2);
  });

  it('should calculate estimated impact', () => {
    const analysis = performanceRecommendationsEngine.generateRecommendations();
    
    expect(analysis.summary.estimatedImpact).toBeGreaterThanOrEqual(0);
    expect(analysis.summary.estimatedImpact).toBeLessThanOrEqual(10);
    expect(analysis.summary.implementationEffort).toBeDefined();
  });
});

describe('Performance Optimization Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    reactProfiler.clearMetrics();
    memoryOptimizer.stopMonitoring();
    performanceBudgetMonitor.clearViolations();
    performanceRecommendationsEngine.clearRecommendations();
  });

  it('should integrate all monitoring systems', () => {
    const componentId = 'IntegratedComponent';
    
    // Enable monitoring
    reactProfiler.enableProfiling();
    memoryOptimizer.startMonitoring(100);
    memoryOptimizer.registerComponent(componentId);
    
    // Simulate component activity
    reactProfiler.onRenderCallback(
      componentId,
      'update',
      75, // Slow render
      75,
      1000,
      1075,
      new Set()
    );
    
    memoryOptimizer.recordComponentMemory(componentId);
    performanceBudgetMonitor.recordMetric('render', 'time', 75, componentId);
    
    // Generate comprehensive analysis
    const analysis = performanceRecommendationsEngine.generateRecommendations();
    
    expect(analysis.recommendations.length).toBeGreaterThan(0);
    expect(analysis.score).toBeLessThan(100);
    
    // Cleanup
    reactProfiler.disableProfiling();
    memoryOptimizer.stopMonitoring();
  });

  it('should handle performance optimization lifecycle', () => {
    const componentId = 'LifecycleComponent';
    
    // Component mount
    memoryOptimizer.registerComponent(componentId);
    
    // Component renders
    for (let i = 0; i < 5; i++) {
      reactProfiler.onRenderCallback(
        componentId,
        'update',
        50 + i * 10, // Progressively slower
        50 + i * 10,
        1000 + i * 100,
        1050 + i * 110,
        new Set()
      );
      
      memoryOptimizer.recordComponentMemory(componentId);
    }
    
    // Generate recommendations
    const analysis = performanceRecommendationsEngine.generateRecommendations();
    expect(analysis.recommendations.length).toBeGreaterThan(0);
    
    // Component unmount
    memoryOptimizer.unregisterComponent(componentId);
    
    expect(memoryOptimizer.componentDetectors.has(componentId)).toBe(false);
  });
});

describe('Performance Hooks', () => {
  it('should provide performance monitoring hooks', () => {
    // This would require React testing utilities
    // For now, just verify the functions exist
    expect(typeof reactProfiler.onRenderCallback).toBe('function');
    expect(typeof memoryOptimizer.registerComponent).toBe('function');
    expect(typeof performanceBudgetMonitor.recordMetric).toBe('function');
  });
});

describe('Performance Utilities', () => {
  it('should format bytes correctly', () => {
    const formatter = (bytes: number) => {
      if (bytes === 0) return '0 Bytes';
      const k = 1024;
      const sizes = ['Bytes', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };
    
    expect(formatter(0)).toBe('0 Bytes');
    expect(formatter(1024)).toBe('1 KB');
    expect(formatter(1024 * 1024)).toBe('1 MB');
    expect(formatter(1024 * 1024 * 1024)).toBe('1 GB');
  });

  it('should calculate performance scores', () => {
    const calculateScore = (violations: number, total: number) => {
      if (total === 0) return 100;
      return Math.max(0, ((total - violations) / total) * 100);
    };
    
    expect(calculateScore(0, 10)).toBe(100);
    expect(calculateScore(5, 10)).toBe(50);
    expect(calculateScore(10, 10)).toBe(0);
  });

  it('should detect performance patterns', () => {
    const detectSlowRenders = (renderTimes: number[], threshold: number = 50) => {
      return renderTimes.filter(time => time > threshold);
    };
    
    const renderTimes = [10, 25, 60, 30, 80, 15];
    const slowRenders = detectSlowRenders(renderTimes);
    
    expect(slowRenders).toEqual([60, 80]);
  });
});