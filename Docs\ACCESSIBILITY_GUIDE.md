# Accessibility Guide for Nirmaan AI Construction Calculator

## Overview

This guide provides comprehensive accessibility implementation details for the Nirmaan AI Construction Calculator platform, ensuring full WCAG 2.1 AA compliance and enhanced user experience for all users, including those with disabilities.

## Table of Contents

1. [Accessibility Features](#accessibility-features)
2. [Implementation Guide](#implementation-guide)
3. [Testing and Validation](#testing-and-validation)
4. [Developer Guidelines](#developer-guidelines)
5. [User Guide](#user-guide)
6. [Maintenance](#maintenance)

## Accessibility Features

### 🎯 Core Accessibility Systems

Our platform implements multiple comprehensive accessibility systems:

#### 1. Focus Management System
- **Advanced Focus Trapping**: Automatic focus trapping for modals and overlays
- **Focus History**: Maintains focus history for restoration
- **Smart Focus Navigation**: Intelligent next/previous element focusing
- **Visual Focus Indicators**: Enhanced focus indicators that meet WCAG requirements
- **Focus Announcements**: Screen reader announcements for focus changes

#### 2. Keyboard Navigation System
- **Global Shortcuts**: F1 for help, Ctrl+Home/End for page navigation
- **Context-Specific Shortcuts**: Calculator-specific shortcuts (Ctrl+Enter to calculate)
- **Form Navigation**: Alt+N/P for next/previous field, Home/End for first/last
- **Modal Navigation**: Escape to close, tab trapping within modals
- **Help System**: Interactive keyboard shortcuts help (F1 or Shift+?)

#### 3. Screen Reader Support
- **ARIA Enhancement**: Comprehensive ARIA labels, roles, and properties
- **Live Regions**: Polite and assertive announcements for dynamic content
- **Form Enhancement**: Automatic label association and error announcements
- **Table Enhancement**: Proper table headers and relationships
- **Modal Enhancement**: Proper dialog structure and announcements

#### 4. Visual Accessibility
- **High Contrast Themes**: Light and dark high contrast modes
- **Theme Management**: Automatic system preference detection
- **Font Size Scaling**: Small, normal, large, and extra-large options
- **Motion Preferences**: Respect for reduced motion preferences
- **Color Contrast**: Automatic contrast ratio validation

#### 5. Voice Navigation
- **Speech Recognition**: Voice commands for navigation and actions
- **Speech Synthesis**: Text-to-speech for content reading
- **Voice Commands**: "go home", "calculate cost", "next element", etc.
- **Continuous Listening**: Optional continuous voice recognition
- **Voice Help**: "help" command to list available voice commands

#### 6. Mobile Touch Accessibility
- **Minimum Touch Targets**: 44px minimum touch target size
- **Touch Gestures**: Tap, double-tap, long-press, swipe gestures
- **Haptic Feedback**: Vibration feedback for touch interactions
- **Touch Controls**: Floating accessibility controls for mobile
- **Voice Input**: Voice input for form fields

#### 7. Testing and Validation
- **Automated Testing**: Comprehensive accessibility audit system
- **WCAG Compliance**: Automated WCAG 2.1 AA validation
- **Color Contrast**: Automatic contrast ratio checking
- **Report Generation**: Detailed accessibility reports
- **Real-time Monitoring**: Continuous accessibility monitoring

## Implementation Guide

### Quick Start

```typescript
import { 
  focusManager, 
  screenReader, 
  keyboardNav, 
  themeManager,
  voiceNav,
  mobileTouchAccessibility,
  accessibilityTester
} from '@/lib/accessibility';

// Initialize accessibility systems
const initializeAccessibility = () => {
  // Enable keyboard navigation
  keyboardNav.activateContext('calculator');
  
  // Enable voice navigation if supported
  if (voiceNav.isSupported()) {
    voiceNav.enable();
  }
  
  // Enable mobile touch accessibility
  if (mobileTouchAccessibility.isTouchDevice()) {
    mobileTouchAccessibility.enable();
  }
  
  // Apply saved theme preferences
  themeManager.setThemeMode('light'); // or user preference
};
```

### Focus Management

```typescript
import { useFocusManagement } from '@/lib/accessibility/focus-management';

const MyComponent = () => {
  const { focus, createFocusTrap, announce } = useFocusManagement();
  
  const handleOpenModal = () => {
    const modal = document.getElementById('my-modal');
    if (modal) {
      const trap = createFocusTrap(modal);
      trap.activate();
      announce('Modal opened');
    }
  };
  
  return (
    <button onClick={handleOpenModal} aria-label="Open settings modal">
      Settings
    </button>
  );
};
```

### Screen Reader Enhancement

```typescript
import { useScreenReader } from '@/lib/accessibility/screen-reader';

const FormComponent = () => {
  const { enhanceFormField, announceValidationError } = useScreenReader();
  
  useEffect(() => {
    const field = document.getElementById('plot-size');
    if (field) {
      enhanceFormField(field, 'Plot Size', {
        required: true,
        helpText: 'Enter the plot size in square feet'
      });
    }
  }, []);
  
  const handleValidationError = (field: string, error: string) => {
    announceValidationError(field, error);
  };
  
  return (
    <form>
      <input
        id="plot-size"
        type="number"
        placeholder="Enter plot size"
        aria-describedby="plot-size-help"
      />
      <div id="plot-size-help">Enter the plot size in square feet</div>
    </form>
  );
};
```

### Keyboard Navigation

```typescript
import { useKeyboardNavigation } from '@/lib/accessibility/keyboard-navigation';

const CalculatorComponent = () => {
  const { registerContext, activateContext } = useKeyboardNavigation();
  
  useEffect(() => {
    // Register calculator-specific shortcuts
    registerContext({
      name: 'calculator',
      priority: 100,
      shortcuts: [
        {
          key: 'Enter',
          ctrlKey: true,
          description: 'Calculate construction cost',
          action: () => handleCalculate(),
          enabled: true
        },
        {
          key: 'r',
          ctrlKey: true,
          description: 'Reset form',
          action: () => handleReset(),
          enabled: true
        }
      ]
    });
    
    activateContext('calculator');
    
    return () => deactivateContext('calculator');
  }, []);
  
  return (
    <div role="main" aria-label="Construction cost calculator">
      {/* Calculator form */}
    </div>
  );
};
```

### Theme Management

```typescript
import { useAccessibilityTheme } from '@/lib/accessibility/theme-manager';

const ThemeControls = () => {
  const { 
    getConfig, 
    setThemeMode, 
    toggleHighContrast, 
    setFontSize 
  } = useAccessibilityTheme();
  
  const config = getConfig();
  
  return (
    <div role="group" aria-label="Accessibility settings">
      <button onClick={toggleHighContrast}>
        {config.mode.includes('high-contrast') ? 'Disable' : 'Enable'} High Contrast
      </button>
      
      <select
        value={config.fontSize}
        onChange={(e) => setFontSize(e.target.value as any)}
        aria-label="Font size"
      >
        <option value="small">Small</option>
        <option value="normal">Normal</option>
        <option value="large">Large</option>
        <option value="extra-large">Extra Large</option>
      </select>
    </div>
  );
};
```

### Voice Navigation

```typescript
import { useVoiceNavigation } from '@/lib/accessibility/voice-navigation';

const VoiceComponent = () => {
  const { 
    isSupported, 
    enable, 
    startListening, 
    registerCommand 
  } = useVoiceNavigation();
  
  useEffect(() => {
    if (isSupported()) {
      // Register custom voice commands
      registerCommand({
        phrase: 'show results',
        action: () => scrollToResults(),
        description: 'Scroll to calculation results',
        enabled: true
      });
    }
  }, []);
  
  return (
    <div>
      {isSupported() && (
        <button onClick={() => { enable(); startListening(); }}>
          🎤 Enable Voice Navigation
        </button>
      )}
    </div>
  );
};
```

### Mobile Touch Enhancement

```typescript
import { useMobileTouchAccessibility } from '@/lib/accessibility/mobile-touch';

const MobileComponent = () => {
  const { 
    isTouchDevice, 
    enhanceElement, 
    registerGesture,
    createTouchControls
  } = useMobileTouchAccessibility();
  
  useEffect(() => {
    if (isTouchDevice()) {
      const container = document.getElementById('calculator-container');
      if (container) {
        createTouchControls(container);
      }
      
      // Enhance all buttons
      const buttons = document.querySelectorAll('button');
      buttons.forEach(button => enhanceElement(button as HTMLElement));
    }
  }, []);
  
  return (
    <div id="calculator-container">
      {/* Your mobile-optimized content */}
    </div>
  );
};
```

### Accessibility Testing

```typescript
import { useAccessibilityTesting } from '@/lib/accessibility/testing';

const TestingComponent = () => {
  const { audit, generateReport } = useAccessibilityTesting();
  
  const runAccessibilityAudit = async () => {
    const results = await audit({
      level: 'AA',
      selector: '#main-content'
    });
    
    console.log('Accessibility Score:', results.summary.score);
    console.log('Violations:', results.violations.length);
    console.log('Warnings:', results.warnings.length);
    
    const report = generateReport(results);
    console.log(report);
  };
  
  return (
    <button onClick={runAccessibilityAudit}>
      Run Accessibility Audit
    </button>
  );
};
```

## Testing and Validation

### Automated Testing

Our platform includes comprehensive automated accessibility testing:

```bash
# Run accessibility tests
npm run test:accessibility

# Generate accessibility report
npm run accessibility:audit

# Check color contrast
npm run accessibility:contrast
```

### Manual Testing Checklist

#### Keyboard Navigation
- [ ] All interactive elements are keyboard accessible
- [ ] Tab order is logical and follows visual flow
- [ ] Focus indicators are visible and clear
- [ ] Keyboard shortcuts work as expected
- [ ] No keyboard traps (except intentional focus traps)

#### Screen Reader Testing
- [ ] All content is announced properly
- [ ] Form labels are associated correctly
- [ ] Error messages are announced
- [ ] Dynamic content changes are announced
- [ ] Page structure is logical with headings

#### Visual Accessibility
- [ ] Color contrast meets WCAG AA standards (4.5:1)
- [ ] High contrast mode works properly
- [ ] Text scales properly up to 200%
- [ ] Information is not conveyed by color alone
- [ ] Focus indicators are visible

#### Mobile Accessibility
- [ ] Touch targets are at least 44px
- [ ] Gestures work as expected
- [ ] Haptic feedback is appropriate
- [ ] Voice input works on supported devices
- [ ] Screen reader works on mobile

### Testing Tools

#### Browser Extensions
- **axe DevTools**: Automated accessibility scanning
- **WAVE**: Visual accessibility feedback
- **Lighthouse**: Accessibility audit scores
- **Colour Contrast Analyser**: Color contrast checking

#### Screen Readers
- **NVDA** (Windows): Free screen reader
- **JAWS** (Windows): Professional screen reader
- **VoiceOver** (macOS/iOS): Built-in screen reader
- **TalkBack** (Android): Built-in screen reader

#### Mobile Testing
- **iOS VoiceOver**: Test on actual iOS devices
- **Android TalkBack**: Test on actual Android devices
- **Voice Control**: Test voice navigation features

## Developer Guidelines

### Code Standards

#### HTML Structure
```html
<!-- Good: Semantic HTML with proper ARIA -->
<main role="main" aria-label="Construction calculator">
  <h1>Calculate Construction Cost</h1>
  
  <form aria-label="Project details form">
    <fieldset>
      <legend>Basic Information</legend>
      
      <label for="plot-size">
        Plot Size (sq ft) <span aria-label="required">*</span>
      </label>
      <input
        id="plot-size"
        type="number"
        required
        aria-describedby="plot-size-help plot-size-error"
        aria-invalid="false"
      />
      <div id="plot-size-help">Enter the total plot area in square feet</div>
      <div id="plot-size-error" role="alert" aria-live="polite"></div>
    </fieldset>
  </form>
</main>
```

#### React Components
```typescript
// Good: Accessible React component
const AccessibleButton: React.FC<{
  children: React.ReactNode;
  onClick: () => void;
  loading?: boolean;
  disabled?: boolean;
  ariaLabel?: string;
}> = ({ children, onClick, loading, disabled, ariaLabel }) => {
  return (
    <button
      onClick={onClick}
      disabled={disabled || loading}
      aria-label={ariaLabel}
      aria-busy={loading}
      className="min-h-[44px] min-w-[44px] focus:ring-2 focus:ring-blue-500"
    >
      {loading ? (
        <>
          <span className="sr-only">Loading...</span>
          <div className="spinner" aria-hidden="true" />
        </>
      ) : (
        children
      )}
    </button>
  );
};
```

#### CSS Best Practices
```css
/* Focus indicators */
.focus-visible {
  outline: 3px solid #3b82f6;
  outline-offset: 2px;
}

/* High contrast support */
@media (prefers-contrast: high) {
  .button {
    border: 2px solid;
    background: var(--high-contrast-bg);
    color: var(--high-contrast-fg);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Text scaling support */
@media (min-width: 320px) {
  .text-base {
    font-size: clamp(1rem, 2.5vw, 1.25rem);
  }
}
```

### Best Practices

#### 1. Use Semantic HTML
- Use appropriate HTML elements (`button`, `nav`, `main`, `article`)
- Don't use `div` or `span` for interactive elements
- Use headings hierarchically (h1 → h2 → h3)

#### 2. Provide Text Alternatives
- All images must have `alt` attributes
- Use empty `alt=""` for decorative images
- Provide captions for videos
- Provide transcripts for audio

#### 3. Ensure Keyboard Accessibility
- All functionality must be keyboard accessible
- Use `tabindex="0"` for custom interactive elements
- Never use positive `tabindex` values
- Implement proper focus management

#### 4. Use ARIA Appropriately
- Use ARIA to enhance, not replace semantic HTML
- Test with actual screen readers
- Keep ARIA simple and purposeful
- Update ARIA states dynamically

#### 5. Design for All Users
- Ensure 4.5:1 color contrast minimum
- Don't rely on color alone for information
- Make touch targets at least 44px
- Support zoom up to 200%

### Common Mistakes to Avoid

#### ❌ Bad Practices
```typescript
// Don't: Div soup without semantics
<div onClick={handleClick}>Click me</div>

// Don't: Missing labels
<input type="text" placeholder="Name" />

// Don't: Inappropriate ARIA
<div role="button" onClick={handleClick}>
  <div role="button">Nested button</div>
</div>

// Don't: Keyboard traps
element.addEventListener('keydown', (e) => {
  if (e.key === 'Tab') {
    e.preventDefault(); // Traps focus!
  }
});
```

#### ✅ Good Practices
```typescript
// Do: Semantic HTML
<button onClick={handleClick}>Click me</button>

// Do: Proper labels
<label htmlFor="name">
  Name <span className="required">*</span>
</label>
<input id="name" type="text" required />

// Do: Proper ARIA usage
<button
  aria-label="Close dialog"
  onClick={handleClose}
>
  ×
</button>

// Do: Proper focus management
const trapFocus = (e: KeyboardEvent) => {
  if (e.key === 'Tab') {
    // Implement proper focus trapping logic
    focusManager.trapTabKey(e, containerElement);
  }
};
```

## User Guide

### Keyboard Shortcuts

#### Global Shortcuts
- **F1** or **Shift + ?**: Show keyboard shortcuts help
- **Ctrl + Home**: Go to top of page
- **Ctrl + End**: Go to bottom of page
- **Tab**: Navigate to next element
- **Shift + Tab**: Navigate to previous element

#### Calculator Shortcuts
- **Ctrl + Enter**: Calculate construction cost
- **Ctrl + R**: Reset calculator form
- **Alt + ↓**: Open dropdown/select
- **Ctrl + Shift + P**: Export PDF report

#### Form Navigation
- **Alt + N**: Next form field
- **Alt + P**: Previous form field
- **Home**: First form field
- **End**: Last form field

#### Navigation Shortcuts
- **H**: Go to home page
- **C**: Go to calculator
- **M**: Open main menu

### Screen Reader Support

Our platform is fully compatible with popular screen readers:

- **NVDA** (Windows)
- **JAWS** (Windows)
- **VoiceOver** (macOS/iOS)
- **TalkBack** (Android)

#### Screen Reader Features
- Comprehensive ARIA labeling
- Live region announcements
- Form field descriptions
- Error message announcements
- Dynamic content updates

### Voice Navigation

If your browser supports speech recognition, you can use voice commands:

#### Available Voice Commands
- **"go home"**: Navigate to home page
- **"go to calculator"**: Navigate to calculator
- **"calculate cost"**: Run calculation
- **"reset form"**: Reset calculator form
- **"next element"**: Move focus to next element
- **"help"**: Show available commands
- **"stop listening"**: Disable voice recognition

### Mobile Accessibility

#### Touch Gestures
- **Single Tap**: Activate element
- **Double Tap**: Alternative activation
- **Long Press**: Show context menu
- **Swipe Left/Right**: Navigate between cards
- **Swipe Up/Down**: Scroll content

#### Mobile Controls
- **🎤 Voice Control**: Toggle voice navigation
- **📖 Reading Mode**: Enhanced text display
- **🌓 High Contrast**: Toggle high contrast theme
- **A+/A-**: Increase/decrease text size

### Accessibility Settings

#### Theme Options
- **Light Theme**: Standard light theme
- **Dark Theme**: Dark theme for low light
- **High Contrast**: Enhanced contrast for visibility
- **High Contrast Dark**: Dark high contrast theme

#### Text Size Options
- **Small**: 14px base font size
- **Normal**: 16px base font size (default)
- **Large**: 18px base font size
- **Extra Large**: 20px base font size

#### Motion Settings
- **Auto**: Default animations
- **Reduced**: Reduced motion (respects system preference)
- **None**: No animations

## Maintenance

### Regular Testing Schedule

#### Weekly
- [ ] Automated accessibility tests
- [ ] Color contrast validation
- [ ] Keyboard navigation check

#### Monthly
- [ ] Screen reader testing (NVDA/VoiceOver)
- [ ] Mobile accessibility testing
- [ ] Voice navigation testing

#### Quarterly
- [ ] Full manual accessibility audit
- [ ] User testing with assistive technology users
- [ ] Update accessibility documentation

### Updating Accessibility Features

When adding new features:

1. **Design Phase**
   - Consider accessibility from the start
   - Ensure color contrast meets requirements
   - Plan keyboard navigation flow

2. **Development Phase**
   - Use semantic HTML
   - Add appropriate ARIA labels
   - Test with keyboard navigation
   - Test with screen readers

3. **Testing Phase**
   - Run automated accessibility tests
   - Manual keyboard testing
   - Screen reader testing
   - Mobile accessibility testing

4. **Release Phase**
   - Include accessibility notes in release
   - Update documentation
   - Monitor for accessibility issues

### Reporting Accessibility Issues

If you encounter accessibility issues:

1. **Immediate Issues**
   - Document the issue with steps to reproduce
   - Include assistive technology used
   - Note impact on user experience

2. **Contact Information**
   - Email: <EMAIL>
   - Include "Accessibility Issue" in subject line
   - Provide detailed description and screenshots

3. **Issue Resolution**
   - We aim to resolve critical issues within 24 hours
   - Non-critical issues within 1 week
   - You'll receive updates on progress

## Resources

### Documentation
- [WCAG 2.1 Guidelines](https://www.w3.org/WAI/WCAG21/quickref/)
- [ARIA Authoring Practices](https://www.w3.org/WAI/ARIA/apg/)
- [MDN Accessibility](https://developer.mozilla.org/en-US/docs/Web/Accessibility)

### Testing Tools
- [axe-core](https://github.com/dequelabs/axe-core)
- [WAVE Web Accessibility Evaluator](https://wave.webaim.org/)
- [Color Contrast Analyzers](https://www.tpgi.com/color-contrast-checker/)

### Screen Readers
- [NVDA](https://www.nvaccess.org/) (Free)
- [JAWS](https://www.freedomscientific.com/products/software/jaws/)
- [VoiceOver](https://support.apple.com/guide/voiceover/) (Built-in on Apple devices)

---

## Conclusion

The Nirmaan AI Construction Calculator is committed to providing an accessible experience for all users. Our comprehensive accessibility implementation ensures WCAG 2.1 AA compliance while going beyond minimum requirements to create an inclusive platform.

For questions, suggestions, or accessibility support, please contact our accessibility <NAME_EMAIL>.

**Last Updated**: July 16, 2025  
**Version**: 1.0.0  
**WCAG Compliance**: 2.1 AA