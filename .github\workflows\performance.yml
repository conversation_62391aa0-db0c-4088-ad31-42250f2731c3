name: ⚡ Performance Monitoring

on:
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]
  schedule:
    # Run performance tests daily at 4 AM UTC
    - cron: '0 4 * * *'
  workflow_dispatch:
    inputs:
      test_type:
        description: 'Type of performance test'
        required: true
        default: 'all'
        type: choice
        options:
          - all
          - lighthouse
          - bundle-size
          - load-test
          - visual-regression

env:
  NODE_VERSION: '18'
  PERFORMANCE_BUDGET_CPU: 3000
  PERFORMANCE_BUDGET_MEMORY: 50
  PERFORMANCE_BUDGET_BUNDLE: 500

jobs:
  # Lighthouse Performance Audit
  lighthouse-audit:
    name: 🔍 Lighthouse Performance Audit
    runs-on: ubuntu-latest
    timeout-minutes: 20
    
    outputs:
      performance-score: ${{ steps.lighthouse.outputs.performance-score }}
      accessibility-score: ${{ steps.lighthouse.outputs.accessibility-score }}
      seo-score: ${{ steps.lighthouse.outputs.seo-score }}
      best-practices-score: ${{ steps.lighthouse.outputs.best-practices-score }}
      
    steps:
      - name: 📦 Checkout code
        uses: actions/checkout@v4
        
      - name: 🔧 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 📥 Install dependencies
        run: npm ci --prefer-offline --no-audit --progress=false
        
      - name: 🏗️ Build application
        run: npm run build
        
      - name: 🚀 Start application
        run: npm start &
        env:
          PORT: 3000
          
      - name: ⏳ Wait for application
        run: npx wait-on http://localhost:3000 --timeout 60000
        
      - name: 📥 Install Lighthouse CI
        run: npm install -g @lhci/cli@0.12.x
        
      - name: ⚡ Run Lighthouse audit
        id: lighthouse
        run: |
          # Create comprehensive Lighthouse configuration
          cat > lighthouserc.performance.js << 'EOF'
          module.exports = {
            ci: {
              collect: {
                numberOfRuns: 5,
                startServerCommand: 'npm start',
                url: ['http://localhost:3000', 'http://localhost:3000/calculator'],
                settings: {
                  chromeFlags: '--no-sandbox --disable-dev-shm-usage',
                  emulatedFormFactor: 'desktop',
                  throttling: {
                    rttMs: 40,
                    throughputKbps: 10240,
                    cpuSlowdownMultiplier: 1
                  }
                }
              },
              assert: {
                assertions: {
                  'categories:performance': ['error', { minScore: 0.7 }],
                  'categories:accessibility': ['error', { minScore: 0.9 }],
                  'categories:best-practices': ['error', { minScore: 0.8 }],
                  'categories:seo': ['error', { minScore: 0.8 }],
                  'first-contentful-paint': ['error', { maxNumericValue: 2500 }],
                  'largest-contentful-paint': ['error', { maxNumericValue: 4000 }],
                  'cumulative-layout-shift': ['error', { maxNumericValue: 0.1 }],
                  'speed-index': ['error', { maxNumericValue: 4000 }],
                  'interactive': ['error', { maxNumericValue: 5000 }]
                }
              },
              upload: {
                target: 'temporary-public-storage',
              }
            }
          };
          EOF
          
          # Run Lighthouse audit
          lhci autorun --config=lighthouserc.performance.js
          
          # Extract scores from the report
          PERF_SCORE=$(jq -r '.[] | select(.url | contains("localhost:3000")) | .categories.performance.score * 100' .lighthouseci/lhr-*.json | head -1)
          A11Y_SCORE=$(jq -r '.[] | select(.url | contains("localhost:3000")) | .categories.accessibility.score * 100' .lighthouseci/lhr-*.json | head -1)
          SEO_SCORE=$(jq -r '.[] | select(.url | contains("localhost:3000")) | .categories.seo.score * 100' .lighthouseci/lhr-*.json | head -1)
          BP_SCORE=$(jq -r '.[] | select(.url | contains("localhost:3000")) | .categories."best-practices".score * 100' .lighthouseci/lhr-*.json | head -1)
          
          echo "performance-score=$PERF_SCORE" >> $GITHUB_OUTPUT
          echo "accessibility-score=$A11Y_SCORE" >> $GITHUB_OUTPUT
          echo "seo-score=$SEO_SCORE" >> $GITHUB_OUTPUT
          echo "best-practices-score=$BP_SCORE" >> $GITHUB_OUTPUT
          
          echo "Performance Score: $PERF_SCORE"
          echo "Accessibility Score: $A11Y_SCORE"
          echo "SEO Score: $SEO_SCORE"
          echo "Best Practices Score: $BP_SCORE"
          
      - name: 📊 Core Web Vitals Analysis
        run: |
          # Extract Core Web Vitals metrics
          FCP=$(jq -r '.[] | select(.url | contains("localhost:3000")) | .audits."first-contentful-paint".numericValue' .lighthouseci/lhr-*.json | head -1)
          LCP=$(jq -r '.[] | select(.url | contains("localhost:3000")) | .audits."largest-contentful-paint".numericValue' .lighthouseci/lhr-*.json | head -1)
          CLS=$(jq -r '.[] | select(.url | contains("localhost:3000")) | .audits."cumulative-layout-shift".numericValue' .lighthouseci/lhr-*.json | head -1)
          FID=$(jq -r '.[] | select(.url | contains("localhost:3000")) | .audits."max-potential-fid".numericValue' .lighthouseci/lhr-*.json | head -1)
          
          echo "Core Web Vitals:"
          echo "- First Contentful Paint: ${FCP}ms"
          echo "- Largest Contentful Paint: ${LCP}ms"
          echo "- Cumulative Layout Shift: $CLS"
          echo "- First Input Delay: ${FID}ms"
          
          # Check against thresholds
          if (( $(echo "$FCP > 1800" | bc -l) )); then
            echo "⚠️ FCP exceeds threshold (1800ms)"
          fi
          
          if (( $(echo "$LCP > 2500" | bc -l) )); then
            echo "⚠️ LCP exceeds threshold (2500ms)"
          fi
          
          if (( $(echo "$CLS > 0.1" | bc -l) )); then
            echo "⚠️ CLS exceeds threshold (0.1)"
          fi
          
      - name: 📤 Upload Lighthouse reports
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: lighthouse-reports
          path: |
            .lighthouseci/
            lighthouse-report.html
          retention-days: 30

  # Bundle Size Analysis
  bundle-analysis:
    name: 📦 Bundle Size Analysis
    runs-on: ubuntu-latest
    timeout-minutes: 15
    
    outputs:
      bundle-size: ${{ steps.bundle-size.outputs.size }}
      size-change: ${{ steps.bundle-size.outputs.change }}
      
    steps:
      - name: 📦 Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          
      - name: 🔧 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 📥 Install dependencies
        run: npm ci --prefer-offline --no-audit --progress=false
        
      - name: 🏗️ Build application
        run: npm run build
        
      - name: 📦 Bundle size analysis
        id: bundle-size
        run: |
          # Install bundle analyzer
          npm install -g @next/bundle-analyzer
          
          # Generate bundle analysis
          npm run bundle-report
          
          # Calculate bundle size
          BUNDLE_SIZE=$(find .next/static/chunks -name "*.js" -exec wc -c {} + | tail -n 1 | awk '{print $1}')
          BUNDLE_SIZE_MB=$(echo "scale=2; $BUNDLE_SIZE / 1024 / 1024" | bc)
          
          echo "size=$BUNDLE_SIZE_MB" >> $GITHUB_OUTPUT
          echo "Total bundle size: ${BUNDLE_SIZE_MB}MB"
          
          # Compare with previous build (if available)
          if [ -f reports/baseline.json ]; then
            BASELINE_SIZE=$(jq -r '.bundleSize' reports/baseline.json)
            SIZE_CHANGE=$(echo "scale=2; $BUNDLE_SIZE_MB - $BASELINE_SIZE" | bc)
            echo "change=$SIZE_CHANGE" >> $GITHUB_OUTPUT
            
            if (( $(echo "$SIZE_CHANGE > 0" | bc -l) )); then
              echo "⚠️ Bundle size increased by ${SIZE_CHANGE}MB"
            else
              echo "✅ Bundle size decreased by ${SIZE_CHANGE#-}MB"
            fi
          fi
          
          # Check against budget
          if (( $(echo "$BUNDLE_SIZE_MB > $PERFORMANCE_BUDGET_BUNDLE" | bc -l) )); then
            echo "❌ Bundle size exceeds budget (${PERFORMANCE_BUDGET_BUNDLE}KB)"
            exit 1
          fi
          
      - name: 📊 Dependency analysis
        run: |
          # Analyze dependencies
          npm list --depth=0 --json > dependencies.json
          
          # Check for large dependencies
          echo "Analyzing dependency sizes..."
          
          # Extract dependency count
          DEP_COUNT=$(jq '.dependencies | length' dependencies.json)
          echo "Total dependencies: $DEP_COUNT"
          
          # Check for potential issues
          if [ $DEP_COUNT -gt 100 ]; then
            echo "⚠️ High number of dependencies ($DEP_COUNT)"
          fi
          
      - name: 📤 Upload bundle analysis
        uses: actions/upload-artifact@v4
        with:
          name: bundle-analysis
          path: |
            reports/
            dependencies.json
          retention-days: 30

  # Load Testing
  load-testing:
    name: 🚀 Load Testing
    runs-on: ubuntu-latest
    timeout-minutes: 25
    
    outputs:
      requests-per-second: ${{ steps.load-test.outputs.rps }}
      response-time: ${{ steps.load-test.outputs.response-time }}
      
    steps:
      - name: 📦 Checkout code
        uses: actions/checkout@v4
        
      - name: 🔧 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 📥 Install dependencies
        run: npm ci --prefer-offline --no-audit --progress=false
        
      - name: 🏗️ Build application
        run: npm run build
        
      - name: 🚀 Start application
        run: npm start &
        env:
          PORT: 3000
          
      - name: ⏳ Wait for application
        run: npx wait-on http://localhost:3000 --timeout 60000
        
      - name: 📥 Install load testing tools
        run: |
          npm install -g artillery
          
      - name: 🚀 Run load tests
        id: load-test
        run: |
          # Create Artillery configuration
          cat > artillery-config.yml << 'EOF'
          config:
            target: 'http://localhost:3000'
            phases:
              - duration: 60
                arrivalRate: 10
                name: "Warm up"
              - duration: 120
                arrivalRate: 50
                name: "Load test"
              - duration: 60
                arrivalRate: 100
                name: "Spike test"
            processor: "./artillery-processor.js"
          scenarios:
            - name: "Homepage"
              weight: 40
              flow:
                - get:
                    url: "/"
            - name: "Calculator"
              weight: 40
              flow:
                - get:
                    url: "/calculator"
            - name: "API Health"
              weight: 20
              flow:
                - get:
                    url: "/api/health"
          EOF
          
          # Create processor
          cat > artillery-processor.js << 'EOF'
          module.exports = {
            logRequest: function(requestParams, context, ee, next) {
              console.log('Request:', requestParams.url);
              return next();
            }
          };
          EOF
          
          # Run load test
          artillery run artillery-config.yml --output load-test-results.json
          
          # Parse results
          RPS=$(jq -r '.aggregate.requestsPerSecond' load-test-results.json)
          RESPONSE_TIME=$(jq -r '.aggregate.latency.mean' load-test-results.json)
          
          echo "rps=$RPS" >> $GITHUB_OUTPUT
          echo "response-time=$RESPONSE_TIME" >> $GITHUB_OUTPUT
          
          echo "Load Test Results:"
          echo "- Requests per second: $RPS"
          echo "- Average response time: ${RESPONSE_TIME}ms"
          
          # Check against thresholds
          if (( $(echo "$RESPONSE_TIME > 1000" | bc -l) )); then
            echo "⚠️ High response time: ${RESPONSE_TIME}ms"
          fi
          
          if (( $(echo "$RPS < 50" | bc -l) )); then
            echo "⚠️ Low throughput: $RPS RPS"
          fi
          
      - name: 📊 Memory and CPU profiling
        run: |
          # Install profiling tools
          npm install -g clinic
          
          # Run memory profiling
          timeout 30s clinic doctor -- node .next/server/index.js &
          
          # Let it run for a bit
          sleep 10
          
          # Generate some load
          curl -s http://localhost:3000 > /dev/null
          
          echo "Memory profiling completed"
          
      - name: 📤 Upload load test results
        uses: actions/upload-artifact@v4
        with:
          name: load-test-results
          path: |
            load-test-results.json
            .clinic/
          retention-days: 30

  # Visual Regression Testing
  visual-regression:
    name: 👁️ Visual Regression Testing
    runs-on: ubuntu-latest
    timeout-minutes: 20
    
    steps:
      - name: 📦 Checkout code
        uses: actions/checkout@v4
        
      - name: 🔧 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 📥 Install dependencies
        run: npm ci --prefer-offline --no-audit --progress=false
        
      - name: 🎭 Install Playwright
        run: npx playwright install --with-deps
        
      - name: 🏗️ Build application
        run: npm run build
        
      - name: 🚀 Start application
        run: npm start &
        env:
          PORT: 3000
          
      - name: ⏳ Wait for application
        run: npx wait-on http://localhost:3000 --timeout 60000
        
      - name: 👁️ Run visual regression tests
        run: |
          # Run visual regression tests
          npx playwright test --config=playwright.enhanced.config.ts --project=visual-regression
          
      - name: 📸 Upload visual test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: visual-regression-results
          path: |
            test-results/
            visual-results/
          retention-days: 30

  # Performance Summary
  performance-summary:
    name: 📊 Performance Summary
    runs-on: ubuntu-latest
    needs: [lighthouse-audit, bundle-analysis, load-testing, visual-regression]
    if: always()
    
    steps:
      - name: 📊 Calculate performance score
        id: performance-score
        run: |
          # Calculate weighted performance score
          LIGHTHOUSE_SCORE="${{ needs.lighthouse-audit.outputs.performance-score || 0 }}"
          BUNDLE_SIZE="${{ needs.bundle-analysis.outputs.bundle-size || 0 }}"
          RPS="${{ needs.load-testing.outputs.requests-per-second || 0 }}"
          RESPONSE_TIME="${{ needs.load-testing.outputs.response-time || 0 }}"
          
          # Bundle size score (higher is better, normalized to 100)
          BUNDLE_SCORE=$(echo "scale=2; (500 - $BUNDLE_SIZE) / 5" | bc)
          if (( $(echo "$BUNDLE_SCORE < 0" | bc -l) )); then
            BUNDLE_SCORE=0
          fi
          
          # Load test score (normalized)
          LOAD_SCORE=$(echo "scale=2; ($RPS * 2) + (1000 - $RESPONSE_TIME) / 10" | bc)
          if (( $(echo "$LOAD_SCORE < 0" | bc -l) )); then
            LOAD_SCORE=0
          fi
          
          # Overall performance score
          OVERALL_SCORE=$(echo "scale=2; ($LIGHTHOUSE_SCORE * 0.5) + ($BUNDLE_SCORE * 0.3) + ($LOAD_SCORE * 0.2)" | bc)
          
          echo "Overall Performance Score: $OVERALL_SCORE"
          
      - name: 📊 Generate performance report
        run: |
          echo "# ⚡ Performance Report" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "## 🎯 Performance Metrics" >> $GITHUB_STEP_SUMMARY
          echo "| Metric | Value | Status |" >> $GITHUB_STEP_SUMMARY
          echo "|--------|-------|--------|" >> $GITHUB_STEP_SUMMARY
          echo "| Lighthouse Score | ${{ needs.lighthouse-audit.outputs.performance-score || 'N/A' }} | ${{ needs.lighthouse-audit.outputs.performance-score >= 70 && '✅' || '❌' }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Bundle Size | ${{ needs.bundle-analysis.outputs.bundle-size || 'N/A' }}KB | ${{ needs.bundle-analysis.outputs.bundle-size <= 500 && '✅' || '❌' }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Requests/Second | ${{ needs.load-testing.outputs.requests-per-second || 'N/A' }} | ${{ needs.load-testing.outputs.requests-per-second >= 50 && '✅' || '❌' }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Response Time | ${{ needs.load-testing.outputs.response-time || 'N/A' }}ms | ${{ needs.load-testing.outputs.response-time <= 1000 && '✅' || '❌' }} |" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "## 📊 Core Web Vitals" >> $GITHUB_STEP_SUMMARY
          echo "- **Performance:** ${{ needs.lighthouse-audit.outputs.performance-score || 'N/A' }}/100" >> $GITHUB_STEP_SUMMARY
          echo "- **Accessibility:** ${{ needs.lighthouse-audit.outputs.accessibility-score || 'N/A' }}/100" >> $GITHUB_STEP_SUMMARY
          echo "- **SEO:** ${{ needs.lighthouse-audit.outputs.seo-score || 'N/A' }}/100" >> $GITHUB_STEP_SUMMARY
          echo "- **Best Practices:** ${{ needs.lighthouse-audit.outputs.best-practices-score || 'N/A' }}/100" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "## 🔍 Test Results" >> $GITHUB_STEP_SUMMARY
          echo "| Test Suite | Status |" >> $GITHUB_STEP_SUMMARY
          echo "|------------|--------|" >> $GITHUB_STEP_SUMMARY
          echo "| Lighthouse Audit | ${{ needs.lighthouse-audit.result == 'success' && '✅' || '❌' }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Bundle Analysis | ${{ needs.bundle-analysis.result == 'success' && '✅' || '❌' }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Load Testing | ${{ needs.load-testing.result == 'success' && '✅' || '❌' }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Visual Regression | ${{ needs.visual-regression.result == 'success' && '✅' || '❌' }} |" >> $GITHUB_STEP_SUMMARY
          
      - name: 🚨 Performance alert
        if: |
          needs.lighthouse-audit.outputs.performance-score < 70 ||
          needs.bundle-analysis.outputs.bundle-size > 500 ||
          needs.load-testing.outputs.response-time > 1000
        run: |
          echo "🚨 PERFORMANCE ALERT: Performance degradation detected!"
          echo "Lighthouse Score: ${{ needs.lighthouse-audit.outputs.performance-score || 'N/A' }}"
          echo "Bundle Size: ${{ needs.bundle-analysis.outputs.bundle-size || 'N/A' }}KB"
          echo "Response Time: ${{ needs.load-testing.outputs.response-time || 'N/A' }}ms"
          exit 1