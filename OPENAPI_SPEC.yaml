openapi: 3.0.3
info:
  title: Nirmaan AI Construction Calculator API
  description: |
    ## Overview
    The Nirmaan AI Construction Calculator API provides comprehensive construction cost calculation services for the Indian market. This RESTful API offers endpoints for cost calculations, project management, performance monitoring, and analytics.

    ## Features
    - **Cost Calculation**: Calculate construction costs for Indian projects
    - **Project Management**: Save and manage construction projects
    - **Performance Monitoring**: Track API performance and system health
    - **Analytics**: Web vitals and usage analytics
    - **Rate Limiting**: Built-in rate limiting for fair usage
    - **Authentication**: JWT-based authentication for protected endpoints

    ## Base URL
    - **Development**: `http://localhost:3000/api`
    - **Production**: `https://your-domain.com/api`

    ## Authentication
    Some endpoints require JWT authentication. Include the token in the Authorization header:
    ```
    Authorization: Bearer <jwt_token>
    ```

    ## Rate Limiting
    API endpoints are rate-limited to ensure fair usage:
    - `/calculate`: 100 requests per minute per IP
    - `/projects/*`: 60 requests per minute per IP
    - `/monitoring`: 30 requests per minute per IP

    ## Error Handling
    All errors follow a consistent format with appropriate HTTP status codes and detailed error information.

  version: 1.0.0
  contact:
    name: API Support
    email: <EMAIL>
    url: https://docs.nirmaan.ai
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT
servers:
  - url: http://localhost:3000/api
    description: Development server
  - url: https://your-domain.com/api
    description: Production server

paths:
  /calculate:
    post:
      summary: Calculate Construction Costs
      description: |
        Calculate construction costs for Indian construction projects based on built-up area, quality tier, location, and other parameters.
        
        **Features:**
        - Supports 3 quality tiers: Smart, Premium, Luxury
        - 11+ Indian cities with regional pricing
        - Detailed cost breakdown by categories
        - Materials list with quantities and specifications
        - Construction timeline with phases
        - Special features support
      operationId: calculateCost
      tags:
        - Calculation
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CalculationRequest'
            examples:
              smart_calculation:
                summary: Smart Quality Calculation
                value:
                  builtUpArea: 1000
                  floors: 1
                  qualityTier: smart
                  location: bangalore
              premium_calculation:
                summary: Premium Quality with Parking
                value:
                  builtUpArea: 1500
                  floors: 2
                  qualityTier: premium
                  location: mumbai
                  hasStilt: true
                  parkingType: covered
              luxury_with_features:
                summary: Luxury with Special Features
                value:
                  builtUpArea: 2500
                  floors: 3
                  qualityTier: luxury
                  location: delhi
                  hasBasement: true
                  specialFeatures:
                    - name: Swimming Pool
                      cost: 800000
                      description: Olympic size pool
                    - name: Home Theater
                      cost: 300000
                      description: 7.1 surround sound system
      responses:
        '200':
          description: Calculation successful
          headers:
            X-Request-ID:
              description: Unique request identifier
              schema:
                type: string
            X-Performance-Total:
              description: Total processing time in milliseconds
              schema:
                type: integer
            X-Performance-Calculation:
              description: Calculation time in milliseconds
              schema:
                type: integer
            X-Performance-Validation:
              description: Validation time in milliseconds
              schema:
                type: integer
            X-RateLimit-Limit:
              description: Request limit per window
              schema:
                type: integer
            X-RateLimit-Remaining:
              description: Remaining requests in current window
              schema:
                type: integer
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CalculationResponse'
              examples:
                successful_calculation:
                  summary: Successful Calculation Response
                  value:
                    success: true
                    data:
                      totalCost: 2400000
                      costPerSqft: 2000
                      breakdown:
                        structure:
                          amount: 840000
                          percentage: 35
                          subCategories:
                            - name: Foundation
                              amount: 168000
                              percentage: 7
                              description: RCC foundation with M20 concrete
                        finishing:
                          amount: 720000
                          percentage: 30
                          subCategories:
                            - name: Flooring
                              amount: 216000
                              percentage: 9
                              description: Vitrified tiles flooring
                        mep:
                          amount: 480000
                          percentage: 20
                          subCategories:
                            - name: Electrical
                              amount: 240000
                              percentage: 10
                              description: Complete electrical installation
                        external:
                          amount: 240000
                          percentage: 10
                          subCategories:
                            - name: Compound Wall
                              amount: 120000
                              percentage: 5
                              description: Boundary wall construction
                        other:
                          amount: 120000
                          percentage: 5
                          subCategories:
                            - name: Professional Fees
                              amount: 72000
                              percentage: 3
                              description: Architect and engineer fees
                        total: 2400000
                      materials:
                        - category: Structure
                          name: Cement
                          quantity: 240
                          unit: bags
                          rate: 350
                          totalCost: 84000
                          purpose: RCC work and masonry
                          specifications: OPC 53 grade cement
                      timeline:
                        - name: Foundation
                          duration: 3
                          startAfter: 0
                          dependencies: []
                          description: Excavation and foundation work
                      summary:
                        totalBuiltUpArea: 1200
                        carpetArea: 1020
                        constructionDuration: 8
                        totalCost: 2400000
                        costPerSqft: 2000
                        qualityTier: premium
                        location: bangalore
                        estimateAccuracy: "±10%"
                    timestamp: "2025-01-15T10:30:00.000Z"
                    requestId: calc_1640995200_abc123
                    performance:
                      validationTime: 2.5
                      calculationTime: 15.8
                      totalTime: 18.3
        '400':
          description: Bad Request - Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                missing_fields:
                  summary: Missing Required Fields
                  value:
                    success: false
                    error:
                      type: validation
                      message: "Missing required fields: builtUpArea, qualityTier"
                      code: MISSING_REQUIRED_FIELDS
                      details:
                        missingFields: [builtUpArea, qualityTier]
                        requiredFields: [builtUpArea, qualityTier, location]
                    timestamp: "2025-01-15T10:30:00.000Z"
                    requestId: calc_1640995200_abc123
                validation_failed:
                  summary: Validation Failed
                  value:
                    success: false
                    error:
                      type: validation
                      message: Input validation failed
                      code: VALIDATION_FAILED
                      details:
                        errors:
                          - field: builtUpArea
                            message: Built-up area must be between 100 and 50000 sqft
                            code: INVALID_RANGE
                        errorCount: 1
                    timestamp: "2025-01-15T10:30:00.000Z"
                    requestId: calc_1640995200_abc123
        '422':
          description: Unprocessable Entity - Calculation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                calculation_error:
                  summary: Calculation Engine Error
                  value:
                    success: false
                    error:
                      type: calculation
                      message: Calculation engine error
                      code: CALCULATION_ERROR
                      details: Division by zero in cost calculation
                    timestamp: "2025-01-15T10:30:00.000Z"
                    requestId: calc_1640995200_abc123
        '429':
          description: Too Many Requests - Rate Limit Exceeded
          headers:
            Retry-After:
              description: Seconds to wait before retrying
              schema:
                type: integer
            X-RateLimit-Limit:
              description: Request limit per window
              schema:
                type: integer
            X-RateLimit-Remaining:
              description: Remaining requests in current window
              schema:
                type: integer
            X-RateLimit-Reset:
              description: Rate limit reset time (Unix timestamp)
              schema:
                type: integer
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                rate_limit_exceeded:
                  summary: Rate Limit Exceeded
                  value:
                    success: false
                    error:
                      type: rate_limit
                      message: Too many requests. Please try again later.
                      code: RATE_LIMIT_EXCEEDED
                      retryAfter: 45
                    timestamp: "2025-01-15T10:30:00.000Z"
                    requestId: calc_1640995200_abc123
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                server_error:
                  summary: Internal Server Error
                  value:
                    success: false
                    error:
                      type: server
                      message: Internal server error
                      code: INTERNAL_SERVER_ERROR
                    timestamp: "2025-01-15T10:30:00.000Z"
                    requestId: calc_1640995200_abc123
      security: []
      x-codegen-request-body-name: calculationData

    get:
      summary: Get API Documentation
      description: Get API documentation including supported locations, quality tiers, and example requests.
      operationId: getAPIDocumentation
      tags:
        - Documentation
      responses:
        '200':
          description: API documentation retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIDocumentation'
              examples:
                api_documentation:
                  summary: API Documentation
                  value:
                    name: Clarity Engine Calculator API
                    version: "1.0.0"
                    description: Construction cost calculation API for Indian market
                    endpoints:
                      POST:
                        description: Calculate construction costs
                        required_fields: [builtUpArea, qualityTier, location]
                        optional_fields: [floors, plotArea, hasStilt, parkingType, hasBasement, specialFeatures]
                        response: CalculationResult with cost breakdown and materials list
                    supported_locations: [bangalore, mumbai, delhi, pune, hyderabad, chennai, kolkata, ahmedabad, jaipur, lucknow, bhubaneswar]
                    quality_tiers:
                      smart: "₹1,600-2,000 per sqft - Standard finishes"
                      premium: "₹2,200-2,800 per sqft - Branded materials"
                      luxury: "₹3,000-4,000+ per sqft - Premium finishes"
                    example_request:
                      builtUpArea: 1200
                      floors: 1
                      qualityTier: premium
                      location: bangalore
      security: []

  /projects:
    get:
      summary: Get User Projects
      description: Retrieve user's saved construction projects with pagination support.
      operationId: getUserProjects
      tags:
        - Projects
      parameters:
        - name: limit
          in: query
          description: Number of projects to return (max 100)
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
        - name: offset
          in: query
          description: Number of projects to skip for pagination
          required: false
          schema:
            type: integer
            minimum: 0
            default: 0
      responses:
        '200':
          description: Projects retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProjectsResponse'
              examples:
                projects_list:
                  summary: User Projects List
                  value:
                    projects:
                      - id: proj_abc123
                        name: My Dream Home
                        location: bangalore
                        area_sqft: 1200
                        floors: 1
                        quality_tier: premium
                        created_at: "2025-01-15T10:30:00.000Z"
                        updated_at: "2025-01-15T10:30:00.000Z"
                        totalCost: 2400000
                        costPerSqft: 2000
                        timeline: 8
                    pagination:
                      offset: 0
                      limit: 20
                      hasMore: false
        '401':
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                authentication_required:
                  summary: Authentication Required
                  value:
                    success: false
                    error:
                      type: authentication
                      message: Authentication required
                      code: AUTHENTICATION_REQUIRED
                    timestamp: "2025-01-15T10:30:00.000Z"
                    requestId: calc_1640995200_abc123
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - bearerAuth: []

    delete:
      summary: Delete Project
      description: Delete a specific project by ID.
      operationId: deleteProject
      tags:
        - Projects
      parameters:
        - name: id
          in: query
          description: Project ID to delete
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Project deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
        '400':
          description: Bad Request - Project ID required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - bearerAuth: []

  /projects/save:
    post:
      summary: Save Project
      description: Save a new construction project with calculation data.
      operationId: saveProject
      tags:
        - Projects
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SaveProjectRequest'
            examples:
              save_project:
                summary: Save Project Example
                value:
                  name: My Dream Home
                  location: bangalore
                  area_sqft: 1200
                  floors: 1
                  quality_tier: premium
                  calculation_data:
                    formData:
                      builtUpArea: 1200
                      plotSize: 1500
                      floors: 1
                      qualityTier: premium
                      location: bangalore
                      hasParking: true
                      parkingType: open
                    results:
                      totalCost: 2400000
                      costPerSqft: 2000
                      costBreakdown: {}
                      materials: []
                      timeline:
                        totalDuration: 8
                        phases: []
                      summary:
                        projectType: Residential
                        constructionType: RCC
                        totalBuiltUpArea: 1200
                        estimatedTimeline: 8 months
                    calculatedAt: "2025-01-15T10:30:00.000Z"
                    version: "1.0.0"
      responses:
        '200':
          description: Project saved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SaveProjectResponse'
              examples:
                project_saved:
                  summary: Project Saved
                  value:
                    success: true
                    project:
                      id: proj_abc123
                      name: My Dream Home
                      location: bangalore
                      area_sqft: 1200
                      quality_tier: premium
                      created_at: "2025-01-15T10:30:00.000Z"
        '400':
          description: Bad Request - Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - bearerAuth: []

  /health:
    get:
      summary: Health Check
      description: Get comprehensive health check with system metrics and status.
      operationId: healthCheck
      tags:
        - System
      responses:
        '200':
          description: System is healthy
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthResponse'
              examples:
                healthy_system:
                  summary: Healthy System
                  value:
                    status: healthy
                    timestamp: "2025-01-15T10:30:00.000Z"
                    version: "1.0.0"
                    environment: production
                    uptime: 86400
                    checks:
                      database: true
                      api: true
                      calculations: true
                      memory:
                        used: 256
                        total: 512
                        percentage: 50
                    performance:
                      responseTime: 45
                      requestCount: 1247
                      errorRate: 0.8
        '503':
          description: Service Unavailable - System is unhealthy
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthResponse'
              examples:
                unhealthy_system:
                  summary: Unhealthy System
                  value:
                    status: unhealthy
                    timestamp: "2025-01-15T10:30:00.000Z"
                    error: Health check failed
                    uptime: 86400
      security: []

  /monitoring:
    get:
      summary: Get Monitoring Metrics
      description: Retrieve detailed monitoring metrics and system performance data.
      operationId: getMonitoringMetrics
      tags:
        - Monitoring
      parameters:
        - name: detail
          in: query
          description: Level of detail for monitoring data
          required: false
          schema:
            type: string
            enum: [basic, detailed]
            default: basic
      responses:
        '200':
          description: Monitoring metrics retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MonitoringResponse'
              examples:
                basic_monitoring:
                  summary: Basic Monitoring Data
                  value:
                    status: healthy
                    timestamp: "2025-01-15T10:30:00.000Z"
                    metrics:
                      requests:
                        total: 1247
                        errors: 10
                        errorRate: 0.8
                        throughput: 52.3
                      performance:
                        responseTime:
                          avg: 157
                          max: 2340
                          min: 23
                        memory:
                          usage: 256
                          unit: MB
                      system:
                        uptime: 86400
                        environment: production
                        version: "1.0.0"
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security: []

  /performance/metrics:
    post:
      summary: Submit Performance Metrics
      description: Submit performance metrics for monitoring and analysis.
      operationId: submitPerformanceMetrics
      tags:
        - Performance
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PerformanceMetricRequest'
            examples:
              performance_metric:
                summary: Performance Metric Submission
                value:
                  metric:
                    name: LCP
                    value: 1234
                    rating: good
                    url: "https://your-domain.com/calculator"
                    userAgent: "Mozilla/5.0..."
                    sessionId: session_abc123
                    deviceType: desktop
                    connectionType: 4g
                    route: /calculator
                  page: /calculator
                  viewport:
                    width: 1920
                    height: 1080
      responses:
        '200':
          description: Performance metrics submitted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: received
                  alerts:
                    type: integer
                    example: 0
        '400':
          description: Bad Request - Invalid metric data
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security: []

    get:
      summary: Get Performance Metrics
      description: Retrieve performance metrics data with filtering and aggregation.
      operationId: getPerformanceMetrics
      tags:
        - Performance
      parameters:
        - name: metric
          in: query
          description: Filter by specific metric name
          required: false
          schema:
            type: string
            enum: [LCP, FID, CLS, FCP, TTFB, INP]
        - name: start
          in: query
          description: Start timestamp for filtering (Unix timestamp)
          required: false
          schema:
            type: integer
        - name: end
          in: query
          description: End timestamp for filtering (Unix timestamp)
          required: false
          schema:
            type: integer
        - name: limit
          in: query
          description: Maximum number of metrics to return
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 1000
            default: 100
      responses:
        '200':
          description: Performance metrics retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PerformanceMetricsResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security: []

  /analytics/web-vitals:
    post:
      summary: Submit Web Vitals Analytics
      description: Submit Web Vitals metrics for analysis and monitoring.
      operationId: submitWebVitalsAnalytics
      tags:
        - Analytics
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/WebVitalsRequest'
            examples:
              web_vitals:
                summary: Web Vitals Submission
                value:
                  metrics:
                    - metric: LCP
                      value: 1500
                      rating: good
                      url: "http://localhost:3000/calculator"
                      timestamp: 1640995200000
                      userAgent: "Mozilla/5.0..."
                      deviceType: desktop
                      connectionType: 4g
                      route: /calculator
                      sessionId: session_abc123
                      userId: user_xyz789
      responses:
        '200':
          description: Web vitals analytics submitted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: received
                  processed:
                    type: integer
                    example: 1
                  timestamp:
                    type: integer
                    example: 1640995200000
        '400':
          description: Bad Request - Invalid analytics data
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security: []

    get:
      summary: Get Web Vitals Analytics
      description: Retrieve Web Vitals analytics data with filtering and aggregation.
      operationId: getWebVitalsAnalytics
      tags:
        - Analytics
      parameters:
        - name: metric
          in: query
          description: Filter by specific metric name
          required: false
          schema:
            type: string
            enum: [LCP, FID, CLS, FCP, TTFB, INP]
        - name: timeRange
          in: query
          description: Time range for analytics data
          required: false
          schema:
            type: string
            enum: [1h, 24h, 7d, 30d]
            default: 1h
        - name: deviceType
          in: query
          description: Filter by device type
          required: false
          schema:
            type: string
            enum: [desktop, mobile, tablet]
        - name: route
          in: query
          description: Filter by route/page
          required: false
          schema:
            type: string
        - name: aggregated
          in: query
          description: Return aggregated statistics
          required: false
          schema:
            type: boolean
            default: false
      responses:
        '200':
          description: Web vitals analytics retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebVitalsResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security: []

components:
  schemas:
    CalculationRequest:
      type: object
      required:
        - builtUpArea
        - qualityTier
        - location
      properties:
        builtUpArea:
          type: number
          minimum: 100
          maximum: 50000
          description: Built-up area in square feet
          example: 1200
        floors:
          type: integer
          minimum: 0
          maximum: 10
          description: Number of floors (0 = Ground only, 1 = G+1, etc.)
          example: 1
        qualityTier:
          type: string
          enum: [smart, premium, luxury]
          description: Quality tier for construction
          example: premium
        location:
          type: string
          description: City name for regional pricing
          example: bangalore
        plotArea:
          type: number
          minimum: 100
          maximum: 100000
          description: Plot area in square feet (optional)
          example: 1500
        hasStilt:
          type: boolean
          description: Whether the building has stilt parking
          example: false
        parkingType:
          type: string
          enum: [open, covered, none]
          description: Type of parking facility
          example: open
        hasBasement:
          type: boolean
          description: Whether the building has a basement
          example: false
        specialFeatures:
          type: array
          description: Array of special features
          items:
            $ref: '#/components/schemas/SpecialFeature'

    SpecialFeature:
      type: object
      required:
        - name
        - cost
        - description
      properties:
        name:
          type: string
          description: Name of the special feature
          example: Swimming Pool
        cost:
          type: number
          minimum: 0
          description: Cost of the special feature
          example: 500000
        description:
          type: string
          description: Description of the special feature
          example: Standard swimming pool with filtration system

    CalculationResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        data:
          $ref: '#/components/schemas/CalculationResult'
        timestamp:
          type: string
          format: date-time
          example: "2025-01-15T10:30:00.000Z"
        requestId:
          type: string
          example: calc_1640995200_abc123
        performance:
          $ref: '#/components/schemas/PerformanceInfo'

    CalculationResult:
      type: object
      properties:
        totalCost:
          type: number
          description: Total construction cost
          example: 2400000
        costPerSqft:
          type: number
          description: Cost per square foot
          example: 2000
        breakdown:
          $ref: '#/components/schemas/CostBreakdown'
        materials:
          type: array
          items:
            $ref: '#/components/schemas/MaterialQuantity'
        timeline:
          type: array
          items:
            $ref: '#/components/schemas/ConstructionPhase'
        summary:
          $ref: '#/components/schemas/ProjectSummary'

    CostBreakdown:
      type: object
      properties:
        structure:
          $ref: '#/components/schemas/CategoryCost'
        finishing:
          $ref: '#/components/schemas/CategoryCost'
        mep:
          $ref: '#/components/schemas/CategoryCost'
        external:
          $ref: '#/components/schemas/CategoryCost'
        other:
          $ref: '#/components/schemas/CategoryCost'
        total:
          type: number
          example: 2400000

    CategoryCost:
      type: object
      properties:
        amount:
          type: number
          example: 840000
        percentage:
          type: number
          example: 35
        subCategories:
          type: array
          items:
            $ref: '#/components/schemas/SubCategory'

    SubCategory:
      type: object
      properties:
        name:
          type: string
          example: Foundation
        amount:
          type: number
          example: 168000
        percentage:
          type: number
          example: 7
        description:
          type: string
          example: RCC foundation with M20 concrete

    MaterialQuantity:
      type: object
      properties:
        category:
          type: string
          example: Structure
        name:
          type: string
          example: Cement
        quantity:
          type: number
          example: 240
        unit:
          type: string
          example: bags
        rate:
          type: number
          example: 350
        totalCost:
          type: number
          example: 84000
        purpose:
          type: string
          example: RCC work and masonry
        specifications:
          type: string
          example: OPC 53 grade cement

    ConstructionPhase:
      type: object
      properties:
        name:
          type: string
          example: Foundation
        duration:
          type: number
          description: Duration in weeks
          example: 3
        startAfter:
          type: number
          description: Start after (weeks from project start)
          example: 0
        dependencies:
          type: array
          items:
            type: string
          example: []
        description:
          type: string
          example: Excavation and foundation work

    ProjectSummary:
      type: object
      properties:
        totalBuiltUpArea:
          type: number
          example: 1200
        carpetArea:
          type: number
          example: 1020
        constructionDuration:
          type: number
          description: Duration in months
          example: 8
        totalCost:
          type: number
          example: 2400000
        costPerSqft:
          type: number
          example: 2000
        qualityTier:
          type: string
          enum: [smart, premium, luxury]
          example: premium
        location:
          type: string
          example: bangalore
        estimateAccuracy:
          type: string
          example: "±10%"

    PerformanceInfo:
      type: object
      properties:
        validationTime:
          type: number
          description: Validation time in milliseconds
          example: 2.5
        calculationTime:
          type: number
          description: Calculation time in milliseconds
          example: 15.8
        totalTime:
          type: number
          description: Total processing time in milliseconds
          example: 18.3

    ErrorResponse:
      type: object
      properties:
        success:
          type: boolean
          example: false
        error:
          $ref: '#/components/schemas/ErrorDetails'
        timestamp:
          type: string
          format: date-time
          example: "2025-01-15T10:30:00.000Z"
        requestId:
          type: string
          example: calc_1640995200_abc123

    ErrorDetails:
      type: object
      properties:
        type:
          type: string
          enum: [validation, calculation, rate_limit, authentication, server]
          example: validation
        message:
          type: string
          example: Missing required fields
        code:
          type: string
          example: MISSING_REQUIRED_FIELDS
        details:
          type: object
          description: Additional error context
        retryAfter:
          type: integer
          description: Seconds to wait before retrying (for rate limit errors)
          example: 45

    APIDocumentation:
      type: object
      properties:
        name:
          type: string
          example: Clarity Engine Calculator API
        version:
          type: string
          example: "1.0.0"
        description:
          type: string
          example: Construction cost calculation API for Indian market
        endpoints:
          type: object
        supported_locations:
          type: array
          items:
            type: string
          example: [bangalore, mumbai, delhi]
        quality_tiers:
          type: object
        example_request:
          type: object

    ProjectsResponse:
      type: object
      properties:
        projects:
          type: array
          items:
            $ref: '#/components/schemas/ProjectSummaryItem'
        pagination:
          $ref: '#/components/schemas/PaginationInfo'

    ProjectSummaryItem:
      type: object
      properties:
        id:
          type: string
          example: proj_abc123
        name:
          type: string
          example: My Dream Home
        location:
          type: string
          example: bangalore
        area_sqft:
          type: number
          example: 1200
        floors:
          type: integer
          example: 1
        quality_tier:
          type: string
          enum: [smart, premium, luxury]
          example: premium
        created_at:
          type: string
          format: date-time
          example: "2025-01-15T10:30:00.000Z"
        updated_at:
          type: string
          format: date-time
          example: "2025-01-15T10:30:00.000Z"
        totalCost:
          type: number
          example: 2400000
        costPerSqft:
          type: number
          example: 2000
        timeline:
          type: number
          example: 8

    PaginationInfo:
      type: object
      properties:
        offset:
          type: integer
          example: 0
        limit:
          type: integer
          example: 20
        hasMore:
          type: boolean
          example: false

    SaveProjectRequest:
      type: object
      required:
        - name
        - location
        - area_sqft
        - floors
        - quality_tier
        - calculation_data
      properties:
        name:
          type: string
          minLength: 1
          maxLength: 100
          example: My Dream Home
        location:
          type: string
          minLength: 1
          example: bangalore
        area_sqft:
          type: number
          minimum: 1
          example: 1200
        floors:
          type: integer
          minimum: 0
          maximum: 10
          example: 1
        quality_tier:
          type: string
          enum: [smart, premium, luxury]
          example: premium
        calculation_data:
          type: object
          description: Complete calculation data including form inputs and results

    SaveProjectResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        project:
          type: object
          properties:
            id:
              type: string
              example: proj_abc123
            name:
              type: string
              example: My Dream Home
            location:
              type: string
              example: bangalore
            area_sqft:
              type: number
              example: 1200
            quality_tier:
              type: string
              example: premium
            created_at:
              type: string
              format: date-time
              example: "2025-01-15T10:30:00.000Z"

    HealthResponse:
      type: object
      properties:
        status:
          type: string
          enum: [healthy, degraded, unhealthy]
          example: healthy
        timestamp:
          type: string
          format: date-time
          example: "2025-01-15T10:30:00.000Z"
        version:
          type: string
          example: "1.0.0"
        environment:
          type: string
          example: production
        uptime:
          type: integer
          description: Uptime in seconds
          example: 86400
        checks:
          type: object
          properties:
            database:
              type: boolean
              example: true
            api:
              type: boolean
              example: true
            calculations:
              type: boolean
              example: true
            memory:
              type: object
              properties:
                used:
                  type: number
                  example: 256
                total:
                  type: number
                  example: 512
                percentage:
                  type: number
                  example: 50
        performance:
          type: object
          properties:
            responseTime:
              type: number
              example: 45
            requestCount:
              type: integer
              example: 1247
            errorRate:
              type: number
              example: 0.8
        error:
          type: string
          description: Error message (only present when unhealthy)

    MonitoringResponse:
      type: object
      properties:
        status:
          type: string
          enum: [healthy, degraded, unhealthy]
          example: healthy
        timestamp:
          type: string
          format: date-time
          example: "2025-01-15T10:30:00.000Z"
        metrics:
          type: object
          properties:
            requests:
              type: object
              properties:
                total:
                  type: integer
                  example: 1247
                errors:
                  type: integer
                  example: 10
                errorRate:
                  type: number
                  example: 0.8
                throughput:
                  type: number
                  example: 52.3
            performance:
              type: object
              properties:
                responseTime:
                  type: object
                  properties:
                    avg:
                      type: number
                      example: 157
                    max:
                      type: number
                      example: 2340
                    min:
                      type: number
                      example: 23
                memory:
                  type: object
                  properties:
                    usage:
                      type: number
                      example: 256
                    unit:
                      type: string
                      example: MB
            system:
              type: object
              properties:
                uptime:
                  type: integer
                  example: 86400
                environment:
                  type: string
                  example: production
                version:
                  type: string
                  example: "1.0.0"
        endpoints:
          type: array
          items:
            type: object
            properties:
              path:
                type: string
                example: /api/calculate
              requests:
                type: integer
                example: 856
              averageResponseTime:
                type: number
                example: 145
              errorRate:
                type: number
                example: 0.5
        recentErrors:
          type: array
          items:
            type: object
            properties:
              timestamp:
                type: string
                format: date-time
              error:
                type: string
              endpoint:
                type: string
              statusCode:
                type: integer
        slowRequests:
          type: array
          items:
            type: object
            properties:
              timestamp:
                type: string
                format: date-time
              endpoint:
                type: string
              responseTime:
                type: number
              threshold:
                type: number

    PerformanceMetricRequest:
      type: object
      required:
        - metric
      properties:
        metric:
          type: object
          required:
            - name
            - value
          properties:
            name:
              type: string
              enum: [LCP, FID, CLS, FCP, TTFB, INP]
              example: LCP
            value:
              type: number
              example: 1234
            rating:
              type: string
              enum: [good, needs-improvement, poor]
              example: good
            url:
              type: string
              example: "https://your-domain.com/calculator"
            userAgent:
              type: string
              example: "Mozilla/5.0..."
            sessionId:
              type: string
              example: session_abc123
            userId:
              type: string
              example: user_xyz789
            deviceType:
              type: string
              enum: [desktop, mobile, tablet]
              example: desktop
            connectionType:
              type: string
              enum: [4g, 3g, 2g, slow-2g]
              example: 4g
            route:
              type: string
              example: /calculator
            referrer:
              type: string
              example: "https://google.com"
            buildVersion:
              type: string
              example: "1.0.0"
        page:
          type: string
          example: /calculator
        viewport:
          type: object
          properties:
            width:
              type: number
              example: 1920
            height:
              type: number
              example: 1080

    PerformanceMetricsResponse:
      type: object
      properties:
        metrics:
          type: array
          items:
            type: object
            properties:
              name:
                type: string
                example: LCP
              value:
                type: number
                example: 1234
              rating:
                type: string
                example: good
              timestamp:
                type: integer
                example: 1640995200000
              url:
                type: string
                example: "https://your-domain.com/calculator"
              userAgent:
                type: string
                example: "Mozilla/5.0..."
              deviceType:
                type: string
                example: desktop
        stats:
          type: object
          properties:
            count:
              type: integer
              example: 245
            average:
              type: number
              example: 1456.78
            median:
              type: number
              example: 1234
            p95:
              type: number
              example: 2456
            p99:
              type: number
              example: 3456
            min:
              type: number
              example: 567
            max:
              type: number
              example: 4567
        totalCount:
          type: integer
          example: 245
        timestamp:
          type: integer
          example: 1640995200000

    WebVitalsRequest:
      type: object
      required:
        - metrics
      properties:
        metrics:
          type: array
          items:
            type: object
            required:
              - metric
              - value
            properties:
              metric:
                type: string
                enum: [LCP, FID, CLS, FCP, TTFB, INP]
                example: LCP
              value:
                type: number
                example: 1500
              rating:
                type: string
                enum: [good, needs-improvement, poor]
                example: good
              url:
                type: string
                example: "http://localhost:3000/calculator"
              timestamp:
                type: integer
                example: 1640995200000
              userAgent:
                type: string
                example: "Mozilla/5.0..."
              deviceType:
                type: string
                enum: [desktop, mobile, tablet]
                example: desktop
              connectionType:
                type: string
                enum: [4g, 3g, 2g, slow-2g]
                example: 4g
              route:
                type: string
                example: /calculator
              sessionId:
                type: string
                example: session_abc123
              userId:
                type: string
                example: user_xyz789
              referrer:
                type: string
                example: "https://google.com"
              buildVersion:
                type: string
                example: "1.0.0"
              viewport:
                type: object
                properties:
                  width:
                    type: number
                    example: 1920
                  height:
                    type: number
                    example: 1080

    WebVitalsResponse:
      type: object
      properties:
        stats:
          type: object
          properties:
            overview:
              type: object
              properties:
                totalMetrics:
                  type: integer
                  example: 1247
                uniqueUsers:
                  type: integer
                  example: 234
                uniquePages:
                  type: integer
                  example: 12
                ratingPercentages:
                  type: object
                  properties:
                    good:
                      type: integer
                      example: 78
                    needs-improvement:
                      type: integer
                      example: 15
                    poor:
                      type: integer
                      example: 7
            byMetric:
              type: object
              additionalProperties:
                type: object
                properties:
                  count:
                    type: integer
                    example: 245
                  average:
                    type: number
                    example: 1456.78
                  median:
                    type: number
                    example: 1234
                  p95:
                    type: number
                    example: 2456
                  p99:
                    type: number
                    example: 3456
            performanceScore:
              type: integer
              example: 82
            trends:
              type: object
              properties:
                performanceScore:
                  type: object
                  properties:
                    current:
                      type: integer
                      example: 82
                    previous:
                      type: integer
                      example: 79
                    change:
                      type: integer
                      example: 3
                    trend:
                      type: string
                      enum: [up, down, stable]
                      example: up
        metrics:
          type: array
          items:
            type: object
            properties:
              metric:
                type: string
                example: LCP
              value:
                type: number
                example: 1234
              rating:
                type: string
                example: good
              timestamp:
                type: integer
                example: 1640995200000
              url:
                type: string
                example: "http://localhost:3000/calculator"
              deviceType:
                type: string
                example: desktop
        timeRange:
          type: string
          example: 24h
        totalMetrics:
          type: integer
          example: 1247
        timestamp:
          type: integer
          example: 1640995200000

  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT authentication token obtained from Supabase Auth

  responses:
    BadRequest:
      description: Bad Request - Invalid input parameters
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'

    Unauthorized:
      description: Unauthorized - Authentication required
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'

    RateLimitExceeded:
      description: Too Many Requests - Rate limit exceeded
      headers:
        Retry-After:
          description: Seconds to wait before retrying
          schema:
            type: integer
        X-RateLimit-Limit:
          description: Request limit per window
          schema:
            type: integer
        X-RateLimit-Remaining:
          description: Remaining requests in current window
          schema:
            type: integer
        X-RateLimit-Reset:
          description: Rate limit reset time (Unix timestamp)
          schema:
            type: integer
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'

    UnprocessableEntity:
      description: Unprocessable Entity - Calculation error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'

    InternalServerError:
      description: Internal Server Error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'

    ServiceUnavailable:
      description: Service Unavailable - System is unhealthy
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'

tags:
  - name: Calculation
    description: Construction cost calculation endpoints
  - name: Projects
    description: Project management endpoints
  - name: System
    description: System health and status endpoints
  - name: Monitoring
    description: Performance monitoring endpoints
  - name: Performance
    description: Performance metrics endpoints
  - name: Analytics
    description: Analytics and Web Vitals endpoints
  - name: Documentation
    description: API documentation endpoints

externalDocs:
  description: Find out more about Nirmaan AI
  url: https://docs.nirmaan.ai