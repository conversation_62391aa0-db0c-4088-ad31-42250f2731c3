/**
 * Production Security Middleware
 * Comprehensive security enforcement for Nirmaan AI Construction Calculator
 */

import { NextRequest, NextResponse } from 'next/server';
import { advancedRateLimiter } from '@/lib/security/advanced-rate-limiter';
import { vulnerabilityScanner } from '@/lib/security/vulnerability-scanner';
import { securityHeaders } from '@/lib/security/headers';

interface SecurityMetrics {
  totalRequests: number;
  blockedRequests: number;
  rateLimitedRequests: number;
  vulnerabilitiesDetected: number;
  lastUpdated: Date;
}

interface GeoLocation {
  country?: string;
  region?: string;
  city?: string;
  ip?: string;
}

interface ThreatIntelligence {
  isKnownThreat: boolean;
  threatType?: 'malware' | 'botnet' | 'proxy' | 'scanner' | 'spam';
  confidence: number;
  lastSeen?: Date;
}

class ProductionSecurityMiddleware {
  private metrics: SecurityMetrics = {
    totalRequests: 0,
    blockedRequests: 0,
    rateLimitedRequests: 0,
    vulnerabilitiesDetected: 0,
    lastUpdated: new Date(),
  };

  private blockedCountries: Set<string> = new Set([
    // Add countries based on your security requirements
    // 'CN', 'RU', 'KP' // Example: China, Russia, North Korea
  ]);

  private allowedCountries: Set<string> = new Set([
    'IN', 'US', 'CA', 'GB', 'AU', 'DE', 'FR', 'JP', 'SG', 'AE'
  ]);

  private suspiciousUserAgents: RegExp[] = [
    /bot/i,
    /crawler/i,
    /spider/i,
    /scraper/i,
    /scanner/i,
    /curl/i,
    /wget/i,
    /python-requests/i,
    /postman/i,
    /burp/i,
    /nmap/i,
    /sqlmap/i,
    /nikto/i,
  ];

  private criticalPaths: string[] = [
    '/api/admin',
    '/api/auth',
    '/api/calculate',
    '/api/projects/save',
  ];

  /**
   * Main middleware function
   */
  async process(request: NextRequest): Promise<NextResponse> {
    const startTime = Date.now();
    
    try {
      this.metrics.totalRequests++;
      
      // Extract request information
      const ip = this.extractIP(request);
      const userAgent = request.headers.get('user-agent') || '';
      const path = request.nextUrl.pathname;
      const method = request.method;
      const country = this.extractCountry(request);

      // Skip security checks for static assets and health checks
      if (this.shouldSkipSecurity(path)) {
        return this.createSecureResponse(NextResponse.next(), false);
      }

      // 1. Geofencing - Block/Allow countries
      const geoCheckResult = this.performGeoCheck(country, ip);
      if (!geoCheckResult.allowed) {
        return this.blockRequest('geo_blocked', geoCheckResult.reason || 'Geo-blocking rule violated', ip || 'unknown');
      }

      // 2. Bot detection and user agent validation
      const botCheckResult = this.performBotCheck(userAgent, path);
      if (botCheckResult.isBot && !botCheckResult.allowed) {
        return this.blockRequest('bot_blocked', 'Malicious bot detected', ip);
      }

      // 3. Advanced rate limiting
      const rateLimitResult = await advancedRateLimiter.check(request);
      if (!rateLimitResult.success) {
        this.metrics.rateLimitedRequests++;
        return this.createRateLimitResponse(rateLimitResult);
      }

      // 4. Vulnerability scanning
      const vulnResult = vulnerabilityScanner.scanRequest(request);
      if (vulnResult.vulnerable && vulnResult.riskScore > 10) {
        this.metrics.vulnerabilitiesDetected++;
        this.logSecurityEvent('vulnerability_detected', {
          ip,
          path,
          findings: vulnResult.findings,
          riskScore: vulnResult.riskScore,
        });
        
        // Block high-risk requests immediately
        if (vulnResult.riskScore > 20) {
          return this.blockRequest('vulnerability_blocked', 'High-risk vulnerability detected', ip);
        }
      }

      // 5. Request size validation
      const sizeCheck = this.validateRequestSize(request);
      if (!sizeCheck.valid) {
        return this.blockRequest('size_limit_exceeded', sizeCheck.reason || 'Request size limit exceeded', ip || 'unknown');
      }

      // 6. Path traversal protection
      if (this.detectPathTraversal(path)) {
        return this.blockRequest('path_traversal', 'Path traversal attempt detected', ip || 'unknown');
      }

      // 7. Critical path protection
      if (this.isCriticalPath(path)) {
        const criticalPathResult = await this.validateCriticalPath(request);
        if (!criticalPathResult.allowed) {
          return this.blockRequest('critical_path_violation', criticalPathResult.reason || 'Critical path access violation', ip);
        }
      }

      // 8. Content type validation
      if (method === 'POST' || method === 'PUT') {
        const contentTypeValid = this.validateContentType(request);
        if (!contentTypeValid) {
          return this.blockRequest('invalid_content_type', 'Invalid or missing content type', ip);
        }
      }

      // Record successful request
      advancedRateLimiter.recordRequestResult(request, true, Date.now() - startTime);

      // Create secure response
      const response = NextResponse.next();
      return this.createSecureResponse(response, true);

    } catch (error) {
      console.error('Security middleware error:', error);
      
      // Log the error but don't block the request
      this.logSecurityEvent('middleware_error', {
        error: error instanceof Error ? error.message : 'Unknown error',
        path: request.nextUrl.pathname,
      });

      // Return with basic security headers
      const response = NextResponse.next();
      return this.createSecureResponse(response, true);
    }
  }

  /**
   * Extract real IP address from request
   */
  private extractIP(request: NextRequest): string {
    const forwardedFor = request.headers.get('x-forwarded-for');
    const realIP = request.headers.get('x-real-ip');
    const cfConnectingIP = request.headers.get('cf-connecting-ip');
    
    return (
      cfConnectingIP ||
      realIP ||
      forwardedFor?.split(',')[0]?.trim() ||
      (request as any).ip ||
      '127.0.0.1'
    );
  }

  /**
   * Extract country from request headers
   */
  private extractCountry(request: NextRequest): string | null {
    return (
      request.headers.get('cf-ipcountry') ||
      request.headers.get('x-country-code') ||
      null
    );
  }

  /**
   * Check if security should be skipped for this path
   */
  private shouldSkipSecurity(path: string): boolean {
    const skipPaths = [
      '/_next/',
      '/favicon.ico',
      '/robots.txt',
      '/sitemap.xml',
      '/api/health',
      '/api/metrics',
      '/.well-known/',
    ];

    return skipPaths.some(skipPath => path.startsWith(skipPath));
  }

  /**
   * Perform geofencing checks
   */
  private performGeoCheck(country: string | null, ip: string): { allowed: boolean; reason?: string } {
    // Allow requests without country information (local development)
    if (!country) {
      return { allowed: true };
    }

    // Block known problematic countries
    if (this.blockedCountries.has(country)) {
      return { allowed: false, reason: `Country ${country} is blocked` };
    }

    // For production, you might want to only allow specific countries
    if (process.env.NODE_ENV === 'production' && this.allowedCountries.size > 0) {
      if (!this.allowedCountries.has(country)) {
        return { allowed: false, reason: `Country ${country} is not in allowed list` };
      }
    }

    return { allowed: true };
  }

  /**
   * Perform bot detection
   */
  private performBotCheck(userAgent: string, path: string): { isBot: boolean; allowed: boolean } {
    const isBot = this.suspiciousUserAgents.some(pattern => pattern.test(userAgent));
    
    // Allow some legitimate bots on specific paths
    if (isBot) {
      const legitimateBots = ['googlebot', 'bingbot', 'slurp', 'duckduckbot'];
      const isLegitimate = legitimateBots.some(bot => userAgent.toLowerCase().includes(bot));
      
      if (isLegitimate && (path === '/robots.txt' || path === '/sitemap.xml')) {
        return { isBot: true, allowed: true };
      }
      
      return { isBot: true, allowed: false };
    }

    return { isBot: false, allowed: true };
  }

  /**
   * Validate request size
   */
  private validateRequestSize(request: NextRequest): { valid: boolean; reason?: string } {
    const contentLength = request.headers.get('content-length');
    
    if (contentLength) {
      const size = parseInt(contentLength, 10);
      const maxSize = 10 * 1024 * 1024; // 10MB
      
      if (size > maxSize) {
        return { valid: false, reason: `Request size ${size} exceeds limit ${maxSize}` };
      }
    }

    return { valid: true };
  }

  /**
   * Detect path traversal attempts
   */
  private detectPathTraversal(path: string): boolean {
    const traversalPatterns = [
      /\.\.\//,
      /\.\.\\/,
      /%2e%2e%2f/i,
      /%2e%2e%5c/i,
      /\.%2e%2f/i,
      /\.%2e%5c/i,
    ];

    return traversalPatterns.some(pattern => pattern.test(path));
  }

  /**
   * Check if path is critical
   */
  private isCriticalPath(path: string): boolean {
    return this.criticalPaths.some(criticalPath => path.startsWith(criticalPath));
  }

  /**
   * Validate critical path access
   */
  private async validateCriticalPath(request: NextRequest): Promise<{ allowed: boolean; reason?: string }> {
    const path = request.nextUrl.pathname;
    
    // Require authentication for admin paths
    if (path.startsWith('/api/admin')) {
      const auth = request.headers.get('authorization');
      if (!auth || !auth.startsWith('Bearer ')) {
        return { allowed: false, reason: 'Admin access requires authentication' };
      }
    }

    // Require specific headers for API endpoints
    if (path.startsWith('/api/') && (request.method === 'POST' || request.method === 'PUT')) {
      const csrfToken = request.headers.get('x-csrf-token');
      if (!csrfToken) {
        return { allowed: false, reason: 'CSRF token required for state-changing operations' };
      }
    }

    return { allowed: true };
  }

  /**
   * Validate content type
   */
  private validateContentType(request: NextRequest): boolean {
    const contentType = request.headers.get('content-type');
    const allowedTypes = [
      'application/json',
      'application/x-www-form-urlencoded',
      'multipart/form-data',
      'text/plain',
    ];

    if (!contentType) {
      return false;
    }

    return allowedTypes.some(type => contentType.includes(type));
  }

  /**
   * Create a blocked request response
   */
  private blockRequest(reason: string, message: string, ip: string): NextResponse {
    this.metrics.blockedRequests++;
    
    this.logSecurityEvent('request_blocked', {
      reason,
      message,
      ip,
    });

    const response = NextResponse.json(
      { 
        error: 'Access Denied', 
        code: 'SECURITY_VIOLATION',
        timestamp: new Date().toISOString(),
      },
      { status: 403 }
    );

    return this.createSecureResponse(response, false);
  }

  /**
   * Create rate limit response
   */
  private createRateLimitResponse(rateLimitResult: any): NextResponse {
    const response = NextResponse.json(
      { 
        error: 'Rate limit exceeded',
        code: 'RATE_LIMIT_EXCEEDED',
        retryAfter: rateLimitResult.retryAfter,
        timestamp: new Date().toISOString(),
      },
      { status: 429 }
    );

    // Add rate limit headers
    response.headers.set('X-RateLimit-Limit', rateLimitResult.limit.toString());
    response.headers.set('X-RateLimit-Remaining', rateLimitResult.remaining.toString());
    response.headers.set('X-RateLimit-Reset', new Date(rateLimitResult.resetTime).toISOString());
    response.headers.set('Retry-After', rateLimitResult.retryAfter?.toString() || '60');

    return this.createSecureResponse(response, false);
  }

  /**
   * Create secure response with security headers
   */
  private createSecureResponse(response: NextResponse, isAPI: boolean): NextResponse {
    // Generate nonce for CSP
    securityHeaders.generateNonce();
    
    // Apply security headers
    const secureResponse = securityHeaders.applyHeaders(response, isAPI);
    return secureResponse instanceof NextResponse ? secureResponse : NextResponse.next();
  }

  /**
   * Log security events
   */
  private logSecurityEvent(type: string, details: any): void {
    const event = {
      type,
      timestamp: new Date().toISOString(),
      details,
      environment: process.env.NODE_ENV,
    };

    console.warn(`[SECURITY] ${type}:`, event);

    // In production, you would send this to your logging service
    if (process.env.NODE_ENV === 'production') {
      // TODO: Send to logging service (e.g., Supabase, CloudWatch, etc.)
    }
  }

  /**
   * Get security metrics
   */
  getMetrics(): SecurityMetrics {
    this.metrics.lastUpdated = new Date();
    return { ...this.metrics };
  }
}

// Create singleton instance
const securityMiddleware = new ProductionSecurityMiddleware();

/**
 * Main middleware function
 */
export async function middleware(request: NextRequest) {
  return await securityMiddleware.process(request);
}

/**
 * Middleware configuration
 */
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    {
      source: '/((?!_next/static|_next/image|favicon.ico|public/).*)',
      missing: [
        { type: 'header', key: 'next-router-prefetch' },
        { type: 'header', key: 'purpose', value: 'prefetch' },
      ],
    },
  ],
};

// Export metrics access
export { securityMiddleware };