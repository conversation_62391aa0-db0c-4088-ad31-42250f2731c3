/**
 * Design System Showcase Page
 * Comprehensive demonstration of typography, colors, and design tokens
 */

'use client';

import React, { useState } from 'react';
import { 
  Typography, 
  H1, H2, H3, H4, H5, H6,
  Body, BodyLarge, BodySmall, Lead, Caption, Overline, Muted,
  Code, Blockquote, GradientText,
  TypographyShowcase
} from '@/components/ui/typography';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

// Color palette data for showcase
const colorPalettes = {
  primary: {
    name: 'Primary Blue',
    description: 'Professional brand color for CTAs and important elements',
    shades: {
      50: '#eff6ff',
      100: '#dbeafe',
      200: '#bfdbfe',
      300: '#93c5fd',
      400: '#60a5fa',
      500: '#3b82f6',
      600: '#2563eb',
      700: '#1d4ed8',
      800: '#1e40af',
      900: '#1e3a8a',
      950: '#172554'
    }
  },
  secondary: {
    name: 'Secondary Gray',
    description: 'Neutral colors for text, backgrounds, and borders',
    shades: {
      50: '#f8fafc',
      100: '#f1f5f9',
      200: '#e2e8f0',
      300: '#cbd5e1',
      400: '#94a3b8',
      500: '#64748b',
      600: '#475569',
      700: '#334155',
      800: '#1e293b',
      900: '#0f172a',
      950: '#020617'
    }
  },
  semantic: {
    success: { 500: '#10b981', description: 'Success states and positive actions' },
    warning: { 500: '#f59e0b', description: 'Warning states and caution' },
    error: { 500: '#ef4444', description: 'Error states and destructive actions' },
    info: { 500: '#3b82f6', description: 'Informational content' }
  }
};

// Typography specimens
const typographySpecimens = [
  { variant: 'h1', sample: 'The Future of Construction Intelligence' },
  { variant: 'h2', sample: 'Advanced Cost Calculation Engine' },
  { variant: 'h3', sample: 'Premium Features & Benefits' },
  { variant: 'h4', sample: 'Regional Pricing Analysis' },
  { variant: 'h5', sample: 'Material Cost Breakdown' },
  { variant: 'h6', sample: 'Construction Standards' },
  { variant: 'lead', sample: 'Transform your construction planning with AI-powered precision and professional-grade insights.' },
  { variant: 'body-large', sample: 'Large body text for emphasized content and important information that needs prominence.' },
  { variant: 'body', sample: 'Regular body text for most content. Optimized for readability across all devices and contexts.' },
  { variant: 'body-small', sample: 'Small body text for secondary information, metadata, and supporting content.' },
  { variant: 'caption', sample: 'Caption text for labels and auxiliary information' },
  { variant: 'overline', sample: 'Section Category' }
];

// Component showcase data
const componentShowcase = [
  {
    name: 'Buttons',
    description: 'Interactive elements with consistent styling',
    components: (
      <div className="flex flex-wrap gap-3">
        <Button>Primary Button</Button>
        <Button variant="secondary">Secondary</Button>
        <Button variant="outline">Outline</Button>
        <Button variant="ghost">Ghost</Button>
        <Button variant="destructive">Destructive</Button>
      </div>
    )
  },
  {
    name: 'Cards',
    description: 'Content containers with elevation and spacing',
    components: (
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Default Card</CardTitle>
            <CardDescription>Standard card layout with header content</CardDescription>
          </CardHeader>
          <CardContent>
            <Body>Card content area with proper spacing and typography hierarchy.</Body>
          </CardContent>
        </Card>
        <Card className="bg-primary text-primary-foreground">
          <CardHeader>
            <CardTitle>Primary Card</CardTitle>
            <CardDescription className="text-primary-foreground/80">Enhanced card with primary styling</CardDescription>
          </CardHeader>
          <CardContent>
            <Body className="text-primary-foreground">Premium content with branded appearance.</Body>
          </CardContent>
        </Card>
      </div>
    )
  },
  {
    name: 'Badges',
    description: 'Status indicators and labels',
    components: (
      <div className="flex flex-wrap gap-2">
        <Badge>Default</Badge>
        <Badge variant="secondary">Secondary</Badge>
        <Badge variant="outline">Outline</Badge>
        <Badge variant="destructive">Destructive</Badge>
        <Badge className="bg-success text-white">Success</Badge>
        <Badge className="bg-warning text-white">Warning</Badge>
      </div>
    )
  }
];

const DesignSystemPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('typography');
  const [isDarkMode, setIsDarkMode] = useState(false);

  const toggleDarkMode = () => {
    setIsDarkMode(!isDarkMode);
    document.documentElement.classList.toggle('dark');
  };

  const ColorSwatch: React.FC<{ color: string; name: string; description?: string }> = ({ 
    color, 
    name, 
    description 
  }) => (
    <div className="group relative overflow-hidden rounded-lg border bg-card">
      <div 
        className="h-16 w-full transition-all group-hover:h-20" 
        style={{ backgroundColor: color }}
      />
      <div className="p-3">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">{name}</span>
          <Code className="text-xs">{color}</Code>
        </div>
        {description && (
          <Caption className="mt-1">{description}</Caption>
        )}
      </div>
    </div>
  );

  const ColorPalette: React.FC<{ palette: any; name: string }> = ({ palette, name }) => (
    <div className="space-y-4">
      <div>
        <H4>{palette.name}</H4>
        <Caption>{palette.description}</Caption>
      </div>
      <div className="grid grid-cols-2 gap-3 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6">
        {Object.entries(palette.shades).map(([shade, color]) => (
          <ColorSwatch
            key={shade}
            color={color as string}
            name={`${name}-${shade}`}
          />
        ))}
      </div>
    </div>
  );

  return (
    <div className={cn('min-h-screen bg-background', isDarkMode && 'dark')}>
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <H1 className="mb-2">
                <GradientText>Design System</GradientText>
              </H1>
              <Lead>Enhanced MVP design system for the Nirmaan AI Construction Calculator</Lead>
            </div>
            <Button onClick={toggleDarkMode} variant="outline">
              {isDarkMode ? '☀️ Light' : '🌙 Dark'}
            </Button>
          </div>
        </div>

        {/* Navigation */}
        <div className="mb-8">
          <div className="flex flex-wrap gap-2">
            {['typography', 'colors', 'components', 'spacing', 'effects'].map((tab) => (
              <Button
                key={tab}
                variant={activeTab === tab ? 'default' : 'ghost'}
                onClick={() => setActiveTab(tab)}
                className="capitalize"
              >
                {tab}
              </Button>
            ))}
          </div>
        </div>

        {/* Content */}
        <div className="space-y-12">
          {/* Typography Section */}
          {activeTab === 'typography' && (
            <section className="space-y-8">
              <div>
                <H2>Typography System</H2>
                <Body>
                  Our typography system uses Inter for body text and Poppins for headings, 
                  providing excellent readability and a professional appearance.
                </Body>
              </div>

              {/* Font Information */}
              <Card>
                <CardHeader>
                  <CardTitle>Font Stack</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid gap-4 md:grid-cols-2">
                    <div>
                      <H5 className="mb-2">Primary Font - Inter</H5>
                      <Body>Used for body text, captions, and UI elements</Body>
                      <Code className="mt-2">font-family: 'Inter', sans-serif</Code>
                    </div>
                    <div>
                      <H5 className="mb-2">Display Font - Poppins</H5>
                      <Body>Used for headings and display text</Body>
                      <Code className="mt-2">font-family: 'Poppins', 'Inter', sans-serif</Code>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Typography Scale */}
              <Card>
                <CardHeader>
                  <CardTitle>Typography Scale</CardTitle>
                  <CardDescription>Responsive typography that scales across all screen sizes</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {typographySpecimens.map((specimen, index) => (
                      <div key={index} className="flex flex-col gap-2">
                        <div className="flex items-center justify-between">
                          <Caption className="uppercase">{specimen.variant}</Caption>
                          <Code className="text-xs">var(--typography-{specimen.variant})</Code>
                        </div>
                        <Typography variant={specimen.variant as any}>
                          {specimen.sample}
                        </Typography>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Typography Utilities */}
              <Card>
                <CardHeader>
                  <CardTitle>Typography Utilities</CardTitle>
                  <CardDescription>Pre-built typography classes for consistent styling</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-6 md:grid-cols-2">
                    <div>
                      <H5 className="mb-3">Font Weights</H5>
                      <div className="space-y-2">
                        <Body weight="light">Light (300)</Body>
                        <Body weight="normal">Normal (400)</Body>
                        <Body weight="medium">Medium (500)</Body>
                        <Body weight="semibold">Semibold (600)</Body>
                        <Body weight="bold">Bold (700)</Body>
                      </div>
                    </div>
                    <div>
                      <H5 className="mb-3">Text Colors</H5>
                      <div className="space-y-2">
                        <Body color="default">Default text</Body>
                        <Body color="primary">Primary text</Body>
                        <Body color="secondary">Secondary text</Body>
                        <Body color="success">Success text</Body>
                        <Body color="warning">Warning text</Body>
                        <Body color="error">Error text</Body>
                        <Body color="muted">Muted text</Body>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </section>
          )}

          {/* Colors Section */}
          {activeTab === 'colors' && (
            <section className="space-y-8">
              <div>
                <H2>Color System</H2>
                <Body>
                  Our color system provides a comprehensive palette with semantic meaning 
                  and accessibility-compliant contrast ratios.
                </Body>
              </div>

              {/* Primary & Secondary Palettes */}
              <ColorPalette palette={colorPalettes.primary} name="primary" />
              <ColorPalette palette={colorPalettes.secondary} name="secondary" />

              {/* Semantic Colors */}
              <div className="space-y-4">
                <H4>Semantic Colors</H4>
                <div className="grid grid-cols-2 gap-3 md:grid-cols-4">
                  {Object.entries(colorPalettes.semantic).map(([name, data]) => (
                    <ColorSwatch
                      key={name}
                      color={data[500]}
                      name={name}
                      description={data.description}
                    />
                  ))}
                </div>
              </div>

              {/* Color Usage Guidelines */}
              <Card>
                <CardHeader>
                  <CardTitle>Color Usage Guidelines</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid gap-4 md:grid-cols-2">
                    <div>
                      <H5 className="mb-2">Do's</H5>
                      <ul className="space-y-1 text-sm">
                        <li>✅ Use primary colors for main actions</li>
                        <li>✅ Use semantic colors for status indication</li>
                        <li>✅ Maintain consistent color meaning</li>
                        <li>✅ Test contrast ratios for accessibility</li>
                      </ul>
                    </div>
                    <div>
                      <H5 className="mb-2">Don'ts</H5>
                      <ul className="space-y-1 text-sm">
                        <li>❌ Don't use colors as the only indicator</li>
                        <li>❌ Don't use too many colors in one interface</li>
                        <li>❌ Don't ignore color blindness considerations</li>
                        <li>❌ Don't use low contrast combinations</li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </section>
          )}

          {/* Components Section */}
          {activeTab === 'components' && (
            <section className="space-y-8">
              <div>
                <H2>Component Library</H2>
                <Body>
                  Pre-built components using our design system tokens for consistent 
                  styling and behavior across the application.
                </Body>
              </div>

              {componentShowcase.map((component, index) => (
                <Card key={index}>
                  <CardHeader>
                    <CardTitle>{component.name}</CardTitle>
                    <CardDescription>{component.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    {component.components}
                  </CardContent>
                </Card>
              ))}
            </section>
          )}

          {/* Spacing Section */}
          {activeTab === 'spacing' && (
            <section className="space-y-8">
              <div>
                <H2>Spacing System</H2>
                <Body>
                  Consistent spacing scale based on a 4px grid system for 
                  predictable layouts and visual rhythm.
                </Body>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle>Spacing Scale</CardTitle>
                  <CardDescription>Based on 4px increments for consistent layouts</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {[1, 2, 3, 4, 5, 6, 8, 10, 12, 16, 20, 24].map((size) => (
                      <div key={size} className="flex items-center gap-4">
                        <Code className="w-16 text-right">sp-{size}</Code>
                        <div 
                          className="bg-primary h-4"
                          style={{ width: `${size * 4}px` }}
                        />
                        <Caption>{size * 4}px</Caption>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </section>
          )}

          {/* Effects Section */}
          {activeTab === 'effects' && (
            <section className="space-y-8">
              <div>
                <H2>Visual Effects</H2>
                <Body>
                  Modern visual effects including gradients, shadows, and animations 
                  that enhance the user experience.
                </Body>
              </div>

              {/* Shadows */}
              <Card>
                <CardHeader>
                  <CardTitle>Elevation Shadows</CardTitle>
                  <CardDescription>Consistent shadow system for depth and hierarchy</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
                    {['sm', 'md', 'lg', 'xl', '2xl'].map((size) => (
                      <div key={size} className="text-center">
                        <div 
                          className={`mx-auto mb-3 h-20 w-20 rounded-lg bg-card elevation-${size === '2xl' ? '5' : size === 'xl' ? '4' : size === 'lg' ? '3' : size === 'md' ? '2' : '1'}`}
                        />
                        <Caption>Shadow {size}</Caption>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Gradients */}
              <Card>
                <CardHeader>
                  <CardTitle>Gradient Effects</CardTitle>
                  <CardDescription>Professional gradients for premium visual appeal</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <div className="h-20 rounded-lg gradient-primary flex items-center justify-center">
                      <span className="text-white font-semibold">Primary Gradient</span>
                    </div>
                    <div className="h-20 rounded-lg gradient-accent flex items-center justify-center">
                      <span className="text-white font-semibold">Accent Gradient</span>
                    </div>
                  </div>
                  <div className="mt-4">
                    <H4>
                      <GradientText>Text Gradient Effect</GradientText>
                    </H4>
                  </div>
                </CardContent>
              </Card>

              {/* Interactive States */}
              <Card>
                <CardHeader>
                  <CardTitle>Interactive States</CardTitle>
                  <CardDescription>Smooth transitions and micro-interactions</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="interactive bg-card p-4 rounded-lg border cursor-pointer">
                      <Body>Hover over this card to see the interactive effect</Body>
                    </div>
                    <div className="glass p-4 rounded-lg">
                      <Body>Glass morphism effect with backdrop blur</Body>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </section>
          )}
        </div>

        {/* Footer */}
        <footer className="mt-16 border-t pt-8">
          <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
            <div>
              <Body>Enhanced MVP Design System</Body>
              <Caption>Built for Nirmaan AI Construction Calculator</Caption>
            </div>
            <div className="flex gap-2">
              <Badge variant="outline">v1.0.0</Badge>
              <Badge variant="outline">Inter + Poppins</Badge>
              <Badge variant="outline">CSS Variables</Badge>
            </div>
          </div>
        </footer>
      </div>
    </div>
  );
};

export default DesignSystemPage;