/**
 * Mobile Results Display Component
 * Provides mobile-optimized bottom sheet results display with touch-friendly interactions
 */

'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Share2, 
  Download, 
  Calculator, 
  Home, 
  PieChart, 
  TrendingUp,
  ChevronUp,
  ChevronDown 
} from 'lucide-react';

import { cn } from '@/lib/utils';
import { 
  hapticFeedback, 
  shareContent, 
  detectDevice, 
  isMobileViewport,
  mobileClasses 
} from '@/lib/mobile';
import { MobileSheet, MobileActionSheet } from '@/components/ui/mobile-sheet';
import { PDFExportButton } from '@/components/ui/pdf-export-button';
import { CountUp } from '@/components/ui/count-up';
import { ProgressBar } from '@/components/ui/status-indicators';

interface CalculationResult {
  totalCost: number;
  costPerSqft: number;
  breakdown: {
    structure: number;
    finishing: number;
    mep: number;
    other: number;
  };
  builtUpArea: number;
  quality: string;
  location: string;
}

interface MobileResultsDisplayProps {
  result: CalculationResult;
  isOpen: boolean;
  onClose: () => void;
  className?: string;
}

export function MobileResultsDisplay({
  result,
  isOpen,
  onClose,
  className
}: MobileResultsDisplayProps) {
  const [activeTab, setActiveTab] = useState<'summary' | 'breakdown' | 'details'>('summary');
  const [showActionSheet, setShowActionSheet] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);

  const device = detectDevice();
  const isMobile = isMobileViewport();

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatLargeNumber = (amount: number) => {
    if (amount >= 10000000) {
      return `₹${(amount / 10000000).toFixed(1)}Cr`;
    } else if (amount >= 100000) {
      return `₹${(amount / 100000).toFixed(1)}L`;
    } else {
      return formatCurrency(amount);
    }
  };

  const getBreakdownPercentage = (amount: number) => {
    return (amount / result.totalCost) * 100;
  };

  const handleShare = async () => {
    hapticFeedback.light();
    
    const shareData = {
      title: 'Construction Cost Estimate',
      text: `My construction cost estimate: ${formatCurrency(result.totalCost)} for ${result.builtUpArea} sq ft`,
      url: window.location.href,
    };

    const shareResult = await shareContent(shareData);
    
    if (shareResult.success) {
      console.log('Shared successfully');
    } else {
      console.log('Share failed, fallback to action sheet');
      setShowActionSheet(true);
    }
  };

  const handleTabChange = (tab: typeof activeTab) => {
    setActiveTab(tab);
    hapticFeedback.light();
  };

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
    hapticFeedback.light();
  };

  const breakdownItems = [
    {
      label: 'Structure & Foundation',
      amount: result.breakdown.structure,
      percentage: getBreakdownPercentage(result.breakdown.structure),
      color: 'bg-blue-500',
      icon: Home,
    },
    {
      label: 'Finishing & Interiors',
      amount: result.breakdown.finishing,
      percentage: getBreakdownPercentage(result.breakdown.finishing),
      color: 'bg-green-500',
      icon: PieChart,
    },
    {
      label: 'MEP Systems',
      amount: result.breakdown.mep,
      percentage: getBreakdownPercentage(result.breakdown.mep),
      color: 'bg-yellow-500',
      icon: TrendingUp,
    },
    {
      label: 'Professional Fees & Others',
      amount: result.breakdown.other,
      percentage: getBreakdownPercentage(result.breakdown.other),
      color: 'bg-purple-500',
      icon: Calculator,
    },
  ];

  const actionSheetActions = [
    {
      label: 'Share via WhatsApp',
      onClick: () => {
        const text = `Construction Cost Estimate: ${formatCurrency(result.totalCost)} for ${result.builtUpArea} sq ft`;
        window.open(`https://wa.me/?text=${encodeURIComponent(text)}`);
      },
      icon: <Share2 className="h-5 w-5" />,
    },
    {
      label: 'Copy to Clipboard',
      onClick: async () => {
        const text = `Construction Cost Estimate: ${formatCurrency(result.totalCost)} for ${result.builtUpArea} sq ft`;
        await navigator.clipboard.writeText(text);
      },
      icon: <Download className="h-5 w-5" />,
    },
  ];

  const tabVariants = {
    active: {
      backgroundColor: '#3b82f6',
      color: '#ffffff',
      scale: 1,
    },
    inactive: {
      backgroundColor: '#f3f4f6',
      color: '#6b7280',
      scale: 0.95,
    },
  };

  const contentVariants = {
    enter: {
      opacity: 0,
      y: 20,
    },
    center: {
      opacity: 1,
      y: 0,
    },
    exit: {
      opacity: 0,
      y: -20,
    },
  };

  return (
    <>
      <MobileSheet
        isOpen={isOpen}
        onClose={onClose}
        title="Cost Estimation Results"
        className={className}
      >
        <div className="space-y-6">
          {/* Summary Card */}
          <div className="bg-gradient-to-r from-blue-50 to-blue-100 rounded-xl p-6">
            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <Calculator className="h-6 w-6 text-blue-600 mr-2" />
                <h3 className="text-lg font-semibold text-gray-800">
                  Total Estimated Cost
                </h3>
              </div>
              
              <div className="text-3xl font-bold text-blue-600 mb-2">
                <CountUp
                  value={result.totalCost}
                  formatter={formatLargeNumber}
                  duration={1.5}
                />
              </div>
              
              <p className="text-sm text-gray-600 mb-4">
                {formatCurrency(result.costPerSqft)} per sq ft
              </p>
              
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <div className="text-sm text-gray-500">Area</div>
                  <div className="font-medium">{result.builtUpArea} sq ft</div>
                </div>
                <div>
                  <div className="text-sm text-gray-500">Quality</div>
                  <div className="font-medium capitalize">{result.quality}</div>
                </div>
                <div>
                  <div className="text-sm text-gray-500">Location</div>
                  <div className="font-medium capitalize">{result.location}</div>
                </div>
              </div>
            </div>
          </div>

          {/* Tab Navigation */}
          <div className="flex bg-gray-100 rounded-lg p-1">
            {(['summary', 'breakdown', 'details'] as const).map((tab) => (
              <motion.button
                key={tab}
                onClick={() => handleTabChange(tab)}
                className={cn(
                  'flex-1 py-2 px-4 rounded-md text-sm font-medium transition-all',
                  mobileClasses.touchTarget
                )}
                variants={tabVariants}
                animate={activeTab === tab ? 'active' : 'inactive'}
                whileTap={{ scale: 0.95 }}
              >
                {tab.charAt(0).toUpperCase() + tab.slice(1)}
              </motion.button>
            ))}
          </div>

          {/* Tab Content */}
          <AnimatePresence mode="wait">
            <motion.div
              key={activeTab}
              variants={contentVariants}
              initial="enter"
              animate="center"
              exit="exit"
              transition={{ duration: 0.2 }}
            >
              {activeTab === 'summary' && (
                <div className="space-y-4">
                  <div className="bg-white rounded-lg p-4 border">
                    <h4 className="font-semibold text-gray-800 mb-3">Quick Overview</h4>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="text-center p-3 bg-blue-50 rounded-lg">
                        <div className="text-2xl font-bold text-blue-600">
                          {formatLargeNumber(result.breakdown.structure)}
                        </div>
                        <div className="text-xs text-gray-600">Structure</div>
                      </div>
                      <div className="text-center p-3 bg-green-50 rounded-lg">
                        <div className="text-2xl font-bold text-green-600">
                          {formatLargeNumber(result.breakdown.finishing)}
                        </div>
                        <div className="text-xs text-gray-600">Finishing</div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'breakdown' && (
                <div className="space-y-4">
                  {breakdownItems.map((item, index) => {
                    const Icon = item.icon;
                    return (
                      <motion.div
                        key={item.label}
                        className="bg-white rounded-lg p-4 border"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.1 }}
                      >
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center">
                            <div className={cn('p-2 rounded-lg mr-3', item.color.replace('bg-', 'bg-opacity-20 text-'))}>
                              <Icon className="h-4 w-4" />
                            </div>
                            <div>
                              <div className="font-medium text-gray-800">{item.label}</div>
                              <div className="text-sm text-gray-500">{item.percentage.toFixed(1)}%</div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="font-bold text-gray-800">
                              {formatLargeNumber(item.amount)}
                            </div>
                          </div>
                        </div>
                        <ProgressBar
                          value={item.percentage}
                          max={100}
                          size="sm"
                          color={item.color.includes('blue') ? 'blue' : 
                                item.color.includes('green') ? 'green' :
                                item.color.includes('yellow') ? 'yellow' : 'purple'}
                          showPercentage={false}
                        />
                      </motion.div>
                    );
                  })}
                </div>
              )}

              {activeTab === 'details' && (
                <div className="space-y-4">
                  <div className="bg-white rounded-lg p-4 border">
                    <h4 className="font-semibold text-gray-800 mb-3">Project Details</h4>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Built-up Area</span>
                        <span className="font-medium">{result.builtUpArea} sq ft</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Quality Tier</span>
                        <span className="font-medium capitalize">{result.quality}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Location</span>
                        <span className="font-medium capitalize">{result.location}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Cost per sq ft</span>
                        <span className="font-medium">{formatCurrency(result.costPerSqft)}</span>
                      </div>
                    </div>
                  </div>

                  <div className="bg-yellow-50 rounded-lg p-4 border border-yellow-200">
                    <p className="text-sm text-yellow-800">
                      <strong>Note:</strong> These are preliminary estimates based on
                      current market rates. Actual costs may vary based on specific
                      requirements, material choices, and market conditions.
                    </p>
                  </div>
                </div>
              )}
            </motion.div>
          </AnimatePresence>

          {/* Action Buttons */}
          <div className="flex gap-3">
            <button
              onClick={handleShare}
              className={cn(
                'flex-1 flex items-center justify-center gap-2 py-3 px-4',
                'bg-blue-600 text-white rounded-lg font-medium',
                'hover:bg-blue-700 transition-colors',
                mobileClasses.touchTarget
              )}
            >
              <Share2 className="h-4 w-4" />
              Share
            </button>
            
            <PDFExportButton
              className={cn(
                'flex-1 flex items-center justify-center gap-2 py-3 px-4',
                mobileClasses.touchTarget
              )}
              variant="outline"
            />
          </div>

          {/* Expand/Collapse Button */}
          <button
            onClick={toggleExpanded}
            className={cn(
              'w-full flex items-center justify-center gap-2 py-2 text-gray-500',
              'hover:text-gray-700 transition-colors',
              mobileClasses.touchTarget
            )}
          >
            {isExpanded ? <ChevronDown className="h-4 w-4" /> : <ChevronUp className="h-4 w-4" />}
            {isExpanded ? 'Show Less' : 'Show More'}
          </button>
        </div>
      </MobileSheet>

      {/* Action Sheet */}
      <MobileActionSheet
        isOpen={showActionSheet}
        onClose={() => setShowActionSheet(false)}
        title="Share Results"
        actions={actionSheetActions}
      />
    </>
  );
}

interface MobileCalculatorSummaryProps {
  result: CalculationResult;
  className?: string;
}

export function MobileCalculatorSummary({
  result,
  className
}: MobileCalculatorSummaryProps) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <div className={cn('bg-white rounded-lg shadow-lg p-4', className)}>
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center">
          <Calculator className="h-5 w-5 text-blue-600 mr-2" />
          <h3 className="text-lg font-semibold text-gray-800">
            Cost Summary
          </h3>
        </div>
        <div className="text-right">
          <div className="text-2xl font-bold text-blue-600">
            <CountUp
              value={result.totalCost}
              formatter={formatCurrency}
              duration={1.5}
            />
          </div>
          <div className="text-sm text-gray-500">
            {formatCurrency(result.costPerSqft)}/sq ft
          </div>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-3 text-sm">
        <div className="bg-gray-50 rounded-lg p-3">
          <div className="text-gray-600 mb-1">Area</div>
          <div className="font-medium">{result.builtUpArea} sq ft</div>
        </div>
        <div className="bg-gray-50 rounded-lg p-3">
          <div className="text-gray-600 mb-1">Quality</div>
          <div className="font-medium capitalize">{result.quality}</div>
        </div>
      </div>
    </div>
  );
}