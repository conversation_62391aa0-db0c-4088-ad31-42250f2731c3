# Nirmaan AI Codebase Cleanup Script - SAFE PHASE 1
# This script will clean up unnecessary files and reduce project size by ~400+ MB
# SAFE APPROACH: Only removes clearly unnecessary files

Write-Host "🧹 Starting Nirmaan AI Codebase Cleanup (SAFE PHASE 1)..." -ForegroundColor Green

# Create backup first
Write-Host "Creating backup branch..." -ForegroundColor Cyan
git checkout -b "backup/pre-cleanup-$(Get-Date -Format 'yyyy-MM-dd-HH-mm')" 2>$null
git checkout main 2>$null

# 1. Remove build and cache files (387.99 MB)
Write-Host "Removing build and cache files..." -ForegroundColor Yellow
if (Test-Path ".next") { Remove-Item -Recurse -Force ".next" }
if (Test-Path "tsconfig.tsbuildinfo") { Remove-Item -Force "tsconfig.tsbuildinfo" }

# 2. Remove development log files
Write-Host "Removing development log files..." -ForegroundColor Yellow
$logFiles = @(
    "dev-output.log", "dev-3001.log", "dev-3001.pid", "dev-3011.log",
    "dev-3011.pid", "dev-server.log", "dev.log", "dev.pid"
)
foreach ($file in $logFiles) {
    if (Test-Path $file) { Remove-Item -Force $file }
}

# 3. Remove duplicate configuration files
Write-Host "Removing duplicate configuration files..." -ForegroundColor Yellow
$duplicateConfigs = @(
    "next.config.ts", "next.config.minimal.js", "next.config.complex.ts",
    "playwright.enhanced.config.ts", "playwright.test.config.ts"
)
foreach ($file in $duplicateConfigs) {
    if (Test-Path $file) { Remove-Item -Force $file }
}

# 4. Remove generated test reports and results
Write-Host "Removing generated test reports..." -ForegroundColor Yellow
$reportDirs = @("playwright-report", "test-results", "tests/results")
foreach ($dir in $reportDirs) {
    if (Test-Path $dir) { Remove-Item -Recurse -Force $dir }
}

$reportFiles = @(
    "comprehensive-api-integration-test-report-*.json",
    "cross-browser-compatibility-report-*.json",
    "database-integration-test-report-*.json",
    "simulated-load-stress-test-report-*.json",
    "security-validation-results.json"
)
foreach ($pattern in $reportFiles) {
    Get-ChildItem -Path . -Name $pattern | ForEach-Object { Remove-Item -Force $_ }
}

# 5. Remove redundant documentation (OPTIONAL - Review before running)
Write-Host "Removing redundant documentation (OPTIONAL)..." -ForegroundColor Yellow
$redundantDocs = @(
    "COMPREHENSIVE_EXECUTION_PLAN.md",
    "COMPREHENSIVE_USER_GUIDE.md",
    "MOBILE_ACCESSIBILITY_MANUAL_TESTING_GUIDE.md",
    "API_TESTING_GUIDE.md",
    "COMPREHENSIVE_API_INTEGRATION_TEST_REPORT.md",
    "OPENAPI_SPEC.yaml"
)
# Uncomment the next 3 lines if you want to remove redundant docs
# foreach ($file in $redundantDocs) {
#     if (Test-Path $file) { Remove-Item -Force $file }
# }

# 6. Remove standalone test scripts (move to tests/ directory first if needed)
Write-Host "Removing standalone test scripts..." -ForegroundColor Yellow
$testScripts = @(
    "comprehensive-database-integration-test.js",
    "comprehensive-security-test.js",
    "api-analysis-and-test.js",
    "comprehensive-api-test.js",
    "simulated-load-stress-test.js",
    "comprehensive-load-stress-test.js",
    "comprehensive-cross-browser-test.js",
    "focused-security-test.js",
    "security-validation-summary.js"
)
foreach ($file in $testScripts) {
    if (Test-Path $file) { Remove-Item -Force $file }
}

# 7. Remove demo and temporary files
Write-Host "Removing demo and temporary files..." -ForegroundColor Yellow
$tempFiles = @(
    "demo.html", "demo-pdf-feature.md", "test-data-testid.md", "quick-api-test.sh"
)
foreach ($file in $tempFiles) {
    if (Test-Path $file) { Remove-Item -Force $file }
}

# 8. Clean up empty directories
Write-Host "Cleaning up empty directories..." -ForegroundColor Yellow
Get-ChildItem -Path . -Recurse -Directory | Where-Object {
    (Get-ChildItem -Path $_.FullName -Recurse -File).Count -eq 0
} | Remove-Item -Recurse -Force

Write-Host "✅ Cleanup completed! Project size reduced significantly." -ForegroundColor Green
Write-Host "💡 Run 'npm run build' to regenerate build files when needed." -ForegroundColor Cyan
