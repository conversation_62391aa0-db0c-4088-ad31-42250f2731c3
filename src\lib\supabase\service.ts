/**
 * Supabase Service Client with Full Write Access
 * Uses service role key for admin operations
 */

import { createClient } from '@supabase/supabase-js';
import type { Database } from '@/types/supabase';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

if (!supabaseServiceKey) {
  throw new Error('SUPABASE_SERVICE_ROLE_KEY environment variable is required');
}

/**
 * Service client with full admin privileges
 * Use this for server-side operations that require bypassing RLS
 */
export const supabaseService = createClient<Database>(
  supabaseUrl,
  supabaseServiceKey,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  }
);

/**
 * Test the service client connection and permissions
 */
export async function testServiceConnection() {
  try {
    // Test basic connection
    const { data: _version, error: versionError } = await supabaseService
      .rpc('version' as string)
      .single();

    if (versionError) {
      console.warn('Version check failed:', versionError);
    }

    // Test table access
    const { data: tables, error: tablesError } = await supabaseService
      .from('information_schema.tables' as string)
      .select('table_name')
      .eq('table_schema', 'public');

    if (tablesError) {
      throw new Error(`Tables access failed: ${tablesError.message}`);
    }

    // Test materials table access
    const { data: materials, error: materialsError } = await supabaseService
      .from('materials')
      .select('count', { count: 'exact', head: true });

    if (materialsError) {
      throw new Error(
        `Materials table access failed: ${materialsError.message}`
      );
    }

    return {
      success: true,
      tables: tables?.length || 0,
      materialsCount: materials || 0,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Insert initial materials data using service client
 */
export async function seedMaterialsData() {
  const materialsData = [
    {
      category: 'Cement',
      name: 'OPC 53 Grade Cement',
      brand: 'UltraTech',
      unit: 'bag (50kg)',
      base_price: 420.0,
      specifications: {
        grade: '53',
        type: 'Ordinary Portland Cement',
        standard: 'IS 12269:2013',
        compressive_strength: '53 MPa',
      },
      pricing: {
        bangalore: { retail: 420, bulk: 400 },
        mumbai: { retail: 440, bulk: 420 },
        delhi: { retail: 430, bulk: 410 },
        default: { retail: 425, bulk: 405 },
      },
      quality_score: 9.5,
      popularity_rank: 1,
    },
    // Add more materials as needed
  ];

  try {
    const { data, error } = await supabaseService
      .from('materials')
      .insert(materialsData)
      .select();

    if (error) {
      throw new Error(`Failed to seed materials: ${error.message}`);
    }

    return { success: true, count: data?.length || 0 };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}
