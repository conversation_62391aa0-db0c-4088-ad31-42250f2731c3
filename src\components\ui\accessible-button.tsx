/**
 * Accessible Button Component
 * Fully accessible button with comprehensive WCAG 2.1 AA compliance
 */

import * as React from 'react';
import { Slot } from '@radix-ui/react-slot';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';
import { 
  useFocusManagement, 
  useScreenReader, 
  useMobileTouchAccessibility 
} from '@/lib/accessibility';

const accessibleButtonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:ring-ring/50 focus-visible:ring-[3px] focus-visible:ring-offset-2 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive min-h-[44px] min-w-[44px] relative",
  {
    variants: {
      variant: {
        default:
          'bg-primary text-primary-foreground shadow-xs hover:bg-primary/90 focus-visible:ring-primary/50',
        destructive:
          'bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/50 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',
        outline:
          'border-2 bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50 focus-visible:border-primary',
        secondary:
          'bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80 focus-visible:ring-secondary/50',
        ghost:
          'hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50 focus-visible:ring-accent/50',
        link: 'text-primary underline-offset-4 hover:underline focus-visible:ring-primary/50',
      },
      size: {
        default: 'h-11 px-4 py-2 has-[>svg]:px-3',
        sm: 'h-9 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5',
        lg: 'h-12 rounded-md px-6 has-[>svg]:px-4',
        icon: 'size-11',
      },
      loading: {
        true: 'cursor-wait',
        false: ''
      }
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
      loading: false
    },
  }
);

interface AccessibleButtonProps
  extends React.ComponentProps<'button'>,
    VariantProps<typeof accessibleButtonVariants> {
  asChild?: boolean;
  loading?: boolean;
  loadingText?: string;
  description?: string;
  shortcut?: string;
  hapticFeedback?: boolean;
  announceClick?: boolean;
  confirmAction?: boolean;
  confirmMessage?: string;
}

const AccessibleButton = React.forwardRef<HTMLButtonElement, AccessibleButtonProps>(
  ({
    className,
    variant,
    size,
    loading = false,
    loadingText = 'Loading...',
    description,
    shortcut,
    hapticFeedback = true,
    announceClick = false,
    confirmAction = false,
    confirmMessage = 'Are you sure?',
    asChild = false,
    children,
    onClick,
    onKeyDown,
    disabled,
    'aria-label': ariaLabel,
    'aria-describedby': ariaDescribedby,
    ...props
  }, ref) => {
    const [isPressed, setIsPressed] = React.useState(false);
    const [showConfirm, setShowConfirm] = React.useState(false);
    const buttonRef = React.useRef<HTMLButtonElement>(null);
    const descriptionId = React.useId();
    const shortcutId = React.useId();

    const { announce } = useScreenReader();
    const { addHapticFeedback, isTouchDevice } = useMobileTouchAccessibility();

    // Combine refs
    React.useImperativeHandle(ref, () => buttonRef.current!);

    // Enhanced click handler
    const handleClick = React.useCallback((event: React.MouseEvent<HTMLButtonElement>) => {
      if (loading || disabled) {
        event.preventDefault();
        return;
      }

      // Confirmation dialog for destructive actions
      if (confirmAction && !showConfirm) {
        event.preventDefault();
        setShowConfirm(true);
        announce(confirmMessage);
        return;
      }

      // Reset confirmation state
      if (showConfirm) {
        setShowConfirm(false);
      }

      // Haptic feedback for touch devices
      if (hapticFeedback && isTouchDevice()) {
        addHapticFeedback(variant === 'destructive' ? 'heavy' : 'medium');
      }

      // Screen reader announcement
      if (announceClick) {
        const actionText = children?.toString() || ariaLabel || 'Button activated';
        announce(actionText);
      }

      // Call original onClick
      onClick?.(event);
    }, [loading, disabled, confirmAction, showConfirm, confirmMessage, hapticFeedback, isTouchDevice, addHapticFeedback, variant, announceClick, children, ariaLabel, announce, onClick]);

    // Enhanced keyboard handler
    const handleKeyDown = React.useCallback((event: React.KeyboardEvent<HTMLButtonElement>) => {
      // Handle Enter and Space keys
      if (event.key === 'Enter' || event.key === ' ') {
        event.preventDefault();
        setIsPressed(true);
        
        // Directly call the click handler instead of dispatching event
        const syntheticEvent = {
          ...event,
          preventDefault: () => {},
          stopPropagation: () => {},
          target: buttonRef.current,
          currentTarget: buttonRef.current,
          type: 'click'
        } as React.MouseEvent<HTMLButtonElement>;
        
        handleClick(syntheticEvent);
      }

      // Handle Escape key for confirmation
      if (event.key === 'Escape' && showConfirm) {
        setShowConfirm(false);
        announce('Action cancelled');
      }

      onKeyDown?.(event);
    }, [showConfirm, announce, onKeyDown, handleClick]);

    // Handle key up to reset pressed state
    const handleKeyUp = React.useCallback((event: React.KeyboardEvent<HTMLButtonElement>) => {
      if (event.key === 'Enter' || event.key === ' ') {
        setIsPressed(false);
      }
    }, []);

    // Handle mouse/touch events for visual feedback
    const handleMouseDown = React.useCallback(() => {
      setIsPressed(true);
    }, []);

    const handleMouseUp = React.useCallback(() => {
      setIsPressed(false);
    }, []);

    const handleMouseLeave = React.useCallback(() => {
      setIsPressed(false);
    }, []);

    // Build ARIA describedby
    const buildAriaDescribedby = React.useCallback(() => {
      const ids = [];
      if (description) ids.push(descriptionId);
      if (shortcut) ids.push(shortcutId);
      if (ariaDescribedby) ids.push(ariaDescribedby);
      return ids.length > 0 ? ids.join(' ') : undefined;
    }, [description, shortcut, ariaDescribedby, descriptionId, shortcutId]);

    // Generate accessible label
    const generateAriaLabel = React.useCallback(() => {
      if (ariaLabel) return ariaLabel;
      
      let label = children?.toString() || '';
      
      if (loading) {
        label = loadingText;
      } else if (showConfirm) {
        label = `${confirmMessage} ${label}`;
      }
      
      if (shortcut) {
        label += ` (${shortcut})`;
      }
      
      return label || undefined;
    }, [ariaLabel, children, loading, loadingText, showConfirm, confirmMessage, shortcut]);

    const Comp = asChild ? Slot : 'button';

    return (
      <>
        <Comp
          ref={buttonRef}
          data-slot="button"
          className={cn(
            accessibleButtonVariants({ variant, size, loading, className }),
            isPressed && 'scale-[0.98] brightness-95',
            showConfirm && 'ring-2 ring-yellow-500 ring-offset-2'
          )}
          disabled={disabled || loading}
          aria-label={generateAriaLabel()}
          aria-describedby={buildAriaDescribedby()}
          aria-busy={loading}
          aria-pressed={variant === 'ghost' ? isPressed : undefined}
          onClick={handleClick}
          onKeyDown={handleKeyDown}
          onKeyUp={handleKeyUp}
          onMouseDown={handleMouseDown}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseLeave}
          {...props}
        >
          {/* Loading spinner */}
          {loading && (
            <div
              className="absolute inset-0 flex items-center justify-center"
              aria-hidden="true"
            >
              <div className="animate-spin rounded-full h-4 w-4 border-2 border-current border-t-transparent" />
            </div>
          )}

          {/* Button content */}
          <span className={cn(loading && 'opacity-0')}>
            {showConfirm ? (
              <>
                <span className="sr-only">{confirmMessage}</span>
                <span aria-hidden="true">⚠️</span>
                {children}
              </>
            ) : (
              children
            )}
          </span>

          {/* Loading text for screen readers */}
          {loading && (
            <span className="sr-only">{loadingText}</span>
          )}
        </Comp>

        {/* Hidden description */}
        {description && (
          <div id={descriptionId} className="sr-only">
            {description}
          </div>
        )}

        {/* Hidden shortcut info */}
        {shortcut && (
          <div id={shortcutId} className="sr-only">
            Keyboard shortcut: {shortcut}
          </div>
        )}

        {/* Confirmation toast/overlay */}
        {showConfirm && (
          <div
            className="fixed top-4 left-1/2 transform -translate-x-1/2 bg-yellow-100 border-2 border-yellow-500 rounded-lg p-4 shadow-lg z-50"
            role="alert"
            aria-live="assertive"
          >
            <div className="flex items-center gap-2">
              <span className="text-yellow-800">⚠️ {confirmMessage}</span>
              <button
                className="text-sm text-yellow-700 underline ml-2"
                onClick={() => {
                  setShowConfirm(false);
                  announce('Action cancelled');
                }}
              >
                Cancel
              </button>
            </div>
          </div>
        )}
      </>
    );
  }
);

AccessibleButton.displayName = 'AccessibleButton';

// Higher-order component for common button patterns
export const createAccessibleButton = (defaultProps: Partial<AccessibleButtonProps>) => {
  return React.forwardRef<HTMLButtonElement, AccessibleButtonProps>((props, ref) => (
    <AccessibleButton ref={ref} {...defaultProps} {...props} />
  ));
};

// Pre-configured button variants
export const PrimaryButton = createAccessibleButton({
  variant: 'default',
  hapticFeedback: true,
  announceClick: true
});

export const SecondaryButton = createAccessibleButton({
  variant: 'secondary',
  hapticFeedback: true
});

export const DestructiveButton = createAccessibleButton({
  variant: 'destructive',
  confirmAction: true,
  confirmMessage: 'This action cannot be undone. Continue?',
  hapticFeedback: true,
  announceClick: true
});

export const CalculateButton = createAccessibleButton({
  variant: 'default',
  size: 'lg',
  hapticFeedback: true,
  announceClick: true,
  description: 'Calculate construction cost based on entered parameters',
  shortcut: 'Ctrl+Enter'
});

export const ResetButton = createAccessibleButton({
  variant: 'outline',
  confirmAction: true,
  confirmMessage: 'Reset all form fields?',
  hapticFeedback: true,
  shortcut: 'Ctrl+R'
});

export { AccessibleButton, accessibleButtonVariants };
export type { AccessibleButtonProps };