#!/bin/bash

# Quick API Testing Script for Nirmaan AI Construction Calculator
# This script tests all major API endpoints and provides immediate feedback

BASE_URL="http://localhost:3001/api"
TEMP_FILE="/tmp/api_test_response.json"

echo "🚀 Nirmaan AI Construction Calculator - Quick API Test"
echo "======================================================"
echo "Target: $BASE_URL"
echo "Timestamp: $(date)"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to test an endpoint
test_endpoint() {
    local method=$1
    local endpoint=$2
    local data=$3
    local expected_status=$4
    local test_name=$5
    
    echo -n "Testing $test_name... "
    
    if [ "$method" = "GET" ]; then
        response=$(curl -s -w "%{http_code}" -o "$TEMP_FILE" "$BASE_URL$endpoint" 2>/dev/null)
    else
        response=$(curl -s -w "%{http_code}" -o "$TEMP_FILE" -X "$method" \
            -H "Content-Type: application/json" \
            -d "$data" \
            "$BASE_URL$endpoint" 2>/dev/null)
    fi
    
    status_code="${response: -3}"
    
    if [ "$status_code" = "$expected_status" ]; then
        echo -e "${GREEN}✅ PASS${NC} (HTTP $status_code)"
        if [ -f "$TEMP_FILE" ]; then
            response_body=$(cat "$TEMP_FILE")
            if [[ $response_body == *"success"* ]] || [[ $response_body == *"status"* ]] || [[ $response_body == *"name"* ]]; then
                echo "   Response: $(echo "$response_body" | jq -r '.success // .status // .name // "Valid JSON"' 2>/dev/null || echo "Valid response")"
            fi
        fi
    else
        echo -e "${RED}❌ FAIL${NC} (Expected: $expected_status, Got: $status_code)"
        if [ -f "$TEMP_FILE" ]; then
            error_msg=$(cat "$TEMP_FILE" | jq -r '.error.message // .message // "Unknown error"' 2>/dev/null || echo "Server response error")
            echo "   Error: $error_msg"
        fi
    fi
    echo ""
}

# Function to check if server is running
check_server() {
    echo "🔍 Checking if server is running..."
    if curl -s "$BASE_URL/health" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Server is running${NC}"
        return 0
    else
        echo -e "${RED}❌ Server is not responding${NC}"
        echo "Please start the development server with: npm run dev"
        return 1
    fi
}

# Check server availability
if ! check_server; then
    exit 1
fi

echo ""
echo "🧪 Running API Tests..."
echo "======================="
echo ""

# Test 1: Health Check
test_endpoint "GET" "/health" "" "200" "Health Check"

# Test 2: API Documentation
test_endpoint "GET" "/calculate" "" "200" "API Documentation"

# Test 3: Basic Calculation - Smart Quality
basic_calc='{
  "builtUpArea": 1000,
  "floors": 1,
  "qualityTier": "smart",
  "location": "bangalore"
}'
test_endpoint "POST" "/calculate" "$basic_calc" "200" "Basic Calculation (Smart)"

# Test 4: Premium Calculation with Features
premium_calc='{
  "builtUpArea": 1500,
  "floors": 2,
  "qualityTier": "premium",
  "location": "mumbai",
  "hasStilt": true,
  "parkingType": "covered"
}'
test_endpoint "POST" "/calculate" "$premium_calc" "200" "Premium Calculation"

# Test 5: Luxury with Special Features
luxury_calc='{
  "builtUpArea": 2500,
  "floors": 3,
  "qualityTier": "luxury",
  "location": "delhi",
  "hasBasement": true,
  "specialFeatures": [
    {
      "name": "Swimming Pool",
      "cost": 800000,
      "description": "Olympic size pool"
    }
  ]
}'
test_endpoint "POST" "/calculate" "$luxury_calc" "200" "Luxury with Features"

# Test 6: Input Validation - Missing Required Fields
invalid_calc='{
  "builtUpArea": 1000
}'
test_endpoint "POST" "/calculate" "$invalid_calc" "400" "Validation Test (Missing Fields)"

# Test 7: Input Validation - Invalid Data Types
invalid_types='{
  "builtUpArea": "not_a_number",
  "floors": 1,
  "qualityTier": "premium",
  "location": "bangalore"
}'
test_endpoint "POST" "/calculate" "$invalid_types" "400" "Validation Test (Invalid Types)"

# Test 8: Invalid Quality Tier
invalid_quality='{
  "builtUpArea": 1000,
  "floors": 1,
  "qualityTier": "invalid_tier",
  "location": "bangalore"
}'
test_endpoint "POST" "/calculate" "$invalid_quality" "400" "Validation Test (Invalid Quality)"

# Test 9: Invalid Location
invalid_location='{
  "builtUpArea": 1000,
  "floors": 1,
  "qualityTier": "premium",
  "location": "invalid_city"
}'
test_endpoint "POST" "/calculate" "$invalid_location" "400" "Validation Test (Invalid Location)"

# Test 10: Monitoring Endpoint
test_endpoint "GET" "/monitoring" "" "200" "Monitoring Endpoint"

# Test 11: Monitoring with Details
test_endpoint "GET" "/monitoring?detail=detailed" "" "200" "Detailed Monitoring"

# Test 12: Performance Metrics Submission
metrics_data='{
  "metric": {
    "name": "LCP",
    "value": 1234,
    "rating": "good",
    "url": "http://localhost:3001/calculator"
  }
}'
test_endpoint "POST" "/performance/metrics" "$metrics_data" "200" "Performance Metrics"

# Test 13: Web Vitals Analytics
web_vitals='{
  "metrics": [
    {
      "metric": "LCP",
      "value": 1500,
      "rating": "good",
      "url": "http://localhost:3001/calculator",
      "timestamp": '$(date +%s000)',
      "deviceType": "desktop"
    }
  ]
}'
test_endpoint "POST" "/analytics/web-vitals" "$web_vitals" "200" "Web Vitals Analytics"

# Test 14: Robots.txt
test_endpoint "GET" "/robots" "" "200" "Robots.txt"

# Test 15: Sitemap
test_endpoint "GET" "/sitemap" "" "200" "XML Sitemap"

echo ""
echo "🔄 Testing Rate Limiting..."
echo "==========================="
echo "Making rapid requests to test rate limiting..."

# Rate limiting test - make multiple requests quickly
rate_limit_test() {
    local success_count=0
    local rate_limited=false
    
    for i in {1..10}; do
        response=$(curl -s -w "%{http_code}" -o /dev/null -X POST \
            -H "Content-Type: application/json" \
            -d "$basic_calc" \
            "$BASE_URL/calculate" 2>/dev/null)
        
        status_code="${response: -3}"
        
        if [ "$status_code" = "200" ]; then
            ((success_count++))
        elif [ "$status_code" = "429" ]; then
            rate_limited=true
            echo -e "${YELLOW}⚠️  Rate limiting triggered after $i requests${NC}"
            break
        fi
        
        sleep 0.1  # Small delay between requests
    done
    
    if [ "$rate_limited" = true ]; then
        echo -e "${GREEN}✅ Rate limiting is working correctly${NC}"
    else
        echo -e "${YELLOW}⚠️  Rate limiting not triggered ($success_count successful requests)${NC}"
        echo "   This might indicate rate limiting needs adjustment or testing from multiple IPs"
    fi
}

rate_limit_test

echo ""
echo "📊 Performance Test..."
echo "====================="
echo "Testing response times for calculation endpoint..."

# Performance test - measure response times
performance_test() {
    local total_time=0
    local request_count=5
    
    for i in $(seq 1 $request_count); do
        start_time=$(date +%s%N)
        response=$(curl -s -o /dev/null -w "%{http_code}" -X POST \
            -H "Content-Type: application/json" \
            -d "$basic_calc" \
            "$BASE_URL/calculate" 2>/dev/null)
        end_time=$(date +%s%N)
        
        duration=$((($end_time - $start_time) / 1000000))  # Convert to milliseconds
        total_time=$(($total_time + $duration))
        
        status_code="${response: -3}"
        if [ "$status_code" = "200" ]; then
            echo "   Request $i: ${duration}ms ✅"
        else
            echo "   Request $i: Failed (HTTP $status_code) ❌"
        fi
    done
    
    avg_time=$(($total_time / $request_count))
    echo ""
    echo "   Average response time: ${avg_time}ms"
    
    if [ $avg_time -lt 500 ]; then
        echo -e "   Performance: ${GREEN}Excellent (<500ms)${NC}"
    elif [ $avg_time -lt 1000 ]; then
        echo -e "   Performance: ${YELLOW}Good (<1s)${NC}"
    else
        echo -e "   Performance: ${RED}Needs improvement (>1s)${NC}"
    fi
}

performance_test

echo ""
echo "🏆 Test Summary"
echo "==============="
echo "Timestamp: $(date)"
echo ""
echo "✅ All core endpoints tested"
echo "✅ Input validation verified"
echo "✅ Error handling confirmed"
echo "✅ Rate limiting checked"
echo "✅ Performance measured"
echo ""
echo "📋 Next Steps:"
echo "1. Review any failed tests above"
echo "2. Check server logs for detailed error information"
echo "3. Verify database connectivity for authentication-required endpoints"
echo "4. Consider load testing for production readiness"
echo ""
echo "📄 For detailed analysis, see: COMPREHENSIVE_API_INTEGRATION_TEST_REPORT.md"

# Cleanup
rm -f "$TEMP_FILE"

echo ""
echo "🎉 Testing completed!"