/**
 * Basic Info Step Component
 * First step of the wizard - captures project fundamentals
 */

import React, { useEffect, useState } from 'react';
import { motion, type Variants } from 'framer-motion';
import { Building, MapPin, Layers, Home, Calculator, Lightbulb, TrendingUp, Users } from 'lucide-react';
import { EnhancedCard } from '@/components/ui/enhanced-card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { MobileInput } from '@/components/ui/mobile-input';
import { cn } from '@/lib/utils';
import { isMobileViewport } from '@/lib/mobile';
import { 
  StepComponentProps, 
  StepValidation,
  LOCATION_OPTIONS,
  BUILDING_TYPE_OPTIONS,
  VALIDATION_LIMITS
} from '../types/wizard';
import { 
  HelpTrigger, 
  SmartBadge, 
  ContextualTip, 
  helpContent 
} from '@/components/ui/contextual-help';
import { useSmartDefaults, useBudgetEstimate } from '@/hooks/useSmartDefaults';
import { useRecommendations } from '@/hooks/useRecommendations';

// Animation variants
const containerVariants: Variants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.4,
      staggerChildren: 0.1,
    },
  },
  exit: {
    opacity: 0,
    y: -20,
    transition: { duration: 0.3 },
  },
};

const cardVariants: Variants = {
  hidden: { opacity: 0, scale: 0.95 },
  visible: { 
    opacity: 1, 
    scale: 1,
    transition: { duration: 0.3 }
  },
};

const fieldVariants: Variants = {
  hidden: { opacity: 0, x: -20 },
  visible: { opacity: 1, x: 0 },
};

export function BasicInfoStep({
  data,
  updateData,
  errors,
  onValidation,
  isActive,
}: StepComponentProps) {
  const [localErrors, setLocalErrors] = useState<Record<string, string>>({});
  const [isMobile, setIsMobile] = useState(false);

  // Initialize mobile detection
  useEffect(() => {
    setIsMobile(isMobileViewport());
    const handleResize = () => setIsMobile(isMobileViewport());
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Smart defaults and recommendations
  const { suggestions, applySuggestion, dismissSuggestion } = useSmartDefaults(
    data,
    updateData,
    {
      context: { location: data.location },
      enabled: isActive,
      autoApply: false,
      confidenceThreshold: 0.7
    }
  );

  const { recommendations } = useRecommendations(data, updateData, {
    enabled: isActive,
    maxRecommendations: 3,
    typeFilter: ['tip', 'warning'],
    categoryFilter: ['budget', 'design']
  });

  const { estimate, confidence, breakdown } = useBudgetEstimate(data, {
    location: data.location || 'delhi'
  });

  // Validation function
  const validateStep = (): StepValidation => {
    const stepErrors: Record<string, string> = {};
    
    // Plot Size validation
    const plotSizeValue = parseFloat(data.plotSize || '');
    if (!data.plotSize || isNaN(plotSizeValue) || plotSizeValue <= 0) {
      stepErrors.plotSize = 'Please enter a valid plot size';
    } else if (plotSizeValue < VALIDATION_LIMITS.plotSize.min) {
      stepErrors.plotSize = `Minimum plot size is ${VALIDATION_LIMITS.plotSize.min} sq ft`;
    } else if (plotSizeValue > VALIDATION_LIMITS.plotSize.max) {
      stepErrors.plotSize = `Maximum plot size is ${VALIDATION_LIMITS.plotSize.max} sq ft`;
    }

    // Built-up Area validation (optional but if provided, should be valid)
    if (data.builtUpArea) {
      const builtUpValue = parseFloat(data.builtUpArea);
      if (isNaN(builtUpValue) || builtUpValue <= 0) {
        stepErrors.builtUpArea = 'Please enter a valid built-up area';
      } else if (builtUpValue < VALIDATION_LIMITS.builtUpArea.min) {
        stepErrors.builtUpArea = `Minimum built-up area is ${VALIDATION_LIMITS.builtUpArea.min} sq ft`;
      } else if (builtUpValue > VALIDATION_LIMITS.builtUpArea.max) {
        stepErrors.builtUpArea = `Maximum built-up area is ${VALIDATION_LIMITS.builtUpArea.max} sq ft`;
      } else if (plotSizeValue && builtUpValue > plotSizeValue) {
        stepErrors.builtUpArea = 'Built-up area cannot exceed plot size';
      }
    }

    // Floors validation
    const floorsValue = parseInt(data.floors || '');
    if (!data.floors || isNaN(floorsValue) || floorsValue <= 0) {
      stepErrors.floors = 'Please select number of floors';
    } else if (floorsValue < VALIDATION_LIMITS.floors.min) {
      stepErrors.floors = `Minimum ${VALIDATION_LIMITS.floors.min} floor`;
    } else if (floorsValue > VALIDATION_LIMITS.floors.max) {
      stepErrors.floors = `Maximum ${VALIDATION_LIMITS.floors.max} floors allowed`;
    }

    // Location validation
    if (!data.location) {
      stepErrors.location = 'Please select a location';
    }

    // Building Type validation
    if (!data.buildingType) {
      stepErrors.buildingType = 'Please select building type';
    }

    setLocalErrors(stepErrors);
    
    const validation: StepValidation = {
      isValid: Object.keys(stepErrors).length === 0,
      errors: stepErrors,
    };

    if (onValidation) {
      onValidation(validation);
    }

    return validation;
  };

  // Trigger validation when data changes
  useEffect(() => {
    if (isActive) {
      validateStep();
    }
  }, [data, isActive]);

  // Helper function to handle input changes
  const handleInputChange = (field: string, value: string) => {
    updateData({ [field]: value });
    
    // Clear error when user starts typing
    if (localErrors[field]) {
      setLocalErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  // Auto-calculate built-up area suggestion
  const suggestBuiltUpArea = () => {
    if (data.plotSize && data.floors) {
      const plotSize = parseFloat(data.plotSize);
      const floors = parseInt(data.floors);
      if (!isNaN(plotSize) && !isNaN(floors)) {
        // Typically 60-70% of plot size per floor is buildable
        const suggestedArea = Math.round(plotSize * 0.65 * floors);
        return suggestedArea;
      }
    }
    return null;
  };

  const suggestedArea = suggestBuiltUpArea();

  if (!isActive) return null;

  return (
    <motion.div
      className="space-y-8"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      exit="exit"
    >
      {/* Header */}
      <motion.div 
        className="text-center space-y-2"
        variants={fieldVariants}
      >
        <div className="flex items-center justify-center gap-2 text-primary-600">
          <Building className="h-6 w-6" />
          <h2 className="text-2xl font-bold">Project Basics</h2>
        </div>
        <p className="text-secondary-600 max-w-2xl mx-auto">
          Let's start with the fundamental details of your construction project. 
          This information helps us provide accurate cost estimates.
        </p>
      </motion.div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Plot & Area Details */}
        <motion.div variants={cardVariants}>
          <EnhancedCard 
            variant="outlined" 
            size="lg"
            header={
              <div className="flex items-center gap-2">
                <Calculator className="h-5 w-5 text-primary-600" />
                <h3 className="text-lg font-semibold">Area Details</h3>
              </div>
            }
          >
            <div className="space-y-6">
              {/* Plot Size */}
              <motion.div 
                className="space-y-2"
                variants={fieldVariants}
              >
                <div className="flex items-center gap-2">
                  {isMobile ? (
                    <MobileInput
                      label="Plot Size (sq ft) *"
                      fieldType="number"
                      placeholder="Enter total plot area"
                      value={data.plotSize || ''}
                      onChange={(e) => handleInputChange('plotSize', e.target.value)}
                      error={localErrors.plotSize || errors.plotSize}
                      showClearButton={!!data.plotSize}
                      onClear={() => handleInputChange('plotSize', '')}
                    />
                  ) : (
                    <>
                      <div className="flex-1">
                        <Label htmlFor="plotSize" className="text-sm font-medium flex items-center gap-2">
                          Plot Size (sq ft) *
                          <HelpTrigger
                            helpId="plot-size"
                            content={helpContent.plotSize}
                            trigger="hover"
                            size="sm"
                          />
                        </Label>
                        <Input
                          id="plotSize"
                          type="number"
                          placeholder="Enter total plot area"
                          value={data.plotSize || ''}
                          onChange={(e) => handleInputChange('plotSize', e.target.value)}
                          className={cn(
                            localErrors.plotSize || errors.plotSize 
                              ? 'border-red-500 focus:border-red-500' 
                              : ''
                          )}
                        />
                      </div>
                    </>
                  )}
                </div>
                
                {/* Smart Suggestions */}
                {suggestions
                  .filter(s => s.field === 'plotSize' && s.canApply)
                  .map(suggestion => (
                    <motion.div
                      key={suggestion.field}
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="flex items-center gap-2"
                    >
                      <SmartBadge
                        type="ai-suggestion"
                        confidence={suggestion.confidence}
                        onClick={() => applySuggestion(suggestion.field, suggestion.value)}
                      />
                      <span className="text-sm text-blue-700">
                        {suggestion.reason}
                      </span>
                    </motion.div>
                  ))}

                {(localErrors.plotSize || errors.plotSize) && (
                  <p className="text-sm text-red-600">
                    {localErrors.plotSize || errors.plotSize}
                  </p>
                )}
                <p className="text-xs text-secondary-500">
                  Total area of your land/plot in square feet
                </p>
              </motion.div>

              {/* Built-up Area */}
              <motion.div 
                className="space-y-2"
                variants={fieldVariants}
              >
                {isMobile ? (
                  <MobileInput
                    label="Built-up Area (sq ft)"
                    fieldType="number"
                    placeholder={suggestedArea ? `Suggested: ${suggestedArea} sq ft` : "Enter construction area"}
                    value={data.builtUpArea || ''}
                    onChange={(e) => handleInputChange('builtUpArea', e.target.value)}
                    error={localErrors.builtUpArea || errors.builtUpArea}
                    showClearButton={!!data.builtUpArea}
                    onClear={() => handleInputChange('builtUpArea', '')}
                  />
                ) : (
                  <>
                    <Label htmlFor="builtUpArea" className="text-sm font-medium flex items-center gap-2">
                      Built-up Area (sq ft)
                      <span className="text-secondary-500 font-normal"> - Optional</span>
                      <HelpTrigger
                        helpId="built-up-area"
                        content={helpContent.builtUpArea}
                        trigger="hover"
                        size="sm"
                      />
                    </Label>
                    <Input
                      id="builtUpArea"
                      type="number"
                      placeholder={suggestedArea ? `Suggested: ${suggestedArea} sq ft` : "Enter construction area"}
                      value={data.builtUpArea || ''}
                      onChange={(e) => handleInputChange('builtUpArea', e.target.value)}
                      className={cn(
                        localErrors.builtUpArea || errors.builtUpArea 
                          ? 'border-red-500 focus:border-red-500' 
                          : ''
                      )}
                    />
                  </>
                )}
                
                {/* Smart Suggestions for Built-up Area */}
                {suggestions
                  .filter(s => s.field === 'builtUpArea' && s.canApply)
                  .map(suggestion => (
                    <motion.div
                      key={suggestion.field}
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="flex items-center justify-between p-3 bg-blue-50 border border-blue-200 rounded-lg"
                    >
                      <div className="flex items-center gap-2">
                        <Lightbulb className="h-4 w-4 text-blue-600" />
                        <span className="text-sm text-blue-800">
                          {suggestion.reason}: {suggestion.value} sq ft
                        </span>
                      </div>
                      <button
                        onClick={() => applySuggestion(suggestion.field, suggestion.value)}
                        className="text-xs text-blue-600 underline hover:text-blue-800 font-medium"
                      >
                        Apply
                      </button>
                    </motion.div>
                  ))}

                {(localErrors.builtUpArea || errors.builtUpArea) && (
                  <p className="text-sm text-red-600">
                    {localErrors.builtUpArea || errors.builtUpArea}
                  </p>
                )}
                {suggestedArea && !data.builtUpArea && (
                  <div className="flex items-center gap-2">
                    <p className="text-xs text-blue-600">
                      Suggested: {suggestedArea} sq ft
                    </p>
                    <button
                      type="button"
                      onClick={() => handleInputChange('builtUpArea', suggestedArea.toString())}
                      className="text-xs text-blue-600 underline hover:text-blue-800"
                    >
                      Use suggestion
                    </button>
                  </div>
                )}
                <p className="text-xs text-secondary-500">
                  Total covered construction area (leave empty for auto-calculation)
                </p>
              </motion.div>

              {/* Number of Floors */}
              <motion.div 
                className="space-y-2"
                variants={fieldVariants}
              >
                <Label htmlFor="floors" className="text-sm font-medium">
                  Number of Floors *
                </Label>
                <Select
                  value={data.floors || ''}
                  onValueChange={(value) => handleInputChange('floors', value)}
                >
                  <SelectTrigger 
                    className={cn(
                      localErrors.floors || errors.floors 
                        ? 'border-red-500 focus:border-red-500' 
                        : ''
                    )}
                  >
                    <SelectValue placeholder="Select number of floors" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1 Floor (Ground)</SelectItem>
                    <SelectItem value="2">2 Floors (G + 1)</SelectItem>
                    <SelectItem value="3">3 Floors (G + 2)</SelectItem>
                    <SelectItem value="4">4 Floors (G + 3)</SelectItem>
                  </SelectContent>
                </Select>
                {(localErrors.floors || errors.floors) && (
                  <p className="text-sm text-red-600">
                    {localErrors.floors || errors.floors}
                  </p>
                )}
              </motion.div>
            </div>
          </EnhancedCard>
        </motion.div>

        {/* Location & Type */}
        <motion.div variants={cardVariants}>
          <EnhancedCard 
            variant="outlined" 
            size="lg"
            header={
              <div className="flex items-center gap-2">
                <MapPin className="h-5 w-5 text-primary-600" />
                <h3 className="text-lg font-semibold">Location & Type</h3>
              </div>
            }
          >
            <div className="space-y-6">
              {/* Location */}
              <motion.div 
                className="space-y-2"
                variants={fieldVariants}
              >
                <Label htmlFor="location" className="text-sm font-medium">
                  Location *
                </Label>
                <Select
                  value={data.location || ''}
                  onValueChange={(value) => handleInputChange('location', value)}
                >
                  <SelectTrigger 
                    className={cn(
                      localErrors.location || errors.location 
                        ? 'border-red-500 focus:border-red-500' 
                        : ''
                    )}
                  >
                    <SelectValue placeholder="Select your city" />
                  </SelectTrigger>
                  <SelectContent>
                    {LOCATION_OPTIONS.map((location) => (
                      <SelectItem key={location.value} value={location.value}>
                        <div className="flex items-center justify-between w-full">
                          <span>{location.label}</span>
                          <span className="text-xs text-secondary-500 ml-2">
                            {location.multiplier}x
                          </span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {(localErrors.location || errors.location) && (
                  <p className="text-sm text-red-600">
                    {localErrors.location || errors.location}
                  </p>
                )}
                <p className="text-xs text-secondary-500">
                  Construction costs vary by location due to material and labor prices
                </p>
              </motion.div>

              {/* Building Type */}
              <motion.div 
                className="space-y-2"
                variants={fieldVariants}
              >
                <Label htmlFor="buildingType" className="text-sm font-medium">
                  Building Type *
                </Label>
                <Select
                  value={data.buildingType || ''}
                  onValueChange={(value) => handleInputChange('buildingType', value)}
                >
                  <SelectTrigger 
                    className={cn(
                      localErrors.buildingType || errors.buildingType 
                        ? 'border-red-500 focus:border-red-500' 
                        : ''
                    )}
                  >
                    <SelectValue placeholder="Select building type" />
                  </SelectTrigger>
                  <SelectContent>
                    {BUILDING_TYPE_OPTIONS.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {(localErrors.buildingType || errors.buildingType) && (
                  <p className="text-sm text-red-600">
                    {localErrors.buildingType || errors.buildingType}
                  </p>
                )}
                <p className="text-xs text-secondary-500">
                  Different building types have different structural requirements
                </p>
              </motion.div>

              {/* Enhanced Cost Preview */}
              {data.plotSize && data.floors && data.location && (
                <motion.div
                  className="space-y-4"
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.5 }}
                >
                  {/* Budget Estimate */}
                  {estimate && (
                    <div className="p-4 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg border border-green-200">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium text-green-900 flex items-center gap-2">
                          <TrendingUp className="h-4 w-4" />
                          AI Budget Estimate
                        </h4>
                        <SmartBadge type="ai-suggestion" confidence={confidence} />
                      </div>
                      <div className="text-sm text-green-800 space-y-2">
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <p className="font-medium">Estimated Cost</p>
                            <p className="text-lg font-bold">₹{(estimate / 100000).toFixed(1)}L</p>
                          </div>
                          <div>
                            <p className="font-medium">Per sq ft</p>
                            <p className="text-lg font-bold">₹{Math.round(estimate / parseFloat(data.builtUpArea || data.plotSize || '1000'))}</p>
                          </div>
                        </div>
                        {breakdown && (
                          <div className="text-xs text-green-600 pt-2 border-t border-green-200">
                            Base: ₹{(breakdown.base / 100000).toFixed(1)}L • 
                            Features: ₹{(breakdown.features / 100000).toFixed(1)}L • 
                            Regional: ₹{(breakdown.regional / 100000).toFixed(1)}L
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Traditional Quick Preview */}
                  <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                    <h4 className="font-medium text-blue-900 mb-2">Quick Preview</h4>
                    <div className="text-sm text-blue-800 space-y-1">
                      <p>
                        Estimated area: {data.builtUpArea || (parseFloat(data.plotSize) * 0.65 * parseInt(data.floors)).toFixed(0)} sq ft
                      </p>
                      <p>
                        Cost range: ₹{((parseFloat(data.builtUpArea || data.plotSize) * 0.65 * parseInt(data.floors)) * 1600 / 100000).toFixed(1)}L - 
                        ₹{((parseFloat(data.builtUpArea || data.plotSize) * 0.65 * parseInt(data.floors)) * 3500 / 100000).toFixed(1)}L
                      </p>
                    </div>
                  </div>
                </motion.div>
              )}

              {/* AI Recommendations */}
              {recommendations.length > 0 && (
                <motion.div
                  className="space-y-3"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.7 }}
                >
                  <h4 className="font-medium text-secondary-900 flex items-center gap-2">
                    <Users className="h-4 w-4 text-purple-600" />
                    Smart Recommendations
                  </h4>
                  {recommendations.slice(0, 2).map((rec, index) => (
                    <ContextualTip
                      key={rec.id}
                      content={rec.description}
                      type={rec.type === 'warning' ? 'warning' : 'tip'}
                      dismissible
                      onDismiss={() => {/* Handle dismiss */}}
                    />
                  ))}
                </motion.div>
              )}
            </div>
          </EnhancedCard>
        </motion.div>
      </div>
    </motion.div>
  );
}

export default BasicInfoStep;