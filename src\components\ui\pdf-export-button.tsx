/**
 * Enhanced PDF Export Button
 * Provides better UX with progress tracking, success states, and error handling
 */

'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  FileText, 
  Download, 
  CheckCircle, 
  AlertCircle, 
  Loader2,
  Clock,
  RotateCcw
} from 'lucide-react';

import { AnimatedButton } from './animated-button';
import { usePDFExport } from '@/hooks/usePDFExport';
import { cn } from '@/lib/utils';
import { 
  successCheckmark, 
  errorShake, 
  buttonPulse,
  createHoverAnimation
} from '@/lib/animations';

import type { CalculationInput, CalculationResult } from '@/core/calculator/types';
import type { PDFExportOptions } from '@/lib/pdf/enhanced-generator';

interface PDFExportButtonProps {
  input: CalculationInput;
  result: CalculationResult;
  options?: PDFExportOptions;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  showProgress?: boolean;
  showEstimatedTime?: boolean;
  enableRetry?: boolean;
  compact?: boolean;
  onSuccess?: (filename: string) => void;
  onError?: (error: Error) => void;
}

export function PDFExportButton({
  input,
  result,
  options = {},
  variant = 'outline',
  size = 'sm',
  className,
  showProgress = true,
  showEstimatedTime = true,
  enableRetry = true,
  compact = false,
  onSuccess,
  onError
}: PDFExportButtonProps) {
  const [showDetails, setShowDetails] = useState(false);
  
  const { 
    state, 
    exportPDF, 
    reset, 
    cancel, 
    validateData 
  } = usePDFExport({
    enableRetry,
    enableProgress: showProgress,
    onSuccess,
    onError
  });

  const { 
    isGenerating, 
    progress, 
    error, 
    success, 
    filename, 
    duration, 
    estimatedTime 
  } = state;

  // Validate data on mount
  useEffect(() => {
    const validationErrors = validateData(input, result);
    if (validationErrors.length > 0) {
      console.warn('PDF Export validation errors:', validationErrors);
    }
  }, [input, result, validateData]);

  const handleExport = async () => {
    if (isGenerating) {
      cancel();
      return;
    }

    if (success || error) {
      reset();
      return;
    }

    await exportPDF(input, result, options);
  };

  const getButtonContent = () => {
    if (isGenerating) {
      if (compact) {
        return <Loader2 className="h-4 w-4 animate-spin" />;
      }
      return (
        <div className="flex items-center gap-2">
          <motion.div {...buttonPulse}>
            <Loader2 className="h-4 w-4 animate-spin" />
          </motion.div>
          <span>
            {progress > 0 ? `${Math.round(progress)}%` : 'Generating...'}
          </span>
        </div>
      );
    }

    if (success) {
      if (compact) {
        return (
          <motion.div
            variants={successCheckmark}
            initial="initial"
            animate="animate"
          >
            <CheckCircle className="h-4 w-4 text-green-600" />
          </motion.div>
        );
      }
      return (
        <motion.div
          className="flex items-center gap-2"
          variants={successCheckmark}
          initial="initial"
          animate="animate"
        >
          <CheckCircle className="h-4 w-4 text-green-600" />
          <span>Success!</span>
        </motion.div>
      );
    }

    if (error) {
      if (compact) {
        return (
          <motion.div
            variants={errorShake}
            animate="animate"
          >
            <AlertCircle className="h-4 w-4 text-red-600" />
          </motion.div>
        );
      }
      return (
        <motion.div
          className="flex items-center gap-2"
          variants={errorShake}
          animate="animate"
        >
          <RotateCcw className="h-4 w-4 text-red-600" />
          <span>Retry</span>
        </motion.div>
      );
    }

    if (compact) {
      return <FileText className="h-4 w-4" />;
    }

    return (
      <div className="flex items-center gap-2">
        <Download className="h-4 w-4" />
        <span>Download PDF</span>
      </div>
    );
  };

  const getButtonVariant = () => {
    if (success) return 'success';
    if (error) return 'destructive';
    return variant;
  };

  const validationErrors = validateData(input, result);
  const isDisabled = validationErrors.length > 0;

  return (
    <div className={cn('relative', className)}>
      <AnimatedButton
        variant={getButtonVariant() as any}
        size={size}
        onClick={handleExport}
        disabled={isDisabled}
        pulse={success}
        glow={success}
        className={cn(
          'relative',
          isGenerating && 'cursor-wait',
          success && 'border-green-500 bg-green-50 text-green-700',
          error && 'border-red-500 bg-red-50 text-red-700'
        )}
        title={
          isDisabled 
            ? `Cannot generate PDF: ${validationErrors.join(', ')}`
            : success
            ? `PDF generated successfully: ${filename}`
            : error
            ? `Error: ${error.message}`
            : 'Generate PDF report'
        }
      >
        {getButtonContent()}
      </AnimatedButton>

      {/* Progress Bar */}
      <AnimatePresence>
        {isGenerating && showProgress && !compact && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="absolute top-full left-0 right-0 mt-2 bg-white rounded-lg shadow-sm border p-3 z-10"
          >
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">Generating PDF...</span>
                <span className="font-medium">{Math.round(progress)}%</span>
              </div>
              
              <div className="w-full bg-gray-200 rounded-full h-2">
                <motion.div
                  className="bg-blue-500 h-2 rounded-full"
                  initial={{ width: '0%' }}
                  animate={{ width: `${progress}%` }}
                  transition={{ duration: 0.3 }}
                />
              </div>
              
              {showEstimatedTime && estimatedTime && (
                <div className="flex items-center gap-1 text-xs text-gray-500">
                  <Clock className="h-3 w-3" />
                  <span>Estimated: ~{Math.round(estimatedTime / 1000)}s</span>
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Success/Error Details */}
      <AnimatePresence>
        {(success || error) && !compact && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="absolute top-full left-0 right-0 mt-2 bg-white rounded-lg shadow-sm border p-3 z-10"
          >
            {success && (
              <div className="space-y-2 text-sm">
                <div className="flex items-center gap-2 text-green-600">
                  <CheckCircle className="h-4 w-4" />
                  <span className="font-medium">PDF Generated!</span>
                </div>
                <div className="text-gray-600">
                  <p>File: {filename}</p>
                  {duration && (
                    <p>Generated in {(duration / 1000).toFixed(1)}s</p>
                  )}
                </div>
              </div>
            )}
            
            {error && (
              <div className="space-y-2 text-sm">
                <div className="flex items-center gap-2 text-red-600">
                  <AlertCircle className="h-4 w-4" />
                  <span className="font-medium">Generation Failed</span>
                </div>
                <div className="text-gray-600">
                  <p>{error.message}</p>
                  {enableRetry && (
                    <p className="text-blue-600 font-medium">Click to retry</p>
                  )}
                </div>
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Validation Error Tooltip */}
      {isDisabled && (
        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 bg-red-600 text-white text-xs rounded px-2 py-1 whitespace-nowrap">
          {validationErrors[0]}
        </div>
      )}
    </div>
  );
}

/**
 * Compact PDF Export Button for mobile/small spaces
 */
export function CompactPDFExportButton(props: Omit<PDFExportButtonProps, 'compact'>) {
  return <PDFExportButton {...props} compact />;
}

/**
 * PDF Export Button with detailed progress
 */
export function DetailedPDFExportButton(props: PDFExportButtonProps) {
  return (
    <PDFExportButton
      {...props}
      showProgress
      showEstimatedTime
      enableRetry
      compact={false}
    />
  );
}