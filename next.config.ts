import type { NextConfig } from 'next';

const withBundleAnalyzer = require('@next/bundle-analyzer')({
  enabled: process.env.ANALYZE === 'true',
});

const nextConfig: NextConfig = {
  // Performance optimizations
  typescript: {
    ignoreBuildErrors: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  experimental: {
    optimizeServerReact: true,
  },
  // Security configuration
  headers: async () => [
    {
      source: '/(.*)',
      headers: [
        {
          key: 'X-Content-Type-Options',
          value: 'nosniff',
        },
        {
          key: 'X-Frame-Options',
          value: 'DENY',
        },
        {
          key: 'X-XSS-Protection',
          value: '1; mode=block',
        },
        {
          key: 'Referrer-Policy',
          value: 'strict-origin-when-cross-origin',
        },
        {
          key: 'Permissions-Policy',
          value: 'camera=(), microphone=(), geolocation=(self), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(self), battery=(), bluetooth=(), display-capture=(), document-domain=(), encrypted-media=(self), fullscreen=(self), gamepad=(), midi=(), nfc=(), notifications=(self), persistent-storage=(), picture-in-picture=(self), publickey-credentials-get=(self), screen-wake-lock=(), serial=(), speaker-selection=(), sync-xhr=(), web-share=(self), xr-spatial-tracking=()',
        },
        {
          key: 'Cross-Origin-Embedder-Policy',
          value: 'credentialless',
        },
        {
          key: 'Cross-Origin-Opener-Policy',
          value: 'same-origin',
        },
        {
          key: 'Cross-Origin-Resource-Policy',
          value: 'same-origin',
        },
        {
          key: 'Strict-Transport-Security',
          value: 'max-age=63072000; includeSubDomains; preload',
        },
        {
          key: 'Content-Security-Policy',
          value: [
            "default-src 'self'",
            "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://js.stripe.com https://checkout.stripe.com https://www.google-analytics.com https://www.googletagmanager.com",
            "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdn.jsdelivr.net",
            "font-src 'self' https://fonts.gstatic.com https://cdn.jsdelivr.net data:",
            "img-src 'self' data: blob: https: https://images.unsplash.com https://res.cloudinary.com https://www.google-analytics.com",
            "connect-src 'self' https://*.supabase.co wss://*.supabase.co https://api.stripe.com https://checkout.stripe.com https://www.google-analytics.com https://analytics.google.com",
            "media-src 'self' https: data: blob:",
            "object-src 'none'",
            "child-src 'self' https://js.stripe.com https://checkout.stripe.com",
            "worker-src 'self' blob:",
            "frame-src 'self' https://js.stripe.com https://checkout.stripe.com",
            "form-action 'self' https://checkout.stripe.com",
            "base-uri 'self'",
            "manifest-src 'self'",
            "frame-ancestors 'none'",
            "upgrade-insecure-requests",
          ].join('; '),
        },
      ],
    },
    {
      source: '/api/(.*)',
      headers: [
        {
          key: 'Content-Security-Policy',
          value: "default-src 'none'",
        },
        {
          key: 'Access-Control-Allow-Origin',
          value: process.env.NODE_ENV === 'production' ? 'https://nirmaan.ai' : '*',
        },
        {
          key: 'Access-Control-Allow-Methods',
          value: 'GET, POST, PUT, DELETE, OPTIONS',
        },
        {
          key: 'Access-Control-Allow-Headers',
          value: 'Content-Type, Authorization, X-Requested-With, X-CSRF-Token',
        },
        {
          key: 'Access-Control-Max-Age',
          value: '86400',
        },
        {
          key: 'Vary',
          value: 'Origin, Access-Control-Request-Method, Access-Control-Request-Headers',
        },
      ],
    },
  ],
  // Image optimization
  images: {
    formats: ['image/webp', 'image/avif'],
    minimumCacheTTL: 60,
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
  },
  // Webpack optimizations
  webpack: (config, { buildId, dev, isServer, defaultLoaders, webpack }) => {
    // Optimize bundle size
    config.optimization = {
      ...config.optimization,
      splitChunks: {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            priority: 20,
            chunks: 'all',
          },
          common: {
            name: 'common',
            minChunks: 2,
            priority: 10,
            reuseExistingChunk: true,
            chunks: 'all',
          },
          // React and related libraries
          react: {
            test: /[\\/]node_modules[\\/](react|react-dom|react-router)[\\/]/,
            name: 'react',
            priority: 30,
            chunks: 'all',
          },
          // UI libraries
          ui: {
            test: /[\\/]node_modules[\\/](@radix-ui|framer-motion|lucide-react)[\\/]/,
            name: 'ui',
            priority: 25,
            chunks: 'all',
          },
          // Form libraries
          forms: {
            test: /[\\/]node_modules[\\/](react-hook-form|@hookform|zod)[\\/]/,
            name: 'forms',
            priority: 25,
            chunks: 'all',
          },
          // Analytics and tracking
          analytics: {
            test: /[\\/]node_modules[\\/](@tanstack|web-vitals)[\\/]/,
            name: 'analytics',
            priority: 15,
            chunks: 'all',
          },
        },
      },
    };

    // Tree shaking optimization
    config.optimization.usedExports = true;
    config.optimization.sideEffects = false;

    return config;
  },
  // Compress output
  compress: true,
  // Reduce bundle size
  modularizeImports: {
    'lucide-react': {
      transform: 'lucide-react/dist/esm/icons/{{member}}',
    },
  },
};

export default withBundleAnalyzer(nextConfig);