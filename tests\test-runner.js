#!/usr/bin/env node

/**
 * Comprehensive Test Runner for Nirmaan AI Construction Calculator
 * 
 * This script runs all test suites and generates comprehensive reports
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

class TestRunner {
  constructor() {
    this.results = {
      unit: null,
      integration: null,
      e2e: null,
      accessibility: null,
      performance: null,
      coverage: null,
    };
    
    this.startTime = Date.now();
  }

  async runCommand(command, args = [], options = {}) {
    return new Promise((resolve, reject) => {
      console.log(`\n🚀 Running: ${command} ${args.join(' ')}`);
      
      const child = spawn(command, args, {
        stdio: 'inherit',
        shell: true,
        ...options,
      });
      
      child.on('close', (code) => {
        if (code === 0) {
          resolve({ success: true, code });
        } else {
          resolve({ success: false, code });
        }
      });
      
      child.on('error', (error) => {
        reject(error);
      });
    });
  }

  async runUnitTests() {
    console.log('\n📝 Running Unit Tests...');
    const result = await this.runCommand('npm', ['run', 'test:unit']);
    this.results.unit = result;
    return result;
  }

  async runIntegrationTests() {
    console.log('\n🔗 Running Integration Tests...');
    const result = await this.runCommand('npm', ['run', 'test:integration']);
    this.results.integration = result;
    return result;
  }

  async runAccessibilityTests() {
    console.log('\n♿ Running Accessibility Tests...');
    const result = await this.runCommand('npm', ['run', 'test:a11y']);
    this.results.accessibility = result;
    return result;
  }

  async runPerformanceTests() {
    console.log('\n⚡ Running Performance Tests...');
    const result = await this.runCommand('npm', ['run', 'test:performance']);
    this.results.performance = result;
    return result;
  }

  async runE2ETests() {
    console.log('\n🌐 Running E2E Tests...');
    
    // Check if development server is running
    try {
      const response = await fetch('http://localhost:3000/api/health');
      if (!response.ok) {
        throw new Error('Server not responding');
      }
    } catch (error) {
      console.log('⚠️  Development server not running. Starting server...');
      
      // Start development server in background
      const serverProcess = spawn('npm', ['run', 'dev'], {
        stdio: 'pipe',
        shell: true,
        detached: true,
      });
      
      // Wait for server to start
      await new Promise(resolve => setTimeout(resolve, 10000));
      
      // Run E2E tests
      const result = await this.runCommand('npm', ['run', 'test:e2e']);
      
      // Clean up server process
      process.kill(-serverProcess.pid);
      
      this.results.e2e = result;
      return result;
    }
    
    // Server is already running
    const result = await this.runCommand('npm', ['run', 'test:e2e']);
    this.results.e2e = result;
    return result;
  }

  async runCoverageTests() {
    console.log('\n📊 Generating Coverage Report...');
    const result = await this.runCommand('npm', ['run', 'test:coverage']);
    this.results.coverage = result;
    return result;
  }

  generateReport() {
    const endTime = Date.now();
    const totalTime = Math.round((endTime - this.startTime) / 1000);
    
    console.log('\n' + '='.repeat(80));
    console.log('🧪 COMPREHENSIVE TEST RESULTS SUMMARY');
    console.log('='.repeat(80));
    
    const testSuites = [
      { name: 'Unit Tests', key: 'unit', icon: '📝' },
      { name: 'Integration Tests', key: 'integration', icon: '🔗' },
      { name: 'Accessibility Tests', key: 'accessibility', icon: '♿' },
      { name: 'Performance Tests', key: 'performance', icon: '⚡' },
      { name: 'E2E Tests', key: 'e2e', icon: '🌐' },
      { name: 'Coverage Report', key: 'coverage', icon: '📊' },
    ];
    
    let totalPassed = 0;
    let totalFailed = 0;
    
    testSuites.forEach(suite => {
      const result = this.results[suite.key];
      if (result) {
        const status = result.success ? '✅ PASSED' : '❌ FAILED';
        console.log(`${suite.icon} ${suite.name.padEnd(20)} ${status}`);
        
        if (result.success) {
          totalPassed++;
        } else {
          totalFailed++;
        }
      } else {
        console.log(`${suite.icon} ${suite.name.padEnd(20)} ⏭️  SKIPPED`);
      }
    });
    
    console.log('\n' + '-'.repeat(80));
    console.log(`📈 OVERALL RESULTS:`);
    console.log(`   ✅ Passed: ${totalPassed} test suites`);
    console.log(`   ❌ Failed: ${totalFailed} test suites`);
    console.log(`   ⏱️  Total Time: ${totalTime}s`);
    
    // Coverage summary
    if (this.results.coverage && this.results.coverage.success) {
      this.displayCoverageSummary();
    }
    
    console.log('\n' + '='.repeat(80));
    
    if (totalFailed === 0) {
      console.log('🎉 ALL TESTS PASSED! Ready for production deployment.');
      return true;
    } else {
      console.log('⚠️  Some tests failed. Please review and fix issues before deployment.');
      return false;
    }
  }

  displayCoverageSummary() {
    try {
      const coveragePath = path.join(process.cwd(), 'coverage', 'coverage-summary.json');
      if (fs.existsSync(coveragePath)) {
        const coverage = JSON.parse(fs.readFileSync(coveragePath, 'utf8'));
        const total = coverage.total;
        
        console.log('\n📊 COVERAGE SUMMARY:');
        console.log(`   Lines:      ${total.lines.pct}%`);
        console.log(`   Statements: ${total.statements.pct}%`);
        console.log(`   Functions:  ${total.functions.pct}%`);
        console.log(`   Branches:   ${total.branches.pct}%`);
        
        // Check if coverage meets thresholds
        const meetsThreshold = total.lines.pct >= 80 && 
                              total.statements.pct >= 80 && 
                              total.functions.pct >= 80 && 
                              total.branches.pct >= 80;
        
        if (meetsThreshold) {
          console.log('   ✅ Coverage thresholds met (80%+ required)');
        } else {
          console.log('   ⚠️  Coverage below 80% threshold');
        }
      }
    } catch (error) {
      console.log('   ⚠️  Could not read coverage summary');
    }
  }

  async runAll() {
    console.log('🚀 Starting Comprehensive Test Suite for Nirmaan AI Construction Calculator');
    console.log('⏱️  This may take several minutes...\n');
    
    try {
      // Run tests in optimal order
      await this.runUnitTests();
      await this.runIntegrationTests();
      await this.runAccessibilityTests();
      await this.runPerformanceTests();
      await this.runCoverageTests();
      
      // E2E tests last as they take longest
      if (process.env.SKIP_E2E !== 'true') {
        await this.runE2ETests();
      } else {
        console.log('⏭️  Skipping E2E tests (SKIP_E2E=true)');
      }
      
    } catch (error) {
      console.error('\n❌ Test runner encountered an error:', error.message);
    }
    
    const success = this.generateReport();
    process.exit(success ? 0 : 1);
  }

  async runQuick() {
    console.log('⚡ Running Quick Test Suite (Unit + Integration only)');
    
    await this.runUnitTests();
    await this.runIntegrationTests();
    
    const success = this.generateReport();
    process.exit(success ? 0 : 1);
  }
}

// Parse command line arguments
const args = process.argv.slice(2);
const testRunner = new TestRunner();

if (args.includes('--quick') || args.includes('-q')) {
  testRunner.runQuick();
} else if (args.includes('--unit')) {
  testRunner.runUnitTests().then(() => testRunner.generateReport());
} else if (args.includes('--integration')) {
  testRunner.runIntegrationTests().then(() => testRunner.generateReport());
} else if (args.includes('--e2e')) {
  testRunner.runE2ETests().then(() => testRunner.generateReport());
} else if (args.includes('--accessibility') || args.includes('--a11y')) {
  testRunner.runAccessibilityTests().then(() => testRunner.generateReport());
} else if (args.includes('--performance')) {
  testRunner.runPerformanceTests().then(() => testRunner.generateReport());
} else if (args.includes('--coverage')) {
  testRunner.runCoverageTests().then(() => testRunner.generateReport());
} else {
  testRunner.runAll();
}