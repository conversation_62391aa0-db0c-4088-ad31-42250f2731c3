# 💼 Business Logic Documentation

*Comprehensive guide to the calculation engine and business rules of Nirmaan AI Construction Calculator*

## 🎯 Overview

This document details the core business logic, calculation algorithms, and industry standards that power the Nirmaan AI Construction Calculator. Understanding these calculations is crucial for maintaining accuracy and building trust with users.

## 🧮 Core Calculation Engine

### Cost Calculation Formula

**Base Formula:**
```
Total Cost = Area × Cost per Sq Ft × Location Multiplier × Quality Factor
```

**Detailed Breakdown:**
```
Total Project Cost = Structure (35%) + Finishing (30%) + MEP (20%) + External (10%) + Other (5%)
```

### Implementation Architecture

```typescript
// Core calculation interface
interface CalculationInput {
  location: string;
  area: number;
  qualityTier: 'smart' | 'premium' | 'luxury';
  floors?: number;
  additionalFeatures?: string[];
}

interface CalculationResult {
  totalCost: number;
  costPerSqft: number;
  breakdown: CostBreakdown;
  materials: MaterialRequirement[];
  timeline: ProjectTimeline;
}
```

## 🏗️ Quality Tier System

### Tier Specifications

#### 🥉 Smart Choice (₹1,800/sq ft)
**Target Market**: Budget-conscious builders, rental properties

**Structure Standards:**
- **Concrete Grade**: M20 (20 MPa compressive strength)
- **Steel Specification**: TMT Fe500 (500 MPa yield strength)
- **Foundation**: Standard depth (4-6 feet)
- **Wall Thickness**: 9" external, 4.5" internal
- **Cement Consumption**: 7 bags per 100 sq ft
- **Steel Consumption**: 4 kg per sq ft

**Material Specifications:**
```json
{
  "cement": {
    "grade": "OPC 43",
    "brand": "UltraTech/ACC",
    "consumption": "7 bags/100 sqft"
  },
  "steel": {
    "grade": "Fe500",
    "brand": "TATA/SAIL",
    "consumption": "4 kg/sqft"
  },
  "bricks": {
    "type": "Red clay bricks",
    "strength": "3.5 MPa",
    "consumption": "40 per sqft"
  }
}
```

**Finishing Standards:**
- **Flooring**: Vitrified tiles (₹40-60/sq ft)
- **Wall Paint**: Asian Paints Tractor Emulsion
- **Kitchen**: Basic modular with laminate
- **Bathroom**: Standard Cera/Parryware fixtures
- **Electrical**: ISI marked basic fittings

#### 🥈 Premium Selection (₹2,500/sq ft)
**Target Market**: Modern family homes, good resale value

**Structure Standards:**
- **Concrete Grade**: M25 (25 MPa compressive strength)
- **Steel Specification**: TMT Fe500/Fe550
- **Foundation**: Enhanced design with waterproofing
- **Wall Thickness**: 9" external, 6" internal
- **Cement Consumption**: 8 bags per 100 sq ft
- **Steel Consumption**: 5 kg per sq ft

**Material Specifications:**
```json
{
  "cement": {
    "grade": "OPC 53",
    "brand": "UltraTech/Ambuja",
    "consumption": "8 bags/100 sqft"
  },
  "steel": {
    "grade": "Fe500/Fe550",
    "brand": "TATA/JSW",
    "consumption": "5 kg/sqft"
  },
  "blocks": {
    "type": "AAC blocks",
    "strength": "4 MPa",
    "consumption": "8 per sqft"
  }
}
```

**Finishing Standards:**
- **Flooring**: Premium vitrified/granite (₹80-120/sq ft)
- **Wall Paint**: Asian Paints Royale/Berger Silk
- **Kitchen**: Modular with granite countertop
- **Bathroom**: Mid-range Kohler/Grohe fixtures
- **Electrical**: Premium switches with basic automation

#### 🥇 Luxury Collection (₹3,500/sq ft)
**Target Market**: High-end homes, premium properties

**Structure Standards:**
- **Concrete Grade**: M30+ (30+ MPa compressive strength)
- **Steel Specification**: Corrosion-resistant TMT Fe550
- **Foundation**: Deep foundation with advanced waterproofing
- **Wall Thickness**: 9" external, 6" internal with insulation
- **Cement Consumption**: 9 bags per 100 sq ft
- **Steel Consumption**: 6 kg per sq ft

**Material Specifications:**
```json
{
  "cement": {
    "grade": "PPC 53",
    "brand": "ACC Gold/UltraTech Super",
    "consumption": "9 bags/100 sqft"
  },
  "steel": {
    "grade": "Fe550 CRS",
    "brand": "TATA Tiscon/JSW Neosteel",
    "consumption": "6 kg/sqft"
  },
  "blocks": {
    "type": "AAC blocks with insulation",
    "strength": "5 MPa",
    "consumption": "8 per sqft"
  }
}
```

**Finishing Standards:**
- **Flooring**: Italian marble/hardwood (₹150+/sq ft)
- **Wall Paint**: Premium international brands
- **Kitchen**: Designer modular with quartz countertop
- **Bathroom**: Premium Kohler/Hansgrohe fixtures
- **Electrical**: Smart home automation throughout

## 📊 Cost Breakdown Categories

### 1. Structure & RCC (35% of total cost)

**Components Included:**
- **Foundation**: Excavation, concrete, reinforcement
- **Superstructure**: Columns, beams, slabs
- **Masonry**: Walls, partitions
- **Structural Steel**: Reinforcement bars

**Calculation Logic:**
```typescript
const calculateStructureCost = (area: number, qualityTier: QualityTier): number => {
  const baseCosts = {
    smart: 630,    // ₹630 per sq ft (35% of ₹1,800)
    premium: 875,  // ₹875 per sq ft (35% of ₹2,500)
    luxury: 1225   // ₹1,225 per sq ft (35% of ₹3,500)
  };
  
  return area * baseCosts[qualityTier];
};
```

**Material Consumption Standards:**
```json
{
  "cement": {
    "foundation": "2.5 bags/sqft",
    "superstructure": "4.5 bags/sqft",
    "masonry": "1 bag/sqft"
  },
  "steel": {
    "foundation": "1.5 kg/sqft",
    "superstructure": "2.5 kg/sqft"
  },
  "aggregate": {
    "coarse": "0.5 cum/sqft",
    "fine": "0.3 cum/sqft"
  }
}
```

### 2. Finishing Work (30% of total cost)

**Components Included:**
- **Flooring**: Tiles, marble, wood
- **Wall Finishes**: Plastering, painting
- **Ceiling**: False ceiling, paint
- **Doors & Windows**: Materials and installation

**Calculation Logic:**
```typescript
const calculateFinishingCost = (area: number, qualityTier: QualityTier): number => {
  const finishingRates = {
    smart: {
      flooring: 50,     // ₹50 per sq ft
      painting: 25,     // ₹25 per sq ft
      doors: 15000,     // ₹15,000 per door
      windows: 8000     // ₹8,000 per window
    },
    premium: {
      flooring: 100,    // ₹100 per sq ft
      painting: 40,     // ₹40 per sq ft
      doors: 25000,     // ₹25,000 per door
      windows: 15000    // ₹15,000 per window
    },
    luxury: {
      flooring: 180,    // ₹180 per sq ft
      painting: 60,     // ₹60 per sq ft
      doors: 45000,     // ₹45,000 per door
      windows: 25000    // ₹25,000 per window
    }
  };
  
  const rates = finishingRates[qualityTier];
  const doorCount = Math.ceil(area / 200); // 1 door per 200 sq ft
  const windowCount = Math.ceil(area / 150); // 1 window per 150 sq ft
  
  return (rates.flooring * area) + 
         (rates.painting * area * 2.5) + // 2.5x for wall area
         (rates.doors * doorCount) + 
         (rates.windows * windowCount);
};
```

### 3. MEP Work (20% of total cost)

**Components Included:**
- **Electrical**: Wiring, switches, fixtures
- **Plumbing**: Pipes, fittings, fixtures
- **HVAC**: Ventilation systems (luxury tier)

**Electrical Specifications:**
```json
{
  "smart": {
    "points_per_sqft": 2,
    "cost_per_point": 800,
    "additional_features": ["basic_lighting", "fans", "plugs"]
  },
  "premium": {
    "points_per_sqft": 3,
    "cost_per_point": 1200,
    "additional_features": ["premium_lighting", "fans", "plugs", "basic_automation"]
  },
  "luxury": {
    "points_per_sqft": 4,
    "cost_per_point": 2000,
    "additional_features": ["designer_lighting", "smart_fans", "smart_plugs", "home_automation"]
  }
}
```

**Plumbing Specifications:**
```json
{
  "smart": {
    "cost_per_bathroom": 45000,
    "cost_per_kitchen": 35000,
    "fixtures": "standard"
  },
  "premium": {
    "cost_per_bathroom": 75000,
    "cost_per_kitchen": 55000,
    "fixtures": "mid_range"
  },
  "luxury": {
    "cost_per_bathroom": 150000,
    "cost_per_kitchen": 100000,
    "fixtures": "premium"
  }
}
```

### 4. External Works (10% of total cost)

**Components Included:**
- **Compound Wall**: Boundary wall construction
- **Landscaping**: Basic garden development
- **Parking**: Paving and marking
- **Utilities**: Water connection, sewage

**Calculation Logic:**
```typescript
const calculateExternalCost = (area: number, qualityTier: QualityTier): number => {
  const externalRates = {
    smart: 180,    // ₹180 per sq ft (10% of ₹1,800)
    premium: 250,  // ₹250 per sq ft (10% of ₹2,500)
    luxury: 350    // ₹350 per sq ft (10% of ₹3,500)
  };
  
  return area * externalRates[qualityTier];
};
```

### 5. Other Costs (5% of total cost)

**Components Included:**
- **Professional Fees**: Architect, engineer, consultant (2%)
- **Permits & Approvals**: Government fees (1%)
- **Contingency**: Unexpected costs (2%)

**Breakdown:**
```json
{
  "professional_fees": {
    "architect": "1%",
    "structural_engineer": "0.5%",
    "consultant": "0.5%"
  },
  "permits": {
    "building_permit": "0.5%",
    "completion_certificate": "0.3%",
    "other_approvals": "0.2%"
  },
  "contingency": "2%"
}
```

## 🌍 Regional Pricing System

### Location Multipliers

**Metro Cities (Tier 1)**
```json
{
  "mumbai": {
    "multiplier": 1.25,
    "factors": ["high_real_estate", "transportation_cost", "labor_premium"]
  },
  "delhi": {
    "multiplier": 1.15,
    "factors": ["government_fees", "labor_cost", "material_transport"]
  },
  "bangalore": {
    "multiplier": 1.10,
    "factors": ["tech_hub_premium", "skilled_labor_demand"]
  },
  "chennai": {
    "multiplier": 1.05,
    "factors": ["port_city_advantage", "moderate_labor_cost"]
  },
  "hyderabad": {
    "multiplier": 1.00,
    "factors": ["baseline_city", "balanced_costs"]
  }
}
```

**Tier 2 Cities**
```json
{
  "pune": {
    "multiplier": 1.08,
    "factors": ["proximity_to_mumbai", "industrial_hub"]
  },
  "ahmedabad": {
    "multiplier": 0.95,
    "factors": ["industrial_city", "lower_labor_cost"]
  },
  "jaipur": {
    "multiplier": 0.90,
    "factors": ["tourist_city", "traditional_crafts"]
  },
  "lucknow": {
    "multiplier": 0.85,
    "factors": ["government_city", "lower_costs"]
  }
}
```

### Seasonal Adjustments

**Monsoon Impact (June-September)**
```json
{
  "material_price_increase": 5,
  "labor_productivity_decrease": 15,
  "transportation_delays": 10,
  "overall_impact": "+8% to project cost"
}
```

**Festival Season Impact (October-December)**
```json
{
  "labor_availability_decrease": 20,
  "material_demand_increase": 10,
  "transportation_cost_increase": 8,
  "overall_impact": "+12% to project cost"
}
```

## 📏 Material Consumption Standards

### IS Code Compliance

**Cement Consumption (IS 456:2000)**
```json
{
  "M20_concrete": {
    "cement": "320 kg/cum",
    "fine_aggregate": "680 kg/cum",
    "coarse_aggregate": "1190 kg/cum",
    "water": "180 liters/cum"
  },
  "M25_concrete": {
    "cement": "370 kg/cum",
    "fine_aggregate": "665 kg/cum",
    "coarse_aggregate": "1165 kg/cum",
    "water": "185 liters/cum"
  },
  "M30_concrete": {
    "cement": "420 kg/cum",
    "fine_aggregate": "650 kg/cum",
    "coarse_aggregate": "1140 kg/cum",
    "water": "190 liters/cum"
  }
}
```

**Steel Consumption (IS 13920:2016)**
```json
{
  "residential_building": {
    "foundation": "80-100 kg/cum",
    "columns": "120-150 kg/cum",
    "beams": "100-130 kg/cum",
    "slabs": "60-80 kg/cum"
  },
  "seismic_zones": {
    "zone_2": "standard_reinforcement",
    "zone_3": "+10% reinforcement",
    "zone_4": "+20% reinforcement",
    "zone_5": "+30% reinforcement"
  }
}
```

### Wastage Factors

**Material Wastage Allowances**
```json
{
  "cement": "2%",
  "steel": "5%",
  "bricks": "5%",
  "tiles": "10%",
  "paint": "8%",
  "electrical_items": "3%",
  "plumbing_items": "5%"
}
```

## ⏱️ Timeline Estimation

### Construction Phases

**Phase-wise Duration (for 1000 sq ft)**
```json
{
  "foundation": {
    "duration_weeks": 3,
    "cost_percentage": 15,
    "weather_dependency": "high"
  },
  "superstructure": {
    "duration_weeks": 8,
    "cost_percentage": 35,
    "weather_dependency": "medium"
  },
  "masonry": {
    "duration_weeks": 4,
    "cost_percentage": 15,
    "weather_dependency": "medium"
  },
  "finishing": {
    "duration_weeks": 10,
    "cost_percentage": 30,
    "weather_dependency": "low"
  },
  "final_touches": {
    "duration_weeks": 2,
    "cost_percentage": 5,
    "weather_dependency": "low"
  }
}
```

**Timeline Calculation Logic**
```typescript
const calculateTimeline = (area: number, qualityTier: QualityTier): ProjectTimeline => {
  const baseWeeks = {
    smart: 20,     // 20 weeks for basic construction
    premium: 25,   // 25 weeks for premium construction
    luxury: 35     // 35 weeks for luxury construction
  };
  
  const areaFactor = Math.sqrt(area / 1000); // Square root scaling
  const totalWeeks = baseWeeks[qualityTier] * areaFactor;
  
  return {
    totalWeeks: Math.ceil(totalWeeks),
    phases: calculatePhases(totalWeeks),
    criticalPath: identifyCriticalPath(),
    weatherBuffer: Math.ceil(totalWeeks * 0.15) // 15% buffer for weather
  };
};
```

## 💰 Cost Optimization Logic

### Value Engineering Principles

**Cost Reduction Strategies**
```json
{
  "structure_optimization": {
    "beam_size_optimization": "5-8% savings",
    "slab_thickness_optimization": "3-5% savings",
    "foundation_design": "8-12% savings"
  },
  "material_substitution": {
    "aac_blocks_vs_bricks": "10-15% savings",
    "precast_vs_cast_in_situ": "8-12% savings",
    "alternative_finishes": "15-25% savings"
  },
  "process_optimization": {
    "bulk_procurement": "5-8% savings",
    "local_sourcing": "3-6% savings",
    "construction_sequence": "2-4% savings"
  }
}
```

### Budget Allocation Rules

**Recommended Budget Distribution**
```json
{
  "never_compromise": {
    "foundation": "Always use recommended grade",
    "structural_steel": "Never reduce below IS code",
    "waterproofing": "Critical for longevity"
  },
  "flexible_areas": {
    "finishing_materials": "Can upgrade later",
    "fixtures": "Can be enhanced post-construction",
    "landscaping": "Can be phased"
  },
  "smart_investments": {
    "electrical_infrastructure": "Plan for future needs",
    "plumbing_rough_in": "Install extra points",
    "structural_provisions": "Plan for future additions"
  }
}
```

## 🔍 Quality Control Metrics

### Inspection Checkpoints

**Critical Quality Gates**
```json
{
  "foundation_stage": {
    "soil_bearing_capacity": "Confirmed by soil test",
    "foundation_depth": "As per IS 1904",
    "reinforcement_cover": "50mm minimum",
    "concrete_grade": "Tested at 7, 14, 28 days"
  },
  "superstructure_stage": {
    "column_verticality": "Within 1:500",
    "beam_alignment": "±10mm tolerance",
    "slab_thickness": "±5mm tolerance",
    "concrete_strength": "Cube test reports"
  },
  "finishing_stage": {
    "wall_plaster_thickness": "12mm±2mm",
    "tile_alignment": "±2mm gap",
    "paint_finish": "No patches or streaks",
    "electrical_testing": "All circuits tested"
  }
}
```

## 📊 ROI and Investment Analysis

### Return on Investment Calculations

**Property Value Enhancement**
```json
{
  "quality_tier_impact": {
    "smart_to_premium": "15-20% value increase",
    "premium_to_luxury": "25-30% value increase",
    "location_factor": "Metro cities: +10% premium"
  },
  "feature_wise_roi": {
    "modular_kitchen": "ROI: 80-100%",
    "additional_bathroom": "ROI: 70-90%",
    "home_automation": "ROI: 40-60%",
    "premium_flooring": "ROI: 60-80%"
  }
}
```

**Maintenance Cost Projections**
```json
{
  "annual_maintenance": {
    "smart": "1-2% of construction cost",
    "premium": "0.8-1.5% of construction cost",
    "luxury": "0.5-1% of construction cost"
  },
  "major_repairs": {
    "painting": "Every 5-7 years",
    "flooring": "Every 10-15 years",
    "plumbing": "Every 15-20 years",
    "electrical": "Every 20-25 years"
  }
}
```

## 🔧 Error Handling and Validation

### Input Validation Rules

**Area Constraints**
```typescript
const validateArea = (area: number): ValidationResult => {
  if (area < 200) return { valid: false, error: "Minimum area is 200 sq ft" };
  if (area > 50000) return { valid: false, error: "Maximum area is 50,000 sq ft" };
  if (area % 1 !== 0) return { valid: false, error: "Area must be a whole number" };
  return { valid: true };
};
```

**Location Validation**
```typescript
const validateLocation = (location: string): ValidationResult => {
  const supportedCities = ['mumbai', 'delhi', 'bangalore', 'chennai', 'hyderabad'];
  if (!supportedCities.includes(location.toLowerCase())) {
    return { 
      valid: false, 
      error: `Location not supported. Supported cities: ${supportedCities.join(', ')}` 
    };
  }
  return { valid: true };
};
```

### Calculation Accuracy Measures

**Precision Standards**
```json
{
  "decimal_places": {
    "cost_calculations": 0,
    "material_quantities": 2,
    "area_calculations": 2,
    "percentage_values": 1
  },
  "rounding_rules": {
    "total_cost": "Round to nearest ₹1,000",
    "material_quantities": "Round up to next unit",
    "timeline": "Round up to next week"
  }
}
```

---

## 📞 Business Logic Support

For questions about calculation methodology:
- **Technical Team**: <EMAIL>
- **Industry Experts**: <EMAIL>
- **Documentation Updates**: Quarterly reviews with industry changes

---

*This business logic documentation is updated quarterly to reflect industry changes, new IS codes, and market conditions. Last updated: July 2025*