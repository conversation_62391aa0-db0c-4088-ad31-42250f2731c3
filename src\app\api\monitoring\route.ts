import { NextRequest, NextResponse } from 'next/server';
import { performanceMonitor } from '@/lib/performance/monitoring';

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const detail = searchParams.get('detail') || 'basic';
    
    const metrics = performanceMonitor.getMetrics();
    const healthStatus = performanceMonitor.getHealthStatus();
    
    const response: any = {
      status: healthStatus,
      timestamp: new Date().toISOString(),
      metrics: {
        requests: {
          total: metrics.requestCount,
          errors: metrics.errorCount,
          errorRate: performanceMonitor.getErrorRate(),
          throughput: metrics.throughput,
        },
        performance: {
          responseTime: {
            avg: Math.round(metrics.responseTimeAvg),
            max: metrics.responseTimeMax,
            min: metrics.responseTimeMin === Infinity ? 0 : metrics.responseTimeMin,
          },
          memory: {
            usage: Math.round(metrics.memoryUsage),
            unit: 'MB',
          },
        },
        system: {
          uptime: Math.floor((Date.now() - metrics.lastReset.getTime()) / 1000),
          environment: process.env.NODE_ENV || 'development',
          version: process.env.APP_VERSION || '1.0.0',
        },
      },
    };
    
    // Add detailed information if requested
    if (detail === 'detailed') {
      response.endpoints = performanceMonitor.getTopEndpoints(10);
      response.recentErrors = performanceMonitor.getRecentErrors().slice(-5);
      response.slowRequests = performanceMonitor.getSlowRequests(1000).slice(-5);
      response.requestHistory = performanceMonitor.getRequestHistory().slice(-20);
    }
    
    return NextResponse.json(response, {
      status: 200,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Content-Type': 'application/json',
      },
    });
  } catch (error) {
    console.error('Monitoring endpoint error:', error);
    
    return NextResponse.json(
      {
        status: 'error',
        message: 'Monitoring data unavailable',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    if (body.action === 'reset') {
      // Reset metrics (for development/testing)
      if (process.env.NODE_ENV === 'development') {
        // Would reset metrics here
        return NextResponse.json({ message: 'Metrics reset' });
      } else {
        return NextResponse.json({ error: 'Reset not allowed in production' }, { status: 403 });
      }
    }
    
    return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
  } catch (error) {
    return NextResponse.json({ error: 'Invalid request' }, { status: 400 });
  }
}