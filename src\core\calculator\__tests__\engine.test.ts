import { vi, describe, it, expect, beforeEach, type MockedFunction } from 'vitest';
import { calculate } from '../engine';
import { calculateStructureCost } from '../calculations/structure';
import { calculateFinishingCost } from '../calculations/finishing';
import { calculateMEPCost } from '../calculations/mep';
import { calculateExternalCost } from '../calculations/external';
import { getMaterialSummary } from '../materials/quantities';
import { 
  mockCalculatorFormData, 
  generateFormData,
  mockLocations 
} from '@/test/utils/mock-data';

// Mock the calculation modules
vi.mock('../calculations/structure');
vi.mock('../calculations/finishing');
vi.mock('../calculations/mep');
vi.mock('../calculations/external');
vi.mock('../materials/quantities');

describe('Calculator Engine', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Setup default mocks
    (calculateStructureCost as MockedFunction<typeof calculateStructureCost>).mockReturnValue({
      cost: 600000,
      breakdown: {
        foundation: { cost: 90000, description: 'Foundation work' },
        columns: { cost: 150000, description: 'Columns and beams' },
        slabs: { cost: 210000, description: 'RCC slabs' },
        walls: { cost: 120000, description: 'Brick walls' },
        staircase: { cost: 30000, description: 'Staircase' },
      },
    });
    
    (calculateFinishingCost as MockedFunction<typeof calculateFinishingCost>).mockReturnValue({
      cost: 540000,
      breakdown: {
        flooring: { cost: 189000, description: 'Flooring work' },
        doors: { cost: 135000, description: 'Doors and windows' },
        painting: { cost: 108000, description: 'Painting work' },
        kitchen: { cost: 81000, description: 'Kitchen and bathrooms' },
        interior: { cost: 27000, description: 'Interior elements' },
      },
    });
    
    (calculateMEPCost as MockedFunction<typeof calculateFinishingCost>).mockReturnValue({
      cost: 360000,
      breakdown: {
        electrical: { cost: 180000, description: 'Electrical work' },
        plumbing: { cost: 126000, description: 'Plumbing work' },
        hvac: { cost: 54000, description: 'HVAC provision' },
      },
    });
    
    (calculateExternalCost as MockedFunction<typeof calculateFinishingCost>).mockReturnValue({
      cost: 180000,
      breakdown: {
        compound: { cost: 63000, description: 'Compound wall' },
        landscaping: { cost: 45000, description: 'Landscaping' },
        parking: { cost: 36000, description: 'Parking area' },
        utilities: { cost: 27000, description: 'External utilities' },
        site: { cost: 9000, description: 'Site development' },
      },
    });
    
    (getMaterialSummary as MockedFunction<typeof calculateFinishingCost>).mockReturnValue({
      materials: [
        { material: 'cement', totalQuantity: 300, unit: 'bags', rate: 400, totalAmount: 120000 },
        { material: 'steel', totalQuantity: 4500, unit: 'kg', rate: 85, totalAmount: 382500 },
        { material: 'bricks', totalQuantity: 9000, unit: 'pieces', rate: 8, totalAmount: 72000 },
        { material: 'sand', totalQuantity: 78, unit: 'cubic meters', rate: 2000, totalAmount: 156000 },
        { material: 'aggregate', totalQuantity: 58.5, unit: 'cubic meters', rate: 1600, totalAmount: 93600 },
      ],
      totalMaterialCost: 824100,
    });
  });

  describe('calculateConstructionCost', () => {
    it('should calculate correct total cost with all components', () => {
      const result = calculate(mockCalculatorFormData);
      
      expect(result).toBeDefined();
      expect(result.totalCost).toBeGreaterThan(0);
      
      // Check if all calculation functions were called
      expect(calculateStructureCost).toHaveBeenCalledWith(
        mockCalculatorFormData.builtUpArea,
        mockCalculatorFormData.qualityTier,
        mockCalculatorFormData.floors,
        mockCalculatorFormData.hasBasement
      );
      
      expect(calculateFinishingCost).toHaveBeenCalledWith(
        mockCalculatorFormData.builtUpArea,
        mockCalculatorFormData.qualityTier,
        mockCalculatorFormData.hasBasement
      );
      
      expect(calculateMEPCost).toHaveBeenCalledWith(
        mockCalculatorFormData.builtUpArea,
        mockCalculatorFormData.qualityTier,
        mockCalculatorFormData.hasBasement
      );
      
      expect(calculateExternalCost).toHaveBeenCalledWith(
        mockCalculatorFormData
      );
      
      expect(getMaterialSummary).toHaveBeenCalledWith(
        mockCalculatorFormData
      );
    });

    it('should apply location multiplier correctly', () => {
      const bangaloreData = generateFormData({ location: 'bangalore' });
      const mumbaiData = generateFormData({ location: 'mumbai' });
      
      const bangaloreResult = calculate(bangaloreData);
      const mumbaiResult = calculate(mumbaiData);
      
      expect(bangaloreResult).toBeDefined();
      expect(mumbaiResult).toBeDefined();
      
      // Mumbai has 1.2x multiplier vs Bangalore's 0.95x
      const bangaloreCost = bangaloreResult.totalCost;
      const mumbaiCost = mumbaiResult.totalCost;
      
      // Mumbai should be more expensive
      expect(mumbaiCost).toBeGreaterThan(bangaloreCost);
    });

    it('should calculate cost per sqft correctly', () => {
      const result = calculate(mockCalculatorFormData);
      
      expect(result).toBeDefined();
      const { totalCost, costPerSqft } = result;
      
      const expectedCostPerSqFt = Math.round(totalCost / (mockCalculatorFormData.builtUpArea * (mockCalculatorFormData.floors + 1)));
      expect(costPerSqft).toBe(expectedCostPerSqFt);
    });

    it('should calculate percentage breakdown correctly', () => {
      const result = calculate(mockCalculatorFormData);
      
      expect(result).toBeDefined();
      const { breakdown } = result;
      
      // Sum of all percentages should be 100
      const totalPercentage = 
        breakdown.structure.percentage +
        breakdown.finishing.percentage +
        breakdown.mep.percentage +
        breakdown.external.percentage +
        breakdown.other.percentage;
      
      expect(totalPercentage).toBeCloseTo(100, 1);
    });

    it('should handle different quality tiers', () => {
      const smartData = generateFormData({ qualityTier: 'smart' });
      const premiumData = generateFormData({ qualityTier: 'premium' });
      const luxuryData = generateFormData({ qualityTier: 'luxury' });
      
      const smartResult = calculate(smartData);
      const premiumResult = calculate(premiumData);
      const luxuryResult = calculate(luxuryData);
      
      expect(smartResult).toBeDefined();
      expect(premiumResult).toBeDefined();
      expect(luxuryResult).toBeDefined();
      
      // Costs should increase with quality tier
      expect(smartResult.totalCost).toBeLessThan(premiumResult.totalCost);
      expect(premiumResult.totalCost).toBeLessThan(luxuryResult.totalCost);
    });

    it('should handle basement inclusion', () => {
      const withoutBasement = generateFormData({ hasBasement: false });
      const withBasement = generateFormData({ hasBasement: true });
      
      const withoutResult = calculate(withoutBasement);
      const withResult = calculate(withBasement);
      
      expect(withoutResult).toBeDefined();
      expect(withResult).toBeDefined();
      
      // Cost with basement should be higher
      expect(withResult.totalCost).toBeGreaterThan(withoutResult.totalCost);
    });

    it('should generate correct project timeline', () => {
      const smallProject = generateFormData({ builtUpArea: 800 });
      const mediumProject = generateFormData({ builtUpArea: 1500 });
      const largeProject = generateFormData({ builtUpArea: 3000 });
      
      const smallResult = calculate(smallProject);
      const mediumResult = calculate(mediumProject);
      const largeResult = calculate(largeProject);
      
      expect(smallResult.timeline).toBeDefined();
      expect(mediumResult.timeline).toBeDefined();
      expect(largeResult.timeline).toBeDefined();
    });

    it('should include quality specifications', () => {
      const result = calculate(mockCalculatorFormData);
      
      expect(result).toBeDefined();
      const { materials } = result;
      
      expect(materials).toBeDefined();
      expect(Array.isArray(materials)).toBe(true);
    });

    it('should include location factors', () => {
      const result = calculate(mockCalculatorFormData);
      
      expect(result).toBeDefined();
      const { summary } = result;
      
      expect(summary.location).toBeDefined();
      expect(summary.qualityTier).toBeDefined();
    });

    it('should handle edge cases gracefully', () => {
      // Minimum area
      const minAreaData = generateFormData({ builtUpArea: 500, plotArea: 300 });
      const minResult = calculate(minAreaData);
      expect(minResult).toBeDefined();
      
      // Maximum floors
      const maxFloorsData = generateFormData({ floors: 4 });
      const maxResult = calculate(maxFloorsData);
      expect(maxResult).toBeDefined();
    });

    it('should calculate professional fees correctly', () => {
      const result = calculate(mockCalculatorFormData);
      
      expect(result).toBeDefined();
      const { breakdown } = result;
      
      expect(breakdown.other).toBeDefined();
      expect(breakdown.other.amount).toBeGreaterThan(0);
    });

    it('should calculate statutory charges correctly', () => {
      const result = calculate(mockCalculatorFormData);
      
      expect(result).toBeDefined();
      const { breakdown } = result;
      
      expect(breakdown.other).toBeDefined();
      expect(breakdown.other.subCategories).toBeDefined();
      expect(Array.isArray(breakdown.other.subCategories)).toBe(true);
    });

    it('should include material cost breakdown', () => {
      const result = calculate(mockCalculatorFormData);
      
      expect(result).toBeDefined();
      const { materials } = result;
      
      expect(materials).toBeDefined();
      expect(Array.isArray(materials)).toBe(true);
      
      // Verify material structure if materials exist
      if (materials.length > 0) {
        expect(materials[0]).toHaveProperty('quantity');
        expect(materials[0]).toHaveProperty('unit');
        expect(materials[0]).toHaveProperty('rate');
        expect(materials[0]).toHaveProperty('totalCost');
      }
    });
  });

  describe('Error Handling', () => {
    it('should handle calculation errors gracefully', () => {
      (calculateStructureCost as MockedFunction<typeof calculateFinishingCost>).mockImplementation(() => {
        throw new Error('Calculation error');
      });
      
      expect(() => {
        calculate(mockCalculatorFormData);
      }).toThrow();
    });

    it('should validate input data', () => {
      const invalidData = generateFormData({ builtUpArea: -100 });
      
      expect(() => {
        calculate(invalidData);
      }).toThrow();
    });
  });
});