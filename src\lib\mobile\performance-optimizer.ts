/**
 * Mobile Performance Optimizer
 * Optimizes performance specifically for mobile devices
 */

interface PerformanceConfig {
  enableImageOptimization?: boolean;
  enableLazyLoading?: boolean;
  enableCodeSplitting?: boolean;
  enableMemoryOptimization?: boolean;
  enableBatteryOptimization?: boolean;
  maxImageSize?: number;
  lazyLoadThreshold?: number;
}

interface PerformanceMetrics {
  loadTime: number;
  renderTime: number;
  memoryUsage: number;
  batteryLevel?: number;
  networkType?: string;
  devicePixelRatio: number;
}

class MobilePerformanceOptimizer {
  private config: Required<PerformanceConfig>;
  private metrics: PerformanceMetrics;
  private observers: Map<string, IntersectionObserver> = new Map();
  private optimizations: Set<string> = new Set();
  private callbacks: Map<string, Function[]> = new Map();

  constructor(config: Partial<PerformanceConfig> = {}) {
    this.config = {
      enableImageOptimization: true,
      enableLazyLoading: true,
      enableCodeSplitting: true,
      enableMemoryOptimization: true,
      enableBatteryOptimization: true,
      maxImageSize: 1920,
      lazyLoadThreshold: 100,
      ...config
    };

    this.metrics = this.initializeMetrics();
    this.init();
  }

  private initializeMetrics(): PerformanceMetrics {
    return {
      loadTime: performance.now(),
      renderTime: 0,
      memoryUsage: this.getMemoryUsage(),
      batteryLevel: this.getBatteryLevel(),
      networkType: this.getNetworkType(),
      devicePixelRatio: window.devicePixelRatio || 1
    };
  }

  private init(): void {
    this.setupPerformanceObserver();
    this.optimizeInitialLoad();
    this.setupBatteryOptimization();
    this.setupNetworkOptimization();
    this.setupMemoryOptimization();
  }

  private setupPerformanceObserver(): void {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach(entry => {
          if (entry.entryType === 'largest-contentful-paint') {
            this.metrics.renderTime = entry.startTime;
            this.emit('lcp', { time: entry.startTime });
          }
        });
      });

      try {
        observer.observe({ entryTypes: ['largest-contentful-paint', 'first-input'] });
      } catch (error) {
        console.warn('Performance observer not supported:', error);
      }
    }
  }

  private optimizeInitialLoad(): void {
    // Preload critical resources
    this.preloadCriticalResources();
    
    // Optimize font loading
    this.optimizeFontLoading();
    
    // Setup lazy loading
    if (this.config.enableLazyLoading) {
      this.setupLazyLoading();
    }
    
    // Optimize images
    if (this.config.enableImageOptimization) {
      this.optimizeImages();
    }
  }

  private preloadCriticalResources(): void {
    const criticalResources = [
      '/api/materials/core-materials.json',
      '/fonts/inter-var.woff2'
    ];

    criticalResources.forEach(resource => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.href = resource;
      link.as = resource.endsWith('.json') ? 'fetch' : 
               resource.endsWith('.woff2') ? 'font' : 'script';
      
      if (resource.endsWith('.woff2')) {
        link.crossOrigin = 'anonymous';
      }
      
      document.head.appendChild(link);
    });
  }

  private optimizeFontLoading(): void {
    // Use font-display: swap for better perceived performance
    const style = document.createElement('style');
    style.textContent = `
      @font-face {
        font-family: 'Inter';
        font-display: swap;
        src: url('/fonts/inter-var.woff2') format('woff2');
      }
    `;
    document.head.appendChild(style);
  }

  private setupLazyLoading(): void {
    const lazyObserver = new IntersectionObserver(
      (entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const element = entry.target as HTMLElement;
            this.loadElement(element);
            lazyObserver.unobserve(element);
          }
        });
      },
      {
        rootMargin: `${this.config.lazyLoadThreshold}px`
      }
    );

    this.observers.set('lazy', lazyObserver);
    this.observeLazyElements();
  }

  private observeLazyElements(): void {
    const lazyObserver = this.observers.get('lazy');
    if (!lazyObserver) return;

    // Observe images
    document.querySelectorAll('img[data-src]').forEach(img => {
      lazyObserver.observe(img);
    });

    // Observe components
    document.querySelectorAll('[data-lazy-load]').forEach(element => {
      lazyObserver.observe(element);
    });
  }

  private loadElement(element: HTMLElement): void {
    if (element.tagName === 'IMG') {
      const img = element as HTMLImageElement;
      const src = img.dataset.src;
      if (src) {
        img.src = src;
        img.removeAttribute('data-src');
        this.emit('imageLoaded', { element: img, src });
      }
    } else if (element.dataset.lazyLoad) {
      // Trigger lazy component loading
      element.classList.add('lazy-loaded');
      this.emit('componentLoaded', { element });
    }
  }

  private optimizeImages(): void {
    const images = document.querySelectorAll('img');
    
    images.forEach(img => {
      this.optimizeImage(img);
    });
  }

  private optimizeImage(img: HTMLImageElement): void {
    // Set loading attribute for native lazy loading
    if ('loading' in img && !img.loading) {
      img.loading = 'lazy';
    }

    // Optimize image size based on device
    if (img.src && !img.dataset.optimized) {
      const optimizedSrc = this.getOptimizedImageSrc(img.src);
      if (optimizedSrc !== img.src) {
        img.src = optimizedSrc;
      }
      img.dataset.optimized = 'true';
    }

    // Add decoding attribute
    img.decoding = 'async';
  }

  private getOptimizedImageSrc(originalSrc: string): string {
    const dpr = Math.min(window.devicePixelRatio || 1, 2); // Cap at 2x for performance
    const maxWidth = Math.min(window.innerWidth * dpr, this.config.maxImageSize);
    
    // If using a CDN like Cloudinary or similar, add optimization parameters
    if (originalSrc.includes('cloudinary.com')) {
      return originalSrc.replace('/upload/', `/upload/w_${maxWidth},f_auto,q_auto/`);
    }
    
    // For local images, you might want to have different sizes available
    return originalSrc;
  }

  private setupBatteryOptimization(): void {
    if (!this.config.enableBatteryOptimization) return;

    this.getBatteryInfo().then(battery => {
      if (battery) {
        const lowBattery = battery.level < 0.2;
        const isCharging = battery.charging;
        
        if (lowBattery && !isCharging) {
          this.enablePowerSavingMode();
        }
        
        battery.addEventListener('levelchange', () => {
          this.metrics.batteryLevel = battery.level;
          
          if (battery.level < 0.15 && !battery.charging) {
            this.enablePowerSavingMode();
          } else if (battery.level > 0.5 || battery.charging) {
            this.disablePowerSavingMode();
          }
        });
      }
    });
  }

  private async getBatteryInfo(): Promise<any> {
    try {
      if ('getBattery' in navigator) {
        return await (navigator as any).getBattery();
      }
    } catch (error) {
      console.warn('Battery API not available:', error);
    }
    return null;
  }

  private enablePowerSavingMode(): void {
    if (this.optimizations.has('powerSaving')) return;
    
    this.optimizations.add('powerSaving');
    
    // Reduce animation frame rate
    document.documentElement.style.setProperty('--animation-duration', '0.5s');
    
    // Disable non-critical animations
    document.body.classList.add('power-saving-mode');
    
    // Reduce image quality
    this.reduceImageQuality();
    
    this.emit('powerSavingEnabled', { enabled: true });
  }

  private disablePowerSavingMode(): void {
    if (!this.optimizations.has('powerSaving')) return;
    
    this.optimizations.delete('powerSaving');
    
    // Restore normal animation frame rate
    document.documentElement.style.removeProperty('--animation-duration');
    
    // Re-enable animations
    document.body.classList.remove('power-saving-mode');
    
    this.emit('powerSavingDisabled', { enabled: false });
  }

  private reduceImageQuality(): void {
    const images = document.querySelectorAll('img');
    images.forEach(img => {
      if (img.src.includes('q_auto')) {
        img.src = img.src.replace('q_auto', 'q_60');
      }
    });
  }

  private setupNetworkOptimization(): void {
    const connection = this.getNetworkConnection();
    
    if (connection) {
      this.optimizeForConnection(connection);
      
      connection.addEventListener('change', () => {
        this.metrics.networkType = connection.effectiveType;
        this.optimizeForConnection(connection);
      });
    }
  }

  private getNetworkConnection(): any {
    return (navigator as any).connection || 
           (navigator as any).mozConnection || 
           (navigator as any).webkitConnection;
  }

  private optimizeForConnection(connection: any): void {
    const effectiveType = connection.effectiveType;
    
    if (effectiveType === 'slow-2g' || effectiveType === '2g') {
      this.enableLowBandwidthMode();
    } else if (effectiveType === '3g') {
      this.enableMediumBandwidthMode();
    } else {
      this.enableHighBandwidthMode();
    }
  }

  private enableLowBandwidthMode(): void {
    if (this.optimizations.has('lowBandwidth')) return;
    
    this.optimizations.add('lowBandwidth');
    
    // Disable autoplay videos
    document.querySelectorAll('video[autoplay]').forEach(video => {
      (video as HTMLVideoElement).autoplay = false;
    });
    
    // Use lower quality images
    this.config.maxImageSize = 800;
    
    this.emit('lowBandwidthMode', { enabled: true });
  }

  private enableMediumBandwidthMode(): void {
    this.optimizations.delete('lowBandwidth');
    this.config.maxImageSize = 1200;
    
    this.emit('mediumBandwidthMode', { enabled: true });
  }

  private enableHighBandwidthMode(): void {
    this.optimizations.delete('lowBandwidth');
    this.config.maxImageSize = 1920;
    
    this.emit('highBandwidthMode', { enabled: true });
  }

  private setupMemoryOptimization(): void {
    if (!this.config.enableMemoryOptimization) return;

    // Monitor memory usage
    setInterval(() => {
      const currentMemory = this.getMemoryUsage();
      this.metrics.memoryUsage = currentMemory;
      
      if (currentMemory > 100) { // MB
        this.optimizeMemoryUsage();
      }
    }, 30000); // Check every 30 seconds
  }

  private getMemoryUsage(): number {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      return Math.round(memory.usedJSHeapSize / 1024 / 1024); // Convert to MB
    }
    return 0;
  }

  private getBatteryLevel(): number | undefined {
    // This will be set asynchronously when battery API is available
    return undefined;
  }

  private getNetworkType(): string {
    const connection = this.getNetworkConnection();
    return connection?.effectiveType || 'unknown';
  }

  private optimizeMemoryUsage(): void {
    // Clean up unused observers
    this.observers.forEach((observer, key) => {
      if (key !== 'lazy') {
        observer.disconnect();
        this.observers.delete(key);
      }
    });
    
    // Trigger garbage collection if possible
    if ('gc' in window) {
      (window as any).gc();
    }
    
    this.emit('memoryOptimized', { usage: this.metrics.memoryUsage });
  }

  // Public API
  public addLazyElement(element: HTMLElement): void {
    const lazyObserver = this.observers.get('lazy');
    if (lazyObserver) {
      lazyObserver.observe(element);
    }
  }

  public optimizeNewImage(img: HTMLImageElement): void {
    this.optimizeImage(img);
  }

  public getMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  public getOptimizationStatus(): string[] {
    return Array.from(this.optimizations);
  }

  public forceOptimization(type: 'images' | 'memory' | 'lazy'): void {
    switch (type) {
      case 'images':
        this.optimizeImages();
        break;
      case 'memory':
        this.optimizeMemoryUsage();
        break;
      case 'lazy':
        this.observeLazyElements();
        break;
    }
  }

  // Event system
  public on(eventName: string, callback: Function): void {
    if (!this.callbacks.has(eventName)) {
      this.callbacks.set(eventName, []);
    }
    this.callbacks.get(eventName)!.push(callback);
  }

  public off(eventName: string, callback?: Function): void {
    if (!callback) {
      this.callbacks.delete(eventName);
      return;
    }

    const callbacks = this.callbacks.get(eventName);
    if (callbacks) {
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  private emit(eventName: string, data: any): void {
    const callbacks = this.callbacks.get(eventName);
    if (callbacks) {
      callbacks.forEach(callback => callback(data));
    }
  }

  public destroy(): void {
    this.observers.forEach(observer => observer.disconnect());
    this.observers.clear();
    this.callbacks.clear();
    this.optimizations.clear();
  }
}

// Create singleton instance
const mobilePerformanceOptimizer = new MobilePerformanceOptimizer();

// Auto-optimize on page load
if (typeof window !== 'undefined') {
  window.addEventListener('load', () => {
    mobilePerformanceOptimizer.forceOptimization('images');
    mobilePerformanceOptimizer.forceOptimization('lazy');
  });
}

export default mobilePerformanceOptimizer;
export { MobilePerformanceOptimizer };
export type { PerformanceConfig, PerformanceMetrics };