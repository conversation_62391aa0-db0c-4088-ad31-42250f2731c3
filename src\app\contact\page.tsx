import { Layout, Container } from '@/components/layout';
import { Card } from '@/components/ui/card';
import { Mail, Phone, MapPin } from 'lucide-react';

export default function ContactPage() {
  return (
    <Layout>
      <Container>
        <div className='py-12'>
          <div className='text-center mb-8'>
            <h1 className='text-4xl font-bold mb-4'>Contact Us</h1>
            <p className='text-lg text-muted-foreground'>
              Get in touch with our team for support and inquiries
            </p>
          </div>

          <div className='grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto'>
            <Card className='p-6 text-center space-y-4'>
              <div className='w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center mx-auto'>
                <Mail className='h-6 w-6 text-primary' />
              </div>
              <h3 className='font-semibold'>Email</h3>
              <p className='text-sm text-muted-foreground'>
                <EMAIL>
              </p>
            </Card>

            <Card className='p-6 text-center space-y-4'>
              <div className='w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center mx-auto'>
                <Phone className='h-6 w-6 text-primary' />
              </div>
              <h3 className='font-semibold'>Phone</h3>
              <p className='text-sm text-muted-foreground'>+91 98765 43210</p>
            </Card>

            <Card className='p-6 text-center space-y-4'>
              <div className='w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center mx-auto'>
                <MapPin className='h-6 w-6 text-primary' />
              </div>
              <h3 className='font-semibold'>Office</h3>
              <p className='text-sm text-muted-foreground'>
                Mumbai, Maharashtra
              </p>
            </Card>
          </div>
        </div>
      </Container>
    </Layout>
  );
}
