/**
 * Enhanced Skeleton Components with Progressive Loading
 * Provides comprehensive loading states with accessibility and responsive design
 */

'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { cn } from "@/lib/utils";
import { shimmerLoading, prefersReducedMotion, staggerContainer, staggerItem, fadeIn } from '@/lib/animations';
import { useEffect, useState } from 'react';

interface SkeletonProps extends React.HTMLAttributes<HTMLDivElement> {
  shimmer?: boolean;
  variant?: 'default' | 'card' | 'text' | 'circle' | 'button' | 'avatar' | 'paragraph' | 'image' | 'list-item';
  lines?: number;
  width?: string;
  height?: string;
  animation?: 'pulse' | 'wave' | 'shimmer' | 'none';
  delay?: number;
  responsive?: boolean;
}

function Skeleton({
  className,
  shimmer = true,
  variant = 'default',
  lines = 1,
  width,
  height,
  animation = 'shimmer',
  delay = 0,
  responsive = true,
  ...props
}: SkeletonProps) {
  const [isVisible, setIsVisible] = useState(false);
  
  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), delay);
    return () => clearTimeout(timer);
  }, [delay]);

  const baseClasses = "relative overflow-hidden";
  
  const variantClasses = {
    default: "rounded-md bg-gray-200 h-4",
    card: "rounded-lg bg-gray-200 h-32 w-full",
    text: "rounded bg-gray-200 h-4 w-full",
    circle: "rounded-full bg-gray-200 aspect-square",
    button: "rounded-md bg-gray-200 h-10 w-24",
    avatar: "rounded-full bg-gray-200 w-10 h-10",
    paragraph: "rounded bg-gray-200 h-4 w-full",
    image: "rounded-lg bg-gray-200 aspect-video w-full",
    'list-item': "rounded-md bg-gray-200 h-12 w-full"
  };

  const responsiveClasses = responsive ? {
    default: "h-3 sm:h-4",
    text: "h-3 sm:h-4",
    paragraph: "h-3 sm:h-4",
    button: "h-8 sm:h-10 w-20 sm:w-24",
    avatar: "w-8 h-8 sm:w-10 sm:h-10",
    'list-item': "h-10 sm:h-12"
  } : {};

  const getAnimationClasses = () => {
    if (prefersReducedMotion || animation === 'none') return "bg-gray-200";
    
    switch (animation) {
      case 'pulse':
        return "bg-gray-200 animate-pulse";
      case 'wave':
        return "bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 bg-[length:200px_100%] animate-[wave_1.5s_ease-in-out_infinite]";
      case 'shimmer':
      default:
        return shimmer 
          ? "bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 bg-[length:200px_100%]"
          : "bg-gray-200";
    }
  };

  const animationClasses = getAnimationClasses();

  // For multiple text lines
  if ((variant === 'text' || variant === 'paragraph') && lines > 1) {
    return (
      <AnimatePresence>
        {isVisible && (
          <motion.div 
            className="space-y-2"
            variants={staggerContainer}
            initial="initial"
            animate="animate"
          >
            {Array.from({ length: lines }, (_, i) => (
              <motion.div
                key={i}
                variants={staggerItem}
                className={cn(
                  baseClasses,
                  variantClasses[variant],
                  animationClasses,
                  responsive && responsiveClasses[variant],
                  className
                )}
                style={{
                  width: width || (i === lines - 1 ? '75%' : '100%'),
                  height: height || undefined
                }}
                {...(shimmer && !prefersReducedMotion ? shimmerLoading : {})}
                {...props}
              />
            ))}
          </motion.div>
        )}
      </AnimatePresence>
    );
  }

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          variants={fadeIn}
          initial="initial"
          animate="animate"
          exit="exit"
          className={cn(
            baseClasses,
            variantClasses[variant],
            animationClasses,
            responsive && responsiveClasses[variant],
            className
          )}
          style={{
            width: width || undefined,
            height: height || undefined
          }}
          {...(shimmer && !prefersReducedMotion ? shimmerLoading : {})}
          {...props}
        />
      )}
    </AnimatePresence>
  );
}

// Progressive Loading Component
interface ProgressiveSkeletonProps {
  phase: 'loading' | 'loaded';
  skeleton: React.ReactNode;
  content: React.ReactNode;
  transition?: 'fade' | 'slide' | 'scale';
}

function ProgressiveSkeleton({ 
  phase, 
  skeleton, 
  content, 
  transition = 'fade' 
}: ProgressiveSkeletonProps) {
  const transitions = {
    fade: fadeIn,
    slide: {
      initial: { opacity: 0, x: -20 },
      animate: { opacity: 1, x: 0 },
      exit: { opacity: 0, x: 20 }
    },
    scale: {
      initial: { opacity: 0, scale: 0.9 },
      animate: { opacity: 1, scale: 1 },
      exit: { opacity: 0, scale: 1.1 }
    }
  };

  return (
    <AnimatePresence mode="wait">
      {phase === 'loading' ? (
        <motion.div
          key="skeleton"
          variants={transitions[transition]}
          initial="initial"
          animate="animate"
          exit="exit"
        >
          {skeleton}
        </motion.div>
      ) : (
        <motion.div
          key="content"
          variants={transitions[transition]}
          initial="initial"
          animate="animate"
          exit="exit"
        >
          {content}
        </motion.div>
      )}
    </AnimatePresence>
  );
}

// Enhanced skeleton components with staggered animations
interface SkeletonCardProps extends React.HTMLAttributes<HTMLDivElement> {
  showAvatar?: boolean;
  showImage?: boolean;
  textLines?: number;
  delay?: number;
}

function SkeletonCard({ 
  className, 
  showAvatar = true, 
  showImage = true, 
  textLines = 2,
  delay = 0,
  ...props 
}: SkeletonCardProps) {
  return (
    <motion.div 
      className={cn("space-y-4 p-4 sm:p-6 border rounded-lg", className)} 
      variants={staggerContainer}
      initial="initial"
      animate="animate"
      {...props}
    >
      {showAvatar && (
        <motion.div 
          variants={staggerItem}
          className="flex items-center space-x-3 sm:space-x-4"
        >
          <Skeleton variant="avatar" delay={delay} />
          <div className="space-y-2 flex-1">
            <Skeleton variant="text" className="w-3/4" delay={delay + 100} />
            <Skeleton variant="text" className="w-1/2" delay={delay + 200} />
          </div>
        </motion.div>
      )}
      
      {showImage && (
        <motion.div variants={staggerItem}>
          <Skeleton variant="image" delay={delay + 300} />
        </motion.div>
      )}
      
      <motion.div variants={staggerItem} className="space-y-2">
        <Skeleton variant="paragraph" lines={textLines} delay={delay + 400} />
      </motion.div>
    </motion.div>
  );
}

interface SkeletonCalculatorProps extends React.HTMLAttributes<HTMLDivElement> {
  showProgress?: boolean;
  inputCount?: number;
  resultCards?: number;
}

function SkeletonCalculator({ 
  className, 
  showProgress = true,
  inputCount = 4,
  resultCards = 3,
  ...props 
}: SkeletonCalculatorProps) {
  return (
    <motion.div 
      className={cn("space-y-6", className)} 
      variants={staggerContainer}
      initial="initial"
      animate="animate"
      {...props}
    >
      {/* Progress indicator skeleton */}
      {showProgress && (
        <motion.div variants={staggerItem} className="mb-6">
          <div className="flex items-center justify-between mb-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="flex items-center">
                <Skeleton variant="circle" className="w-8 h-8 sm:w-10 sm:h-10" delay={i * 50} />
                {i < 3 && <Skeleton className="w-16 sm:w-20 h-1 mx-2" delay={i * 50 + 25} />}
              </div>
            ))}
          </div>
          <div className="text-center space-y-2">
            <Skeleton variant="text" className="h-5 sm:h-6 w-40 sm:w-48 mx-auto" delay={200} />
            <Skeleton variant="text" className="h-3 sm:h-4 w-48 sm:w-64 mx-auto" delay={250} />
          </div>
        </motion.div>
      )}

      {/* Input form skeleton */}
      <motion.div variants={staggerItem} className="space-y-4">
        <Skeleton variant="text" className="h-5 sm:h-6 w-32 sm:w-40" delay={300} />
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {Array.from({ length: inputCount }, (_, i) => (
            <motion.div 
              key={i} 
              variants={staggerItem}
              className="space-y-2"
            >
              <Skeleton variant="text" className="h-3 sm:h-4 w-20 sm:w-24" delay={400 + i * 50} />
              <Skeleton variant="button" className="h-9 sm:h-10 w-full" delay={450 + i * 50} />
            </motion.div>
          ))}
        </div>
      </motion.div>
      
      {/* Results skeleton */}
      <motion.div variants={staggerItem} className="space-y-4">
        <Skeleton variant="text" className="h-5 sm:h-6 w-40 sm:w-48" delay={800} />
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {Array.from({ length: resultCards }, (_, i) => (
            <SkeletonCard 
              key={i} 
              showAvatar={false} 
              showImage={false}
              textLines={3}
              delay={900 + i * 100}
            />
          ))}
        </div>
      </motion.div>
    </motion.div>
  );
}

interface SkeletonBreakdownProps extends React.HTMLAttributes<HTMLDivElement> {
  itemCount?: number;
  showProgress?: boolean;
}

function SkeletonBreakdown({ 
  className, 
  itemCount = 5, 
  showProgress = true,
  ...props 
}: SkeletonBreakdownProps) {
  return (
    <motion.div 
      className={cn("space-y-3 sm:space-y-4", className)} 
      variants={staggerContainer}
      initial="initial"
      animate="animate"
      {...props}
    >
      {Array.from({ length: itemCount }, (_, i) => (
        <motion.div 
          key={i} 
          variants={staggerItem}
          className="p-3 sm:p-4 border rounded-lg space-y-3"
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Skeleton variant="circle" className="h-6 w-6 sm:h-8 sm:w-8" delay={i * 100} />
              <div className="space-y-2 flex-1">
                <Skeleton variant="text" className="h-3 sm:h-4 w-32 sm:w-40" delay={i * 100 + 50} />
                <Skeleton variant="text" className="h-2 sm:h-3 w-24 sm:w-32" delay={i * 100 + 100} />
              </div>
            </div>
            <div className="text-right space-y-2">
              <Skeleton variant="text" className="h-5 sm:h-6 w-20 sm:w-24" delay={i * 100 + 150} />
              <Skeleton variant="text" className="h-2 sm:h-3 w-12 sm:w-16" delay={i * 100 + 200} />
            </div>
          </div>
          {showProgress && (
            <Skeleton variant="text" className="h-1.5 sm:h-2 w-full" delay={i * 100 + 250} />
          )}
        </motion.div>
      ))}
    </motion.div>
  );
}

// Dashboard skeleton components
interface SkeletonDashboardProps extends React.HTMLAttributes<HTMLDivElement> {
  showStats?: boolean;
  showCharts?: boolean;
  showProjectList?: boolean;
}

function SkeletonDashboard({ 
  className, 
  showStats = true,
  showCharts = true,
  showProjectList = true,
  ...props 
}: SkeletonDashboardProps) {
  return (
    <motion.div 
      className={cn("space-y-6", className)}
      variants={staggerContainer}
      initial="initial"
      animate="animate"
      {...props}
    >
      {/* Header */}
      <motion.div variants={staggerItem} className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="space-y-2">
          <Skeleton variant="text" className="h-7 sm:h-8 w-48 sm:w-64" />
          <Skeleton variant="text" className="h-4 w-32 sm:w-48" />
        </div>
        <div className="flex gap-2">
          <Skeleton variant="button" className="h-9 sm:h-10 w-24 sm:w-32" />
          <Skeleton variant="button" className="h-9 sm:h-10 w-20 sm:w-24" />
        </div>
      </motion.div>

      {/* Stats cards */}
      {showStats && (
        <motion.div variants={staggerItem} className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          {Array.from({ length: 4 }, (_, i) => (
            <motion.div 
              key={i}
              variants={staggerItem}
              className="p-4 sm:p-6 border rounded-lg space-y-2"
            >
              <div className="flex items-center justify-between">
                <Skeleton variant="text" className="h-3 sm:h-4 w-16 sm:w-20" delay={i * 50} />
                <Skeleton variant="circle" className="h-4 w-4 sm:h-5 sm:w-5" delay={i * 50 + 25} />
              </div>
              <Skeleton variant="text" className="h-6 sm:h-8 w-20 sm:w-24" delay={i * 50 + 50} />
              <Skeleton variant="text" className="h-3 w-24 sm:w-32" delay={i * 50 + 75} />
            </motion.div>
          ))}
        </motion.div>
      )}

      {/* Charts */}
      {showCharts && (
        <motion.div variants={staggerItem} className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="p-4 sm:p-6 border rounded-lg space-y-4">
            <Skeleton variant="text" className="h-5 sm:h-6 w-32 sm:w-40" />
            <Skeleton variant="image" className="h-48 sm:h-64" />
          </div>
          <div className="p-4 sm:p-6 border rounded-lg space-y-4">
            <Skeleton variant="text" className="h-5 sm:h-6 w-32 sm:w-40" />
            <Skeleton variant="image" className="h-48 sm:h-64" />
          </div>
        </motion.div>
      )}

      {/* Project list */}
      {showProjectList && (
        <motion.div variants={staggerItem} className="space-y-4">
          <div className="flex items-center justify-between">
            <Skeleton variant="text" className="h-5 sm:h-6 w-32 sm:w-40" />
            <Skeleton variant="text" className="h-4 w-16 sm:w-20" />
          </div>
          <div className="space-y-3">
            {Array.from({ length: 3 }, (_, i) => (
              <motion.div 
                key={i}
                variants={staggerItem}
                className="p-3 sm:p-4 border rounded-lg"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Skeleton variant="circle" className="h-8 w-8 sm:h-10 sm:w-10" delay={i * 100} />
                    <div className="space-y-1">
                      <Skeleton variant="text" className="h-4 w-32 sm:w-48" delay={i * 100 + 50} />
                      <Skeleton variant="text" className="h-3 w-24 sm:w-32" delay={i * 100 + 100} />
                    </div>
                  </div>
                  <div className="text-right space-y-1">
                    <Skeleton variant="text" className="h-4 w-16 sm:w-20" delay={i * 100 + 150} />
                    <Skeleton variant="text" className="h-3 w-12 sm:w-16" delay={i * 100 + 200} />
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      )}
    </motion.div>
  );
}

// Form skeleton components
interface SkeletonFormProps extends React.HTMLAttributes<HTMLDivElement> {
  fieldCount?: number;
  showSubmit?: boolean;
  layout?: 'vertical' | 'horizontal' | 'grid';
}

function SkeletonForm({ 
  className, 
  fieldCount = 6,
  showSubmit = true,
  layout = 'vertical',
  ...props 
}: SkeletonFormProps) {
  const getLayoutClasses = () => {
    switch (layout) {
      case 'horizontal':
        return "grid grid-cols-1 md:grid-cols-2 gap-4";
      case 'grid':
        return "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4";
      case 'vertical':
      default:
        return "space-y-4";
    }
  };

  return (
    <motion.div 
      className={cn("space-y-6", className)}
      variants={staggerContainer}
      initial="initial"
      animate="animate"
      {...props}
    >
      <motion.div variants={staggerItem} className={getLayoutClasses()}>
        {Array.from({ length: fieldCount }, (_, i) => (
          <motion.div 
            key={i}
            variants={staggerItem}
            className="space-y-2"
          >
            <Skeleton variant="text" className="h-3 sm:h-4 w-20 sm:w-24" delay={i * 75} />
            <Skeleton variant="button" className="h-9 sm:h-10 w-full" delay={i * 75 + 50} />
          </motion.div>
        ))}
      </motion.div>

      {showSubmit && (
        <motion.div variants={staggerItem} className="flex justify-end gap-2 pt-4">
          <Skeleton variant="button" className="h-9 sm:h-10 w-20 sm:w-24" delay={fieldCount * 75 + 100} />
          <Skeleton variant="button" className="h-9 sm:h-10 w-24 sm:w-32" delay={fieldCount * 75 + 150} />
        </motion.div>
      )}
    </motion.div>
  );
}

// List skeleton components
interface SkeletonListProps extends React.HTMLAttributes<HTMLDivElement> {
  itemCount?: number;
  showAvatar?: boolean;
  showActions?: boolean;
  variant?: 'simple' | 'detailed' | 'compact';
}

function SkeletonList({ 
  className, 
  itemCount = 5,
  showAvatar = true,
  showActions = true,
  variant = 'simple',
  ...props 
}: SkeletonListProps) {
  const getItemContent = (index: number) => {
    const baseDelay = index * 100;
    
    switch (variant) {
      case 'detailed':
        return (
          <div className="flex items-start justify-between p-4 border rounded-lg">
            <div className="flex items-start space-x-3 flex-1">
              {showAvatar && <Skeleton variant="avatar" delay={baseDelay} />}
              <div className="space-y-2 flex-1">
                <Skeleton variant="text" className="h-4 w-3/4" delay={baseDelay + 50} />
                <Skeleton variant="text" className="h-3 w-1/2" delay={baseDelay + 100} />
                <Skeleton variant="paragraph" lines={2} delay={baseDelay + 150} />
              </div>
            </div>
            {showActions && (
              <div className="flex gap-2">
                <Skeleton variant="button" className="h-8 w-16" delay={baseDelay + 200} />
                <Skeleton variant="button" className="h-8 w-8" delay={baseDelay + 250} />
              </div>
            )}
          </div>
        );
      
      case 'compact':
        return (
          <div className="flex items-center justify-between p-2 border-b">
            <div className="flex items-center space-x-2">
              {showAvatar && <Skeleton variant="circle" className="h-6 w-6" delay={baseDelay} />}
              <Skeleton variant="text" className="h-3 w-32" delay={baseDelay + 50} />
            </div>
            {showActions && (
              <Skeleton variant="text" className="h-3 w-16" delay={baseDelay + 100} />
            )}
          </div>
        );
      
      case 'simple':
      default:
        return (
          <div className="flex items-center justify-between p-3 border rounded-lg">
            <div className="flex items-center space-x-3">
              {showAvatar && <Skeleton variant="avatar" delay={baseDelay} />}
              <div className="space-y-1">
                <Skeleton variant="text" className="h-4 w-40" delay={baseDelay + 50} />
                <Skeleton variant="text" className="h-3 w-24" delay={baseDelay + 100} />
              </div>
            </div>
            {showActions && (
              <div className="flex gap-2">
                <Skeleton variant="button" className="h-8 w-16" delay={baseDelay + 150} />
              </div>
            )}
          </div>
        );
    }
  };

  return (
    <motion.div 
      className={cn("space-y-2", className)}
      variants={staggerContainer}
      initial="initial"
      animate="animate"
      {...props}
    >
      {Array.from({ length: itemCount }, (_, i) => (
        <motion.div key={i} variants={staggerItem}>
          {getItemContent(i)}
        </motion.div>
      ))}
    </motion.div>
  );
}

export { 
  Skeleton,
  SkeletonCard,
  SkeletonCalculator,
  SkeletonBreakdown,
  SkeletonDashboard,
  SkeletonForm,
  SkeletonList,
  ProgressiveSkeleton
};