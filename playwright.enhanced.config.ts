import { defineConfig, devices } from '@playwright/test';
import path from 'path';

/**
 * Enhanced Playwright configuration for comprehensive E2E testing
 * Features:
 * - Cross-browser testing (Chromium, Firefox, Safari)
 * - Mobile device testing
 * - Visual regression testing
 * - Accessibility testing
 * - Performance testing
 * - Parallelization
 */

const baseURL = process.env.BASE_URL || 'http://localhost:3000';
const isCI = !!process.env.CI;
const testDir = './tests/e2e';

export default defineConfig({
  testDir,
  
  /* Run tests in files in parallel */
  fullyParallel: !isCI,
  
  /* Fail the build on CI if you accidentally left test.only in the source code. */
  forbidOnly: isCI,
  
  /* Retry on CI only */
  retries: isCI ? 2 : 0,
  
  /* Opt out of parallel tests on CI. */
  workers: isCI ? 1 : undefined,
  
  /* Reporter to use. See https://playwright.dev/docs/test-reporters */
  reporter: [
    ['html', { outputFolder: 'tests/e2e/reports/html' }],
    ['json', { outputFile: 'tests/e2e/reports/results.json' }],
    ['junit', { outputFile: 'tests/e2e/reports/results.xml' }],
    ['line'],
    ...(isCI ? [['github']] as const : []),
  ],
  
  /* Shared settings for all the projects below. See https://playwright.dev/docs/api/class-testoptions. */
  use: {
    /* Base URL to use in actions like `await page.goto('/')`. */
    baseURL,
    
    /* Collect trace when retrying the failed test. See https://playwright.dev/docs/trace-viewer */
    trace: 'on-first-retry',
    
    /* Take screenshot on failure */
    screenshot: 'only-on-failure',
    
    /* Record video on failure */
    video: 'retain-on-failure',
    
    /* Timeout for each action */
    actionTimeout: 15000,
    
    /* Timeout for navigation */
    navigationTimeout: 30000,
    
    /* Global test timeout */
    testIdAttribute: 'data-testid',
    
    /* Ignore HTTPS errors */
    ignoreHTTPSErrors: true,
    
    /* Context options */
    contextOptions: {
      /* Reduce motion for consistent testing */
      reducedMotion: 'reduce',
      /* Set color scheme */
      colorScheme: 'light',
      /* Set timezone */
      timezoneId: 'Asia/Kolkata',
      /* Set locale */
      locale: 'en-IN',
      /* Set geolocation */
      geolocation: { latitude: 12.9716, longitude: 77.5946 }, // Bangalore
      permissions: ['geolocation'],
    },
  },
  
  /* Configure global timeout */
  timeout: 60000,
  
  /* Configure expect timeout */
  expect: {
    timeout: 10000,
    toHaveScreenshot: { 
      threshold: 0.2,
    },
    toMatchSnapshot: {
      threshold: 0.2,
    },
  },
  
  /* Configure global setup and teardown */
  globalSetup: require.resolve('./tests/e2e/setup/global-setup.ts'),
  globalTeardown: require.resolve('./tests/e2e/setup/global-teardown.ts'),
  
  /* Configure projects for major browsers */
  projects: [
    {
      name: 'setup',
      testMatch: /.*\.setup\.ts/,
      use: {
        ...devices['Desktop Chrome'],
      },
    },
    
    // Desktop browsers
    {
      name: 'chromium',
      use: {
        ...devices['Desktop Chrome'],
        // Enable Chrome DevTools Protocol for performance metrics
        launchOptions: {
          args: [
            '--enable-precise-memory-info',
            '--enable-web-bluetooth',
            '--enable-features=NetworkService,NetworkServiceLogging',
          ],
        },
      },
      dependencies: ['setup'],
    },
    
    {
      name: 'firefox',
      use: {
        ...devices['Desktop Firefox'],
      },
      dependencies: ['setup'],
    },
    
    {
      name: 'webkit',
      use: {
        ...devices['Desktop Safari'],
      },
      dependencies: ['setup'],
    },
    
    // Mobile browsers
    {
      name: 'mobile-chrome',
      use: {
        ...devices['Pixel 5'],
        hasTouch: true,
      },
      dependencies: ['setup'],
    },
    
    {
      name: 'mobile-safari',
      use: {
        ...devices['iPhone 12'],
        hasTouch: true,
      },
      dependencies: ['setup'],
    },
    
    // Tablet
    {
      name: 'tablet',
      use: {
        ...devices['iPad Pro'],
        hasTouch: true,
      },
      dependencies: ['setup'],
    },
    
    // High DPI display
    {
      name: 'high-dpi',
      use: {
        ...devices['Desktop Chrome'],
        deviceScaleFactor: 2,
        viewport: { width: 1920, height: 1080 },
      },
      dependencies: ['setup'],
    },
    
    // Dark mode testing
    {
      name: 'dark-mode',
      use: {
        ...devices['Desktop Chrome'],
        colorScheme: 'dark',
      },
      dependencies: ['setup'],
    },
    
    // Slow network simulation
    {
      name: 'slow-network',
      use: {
        ...devices['Desktop Chrome'],
        launchOptions: {
          args: ['--simulate-slow-network'],
        },
      },
      dependencies: ['setup'],
    },
    
    // Performance testing
    {
      name: 'performance',
      testMatch: /.*\.performance\.spec\.ts/,
      use: {
        ...devices['Desktop Chrome'],
        launchOptions: {
          args: [
            '--enable-precise-memory-info',
            '--enable-web-bluetooth',
            '--enable-features=NetworkService,NetworkServiceLogging',
          ],
        },
      },
      dependencies: ['setup'],
    },
    
    // Accessibility testing
    {
      name: 'accessibility',
      testMatch: /.*\.a11y\.spec\.ts/,
      use: {
        ...devices['Desktop Chrome'],
        // Enable accessibility features
        launchOptions: {
          args: [
            '--force-renderer-accessibility',
            '--enable-experimental-accessibility-features',
          ],
        },
      },
      dependencies: ['setup'],
    },
    
    // Visual regression testing
    {
      name: 'visual-regression',
      testMatch: /.*\.visual\.spec\.ts/,
      use: {
        ...devices['Desktop Chrome'],
        // Consistent viewport for visual tests
        viewport: { width: 1280, height: 720 },
      },
      dependencies: ['setup'],
    },
    
    // API testing
    {
      name: 'api',
      testMatch: /.*\.api\.spec\.ts/,
      use: {
        baseURL: `${baseURL}/api`,
        extraHTTPHeaders: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
      },
    },
  ],
  
  /* Configure test output directory */
  outputDir: 'tests/e2e/results',
  
  /* Configure web server for testing */
  webServer: {
    command: 'npm run build && npm run start',
    url: baseURL,
    reuseExistingServer: !isCI,
    timeout: 120000,
    stdout: 'ignore',
    stderr: 'pipe',
  },
  
  /* Configure metadata */
  metadata: {
    testSuite: 'Nirmaan AI Construction Calculator E2E Tests',
    version: '1.0.0',
    environment: process.env.NODE_ENV || 'test',
    baseURL,
    timestamp: new Date().toISOString(),
  },
});

/* Custom test configuration for specific scenarios */
export const mobileConfig = defineConfig({
  testDir,
  use: { baseURL },
  projects: [
    {
      name: 'mobile-chrome',
      use: {
        ...devices['Pixel 5'],
        hasTouch: true,
      },
    },
    {
      name: 'mobile-safari',
      use: {
        ...devices['iPhone 12'],
        hasTouch: true,
      },
    },
  ],
});

export const performanceConfig = defineConfig({
  testDir,
  use: { baseURL },
  projects: [
    {
      name: 'performance',
      testMatch: /.*\.performance\.spec\.ts/,
      use: {
        ...devices['Desktop Chrome'],
        launchOptions: {
          args: [
            '--enable-precise-memory-info',
            '--enable-web-bluetooth',
            '--enable-features=NetworkService,NetworkServiceLogging',
          ],
        },
      },
    },
  ],
});

export const accessibilityConfig = defineConfig({
  testDir,
  use: { baseURL },
  projects: [
    {
      name: 'accessibility',
      testMatch: /.*\.a11y\.spec\.ts/,
      use: {
        ...devices['Desktop Chrome'],
        launchOptions: {
          args: [
            '--force-renderer-accessibility',
            '--enable-experimental-accessibility-features',
          ],
        },
      },
    },
  ],
});

export const visualConfig = defineConfig({
  testDir,
  use: { baseURL },
  projects: [
    {
      name: 'visual-regression',
      testMatch: /.*\.visual\.spec\.ts/,
      use: {
        ...devices['Desktop Chrome'],
        viewport: { width: 1280, height: 720 },
      },
    },
  ],
});