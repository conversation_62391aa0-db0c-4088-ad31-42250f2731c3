# 🚀 Deployment Guide

*Comprehensive guide for deploying Nirmaan AI Construction Calculator to production*

## 🎯 Overview

This guide covers the complete deployment process for the Nirmaan AI Construction Calculator, from development to production, including CI/CD setup, monitoring, and maintenance procedures.

## 🏗️ Deployment Architecture

### Production Infrastructure

```
┌─────────────────────────────────────────────────────────┐
│                    CDN (Vercel Edge)                    │
├─────────────────────────────────────────────────────────┤
│                Load Balancer & Routing                 │
├─────────────────────────────────────────────────────────┤
│     Frontend (Next.js)     │    API Routes (Serverless) │
├─────────────────────────────┼─────────────────────────────┤
│                Database (Supabase PostgreSQL)          │
├─────────────────────────────────────────────────────────┤
│      Monitoring & Logging (Vercel Analytics)          │
└─────────────────────────────────────────────────────────┘
```

### Environment Tiers

**Development**
- Local development environment
- Hot reloading and debugging tools
- Test data and relaxed security

**Staging**
- Production-like environment
- Feature testing and QA
- Performance testing

**Production**
- Live user environment
- Full monitoring and logging
- Optimized performance

## 🚀 Vercel Deployment

### Prerequisites

1. **Vercel Account**: Sign up at [vercel.com](https://vercel.com)
2. **GitHub Repository**: Code hosted on GitHub
3. **Supabase Project**: Database and backend services
4. **Domain** (Optional): Custom domain for production

### Initial Setup

**1. Install Vercel CLI**
```bash
npm install -g vercel
vercel login
```

**2. Project Configuration**
```bash
# In your project directory
vercel

# Follow the prompts:
# ? Set up and deploy "construction-calculator"? Y
# ? Which scope do you want to deploy to? [Your Team]
# ? Link to existing project? N
# ? What's your project's name? nirmaan-ai
# ? In which directory is your code located? ./
```

**3. Environment Variables**

Configure in Vercel Dashboard:
```env
# Database
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Security
NEXTAUTH_SECRET=your-nextauth-secret
NEXTAUTH_URL=https://your-domain.vercel.app

# Analytics (Optional)
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=G-XXXXXXXXXX
NEXT_PUBLIC_VERCEL_ANALYTICS_ID=your-analytics-id

# Monitoring
NEXT_PUBLIC_SENTRY_DSN=your-sentry-dsn
WEBHOOK_SECRET=your-webhook-secret

# External APIs
RAZORPAY_KEY_ID=your-razorpay-key
RAZORPAY_KEY_SECRET=your-razorpay-secret
```

### Production Deployment

**Manual Deployment**
```bash
# Deploy to production
vercel --prod

# Deploy specific branch
vercel --prod --scope your-team
```

**Automatic Deployment**

Configure automatic deployments in Vercel:
1. Go to Vercel Dashboard
2. Select your project
3. Go to Settings → Git
4. Configure:
   - **Production Branch**: `main`
   - **Preview Branches**: All branches except `main`
   - **Ignored Build Step**: None

### Custom Domain Setup

**1. Add Domain in Vercel**
```bash
vercel domains add your-domain.com
```

**2. Configure DNS**
Add these DNS records:
```
Type: CNAME
Name: www
Value: cname.vercel-dns.com

Type: A
Name: @
Value: 76.76.19.19
```

**3. SSL Certificate**
- Automatically provisioned by Vercel
- Let's Encrypt certificates
- Auto-renewal enabled

## 🗄️ Database Deployment

### Supabase Setup

**1. Create Project**
```bash
# Install Supabase CLI
npm install -g supabase

# Login to Supabase
supabase login

# Link to existing project
supabase link --project-ref your-project-ref
```

**2. Database Migrations**
```bash
# Push migrations to production
supabase db push

# Reset database (caution: destructive)
supabase db reset --linked

# Generate TypeScript types
supabase gen types typescript --linked > src/types/supabase.ts
```

**3. Row Level Security (RLS)**
```sql
-- Enable RLS on all tables
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE calculations ENABLE ROW LEVEL SECURITY;
ALTER TABLE materials ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Users can view own projects" ON projects
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create own projects" ON projects
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own projects" ON projects
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own projects" ON projects
  FOR DELETE USING (auth.uid() = user_id);
```

**4. Database Performance**
```sql
-- Create indexes for performance
CREATE INDEX idx_projects_user_id ON projects(user_id);
CREATE INDEX idx_projects_created_at ON projects(created_at DESC);
CREATE INDEX idx_calculations_project_id ON calculations(project_id);
CREATE INDEX idx_materials_category ON materials(category);

-- Enable connection pooling
-- Configure in Supabase Dashboard: Settings > Database > Connection pooling
```

### Backup Strategy

**Automated Backups**
- Supabase provides automated daily backups
- Point-in-time recovery available
- Backup retention: 30 days (Pro plan)

**Manual Backup**
```bash
# Export database
supabase db dump --data-only > backup-$(date +%Y%m%d).sql

# Export schema only
supabase db dump --schema-only > schema-$(date +%Y%m%d).sql
```

## 🔄 CI/CD Pipeline

### GitHub Actions Setup

**Workflow Files Location**
```
.github/
├── workflows/
│   ├── ci.yml              # Continuous Integration
│   ├── test.yml            # Comprehensive Testing
│   ├── deploy.yml          # Production Deployment
│   └── monitoring.yml      # Health Monitoring
└── CODEOWNERS             # Code review assignments
```

### Continuous Integration (CI)

**Quality Gates**
1. **Code Quality**: ESLint, Prettier, TypeScript
2. **Testing**: Unit, Integration, E2E tests
3. **Security**: Dependency audit, security scan
4. **Performance**: Bundle size, Lighthouse audit
5. **Build**: Production build verification

**Environment Secrets**

Configure in GitHub Settings → Secrets:
```
# Vercel
VERCEL_TOKEN
VERCEL_ORG_ID
VERCEL_PROJECT_ID

# Supabase
SUPABASE_ACCESS_TOKEN
SUPABASE_PROJECT_ID

# Testing
CYPRESS_RECORD_KEY
PERCY_TOKEN

# Monitoring
SENTRY_AUTH_TOKEN
SLACK_WEBHOOK_URL

# Notifications
DISCORD_WEBHOOK
EMAIL_PASSWORD
```

### Deployment Strategy

**Branching Strategy**
```
main (production)
├── develop (staging)
├── feature/new-calculator
├── fix/calculation-bug
└── hotfix/critical-fix
```

**Deployment Flow**
1. **Feature Branch** → Preview deployment
2. **Develop Branch** → Staging deployment
3. **Main Branch** → Production deployment
4. **Tags** → Versioned releases

### Rollback Procedures

**Automatic Rollback Triggers**
- Health check failures
- High error rates (>5%)
- Performance degradation (>3s response time)
- Critical security alerts

**Manual Rollback**
```bash
# Using Vercel CLI
vercel rollback your-deployment-url

# Using GitHub
git revert HEAD
git push origin main
```

## 📊 Monitoring & Observability

### Application Monitoring

**Vercel Analytics**
- Real User Monitoring (RUM)
- Core Web Vitals tracking
- Geographic performance data
- Custom event tracking

**Sentry Error Tracking**
```typescript
// lib/monitoring/sentry.ts
import * as Sentry from '@sentry/nextjs';

Sentry.init({
  dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,
  environment: process.env.NODE_ENV,
  tracesSampleRate: 0.1,
  profilesSampleRate: 0.1,
  beforeSend(event) {
    // Filter out development errors
    if (event.environment === 'development') {
      return null;
    }
    return event;
  }
});
```

### Infrastructure Monitoring

**Health Checks**
```typescript
// app/api/health/route.ts
export async function GET() {
  const checks = {
    database: await checkDatabase(),
    external_apis: await checkExternalAPIs(),
    memory: process.memoryUsage(),
    uptime: process.uptime()
  };

  const isHealthy = Object.values(checks).every(check => 
    typeof check === 'object' ? check.status === 'healthy' : true
  );

  return Response.json({
    status: isHealthy ? 'healthy' : 'unhealthy',
    timestamp: new Date().toISOString(),
    checks
  }, {
    status: isHealthy ? 200 : 503
  });
}
```

**Uptime Monitoring**
- External monitoring service (UptimeRobot, StatusCake)
- 5-minute intervals
- Multi-location checks
- SMS/email alerts

### Performance Monitoring

**Core Web Vitals Tracking**
```typescript
// lib/performance/web-vitals.ts
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals';

export function reportWebVitals({ id, name, value, label }: any) {
  // Send to analytics
  gtag('event', name, {
    event_category: label === 'web-vital' ? 'Web Vitals' : 'Next.js custom metric',
    value: Math.round(name === 'CLS' ? value * 1000 : value),
    event_label: id,
    non_interaction: true,
  });
}
```

**Performance Budgets**
```json
{
  "performance": {
    "budgets": [
      {
        "path": "/**",
        "timings": [
          { "metric": "first-contentful-paint", "budget": 2000 },
          { "metric": "largest-contentful-paint", "budget": 4000 },
          { "metric": "cumulative-layout-shift", "budget": 100 },
          { "metric": "total-blocking-time", "budget": 300 }
        ],
        "resourceSizes": [
          { "resourceType": "script", "budget": 200 },
          { "resourceType": "total", "budget": 500 }
        ]
      }
    ]
  }
}
```

## 🔒 Security Configuration

### Security Headers

**next.config.js Security Setup**
```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY'
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin'
          },
          {
            key: 'Content-Security-Policy',
            value: "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline' *.google-analytics.com *.googletagmanager.com; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' *.supabase.co *.vercel.app;"
          }
        ]
      }
    ];
  }
};
```

### SSL/TLS Configuration

**Automatic HTTPS**
- Vercel provides automatic HTTPS
- TLS 1.2+ only
- HSTS headers enabled
- Certificate auto-renewal

**Security Scanning**
```bash
# Run security audit
npm audit

# Fix vulnerabilities
npm audit fix

# Check for known vulnerabilities
npm install -g snyk
snyk test
```

### Environment Security

**Secret Management**
- Use Vercel environment variables
- Never commit secrets to Git
- Rotate secrets regularly
- Use different secrets per environment

**Access Control**
- Enable 2FA for all accounts
- Use least privilege principle
- Regular access reviews
- Audit logs monitoring

## 📋 Maintenance Procedures

### Regular Maintenance Tasks

**Weekly Tasks**
- Review error logs and fix issues
- Monitor performance metrics
- Check security vulnerabilities
- Update dependencies (minor versions)

**Monthly Tasks**
- Review and optimize database performance
- Analyze user feedback and feature requests
- Update documentation
- Security audit and penetration testing

**Quarterly Tasks**
- Major dependency updates
- Infrastructure cost optimization
- Disaster recovery testing
- Performance benchmarking

### Update Procedures

**Dependency Updates**
```bash
# Check outdated packages
npm outdated

# Update all dependencies
npm update

# Update major versions carefully
npm install package@latest

# Test after updates
npm run test:all
npm run build
```

**Database Migrations**
```bash
# Create new migration
supabase migration new migration-name

# Test migration locally
supabase db reset

# Deploy to staging
supabase db push --environment staging

# Deploy to production
supabase db push --environment production
```

### Backup and Recovery

**Database Backup Schedule**
- **Daily**: Automated Supabase backups
- **Weekly**: Manual export verification
- **Monthly**: Full system backup test

**Recovery Procedures**
```bash
# Point-in-time recovery (Supabase Pro)
# Available through Supabase Dashboard

# Manual restore from backup
supabase db reset
psql -h your-host -U postgres -d postgres < backup.sql
```

**Disaster Recovery Plan**
1. **Incident Detection**: Automated monitoring alerts
2. **Assessment**: Determine scope and impact
3. **Communication**: Notify stakeholders
4. **Recovery**: Execute recovery procedures
5. **Post-mortem**: Document lessons learned

### Performance Optimization

**Regular Optimization Tasks**
```bash
# Analyze bundle size
npm run analyze

# Run Lighthouse audit
npm run lighthouse

# Check Core Web Vitals
npm run performance:audit

# Optimize images
npm run optimize:images
```

**Database Optimization**
```sql
-- Analyze query performance
EXPLAIN ANALYZE SELECT * FROM projects WHERE user_id = 'user-id';

-- Vacuum and analyze tables
VACUUM ANALYZE projects;
VACUUM ANALYZE calculations;

-- Check index usage
SELECT * FROM pg_stat_user_indexes;
```

## 📞 Incident Response

### Incident Classification

**P0 - Critical**
- Service completely down
- Data loss or corruption
- Security breach
- Response time: 15 minutes

**P1 - High**
- Major feature broken
- Performance severely degraded
- Response time: 1 hour

**P2 - Medium**
- Minor feature issues
- Moderate performance impact
- Response time: 4 hours

**P3 - Low**
- Cosmetic issues
- Documentation updates
- Response time: 24 hours

### Incident Response Process

**1. Detection and Alert**
- Automated monitoring alerts
- User reports
- Internal discovery

**2. Initial Response**
- Acknowledge incident
- Assess severity
- Form response team

**3. Investigation and Resolution**
- Identify root cause
- Implement temporary fix
- Deploy permanent solution

**4. Communication**
- Update status page
- Notify stakeholders
- Document timeline

**5. Post-Incident Review**
- Root cause analysis
- Process improvements
- Documentation updates

### Emergency Contacts

**Team Contacts**
- **DevOps Lead**: +91-XXXX-XXXXX
- **Backend Lead**: +91-XXXX-XXXXX
- **Frontend Lead**: +91-XXXX-XXXXX

**External Services**
- **Vercel Support**: <EMAIL>
- **Supabase Support**: <EMAIL>
- **Domain Provider**: [Your DNS provider]

## 🔍 Troubleshooting

### Common Deployment Issues

**Build Failures**
```bash
# Clear build cache
rm -rf .next/
npm run build

# Check for TypeScript errors
npm run type-check

# Verify environment variables
vercel env ls
```

**Database Connection Issues**
```typescript
// Test database connection
import { supabase } from '@/lib/supabase/client';

const testConnection = async () => {
  try {
    const { data, error } = await supabase.from('projects').select('count');
    if (error) throw error;
    console.log('Database connected successfully');
  } catch (error) {
    console.error('Database connection failed:', error);
  }
};
```

**Performance Issues**
```bash
# Check bundle size
npm run analyze

# Run performance audit
npm run lighthouse

# Monitor Core Web Vitals
# Check Vercel Analytics dashboard
```

### Logging and Debugging

**Production Logging**
```typescript
// lib/logger.ts
export const logger = {
  info: (message: string, meta?: any) => {
    console.log(JSON.stringify({ level: 'info', message, meta, timestamp: new Date().toISOString() }));
  },
  
  error: (message: string, error?: Error, meta?: any) => {
    console.error(JSON.stringify({ 
      level: 'error', 
      message, 
      error: error?.message, 
      stack: error?.stack,
      meta, 
      timestamp: new Date().toISOString() 
    }));
  }
};
```

**Debug Mode**
```bash
# Enable debug mode
export DEBUG=true
npm run start

# Vercel deployment with debug
vercel --debug
```

## 📊 Cost Optimization

### Vercel Usage Monitoring

**Function Execution Time**
- Monitor function duration
- Optimize heavy operations
- Use caching where possible

**Bandwidth Usage**
- Optimize images and assets
- Enable compression
- Use CDN effectively

**Database Costs**
- Monitor connection usage
- Optimize queries
- Use connection pooling

### Cost Optimization Strategies

**1. Caching Strategy**
```typescript
// Implement edge caching
export const revalidate = 3600; // 1 hour

// Use React Query for client caching
const { data } = useQuery({
  queryKey: ['materials'],
  queryFn: fetchMaterials,
  staleTime: 5 * 60 * 1000, // 5 minutes
});
```

**2. Image Optimization**
```typescript
// Use Next.js Image component
import Image from 'next/image';

<Image
  src="/images/hero.jpg"
  alt="Construction"
  width={800}
  height={600}
  priority
  placeholder="blur"
/>
```

**3. Bundle Optimization**
```javascript
// next.config.js
module.exports = {
  experimental: {
    optimizeCss: true,
    modularizeImports: {
      '@heroicons/react/24/outline': {
        transform: '@heroicons/react/24/outline/{{member}}',
      },
    },
  },
};
```

---

## 📞 Support

### Deployment Support

- **Documentation**: This guide and [Vercel docs](https://vercel.com/docs)
- **Issues**: Create GitHub issue for deployment problems
- **Emergency**: Contact DevOps team directly

### External Support

- **Vercel Support**: Available through dashboard
- **Supabase Support**: <EMAIL>
- **Domain Issues**: Contact your DNS provider

---

*This deployment guide is regularly updated to reflect the latest best practices and platform changes. Last updated: July 2025*