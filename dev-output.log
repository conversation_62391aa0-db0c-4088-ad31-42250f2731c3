
> clarity-engine@1.0.0 dev
> next dev

   [1m[38;2;173;127;168m▲ Next.js 15.3.5[39m[22m
   - Local:        http://localhost:3000
   - Network:      http://**************:3000
   - Environments: .env.local

 [32m[1m✓[22m[39m Starting...
 [32m[1m✓[22m[39m Ready in 21.9s
 [37m[1m○[22m[39m Compiling /calculator ...
 [32m[1m✓[22m[39m Compiled /calculator in 22.9s (2431 modules)
🚀 Environment Configuration Loaded:
   Environment: development
   Analytics: ❌
   Error Tracking: ❌
   Performance Monitoring: ✅
   Rate Limit: 100 requests/60000ms
🚀 Environment Configuration Loaded:
   Environment: development
   Analytics: ❌
   Error Tracking: ❌
   Performance Monitoring: ✅
   Rate Limit: 100 requests/60000ms
 GET /calculator [32m200[39m in 25828ms
 [37m[1m○[22m[39m Compiling / ...
 [32m[1m✓[22m[39m Compiled / in 2.3s (1952 modules)
🚀 Environment Configuration Loaded:
   Environment: development
   Analytics: ❌
   Error Tracking: ❌
   Performance Monitoring: ✅
   Rate Limit: 100 requests/60000ms
 GET / [34m307[39m in 2740ms
 [32m[1m✓[22m[39m Compiled /calculator in 397ms (1332 modules)
🚀 Environment Configuration Loaded:
   Environment: development
   Analytics: ❌
   Error Tracking: ❌
   Performance Monitoring: ✅
   Rate Limit: 100 requests/60000ms
🚀 Environment Configuration Loaded:
   Environment: development
   Analytics: ❌
   Error Tracking: ❌
   Performance Monitoring: ✅
   Rate Limit: 100 requests/60000ms
 GET /calculator [32m200[39m in 840ms
 GET / [34m307[39m in 143ms
 GET /calculator [32m200[39m in 152ms
 GET /calculator [32m200[39m in 893ms
 GET /calculator [32m200[39m in 1475ms
 GET /calculator [32m200[39m in 1476ms
 GET /calculator [32m200[39m in 1476ms
 GET /calculator [32m200[39m in 1478ms
 GET /calculator [32m200[39m in 1478ms
 GET /calculator [32m200[39m in 1481ms
 GET /calculator [32m200[39m in 1278ms
 GET /calculator [32m200[39m in 1272ms
 GET /calculator [32m200[39m in 1277ms
 GET /calculator [32m200[39m in 867ms
 GET /calculator [32m200[39m in 446ms
 [37m[1m○[22m[39m Compiling /api/performance/metrics ...
 [32m[1m✓[22m[39m Compiled /api/analytics/web-vitals in 15.1s (2430 modules)
Updating real-time dashboard: {
  metric: [32m'FCP'[39m,
  value: [33m972[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674493407[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'FCP'[39m,
  value: [33m716[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674493609[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'FCP'[39m,
  value: [33m412[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674493970[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'LCP'[39m,
  value: [33m972[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674493409[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'LCP'[39m,
  value: [33m716[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674493613[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'LCP'[39m,
  value: [33m412[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674493974[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'TTFB'[39m,
  value: [33m703.0999999977648[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674493417[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'TTFB'[39m,
  value: [33m526[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674493621[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'TTFB'[39m,
  value: [33m291.6000000014901[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674493995[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'CLS'[39m,
  value: [33m0[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674493419[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'CLS'[39m,
  value: [33m0[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674493624[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'CLS'[39m,
  value: [33m0[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674494000[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
[Web Vitals Analytics] Processed 4 metrics
[Web Vitals Analytics] Processed 4 metrics
[Web Vitals Analytics] Processed 4 metrics
 POST /api/analytics/web-vitals [32m200[39m in 16177ms
 POST /api/analytics/web-vitals [32m200[39m in 15698ms
 POST /api/analytics/web-vitals [32m200[39m in 14923ms
Performance metrics error: SyntaxError: Unexpected end of JSON input
    at JSON.parse (<anonymous>)
    at POST (src/app/api/performance/metrics/route.ts:31:33)
[0m [90m 29 |[39m [36mexport[39m [36masync[39m [36mfunction[39m [33mPOST[39m(request[33m:[39m [33mNextRequest[39m) {[0m
[0m [90m 30 |[39m   [36mtry[39m {[0m
[0m[31m[1m>[22m[39m[90m 31 |[39m       [36mconst[39m body [33m=[39m [36mawait[39m request[33m.[39mjson()[33m;[39m[0m
[0m [90m    |[39m                                 [31m[1m^[22m[39m[0m
[0m [90m 32 |[39m       [36mconst[39m { metric } [33m=[39m body[33m;[39m[0m
[0m [90m 33 |[39m[0m
[0m [90m 34 |[39m       [36mif[39m ([33m![39mmetric [33m||[39m [33m![39mmetric[33m.[39mname [33m||[39m [36mtypeof[39m metric[33m.[39mvalue [33m!==[39m [32m'number'[39m) {[0m
Performance metrics error: SyntaxError: Unexpected end of JSON input
    at JSON.parse (<anonymous>)
    at POST (src/app/api/performance/metrics/route.ts:31:33)
[0m [90m 29 |[39m [36mexport[39m [36masync[39m [36mfunction[39m [33mPOST[39m(request[33m:[39m [33mNextRequest[39m) {[0m
[0m [90m 30 |[39m   [36mtry[39m {[0m
[0m[31m[1m>[22m[39m[90m 31 |[39m       [36mconst[39m body [33m=[39m [36mawait[39m request[33m.[39mjson()[33m;[39m[0m
[0m [90m    |[39m                                 [31m[1m^[22m[39m[0m
[0m [90m 32 |[39m       [36mconst[39m { metric } [33m=[39m body[33m;[39m[0m
[0m [90m 33 |[39m[0m
[0m [90m 34 |[39m       [36mif[39m ([33m![39mmetric [33m||[39m [33m![39mmetric[33m.[39mname [33m||[39m [36mtypeof[39m metric[33m.[39mvalue [33m!==[39m [32m'number'[39m) {[0m
Performance metrics error: SyntaxError: Unexpected end of JSON input
    at JSON.parse (<anonymous>)
    at POST (src/app/api/performance/metrics/route.ts:31:33)
[0m [90m 29 |[39m [36mexport[39m [36masync[39m [36mfunction[39m [33mPOST[39m(request[33m:[39m [33mNextRequest[39m) {[0m
[0m [90m 30 |[39m   [36mtry[39m {[0m
[0m[31m[1m>[22m[39m[90m 31 |[39m       [36mconst[39m body [33m=[39m [36mawait[39m request[33m.[39mjson()[33m;[39m[0m
[0m [90m    |[39m                                 [31m[1m^[22m[39m[0m
[0m [90m 32 |[39m       [36mconst[39m { metric } [33m=[39m body[33m;[39m[0m
[0m [90m 33 |[39m[0m
[0m [90m 34 |[39m       [36mif[39m ([33m![39mmetric [33m||[39m [33m![39mmetric[33m.[39mname [33m||[39m [36mtypeof[39m metric[33m.[39mvalue [33m!==[39m [32m'number'[39m) {[0m
Performance metrics error: SyntaxError: Unexpected end of JSON input
    at JSON.parse (<anonymous>)
    at POST (src/app/api/performance/metrics/route.ts:31:33)
[0m [90m 29 |[39m [36mexport[39m [36masync[39m [36mfunction[39m [33mPOST[39m(request[33m:[39m [33mNextRequest[39m) {[0m
[0m [90m 30 |[39m   [36mtry[39m {[0m
[0m[31m[1m>[22m[39m[90m 31 |[39m       [36mconst[39m body [33m=[39m [36mawait[39m request[33m.[39mjson()[33m;[39m[0m
[0m [90m    |[39m                                 [31m[1m^[22m[39m[0m
[0m [90m 32 |[39m       [36mconst[39m { metric } [33m=[39m body[33m;[39m[0m
[0m [90m 33 |[39m[0m
[0m [90m 34 |[39m       [36mif[39m ([33m![39mmetric [33m||[39m [33m![39mmetric[33m.[39mname [33m||[39m [36mtypeof[39m metric[33m.[39mvalue [33m!==[39m [32m'number'[39m) {[0m
Performance metrics error: SyntaxError: Unexpected end of JSON input
    at JSON.parse (<anonymous>)
    at POST (src/app/api/performance/metrics/route.ts:31:33)
[0m [90m 29 |[39m [36mexport[39m [36masync[39m [36mfunction[39m [33mPOST[39m(request[33m:[39m [33mNextRequest[39m) {[0m
[0m [90m 30 |[39m   [36mtry[39m {[0m
[0m[31m[1m>[22m[39m[90m 31 |[39m       [36mconst[39m body [33m=[39m [36mawait[39m request[33m.[39mjson()[33m;[39m[0m
[0m [90m    |[39m                                 [31m[1m^[22m[39m[0m
[0m [90m 32 |[39m       [36mconst[39m { metric } [33m=[39m body[33m;[39m[0m
[0m [90m 33 |[39m[0m
[0m [90m 34 |[39m       [36mif[39m ([33m![39mmetric [33m||[39m [33m![39mmetric[33m.[39mname [33m||[39m [36mtypeof[39m metric[33m.[39mvalue [33m!==[39m [32m'number'[39m) {[0m
Performance metrics error: SyntaxError: Unexpected end of JSON input
    at JSON.parse (<anonymous>)
    at POST (src/app/api/performance/metrics/route.ts:31:33)
[0m [90m 29 |[39m [36mexport[39m [36masync[39m [36mfunction[39m [33mPOST[39m(request[33m:[39m [33mNextRequest[39m) {[0m
[0m [90m 30 |[39m   [36mtry[39m {[0m
[0m[31m[1m>[22m[39m[90m 31 |[39m       [36mconst[39m body [33m=[39m [36mawait[39m request[33m.[39mjson()[33m;[39m[0m
[0m [90m    |[39m                                 [31m[1m^[22m[39m[0m
[0m [90m 32 |[39m       [36mconst[39m { metric } [33m=[39m body[33m;[39m[0m
[0m [90m 33 |[39m[0m
[0m [90m 34 |[39m       [36mif[39m ([33m![39mmetric [33m||[39m [33m![39mmetric[33m.[39mname [33m||[39m [36mtypeof[39m metric[33m.[39mvalue [33m!==[39m [32m'number'[39m) {[0m
Performance metrics error: SyntaxError: Unexpected end of JSON input
    at JSON.parse (<anonymous>)
    at POST (src/app/api/performance/metrics/route.ts:31:33)
[0m [90m 29 |[39m [36mexport[39m [36masync[39m [36mfunction[39m [33mPOST[39m(request[33m:[39m [33mNextRequest[39m) {[0m
[0m [90m 30 |[39m   [36mtry[39m {[0m
[0m[31m[1m>[22m[39m[90m 31 |[39m       [36mconst[39m body [33m=[39m [36mawait[39m request[33m.[39mjson()[33m;[39m[0m
[0m [90m    |[39m                                 [31m[1m^[22m[39m[0m
[0m [90m 32 |[39m       [36mconst[39m { metric } [33m=[39m body[33m;[39m[0m
[0m [90m 33 |[39m[0m
[0m [90m 34 |[39m       [36mif[39m ([33m![39mmetric [33m||[39m [33m![39mmetric[33m.[39mname [33m||[39m [36mtypeof[39m metric[33m.[39mvalue [33m!==[39m [32m'number'[39m) {[0m
Performance metrics error: SyntaxError: Unexpected end of JSON input
    at JSON.parse (<anonymous>)
    at POST (src/app/api/performance/metrics/route.ts:31:33)
[0m [90m 29 |[39m [36mexport[39m [36masync[39m [36mfunction[39m [33mPOST[39m(request[33m:[39m [33mNextRequest[39m) {[0m
[0m [90m 30 |[39m   [36mtry[39m {[0m
[0m[31m[1m>[22m[39m[90m 31 |[39m       [36mconst[39m body [33m=[39m [36mawait[39m request[33m.[39mjson()[33m;[39m[0m
[0m [90m    |[39m                                 [31m[1m^[22m[39m[0m
[0m [90m 32 |[39m       [36mconst[39m { metric } [33m=[39m body[33m;[39m[0m
[0m [90m 33 |[39m[0m
[0m [90m 34 |[39m       [36mif[39m ([33m![39mmetric [33m||[39m [33m![39mmetric[33m.[39mname [33m||[39m [36mtypeof[39m metric[33m.[39mvalue [33m!==[39m [32m'number'[39m) {[0m
Performance metrics error: SyntaxError: Unexpected end of JSON input
    at JSON.parse (<anonymous>)
    at POST (src/app/api/performance/metrics/route.ts:31:33)
[0m [90m 29 |[39m [36mexport[39m [36masync[39m [36mfunction[39m [33mPOST[39m(request[33m:[39m [33mNextRequest[39m) {[0m
[0m [90m 30 |[39m   [36mtry[39m {[0m
[0m[31m[1m>[22m[39m[90m 31 |[39m       [36mconst[39m body [33m=[39m [36mawait[39m request[33m.[39mjson()[33m;[39m[0m
[0m [90m    |[39m                                 [31m[1m^[22m[39m[0m
[0m [90m 32 |[39m       [36mconst[39m { metric } [33m=[39m body[33m;[39m[0m
[0m [90m 33 |[39m[0m
[0m [90m 34 |[39m       [36mif[39m ([33m![39mmetric [33m||[39m [33m![39mmetric[33m.[39mname [33m||[39m [36mtypeof[39m metric[33m.[39mvalue [33m!==[39m [32m'number'[39m) {[0m
Performance metrics error: SyntaxError: Unexpected end of JSON input
    at JSON.parse (<anonymous>)
    at POST (src/app/api/performance/metrics/route.ts:31:33)
[0m [90m 29 |[39m [36mexport[39m [36masync[39m [36mfunction[39m [33mPOST[39m(request[33m:[39m [33mNextRequest[39m) {[0m
[0m [90m 30 |[39m   [36mtry[39m {[0m
[0m[31m[1m>[22m[39m[90m 31 |[39m       [36mconst[39m body [33m=[39m [36mawait[39m request[33m.[39mjson()[33m;[39m[0m
[0m [90m    |[39m                                 [31m[1m^[22m[39m[0m
[0m [90m 32 |[39m       [36mconst[39m { metric } [33m=[39m body[33m;[39m[0m
[0m [90m 33 |[39m[0m
[0m [90m 34 |[39m       [36mif[39m ([33m![39mmetric [33m||[39m [33m![39mmetric[33m.[39mname [33m||[39m [36mtypeof[39m metric[33m.[39mvalue [33m!==[39m [32m'number'[39m) {[0m
Performance metrics error: SyntaxError: Unexpected end of JSON input
    at JSON.parse (<anonymous>)
    at POST (src/app/api/performance/metrics/route.ts:31:33)
[0m [90m 29 |[39m [36mexport[39m [36masync[39m [36mfunction[39m [33mPOST[39m(request[33m:[39m [33mNextRequest[39m) {[0m
[0m [90m 30 |[39m   [36mtry[39m {[0m
[0m[31m[1m>[22m[39m[90m 31 |[39m       [36mconst[39m body [33m=[39m [36mawait[39m request[33m.[39mjson()[33m;[39m[0m
[0m [90m    |[39m                                 [31m[1m^[22m[39m[0m
[0m [90m 32 |[39m       [36mconst[39m { metric } [33m=[39m body[33m;[39m[0m
[0m [90m 33 |[39m[0m
[0m [90m 34 |[39m       [36mif[39m ([33m![39mmetric [33m||[39m [33m![39mmetric[33m.[39mname [33m||[39m [36mtypeof[39m metric[33m.[39mvalue [33m!==[39m [32m'number'[39m) {[0m
Performance metrics error: SyntaxError: Unexpected end of JSON input
    at JSON.parse (<anonymous>)
    at POST (src/app/api/performance/metrics/route.ts:31:33)
[0m [90m 29 |[39m [36mexport[39m [36masync[39m [36mfunction[39m [33mPOST[39m(request[33m:[39m [33mNextRequest[39m) {[0m
[0m [90m 30 |[39m   [36mtry[39m {[0m
[0m[31m[1m>[22m[39m[90m 31 |[39m       [36mconst[39m body [33m=[39m [36mawait[39m request[33m.[39mjson()[33m;[39m[0m
[0m [90m    |[39m                                 [31m[1m^[22m[39m[0m
[0m [90m 32 |[39m       [36mconst[39m { metric } [33m=[39m body[33m;[39m[0m
[0m [90m 33 |[39m[0m
[0m [90m 34 |[39m       [36mif[39m ([33m![39mmetric [33m||[39m [33m![39mmetric[33m.[39mname [33m||[39m [36mtypeof[39m metric[33m.[39mvalue [33m!==[39m [32m'number'[39m) {[0m
[Performance Metric] FCP: 972ms
[Performance Metric] LCP: 972ms
[Performance Metric] LCP: 972ms
[Performance Metric] FCP: 972ms
[Performance Metric] LCP: 972ms
[Performance Metric] LCP: 972ms
[Performance Metric] FCP: 972ms
[Performance Metric] CLS: 0
[Performance Metric] TTFB: 700.1999999992549ms
[Performance Metric] CLS: 0
[Performance Metric] FCP: 972ms
[Performance Metric] LCP: 972ms
[Performance Metric] TTFB: 701.5999999977648ms
[Performance Metric] TTFB: 703.0999999977648ms
[Performance Metric] CLS: 0
[Performance Metric] CLS: 0
[Performance Metric] TTFB: 699.5ms
[Performance Metric] LCP: 864ms
[Performance Metric] TTFB: 702.3000000007451ms
[Performance Metric] CLS: 0
[Performance Metric] FCP: 964ms
[Performance Metric] LCP: 964ms
[Performance Metric] FCP: 864ms
[Performance Metric] FCP: 1160ms
[Performance Metric] LCP: 1160ms
[Performance Metric] CLS: 0
[Performance Metric] TTFB: 905ms
[Performance Alert] WARNING: TTFB needs improvement: 905ms
[Performance Metric] CLS: 0
[Performance Metric] TTFB: 613.3999999985099ms
[Performance Metric] TTFB: 717.5ms
[Performance Metric] CLS: 0
[Performance Metric] FCP: 716ms
[Performance Metric] LCP: 716ms
[Performance Metric] TTFB: 526ms
[Performance Metric] CLS: 0
[Performance Metric] FCP: 412ms
[Performance Metric] TTFB: 291.6000000014901ms
[Performance Metric] CLS: 0
[Performance Metric] LCP: 412ms
[Performance Metric] CLS: 0.047340450816684296
[Performance Metric] CLS: 0.047340450816684296
[Performance Metric] CLS: 0.047340450816684296
[Performance Metric] CLS: 0.047340450816684296
[Performance Metric] CLS: 0.047340450816684296
[Performance Metric] CLS: 0.047340450816684296
[Performance Metric] CLS: 0.047340450816684296
[Performance Metric] INP: 304ms
[Performance Alert] WARNING: INP needs improvement: 304ms
[Performance Metric] INP: 248ms
[Performance Alert] WARNING: INP needs improvement: 248ms
[Performance Metric] INP: 288ms
[Performance Alert] WARNING: INP needs improvement: 288ms
[Performance Metric] INP: 232ms
[Performance Alert] WARNING: INP needs improvement: 232ms
[Performance Metric] INP: 224ms
[Performance Alert] WARNING: INP needs improvement: 224ms
[Performance Metric] INP: 264ms
[Performance Alert] WARNING: INP needs improvement: 264ms
[Performance Metric] INP: 288ms
[Performance Alert] WARNING: INP needs improvement: 288ms
[Performance Metric] FCP: 972ms
 POST /api/performance/metrics [32m200[39m in 17268ms
 POST /api/performance/metrics [32m200[39m in 17268ms
 POST /api/performance/metrics [32m200[39m in 17268ms
 POST /api/performance/metrics [32m200[39m in 17268ms
 POST /api/performance/metrics [32m200[39m in 17269ms
 POST /api/performance/metrics [32m200[39m in 17269ms
 POST /api/performance/metrics [32m200[39m in 17269ms
 POST /api/performance/metrics [32m200[39m in 17268ms
 POST /api/performance/metrics [32m200[39m in 17269ms
 POST /api/performance/metrics [32m200[39m in 17269ms
 POST /api/performance/metrics [32m200[39m in 17268ms
 POST /api/performance/metrics [32m200[39m in 17268ms
 POST /api/performance/metrics [32m200[39m in 17268ms
 POST /api/performance/metrics [32m200[39m in 17260ms
 POST /api/performance/metrics [32m200[39m in 17258ms
 POST /api/performance/metrics [32m200[39m in 17190ms
 POST /api/performance/metrics [32m200[39m in 17189ms
 POST /api/performance/metrics [32m200[39m in 17183ms
 POST /api/performance/metrics [32m200[39m in 17183ms
 POST /api/performance/metrics [32m200[39m in 17183ms
 POST /api/performance/metrics [32m200[39m in 17183ms
 POST /api/performance/metrics [32m200[39m in 17184ms
 POST /api/performance/metrics [32m200[39m in 17182ms
 POST /api/performance/metrics [32m200[39m in 17174ms
 POST /api/performance/metrics [32m200[39m in 17159ms
 POST /api/performance/metrics [32m200[39m in 17158ms
 POST /api/performance/metrics [32m200[39m in 17067ms
 POST /api/performance/metrics [32m200[39m in 17068ms
 POST /api/performance/metrics [32m200[39m in 17067ms
 POST /api/performance/metrics [32m200[39m in 17061ms
 POST /api/performance/metrics [32m200[39m in 16767ms
 POST /api/performance/metrics [32m200[39m in 16759ms
 POST /api/performance/metrics [32m200[39m in 16756ms
 POST /api/performance/metrics [32m200[39m in 16748ms
 POST /api/performance/metrics [32m200[39m in 16452ms
 POST /api/performance/metrics [32m200[39m in 16452ms
 POST /api/performance/metrics [32m200[39m in 16453ms
 POST /api/performance/metrics [32m200[39m in 16452ms
 POST /api/performance/metrics [32m200[39m in 16387ms
 POST /api/performance/metrics [32m200[39m in 15913ms
 POST /api/performance/metrics [32m200[39m in 15874ms
 POST /api/performance/metrics [32m200[39m in 15831ms
 POST /api/performance/metrics [32m200[39m in 15831ms
 POST /api/performance/metrics [32m200[39m in 15108ms
 POST /api/performance/metrics [32m200[39m in 15090ms
 POST /api/performance/metrics [32m200[39m in 17279ms
 POST /api/performance/metrics [32m200[39m in 17124ms
 POST /api/performance/metrics [32m200[39m in 14582ms
 POST /api/performance/metrics [32m200[39m in 14563ms
 POST /api/performance/metrics [32m200[39m in 14510ms
 POST /api/performance/metrics [32m200[39m in 14510ms
 POST /api/performance/metrics [32m200[39m in 14510ms
 POST /api/performance/metrics [32m200[39m in 14482ms
 POST /api/performance/metrics [32m200[39m in 14469ms
🚀 Environment Configuration Loaded:
   Environment: development
   Analytics: ❌
   Error Tracking: ❌
   Performance Monitoring: ✅
   Rate Limit: 100 requests/60000ms
🚀 Environment Configuration Loaded:
   Environment: development
   Analytics: ❌
   Error Tracking: ❌
   Performance Monitoring: ✅
   Rate Limit: 100 requests/60000ms
 [33m[1m⚠[22m[39m Fast Refresh had to perform a full reload. Read more: https://nextjs.org/docs/messages/fast-refresh-reload
 [33m[1m⚠[22m[39m Fast Refresh had to perform a full reload. Read more: https://nextjs.org/docs/messages/fast-refresh-reload
 [33m[1m⚠[22m[39m Fast Refresh had to perform a full reload. Read more: https://nextjs.org/docs/messages/fast-refresh-reload
Updating real-time dashboard: {
  metric: [32m'FCP'[39m,
  value: [33m972[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674493396[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'LCP'[39m,
  value: [33m972[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674493399[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'TTFB'[39m,
  value: [33m699.5[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674493455[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'CLS'[39m,
  value: [33m0[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674493456[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'CLS'[39m,
  value: [33m0.047340450816684296[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674494423[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
[Performance Alert] WARNING: INP needs improvement: 224ms
Updating real-time dashboard: {
  metric: [32m'INP'[39m,
  value: [33m224[39m,
  rating: [32m'needs-improvement'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674495370[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
[Web Vitals Analytics] Processed 6 metrics
 POST /api/analytics/web-vitals [32m200[39m in 391ms
Performance metrics error: SyntaxError: Unexpected end of JSON input
    at JSON.parse (<anonymous>)
    at POST (src/app/api/performance/metrics/route.ts:31:33)
[0m [90m 29 |[39m [36mexport[39m [36masync[39m [36mfunction[39m [33mPOST[39m(request[33m:[39m [33mNextRequest[39m) {[0m
[0m [90m 30 |[39m   [36mtry[39m {[0m
[0m[31m[1m>[22m[39m[90m 31 |[39m       [36mconst[39m body [33m=[39m [36mawait[39m request[33m.[39mjson()[33m;[39m[0m
[0m [90m    |[39m                                 [31m[1m^[22m[39m[0m
[0m [90m 32 |[39m       [36mconst[39m { metric } [33m=[39m body[33m;[39m[0m
[0m [90m 33 |[39m[0m
[0m [90m 34 |[39m       [36mif[39m ([33m![39mmetric [33m||[39m [33m![39mmetric[33m.[39mname [33m||[39m [36mtypeof[39m metric[33m.[39mvalue [33m!==[39m [32m'number'[39m) {[0m
Web vitals analytics error: SyntaxError: Unexpected end of JSON input
    at JSON.parse (<anonymous>)
    at POST (src/app/api/analytics/web-vitals/route.ts:38:33)
[0m [90m 36 |[39m [36mexport[39m [36masync[39m [36mfunction[39m [33mPOST[39m(request[33m:[39m [33mNextRequest[39m) {[0m
[0m [90m 37 |[39m   [36mtry[39m {[0m
[0m[31m[1m>[22m[39m[90m 38 |[39m       [36mconst[39m body [33m=[39m [36mawait[39m request[33m.[39mjson()[33m;[39m[0m
[0m [90m    |[39m                                 [31m[1m^[22m[39m[0m
[0m [90m 39 |[39m       [36mconst[39m { metrics } [33m=[39m body[33m;[39m[0m
[0m [90m 40 |[39m[0m
[0m [90m 41 |[39m       [36mif[39m ([33m![39mmetrics [33m||[39m [33m![39m[33mArray[39m[33m.[39misArray(metrics)) {[0m
Updating real-time dashboard: {
  metric: [32m'FCP'[39m,
  value: [33m972[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674493396[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'FCP'[39m,
  value: [33m972[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674493398[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'FCP'[39m,
  value: [33m864[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674493463[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'FCP'[39m,
  value: [33m972[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674493398[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'FCP'[39m,
  value: [33m1160[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674493495[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'FCP'[39m,
  value: [33m964[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674493484[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'LCP'[39m,
  value: [33m972[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674493399[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'LCP'[39m,
  value: [33m972[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674493402[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'LCP'[39m,
  value: [33m864[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674493465[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'LCP'[39m,
  value: [33m972[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674493402[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'LCP'[39m,
  value: [33m1160[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674493498[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'LCP'[39m,
  value: [33m964[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674493489[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'TTFB'[39m,
  value: [33m701.5999999977648[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674493406[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'TTFB'[39m,
  value: [33m702.3000000007451[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674493466[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'TTFB'[39m,
  value: [33m613.3999999985099[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674493556[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'TTFB'[39m,
  value: [33m700.1999999992549[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674493409[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
[Performance Alert] WARNING: TTFB needs improvement: 905ms
Updating real-time dashboard: {
  metric: [32m'TTFB'[39m,
  value: [33m717.5[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674493570[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'TTFB'[39m,
  value: [33m905[39m,
  rating: [32m'needs-improvement'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674493502[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'CLS'[39m,
  value: [33m0[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674493408[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'CLS'[39m,
  value: [33m0[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674493467[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'CLS'[39m,
  value: [33m0[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674493561[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'CLS'[39m,
  value: [33m0[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674493410[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'CLS'[39m,
  value: [33m0[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674493572[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'CLS'[39m,
  value: [33m0[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674493503[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'CLS'[39m,
  value: [33m0.047340450816684296[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674494123[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'CLS'[39m,
  value: [33m0.047340450816684296[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674494031[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'CLS'[39m,
  value: [33m0.047340450816684296[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674494627[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'CLS'[39m,
  value: [33m0.047340450816684296[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674494087[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'CLS'[39m,
  value: [33m0.047340450816684296[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674494475[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'CLS'[39m,
  value: [33m0.047340450816684296[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674494344[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
[Performance Alert] WARNING: INP needs improvement: 264ms
[Performance Alert] WARNING: INP needs improvement: 232ms
[Performance Alert] WARNING: INP needs improvement: 248ms
[Performance Alert] WARNING: INP needs improvement: 288ms
[Performance Alert] WARNING: INP needs improvement: 304ms
Updating real-time dashboard: {
  metric: [32m'INP'[39m,
  value: [33m264[39m,
  rating: [32m'needs-improvement'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674495012[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'INP'[39m,
  value: [33m232[39m,
  rating: [32m'needs-improvement'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674494925[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'INP'[39m,
  value: [33m248[39m,
  rating: [32m'needs-improvement'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674495288[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'INP'[39m,
  value: [33m288[39m,
  rating: [32m'needs-improvement'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674495155[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
[Performance Alert] WARNING: INP needs improvement: 288ms
Updating real-time dashboard: {
  metric: [32m'INP'[39m,
  value: [33m304[39m,
  rating: [32m'needs-improvement'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674495158[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'INP'[39m,
  value: [33m288[39m,
  rating: [32m'needs-improvement'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674495375[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
[Web Vitals Analytics] Processed 6 metrics
[Web Vitals Analytics] Processed 6 metrics
[Web Vitals Analytics] Processed 6 metrics
[Web Vitals Analytics] Processed 6 metrics
[Web Vitals Analytics] Processed 6 metrics
[Web Vitals Analytics] Processed 6 metrics
 POST /api/analytics/web-vitals [32m200[39m in 687ms
 POST /api/analytics/web-vitals [32m200[39m in 687ms
 POST /api/analytics/web-vitals [32m200[39m in 688ms
 POST /api/analytics/web-vitals [32m200[39m in 688ms
 POST /api/analytics/web-vitals [32m200[39m in 671ms
 POST /api/analytics/web-vitals [32m200[39m in 688ms
 GET /calculator [32m200[39m in 18361ms
 GET /calculator [32m200[39m in 17107ms
 GET /calculator [32m200[39m in 17830ms
[Performance Metric] FCP: 17180ms
[Performance Alert] ERROR: FCP is critically slow: 17180ms
 POST /api/performance/metrics [32m200[39m in 170ms
[Performance Metric] LCP: 17180ms
[Performance Alert] ERROR: LCP is critically slow: 17180ms
[Performance Metric] FCP: 17748ms
[Performance Alert] ERROR: FCP is critically slow: 17748ms
[Performance Metric] LCP: 17748ms
[Performance Alert] ERROR: LCP is critically slow: 17748ms
[Performance Metric] FCP: 17876ms
[Performance Alert] ERROR: FCP is critically slow: 17876ms
[Performance Metric] LCP: 17876ms
[Performance Alert] ERROR: LCP is critically slow: 17876ms
[Performance Metric] TTFB: 17024.79999999702ms
[Performance Alert] ERROR: TTFB is critically slow: 17024.79999999702ms
[Performance Metric] CLS: 0
[Performance Metric] TTFB: 17576.699999999255ms
[Performance Alert] ERROR: TTFB is critically slow: 17576.699999999255ms
[Performance Metric] CLS: 0
[Performance Metric] TTFB: 17717.39999999851ms
[Performance Alert] ERROR: TTFB is critically slow: 17717.39999999851ms
[Performance Metric] CLS: 0
 POST /api/performance/metrics [32m200[39m in 225ms
 POST /api/performance/metrics [32m200[39m in 214ms
 POST /api/performance/metrics [32m200[39m in 214ms
 POST /api/performance/metrics [32m200[39m in 255ms
 POST /api/performance/metrics [32m200[39m in 245ms
 POST /api/performance/metrics [32m200[39m in 244ms
 POST /api/performance/metrics [32m200[39m in 230ms
 POST /api/performance/metrics [32m200[39m in 230ms
 POST /api/performance/metrics [32m200[39m in 230ms
 POST /api/performance/metrics [32m200[39m in 218ms
 POST /api/performance/metrics [32m200[39m in 216ms
[Performance Metric] CLS: 0.027380037117872846
 POST /api/performance/metrics [32m200[39m in 113ms
[Performance Metric] CLS: 0.047340450816684296
[Performance Metric] CLS: 0.027380037117872846
 POST /api/performance/metrics [32m200[39m in 147ms
 POST /api/performance/metrics [32m200[39m in 142ms
[Performance Metric] INP: 56ms
 POST /api/performance/metrics [32m200[39m in 115ms
 GET /calculator [32m200[39m in 219ms
 GET /calculator [32m200[39m in 192ms
[Performance Metric] FCP: 296ms
 POST /api/performance/metrics [32m200[39m in 129ms
[Performance Metric] LCP: 296ms
 POST /api/performance/metrics [32m200[39m in 195ms
[Performance Metric] TTFB: 188.89999999850988ms
[Performance Metric] CLS: 0
 POST /api/performance/metrics [32m200[39m in 186ms
 POST /api/performance/metrics [32m200[39m in 187ms
[Performance Metric] CLS: 0.047340450816684296
 POST /api/performance/metrics [32m200[39m in 101ms
[Performance Metric] INP: 72ms
 POST /api/performance/metrics [32m200[39m in 82ms
[Performance Metric] LCP: 264ms
 POST /api/performance/metrics [32m200[39m in 118ms
[Performance Metric] FCP: 264ms
[Performance Metric] TTFB: 169.80000000074506ms
[Performance Metric] CLS: 0
 POST /api/performance/metrics [32m200[39m in 176ms
 POST /api/performance/metrics [32m200[39m in 151ms
 POST /api/performance/metrics [32m200[39m in 152ms
[Performance Metric] CLS: 0.047340450816684296
 POST /api/performance/metrics [32m200[39m in 90ms
[Performance Metric] INP: 56ms
 POST /api/performance/metrics [32m200[39m in 88ms
 GET /calculator [32m200[39m in 300ms
 GET /calculator [32m200[39m in 339ms
[Performance Metric] FCP: 384ms
[Performance Metric] LCP: 384ms
[Performance Metric] FCP: 412ms
[Performance Metric] LCP: 412ms
[Performance Metric] TTFB: 200.09999999776483ms
[Performance Metric] CLS: 0
 POST /api/performance/metrics [32m200[39m in 220ms
 POST /api/performance/metrics [32m200[39m in 219ms
 POST /api/performance/metrics [32m200[39m in 206ms
 POST /api/performance/metrics [32m200[39m in 206ms
 POST /api/performance/metrics [32m200[39m in 196ms
 POST /api/performance/metrics [32m200[39m in 196ms
Updating real-time dashboard: {
  metric: [32m'FCP'[39m,
  value: [33m384[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674518735[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'LCP'[39m,
  value: [33m384[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674518736[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
[Web Vitals Analytics] Processed 2 metrics
 POST /api/analytics/web-vitals [32m200[39m in 220ms
[Performance Metric] CLS: 0
 POST /api/performance/metrics [32m200[39m in 267ms
Updating real-time dashboard: {
  metric: [32m'FCP'[39m,
  value: [33m412[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674518752[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'LCP'[39m,
  value: [33m412[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674518753[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
[Web Vitals Analytics] Processed 2 metrics
 POST /api/analytics/web-vitals [32m200[39m in 420ms
 POST /api/performance/metrics [32m200[39m in 411ms
Performance metrics error: SyntaxError: Unexpected end of JSON input
    at JSON.parse (<anonymous>)
    at POST (src/app/api/performance/metrics/route.ts:31:33)
[0m [90m 29 |[39m [36mexport[39m [36masync[39m [36mfunction[39m [33mPOST[39m(request[33m:[39m [33mNextRequest[39m) {[0m
[0m [90m 30 |[39m   [36mtry[39m {[0m
[0m[31m[1m>[22m[39m[90m 31 |[39m       [36mconst[39m body [33m=[39m [36mawait[39m request[33m.[39mjson()[33m;[39m[0m
[0m [90m    |[39m                                 [31m[1m^[22m[39m[0m
[0m [90m 32 |[39m       [36mconst[39m { metric } [33m=[39m body[33m;[39m[0m
[0m [90m 33 |[39m[0m
[0m [90m 34 |[39m       [36mif[39m ([33m![39mmetric [33m||[39m [33m![39mmetric[33m.[39mname [33m||[39m [36mtypeof[39m metric[33m.[39mvalue [33m!==[39m [32m'number'[39m) {[0m
Performance metrics error: SyntaxError: Unexpected end of JSON input
    at JSON.parse (<anonymous>)
    at POST (src/app/api/performance/metrics/route.ts:31:33)
[0m [90m 29 |[39m [36mexport[39m [36masync[39m [36mfunction[39m [33mPOST[39m(request[33m:[39m [33mNextRequest[39m) {[0m
[0m [90m 30 |[39m   [36mtry[39m {[0m
[0m[31m[1m>[22m[39m[90m 31 |[39m       [36mconst[39m body [33m=[39m [36mawait[39m request[33m.[39mjson()[33m;[39m[0m
[0m [90m    |[39m                                 [31m[1m^[22m[39m[0m
[0m [90m 32 |[39m       [36mconst[39m { metric } [33m=[39m body[33m;[39m[0m
[0m [90m 33 |[39m[0m
[0m [90m 34 |[39m       [36mif[39m ([33m![39mmetric [33m||[39m [33m![39mmetric[33m.[39mname [33m||[39m [36mtypeof[39m metric[33m.[39mvalue [33m!==[39m [32m'number'[39m) {[0m
Performance metrics error: SyntaxError: Unexpected end of JSON input
    at JSON.parse (<anonymous>)
    at POST (src/app/api/performance/metrics/route.ts:31:33)
[0m [90m 29 |[39m [36mexport[39m [36masync[39m [36mfunction[39m [33mPOST[39m(request[33m:[39m [33mNextRequest[39m) {[0m
[0m [90m 30 |[39m   [36mtry[39m {[0m
[0m[31m[1m>[22m[39m[90m 31 |[39m       [36mconst[39m body [33m=[39m [36mawait[39m request[33m.[39mjson()[33m;[39m[0m
[0m [90m    |[39m                                 [31m[1m^[22m[39m[0m
[0m [90m 32 |[39m       [36mconst[39m { metric } [33m=[39m body[33m;[39m[0m
[0m [90m 33 |[39m[0m
[0m [90m 34 |[39m       [36mif[39m ([33m![39mmetric [33m||[39m [33m![39mmetric[33m.[39mname [33m||[39m [36mtypeof[39m metric[33m.[39mvalue [33m!==[39m [32m'number'[39m) {[0m
 GET /calculator [32m200[39m in 672ms
 GET /calculator [32m200[39m in 637ms
[Performance Metric] FCP: 632ms
 POST /api/performance/metrics [32m200[39m in 106ms
[Performance Metric] LCP: 632ms
[Performance Metric] FCP: 584ms
[Performance Metric] LCP: 584ms
[Performance Metric] TTFB: 469.8999999985099ms
[Performance Metric] CLS: 0
 POST /api/performance/metrics [32m200[39m in 227ms
 POST /api/performance/metrics [32m200[39m in 228ms
 POST /api/performance/metrics [32m200[39m in 227ms
 POST /api/performance/metrics [32m200[39m in 211ms
 POST /api/performance/metrics [32m200[39m in 211ms
[Performance Metric] CLS: 0
[Performance Metric] TTFB: 453.1000000014901ms
 POST /api/performance/metrics [32m200[39m in 274ms
 POST /api/performance/metrics [32m200[39m in 274ms
 POST /api/performance/metrics [32m200[39m in 167ms
Performance metrics error: SyntaxError: Unexpected end of JSON input
    at JSON.parse (<anonymous>)
    at POST (src/app/api/performance/metrics/route.ts:31:33)
[0m [90m 29 |[39m [36mexport[39m [36masync[39m [36mfunction[39m [33mPOST[39m(request[33m:[39m [33mNextRequest[39m) {[0m
[0m [90m 30 |[39m   [36mtry[39m {[0m
[0m[31m[1m>[22m[39m[90m 31 |[39m       [36mconst[39m body [33m=[39m [36mawait[39m request[33m.[39mjson()[33m;[39m[0m
[0m [90m    |[39m                                 [31m[1m^[22m[39m[0m
[0m [90m 32 |[39m       [36mconst[39m { metric } [33m=[39m body[33m;[39m[0m
[0m [90m 33 |[39m[0m
[0m [90m 34 |[39m       [36mif[39m ([33m![39mmetric [33m||[39m [33m![39mmetric[33m.[39mname [33m||[39m [36mtypeof[39m metric[33m.[39mvalue [33m!==[39m [32m'number'[39m) {[0m
[Performance Metric] INP: 56ms
 POST /api/performance/metrics [32m200[39m in 342ms
Updating real-time dashboard: {
  metric: [32m'FCP'[39m,
  value: [33m296[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674515833[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'LCP'[39m,
  value: [33m296[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674515834[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'TTFB'[39m,
  value: [33m188.89999999850988[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674515874[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'CLS'[39m,
  value: [33m0[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674515879[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'CLS'[39m,
  value: [33m0.047340450816684296[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674516025[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'INP'[39m,
  value: [33m72[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674516280[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
[Web Vitals Analytics] Processed 6 metrics
 POST /api/analytics/web-vitals [32m200[39m in 90ms
Updating real-time dashboard: {
  metric: [32m'FCP'[39m,
  value: [33m264[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674516446[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'LCP'[39m,
  value: [33m264[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674516447[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'TTFB'[39m,
  value: [33m169.80000000074506[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674516470[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'CLS'[39m,
  value: [33m0[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674516472[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'CLS'[39m,
  value: [33m0.047340450816684296[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674516693[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'INP'[39m,
  value: [33m56[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674516880[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
[Web Vitals Analytics] Processed 6 metrics
 POST /api/analytics/web-vitals [32m200[39m in 79ms
 GET /calculator [32m200[39m in 166ms
[Performance Metric] FCP: 264ms
 POST /api/performance/metrics [32m200[39m in 79ms
[Performance Metric] LCP: 264ms
 POST /api/performance/metrics [32m200[39m in 133ms
Updating real-time dashboard: {
  metric: [32m'FCP'[39m,
  value: [33m264[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674522289[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'LCP'[39m,
  value: [33m264[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674522290[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
[Web Vitals Analytics] Processed 2 metrics
 POST /api/analytics/web-vitals [32m200[39m in 138ms
[Performance Metric] TTFB: 146.89999999850988ms
[Performance Metric] CLS: 0
 POST /api/performance/metrics [32m200[39m in 149ms
 POST /api/performance/metrics [32m200[39m in 149ms
 GET /calculator [32m200[39m in 321ms
Performance metrics error: SyntaxError: Unexpected end of JSON input
    at JSON.parse (<anonymous>)
    at POST (src/app/api/performance/metrics/route.ts:31:33)
[0m [90m 29 |[39m [36mexport[39m [36masync[39m [36mfunction[39m [33mPOST[39m(request[33m:[39m [33mNextRequest[39m) {[0m
[0m [90m 30 |[39m   [36mtry[39m {[0m
[0m[31m[1m>[22m[39m[90m 31 |[39m       [36mconst[39m body [33m=[39m [36mawait[39m request[33m.[39mjson()[33m;[39m[0m
[0m [90m    |[39m                                 [31m[1m^[22m[39m[0m
[0m [90m 32 |[39m       [36mconst[39m { metric } [33m=[39m body[33m;[39m[0m
[0m [90m 33 |[39m[0m
[0m [90m 34 |[39m       [36mif[39m ([33m![39mmetric [33m||[39m [33m![39mmetric[33m.[39mname [33m||[39m [36mtypeof[39m metric[33m.[39mvalue [33m!==[39m [32m'number'[39m) {[0m
[Performance Metric] FCP: 356ms
 POST /api/performance/metrics [32m200[39m in 97ms
[Performance Metric] LCP: 356ms
[Performance Metric] TTFB: 297.30000000074506ms
[Performance Metric] CLS: 0
 POST /api/performance/metrics [32m200[39m in 122ms
 POST /api/performance/metrics [32m200[39m in 99ms
 POST /api/performance/metrics [32m200[39m in 100ms
[Performance Metric] CLS: 0.047340450816684296
 POST /api/performance/metrics [32m200[39m in 76ms
[Performance Metric] INP: 40ms
 POST /api/performance/metrics [32m200[39m in 63ms
Updating real-time dashboard: {
  metric: [32m'FCP'[39m,
  value: [33m584[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674519781[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'LCP'[39m,
  value: [33m584[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674519781[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'TTFB'[39m,
  value: [33m453.1000000014901[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674519830[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'CLS'[39m,
  value: [33m0[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674519831[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'INP'[39m,
  value: [33m56[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674519968[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
[Web Vitals Analytics] Processed 5 metrics
 POST /api/analytics/web-vitals [32m200[39m in 79ms
Updating real-time dashboard: {
  metric: [32m'FCP'[39m,
  value: [33m356[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674522938[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'LCP'[39m,
  value: [33m356[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674522939[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'TTFB'[39m,
  value: [33m297.30000000074506[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674522957[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'CLS'[39m,
  value: [33m0[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674522958[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'CLS'[39m,
  value: [33m0.047340450816684296[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674523084[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'INP'[39m,
  value: [33m40[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674523313[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
[Web Vitals Analytics] Processed 6 metrics
 POST /api/analytics/web-vitals [32m200[39m in 67ms
 GET /calculator [32m200[39m in 170ms
 GET / [34m307[39m in 228ms
 GET /calculator [32m200[39m in 178ms
 GET / [34m307[39m in 145ms
 GET /calculator [32m200[39m in 188ms
 GET /calculator [32m200[39m in 1716ms
 GET /calculator [32m200[39m in 1750ms
 GET /calculator [32m200[39m in 1751ms
 GET /calculator [32m200[39m in 2446ms
 GET /calculator [32m200[39m in 1110ms
 GET /calculator [32m200[39m in 1337ms
 GET /calculator [32m200[39m in 2611ms
 GET /calculator [32m200[39m in 2612ms
 GET /calculator [32m200[39m in 2613ms
 GET /calculator [32m200[39m in 232ms
[Performance Metric] FCP: 356ms
 POST /api/performance/metrics [32m200[39m in 144ms
[Performance Metric] LCP: 356ms
[Performance Metric] TTFB: 221.59999999403954ms
[Performance Metric] CLS: 0
 POST /api/performance/metrics [32m200[39m in 213ms
 POST /api/performance/metrics [32m200[39m in 174ms
 POST /api/performance/metrics [32m200[39m in 175ms
[Performance Metric] CLS: 0.047340450816684296
 POST /api/performance/metrics [32m200[39m in 126ms
[Performance Metric] INP: 88ms
 POST /api/performance/metrics [32m200[39m in 108ms
 [37m[1m○[22m[39m Compiling /api/calculate ...
 [32m[1m✓[22m[39m Compiled /api/calculate in 1171ms (2439 modules)
Calculation metrics: {
  event: [32m'calculation_completed'[39m,
  requestId: [32m'calc_1752674991247_gg4oiewu4g9'[39m,
  timestamp: [32m'2025-07-16T14:09:51.249Z'[39m,
  input: {
    builtUpArea: [33m1200[39m,
    floors: [33m2[39m,
    qualityTier: [32m'premium'[39m,
    location: [32m'bangalore'[39m,
    hasSpecialFeatures: [33mfalse[39m
  },
  result: { totalCost: [33m8424000[39m, costPerSqft: [33m2340[39m, totalBuiltUpArea: [33m3600[39m },
  performance: {
    validationTime: [33m0.12496899999678135[39m,
    calculationTime: [33m0.8067689999006689[39m,
    totalTime: [33m1.7460809997282922[39m,
    inputSize: [33m107[39m,
    outputSize: [33m5953[39m
  },
  client: { ip: [32m'::1'[39m, userAgent: [32m'browser'[39m }
}
 POST /api/calculate [32m200[39m in 1456ms
[Performance Metric] CLS: 0.09583333333333334
 POST /api/performance/metrics [32m200[39m in 98ms
 [32m[1m✓[22m[39m Compiled /api/analytics/web-vitals in 451ms (1361 modules)
Updating real-time dashboard: {
  metric: [32m'FCP'[39m,
  value: [33m356[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674988212[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'LCP'[39m,
  value: [33m356[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674988219[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'TTFB'[39m,
  value: [33m221.59999999403954[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674988257[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'CLS'[39m,
  value: [33m0[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674988258[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'CLS'[39m,
  value: [33m0.047340450816684296[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674988510[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'INP'[39m,
  value: [33m88[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674988795[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'CLS'[39m,
  value: [33m0.09583333333333334[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752674991298[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
[Web Vitals Analytics] Processed 7 metrics
 POST /api/analytics/web-vitals [32m200[39m in 743ms
🚀 Environment Configuration Loaded:
   Environment: development
   Analytics: ❌
   Error Tracking: ❌
   Performance Monitoring: ✅
   Rate Limit: 100 requests/60000ms
 GET / [34m307[39m in 414ms
🚀 Environment Configuration Loaded:
   Environment: development
   Analytics: ❌
   Error Tracking: ❌
   Performance Monitoring: ✅
   Rate Limit: 100 requests/60000ms
 GET /calculator [32m200[39m in 457ms
 GET / [34m307[39m in 185ms
 GET /calculator [32m200[39m in 195ms
 GET /calculator [32m200[39m in 1097ms
 GET /calculator [32m200[39m in 1099ms
 GET /calculator [32m200[39m in 1099ms
 GET /calculator [32m200[39m in 1164ms
 GET /calculator [32m200[39m in 1220ms
 GET /calculator [32m200[39m in 1057ms
 GET /calculator [32m200[39m in 1058ms
 GET /calculator [32m200[39m in 949ms
 GET /calculator [32m200[39m in 950ms
 GET /calculator [32m200[39m in 952ms
 GET /calculator [32m200[39m in 1020ms
[Performance Metric] FCP: 668ms
 POST /api/performance/metrics [32m200[39m in 174ms
[Performance Metric] LCP: 668ms
[Performance Metric] FCP: 664ms
[Performance Metric] LCP: 664ms
[Performance Metric] FCP: 664ms
[Performance Metric] LCP: 664ms
 POST /api/performance/metrics [32m200[39m in 360ms
 POST /api/performance/metrics [32m200[39m in 334ms
 POST /api/performance/metrics [32m200[39m in 334ms
 POST /api/performance/metrics [32m200[39m in 322ms
 POST /api/performance/metrics [32m200[39m in 321ms
[Performance Metric] TTFB: 462.5ms
[Performance Metric] CLS: 0
 POST /api/performance/metrics [32m200[39m in 502ms
 POST /api/performance/metrics [32m200[39m in 500ms
[Performance Metric] TTFB: 459.6000000014901ms
[Performance Metric] CLS: 0
[Performance Metric] FCP: 892ms
[Performance Metric] LCP: 892ms
[Performance Metric] TTFB: 727.3999999985099ms
[Performance Metric] CLS: 0
[Performance Metric] TTFB: 461.1000000014901ms
[Performance Metric] CLS: 0
[Performance Metric] LCP: 824ms
[Performance Metric] FCP: 824ms
[Performance Metric] TTFB: 662.6000000014901ms
[Performance Metric] CLS: 0
 POST /api/performance/metrics [32m200[39m in 1200ms
 POST /api/performance/metrics [32m200[39m in 1203ms
 POST /api/performance/metrics [32m200[39m in 1204ms
 POST /api/performance/metrics [32m200[39m in 1203ms
 POST /api/performance/metrics [32m200[39m in 1203ms
 POST /api/performance/metrics [32m200[39m in 1203ms
 POST /api/performance/metrics [32m200[39m in 1094ms
 POST /api/performance/metrics [32m200[39m in 1094ms
 POST /api/performance/metrics [32m200[39m in 1094ms
 POST /api/performance/metrics [32m200[39m in 1093ms
 POST /api/performance/metrics [32m200[39m in 1078ms
 POST /api/performance/metrics [32m200[39m in 1078ms
[Performance Metric] FCP: 744ms
[Performance Metric] LCP: 744ms
[Performance Metric] FCP: 752ms
[Performance Metric] CLS: 0
[Performance Metric] LCP: 752ms
[Performance Metric] TTFB: 533.2000000029802ms
[Performance Metric] CLS: 0
[Performance Metric] TTFB: 531.6999999955297ms
[Performance Metric] FCP: 528ms
[Performance Metric] LCP: 528ms
[Performance Metric] FCP: 528ms
[Performance Metric] TTFB: 376.40000000596046ms
[Performance Metric] FCP: 528ms
[Performance Metric] CLS: 0
[Performance Metric] LCP: 528ms
[Performance Metric] CLS: 0
[Performance Metric] LCP: 528ms
[Performance Metric] TTFB: 376.70000000298023ms
 POST /api/performance/metrics [32m200[39m in 1643ms
 POST /api/performance/metrics [32m200[39m in 1643ms
 POST /api/performance/metrics [32m200[39m in 1596ms
 POST /api/performance/metrics [32m200[39m in 1596ms
 POST /api/performance/metrics [32m200[39m in 1597ms
 POST /api/performance/metrics [32m200[39m in 1598ms
 POST /api/performance/metrics [32m200[39m in 1528ms
 POST /api/performance/metrics [32m200[39m in 1530ms
 POST /api/performance/metrics [32m200[39m in 1509ms
 POST /api/performance/metrics [32m200[39m in 1509ms
 POST /api/performance/metrics [32m200[39m in 1509ms
 POST /api/performance/metrics [32m200[39m in 1495ms
 POST /api/performance/metrics [32m200[39m in 1493ms
 POST /api/performance/metrics [32m200[39m in 1493ms
 POST /api/performance/metrics [32m200[39m in 1493ms
 POST /api/performance/metrics [32m200[39m in 1494ms
 POST /api/performance/metrics [32m200[39m in 1494ms
 POST /api/performance/metrics [32m200[39m in 1495ms
[Performance Metric] FCP: 648ms
[Performance Metric] LCP: 648ms
[Performance Metric] TTFB: 465.5ms
[Performance Metric] CLS: 0
[Performance Metric] CLS: 0.047340450816684296
[Performance Metric] TTFB: 378.29999999701977ms
[Performance Metric] CLS: 0
 POST /api/performance/metrics [32m200[39m in 1967ms
 POST /api/performance/metrics [32m200[39m in 1965ms
 POST /api/performance/metrics [32m200[39m in 1917ms
 POST /api/performance/metrics [32m200[39m in 1914ms
 POST /api/performance/metrics [32m200[39m in 1862ms
 POST /api/performance/metrics [32m200[39m in 1861ms
 POST /api/performance/metrics [32m200[39m in 1861ms
Updating real-time dashboard: {
  metric: [32m'FCP'[39m,
  value: [33m528[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675031014[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'LCP'[39m,
  value: [33m528[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675031016[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'TTFB'[39m,
  value: [33m376.40000000596046[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675031032[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'CLS'[39m,
  value: [33m0[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675031033[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
[Web Vitals Analytics] Processed 4 metrics
 POST /api/analytics/web-vitals [32m200[39m in 2054ms
[Performance Metric] CLS: 0.047340450816684296
 POST /api/performance/metrics [32m200[39m in 1954ms
Updating real-time dashboard: {
  metric: [32m'FCP'[39m,
  value: [33m648[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675031211[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'LCP'[39m,
  value: [33m648[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675031221[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'TTFB'[39m,
  value: [33m465.5[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675031228[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'CLS'[39m,
  value: [33m0[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675031232[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
[Web Vitals Analytics] Processed 4 metrics
 POST /api/analytics/web-vitals [32m200[39m in 1959ms
[Performance Metric] CLS: 0.047340450816684296
[Performance Metric] CLS: 0.047340450816684296
[Performance Metric] CLS: 0.047340450816684296
 POST /api/performance/metrics [32m200[39m in 1664ms
 POST /api/performance/metrics [32m200[39m in 1660ms
 POST /api/performance/metrics [32m200[39m in 1653ms
 POST /api/performance/metrics [32m200[39m in 1138ms
Performance metrics error: SyntaxError: Unexpected end of JSON input
    at JSON.parse (<anonymous>)
    at POST (src/app/api/performance/metrics/route.ts:31:33)
[0m [90m 29 |[39m [36mexport[39m [36masync[39m [36mfunction[39m [33mPOST[39m(request[33m:[39m [33mNextRequest[39m) {[0m
[0m [90m 30 |[39m   [36mtry[39m {[0m
[0m[31m[1m>[22m[39m[90m 31 |[39m       [36mconst[39m body [33m=[39m [36mawait[39m request[33m.[39mjson()[33m;[39m[0m
[0m [90m    |[39m                                 [31m[1m^[22m[39m[0m
[0m [90m 32 |[39m       [36mconst[39m { metric } [33m=[39m body[33m;[39m[0m
[0m [90m 33 |[39m[0m
[0m [90m 34 |[39m       [36mif[39m ([33m![39mmetric [33m||[39m [33m![39mmetric[33m.[39mname [33m||[39m [36mtypeof[39m metric[33m.[39mvalue [33m!==[39m [32m'number'[39m) {[0m
Performance metrics error: SyntaxError: Unexpected end of JSON input
    at JSON.parse (<anonymous>)
    at POST (src/app/api/performance/metrics/route.ts:31:33)
[0m [90m 29 |[39m [36mexport[39m [36masync[39m [36mfunction[39m [33mPOST[39m(request[33m:[39m [33mNextRequest[39m) {[0m
[0m [90m 30 |[39m   [36mtry[39m {[0m
[0m[31m[1m>[22m[39m[90m 31 |[39m       [36mconst[39m body [33m=[39m [36mawait[39m request[33m.[39mjson()[33m;[39m[0m
[0m [90m    |[39m                                 [31m[1m^[22m[39m[0m
[0m [90m 32 |[39m       [36mconst[39m { metric } [33m=[39m body[33m;[39m[0m
[0m [90m 33 |[39m[0m
[0m [90m 34 |[39m       [36mif[39m ([33m![39mmetric [33m||[39m [33m![39mmetric[33m.[39mname [33m||[39m [36mtypeof[39m metric[33m.[39mvalue [33m!==[39m [32m'number'[39m) {[0m
Performance metrics error: SyntaxError: Unexpected end of JSON input
    at JSON.parse (<anonymous>)
    at POST (src/app/api/performance/metrics/route.ts:31:33)
[0m [90m 29 |[39m [36mexport[39m [36masync[39m [36mfunction[39m [33mPOST[39m(request[33m:[39m [33mNextRequest[39m) {[0m
[0m [90m 30 |[39m   [36mtry[39m {[0m
[0m[31m[1m>[22m[39m[90m 31 |[39m       [36mconst[39m body [33m=[39m [36mawait[39m request[33m.[39mjson()[33m;[39m[0m
[0m [90m    |[39m                                 [31m[1m^[22m[39m[0m
[0m [90m 32 |[39m       [36mconst[39m { metric } [33m=[39m body[33m;[39m[0m
[0m [90m 33 |[39m[0m
[0m [90m 34 |[39m       [36mif[39m ([33m![39mmetric [33m||[39m [33m![39mmetric[33m.[39mname [33m||[39m [36mtypeof[39m metric[33m.[39mvalue [33m!==[39m [32m'number'[39m) {[0m
[Performance Metric] INP: 184ms
[Performance Metric] CLS: 0.047340450816684296
[Performance Metric] CLS: 0.047340450816684296
[Performance Metric] INP: 392ms
[Performance Alert] WARNING: INP needs improvement: 392ms
[Performance Metric] INP: 304ms
[Performance Alert] WARNING: INP needs improvement: 304ms
[Performance Metric] INP: 344ms
[Performance Alert] WARNING: INP needs improvement: 344ms
[Performance Metric] INP: 352ms
[Performance Alert] WARNING: INP needs improvement: 352ms
[Performance Metric] CLS: 0.047340450816684296
[Performance Metric] INP: 248ms
[Performance Alert] WARNING: INP needs improvement: 248ms
[Performance Metric] INP: 240ms
[Performance Alert] WARNING: INP needs improvement: 240ms
[Performance Metric] INP: 256ms
[Performance Alert] WARNING: INP needs improvement: 256ms
 POST /api/performance/metrics [32m200[39m in 1856ms
 POST /api/performance/metrics [32m200[39m in 1811ms
 POST /api/performance/metrics [32m200[39m in 1811ms
 POST /api/performance/metrics [32m200[39m in 1742ms
 POST /api/performance/metrics [32m200[39m in 1768ms
 POST /api/performance/metrics [32m200[39m in 1767ms
 POST /api/performance/metrics [32m200[39m in 1767ms
 POST /api/performance/metrics [32m200[39m in 1766ms
 POST /api/performance/metrics [32m200[39m in 1660ms
 POST /api/performance/metrics [32m200[39m in 1608ms
 POST /api/performance/metrics [32m200[39m in 1575ms
[Performance Metric] INP: 336ms
[Performance Alert] WARNING: INP needs improvement: 336ms
[Performance Metric] INP: 256ms
[Performance Alert] WARNING: INP needs improvement: 256ms
 POST /api/performance/metrics [32m200[39m in 1204ms
 POST /api/performance/metrics [32m200[39m in 1188ms
Calculation metrics: {
  event: [32m'calculation_completed'[39m,
  requestId: [32m'calc_1752675034791_2xg19gz6oig'[39m,
  timestamp: [32m'2025-07-16T14:10:34.793Z'[39m,
  input: {
    builtUpArea: [33m1200[39m,
    floors: [33m2[39m,
    qualityTier: [32m'premium'[39m,
    location: [32m'bangalore'[39m,
    hasSpecialFeatures: [33mfalse[39m
  },
  result: { totalCost: [33m8424000[39m, costPerSqft: [33m2340[39m, totalBuiltUpArea: [33m3600[39m },
  performance: {
    validationTime: [33m0.11440900014713407[39m,
    calculationTime: [33m0.9669869998469949[39m,
    totalTime: [33m1.4059989997185767[39m,
    inputSize: [33m107[39m,
    outputSize: [33m5953[39m
  },
  client: { ip: [32m'::1'[39m, userAgent: [32m'browser'[39m }
}
 POST /api/calculate [32m200[39m in 283ms
Calculation metrics: {
  event: [32m'calculation_completed'[39m,
  requestId: [32m'calc_1752675034853_d3j288aspnh'[39m,
  timestamp: [32m'2025-07-16T14:10:34.853Z'[39m,
  input: {
    builtUpArea: [33m1200[39m,
    floors: [33m2[39m,
    qualityTier: [32m'premium'[39m,
    location: [32m'bangalore'[39m,
    hasSpecialFeatures: [33mfalse[39m
  },
  result: { totalCost: [33m8424000[39m, costPerSqft: [33m2340[39m, totalBuiltUpArea: [33m3600[39m },
  performance: {
    validationTime: [33m0.02666600001975894[39m,
    calculationTime: [33m0.22174199996516109[39m,
    totalTime: [33m0.6589790000580251[39m,
    inputSize: [33m107[39m,
    outputSize: [33m5953[39m
  },
  client: { ip: [32m'::1'[39m, userAgent: [32m'browser'[39m }
}
Calculation metrics: {
  event: [32m'calculation_completed'[39m,
  requestId: [32m'calc_1752675034853_fqyksn55gpg'[39m,
  timestamp: [32m'2025-07-16T14:10:34.854Z'[39m,
  input: {
    builtUpArea: [33m1200[39m,
    floors: [33m2[39m,
    qualityTier: [32m'premium'[39m,
    location: [32m'bangalore'[39m,
    hasSpecialFeatures: [33mfalse[39m
  },
  result: { totalCost: [33m8424000[39m, costPerSqft: [33m2340[39m, totalBuiltUpArea: [33m3600[39m },
  performance: {
    validationTime: [33m0.01600300008431077[39m,
    calculationTime: [33m0.09907100023701787[39m,
    totalTime: [33m1.5825979998335242[39m,
    inputSize: [33m107[39m,
    outputSize: [33m5952[39m
  },
  client: { ip: [32m'::1'[39m, userAgent: [32m'browser'[39m }
}
Calculation metrics: {
  event: [32m'calculation_completed'[39m,
  requestId: [32m'calc_1752675034853_ig1ip6cl4je'[39m,
  timestamp: [32m'2025-07-16T14:10:34.855Z'[39m,
  input: {
    builtUpArea: [33m1200[39m,
    floors: [33m2[39m,
    qualityTier: [32m'premium'[39m,
    location: [32m'bangalore'[39m,
    hasSpecialFeatures: [33mfalse[39m
  },
  result: { totalCost: [33m8424000[39m, costPerSqft: [33m2340[39m, totalBuiltUpArea: [33m3600[39m },
  performance: {
    validationTime: [33m0.011202000081539154[39m,
    calculationTime: [33m0.07253700029104948[39m,
    totalTime: [33m2.287434000056237[39m,
    inputSize: [33m107[39m,
    outputSize: [33m5953[39m
  },
  client: { ip: [32m'::1'[39m, userAgent: [32m'browser'[39m }
}
 POST /api/calculate [32m200[39m in 344ms
 POST /api/calculate [32m200[39m in 345ms
 POST /api/calculate [32m200[39m in 346ms
 GET /calculator [32m200[39m in 3396ms
 GET /calculator [32m200[39m in 3639ms
[Performance Metric] CLS: 0.09583333333333334
 POST /api/performance/metrics [32m200[39m in 201ms
[Performance Metric] CLS: 0.09583333333333334
 POST /api/performance/metrics [32m200[39m in 208ms
[Performance Metric] CLS: 0.09583333333333334
[Performance Metric] CLS: 0.09583333333333334
 POST /api/performance/metrics [32m200[39m in 240ms
 POST /api/performance/metrics [32m200[39m in 239ms
[Performance Metric] FCP: 3228ms
[Performance Alert] ERROR: FCP is critically slow: 3228ms
 POST /api/performance/metrics [32m200[39m in 138ms
[Performance Metric] LCP: 3228ms
[Performance Alert] WARNING: LCP needs improvement: 3228ms
[Performance Metric] FCP: 3440ms
[Performance Alert] ERROR: FCP is critically slow: 3440ms
[Performance Metric] LCP: 3440ms
[Performance Alert] WARNING: LCP needs improvement: 3440ms
[Performance Metric] CLS: 0
[Performance Metric] TTFB: 2708.9000000059605ms
[Performance Alert] ERROR: TTFB is critically slow: 2708.9000000059605ms
[Performance Metric] CLS: 0
[Performance Metric] TTFB: 2504.5ms
[Performance Alert] ERROR: TTFB is critically slow: 2504.5ms
 POST /api/performance/metrics [32m200[39m in 198ms
 POST /api/performance/metrics [32m200[39m in 198ms
 POST /api/performance/metrics [32m200[39m in 225ms
 POST /api/performance/metrics [32m200[39m in 223ms
 POST /api/performance/metrics [32m200[39m in 223ms
 POST /api/performance/metrics [32m200[39m in 200ms
 POST /api/performance/metrics [32m200[39m in 199ms
Updating real-time dashboard: {
  metric: [32m'FCP'[39m,
  value: [33m664[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675030623[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'LCP'[39m,
  value: [33m664[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675030625[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'TTFB'[39m,
  value: [33m459.6000000014901[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675030700[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'CLS'[39m,
  value: [33m0[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675030701[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'CLS'[39m,
  value: [33m0.047340450816684296[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675031264[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'INP'[39m,
  value: [33m184[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675032086[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
[Web Vitals Analytics] Processed 6 metrics
 POST /api/analytics/web-vitals [32m200[39m in 123ms
 POST /api/performance/metrics [32m200[39m in 136ms
 POST /api/performance/metrics [32m200[39m in 141ms
Performance metrics error: SyntaxError: Unexpected end of JSON input
    at JSON.parse (<anonymous>)
    at POST (src/app/api/performance/metrics/route.ts:31:33)
[0m [90m 29 |[39m [36mexport[39m [36masync[39m [36mfunction[39m [33mPOST[39m(request[33m:[39m [33mNextRequest[39m) {[0m
[0m [90m 30 |[39m   [36mtry[39m {[0m
[0m[31m[1m>[22m[39m[90m 31 |[39m       [36mconst[39m body [33m=[39m [36mawait[39m request[33m.[39mjson()[33m;[39m[0m
[0m [90m    |[39m                                 [31m[1m^[22m[39m[0m
[0m [90m 32 |[39m       [36mconst[39m { metric } [33m=[39m body[33m;[39m[0m
[0m [90m 33 |[39m[0m
[0m [90m 34 |[39m       [36mif[39m ([33m![39mmetric [33m||[39m [33m![39mmetric[33m.[39mname [33m||[39m [36mtypeof[39m metric[33m.[39mvalue [33m!==[39m [32m'number'[39m) {[0m
Performance metrics error: SyntaxError: Unexpected end of JSON input
    at JSON.parse (<anonymous>)
    at POST (src/app/api/performance/metrics/route.ts:31:33)
[0m [90m 29 |[39m [36mexport[39m [36masync[39m [36mfunction[39m [33mPOST[39m(request[33m:[39m [33mNextRequest[39m) {[0m
[0m [90m 30 |[39m   [36mtry[39m {[0m
[0m[31m[1m>[22m[39m[90m 31 |[39m       [36mconst[39m body [33m=[39m [36mawait[39m request[33m.[39mjson()[33m;[39m[0m
[0m [90m    |[39m                                 [31m[1m^[22m[39m[0m
[0m [90m 32 |[39m       [36mconst[39m { metric } [33m=[39m body[33m;[39m[0m
[0m [90m 33 |[39m[0m
[0m [90m 34 |[39m       [36mif[39m ([33m![39mmetric [33m||[39m [33m![39mmetric[33m.[39mname [33m||[39m [36mtypeof[39m metric[33m.[39mvalue [33m!==[39m [32m'number'[39m) {[0m
Updating real-time dashboard: {
  metric: [32m'FCP'[39m,
  value: [33m664[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675030634[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'FCP'[39m,
  value: [33m892[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675030699[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'LCP'[39m,
  value: [33m664[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675030636[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'LCP'[39m,
  value: [33m892[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675030701[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'TTFB'[39m,
  value: [33m461.1000000014901[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675030731[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'TTFB'[39m,
  value: [33m727.3999999985099[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675030711[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'CLS'[39m,
  value: [33m0[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675030744[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'CLS'[39m,
  value: [33m0[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675030714[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'CLS'[39m,
  value: [33m0.047340450816684296[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675031547[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'CLS'[39m,
  value: [33m0.047340450816684296[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675031711[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
[Performance Alert] WARNING: INP needs improvement: 352ms
[Performance Alert] WARNING: INP needs improvement: 344ms
Updating real-time dashboard: {
  metric: [32m'INP'[39m,
  value: [33m352[39m,
  rating: [32m'needs-improvement'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675032440[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'INP'[39m,
  value: [33m344[39m,
  rating: [32m'needs-improvement'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675032551[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
[Web Vitals Analytics] Processed 6 metrics
Updating real-time dashboard: {
  metric: [32m'CLS'[39m,
  value: [33m0.09583333333333334[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675034935[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
[Web Vitals Analytics] Processed 7 metrics
 POST /api/analytics/web-vitals [32m200[39m in 336ms
 POST /api/analytics/web-vitals [32m200[39m in 300ms
Updating real-time dashboard: {
  metric: [32m'FCP'[39m,
  value: [33m824[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675030773[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'LCP'[39m,
  value: [33m824[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675030774[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'TTFB'[39m,
  value: [33m662.6000000014901[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675030784[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'CLS'[39m,
  value: [33m0[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675030786[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'CLS'[39m,
  value: [33m0.047340450816684296[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675031732[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
[Performance Alert] WARNING: INP needs improvement: 304ms
Updating real-time dashboard: {
  metric: [32m'INP'[39m,
  value: [33m304[39m,
  rating: [32m'needs-improvement'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675032451[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'CLS'[39m,
  value: [33m0.09583333333333334[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675034927[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
[Web Vitals Analytics] Processed 7 metrics
 POST /api/analytics/web-vitals [32m200[39m in 364ms
Updating real-time dashboard: {
  metric: [32m'FCP'[39m,
  value: [33m752[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675030917[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'LCP'[39m,
  value: [33m752[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675030922[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'TTFB'[39m,
  value: [33m533.2000000029802[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675030927[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'CLS'[39m,
  value: [33m0[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675030929[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'CLS'[39m,
  value: [33m0.047340450816684296[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675032060[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
[Performance Alert] WARNING: INP needs improvement: 248ms
Updating real-time dashboard: {
  metric: [32m'INP'[39m,
  value: [33m248[39m,
  rating: [32m'needs-improvement'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675032734[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
[Performance Alert] WARNING: INP needs improvement: 256ms
Updating real-time dashboard: {
  metric: [32m'INP'[39m,
  value: [33m256[39m,
  rating: [32m'needs-improvement'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675033333[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'CLS'[39m,
  value: [33m0.09583333333333334[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675034934[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
[Web Vitals Analytics] Processed 8 metrics
 POST /api/analytics/web-vitals [32m200[39m in 236ms
Updating real-time dashboard: {
  metric: [32m'FCP'[39m,
  value: [33m528[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675031015[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'FCP'[39m,
  value: [33m528[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675031017[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'LCP'[39m,
  value: [33m528[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675031017[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'LCP'[39m,
  value: [33m528[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675031019[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'TTFB'[39m,
  value: [33m378.29999999701977[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675031264[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'TTFB'[39m,
  value: [33m376.70000000298023[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675031040[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'CLS'[39m,
  value: [33m0[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675031267[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'CLS'[39m,
  value: [33m0[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675031041[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'CLS'[39m,
  value: [33m0.047340450816684296[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675032169[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'CLS'[39m,
  value: [33m0.047340450816684296[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675032144[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
[Performance Alert] WARNING: INP needs improvement: 256ms
[Performance Alert] WARNING: INP needs improvement: 240ms
Updating real-time dashboard: {
  metric: [32m'INP'[39m,
  value: [33m256[39m,
  rating: [32m'needs-improvement'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675032807[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'INP'[39m,
  value: [33m240[39m,
  rating: [32m'needs-improvement'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675032769[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
[Web Vitals Analytics] Processed 6 metrics
[Performance Alert] WARNING: INP needs improvement: 336ms
Updating real-time dashboard: {
  metric: [32m'INP'[39m,
  value: [33m336[39m,
  rating: [32m'needs-improvement'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675033321[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'CLS'[39m,
  value: [33m0.09583333333333334[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675034857[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
[Web Vitals Analytics] Processed 8 metrics
 POST /api/analytics/web-vitals [32m200[39m in 222ms
 POST /api/analytics/web-vitals [32m200[39m in 215ms
 GET /calculator [32m200[39m in 619ms
[Performance Metric] FCP: 556ms
 POST /api/performance/metrics [32m200[39m in 113ms
[Performance Metric] LCP: 556ms
[Performance Metric] CLS: 0
[Performance Metric] TTFB: 414.80000000447035ms
 POST /api/performance/metrics [32m200[39m in 195ms
 POST /api/performance/metrics [32m200[39m in 171ms
 POST /api/performance/metrics [32m200[39m in 172ms
Updating real-time dashboard: {
  metric: [32m'FCP'[39m,
  value: [33m556[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675036587[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'LCP'[39m,
  value: [33m556[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675036588[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'TTFB'[39m,
  value: [33m414.80000000447035[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675036612[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'CLS'[39m,
  value: [33m0[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675036613[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
[Web Vitals Analytics] Processed 4 metrics
 POST /api/analytics/web-vitals [32m200[39m in 222ms
 GET /calculator [32m200[39m in 319ms
[Performance Metric] FCP: 344ms
 POST /api/performance/metrics [32m200[39m in 142ms
[Performance Metric] LCP: 344ms
[Performance Metric] TTFB: 297.69999999552965ms
[Performance Metric] CLS: 0
 POST /api/performance/metrics [32m200[39m in 198ms
 POST /api/performance/metrics [32m200[39m in 173ms
 POST /api/performance/metrics [32m200[39m in 173ms
[Performance Metric] CLS: 0.027380037117872846
 POST /api/performance/metrics [32m200[39m in 124ms
[Performance Metric] INP: 72ms
 POST /api/performance/metrics [32m200[39m in 89ms
 GET /calculator [32m200[39m in 457ms
 GET /calculator [32m200[39m in 434ms
 GET /calculator [32m200[39m in 451ms
[Performance Metric] FCP: 380ms
 POST /api/performance/metrics [32m200[39m in 130ms
[Performance Metric] LCP: 380ms
[Performance Metric] FCP: 460ms
[Performance Metric] LCP: 460ms
[Performance Metric] FCP: 364ms
[Performance Metric] TTFB: 266.79999999701977ms
[Performance Metric] CLS: 0
[Performance Metric] LCP: 364ms
 POST /api/performance/metrics [32m200[39m in 256ms
 POST /api/performance/metrics [32m200[39m in 235ms
 POST /api/performance/metrics [32m200[39m in 236ms
 POST /api/performance/metrics [32m200[39m in 226ms
 POST /api/performance/metrics [32m200[39m in 226ms
 POST /api/performance/metrics [32m200[39m in 227ms
 POST /api/performance/metrics [32m200[39m in 227ms
Updating real-time dashboard: {
  metric: [32m'FCP'[39m,
  value: [33m460[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675041460[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'LCP'[39m,
  value: [33m460[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675041461[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
[Web Vitals Analytics] Processed 2 metrics
 POST /api/analytics/web-vitals [32m200[39m in 337ms
[Performance Metric] TTFB: 363.8999999985099ms
[Performance Metric] CLS: 0
[Performance Metric] CLS: 0
[Performance Metric] TTFB: 253.29999999701977ms
 POST /api/performance/metrics [32m200[39m in 359ms
 POST /api/performance/metrics [32m200[39m in 360ms
 POST /api/performance/metrics [32m200[39m in 360ms
 POST /api/performance/metrics [32m200[39m in 360ms
[Performance Metric] CLS: 0.047340450816684296
[Performance Metric] CLS: 0.047340450816684296
 POST /api/performance/metrics [32m200[39m in 347ms
 POST /api/performance/metrics [32m200[39m in 306ms
 GET /calculator [32m200[39m in 585ms
[Performance Metric] CLS: 0.047340450816684296
 POST /api/performance/metrics [32m200[39m in 408ms
Updating real-time dashboard: {
  metric: [32m'TTFB'[39m,
  value: [33m363.8999999985099[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675041496[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'CLS'[39m,
  value: [33m0[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675041496[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'CLS'[39m,
  value: [33m0.047340450816684296[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675041710[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
[Web Vitals Analytics] Processed 3 metrics
 POST /api/analytics/web-vitals [32m200[39m in 81ms
[Performance Metric] INP: 3288ms
[Performance Alert] ERROR: INP is critically slow: 3288ms
 POST /api/performance/metrics [32m200[39m in 79ms
[Performance Metric] INP: 176ms
 POST /api/performance/metrics [32m200[39m in 92ms
 GET /calculator [32m200[39m in 247ms
[Performance Metric] FCP: 4320ms
[Performance Alert] ERROR: FCP is critically slow: 4320ms
 POST /api/performance/metrics [32m200[39m in 111ms
[Performance Metric] LCP: 4320ms
[Performance Alert] ERROR: LCP is critically slow: 4320ms
[Performance Metric] TTFB: 2836.5ms
[Performance Alert] ERROR: TTFB is critically slow: 2836.5ms
[Performance Metric] CLS: 0
 POST /api/performance/metrics [32m200[39m in 142ms
 POST /api/performance/metrics [32m200[39m in 168ms
 POST /api/performance/metrics [32m200[39m in 146ms
[Performance Metric] FCP: 300ms
 POST /api/performance/metrics [32m200[39m in 166ms
Updating real-time dashboard: {
  metric: [32m'FCP'[39m,
  value: [33m300[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675046356[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'LCP'[39m,
  value: [33m300[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675046357[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
[Web Vitals Analytics] Processed 2 metrics
 POST /api/analytics/web-vitals [32m200[39m in 191ms
 POST /api/performance/metrics [32m200[39m in 206ms
Performance metrics error: SyntaxError: Unexpected end of JSON input
    at JSON.parse (<anonymous>)
    at POST (src/app/api/performance/metrics/route.ts:31:33)
[0m [90m 29 |[39m [36mexport[39m [36masync[39m [36mfunction[39m [33mPOST[39m(request[33m:[39m [33mNextRequest[39m) {[0m
[0m [90m 30 |[39m   [36mtry[39m {[0m
[0m[31m[1m>[22m[39m[90m 31 |[39m       [36mconst[39m body [33m=[39m [36mawait[39m request[33m.[39mjson()[33m;[39m[0m
[0m [90m    |[39m                                 [31m[1m^[22m[39m[0m
[0m [90m 32 |[39m       [36mconst[39m { metric } [33m=[39m body[33m;[39m[0m
[0m [90m 33 |[39m[0m
[0m [90m 34 |[39m       [36mif[39m ([33m![39mmetric [33m||[39m [33m![39mmetric[33m.[39mname [33m||[39m [36mtypeof[39m metric[33m.[39mvalue [33m!==[39m [32m'number'[39m) {[0m
[Performance Metric] LCP: 300ms
[Performance Metric] TTFB: 203.70000000298023ms
[Performance Metric] CLS: 0
 POST /api/performance/metrics [32m200[39m in 984ms
 POST /api/performance/metrics [32m200[39m in 930ms
 POST /api/performance/metrics [32m200[39m in 930ms
Updating real-time dashboard: {
  metric: [32m'FCP'[39m,
  value: [33m380[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675041438[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'LCP'[39m,
  value: [33m380[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675041440[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'TTFB'[39m,
  value: [33m266.79999999701977[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675041466[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'CLS'[39m,
  value: [33m0[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675041469[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'CLS'[39m,
  value: [33m0.047340450816684296[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675041675[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
[Performance Alert] CRITICAL: INP is critically slow: 3288ms
Updating real-time dashboard: {
  metric: [32m'INP'[39m,
  value: [33m3288[39m,
  rating: [32m'poor'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675045100[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
[Web Vitals Analytics] Processed 6 metrics
 POST /api/analytics/web-vitals [32m200[39m in 935ms
Updating real-time dashboard: {
  metric: [32m'FCP'[39m,
  value: [33m364[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675041466[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'LCP'[39m,
  value: [33m364[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675041467[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'TTFB'[39m,
  value: [33m253.29999999701977[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675041495[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'CLS'[39m,
  value: [33m0[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675041496[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'CLS'[39m,
  value: [33m0.047340450816684296[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675041789[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'INP'[39m,
  value: [33m176[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675045279[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
[Web Vitals Analytics] Processed 6 metrics
 POST /api/analytics/web-vitals [32m200[39m in 1009ms
Performance metrics error: SyntaxError: Unexpected end of JSON input
    at JSON.parse (<anonymous>)
    at POST (src/app/api/performance/metrics/route.ts:31:33)
[0m [90m 29 |[39m [36mexport[39m [36masync[39m [36mfunction[39m [33mPOST[39m(request[33m:[39m [33mNextRequest[39m) {[0m
[0m [90m 30 |[39m   [36mtry[39m {[0m
[0m[31m[1m>[22m[39m[90m 31 |[39m       [36mconst[39m body [33m=[39m [36mawait[39m request[33m.[39mjson()[33m;[39m[0m
[0m [90m    |[39m                                 [31m[1m^[22m[39m[0m
[0m [90m 32 |[39m       [36mconst[39m { metric } [33m=[39m body[33m;[39m[0m
[0m [90m 33 |[39m[0m
[0m [90m 34 |[39m       [36mif[39m ([33m![39mmetric [33m||[39m [33m![39mmetric[33m.[39mname [33m||[39m [36mtypeof[39m metric[33m.[39mvalue [33m!==[39m [32m'number'[39m) {[0m
 GET /calculator [32m200[39m in 1183ms
[Performance Metric] FCP: 1192ms
[Performance Metric] LCP: 1192ms
 POST /api/performance/metrics [32m200[39m in 157ms
 POST /api/performance/metrics [32m200[39m in 158ms
[Performance Metric] TTFB: 1101.6999999955297ms
[Performance Alert] WARNING: TTFB needs improvement: 1101.6999999955297ms
[Performance Metric] CLS: 0
 POST /api/performance/metrics [32m200[39m in 182ms
 POST /api/performance/metrics [32m200[39m in 182ms
[Performance Metric] INP: 48ms
 POST /api/performance/metrics [32m200[39m in 142ms
 GET /calculator [32m200[39m in 175ms
[Performance Metric] FCP: 224ms
 POST /api/performance/metrics [32m200[39m in 109ms
[Performance Metric] LCP: 224ms
[Performance Metric] TTFB: 155.30000000447035ms
[Performance Metric] CLS: 0
 POST /api/performance/metrics [32m200[39m in 274ms
 POST /api/performance/metrics [32m200[39m in 257ms
 POST /api/performance/metrics [32m200[39m in 257ms
Updating real-time dashboard: {
  metric: [32m'FCP'[39m,
  value: [33m224[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675049210[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'LCP'[39m,
  value: [33m224[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675049211[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
[Web Vitals Analytics] Processed 2 metrics
 POST /api/analytics/web-vitals [32m200[39m in 314ms
Performance metrics error: SyntaxError: Unexpected end of JSON input
    at JSON.parse (<anonymous>)
    at POST (src/app/api/performance/metrics/route.ts:31:33)
[0m [90m 29 |[39m [36mexport[39m [36masync[39m [36mfunction[39m [33mPOST[39m(request[33m:[39m [33mNextRequest[39m) {[0m
[0m [90m 30 |[39m   [36mtry[39m {[0m
[0m[31m[1m>[22m[39m[90m 31 |[39m       [36mconst[39m body [33m=[39m [36mawait[39m request[33m.[39mjson()[33m;[39m[0m
[0m [90m    |[39m                                 [31m[1m^[22m[39m[0m
[0m [90m 32 |[39m       [36mconst[39m { metric } [33m=[39m body[33m;[39m[0m
[0m [90m 33 |[39m[0m
[0m [90m 34 |[39m       [36mif[39m ([33m![39mmetric [33m||[39m [33m![39mmetric[33m.[39mname [33m||[39m [36mtypeof[39m metric[33m.[39mvalue [33m!==[39m [32m'number'[39m) {[0m
 GET /calculator [32m200[39m in 754ms
[Performance Metric] FCP: 612ms
 POST /api/performance/metrics [32m200[39m in 135ms
[Performance Metric] LCP: 612ms
[Performance Metric] TTFB: 479.5ms
[Performance Metric] CLS: 0
 POST /api/performance/metrics [32m200[39m in 228ms
 POST /api/performance/metrics [32m200[39m in 206ms
 POST /api/performance/metrics [32m200[39m in 205ms
[Performance Metric] CLS: 0.047340450816684296
 POST /api/performance/metrics [32m200[39m in 119ms
[Performance Metric] INP: 40ms
 POST /api/performance/metrics [32m200[39m in 113ms
Updating real-time dashboard: {
  metric: [32m'FCP'[39m,
  value: [33m1192[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675047828[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'LCP'[39m,
  value: [33m1192[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675047828[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
[Performance Alert] WARNING: TTFB needs improvement: 1101.6999999955297ms
Updating real-time dashboard: {
  metric: [32m'TTFB'[39m,
  value: [33m1101.6999999955297[39m,
  rating: [32m'needs-improvement'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675047875[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'CLS'[39m,
  value: [33m0[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675047876[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'INP'[39m,
  value: [33m48[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675048012[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
[Web Vitals Analytics] Processed 5 metrics
 POST /api/analytics/web-vitals [32m200[39m in 120ms
Updating real-time dashboard: {
  metric: [32m'FCP'[39m,
  value: [33m612[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675050324[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'LCP'[39m,
  value: [33m612[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675050325[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'TTFB'[39m,
  value: [33m479.5[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675050346[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'CLS'[39m,
  value: [33m0[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675050346[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'CLS'[39m,
  value: [33m0.047340450816684296[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675050537[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'INP'[39m,
  value: [33m40[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675050708[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
[Web Vitals Analytics] Processed 6 metrics
 POST /api/analytics/web-vitals [32m200[39m in 90ms
 GET /calculator [32m200[39m in 169ms
 [37m[1m○[22m[39m Compiling /_not-found ...
 [32m[1m✓[22m[39m Compiled /_not-found in 7.4s (2442 modules)
🚀 Environment Configuration Loaded:
   Environment: development
   Analytics: ❌
   Error Tracking: ❌
   Performance Monitoring: ✅
   Rate Limit: 100 requests/60000ms
🚀 Environment Configuration Loaded:
   Environment: development
   Analytics: ❌
   Error Tracking: ❌
   Performance Monitoring: ✅
   Rate Limit: 100 requests/60000ms
Performance metrics error: SyntaxError: Unexpected end of JSON input
    at JSON.parse (<anonymous>)
    at POST (src/app/api/performance/metrics/route.ts:31:33)
[0m [90m 29 |[39m [36mexport[39m [36masync[39m [36mfunction[39m [33mPOST[39m(request[33m:[39m [33mNextRequest[39m) {[0m
[0m [90m 30 |[39m   [36mtry[39m {[0m
[0m[31m[1m>[22m[39m[90m 31 |[39m       [36mconst[39m body [33m=[39m [36mawait[39m request[33m.[39mjson()[33m;[39m[0m
[0m [90m    |[39m                                 [31m[1m^[22m[39m[0m
[0m [90m 32 |[39m       [36mconst[39m { metric } [33m=[39m body[33m;[39m[0m
[0m [90m 33 |[39m[0m
[0m [90m 34 |[39m       [36mif[39m ([33m![39mmetric [33m||[39m [33m![39mmetric[33m.[39mname [33m||[39m [36mtypeof[39m metric[33m.[39mvalue [33m!==[39m [32m'number'[39m) {[0m
Performance metrics error: SyntaxError: Unexpected end of JSON input
    at JSON.parse (<anonymous>)
    at POST (src/app/api/performance/metrics/route.ts:31:33)
[0m [90m 29 |[39m [36mexport[39m [36masync[39m [36mfunction[39m [33mPOST[39m(request[33m:[39m [33mNextRequest[39m) {[0m
[0m [90m 30 |[39m   [36mtry[39m {[0m
[0m[31m[1m>[22m[39m[90m 31 |[39m       [36mconst[39m body [33m=[39m [36mawait[39m request[33m.[39mjson()[33m;[39m[0m
[0m [90m    |[39m                                 [31m[1m^[22m[39m[0m
[0m [90m 32 |[39m       [36mconst[39m { metric } [33m=[39m body[33m;[39m[0m
[0m [90m 33 |[39m[0m
[0m [90m 34 |[39m       [36mif[39m ([33m![39mmetric [33m||[39m [33m![39mmetric[33m.[39mname [33m||[39m [36mtypeof[39m metric[33m.[39mvalue [33m!==[39m [32m'number'[39m) {[0m
Performance metrics error: SyntaxError: Unexpected end of JSON input
    at JSON.parse (<anonymous>)
    at POST (src/app/api/performance/metrics/route.ts:31:33)
[0m [90m 29 |[39m [36mexport[39m [36masync[39m [36mfunction[39m [33mPOST[39m(request[33m:[39m [33mNextRequest[39m) {[0m
[0m [90m 30 |[39m   [36mtry[39m {[0m
[0m[31m[1m>[22m[39m[90m 31 |[39m       [36mconst[39m body [33m=[39m [36mawait[39m request[33m.[39mjson()[33m;[39m[0m
[0m [90m    |[39m                                 [31m[1m^[22m[39m[0m
[0m [90m 32 |[39m       [36mconst[39m { metric } [33m=[39m body[33m;[39m[0m
[0m [90m 33 |[39m[0m
[0m [90m 34 |[39m       [36mif[39m ([33m![39mmetric [33m||[39m [33m![39mmetric[33m.[39mname [33m||[39m [36mtypeof[39m metric[33m.[39mvalue [33m!==[39m [32m'number'[39m) {[0m
 GET / [34m307[39m in 248ms
 GET /calculator [32m200[39m in 186ms
 GET / [34m307[39m in 126ms
 GET /calculator [32m200[39m in 163ms
 GET /calculator [32m200[39m in 419ms
 GET /calculator [32m200[39m in 459ms
 GET /calculator [32m200[39m in 283ms
[Performance Metric] FCP: 420ms
 POST /api/performance/metrics [32m200[39m in 175ms
[Performance Metric] LCP: 420ms
[Performance Metric] FCP: 488ms
[Performance Metric] LCP: 488ms
[Performance Metric] TTFB: 254.39999999850988ms
[Performance Metric] CLS: 0
[Performance Metric] CLS: 0
[Performance Metric] TTFB: 369.30000000447035ms
 POST /api/performance/metrics [32m200[39m in 352ms
 POST /api/performance/metrics [32m200[39m in 347ms
 POST /api/performance/metrics [32m200[39m in 346ms
 POST /api/performance/metrics [32m200[39m in 320ms
 POST /api/performance/metrics [32m200[39m in 320ms
 POST /api/performance/metrics [32m200[39m in 318ms
 POST /api/performance/metrics [32m200[39m in 319ms
[Performance Metric] CLS: 0.038910191793202875
 POST /api/performance/metrics [32m200[39m in 153ms
[Performance Metric] CLS: 0.047340450816684296
 POST /api/performance/metrics [32m200[39m in 246ms
[Performance Metric] INP: 80ms
 POST /api/performance/metrics [32m200[39m in 210ms
 GET /apple-touch-icon.png [33m404[39m in 253ms
 GET /favicon-16x16.png [33m404[39m in 249ms
[Performance Metric] INP: 88ms
 POST /api/performance/metrics [32m200[39m in 262ms
[Performance Metric] FCP: 447ms
 POST /api/performance/metrics [32m200[39m in 145ms
[Performance Metric] LCP: 459ms
[Performance Metric] TTFB: 253ms
 POST /api/performance/metrics [32m200[39m in 204ms
 POST /api/performance/metrics [32m200[39m in 135ms
 GET /calculator [32m200[39m in 153ms
[Performance Metric] FCP: 252ms
 POST /api/performance/metrics [32m200[39m in 106ms
[Performance Metric] LCP: 252ms
[Performance Metric] TTFB: 147ms
[Performance Metric] CLS: 0
 POST /api/performance/metrics [32m200[39m in 195ms
 POST /api/performance/metrics [32m200[39m in 172ms
 POST /api/performance/metrics [32m200[39m in 172ms
 GET /favicon-32x32.png [33m404[39m in 192ms
[Performance Metric] CLS: 0.047340450816684296
 POST /api/performance/metrics [32m200[39m in 78ms
 GET /favicon-16x16.png [33m404[39m in 67ms
 [32m[1m✓[22m[39m Compiled /favicon.ico in 432ms (1353 modules)
 GET /favicon.ico [32m200[39m in 965ms
[Performance Metric] INP: 64ms
 POST /api/performance/metrics [32m200[39m in 940ms
Web vitals analytics error: SyntaxError: Unexpected end of JSON input
    at JSON.parse (<anonymous>)
    at POST (src/app/api/analytics/web-vitals/route.ts:38:33)
[0m [90m 36 |[39m [36mexport[39m [36masync[39m [36mfunction[39m [33mPOST[39m(request[33m:[39m [33mNextRequest[39m) {[0m
[0m [90m 37 |[39m   [36mtry[39m {[0m
[0m[31m[1m>[22m[39m[90m 38 |[39m       [36mconst[39m body [33m=[39m [36mawait[39m request[33m.[39mjson()[33m;[39m[0m
[0m [90m    |[39m                                 [31m[1m^[22m[39m[0m
[0m [90m 39 |[39m       [36mconst[39m { metrics } [33m=[39m body[33m;[39m[0m
[0m [90m 40 |[39m[0m
[0m [90m 41 |[39m       [36mif[39m ([33m![39mmetrics [33m||[39m [33m![39m[33mArray[39m[33m.[39misArray(metrics)) {[0m
🚀 Environment Configuration Loaded:
   Environment: development
   Analytics: ❌
   Error Tracking: ❌
   Performance Monitoring: ✅
   Rate Limit: 100 requests/60000ms
 GET / [34m307[39m in 307ms
🚀 Environment Configuration Loaded:
   Environment: development
   Analytics: ❌
   Error Tracking: ❌
   Performance Monitoring: ✅
   Rate Limit: 100 requests/60000ms
 GET /calculator [32m200[39m in 308ms
 GET / [34m307[39m in 109ms
 GET /calculator [32m200[39m in 152ms
 GET /calculator [32m200[39m in 219ms
[Performance Metric] FCP: 256ms
 POST /api/performance/metrics [32m200[39m in 130ms
[Performance Metric] LCP: 256ms
[Performance Metric] TTFB: 185.80000000447035ms
[Performance Metric] CLS: 0
 POST /api/performance/metrics [32m200[39m in 165ms
 POST /api/performance/metrics [32m200[39m in 140ms
 POST /api/performance/metrics [32m200[39m in 139ms
[Performance Metric] CLS: 0.047340450816684296
 POST /api/performance/metrics [32m200[39m in 82ms
[Performance Metric] INP: 72ms
 POST /api/performance/metrics [32m200[39m in 79ms
Updating real-time dashboard: {
  metric: [32m'FCP'[39m,
  value: [33m256[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675471699[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'LCP'[39m,
  value: [33m256[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675471700[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'TTFB'[39m,
  value: [33m185.80000000447035[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675471724[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'CLS'[39m,
  value: [33m0[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675471725[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'CLS'[39m,
  value: [33m0.047340450816684296[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675471861[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'INP'[39m,
  value: [33m72[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675472108[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
[Web Vitals Analytics] Processed 6 metrics
 POST /api/analytics/web-vitals [32m200[39m in 65ms
 GET / [34m307[39m in 129ms
 GET /calculator [32m200[39m in 179ms
 GET / [34m307[39m in 136ms
 GET /calculator [32m200[39m in 172ms
 GET /calculator [32m200[39m in 197ms
[Performance Metric] FCP: 236ms
 POST /api/performance/metrics [32m200[39m in 96ms
[Performance Metric] LCP: 236ms
[Performance Metric] TTFB: 165.69999999552965ms
[Performance Metric] CLS: 0
 POST /api/performance/metrics [32m200[39m in 147ms
 POST /api/performance/metrics [32m200[39m in 123ms
 POST /api/performance/metrics [32m200[39m in 122ms
[Performance Metric] CLS: 0.047340450816684296
 POST /api/performance/metrics [32m200[39m in 74ms
[Performance Metric] INP: 56ms
 POST /api/performance/metrics [32m200[39m in 75ms
Updating real-time dashboard: {
  metric: [32m'FCP'[39m,
  value: [33m236[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675567565[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'LCP'[39m,
  value: [33m236[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675567566[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'TTFB'[39m,
  value: [33m165.69999999552965[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675567590[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'CLS'[39m,
  value: [33m0[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675567591[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'CLS'[39m,
  value: [33m0.047340450816684296[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675567736[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'INP'[39m,
  value: [33m56[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675567954[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
[Web Vitals Analytics] Processed 6 metrics
 POST /api/analytics/web-vitals [32m200[39m in 68ms
 GET /calculator [32m200[39m in 149ms
 GET / [34m307[39m in 115ms
 GET /calculator [32m200[39m in 158ms
 GET / [34m307[39m in 109ms
 GET /calculator [32m200[39m in 152ms
 GET /calculator [32m200[39m in 190ms
[Performance Metric] FCP: 232ms
 POST /api/performance/metrics [32m200[39m in 108ms
[Performance Metric] LCP: 232ms
[Performance Metric] TTFB: 161.39999999850988ms
[Performance Metric] CLS: 0
 POST /api/performance/metrics [32m200[39m in 155ms
 POST /api/performance/metrics [32m200[39m in 130ms
 POST /api/performance/metrics [32m200[39m in 129ms
[Performance Metric] CLS: 0.047340450816684296
 POST /api/performance/metrics [32m200[39m in 82ms
[Performance Metric] INP: 56ms
 POST /api/performance/metrics [32m200[39m in 74ms
Updating real-time dashboard: {
  metric: [32m'FCP'[39m,
  value: [33m232[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675659951[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'LCP'[39m,
  value: [33m232[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675659951[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'TTFB'[39m,
  value: [33m161.39999999850988[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675659976[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'CLS'[39m,
  value: [33m0[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675659976[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'CLS'[39m,
  value: [33m0.047340450816684296[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675660121[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
Updating real-time dashboard: {
  metric: [32m'INP'[39m,
  value: [33m56[39m,
  rating: [32m'good'[39m,
  url: [32m'http://localhost:3000/calculator'[39m,
  timestamp: [33m1752675660367[39m,
  userAgent: [32m'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36'[39m,
  connectionType: [32m'4g'[39m,
  deviceType: [90mundefined[39m,
  sessionId: [90mundefined[39m,
  userId: [90mundefined[39m,
  buildVersion: [90mundefined[39m,
  route: [90mundefined[39m,
  referrer: [90mundefined[39m,
  viewport: [90mundefined[39m
}
[Web Vitals Analytics] Processed 6 metrics
 POST /api/analytics/web-vitals [32m200[39m in 73ms
