/**
 * Monitoring and Error Tracking Utilities
 * Centralized monitoring configuration for production environments
 */

import { isErrorTrackingEnabled, isPerformanceMonitoringEnabled } from './env';

// Error severity levels
export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

// Performance metric types
export interface PerformanceMetric {
  name: string;
  value: number;
  unit: string;
  timestamp: number;
  url?: string;
  userAgent?: string;
}

// Error tracking interface
export interface ErrorReport {
  error: Error;
  severity: ErrorSeverity;
  context?: Record<string, any>;
  userId?: string;
  sessionId?: string;
  url?: string;
  userAgent?: string;
  timestamp: number;
}

// Initialize monitoring services
export function initializeMonitoring() {
  if (typeof window === 'undefined') return;

  // Initialize Sentry in production
  if (isErrorTrackingEnabled && process.env.NODE_ENV === 'production') {
    initializeSentry();
  }

  // Initialize performance monitoring
  if (isPerformanceMonitoringEnabled) {
    initializePerformanceMonitoring();
  }
}

// Sentry configuration
function initializeSentry() {
  // In a real implementation, you would:
  // import * as Sentry from '@sentry/nextjs';
  // 
  // Sentry.init({
  //   dsn: process.env.SENTRY_DSN,
  //   environment: process.env.NODE_ENV,
  //   tracesSampleRate: 0.1,
  //   beforeSend(event) {
  //     // Filter out sensitive data
  //     return event;
  //   },
  // });
  
  console.log('Sentry monitoring initialized');
}

// Performance monitoring setup
function initializePerformanceMonitoring() {
  // Monitor Long Tasks API
  if ('PerformanceObserver' in window) {
    const longTaskObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        reportPerformanceMetric({
          name: 'long_task',
          value: entry.duration,
          unit: 'ms',
          timestamp: Date.now(),
          url: window.location.href,
          userAgent: navigator.userAgent,
        });
      }
    });

    try {
      longTaskObserver.observe({ entryTypes: ['longtask'] });
    } catch (e) {
      // Long tasks not supported
    }
  }

  // Monitor Resource Timing
  monitorResourcePerformance();
  
  console.log('Performance monitoring initialized');
}

// Resource performance monitoring
function monitorResourcePerformance() {
  if ('PerformanceObserver' in window) {
    const resourceObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        const resource = entry as PerformanceResourceTiming;
        
        // Monitor slow resources (> 1000ms)
        if (resource.duration > 1000) {
          reportPerformanceMetric({
            name: 'slow_resource',
            value: resource.duration,
            unit: 'ms',
            timestamp: Date.now(),
            url: resource.name,
          });
        }
      }
    });

    try {
      resourceObserver.observe({ entryTypes: ['resource'] });
    } catch (e) {
      // Resource timing not supported
    }
  }
}

// Report error to monitoring services
export function reportError(errorReport: ErrorReport) {
  if (!isErrorTrackingEnabled) return;

  // Log to console in development
  if (process.env.NODE_ENV === 'development') {
    console.error('Error Report:', errorReport);
  }

  // Send to Sentry in production
  if (process.env.NODE_ENV === 'production') {
    // Sentry.captureException(errorReport.error, {
    //   level: errorReport.severity,
    //   extra: errorReport.context,
    //   user: { id: errorReport.userId },
    //   tags: {
    //     url: errorReport.url,
    //     sessionId: errorReport.sessionId,
    //   },
    // });
  }

  // Send to custom analytics
  sendToAnalytics('error_occurred', {
    error_message: errorReport.error.message,
    error_severity: errorReport.severity,
    error_stack: errorReport.error.stack,
    ...errorReport.context,
  });
}

// Report performance metric
export function reportPerformanceMetric(metric: PerformanceMetric) {
  if (!isPerformanceMonitoringEnabled) return;

  // Log to console in development
  if (process.env.NODE_ENV === 'development') {
    console.log('Performance Metric:', metric);
  }

  // Send to performance monitoring service
  if (typeof window !== 'undefined' && navigator.sendBeacon) {
    try {
      navigator.sendBeacon('/api/performance', JSON.stringify(metric));
    } catch (e) {
      // Fallback to fetch
      fetch('/api/performance', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(metric),
      }).catch(() => {
        // Silently fail
      });
    }
  }

  // Send to analytics
  sendToAnalytics('performance_metric', {
    metric_name: metric.name,
    metric_value: metric.value,
    metric_unit: metric.unit,
  });
}

// Send data to analytics services
function sendToAnalytics(eventName: string, properties: Record<string, any>) {
  // Google Analytics
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', eventName, properties);
  }

  // PostHog
  if (typeof window !== 'undefined' && window.posthog) {
    window.posthog.capture(eventName, properties);
  }
}

// Utility functions for common error scenarios
export function reportCalculationError(error: Error, calculationData: any) {
  reportError({
    error,
    severity: ErrorSeverity.HIGH,
    context: {
      type: 'calculation_error',
      calculationData,
    },
    timestamp: Date.now(),
    url: typeof window !== 'undefined' ? window.location.href : undefined,
  });
}

export function reportAPIError(error: Error, endpoint: string, requestData?: any) {
  reportError({
    error,
    severity: ErrorSeverity.MEDIUM,
    context: {
      type: 'api_error',
      endpoint,
      requestData,
    },
    timestamp: Date.now(),
    url: typeof window !== 'undefined' ? window.location.href : undefined,
  });
}

export function reportUIError(error: Error, componentName: string, props?: any) {
  reportError({
    error,
    severity: ErrorSeverity.LOW,
    context: {
      type: 'ui_error',
      componentName,
      props,
    },
    timestamp: Date.now(),
    url: typeof window !== 'undefined' ? window.location.href : undefined,
  });
}

// Performance monitoring utilities
export function measureAsyncOperation<T>(
  name: string,
  operation: () => Promise<T>
): Promise<T> {
  const startTime = Date.now();
  
  return operation().finally(() => {
    const duration = Date.now() - startTime;
    
    reportPerformanceMetric({
      name: `async_operation_${name}`,
      value: duration,
      unit: 'ms',
      timestamp: Date.now(),
    });
  });
}

export function measureSyncOperation<T>(
  name: string,
  operation: () => T
): T {
  const startTime = Date.now();
  
  try {
    return operation();
  } finally {
    const duration = Date.now() - startTime;
    
    reportPerformanceMetric({
      name: `sync_operation_${name}`,
      value: duration,
      unit: 'ms',
      timestamp: Date.now(),
    });
  }
}