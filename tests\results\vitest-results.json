{"numTotalTestSuites": 40, "numPassedTestSuites": 13, "numFailedTestSuites": 27, "numPendingTestSuites": 0, "numTotalTests": 97, "numPassedTests": 54, "numFailedTests": 43, "numPendingTests": 0, "numTodoTests": 0, "snapshot": {"added": 0, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0, "didUpdate": false}, "startTime": 1752575331455, "success": false, "testResults": [{"assertionResults": [{"ancestorTitles": ["Utility Functions", "cn (className merger)"], "fullName": "Utility Functions cn (className merger) should merge simple class names", "status": "passed", "title": "should merge simple class names", "duration": 4.610070000002452, "failureMessages": [], "location": {"line": 12, "column": 5}, "meta": {}}, {"ancestorTitles": ["Utility Functions", "cn (className merger)"], "fullName": "Utility Functions cn (className merger) should handle conditional classes", "status": "passed", "title": "should handle conditional classes", "duration": 0.33974899999884656, "failureMessages": [], "location": {"line": 17, "column": 5}, "meta": {}}, {"ancestorTitles": ["Utility Functions", "cn (className merger)"], "fullName": "Utility Functions cn (className merger) should merge conflicting Tailwind classes", "status": "passed", "title": "should merge conflicting Tailwind classes", "duration": 0.20734499999889522, "failureMessages": [], "location": {"line": 30, "column": 5}, "meta": {}}, {"ancestorTitles": ["Utility Functions", "cn (className merger)"], "fullName": "Utility Functions cn (className merger) should handle complex Tailwind merges", "status": "failed", "title": "should handle complex Tailwind merges", "duration": 3.017729999999574, "failureMessages": ["AssertionError: expected 'bg-blue-500 text-black' to be 'text-white bg-blue-500 text-black' // Object.is equality\n    at /mnt/d/real estate/Nirmaan_AI_cons_calc_Claude/src/lib/__tests__/utils.test.ts:42:22\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 35, "column": 5}, "meta": {}}, {"ancestorTitles": ["Utility Functions", "cn (className merger)"], "fullName": "Utility Functions cn (className merger) should handle arrays of classes", "status": "passed", "title": "should handle arrays of classes", "duration": 0.27828299999964656, "failureMessages": [], "location": {"line": 45, "column": 5}, "meta": {}}, {"ancestorTitles": ["Utility Functions", "cn (className merger)"], "fullName": "Utility Functions cn (className merger) should handle objects with boolean values", "status": "passed", "title": "should handle objects with boolean values", "duration": 0.1658480000005511, "failureMessages": [], "location": {"line": 53, "column": 5}, "meta": {}}, {"ancestorTitles": ["Utility Functions", "cn (className merger)"], "fullName": "Utility Functions cn (className merger) should handle mixed input types", "status": "passed", "title": "should handle mixed input types", "duration": 0.10883599999942817, "failureMessages": [], "location": {"line": 63, "column": 5}, "meta": {}}, {"ancestorTitles": ["Utility Functions", "cn (className merger)"], "fullName": "Utility Functions cn (className merger) should handle empty inputs", "status": "passed", "title": "should handle empty inputs", "duration": 0.22299399999974412, "failureMessages": [], "location": {"line": 74, "column": 5}, "meta": {}}, {"ancestorTitles": ["Utility Functions", "cn (className merger)"], "fullName": "Utility Functions cn (className merger) should handle responsive classes", "status": "failed", "title": "should handle responsive classes", "duration": 0.8251019999988785, "failureMessages": ["AssertionError: expected 'text-sm lg:text-lg md:text-xl' to be 'text-sm md:text-base lg:text-lg md:te…' // Object.is equality\n    at /mnt/d/real estate/Nirmaan_AI_cons_calc_<PERSON>/src/lib/__tests__/utils.test.ts:89:22\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_<PERSON>/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_<PERSON>/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 81, "column": 5}, "meta": {}}, {"ancestorTitles": ["Utility Functions", "cn (className merger)"], "fullName": "Utility Functions cn (className merger) should handle hover and focus states", "status": "failed", "title": "should handle hover and focus states", "duration": 0.3843640000013693, "failureMessages": ["AssertionError: expected 'hover:bg-gray-200 focus:bg-gray-300 b…' to be 'bg-gray-100 hover:bg-gray-200 focus:b…' // Object.is equality\n    at /mnt/d/real estate/Nirmaan_AI_cons_calc_Claude/src/lib/__tests__/utils.test.ts:100:22\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_<PERSON>/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_<PERSON>/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 92, "column": 5}, "meta": {}}, {"ancestorTitles": ["Utility Functions", "cn (className merger)"], "fullName": "Utility Functions cn (className merger) should handle complex component styling", "status": "passed", "title": "should handle complex component styling", "duration": 0.32766800000172225, "failureMessages": [], "location": {"line": 103, "column": 5}, "meta": {}}, {"ancestorTitles": ["Utility Functions", "formatCurrency"], "fullName": "Utility Functions formatCurrency should format Indian currency correctly", "status": "passed", "title": "should format Indian currency correctly", "duration": 9.000543999998627, "failureMessages": [], "location": {"line": 141, "column": 5}, "meta": {}}, {"ancestorTitles": ["Utility Functions", "formatCurrency"], "fullName": "Utility Functions formatCurrency should handle decimal values", "status": "passed", "title": "should handle decimal values", "duration": 0.2760939999971015, "failureMessages": [], "location": {"line": 147, "column": 5}, "meta": {}}, {"ancestorTitles": ["Utility Functions", "formatCurrency"], "fullName": "Utility Functions formatCurrency should handle large numbers", "status": "passed", "title": "should handle large numbers", "duration": 0.3523520000017015, "failureMessages": [], "location": {"line": 152, "column": 5}, "meta": {}}, {"ancestorTitles": ["Utility Functions", "formatCurrency"], "fullName": "Utility Functions formatCurrency should handle zero and negative values", "status": "passed", "title": "should handle zero and negative values", "duration": 0.22685900000215042, "failureMessages": [], "location": {"line": 157, "column": 5}, "meta": {}}, {"ancestorTitles": ["Utility Functions", "formatArea"], "fullName": "Utility Functions formatArea should format area with default unit", "status": "passed", "title": "should format area with default unit", "duration": 0.16402599999855738, "failureMessages": [], "location": {"line": 168, "column": 5}, "meta": {}}, {"ancestorTitles": ["Utility Functions", "formatArea"], "fullName": "Utility Functions formatArea should format area with custom unit", "status": "passed", "title": "should format area with custom unit", "duration": 0.4053649999987101, "failureMessages": [], "location": {"line": 173, "column": 5}, "meta": {}}, {"ancestorTitles": ["Utility Functions", "formatArea"], "fullName": "Utility Functions formatArea should handle large areas", "status": "passed", "title": "should handle large areas", "duration": 0.11541700000088895, "failureMessages": [], "location": {"line": 178, "column": 5}, "meta": {}}, {"ancestorTitles": ["Utility Functions", "calculateCostPerSqFt"], "fullName": "Utility Functions calculateCostPerSqFt should calculate cost per square foot correctly", "status": "passed", "title": "should calculate cost per square foot correctly", "duration": 0.10479599999962375, "failureMessages": [], "location": {"line": 188, "column": 5}, "meta": {}}, {"ancestorTitles": ["Utility Functions", "calculateCostPerSqFt"], "fullName": "Utility Functions calculateCostPerSqFt should round to nearest integer", "status": "passed", "title": "should round to nearest integer", "duration": 0.06419499999901745, "failureMessages": [], "location": {"line": 193, "column": 5}, "meta": {}}, {"ancestorTitles": ["Utility Functions", "calculateCostPerSqFt"], "fullName": "Utility Functions calculateCostPerSqFt should handle decimal areas", "status": "passed", "title": "should handle decimal areas", "duration": 0.052734000000782544, "failureMessages": [], "location": {"line": 198, "column": 5}, "meta": {}}, {"ancestorTitles": ["Utility Functions", "slugify"], "fullName": "Utility Functions slugify should convert text to URL-friendly slug", "status": "passed", "title": "should convert text to URL-friendly slug", "duration": 0.16554399999949965, "failureMessages": [], "location": {"line": 213, "column": 5}, "meta": {}}, {"ancestorTitles": ["Utility Functions", "slugify"], "fullName": "Utility Functions slugify should handle special characters", "status": "passed", "title": "should handle special characters", "duration": 0.07052900000053342, "failureMessages": [], "location": {"line": 218, "column": 5}, "meta": {}}, {"ancestorTitles": ["Utility Functions", "slugify"], "fullName": "Utility Functions slugify should handle multiple spaces and hyphens", "status": "passed", "title": "should handle multiple spaces and hyphens", "duration": 0.06058899999698042, "failureMessages": [], "location": {"line": 223, "column": 5}, "meta": {}}, {"ancestorTitles": ["Utility Functions", "slugify"], "fullName": "Utility Functions slugify should handle empty and edge cases", "status": "passed", "title": "should handle empty and edge cases", "duration": 0.06502199999886216, "failureMessages": [], "location": {"line": 228, "column": 5}, "meta": {}}, {"ancestorTitles": ["Utility Functions", "debounce"], "fullName": "Utility Functions debounce should delay function execution", "status": "passed", "title": "should delay function execution", "duration": 1.9485739999981888, "failureMessages": [], "location": {"line": 256, "column": 5}, "meta": {}}, {"ancestorTitles": ["Utility Functions", "debounce"], "fullName": "Utility Functions debounce should cancel previous calls", "status": "passed", "title": "should cancel previous calls", "duration": 0.5254210000020976, "failureMessages": [], "location": {"line": 267, "column": 5}, "meta": {}}, {"ancestorTitles": ["Utility Functions", "debounce"], "fullName": "Utility Functions debounce should handle multiple arguments", "status": "failed", "title": "should handle multiple arguments", "duration": 0.4848100000017439, "failureMessages": ["ReferenceError: jest is not defined\n    at /mnt/d/real estate/Nirmaan_AI_cons_calc_Claude/src/lib/__tests__/utils.test.ts:287:7\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 281, "column": 5}, "meta": {}}, {"ancestorTitles": ["Utility Functions", "generateId"], "fullName": "Utility Functions generateId should generate unique IDs", "status": "passed", "title": "should generate unique IDs", "duration": 0.1966790000005858, "failureMessages": [], "location": {"line": 300, "column": 5}, "meta": {}}, {"ancestorTitles": ["Utility Functions", "generateId"], "fullName": "Utility Functions generateId should include prefix when provided", "status": "passed", "title": "should include prefix when provided", "duration": 0.14231500000096275, "failureMessages": [], "location": {"line": 309, "column": 5}, "meta": {}}, {"ancestorTitles": ["Utility Functions", "generateId"], "fullName": "Utility Functions generateId should generate IDs without prefix", "status": "passed", "title": "should generate IDs without prefix", "duration": 0.0946220000005269, "failureMessages": [], "location": {"line": 314, "column": 5}, "meta": {}}, {"ancestorTitles": ["Utility Functions", "validateEmail"], "fullName": "Utility Functions validateEmail should validate correct email addresses", "status": "passed", "title": "should validate correct email addresses", "duration": 0.21148399999947287, "failureMessages": [], "location": {"line": 326, "column": 5}, "meta": {}}, {"ancestorTitles": ["Utility Functions", "validateEmail"], "fullName": "Utility Functions validateEmail should reject invalid email addresses", "status": "failed", "title": "should reject invalid email addresses", "duration": 1.2323239999968791, "failureMessages": ["AssertionError: expected true to be false // Object.is equality\n    at /mnt/d/real estate/Nirmaan_AI_cons_calc_Claude/src/lib/__tests__/utils.test.ts:336:55\n    at file:///mnt/d/real%20estate/<PERSON>rmaan_AI_cons_calc_<PERSON>/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 332, "column": 5}, "meta": {}}, {"ancestorTitles": ["Utility Functions", "validateEmail"], "fullName": "Utility Functions validateEmail should handle edge cases", "status": "passed", "title": "should handle edge cases", "duration": 0.11732699999993201, "failureMessages": [], "location": {"line": 339, "column": 5}, "meta": {}}, {"ancestorTitles": ["Utility Functions", "deepClone"], "fullName": "Utility Functions deepClone should clone primitive values", "status": "passed", "title": "should clone primitive values", "duration": 0.12084299999696668, "failureMessages": [], "location": {"line": 363, "column": 5}, "meta": {}}, {"ancestorTitles": ["Utility Functions", "deepClone"], "fullName": "Utility Functions deepClone should clone arrays", "status": "passed", "title": "should clone arrays", "duration": 0.4928460000010091, "failureMessages": [], "location": {"line": 370, "column": 5}, "meta": {}}, {"ancestorTitles": ["Utility Functions", "deepClone"], "fullName": "Utility Functions deepClone should clone objects", "status": "passed", "title": "should clone objects", "duration": 0.24980999999752385, "failureMessages": [], "location": {"line": 379, "column": 5}, "meta": {}}, {"ancestorTitles": ["Utility Functions", "deepClone"], "fullName": "Utility Functions deepClone should clone dates", "status": "passed", "title": "should clone dates", "duration": 0.1098390000006475, "failureMessages": [], "location": {"line": 396, "column": 5}, "meta": {}}], "startTime": 1752575351455, "endTime": 1752575351483.2498, "status": "failed", "message": "", "name": "/mnt/d/real estate/<PERSON>rmaan_AI_cons_calc_<PERSON>/src/lib/__tests__/utils.test.ts"}, {"assertionResults": [{"ancestorTitles": ["Calculator API Integration Tests", "Calculation Scenarios"], "fullName": "Calculator API Integration Tests Calculation Scenarios should calculate smart tier Bangalore project correctly", "status": "passed", "title": "should calculate smart tier Bangalore project correctly", "duration": 92.23294800000076, "failureMessages": [], "location": {"line": 39, "column": 5}, "meta": {}}, {"ancestorTitles": ["Calculator API Integration Tests", "Calculation Scenarios"], "fullName": "Calculator API Integration Tests Calculation Scenarios should calculate premium tier Mumbai project correctly", "status": "failed", "title": "should calculate premium tier Mumbai project correctly", "duration": 83.7423839999974, "failureMessages": ["AssertionError: expected [ Array(1) ] to have a length of +0 but got 1\n    at Proxy.<anonymous> (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/expect/dist/index.js:1257:20)\n    at Proxy.<anonymous> (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/chai/chai.js:1706:25)\n    at /mnt/d/real estate/Nirmaan_AI_cons_calc_Claude/src/test/integration/api-integration.test.js:68:32\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 55, "column": 5}, "meta": {}}, {"ancestorTitles": ["Calculator API Integration Tests", "Calculation Scenarios"], "fullName": "Calculator API Integration Tests Calculation Scenarios should calculate luxury tier Delhi project correctly", "status": "failed", "title": "should calculate luxury tier Delhi project correctly", "duration": 72.57279100000233, "failureMessages": ["AssertionError: expected [ Array(1) ] to have a length of +0 but got 1\n    at Proxy.<anonymous> (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/expect/dist/index.js:1257:20)\n    at Proxy.<anonymous> (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/chai/chai.js:1706:25)\n    at /mnt/d/real estate/Nirmaan_AI_cons_calc_Claude/src/test/integration/api-integration.test.js:84:32\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 71, "column": 5}, "meta": {}}, {"ancestorTitles": ["Calculator API Integration Tests", "Validation Tests"], "fullName": "Calculator API Integration Tests Validation Tests should reject missing required fields", "status": "passed", "title": "should reject missing required fields", "duration": 76.20831999999791, "failureMessages": [], "location": {"line": 89, "column": 5}, "meta": {}}, {"ancestorTitles": ["Calculator API Integration Tests", "Validation Tests"], "fullName": "Calculator API Integration Tests Validation Tests should reject invalid built up area", "status": "passed", "title": "should reject invalid built up area", "duration": 84.41778300000078, "failureMessages": [], "location": {"line": 97, "column": 5}, "meta": {}}, {"ancestorTitles": ["Calculator API Integration Tests", "Validation Tests"], "fullName": "Calculator API Integration Tests Validation Tests should reject invalid quality tier", "status": "passed", "title": "should reject invalid quality tier", "duration": 78.36010700000043, "failureMessages": [], "location": {"line": 105, "column": 5}, "meta": {}}, {"ancestorTitles": ["Calculator API Integration Tests", "API Documentation"], "fullName": "Calculator API Integration Tests API Documentation should provide API documentation on GET request", "status": "passed", "title": "should provide API documentation on GET request", "duration": 80.36962700000004, "failureMessages": [], "location": {"line": 115, "column": 5}, "meta": {}}], "startTime": 1752575358752, "endTime": 1752575359320.3696, "status": "failed", "message": "", "name": "/mnt/d/real estate/<PERSON>rmaan_AI_cons_calc_<PERSON>/src/test/integration/api-integration.test.js"}, {"assertionResults": [{"ancestorTitles": ["Calculation Performance Tests", "Single Calculation Performance"], "fullName": "Calculation Performance Tests Single Calculation Performance should complete calculation within performance threshold", "status": "passed", "title": "should complete calculation within performance threshold", "duration": 2.4111880000018573, "failureMessages": [], "location": {"line": 21, "column": 5}, "meta": {}}, {"ancestorTitles": ["Calculation Performance Tests", "Single Calculation Performance"], "fullName": "Calculation Performance Tests Single Calculation Performance should handle different quality tiers with consistent performance", "status": "failed", "title": "should handle different quality tiers with consistent performance", "duration": 1.7062230000010459, "failureMessages": ["AssertionError: expected 3.319362025144694 to be less than 2\n    at /mnt/d/real estate/Nirmaan_AI_cons_calc_Claude/src/test/performance/calculation-performance.test.ts:62:29\n    at file:///mnt/d/real%20estate/<PERSON>rmaan_AI_cons_calc_<PERSON>/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_<PERSON>/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 36, "column": 5}, "meta": {}}], "startTime": 1752575351727, "endTime": 1752575351730.7063, "status": "failed", "message": "", "name": "/mnt/d/real estate/<PERSON>rmaan_AI_cons_calc_Claude/src/test/performance/calculation-performance.test.ts"}, {"assertionResults": [{"ancestorTitles": ["Calculator Engine", "calculateConstructionCost"], "fullName": "Calculator Engine calculateConstructionCost should calculate correct total cost with all components", "status": "failed", "title": "should calculate correct total cost with all components", "duration": 2.5457229999956326, "failureMessages": ["TypeError: Cannot read properties of undefined (reading 'map')\n    at applyRegionalMultipliers (/mnt/d/real estate/Nirmaan_AI_cons_calc_Claude/src/core/calculator/engine.ts:189:50)\n    at Module.calculate (/mnt/d/real estate/Nirmaan_AI_cons_calc_<PERSON>/src/core/calculator/engine.ts:58:25)\n    at /mnt/d/real estate/Nirmaan_AI_cons_calc_Claude/src/core/calculator/__tests__/engine.test.ts:82:22\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 81, "column": 5}, "meta": {}}, {"ancestorTitles": ["Calculator Engine", "calculateConstructionCost"], "fullName": "Calculator Engine calculateConstructionCost should apply location multiplier correctly", "status": "failed", "title": "should apply location multiplier correctly", "duration": 0.4233700000040699, "failureMessages": ["TypeError: Cannot read properties of undefined (reading 'map')\n    at applyRegionalMultipliers (/mnt/d/real estate/Nirmaan_AI_cons_calc_Claude/src/core/calculator/engine.ts:189:50)\n    at Module.calculate (/mnt/d/real estate/Nirmaan_AI_cons_calc_<PERSON>/src/core/calculator/engine.ts:58:25)\n    at /mnt/d/real estate/Nirmaan_AI_cons_calc_Claude/src/core/calculator/__tests__/engine.test.ts:120:31\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 116, "column": 5}, "meta": {}}, {"ancestorTitles": ["Calculator Engine", "calculateConstructionCost"], "fullName": "Calculator Engine calculateConstructionCost should calculate cost per sqft correctly", "status": "failed", "title": "should calculate cost per sqft correctly", "duration": 0.4876379999986966, "failureMessages": ["TypeError: Cannot read properties of undefined (reading 'map')\n    at applyRegionalMultipliers (/mnt/d/real estate/Nirmaan_AI_cons_calc_Claude/src/core/calculator/engine.ts:189:50)\n    at Module.calculate (/mnt/d/real estate/Nirmaan_AI_cons_calc_<PERSON>/src/core/calculator/engine.ts:58:25)\n    at /mnt/d/real estate/Nirmaan_AI_cons_calc_Claude/src/core/calculator/__tests__/engine.test.ts:135:22\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 134, "column": 5}, "meta": {}}, {"ancestorTitles": ["Calculator Engine", "calculateConstructionCost"], "fullName": "Calculator Engine calculateConstructionCost should calculate percentage breakdown correctly", "status": "failed", "title": "should calculate percentage breakdown correctly", "duration": 0.43946000000141794, "failureMessages": ["TypeError: Cannot read properties of undefined (reading 'map')\n    at applyRegionalMultipliers (/mnt/d/real estate/Nirmaan_AI_cons_calc_Claude/src/core/calculator/engine.ts:189:50)\n    at Module.calculate (/mnt/d/real estate/Nirmaan_AI_cons_calc_<PERSON>/src/core/calculator/engine.ts:58:25)\n    at /mnt/d/real estate/Nirmaan_AI_cons_calc_Claude/src/core/calculator/__tests__/engine.test.ts:145:22\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 144, "column": 5}, "meta": {}}, {"ancestorTitles": ["Calculator Engine", "calculateConstructionCost"], "fullName": "Calculator Engine calculateConstructionCost should handle different quality tiers", "status": "failed", "title": "should handle different quality tiers", "duration": 0.3831740000023274, "failureMessages": ["TypeError: Cannot read properties of undefined (reading 'map')\n    at applyRegionalMultipliers (/mnt/d/real estate/Nirmaan_AI_cons_calc_Claude/src/core/calculator/engine.ts:189:50)\n    at Module.calculate (/mnt/d/real estate/Nirmaan_AI_cons_calc_<PERSON>/src/core/calculator/engine.ts:58:25)\n    at /mnt/d/real estate/Nirmaan_AI_cons_calc_Claude/src/core/calculator/__tests__/engine.test.ts:166:27\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 161, "column": 5}, "meta": {}}, {"ancestorTitles": ["Calculator Engine", "calculateConstructionCost"], "fullName": "Calculator Engine calculateConstructionCost should handle basement inclusion", "status": "failed", "title": "should handle basement inclusion", "duration": 0.4319290000057663, "failureMessages": ["TypeError: Cannot read properties of undefined (reading 'map')\n    at applyRegionalMultipliers (/mnt/d/real estate/Nirmaan_AI_cons_calc_Claude/src/core/calculator/engine.ts:189:50)\n    at Module.calculate (/mnt/d/real estate/Nirmaan_AI_cons_calc_<PERSON>/src/core/calculator/engine.ts:58:25)\n    at /mnt/d/real estate/Nirmaan_AI_cons_calc_Claude/src/core/calculator/__tests__/engine.test.ts:183:29\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 179, "column": 5}, "meta": {}}, {"ancestorTitles": ["Calculator Engine", "calculateConstructionCost"], "fullName": "Calculator Engine calculateConstructionCost should generate correct project timeline", "status": "failed", "title": "should generate correct project timeline", "duration": 0.336950999997498, "failureMessages": ["TypeError: Cannot read properties of undefined (reading 'map')\n    at applyRegionalMultipliers (/mnt/d/real estate/Nirmaan_AI_cons_calc_Claude/src/core/calculator/engine.ts:189:50)\n    at Module.calculate (/mnt/d/real estate/Nirmaan_AI_cons_calc_<PERSON>/src/core/calculator/engine.ts:58:25)\n    at /mnt/d/real estate/Nirmaan_AI_cons_calc_Claude/src/core/calculator/__tests__/engine.test.ts:198:27\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 193, "column": 5}, "meta": {}}, {"ancestorTitles": ["Calculator Engine", "calculateConstructionCost"], "fullName": "Calculator Engine calculateConstructionCost should include quality specifications", "status": "failed", "title": "should include quality specifications", "duration": 0.3486789999951725, "failureMessages": ["TypeError: Cannot read properties of undefined (reading 'map')\n    at applyRegionalMultipliers (/mnt/d/real estate/Nirmaan_AI_cons_calc_Claude/src/core/calculator/engine.ts:189:50)\n    at Module.calculate (/mnt/d/real estate/Nirmaan_AI_cons_calc_<PERSON>/src/core/calculator/engine.ts:58:25)\n    at /mnt/d/real estate/Nirmaan_AI_cons_calc_Claude/src/core/calculator/__tests__/engine.test.ts:208:22\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 207, "column": 5}, "meta": {}}, {"ancestorTitles": ["Calculator Engine", "calculateConstructionCost"], "fullName": "Calculator Engine calculateConstructionCost should include location factors", "status": "failed", "title": "should include location factors", "duration": 0.4677139999985229, "failureMessages": ["TypeError: Cannot read properties of undefined (reading 'map')\n    at applyRegionalMultipliers (/mnt/d/real estate/Nirmaan_AI_cons_calc_Claude/src/core/calculator/engine.ts:189:50)\n    at Module.calculate (/mnt/d/real estate/Nirmaan_AI_cons_calc_<PERSON>/src/core/calculator/engine.ts:58:25)\n    at /mnt/d/real estate/Nirmaan_AI_cons_calc_Claude/src/core/calculator/__tests__/engine.test.ts:218:22\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 217, "column": 5}, "meta": {}}, {"ancestorTitles": ["Calculator Engine", "calculateConstructionCost"], "fullName": "Calculator Engine calculateConstructionCost should handle edge cases gracefully", "status": "failed", "title": "should handle edge cases gracefully", "duration": 0.5924589999995078, "failureMessages": ["Error: Validation failed: Plot area cannot be smaller than built-up area\n    at Module.calculate (/mnt/d/real estate/Nirmaan_AI_cons_calc_Claude/src/core/calculator/engine.ts:40:11)\n    at /mnt/d/real estate/Nirmaan_AI_cons_calc_Claude/src/core/calculator/__tests__/engine.test.ts:230:25\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 227, "column": 5}, "meta": {}}, {"ancestorTitles": ["Calculator Engine", "calculateConstructionCost"], "fullName": "Calculator Engine calculateConstructionCost should calculate professional fees correctly", "status": "failed", "title": "should calculate professional fees correctly", "duration": 0.3347460000004503, "failureMessages": ["TypeError: Cannot read properties of undefined (reading 'map')\n    at applyRegionalMultipliers (/mnt/d/real estate/Nirmaan_AI_cons_calc_Claude/src/core/calculator/engine.ts:189:50)\n    at Module.calculate (/mnt/d/real estate/Nirmaan_AI_cons_calc_<PERSON>/src/core/calculator/engine.ts:58:25)\n    at /mnt/d/real estate/Nirmaan_AI_cons_calc_Claude/src/core/calculator/__tests__/engine.test.ts:240:22\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 239, "column": 5}, "meta": {}}, {"ancestorTitles": ["Calculator Engine", "calculateConstructionCost"], "fullName": "Calculator Engine calculateConstructionCost should calculate statutory charges correctly", "status": "failed", "title": "should calculate statutory charges correctly", "duration": 0.2754060000006575, "failureMessages": ["TypeError: Cannot read properties of undefined (reading 'map')\n    at applyRegionalMultipliers (/mnt/d/real estate/Nirmaan_AI_cons_calc_Claude/src/core/calculator/engine.ts:189:50)\n    at Module.calculate (/mnt/d/real estate/Nirmaan_AI_cons_calc_<PERSON>/src/core/calculator/engine.ts:58:25)\n    at /mnt/d/real estate/Nirmaan_AI_cons_calc_Claude/src/core/calculator/__tests__/engine.test.ts:250:22\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 249, "column": 5}, "meta": {}}, {"ancestorTitles": ["Calculator Engine", "calculateConstructionCost"], "fullName": "Calculator Engine calculateConstructionCost should include material cost breakdown", "status": "failed", "title": "should include material cost breakdown", "duration": 0.26396000000386266, "failureMessages": ["TypeError: Cannot read properties of undefined (reading 'map')\n    at applyRegionalMultipliers (/mnt/d/real estate/Nirmaan_AI_cons_calc_Claude/src/core/calculator/engine.ts:189:50)\n    at Module.calculate (/mnt/d/real estate/Nirmaan_AI_cons_calc_<PERSON>/src/core/calculator/engine.ts:58:25)\n    at /mnt/d/real estate/Nirmaan_AI_cons_calc_Claude/src/core/calculator/__tests__/engine.test.ts:261:22\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 260, "column": 5}, "meta": {}}, {"ancestorTitles": ["Calculator Engine", "Erro<PERSON>"], "fullName": "Calculator Engine Error Handling should handle calculation errors gracefully", "status": "passed", "title": "should handle calculation errors gracefully", "duration": 0.8877309999952558, "failureMessages": [], "location": {"line": 280, "column": 5}, "meta": {}}, {"ancestorTitles": ["Calculator Engine", "Erro<PERSON>"], "fullName": "Calculator Engine Error Handling should validate input data", "status": "passed", "title": "should validate input data", "duration": 0.19278599999961443, "failureMessages": [], "location": {"line": 290, "column": 5}, "meta": {}}], "startTime": 1752575370417, "endTime": 1752575370426.1929, "status": "failed", "message": "", "name": "/mnt/d/real estate/<PERSON>rmaan_AI_cons_calc_Claude/src/core/calculator/__tests__/engine.test.ts"}, {"assertionResults": [{"ancestorTitles": ["Structure Cost Calculations", "calculateStructureCost"], "fullName": "Structure Cost Calculations calculateStructureCost should calculate correct structure cost for smart tier", "status": "passed", "title": "should calculate correct structure cost for smart tier", "duration": 1.8412879999959841, "failureMessages": [], "location": {"line": 6, "column": 5}, "meta": {}}, {"ancestorTitles": ["Structure Cost Calculations", "calculateStructureCost"], "fullName": "Structure Cost Calculations calculateStructureCost should calculate correct structure cost for premium tier", "status": "passed", "title": "should calculate correct structure cost for premium tier", "duration": 0.25473500000225613, "failureMessages": [], "location": {"line": 24, "column": 5}, "meta": {}}, {"ancestorTitles": ["Structure Cost Calculations", "calculateStructureCost"], "fullName": "Structure Cost Calculations calculateStructureCost should calculate correct structure cost for luxury tier", "status": "passed", "title": "should calculate correct structure cost for luxury tier", "duration": 0.182701999998244, "failureMessages": [], "location": {"line": 40, "column": 5}, "meta": {}}, {"ancestorTitles": ["Structure Cost Calculations", "calculateStructureCost"], "fullName": "Structure Cost Calculations calculateStructureCost should apply floor multipliers correctly", "status": "passed", "title": "should apply floor multipliers correctly", "duration": 0.25987999999779277, "failureMessages": [], "location": {"line": 56, "column": 5}, "meta": {}}, {"ancestorTitles": ["Structure Cost Calculations", "calculateStructureCost"], "fullName": "Structure Cost Calculations calculateStructureCost should add basement cost correctly", "status": "passed", "title": "should add basement cost correctly", "duration": 0.22663599999941653, "failureMessages": [], "location": {"line": 83, "column": 5}, "meta": {}}, {"ancestorTitles": ["Structure Cost Calculations", "calculateStructureCost"], "fullName": "Structure Cost Calculations calculateStructureCost should calculate breakdown percentages correctly", "status": "passed", "title": "should calculate breakdown percentages correctly", "duration": 0.5931369999962044, "failureMessages": [], "location": {"line": 102, "column": 5}, "meta": {}}, {"ancestorTitles": ["Structure Cost Calculations", "calculateStructureCost"], "fullName": "Structure Cost Calculations calculateStructureCost should have proper descriptions for each component", "status": "passed", "title": "should have proper descriptions for each component", "duration": 0.30859099999361206, "failureMessages": [], "location": {"line": 126, "column": 5}, "meta": {}}, {"ancestorTitles": ["Structure Cost Calculations", "calculateStructureCost"], "fullName": "Structure Cost Calculations calculateStructureCost should handle edge cases", "status": "passed", "title": "should handle edge cases", "duration": 0.14295900000433903, "failureMessages": [], "location": {"line": 150, "column": 5}, "meta": {}}, {"ancestorTitles": ["Structure Cost Calculations", "calculateStructureCost"], "fullName": "Structure Cost Calculations calculateStructureCost should round costs to nearest integer", "status": "passed", "title": "should round costs to nearest integer", "duration": 0.39618399999744724, "failureMessages": [], "location": {"line": 177, "column": 5}, "meta": {}}, {"ancestorTitles": ["Structure Cost Calculations", "Quality Tier Variations"], "fullName": "Structure Cost Calculations Quality Tier Variations should have increasing costs across quality tiers", "status": "passed", "title": "should have increasing costs across quality tiers", "duration": 0.2844919999988633, "failureMessages": [], "location": {"line": 204, "column": 5}, "meta": {}}, {"ancestorTitles": ["Structure Cost Calculations", "calculateFoundationCost"], "fullName": "Structure Cost Calculations calculateFoundationCost should calculate foundation cost based on soil type", "status": "failed", "title": "should calculate foundation cost based on soil type", "duration": 2.8558220000049914, "failureMessages": ["AssertionError: expected 198000.00000000003 to be 198000 // Object.is equality\n    at /mnt/d/real estate/Nirmaan_AI_cons_calc_Claude/src/core/calculator/calculations/__tests__/structure.test.ts:228:24\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_<PERSON>/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 219, "column": 5}, "meta": {}}, {"ancestorTitles": ["Structure Cost Calculations", "calculateFoundationCost"], "fullName": "Structure Cost Calculations calculateFoundationCost should increase cost with more floors", "status": "passed", "title": "should increase cost with more floors", "duration": 0.17813099999330007, "failureMessages": [], "location": {"line": 233, "column": 5}, "meta": {}}, {"ancestorTitles": ["Structure Cost Calculations", "calculateConcreteCost"], "fullName": "Structure Cost Calculations calculateConcreteCost should calculate concrete volume and cost correctly", "status": "passed", "title": "should calculate concrete volume and cost correctly", "duration": 0.24860800000169547, "failureMessages": [], "location": {"line": 247, "column": 5}, "meta": {}}, {"ancestorTitles": ["Structure Cost Calculations", "calculateSteelCost"], "fullName": "Structure Cost Calculations calculateSteelCost should calculate steel weight and cost correctly", "status": "passed", "title": "should calculate steel weight and cost correctly", "duration": 0.24583800000254996, "failureMessages": [], "location": {"line": 270, "column": 5}, "meta": {}}], "startTime": 1752575360163, "endTime": 1752575360171.2485, "status": "failed", "message": "", "name": "/mnt/d/real estate/<PERSON>rmaan_AI_cons_calc_<PERSON>/src/core/calculator/calculations/__tests__/structure.test.ts"}, {"assertionResults": [{"ancestorTitles": ["/api/calculate", "Successful Requests"], "fullName": "/api/calculate Successful Requests should return calculation result for valid request", "status": "failed", "title": "should return calculation result for valid request", "duration": 1.077939000002516, "failureMessages": ["ReferenceError: jest is not defined\n    at /mnt/d/real estate/Nirmaan_AI_cons_calc_Claude/src/app/api/calculate/__tests__/route.test.ts:22:5\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1429:21)\n    at runTest (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1566:26)\n    at runSuite (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 40, "column": 5}, "meta": {}}, {"ancestorTitles": ["/api/calculate", "Successful Requests"], "fullName": "/api/calculate Successful Requests should include proper response headers", "status": "failed", "title": "should include proper response headers", "duration": 0.2523949999958859, "failureMessages": ["ReferenceError: jest is not defined\n    at /mnt/d/real estate/Nirmaan_AI_cons_calc_Claude/src/app/api/calculate/__tests__/route.test.ts:22:5\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1429:21)\n    at runTest (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1566:26)\n    at runSuite (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 55, "column": 5}, "meta": {}}, {"ancestorTitles": ["/api/calculate", "Successful Requests"], "fullName": "/api/calculate Successful Requests should handle different quality tiers", "status": "failed", "title": "should handle different quality tiers", "duration": 0.674114000001282, "failureMessages": ["ReferenceError: jest is not defined\n    at /mnt/d/real estate/Nirmaan_AI_cons_calc_Claude/src/app/api/calculate/__tests__/route.test.ts:22:5\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1429:21)\n    at runTest (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1566:26)\n    at runSuite (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 65, "column": 5}, "meta": {}}, {"ancestorTitles": ["/api/calculate", "Successful Requests"], "fullName": "/api/calculate Successful Requests should handle different floor configurations", "status": "failed", "title": "should handle different floor configurations", "duration": 0.45407500000146683, "failureMessages": ["ReferenceError: jest is not defined\n    at /mnt/d/real estate/Nirmaan_AI_cons_calc_Claude/src/app/api/calculate/__tests__/route.test.ts:22:5\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1429:21)\n    at runTest (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1566:26)\n    at runSuite (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 82, "column": 5}, "meta": {}}, {"ancestorTitles": ["/api/calculate", "Successful Requests"], "fullName": "/api/calculate Successful Requests should handle basement inclusion", "status": "failed", "title": "should handle basement inclusion", "duration": 0.3581689999991795, "failureMessages": ["ReferenceError: jest is not defined\n    at /mnt/d/real estate/Nirmaan_AI_cons_calc_Claude/src/app/api/calculate/__tests__/route.test.ts:22:5\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1429:21)\n    at runTest (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1566:26)\n    at runSuite (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 96, "column": 5}, "meta": {}}, {"ancestorTitles": ["/api/calculate", "Input Validation"], "fullName": "/api/calculate Input Validation should reject invalid JSON", "status": "failed", "title": "should reject invalid JSON", "duration": 0.19548200000281213, "failureMessages": ["ReferenceError: jest is not defined\n    at /mnt/d/real estate/Nirmaan_AI_cons_calc_Claude/src/app/api/calculate/__tests__/route.test.ts:22:5\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1429:21)\n    at runTest (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1566:26)\n    at runSuite (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 111, "column": 5}, "meta": {}}, {"ancestorTitles": ["/api/calculate", "Input Validation"], "fullName": "/api/calculate Input Validation should validate required fields", "status": "failed", "title": "should validate required fields", "duration": 0.23439700000017183, "failureMessages": ["ReferenceError: jest is not defined\n    at /mnt/d/real estate/Nirmaan_AI_cons_calc_Claude/src/app/api/calculate/__tests__/route.test.ts:22:5\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1429:21)\n    at runTest (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1566:26)\n    at runSuite (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 126, "column": 5}, "meta": {}}, {"ancestorTitles": ["/api/calculate", "Input Validation"], "fullName": "/api/calculate Input Validation should validate data types and ranges", "status": "failed", "title": "should validate data types and ranges", "duration": 0.24926599999889731, "failureMessages": ["ReferenceError: jest is not defined\n    at /mnt/d/real estate/Nirmaan_AI_cons_calc_Claude/src/app/api/calculate/__tests__/route.test.ts:22:5\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1429:21)\n    at runTest (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1566:26)\n    at runSuite (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 143, "column": 5}, "meta": {}}, {"ancestorTitles": ["/api/calculate", "Input Validation"], "fullName": "/api/calculate Input Validation should validate area constraints", "status": "failed", "title": "should validate area constraints", "duration": 0.21044399999664165, "failureMessages": ["ReferenceError: jest is not defined\n    at /mnt/d/real estate/Nirmaan_AI_cons_calc_Claude/src/app/api/calculate/__tests__/route.test.ts:22:5\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1429:21)\n    at runTest (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1566:26)\n    at runSuite (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 162, "column": 5}, "meta": {}}, {"ancestorTitles": ["/api/calculate", "Input Validation"], "fullName": "/api/calculate Input Validation should validate location", "status": "failed", "title": "should validate location", "duration": 0.23616000000038184, "failureMessages": ["ReferenceError: jest is not defined\n    at /mnt/d/real estate/Nirmaan_AI_cons_calc_Claude/src/app/api/calculate/__tests__/route.test.ts:22:5\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1429:21)\n    at runTest (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1566:26)\n    at runSuite (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 182, "column": 5}, "meta": {}}, {"ancestorTitles": ["/api/calculate", "Input Validation"], "fullName": "/api/calculate Input Validation should validate parking type", "status": "failed", "title": "should validate parking type", "duration": 0.14673199999378994, "failureMessages": ["ReferenceError: jest is not defined\n    at /mnt/d/real estate/Nirmaan_AI_cons_calc_Claude/src/app/api/calculate/__tests__/route.test.ts:22:5\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1429:21)\n    at runTest (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1566:26)\n    at runSuite (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 196, "column": 5}, "meta": {}}, {"ancestorTitles": ["/api/calculate", "Erro<PERSON>"], "fullName": "/api/calculate Error Handling should handle calculation engine errors", "status": "failed", "title": "should handle calculation engine errors", "duration": 0.1347269999969285, "failureMessages": ["ReferenceError: jest is not defined\n    at /mnt/d/real estate/Nirmaan_AI_cons_calc_Claude/src/app/api/calculate/__tests__/route.test.ts:22:5\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1429:21)\n    at runTest (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1566:26)\n    at runSuite (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 208, "column": 5}, "meta": {}}, {"ancestorTitles": ["/api/calculate", "Erro<PERSON>"], "fullName": "/api/calculate Error Handling should handle unexpected errors", "status": "failed", "title": "should handle unexpected errors", "duration": 0.18804699999600416, "failureMessages": ["ReferenceError: jest is not defined\n    at /mnt/d/real estate/Nirmaan_AI_cons_calc_Claude/src/app/api/calculate/__tests__/route.test.ts:22:5\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1429:21)\n    at runTest (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1566:26)\n    at runSuite (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 223, "column": 5}, "meta": {}}, {"ancestorTitles": ["/api/calculate", "Erro<PERSON>"], "fullName": "/api/calculate Error Handling should include request ID in error responses", "status": "failed", "title": "should include request ID in error responses", "duration": 0.268393999998807, "failureMessages": ["ReferenceError: jest is not defined\n    at /mnt/d/real estate/Nirmaan_AI_cons_calc_Claude/src/app/api/calculate/__tests__/route.test.ts:22:5\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1429:21)\n    at runTest (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1566:26)\n    at runSuite (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 238, "column": 5}, "meta": {}}, {"ancestorTitles": ["/api/calculate", "HTTP Methods"], "fullName": "/api/calculate HTTP Methods should only accept POST requests", "status": "failed", "title": "should only accept POST requests", "duration": 0.1421909999990021, "failureMessages": ["ReferenceError: jest is not defined\n    at /mnt/d/real estate/Nirmaan_AI_cons_calc_Claude/src/app/api/calculate/__tests__/route.test.ts:22:5\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1429:21)\n    at runTest (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1566:26)\n    at runSuite (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 248, "column": 5}, "meta": {}}, {"ancestorTitles": ["/api/calculate", "Security Headers"], "fullName": "/api/calculate Security Headers should include security headers in response", "status": "failed", "title": "should include security headers in response", "duration": 0.2531119999985094, "failureMessages": ["ReferenceError: jest is not defined\n    at /mnt/d/real estate/Nirmaan_AI_cons_calc_Claude/src/app/api/calculate/__tests__/route.test.ts:22:5\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1429:21)\n    at runTest (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1566:26)\n    at runSuite (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 261, "column": 5}, "meta": {}}, {"ancestorTitles": ["/api/calculate", "Performance"], "fullName": "/api/calculate Performance should complete calculation within reasonable time", "status": "failed", "title": "should complete calculation within reasonable time", "duration": 0.1343750000014552, "failureMessages": ["ReferenceError: jest is not defined\n    at /mnt/d/real estate/Nirmaan_AI_cons_calc_Claude/src/app/api/calculate/__tests__/route.test.ts:22:5\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1429:21)\n    at runTest (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1566:26)\n    at runSuite (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 272, "column": 5}, "meta": {}}, {"ancestorTitles": ["/api/calculate", "Performance"], "fullName": "/api/calculate Performance should include processing time in response", "status": "failed", "title": "should include processing time in response", "duration": 0.09754899999825284, "failureMessages": ["ReferenceError: jest is not defined\n    at /mnt/d/real estate/Nirmaan_AI_cons_calc_Claude/src/app/api/calculate/__tests__/route.test.ts:22:5\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1429:21)\n    at runTest (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1566:26)\n    at runSuite (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 285, "column": 5}, "meta": {}}, {"ancestorTitles": ["/api/calculate", "Edge Cases"], "fullName": "/api/calculate Edge Cases should handle very large areas", "status": "failed", "title": "should handle very large areas", "duration": 0.12093000000459142, "failureMessages": ["ReferenceError: jest is not defined\n    at /mnt/d/real estate/Nirmaan_AI_cons_calc_Claude/src/app/api/calculate/__tests__/route.test.ts:22:5\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1429:21)\n    at runTest (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1566:26)\n    at runSuite (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 297, "column": 5}, "meta": {}}, {"ancestorTitles": ["/api/calculate", "Edge Cases"], "fullName": "/api/calculate Edge Cases should handle minimum viable inputs", "status": "failed", "title": "should handle minimum viable inputs", "duration": 0.09146899999905145, "failureMessages": ["ReferenceError: jest is not defined\n    at /mnt/d/real estate/Nirmaan_AI_cons_calc_Claude/src/app/api/calculate/__tests__/route.test.ts:22:5\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1429:21)\n    at runTest (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1566:26)\n    at runSuite (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 310, "column": 5}, "meta": {}}, {"ancestorTitles": ["/api/calculate", "Content Type Handling"], "fullName": "/api/calculate Content Type Handling should reject non-JSON content type", "status": "failed", "title": "should reject non-JSON content type", "duration": 0.09402499999850988, "failureMessages": ["ReferenceError: jest is not defined\n    at /mnt/d/real estate/Nirmaan_AI_cons_calc_Claude/src/app/api/calculate/__tests__/route.test.ts:22:5\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1429:21)\n    at runTest (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1566:26)\n    at runSuite (file:///mnt/d/real%20estate/Nirmaan_AI_cons_calc_Claude/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 332, "column": 5}, "meta": {}}], "startTime": 1752575372252, "endTime": 1752575372258.1343, "status": "failed", "message": "", "name": "/mnt/d/real estate/<PERSON>rmaan_AI_cons_calc_<PERSON>/src/app/api/calculate/__tests__/route.test.ts"}]}