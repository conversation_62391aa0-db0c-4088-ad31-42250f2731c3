import type { 
  CalculationInput,
  CalculationResult
} from '@/core/calculator/types';

// Mock Calculator Form Data
export const mockCalculatorFormData: CalculationInput = {
  plotArea: 1000,
  builtUpArea: 800,
  floors: 1,
  qualityTier: 'smart',
  location: 'bangalore',
  hasBasement: false,
  parkingType: 'open',
};

// Generate form data with overrides
export const generateFormData = (overrides: Partial<CalculationInput> = {}): CalculationInput => ({
  ...mockCalculatorFormData,
  ...overrides,
});

// Mock API Response
export const mockApiResponse: CalculationResult = {
  totalCost: 1500000,
  costPerSqft: 1875,
  breakdown: {
    structure: {
      amount: 600000,
      percentage: 40,
      subCategories: [
        { name: 'Foundation', amount: 120000, percentage: 20, description: 'Foundation work' },
        { name: 'Columns & Beams', amount: 180000, percentage: 30, description: 'RCC columns and beams' },
        { name: 'Slabs', amount: 240000, percentage: 40, description: 'RCC slabs' },
        { name: 'Walls', amount: 60000, percentage: 10, description: 'Brick walls' },
      ],
    },
    finishing: {
      amount: 450000,
      percentage: 30,
      subCategories: [
        { name: 'Flooring', amount: 180000, percentage: 40, description: 'Flooring work' },
        { name: 'Paint', amount: 135000, percentage: 30, description: 'Painting work' },
        { name: 'Doors & Windows', amount: 135000, percentage: 30, description: 'Doors and windows' },
      ],
    },
    mep: {
      amount: 300000,
      percentage: 20,
      subCategories: [
        { name: 'Electrical', amount: 150000, percentage: 50, description: 'Electrical work' },
        { name: 'Plumbing', amount: 120000, percentage: 40, description: 'Plumbing work' },
        { name: 'HVAC', amount: 30000, percentage: 10, description: 'HVAC provision' },
      ],
    },
    external: {
      amount: 150000,
      percentage: 10,
      subCategories: [
        { name: 'Compound Wall', amount: 75000, percentage: 50, description: 'Compound wall' },
        { name: 'Landscaping', amount: 45000, percentage: 30, description: 'Basic landscaping' },
        { name: 'Parking', amount: 30000, percentage: 20, description: 'Parking area' },
      ],
    },
    other: {
      amount: 0,
      percentage: 0,
      subCategories: [],
    },
    total: 1500000,
  },
  materials: [
    {
      category: 'Cement',
      name: 'OPC 43 Grade Cement',
      quantity: 300,
      unit: 'bags',
      rate: 420,
      totalCost: 126000,
      purpose: 'RCC work and masonry',
      specifications: 'Grade: M20',
    },
    {
      category: 'Steel',
      name: 'TMT Steel Fe415',
      quantity: 6000,
      unit: 'kg',
      rate: 65,
      totalCost: 390000,
      purpose: 'RCC reinforcement',
      specifications: 'Grade: Fe415',
    },
  ],
  timeline: [
    {
      name: 'Foundation',
      duration: 4,
      startAfter: 0,
      description: 'Foundation and basement work',
    },
    {
      name: 'Superstructure',
      duration: 8,
      startAfter: 4,
      description: 'RCC structure work',
    },
    {
      name: 'Finishing',
      duration: 6,
      startAfter: 12,
      description: 'Finishing and MEP work',
    },
  ],
  summary: {
    totalBuiltUpArea: 1600,
    carpetArea: 1120,
    constructionDuration: 6,
    totalCost: 1500000,
    costPerSqft: 1875,
    qualityTier: 'smart',
    location: 'bangalore',
    estimateAccuracy: '±15%',
  },
};

// Mock Error Responses
export const mockErrorResponses = {
  validation: {
    success: false,
    error: 'VALIDATION_FAILED',
    details: { missing: ['builtUpArea'] },
  },
  serverError: {
    success: false,
    error: 'SERVER_ERROR',
    message: 'Internal server error',
  },
};

// Mock Locations
export const mockLocations = [
  { id: 'bangalore', name: 'Bangalore', state: 'Karnataka', multiplier: 0.95 },
  { id: 'mumbai', name: 'Mumbai', state: 'Maharashtra', multiplier: 1.2 },
  { id: 'delhi', name: 'Delhi', state: 'Delhi', multiplier: 1.0 },
];