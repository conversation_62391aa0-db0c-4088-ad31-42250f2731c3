/**
 * Enhanced MVP Design System Configuration
 * Transforms calculator from basic to premium TurboTax-like experience
 */

export const designSystem = {
  // Modern Color Palette - Premium Brand Colors
  colors: {
    primary: {
      50: '#eff6ff',
      100: '#dbeafe', 
      200: '#bfdbfe',
      300: '#93c5fd',
      400: '#60a5fa',
      500: '#3b82f6', // Main brand blue
      600: '#2563eb',
      700: '#1d4ed8',
      800: '#1e40af',
      900: '#1e3a8a',
    },
    secondary: {
      50: '#f8fafc',
      100: '#f1f5f9',
      200: '#e2e8f0',
      300: '#cbd5e1',
      400: '#94a3b8',
      500: '#64748b',
      600: '#475569',
      700: '#334155',
      800: '#1e293b',
      900: '#0f172a',
    },
    success: '#10b981',
    warning: '#f59e0b', 
    error: '#ef4444',
    background: '#ffffff',
    foreground: '#0f172a',
    muted: '#f1f5f9',
    border: '#e2e8f0',
    card: '#ffffff',
    accent: '#8b5cf6', // Purple accent for premium feel
  },

  // Professional Typography Scale
  typography: {
    fontFamily: {
      sans: ['Inter', 'system-ui', 'sans-serif'],
      mono: ['JetBrains Mono', 'monospace'],
      display: ['Poppins', 'Inter', 'sans-serif'], // For headings
    },
    fontSize: {
      xs: '0.75rem',
      sm: '0.875rem', 
      base: '1rem',
      lg: '1.125rem',
      xl: '1.25rem',
      '2xl': '1.5rem',
      '3xl': '1.875rem',
      '4xl': '2.25rem',
      '5xl': '3rem',
    },
    fontWeight: {
      normal: '400',
      medium: '500',
      semibold: '600',
      bold: '700',
    },
    lineHeight: {
      tight: '1.25',
      snug: '1.375',
      normal: '1.5',
      relaxed: '1.625',
    },
  },

  // Consistent Spacing Scale
  spacing: {
    xs: '0.5rem',   // 8px
    sm: '1rem',     // 16px
    md: '1.5rem',   // 24px
    lg: '2rem',     // 32px
    xl: '3rem',     // 48px
    '2xl': '4rem',  // 64px
    '3xl': '6rem',  // 96px
  },

  // Modern Border Radius
  borderRadius: {
    none: '0',
    sm: '0.375rem',   // 6px
    md: '0.5rem',     // 8px
    lg: '0.75rem',    // 12px
    xl: '1rem',       // 16px
    '2xl': '1.5rem',  // 24px
    full: '9999px',
  },

  // Professional Shadows
  shadows: {
    xs: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
    sm: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
    md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
    lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
    xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
    inner: 'inset 0 2px 4px 0 rgb(0 0 0 / 0.05)',
  },

  // Smooth Animations
  animation: {
    fast: '150ms ease-out',
    normal: '300ms ease-out',
    slow: '500ms ease-out',
    bounce: '600ms cubic-bezier(0.68, -0.55, 0.265, 1.55)',
  },

  // Component Variants
  components: {
    button: {
      sizes: {
        sm: {
          padding: '0.5rem 1rem',
          fontSize: '0.875rem',
          borderRadius: '0.375rem',
        },
        md: {
          padding: '0.75rem 1.5rem',
          fontSize: '1rem',
          borderRadius: '0.5rem',
        },
        lg: {
          padding: '1rem 2rem',
          fontSize: '1.125rem',
          borderRadius: '0.75rem',
        },
      },
      variants: {
        primary: {
          background: 'var(--primary-500)',
          color: 'white',
          hover: 'var(--primary-600)',
        },
        secondary: {
          background: 'var(--secondary-100)',
          color: 'var(--secondary-700)',
          hover: 'var(--secondary-200)',
        },
        outline: {
          background: 'transparent',
          border: '1px solid var(--border)',
          color: 'var(--foreground)',
          hover: 'var(--muted)',
        },
      },
    },
    card: {
      padding: 'var(--spacing-lg)',
      borderRadius: 'var(--radius-lg)',
      boxShadow: 'var(--shadow-md)',
      background: 'var(--card)',
      border: '1px solid var(--border)',
    },
  },

  // Breakpoints for Responsive Design
  breakpoints: {
    sm: '640px',
    md: '768px',
    lg: '1024px',
    xl: '1280px',
    '2xl': '1536px',
  },

  // Z-index Scale
  zIndex: {
    dropdown: 1000,
    sticky: 1020,
    fixed: 1030,
    backdrop: 1040,
    modal: 1050,
    popover: 1060,
    tooltip: 1070,
  },
};

export type DesignSystem = typeof designSystem;

// Enhanced CSS Custom Properties for Runtime Access
export const cssVariables = {
  // Primary Color Scale
  '--primary-50': designSystem.colors.primary[50],
  '--primary-100': designSystem.colors.primary[100],
  '--primary-200': designSystem.colors.primary[200],
  '--primary-300': designSystem.colors.primary[300],
  '--primary-400': designSystem.colors.primary[400],
  '--primary-500': designSystem.colors.primary[500],
  '--primary-600': designSystem.colors.primary[600],
  '--primary-700': designSystem.colors.primary[700],
  '--primary-800': designSystem.colors.primary[800],
  '--primary-900': designSystem.colors.primary[900],
  '--primary-950': '#172554',
  
  // Secondary Color Scale
  '--secondary-50': designSystem.colors.secondary[50],
  '--secondary-100': designSystem.colors.secondary[100],
  '--secondary-200': designSystem.colors.secondary[200],
  '--secondary-300': designSystem.colors.secondary[300],
  '--secondary-400': designSystem.colors.secondary[400],
  '--secondary-500': designSystem.colors.secondary[500],
  '--secondary-600': designSystem.colors.secondary[600],
  '--secondary-700': designSystem.colors.secondary[700],
  '--secondary-800': designSystem.colors.secondary[800],
  '--secondary-900': designSystem.colors.secondary[900],
  '--secondary-950': '#020617',

  // Semantic Colors
  '--success-50': '#ecfdf5',
  '--success-500': designSystem.colors.success,
  '--success-600': '#059669',
  '--warning-50': '#fffbeb',
  '--warning-500': designSystem.colors.warning,
  '--warning-600': '#d97706',
  '--error-50': '#fef2f2',
  '--error-500': designSystem.colors.error,
  '--error-600': '#dc2626',
  '--info-50': '#eff6ff',
  '--info-500': '#3b82f6',
  '--info-600': '#2563eb',

  // Accent Colors
  '--accent-50': '#faf5ff',
  '--accent-500': designSystem.colors.accent,
  '--accent-600': '#9333ea',

  // Base Colors
  '--background': designSystem.colors.background,
  '--foreground': designSystem.colors.foreground,
  '--muted': designSystem.colors.muted,
  '--border': designSystem.colors.border,
  '--card': designSystem.colors.card,

  // Typography Scale
  '--typography-xs': 'clamp(0.75rem, 0.7rem + 0.25vw, 0.875rem)',
  '--typography-sm': 'clamp(0.875rem, 0.825rem + 0.25vw, 1rem)',
  '--typography-base': 'clamp(1rem, 0.95rem + 0.25vw, 1.125rem)',
  '--typography-lg': 'clamp(1.125rem, 1.05rem + 0.375vw, 1.25rem)',
  '--typography-xl': 'clamp(1.25rem, 1.15rem + 0.5vw, 1.5rem)',
  '--typography-2xl': 'clamp(1.5rem, 1.35rem + 0.75vw, 1.875rem)',
  '--typography-3xl': 'clamp(1.875rem, 1.65rem + 1.125vw, 2.25rem)',
  '--typography-4xl': 'clamp(2.25rem, 1.95rem + 1.5vw, 3rem)',
  '--typography-5xl': 'clamp(3rem, 2.55rem + 2.25vw, 4rem)',
  '--typography-6xl': 'clamp(4rem, 3.4rem + 3vw, 5.5rem)',

  // Font Families
  '--font-inter': "'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif",
  '--font-poppins': "'Poppins', 'Inter', system-ui, sans-serif",
  '--font-mono': "'JetBrains Mono', 'Fira Code', monospace",

  // Font Weights
  '--font-light': '300',
  '--font-normal': '400',
  '--font-medium': '500',
  '--font-semibold': '600',
  '--font-bold': '700',

  // Line Heights
  '--leading-none': '1',
  '--leading-tight': '1.25',
  '--leading-snug': '1.375',
  '--leading-normal': '1.5',
  '--leading-relaxed': '1.625',
  '--leading-loose': '2',

  // Letter Spacing
  '--tracking-tight': '-0.025em',
  '--tracking-normal': '0em',
  '--tracking-wide': '0.025em',
  '--tracking-wider': '0.05em',
  '--tracking-widest': '0.1em',

  // Spacing Scale
  '--spacing-0': '0',
  '--spacing-1': '0.25rem',
  '--spacing-2': '0.5rem',
  '--spacing-3': '0.75rem',
  '--spacing-4': '1rem',
  '--spacing-5': '1.25rem',
  '--spacing-6': '1.5rem',
  '--spacing-8': '2rem',
  '--spacing-10': '2.5rem',
  '--spacing-12': '3rem',
  '--spacing-16': '4rem',
  '--spacing-20': '5rem',
  '--spacing-24': '6rem',
  '--spacing-32': '8rem',

  // Border Radius
  '--radius-none': '0',
  '--radius-sm': designSystem.borderRadius.sm,
  '--radius-md': designSystem.borderRadius.md,
  '--radius-lg': designSystem.borderRadius.lg,
  '--radius-xl': designSystem.borderRadius.xl,
  '--radius-2xl': designSystem.borderRadius['2xl'],
  '--radius-full': '9999px',

  // Shadows
  '--shadow-xs': designSystem.shadows.xs,
  '--shadow-sm': designSystem.shadows.sm,
  '--shadow-md': designSystem.shadows.md,
  '--shadow-lg': designSystem.shadows.lg,
  '--shadow-xl': designSystem.shadows.xl,
  '--shadow-2xl': '0 25px 50px -12px rgb(0 0 0 / 0.25)',
  '--shadow-inner': designSystem.shadows.inner,

  // Animations
  '--animation-fast': designSystem.animation.fast,
  '--animation-normal': designSystem.animation.normal,
  '--animation-slow': designSystem.animation.slow,
  '--animation-bounce': '600ms cubic-bezier(0.68, -0.55, 0.265, 1.55)',

  // Z-Index Scale
  '--z-dropdown': '1000',
  '--z-sticky': '1020',
  '--z-fixed': '1030',
  '--z-backdrop': '1040',
  '--z-modal': '1050',
  '--z-popover': '1060',
  '--z-tooltip': '1070',
  '--z-toast': '1080',
};

/**
 * Apply CSS variables to the document root
 * Useful for dynamic theme switching
 */
export const applyCSSVariables = (variables: Record<string, string> = cssVariables) => {
  if (typeof document !== 'undefined') {
    const root = document.documentElement;
    Object.entries(variables).forEach(([property, value]) => {
      root.style.setProperty(property, value);
    });
  }
};

/**
 * Theme switching utility
 */
export const toggleTheme = () => {
  if (typeof document !== 'undefined') {
    document.documentElement.classList.toggle('dark');
  }
};

/**
 * Get current theme
 */
export const getCurrentTheme = (): 'light' | 'dark' => {
  if (typeof document !== 'undefined') {
    return document.documentElement.classList.contains('dark') ? 'dark' : 'light';
  }
  return 'light';
};