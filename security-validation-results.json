{"timestamp": "2025-07-16T02:48:34.180Z", "score": 90, "total": 72, "passed": 65, "failed": 7, "categories": {"Files": {"passed": 9, "total": 9, "failed": []}, "Middleware": {"passed": 10, "total": 10, "failed": []}, "Headers": {"passed": 10, "total": 10, "failed": []}, "Rate Limiter": {"passed": 10, "total": 10, "failed": []}, "Vulnerability Scanner": {"passed": 11, "total": 12, "failed": [{"category": "Vulnerability Scanner", "test": "XSS Detection", "passed": false, "details": "❌ Missing"}]}, "Input Sanitizer": {"passed": 5, "total": 8, "failed": [{"category": "Input Sanitizer", "test": "SQL Injection Prevention", "passed": false, "details": "❌ Missing"}, {"category": "Input Sanitizer", "test": "File Upload Security", "passed": false, "details": "❌ Missing"}, {"category": "Input Sanitizer", "test": "URL Validation", "passed": false, "details": "❌ Missing"}]}, "CORS": {"passed": 5, "total": 6, "failed": [{"category": "CORS", "test": "Origin Validation", "passed": false, "details": "❌ Missing"}]}, "Monitoring": {"passed": 5, "total": 7, "failed": [{"category": "Monitoring", "test": "Threat Intelligence", "passed": false, "details": "❌ Missing"}, {"category": "Monitoring", "test": "Audit Trail", "passed": false, "details": "❌ Missing"}]}}, "results": [{"category": "Files", "test": "middleware.ts", "passed": true, "details": "✅ File exists"}, {"category": "Files", "test": "src/lib/security/headers.ts", "passed": true, "details": "✅ File exists"}, {"category": "Files", "test": "src/lib/security/advanced-rate-limiter.ts", "passed": true, "details": "✅ File exists"}, {"category": "Files", "test": "src/lib/security/vulnerability-scanner.ts", "passed": true, "details": "✅ File exists"}, {"category": "Files", "test": "src/lib/security/input-sanitizer.ts", "passed": true, "details": "✅ File exists"}, {"category": "Files", "test": "src/lib/security/cors-config.ts", "passed": true, "details": "✅ File exists"}, {"category": "Files", "test": "src/lib/security/security-monitor.ts", "passed": true, "details": "✅ File exists"}, {"category": "Files", "test": "src/lib/security/security-tester.ts", "passed": true, "details": "✅ File exists"}, {"category": "Files", "test": "src/lib/security/incident-response.ts", "passed": true, "details": "✅ File exists"}, {"category": "Middleware", "test": "Production Security Middleware", "passed": true, "details": "✅ Implemented"}, {"category": "Middleware", "test": "Rate Limiting Integration", "passed": true, "details": "✅ Implemented"}, {"category": "Middleware", "test": "Vulnerability Scanning", "passed": true, "details": "✅ Implemented"}, {"category": "Middleware", "test": "Security Headers", "passed": true, "details": "✅ Implemented"}, {"category": "Middleware", "test": "Geographic Blocking", "passed": true, "details": "✅ Implemented"}, {"category": "Middleware", "test": "Bot Detection", "passed": true, "details": "✅ Implemented"}, {"category": "Middleware", "test": "Path Traversal Protection", "passed": true, "details": "✅ Implemented"}, {"category": "Middleware", "test": "Critical Path Protection", "passed": true, "details": "✅ Implemented"}, {"category": "Middleware", "test": "Request Size Validation", "passed": true, "details": "✅ Implemented"}, {"category": "Middleware", "test": "Content Type Validation", "passed": true, "details": "✅ Implemented"}, {"category": "Headers", "test": "Content Security Policy", "passed": true, "details": "✅ Configured"}, {"category": "Headers", "test": "X-Content-Type-Options", "passed": true, "details": "✅ Configured"}, {"category": "Headers", "test": "X-Frame-Options", "passed": true, "details": "✅ Configured"}, {"category": "Headers", "test": "X-XSS-Protection", "passed": true, "details": "✅ Configured"}, {"category": "Headers", "test": "Strict-Transport-Security", "passed": true, "details": "✅ Configured"}, {"category": "Headers", "test": "Referrer-Policy", "passed": true, "details": "✅ Configured"}, {"category": "Headers", "test": "Permissions-Policy", "passed": true, "details": "✅ Configured"}, {"category": "Headers", "test": "Cross-Origin Policies", "passed": true, "details": "✅ Configured"}, {"category": "Headers", "test": "Nonce Generation", "passed": true, "details": "✅ Configured"}, {"category": "Headers", "test": "CSP Builder", "passed": true, "details": "✅ Configured"}, {"category": "Rate Limiter", "test": "Multi-tier Rate Limiting", "passed": true, "details": "✅ Implemented"}, {"category": "Rate Limiter", "test": "VIP Tier Implementation", "passed": true, "details": "✅ Implemented"}, {"category": "Rate Limiter", "test": "Anonymous Tier Implementation", "passed": true, "details": "✅ Implemented"}, {"category": "Rate Limiter", "test": "Bot Detection Tier", "passed": true, "details": "✅ Implemented"}, {"category": "Rate Limiter", "test": "Burst Protection", "passed": true, "details": "✅ Implemented"}, {"category": "Rate Limiter", "test": "Progressive Blocking", "passed": true, "details": "✅ Implemented"}, {"category": "Rate Limiter", "test": "IP Blocking", "passed": true, "details": "✅ Implemented"}, {"category": "Rate Limiter", "test": "Client Statistics", "passed": true, "details": "✅ Implemented"}, {"category": "Rate Limiter", "test": "Cleanup Task", "passed": true, "details": "✅ Implemented"}, {"category": "Rate Limiter", "test": "Rate Limit Result", "passed": true, "details": "✅ Implemented"}, {"category": "Vulnerability Scanner", "test": "SQL Injection Detection", "passed": true, "details": "✅ Implemented"}, {"category": "Vulnerability Scanner", "test": "XSS Detection", "passed": false, "details": "❌ Missing"}, {"category": "Vulnerability Scanner", "test": "Path Traversal Detection", "passed": true, "details": "✅ Implemented"}, {"category": "Vulnerability Scanner", "test": "Command Injection Detection", "passed": true, "details": "✅ Implemented"}, {"category": "Vulnerability Scanner", "test": "Information Disclosure", "passed": true, "details": "✅ Implemented"}, {"category": "Vulnerability Scanner", "test": "Authentication Bypass", "passed": true, "details": "✅ Implemented"}, {"category": "Vulnerability Scanner", "test": "DoS Detection", "passed": true, "details": "✅ Implemented"}, {"category": "Vulnerability Scanner", "test": "CSRF Detection", "passed": true, "details": "✅ Implemented"}, {"category": "Vulnerability Scanner", "test": "Vulnerability Rules", "passed": true, "details": "✅ Implemented"}, {"category": "Vulnerability Scanner", "test": "Risk Scoring", "passed": true, "details": "✅ Implemented"}, {"category": "Vulnerability Scanner", "test": "OWASP Mapping", "passed": true, "details": "✅ Implemented"}, {"category": "Vulnerability Scanner", "test": "CWE Mapping", "passed": true, "details": "✅ Implemented"}, {"category": "Input Sanitizer", "test": "File Exists", "passed": true, "details": "✅ File exists"}, {"category": "Input Sanitizer", "test": "HTML Sanitization", "passed": true, "details": "✅ Implemented"}, {"category": "Input Sanitizer", "test": "SQL Injection Prevention", "passed": false, "details": "❌ Missing"}, {"category": "Input Sanitizer", "test": "XSS Prevention", "passed": true, "details": "✅ Implemented"}, {"category": "Input Sanitizer", "test": "File Upload Security", "passed": false, "details": "❌ Missing"}, {"category": "Input Sanitizer", "test": "Email Validation", "passed": true, "details": "✅ Implemented"}, {"category": "Input Sanitizer", "test": "URL Validation", "passed": false, "details": "❌ Missing"}, {"category": "Input Sanitizer", "test": "Phone Validation", "passed": true, "details": "✅ Implemented"}, {"category": "CORS", "test": "File Exists", "passed": true, "details": "✅ File exists"}, {"category": "CORS", "test": "Origin Validation", "passed": false, "details": "❌ Missing"}, {"category": "CORS", "test": "Credentials Control", "passed": true, "details": "✅ Configured"}, {"category": "CORS", "test": "Methods Control", "passed": true, "details": "✅ Configured"}, {"category": "CORS", "test": "Headers Control", "passed": true, "details": "✅ Configured"}, {"category": "CORS", "test": "Geographic Control", "passed": true, "details": "✅ Configured"}, {"category": "Monitoring", "test": "File Exists", "passed": true, "details": "✅ File exists"}, {"category": "Monitoring", "test": "Real-time Monitoring", "passed": true, "details": "✅ Implemented"}, {"category": "Monitoring", "test": "Incident Tracking", "passed": true, "details": "✅ Implemented"}, {"category": "Monitoring", "test": "Alert System", "passed": true, "details": "✅ Implemented"}, {"category": "Monitoring", "test": "Security Events", "passed": true, "details": "✅ Implemented"}, {"category": "Monitoring", "test": "Threat Intelligence", "passed": false, "details": "❌ Missing"}, {"category": "Monitoring", "test": "Audit Trail", "passed": false, "details": "❌ Missing"}]}