{"config": {"configFile": "/mnt/d/real estate/<PERSON><PERSON>an_AI_cons_calc_<PERSON>/playwright.enhanced.config.ts", "rootDir": "/mnt/d/real estate/<PERSON>rmaan_AI_cons_calc_Claude/tests/e2e", "forbidOnly": false, "fullyParallel": true, "globalSetup": "/mnt/d/real estate/<PERSON>rmaan_AI_cons_calc_Claude/tests/e2e/setup/global-setup.ts", "globalTeardown": "/mnt/d/real estate/<PERSON>rmaan_AI_cons_calc_Claude/tests/e2e/setup/global-teardown.ts", "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"testSuite": "Nirmaan AI Construction Calculator E2E Tests", "version": "1.0.0", "environment": "test", "baseURL": "http://localhost:3000", "timestamp": "2025-07-15T10:15:40.264Z"}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "tests/e2e/reports/html"}], ["json", {"outputFile": "tests/e2e/reports/results.json"}], ["junit", {"outputFile": "tests/e2e/reports/results.xml"}], ["line", null]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/mnt/d/real estate/<PERSON>rmaan_AI_cons_calc_Claude/tests/e2e/results", "repeatEach": 1, "retries": 0, "metadata": {"testSuite": "Nirmaan AI Construction Calculator E2E Tests", "version": "1.0.0", "environment": "test", "baseURL": "http://localhost:3000", "timestamp": "2025-07-15T10:15:40.264Z"}, "id": "setup", "name": "setup", "testDir": "/mnt/d/real estate/<PERSON>rmaan_AI_cons_calc_Claude/tests/e2e", "testIgnore": [], "testMatch": ["/.*\\.setup\\.ts/"], "timeout": 60000}, {"outputDir": "/mnt/d/real estate/<PERSON>rmaan_AI_cons_calc_Claude/tests/e2e/results", "repeatEach": 1, "retries": 0, "metadata": {"testSuite": "Nirmaan AI Construction Calculator E2E Tests", "version": "1.0.0", "environment": "test", "baseURL": "http://localhost:3000", "timestamp": "2025-07-15T10:15:40.264Z"}, "id": "chromium", "name": "chromium", "testDir": "/mnt/d/real estate/<PERSON>rmaan_AI_cons_calc_Claude/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "/mnt/d/real estate/<PERSON>rmaan_AI_cons_calc_Claude/tests/e2e/results", "repeatEach": 1, "retries": 0, "metadata": {"testSuite": "Nirmaan AI Construction Calculator E2E Tests", "version": "1.0.0", "environment": "test", "baseURL": "http://localhost:3000", "timestamp": "2025-07-15T10:15:40.264Z"}, "id": "firefox", "name": "firefox", "testDir": "/mnt/d/real estate/<PERSON>rmaan_AI_cons_calc_Claude/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "/mnt/d/real estate/<PERSON>rmaan_AI_cons_calc_Claude/tests/e2e/results", "repeatEach": 1, "retries": 0, "metadata": {"testSuite": "Nirmaan AI Construction Calculator E2E Tests", "version": "1.0.0", "environment": "test", "baseURL": "http://localhost:3000", "timestamp": "2025-07-15T10:15:40.264Z"}, "id": "webkit", "name": "webkit", "testDir": "/mnt/d/real estate/<PERSON>rmaan_AI_cons_calc_Claude/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "/mnt/d/real estate/<PERSON>rmaan_AI_cons_calc_Claude/tests/e2e/results", "repeatEach": 1, "retries": 0, "metadata": {"testSuite": "Nirmaan AI Construction Calculator E2E Tests", "version": "1.0.0", "environment": "test", "baseURL": "http://localhost:3000", "timestamp": "2025-07-15T10:15:40.264Z"}, "id": "mobile-chrome", "name": "mobile-chrome", "testDir": "/mnt/d/real estate/<PERSON>rmaan_AI_cons_calc_Claude/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "/mnt/d/real estate/<PERSON>rmaan_AI_cons_calc_Claude/tests/e2e/results", "repeatEach": 1, "retries": 0, "metadata": {"testSuite": "Nirmaan AI Construction Calculator E2E Tests", "version": "1.0.0", "environment": "test", "baseURL": "http://localhost:3000", "timestamp": "2025-07-15T10:15:40.264Z"}, "id": "mobile-safari", "name": "mobile-safari", "testDir": "/mnt/d/real estate/<PERSON>rmaan_AI_cons_calc_Claude/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "/mnt/d/real estate/<PERSON>rmaan_AI_cons_calc_Claude/tests/e2e/results", "repeatEach": 1, "retries": 0, "metadata": {"testSuite": "Nirmaan AI Construction Calculator E2E Tests", "version": "1.0.0", "environment": "test", "baseURL": "http://localhost:3000", "timestamp": "2025-07-15T10:15:40.264Z"}, "id": "tablet", "name": "tablet", "testDir": "/mnt/d/real estate/<PERSON>rmaan_AI_cons_calc_Claude/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "/mnt/d/real estate/<PERSON>rmaan_AI_cons_calc_Claude/tests/e2e/results", "repeatEach": 1, "retries": 0, "metadata": {"testSuite": "Nirmaan AI Construction Calculator E2E Tests", "version": "1.0.0", "environment": "test", "baseURL": "http://localhost:3000", "timestamp": "2025-07-15T10:15:40.264Z"}, "id": "high-dpi", "name": "high-dpi", "testDir": "/mnt/d/real estate/<PERSON>rmaan_AI_cons_calc_Claude/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "/mnt/d/real estate/<PERSON>rmaan_AI_cons_calc_Claude/tests/e2e/results", "repeatEach": 1, "retries": 0, "metadata": {"testSuite": "Nirmaan AI Construction Calculator E2E Tests", "version": "1.0.0", "environment": "test", "baseURL": "http://localhost:3000", "timestamp": "2025-07-15T10:15:40.264Z"}, "id": "dark-mode", "name": "dark-mode", "testDir": "/mnt/d/real estate/<PERSON>rmaan_AI_cons_calc_Claude/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "/mnt/d/real estate/<PERSON>rmaan_AI_cons_calc_Claude/tests/e2e/results", "repeatEach": 1, "retries": 0, "metadata": {"testSuite": "Nirmaan AI Construction Calculator E2E Tests", "version": "1.0.0", "environment": "test", "baseURL": "http://localhost:3000", "timestamp": "2025-07-15T10:15:40.264Z"}, "id": "slow-network", "name": "slow-network", "testDir": "/mnt/d/real estate/<PERSON>rmaan_AI_cons_calc_Claude/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "/mnt/d/real estate/<PERSON>rmaan_AI_cons_calc_Claude/tests/e2e/results", "repeatEach": 1, "retries": 0, "metadata": {"testSuite": "Nirmaan AI Construction Calculator E2E Tests", "version": "1.0.0", "environment": "test", "baseURL": "http://localhost:3000", "timestamp": "2025-07-15T10:15:40.264Z"}, "id": "performance", "name": "performance", "testDir": "/mnt/d/real estate/<PERSON>rmaan_AI_cons_calc_Claude/tests/e2e", "testIgnore": [], "testMatch": ["/.*\\.performance\\.spec\\.ts/"], "timeout": 60000}, {"outputDir": "/mnt/d/real estate/<PERSON>rmaan_AI_cons_calc_Claude/tests/e2e/results", "repeatEach": 1, "retries": 0, "metadata": {"testSuite": "Nirmaan AI Construction Calculator E2E Tests", "version": "1.0.0", "environment": "test", "baseURL": "http://localhost:3000", "timestamp": "2025-07-15T10:15:40.264Z"}, "id": "accessibility", "name": "accessibility", "testDir": "/mnt/d/real estate/<PERSON>rmaan_AI_cons_calc_Claude/tests/e2e", "testIgnore": [], "testMatch": ["/.*\\.a11y\\.spec\\.ts/"], "timeout": 60000}, {"outputDir": "/mnt/d/real estate/<PERSON>rmaan_AI_cons_calc_Claude/tests/e2e/results", "repeatEach": 1, "retries": 0, "metadata": {"testSuite": "Nirmaan AI Construction Calculator E2E Tests", "version": "1.0.0", "environment": "test", "baseURL": "http://localhost:3000", "timestamp": "2025-07-15T10:15:40.264Z"}, "id": "visual-regression", "name": "visual-regression", "testDir": "/mnt/d/real estate/<PERSON>rmaan_AI_cons_calc_Claude/tests/e2e", "testIgnore": [], "testMatch": ["/.*\\.visual\\.spec\\.ts/"], "timeout": 60000}, {"outputDir": "/mnt/d/real estate/<PERSON>rmaan_AI_cons_calc_Claude/tests/e2e/results", "repeatEach": 1, "retries": 0, "metadata": {"testSuite": "Nirmaan AI Construction Calculator E2E Tests", "version": "1.0.0", "environment": "test", "baseURL": "http://localhost:3000", "timestamp": "2025-07-15T10:15:40.264Z"}, "id": "api", "name": "api", "testDir": "/mnt/d/real estate/<PERSON>rmaan_AI_cons_calc_Claude/tests/e2e", "testIgnore": [], "testMatch": ["/.*\\.api\\.spec\\.ts/"], "timeout": 60000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.54.1", "workers": 12, "webServer": {"command": "npm run build && npm run start", "url": "http://localhost:3000", "reuseExistingServer": true, "timeout": 120000, "stdout": "ignore", "stderr": "pipe"}}, "suites": [], "errors": [{"message": "Error: ENOENT: no such file or directory, open '/mnt/d/real estate/Nirmaan_AI_cons_calc_Claude/tests/e2e/data/test-data.json'", "stack": "Error: ENOENT: no such file or directory, open '/mnt/d/real estate/Nirmaan_AI_cons_calc_<PERSON>/tests/e2e/data/test-data.json'\n    at setupTestData (/mnt/d/real estate/Nirmaan_AI_cons_calc_<PERSON>/tests/e2e/setup/global-setup.ts:166:6)\n    at globalSetup (/mnt/d/real estate/Nirmaan_AI_cons_calc_Claude/tests/e2e/setup/global-setup.ts:25:11)", "location": {"file": "/mnt/d/real estate/<PERSON>rmaan_AI_cons_calc_Claude/tests/e2e/setup/global-setup.ts", "column": 6, "line": 166}, "snippet": "\u001b[90m   at \u001b[39msetup/global-setup.ts:166\n\n\u001b[0m \u001b[90m 164 |\u001b[39m   \u001b[90m// Save test data to file\u001b[39m\n \u001b[90m 165 |\u001b[39m   \u001b[36mconst\u001b[39m testDataPath \u001b[33m=\u001b[39m path\u001b[33m.\u001b[39mjoin(process\u001b[33m.\u001b[39mcwd()\u001b[33m,\u001b[39m \u001b[32m'tests/e2e/data/test-data.json'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 166 |\u001b[39m   fs\u001b[33m.\u001b[39mwriteFileSync(testDataPath\u001b[33m,\u001b[39m \u001b[33mJSON\u001b[39m\u001b[33m.\u001b[39mstringify(testData\u001b[33m,\u001b[39m \u001b[36mnull\u001b[39m\u001b[33m,\u001b[39m \u001b[35m2\u001b[39m))\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m      \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 167 |\u001b[39m   \n \u001b[90m 168 |\u001b[39m   console\u001b[33m.\u001b[39mlog(\u001b[32m'✅ Test data setup completed'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 169 |\u001b[39m }\u001b[0m"}], "stats": {"startTime": "2025-07-15T10:15:40.276Z", "duration": 31089.203, "expected": 0, "skipped": 0, "unexpected": 0, "flaky": 0}}