/**
 * Calculator Constants for Indian Construction Industry
 * Based on current market rates and Indian Standards (IS codes)
 */

import type {
  QualityTierRates,
  RegionalMultiplier,
  MaterialConsumptionRate,
} from './types';

// Base construction costs per sqft by quality tier (as of 2024)
export const BASE_CONSTRUCTION_RATES: QualityTierRates = {
  smart: 1800, // ₹1,600-2,000/sqft - Standard quality, local materials
  premium: 2500, // ₹2,200-2,800/sqft - Branded materials, better finishes
  luxury: 3500, // ₹3,000-4,000+/sqft - Premium brands, high-end finishes
};

// Regional cost multipliers for major Indian cities
export const REGIONAL_MULTIPLIERS: RegionalMultiplier = {
  bangalore: 1.0, // Base city
  mumbai: 1.25, // Highest costs
  delhi: 1.15, // High costs
  pune: 1.05, // Slightly above Bangalore
  hyderabad: 0.95, // Slightly below Bangalore
  chennai: 1.0, // Similar to Bangalore
  kolkata: 0.9, // Lower costs
  ahmedabad: 0.85, // Lower costs
  jaipur: 0.8, // Lower costs
  lucknow: 0.75, // Lower costs
  bhubaneswar: 0.7, // Lower costs
  tier2: 0.65, // Tier 2 cities
  tier3: 0.55, // Tier 3 cities
  default: 1.0, // Fallback
};

// Floor multipliers (higher floors need stronger structure)
export const FLOOR_MULTIPLIERS = {
  0: 1.0, // Ground only
  1: 1.05, // G+1
  2: 1.1, // G+2
  3: 1.15, // G+3
  4: 1.2, // G+4
  5: 1.25, // G+5 and above
};

// Cost breakdown percentages by category
export const COST_BREAKDOWN_PERCENTAGES = {
  structure: 0.35, // 35% - Foundation, columns, beams, slabs
  finishing: 0.3, // 30% - Flooring, painting, doors, windows
  mep: 0.2, // 20% - Electrical, plumbing, HVAC
  external: 0.1, // 10% - Compound wall, landscaping, gate
  other: 0.05, // 5% - Miscellaneous, contingency
};

// Structure cost sub-breakdown
export const STRUCTURE_BREAKDOWN = {
  foundation: 0.15, // 15% of structure cost
  columns: 0.25, // 25% of structure cost (includes beams)
  slabs: 0.35, // 35% of structure cost
  walls: 0.2, // 20% of structure cost
  staircase: 0.05, // 5% of structure cost
};

// Material consumption rates per sqft (based on IS codes)
export const MATERIAL_CONSUMPTION_RATES: MaterialConsumptionRate[] = [
  {
    material: 'cement',
    unit: 'bags (50kg)',
    consumptionPerSqft: 0.38, // bags per sqft
    qualityVariation: {
      smart: 0.36,
      premium: 0.38,
      luxury: 0.42,
    },
  },
  {
    material: 'steel',
    unit: 'kg',
    consumptionPerSqft: 4.0, // kg per sqft
    qualityVariation: {
      smart: 3.8,
      premium: 4.0,
      luxury: 4.5,
    },
  },
  {
    material: 'bricks',
    unit: 'pieces',
    consumptionPerSqft: 8, // pieces per sqft (9" wall)
    qualityVariation: {
      smart: 8,
      premium: 7, // Better quality bricks, less wastage
      luxury: 6, // Premium bricks, precise work
    },
  },
  {
    material: 'sand',
    unit: 'cft',
    consumptionPerSqft: 0.816, // cubic feet per sqft
    qualityVariation: {
      smart: 0.85,
      premium: 0.816,
      luxury: 0.78,
    },
  },
  {
    material: 'aggregate',
    unit: 'cft',
    consumptionPerSqft: 0.608, // cubic feet per sqft
    qualityVariation: {
      smart: 0.65,
      premium: 0.608,
      luxury: 0.58,
    },
  },
];

// Quality tier specifications
export const QUALITY_TIER_SPECS = {
  smart: {
    name: 'Smart Choice',
    description: 'Value engineering with standard finishes',
    concreteGrade: 'M20',
    steelGrade: 'Fe415',
    brickType: 'Red Clay Bricks',
    flooringOptions: ['Vitrified Tiles', 'Ceramic Tiles'],
    paintBrand: 'Asian Paints Tractor Emulsion',
    fixtures: 'Cera/Parryware',
    electrical: 'Anchor/Roma',
    priceRange: '₹1,600 - ₹2,000 per sqft',
  },
  premium: {
    name: 'Premium Selection',
    description: 'Branded materials with better finishes',
    concreteGrade: 'M25',
    steelGrade: 'Fe500',
    brickType: 'Fly Ash Bricks/AAC Blocks',
    flooringOptions: ['Premium Vitrified', 'Granite', 'Marble'],
    paintBrand: 'Asian Paints Royale/Berger Silk',
    fixtures: 'Kohler/American Standard',
    electrical: 'Legrand/Schneider',
    priceRange: '₹2,200 - ₹2,800 per sqft',
  },
  luxury: {
    name: 'Luxury Collection',
    description: 'Premium brands with high-end finishes',
    concreteGrade: 'M30+',
    steelGrade: 'Fe500D/Fe550',
    brickType: 'AAC Blocks/Solid Concrete Blocks',
    flooringOptions: ['Italian Marble', 'Imported Tiles', 'Hardwood'],
    paintBrand: 'Asian Paints Royale Play/Dulux',
    fixtures: 'Grohe/Hansgrohe',
    electrical: 'MK/Schneider Premium',
    priceRange: '₹3,000 - ₹4,000+ per sqft',
  },
};

// Wastage factors for materials
export const WASTAGE_FACTORS = {
  cement: 0.02, // 2% wastage
  steel: 0.05, // 5% wastage (cutting, bending)
  bricks: 0.05, // 5% wastage (breakage)
  tiles: 0.1, // 10% wastage (cutting, breakage)
  paint: 0.15, // 15% wastage (absorption, spillage)
  sand: 0.2, // 20% wastage (moisture, handling)
  aggregate: 0.15, // 15% wastage
  default: 0.1, // 10% default wastage
};

// Construction timeline phases (in weeks)
export const CONSTRUCTION_PHASES = [
  {
    name: 'Site Preparation & Excavation',
    duration: 1,
    startAfter: 0,
    description: 'Site clearing, soil testing, excavation',
  },
  {
    name: 'Foundation Work',
    duration: 2,
    startAfter: 1,
    description: 'Foundation excavation, reinforcement, concrete pouring',
  },
  {
    name: 'Plinth Beam & Ground Floor Slab',
    duration: 2,
    startAfter: 3,
    description: 'Plinth beam construction, ground floor slab',
  },
  {
    name: 'Superstructure (Per Floor)',
    duration: 3, // per floor
    startAfter: 5,
    description: 'Columns, beams, slab for each floor',
  },
  {
    name: 'Masonry Work',
    duration: 4,
    startAfter: 8,
    description: 'Brick/block work, internal walls',
  },
  {
    name: 'Roofing & Waterproofing',
    duration: 2,
    startAfter: 12,
    description: 'Roof slab, waterproofing, parapet',
  },
  {
    name: 'Plumbing & Electrical',
    duration: 3,
    startAfter: 14,
    description: 'Rough plumbing and electrical work',
  },
  {
    name: 'Plastering',
    duration: 3,
    startAfter: 17,
    description: 'Internal and external plastering',
  },
  {
    name: 'Flooring & Tiling',
    duration: 3,
    startAfter: 20,
    description: 'Floor tiling, bathroom tiling',
  },
  {
    name: 'Doors & Windows',
    duration: 2,
    startAfter: 23,
    description: 'Door and window installation',
  },
  {
    name: 'Painting & Finishing',
    duration: 2,
    startAfter: 25,
    description: 'Interior and exterior painting',
  },
  {
    name: 'Final Electrical & Plumbing',
    duration: 2,
    startAfter: 27,
    description: 'Final electrical and plumbing fixtures',
  },
  {
    name: 'Cleanup & Handover',
    duration: 1,
    startAfter: 29,
    description: 'Final cleanup and project handover',
  },
];

// Validation constants
export const VALIDATION_LIMITS = {
  minArea: 500, // Minimum 500 sqft
  maxArea: 50000, // Maximum 50,000 sqft
  minFloors: 0, // Ground only
  maxFloors: 10, // Maximum G+10
  minCostPerSqft: 800, // Minimum realistic cost
  maxCostPerSqft: 8000, // Maximum realistic cost
};
