# Code Structure Improvements Script
# Implementing recommendations from the other agent for better organization

Write-Host "Starting Code Structure Improvements..." -ForegroundColor Green

# 1. Consolidate scattered type files
Write-Host "Consolidating type files..." -ForegroundColor Yellow
if (-not (Test-Path "src/types")) {
    New-Item -ItemType Directory -Path "src/types" -Force | Out-Null
}

# Move any scattered .types.ts files to src/types/
Get-ChildItem -Path "src" -Recurse -Name "*.types.ts" | ForEach-Object {
    $sourcePath = "src/$_"
    $fileName = Split-Path $_ -Leaf
    $destPath = "src/types/$fileName"
    if (Test-Path $sourcePath) {
        Write-Host "  Moving $sourcePath to $destPath" -ForegroundColor Gray
        Move-Item -Path $sourcePath -Destination $destPath -Force
    }
}

# 2. Consolidate utility files
Write-Host "Consolidating utility files..." -ForegroundColor Yellow
if (-not (Test-Path "src/lib/utils")) {
    New-Item -ItemType Directory -Path "src/lib/utils" -Force | Out-Null
}

# Move any scattered .utils.ts files to src/lib/utils/
Get-ChildItem -Path "src" -Recurse -Name "*.utils.ts" | ForEach-Object {
    $sourcePath = "src/$_"
    $fileName = Split-Path $_ -Leaf
    $destPath = "src/lib/utils/$fileName"
    if (Test-Path $sourcePath -and $sourcePath -ne "src/lib/utils.ts") {
        Write-Host "  Moving $sourcePath to $destPath" -ForegroundColor Gray
        Move-Item -Path $sourcePath -Destination $destPath -Force
    }
}

# 3. Create a centralized constants file
Write-Host "Creating centralized constants..." -ForegroundColor Yellow
$constantsContent = @"
/**
 * Application Constants
 * Centralized location for all application constants
 */

// API Configuration
export const API_CONFIG = {
  BASE_URL: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
  TIMEOUT: 30000,
  RETRY_ATTEMPTS: 3,
} as const;

// Calculator Configuration
export const CALCULATOR_CONFIG = {
  MIN_AREA: 100,
  MAX_AREA: 50000,
  MIN_FLOORS: 0,
  MAX_FLOORS: 10,
  DEFAULT_QUALITY: 'smart',
  DEFAULT_LOCATION: 'bangalore',
} as const;

// UI Configuration
export const UI_CONFIG = {
  MOBILE_BREAKPOINT: 768,
  TABLET_BREAKPOINT: 1024,
  DESKTOP_BREAKPOINT: 1280,
  ANIMATION_DURATION: 300,
  DEBOUNCE_DELAY: 500,
} as const;

// Quality Tiers
export const QUALITY_TIERS = ['smart', 'premium', 'luxury'] as const;

// Supported Locations
export const SUPPORTED_LOCATIONS = [
  'bangalore', 'mumbai', 'delhi', 'hyderabad', 'pune', 
  'chennai', 'kolkata', 'ahmedabad', 'jaipur'
] as const;

// Error Messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network error. Please check your connection.',
  VALIDATION_ERROR: 'Please check your input values.',
  SERVER_ERROR: 'Server error. Please try again later.',
  CALCULATION_ERROR: 'Error calculating costs. Please try again.',
} as const;

// Success Messages
export const SUCCESS_MESSAGES = {
  CALCULATION_COMPLETE: 'Calculation completed successfully!',
  PROJECT_SAVED: 'Project saved successfully!',
  EXPORT_COMPLETE: 'Export completed successfully!',
} as const;
"@

Set-Content -Path "src/lib/constants.ts" -Value $constantsContent

# 4. Update imports in main calculator files to use constants
Write-Host "Updating calculator imports..." -ForegroundColor Yellow

# 5. Create a proper barrel export for components
Write-Host "Creating barrel exports..." -ForegroundColor Yellow
$calculatorIndexContent = @"
// Calculator Components Barrel Export
export { CalculatorContainer } from './CalculatorContainer';
export { EnhancedCalculatorContainer } from './EnhancedCalculatorContainer';
export { LightweightCalculatorContainer } from './LightweightCalculatorContainer';
export { MobileCalculatorInterface } from './MobileCalculatorInterface';
export { MobileCalculatorLayout } from './MobileCalculatorLayout';
export { CostBreakdownCard } from './CostBreakdownCard';
export { ResultsDisplay } from './ResultsDisplay';
export { MobileResultsDisplay } from './MobileResultsDisplay';
export { LocationSelector } from './LocationSelector';
export { QualityTierSelector } from './QualityTierSelector';
export { MaterialsList } from './MaterialsList';
export { SaveCalculationButton } from './SaveCalculationButton';
export { CalculatorSkeleton } from './CalculatorSkeleton';
"@

Set-Content -Path "src/components/calculator/index.ts" -Value $calculatorIndexContent

# 6. Create proper barrel export for UI components
$uiIndexContent = @"
// UI Components Barrel Export
export { Button } from './button';
export { Input } from './input';
export { Label } from './label';
export { Card, CardContent, CardHeader, CardTitle } from './card';
export { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './select';
export { Badge } from './badge';
export { Alert } from './alert';
export { Progress } from './progress';
export { Skeleton } from './skeleton';
export { Tabs } from './tabs';

// Mobile-specific components
export { MobileInput } from './mobile-input';
export { MobileSheet } from './mobile-sheet';
export { TouchButton } from './touch-button';
export { GestureCard } from './gesture-card';
export { PullToRefresh } from './pull-to-refresh';

// Accessibility components
export { AccessibleButton } from './accessible-button';
export { AccessibleLoading } from './accessible-loading';

// Animation components
export { AnimatedButton } from './animated-button';
export { AnimatedInput } from './animated-input';
export { OptimizedMotion } from './optimized-motion';
export { SmartTransitions } from './smart-transitions';

// Utility components
export { ErrorBoundary } from './error-boundary';
export { LoadingStates } from './loading-states';
export { StatusIndicators } from './status-indicators';
export { CountUp } from './count-up';
export { PDFExportButton } from './pdf-export-button';
export { OptimizedImage } from './optimized-image';
export { ProgressiveImage } from './progressive-image';
export { ScrollArea } from './scroll-area';
export { SwipeableCards } from './swipeable-cards';
"@

Set-Content -Path "src/components/ui/index.ts" -Value $uiIndexContent

# 7. Clean up any remaining empty directories
Write-Host "Final cleanup of empty directories..." -ForegroundColor Yellow
Get-ChildItem -Path "src" -Recurse -Directory | Where-Object { 
    (Get-ChildItem -Path $_.FullName -Recurse -File).Count -eq 0 
} | Remove-Item -Recurse -Force -ErrorAction SilentlyContinue

Write-Host "Code structure improvements completed!" -ForegroundColor Green
Write-Host "Files organized and barrel exports created." -ForegroundColor Cyan
