'use client';

import { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { AnimatedButton } from '@/components/ui/animated-button';
import { AccessibleButton, CalculateButton, ResetButton } from '@/components/ui/accessible-button';
import { LoadingCard, ErrorState, LoadingSpinner } from '@/components/ui/loading-states';
import { CalculatorErrorBoundary } from '@/components/ui/error-boundary';
import { StatusBadge, StepIndicator } from '@/components/ui/status-indicators';
import { MobileInput } from '@/components/ui/mobile-input';
import { QualityTierSelector } from '@/components/ui/swipeable-cards';
import { MobileResultsDisplay } from '@/components/calculator/MobileResultsDisplay';
import { RefreshableContainer } from '@/components/ui/pull-to-refresh';
import { isMobileViewport } from '@/lib/mobile';
import { useAccessibility } from '@/components/providers/AccessibilityProvider';
import { 
  useFocusManagement, 
  useScreenReader, 
  useKeyboardNavigation,
  useMobileTouchAccessibility 
} from '@/lib/accessibility';
import { calculatorFormSchema } from '@/lib/validation/calculator';
import { VALIDATION_LIMITS } from '@/core/calculator/constants';

interface CalculationInputs {
  plotSize: string;
  floors: string;
  quality: string;
  location: string;
  buildingType: string;
}

interface CalculationResult {
  totalCost: number;
  costPerSqft: number;
  breakdown: {
    structure: number;
    finishing: number;
    mep: number;
    other: number;
  };
  builtUpArea: number;
  quality: string;
  location: string;
}

export function CalculatorContainer() {
  const [inputs, setInputs] = useState<CalculationInputs>({
    plotSize: '',
    floors: '1',
    quality: 'smart',
    location: 'delhi',
    buildingType: 'residential',
  });

  const [result, setResult] = useState<CalculationResult | null>(null);
  const [isCalculating, setIsCalculating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [showMobileResults, setShowMobileResults] = useState(false);

  const isMobile = isMobileViewport();
  const containerRef = useRef<HTMLDivElement>(null);

  // Accessibility hooks
  const { announceMessage, setupComponent } = useAccessibility();
  const { announce, announceError, announceSuccess, announceLoading } = useScreenReader();
  const { registerContext, activateContext, deactivateContext } = useKeyboardNavigation();
  const { enhanceElement, isTouchDevice } = useMobileTouchAccessibility();

  // Initialize accessibility for calculator
  useEffect(() => {
    // Register calculator-specific keyboard shortcuts
    registerContext({
      name: 'calculator',
      priority: 100,
      shortcuts: [
        {
          key: 'Enter',
          ctrlKey: true,
          description: 'Calculate construction cost',
          action: () => {
            if (!isCalculating && inputs.plotSize) {
              handleCalculate();
            }
          },
          disabled: false
        },
        {
          key: 'r',
          ctrlKey: true,
          description: 'Reset calculator form',
          action: (e) => {
            e.preventDefault();
            resetCalculation();
          },
          disabled: false
        },
        {
          key: 'p',
          ctrlKey: true,
          shiftKey: true,
          description: 'Export PDF report',
          action: (e) => {
            e.preventDefault();
            if (result) {
              announceMessage('PDF export functionality would be triggered here');
            }
          },
          disabled: false
        }
      ]
    });

    activateContext('calculator');

    // Setup component accessibility
    if (containerRef.current) {
      let cleanup: (() => void) | undefined;
      
      setupComponent(containerRef.current, {
        context: 'calculator',
        enableVoice: true,
        enableTouch: isTouchDevice(),
        enhanceForm: true
      }).then((cleanupFn) => {
        cleanup = cleanupFn;
      });

      return () => {
        if (cleanup) cleanup();
        deactivateContext('calculator');
      };
    }

    return () => deactivateContext('calculator');
  }, [registerContext, activateContext, deactivateContext, setupComponent, isTouchDevice, isCalculating, inputs.plotSize, result, announceMessage]);

  // Announce page load
  useEffect(() => {
    announce('Construction cost calculator loaded. Enter your project details to get started.', 'polite');
  }, [announce]);

  const handleInputChange = (field: keyof CalculationInputs, value: string) => {
    setInputs(prev => ({ ...prev, [field]: value }));
    // Clear validation error when user starts typing
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  const validateInputs = (): boolean => {
    const errors: Record<string, string> = {};
    
    // Parse the plot size
    const plotSizeValue = parseFloat(inputs.plotSize);
    const floorsValue = parseInt(inputs.floors);
    
    // Validate plot size using proper schema validation
    if (!inputs.plotSize || isNaN(plotSizeValue) || plotSizeValue <= 0) {
      errors.plotSize = 'Please enter a valid plot size';
      announceError('Plot Size', 'Please enter a valid plot size');
    } else if (plotSizeValue < VALIDATION_LIMITS.minArea) {
      errors.plotSize = `Minimum area is ${VALIDATION_LIMITS.minArea} sq ft`;
      announceError('Plot Size', `Minimum area is ${VALIDATION_LIMITS.minArea} sq ft`);
    } else if (plotSizeValue > VALIDATION_LIMITS.maxArea) {
      errors.plotSize = `Maximum area is ${VALIDATION_LIMITS.maxArea} sq ft`;
      announceError('Plot Size', `Maximum area is ${VALIDATION_LIMITS.maxArea} sq ft`);
    }
    
    // Validate floors
    if (!inputs.floors || isNaN(floorsValue) || floorsValue <= 0) {
      errors.floors = 'Please enter a valid number of floors';
      announceError('Number of Floors', 'Please enter a valid number of floors');
    } else if (floorsValue < VALIDATION_LIMITS.minFloors) {
      errors.floors = `Minimum ${VALIDATION_LIMITS.minFloors} floors`;
      announceError('Number of Floors', `Minimum ${VALIDATION_LIMITS.minFloors} floors`);
    } else if (floorsValue > VALIDATION_LIMITS.maxFloors) {
      errors.floors = `Maximum ${VALIDATION_LIMITS.maxFloors} floors allowed`;
      announceError('Number of Floors', `Maximum ${VALIDATION_LIMITS.maxFloors} floors allowed`);
    }
    
    if (!inputs.quality) {
      errors.quality = 'Please select a quality tier';
      announceError('Quality Tier', 'Please select a quality tier');
    }
    
    if (!inputs.location) {
      errors.location = 'Please select a location';
      announceError('Location', 'Please select a location');
    }
    
    setValidationErrors(errors);
    
    if (Object.keys(errors).length > 0) {
      announce(`Form has ${Object.keys(errors).length} validation errors. Please correct them and try again.`, 'assertive');
    }
    
    return Object.keys(errors).length === 0;
  };

  const resetCalculation = () => {
    setResult(null);
    setError(null);
    setValidationErrors({});
    announceSuccess('Calculator form has been reset');
  };

  const handleCalculate = async () => {
    if (!validateInputs()) {
      return;
    }

    setIsCalculating(true);
    setError(null);
    setResult(null);
    
    announceLoading('Calculating construction cost');

    try {
      // Simulate API call with delay
      await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 1000));
      
      // Simulate potential error
      if (Math.random() < 0.1) {
        throw new Error('Calculation service temporarily unavailable');
      }
      const response = await fetch('/api/calculate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(inputs),
      });

      if (!response.ok) throw new Error('Calculation failed');

      const data = await response.json();
      const resultWithMetadata = {
        ...data,
        builtUpArea: Math.round(parseInt(inputs.plotSize) * 0.6 * parseInt(inputs.floors)),
        quality: inputs.quality,
        location: inputs.location,
      };
      setResult(resultWithMetadata);
      
      // Announce successful calculation
      const totalCostFormatted = formatCurrency(resultWithMetadata.totalCost);
      announceSuccess(`Calculation complete. Total estimated cost is ${totalCostFormatted}`);
      
      // Show mobile results if on mobile
      if (isMobile) {
        setShowMobileResults(true);
      }
    } catch (error) {
      console.error('Error calculating costs:', error);
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
      setError(errorMessage);
      announce(`Error: ${errorMessage}`, 'assertive');
      
      // For demo purposes, show a sample result anyway
      const sampleResult = {
        totalCost: 2500000,
        costPerSqft: 2000,
        breakdown: {
          structure: 1000000,
          finishing: 800000,
          mep: 400000,
          other: 300000,
        },
        builtUpArea: Math.round(parseInt(inputs.plotSize) * 0.6 * parseInt(inputs.floors)),
        quality: inputs.quality,
        location: inputs.location,
      };
      setResult(sampleResult);
      
      const totalCostFormatted = formatCurrency(sampleResult.totalCost);
      announce(`Using sample data. Estimated cost is ${totalCostFormatted}`, 'polite');
      
      // Show mobile results if on mobile
      if (isMobile) {
        setShowMobileResults(true);
      }
    } finally {
      setIsCalculating(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const qualityTiers = [
    {
      id: 'smart',
      title: 'Smart Choice',
      price: '₹1,600-2,000/sqft',
      value: 'smart',
      features: [
        'M20 concrete grade',
        'Standard finishes',
        'Cera/Parryware fixtures',
        'Basic electrical fittings',
        'Standard tiles & paint'
      ],
    },
    {
      id: 'premium',
      title: 'Premium Selection',
      price: '₹2,200-2,800/sqft',
      value: 'premium',
      popular: true,
      features: [
        'M25 concrete grade',
        'Branded materials',
        'Kohler/Grohe fixtures',
        'Premium electrical fittings',
        'Designer tiles & textures'
      ],
    },
    {
      id: 'luxury',
      title: 'Luxury Collection',
      price: '₹3,000+/sqft',
      value: 'luxury',
      features: [
        'M30+ concrete grade',
        'International brands',
        'Imported fixtures',
        'Home automation ready',
        'Premium finishes & materials'
      ],
    },
  ];

  const handleRefresh = async () => {
    // Reset form and results
    setResult(null);
    setError(null);
    setValidationErrors({});
    setShowMobileResults(false);
    
    // Optionally reload any cached data
    await new Promise(resolve => setTimeout(resolve, 500));
  };

  return (
    <CalculatorErrorBoundary>
      <RefreshableContainer onRefresh={handleRefresh}>
        <div 
          ref={containerRef}
          className='max-w-6xl mx-auto space-y-8'
          role="main"
          aria-label="Construction cost calculator"
          data-testid="calculator-container"
        >
          {/* Input Form */}
          <Card>
        <CardHeader>
          <CardTitle>Project Details</CardTitle>
        </CardHeader>
        <CardContent className='space-y-6'>
          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
            <div className='space-y-2'>
              {isMobile ? (
                <MobileInput
                  label='Plot Size (sq ft)'
                  fieldType='number'
                  placeholder='Enter plot size'
                  value={inputs.plotSize}
                  onChange={e => handleInputChange('plotSize', e.target.value)}
                  error={validationErrors.plotSize}
                  showClearButton={!!inputs.plotSize}
                  onClear={() => handleInputChange('plotSize', '')}
                />
              ) : (
                <>
                  <Label htmlFor='plotSize'>Plot Size (sq ft)</Label>
                  <Input
                    id='plotSize'
                    type='number'
                    placeholder='Enter plot size'
                    value={inputs.plotSize}
                    onChange={e => handleInputChange('plotSize', e.target.value)}
                    className={validationErrors.plotSize ? 'border-red-500 focus:border-red-500' : ''}
                  />
                  {validationErrors.plotSize && (
                    <p className="text-sm text-red-600">{validationErrors.plotSize}</p>
                  )}
                </>
              )}
            </div>

            <div className='space-y-2'>
              <Label htmlFor='floors'>Number of Floors</Label>
              <Select
                value={inputs.floors}
                onValueChange={value => handleInputChange('floors', value)}
              >
                <SelectTrigger className={validationErrors.floors ? 'border-red-500 focus:border-red-500' : ''}>
                  <SelectValue placeholder='Select floors' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='1'>1 Floor</SelectItem>
                  <SelectItem value='2'>2 Floors</SelectItem>
                  <SelectItem value='3'>3 Floors</SelectItem>
                  <SelectItem value='4'>4 Floors</SelectItem>
                </SelectContent>
              </Select>
              {validationErrors.floors && (
                <p className="text-sm text-red-600">{validationErrors.floors}</p>
              )}
            </div>

            <div className='space-y-2 col-span-full'>
              <Label htmlFor='quality'>Quality Tier</Label>
              {isMobile ? (
                <QualityTierSelector
                  tiers={qualityTiers}
                  selectedTier={inputs.quality}
                  onTierSelect={value => handleInputChange('quality', value)}
                />
              ) : (
                <Select
                  value={inputs.quality}
                  onValueChange={value => handleInputChange('quality', value)}
                >
                  <SelectTrigger className={validationErrors.quality ? 'border-red-500 focus:border-red-500' : ''}>
                    <SelectValue placeholder='Select quality' />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='smart'>
                      Smart Choice (₹1,600-2,000/sqft)
                    </SelectItem>
                    <SelectItem value='premium'>
                      Premium Selection (₹2,200-2,800/sqft)
                    </SelectItem>
                    <SelectItem value='luxury'>
                      Luxury Collection (₹3,000+/sqft)
                    </SelectItem>
                  </SelectContent>
                </Select>
              )}
              {validationErrors.quality && (
                <p className="text-sm text-red-600">{validationErrors.quality}</p>
              )}
            </div>

            <div className='space-y-2'>
              <Label htmlFor='location'>Location</Label>
              <Select
                value={inputs.location}
                onValueChange={value => handleInputChange('location', value)}
              >
                <SelectTrigger className={validationErrors.location ? 'border-red-500 focus:border-red-500' : ''}>
                  <SelectValue placeholder='Select location' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='delhi'>Delhi NCR</SelectItem>
                  <SelectItem value='mumbai'>Mumbai</SelectItem>
                  <SelectItem value='bangalore'>Bangalore</SelectItem>
                  <SelectItem value='chennai'>Chennai</SelectItem>
                  <SelectItem value='hyderabad'>Hyderabad</SelectItem>
                  <SelectItem value='pune'>Pune</SelectItem>
                </SelectContent>
              </Select>
              {validationErrors.location && (
                <p className="text-sm text-red-600">{validationErrors.location}</p>
              )}
            </div>

            <div className='space-y-2'>
              <Label htmlFor='buildingType'>Building Type</Label>
              <Select
                value={inputs.buildingType}
                onValueChange={value =>
                  handleInputChange('buildingType', value)
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder='Select type' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='residential'>Residential</SelectItem>
                  <SelectItem value='commercial'>Commercial</SelectItem>
                  <SelectItem value='mixed'>Mixed Use</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className='flex justify-center gap-4'>
            <CalculateButton
              onClick={handleCalculate}
              disabled={!inputs.plotSize || isCalculating}
              loading={isCalculating}
              loadingText="Calculating construction cost..."
              data-testid="calculate-button"
            >
              Calculate Construction Cost
            </CalculateButton>
            
            {result && (
              <ResetButton
                onClick={resetCalculation}
                data-testid="reset-button"
              >
                Reset
              </ResetButton>
            )}
          </div>
          
          {/* Display general error */}
          {error && (
            <div className="mt-4">
              <StatusBadge status="error" text={error} />
            </div>
          )}
        </CardContent>
      </Card>

      {/* Loading State */}
      {isCalculating && (
        <LoadingCard 
          title="Calculating Construction Cost"
          description="Please wait while we process your project details and generate accurate cost estimates."
        />
      )}

      {/* Results */}
      {result && !isCalculating && (
        <Card>
          <CardHeader>
            <CardTitle>Cost Estimation Results</CardTitle>
          </CardHeader>
          <CardContent>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-8'>
              {/* Summary */}
              <div className='space-y-4'>
                <div className='text-center p-6 bg-blue-50 rounded-lg'>
                  <h3 className='text-lg font-semibold text-gray-800 mb-2'>
                    Total Estimated Cost
                  </h3>
                  <p className='text-3xl font-bold text-blue-600'>
                    {formatCurrency(result.totalCost)}
                  </p>
                  <p className='text-sm text-gray-600 mt-2'>
                    {formatCurrency(result.costPerSqft)} per sq ft
                  </p>
                </div>

                <div className='space-y-2'>
                  <p className='text-sm text-gray-600'>
                    • Built-up area:{' '}
                    {Math.round(
                      parseInt(inputs.plotSize) * 0.6 * parseInt(inputs.floors)
                    )}{' '}
                    sq ft
                  </p>
                  <p className='text-sm text-gray-600'>
                    • Quality tier:{' '}
                    {inputs.quality.charAt(0).toUpperCase() +
                      inputs.quality.slice(1)}
                  </p>
                  <p className='text-sm text-gray-600'>
                    • Location:{' '}
                    {inputs.location.charAt(0).toUpperCase() +
                      inputs.location.slice(1)}
                  </p>
                </div>
              </div>

              {/* Breakdown */}
              <div className='space-y-4'>
                <h3 className='text-lg font-semibold text-gray-800'>
                  Cost Breakdown
                </h3>
                <div className='space-y-3'>
                  <div className='flex justify-between items-center p-3 bg-gray-50 rounded'>
                    <span className='font-medium'>Structure & Foundation</span>
                    <span className='font-semibold'>
                      {formatCurrency(result.breakdown.structure)}
                    </span>
                  </div>
                  <div className='flex justify-between items-center p-3 bg-gray-50 rounded'>
                    <span className='font-medium'>Finishing & Interiors</span>
                    <span className='font-semibold'>
                      {formatCurrency(result.breakdown.finishing)}
                    </span>
                  </div>
                  <div className='flex justify-between items-center p-3 bg-gray-50 rounded'>
                    <span className='font-medium'>MEP Systems</span>
                    <span className='font-semibold'>
                      {formatCurrency(result.breakdown.mep)}
                    </span>
                  </div>
                  <div className='flex justify-between items-center p-3 bg-gray-50 rounded'>
                    <span className='font-medium'>
                      Professional Fees & Others
                    </span>
                    <span className='font-semibold'>
                      {formatCurrency(result.breakdown.other)}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <div className='mt-6 p-4 bg-yellow-50 rounded-lg'>
              <p className='text-sm text-yellow-800'>
                <strong>Note:</strong> These are preliminary estimates based on
                current market rates. Actual costs may vary based on specific
                requirements, material choices, and market conditions. For
                detailed estimates, please consult with our professional team.
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Mobile Results Display */}
      {result && (
        <MobileResultsDisplay
          result={result}
          isOpen={showMobileResults}
          onClose={() => setShowMobileResults(false)}
        />
      )}
    </div>
    </RefreshableContainer>
    </CalculatorErrorBoundary>
  );
}
