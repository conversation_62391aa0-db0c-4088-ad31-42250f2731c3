'use client';

import { useState, useCallback, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useSearchParams } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
// Dynamic icon imports to reduce bundle size
import { lazy, Suspense } from 'react';

// Lazy load icons
const Calculator = lazy(() => import('lucide-react').then(m => ({ default: m.Calculator })));
const Building2 = lazy(() => import('lucide-react').then(m => ({ default: m.Building2 })));
const Layers = lazy(() => import('lucide-react').then(m => ({ default: m.Layers })));
const CheckCircle2 = lazy(() => import('lucide-react').then(m => ({ default: m.CheckCircle2 })));
const AlertTriangle = lazy(() => import('lucide-react').then(m => ({ default: m.AlertTriangle })));
const Info = lazy(() => import('lucide-react').then(m => ({ default: m.Info })));

// Icon placeholder for suspense
const IconPlaceholder = ({ className }: { className?: string }) => (
  <div className={`${className} bg-gray-200 rounded animate-pulse`} />
);

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { AnimatedButton } from '@/components/ui/animated-button';
import { Input } from '@/components/ui/input';
import { MobileInput } from '@/components/ui/mobile-input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

import { QualityTierSelector } from './QualityTierSelector';
import { LocationSelector } from './LocationSelector';
import { ResultsDisplay } from './ResultsDisplay';
import { ResultsSkeleton } from './CalculatorSkeleton';
import { CalculatorErrorBoundary } from '@/components/ui/error-boundary';

import {
  calculatorFormSchema,
  type CalculatorFormData,
  formatAreaInput,
  validateBuiltUpArea,
} from '@/lib/validation/calculator';
import { CalculationResult } from '@/core/calculator/types';
import { useCalculator, CalculatorApiError, validateCalculatorInput } from '@/hooks/useCalculator';
import { isMobileViewport, mobileClasses, hapticFeedback } from '@/lib/mobile';
import { cn } from '@/lib/utils';

interface FormSection {
  id: string;
  title: string;
  icon: React.ComponentType<{ className?: string }>;
  description: string;
}

const formSections: FormSection[] = [
  {
    id: 'basics',
    title: 'Project Basics',
    icon: Building2,
    description: 'Tell us about your construction project',
  },
  {
    id: 'specifications',
    title: 'Specifications',
    icon: Layers,
    description: 'Select quality and location preferences',
  },
  {
    id: 'features',
    title: 'Additional Features',
    icon: CheckCircle2,
    description: 'Optional features and add-ons',
  },
];

export function EnhancedCalculatorContainer() {
  const searchParams = useSearchParams();
  const [currentSection, setCurrentSection] = useState(0);
  const [showValidationErrors, setShowValidationErrors] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [loadingMessage, setLoadingMessage] = useState<string | null>(null);

  // Enhanced calculator hook with API integration
  const {
    calculate,
    isCalculating,
    calculationError,
    lastResult,
    getCachedResult,
    prefetchCalculation,
    resetCalculation,
  } = useCalculator();

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isValid },
    trigger,
  } = useForm({
    resolver: zodResolver(calculatorFormSchema),
    mode: 'onChange',
    defaultValues: {
      builtUpArea: 0,
      floors: 1,
      qualityTier: 'smart' as const,
      location: 'bangalore',
      hasStilt: false,
      parkingType: 'none' as const,
      hasBasement: false,
    },
  });

  const watchedValues = watch();

  // Check if mobile on component mount
  useEffect(() => {
    setIsMobile(isMobileViewport());
    const handleResize = () => setIsMobile(isMobileViewport());
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Load saved calculation data if available
  useEffect(() => {
    const shouldLoad = searchParams.get('load') === 'true';
    if (shouldLoad) {
      setLoadingMessage('Loading saved calculation...');
      
      try {
        const savedData = localStorage.getItem('loadedCalculation');
        if (savedData) {
          const formData = JSON.parse(savedData);
          
          // Set form values
          Object.entries(formData).forEach(([key, value]) => {
            if (value !== undefined && value !== null) {
              setValue(key as keyof CalculatorFormData, value as any);
            }
          });
          
          // Clear the localStorage item
          localStorage.removeItem('loadedCalculation');
          
          setLoadingMessage('Calculation loaded successfully!');
          setTimeout(() => setLoadingMessage(null), 2000);
        }
      } catch (error) {
        console.error('Error loading calculation:', error);
        setLoadingMessage(null);
      }
    }
  }, [searchParams, setValue]);

  // Predictive caching - prefetch when form is nearly complete
  useEffect(() => {
    const validation = validateCalculatorInput(watchedValues);
    if (validation.isValid && currentSection === formSections.length - 1) {
      // Prefetch the calculation when user reaches final section
      prefetchCalculation(watchedValues);
    }
  }, [watchedValues, currentSection, prefetchCalculation]);

  const handleAreaChange = (field: 'builtUpArea' | 'plotArea', value: string) => {
    const numValue = formatAreaInput(value);
    if (numValue !== undefined) {
      setValue(field, numValue);
      trigger(field);
    }
  };

  const nextSection = async () => {
    const fieldsToValidate = getFieldsForSection(currentSection);
    const isValid = await trigger(fieldsToValidate);

    if (isValid && currentSection < formSections.length - 1) {
      setCurrentSection(currentSection + 1);
      hapticFeedback.light();
    } else if (!isValid) {
      hapticFeedback.error();
    }
  };

  const prevSection = () => {
    if (currentSection > 0) {
      setCurrentSection(currentSection - 1);
      hapticFeedback.light();
    }
  };

  const getFieldsForSection = (section: number): (keyof CalculatorFormData)[] => {
    switch (section) {
      case 0:
        return ['builtUpArea', 'plotArea', 'floors'];
      case 1:
        return ['qualityTier', 'location'];
      case 2:
        return ['hasStilt', 'parkingType', 'hasBasement'];
      default:
        return [];
    }
  };

  // Enhanced submit handler with proper error handling
  const onSubmit = useCallback(async (data: CalculatorFormData) => {
    setShowValidationErrors(false);

    try {
      // Check if we have a cached result first
      const cachedResult = getCachedResult(data);
      if (cachedResult) {
        console.log('Using cached result');
        // Still call the API to ensure freshness, but don't block UI
        calculate(data).catch(console.error);
        return;
      }

      // Perform calculation with enhanced error handling
      await calculate(data);
    } catch (error) {
      console.error('Calculation failed:', error);
      setShowValidationErrors(true);
    }
  }, [calculate, getCachedResult]);

  // Reset function
  const handleReset = useCallback(() => {
    resetCalculation();
    setCurrentSection(0);
    setShowValidationErrors(false);
  }, [resetCalculation]);

  // Render loading message if loading saved calculation
  if (loadingMessage) {
    return (
      <CalculatorErrorBoundary>
        <div className="max-w-6xl mx-auto">
          <Card>
            <CardContent className="pt-6">
              <div className="text-center space-y-4">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto"></div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">
                    {loadingMessage}
                  </h3>
                  <p className="text-sm text-gray-600">
                    Restoring your previous calculation settings...
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </CalculatorErrorBoundary>
    );
  }

  // Render loading state
  if (isCalculating) {
    return (
      <CalculatorErrorBoundary>
        <div className="max-w-6xl mx-auto space-y-8">
          <Card>
            <CardContent className="pt-6">
              <div className="text-center space-y-4">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">
                    Calculating Construction Costs
                  </h3>
                  <p className="text-sm text-gray-600">
                    Processing your requirements and generating detailed estimates...
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          <ResultsSkeleton />
        </div>
      </CalculatorErrorBoundary>
    );
  }

  // Render results if available
  if (lastResult) {
    return (
      <CalculatorErrorBoundary>
        <div className="max-w-6xl mx-auto space-y-8">
          <ResultsDisplay
            result={lastResult}
            formData={watchedValues}
            onBack={handleReset}
          />
        </div>
      </CalculatorErrorBoundary>
    );
  }

  return (
    <CalculatorErrorBoundary>
      <div className="max-w-4xl mx-auto space-y-8">
        {/* Error Display */}
        {calculationError && (
          <Card className="border-red-200 bg-red-50">
            <CardContent className="pt-6">
              <div className="flex items-start gap-3">
                <Suspense fallback={<IconPlaceholder className="h-5 w-5 mt-0.5" />}>
                  <AlertTriangle className="h-5 w-5 text-red-600 mt-0.5" />
                </Suspense>
                <div className="flex-1">
                  <h3 className="text-sm font-medium text-red-800">
                    Calculation Error
                  </h3>
                  <p className="text-sm text-red-700 mt-1">
                    {calculationError.message}
                  </p>
                  {calculationError.isRateLimit && (
                    <p className="text-xs text-red-600 mt-2">
                      Please wait a moment before trying again.
                    </p>
                  )}
                  {calculationError.isValidation && calculationError.details && (
                    <div className="mt-2">
                      <p className="text-xs text-red-600">Validation issues:</p>
                      <ul className="text-xs text-red-600 list-disc list-inside">
                        {Array.isArray(calculationError.details.errors) &&
                          calculationError.details.errors.map((error: any, index: number) => (
                            <li key={index}>{error.message}</li>
                          ))}
                      </ul>
                    </div>
                  )}
                  <AnimatedButton
                    onClick={() => resetCalculation()}
                    variant="outline"
                    size="sm"
                    className="mt-3 border-red-300 text-red-700 hover:bg-red-100"
                  >
                    Try Again
                  </AnimatedButton>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Validation Info */}
        {showValidationErrors && (
          <Card className="border-yellow-200 bg-yellow-50">
            <CardContent className="pt-6">
              <div className="flex items-start gap-3">
                <Suspense fallback={<IconPlaceholder className="h-5 w-5 mt-0.5" />}>
                  <Info className="h-5 w-5 text-yellow-600 mt-0.5" />
                </Suspense>
                <div className="flex-1">
                  <h3 className="text-sm font-medium text-yellow-800">
                    Input Validation
                  </h3>
                  <p className="text-sm text-yellow-700 mt-1">
                    Please check your inputs and try again. All required fields must be filled with valid values.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Progress indicator */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between mb-4">
                {formSections.map((section, index) => {
                  const Icon = section.icon;
                  const isActive = index === currentSection;
                  const isCompleted = index < currentSection;

                  return (
                    <div key={section.id} className="flex items-center">
                      <motion.div
                        className={`flex items-center justify-center w-10 h-10 rounded-full transition-colors duration-300 ${
                          isCompleted
                            ? 'bg-green-500 text-white'
                            : isActive
                            ? 'bg-blue-500 text-white'
                            : 'bg-gray-200 text-gray-500'
                        }`}
                        whileHover={{ scale: 1.1 }}
                        animate={{
                          scale: isActive ? [1, 1.1, 1] : 1,
                        }}
                        transition={{
                          duration: isActive ? 2 : 0.2,
                          repeat: isActive ? Infinity : 0,
                          repeatType: 'reverse'
                        }}
                      >
                        <AnimatePresence mode="wait">
                          {isCompleted ? (
                            <motion.div
                              key="check"
                              initial={{ scale: 0, rotate: -180 }}
                              animate={{ scale: 1, rotate: 0 }}
                              exit={{ scale: 0, rotate: 180 }}
                              transition={{ type: 'spring', stiffness: 500, damping: 30 }}
                            >
                              <CheckCircle2 className="h-5 w-5" />
                            </motion.div>
                          ) : (
                            <motion.div
                              key="icon"
                              initial={{ scale: 0 }}
                              animate={{ scale: 1 }}
                              exit={{ scale: 0 }}
                              transition={{ type: 'spring', stiffness: 500, damping: 30 }}
                            >
                              <Icon className="h-5 w-5" />
                            </motion.div>
                          )}
                        </AnimatePresence>
                      </motion.div>
                      {index < formSections.length - 1 && (
                        <div className="w-20 h-1 mx-2 bg-gray-200 rounded-full overflow-hidden">
                          <motion.div
                            className="h-full bg-green-500 rounded-full"
                            initial={{ width: 0 }}
                            animate={{ 
                              width: isCompleted ? '100%' : '0%'
                            }}
                            transition={{ duration: 0.5, delay: 0.2 }}
                          />
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>

              <motion.div 
                className="text-center"
                key={currentSection}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                <h3 className="text-lg font-semibold text-gray-900">
                  {formSections[currentSection].title}
                </h3>
                <p className="text-sm text-gray-600">
                  {formSections[currentSection].description}
                </p>
              </motion.div>
            </CardContent>
          </Card>
        </motion.div>

      {/* Form sections */}
      <form onSubmit={handleSubmit(onSubmit)}>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Suspense fallback={<IconPlaceholder className="h-5 w-5" />}>
                <Calculator className="h-5 w-5" />
              </Suspense>
              Construction Cost Calculator
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentSection}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
              >
                {/* Section 0: Project Basics */}
                {currentSection === 0 && (
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 gap-6">
                      {isMobile ? (
                        <>
                          <MobileInput
                            label="Built-up Area (sq ft) *"
                            fieldType="area"
                            inputMode="numeric"
                            placeholder="e.g., 1500"
                            {...register('builtUpArea', { valueAsNumber: true })}
                            error={errors.builtUpArea?.message}
                            helper="Total covered area of the building"
                            showClearButton
                            onClear={() => setValue('builtUpArea', 0)}
                          />

                          <MobileInput
                            label="Plot Area (sq ft)"
                            fieldType="area"
                            inputMode="numeric"
                            placeholder="e.g., 2000"
                            {...register('plotArea', { valueAsNumber: true })}
                            error={errors.plotArea?.message}
                            helper="Total land area (optional)"
                            showClearButton
                            onClear={() => setValue('plotArea', undefined)}
                          />
                        </>
                      ) : (
                        <>
                          <div className="space-y-2">
                            <Label htmlFor="builtUpArea">Built-up Area (sq ft) *</Label>
                            <motion.div
                              whileFocus={{ scale: 1.01 }}
                              transition={{ duration: 0.15 }}
                            >
                              <Input
                                id="builtUpArea"
                                type="number"
                                placeholder="e.g., 1500"
                                {...register('builtUpArea', { valueAsNumber: true })}
                                className={errors.builtUpArea ? 'border-red-500' : ''}
                              />
                            </motion.div>
                            {errors.builtUpArea && (
                              <motion.p
                                initial={{ opacity: 0, y: -5 }}
                                animate={{ opacity: 1, y: 0 }}
                                className="text-sm text-red-600"
                              >
                                {errors.builtUpArea.message}
                              </motion.p>
                            )}
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="plotArea">Plot Area (sq ft)</Label>
                            <motion.div
                              whileFocus={{ scale: 1.01 }}
                              transition={{ duration: 0.15 }}
                            >
                              <Input
                                id="plotArea"
                                type="number"
                                placeholder="e.g., 2000"
                                {...register('plotArea', { valueAsNumber: true })}
                                className={errors.plotArea ? 'border-red-500' : ''}
                              />
                            </motion.div>
                            {errors.plotArea && (
                              <motion.p
                                initial={{ opacity: 0, y: -5 }}
                                animate={{ opacity: 1, y: 0 }}
                                className="text-sm text-red-600"
                              >
                                {errors.plotArea.message}
                              </motion.p>
                            )}
                          </div>
                        </>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="floors">Number of Floors</Label>
                      <Select
                        value={watchedValues.floors?.toString()}
                        onValueChange={(value) => {
                          setValue('floors', parseInt(value));
                          hapticFeedback.light();
                        }}
                      >
                        <SelectTrigger className={cn(
                          isMobile && mobileClasses.touchTarget,
                          'text-base' // 16px minimum for iOS
                        )}>
                          <SelectValue placeholder="Select floors" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="1" className={isMobile ? mobileClasses.touchTarget : ''}>
                            Ground (G)
                          </SelectItem>
                          <SelectItem value="2" className={isMobile ? mobileClasses.touchTarget : ''}>
                            Ground + 1 (G+1)
                          </SelectItem>
                          <SelectItem value="3" className={isMobile ? mobileClasses.touchTarget : ''}>
                            Ground + 2 (G+2)
                          </SelectItem>
                          <SelectItem value="4" className={isMobile ? mobileClasses.touchTarget : ''}>
                            Ground + 3 (G+3)
                          </SelectItem>
                          <SelectItem value="5" className={isMobile ? mobileClasses.touchTarget : ''}>
                            Ground + 4 (G+4)
                          </SelectItem>
                        </SelectContent>
                      </Select>
                      {errors.floors && (
                        <motion.p
                          initial={{ opacity: 0, y: -5 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="text-sm text-red-600"
                        >
                          {errors.floors.message}
                        </motion.p>
                      )}
                    </div>
                  </div>
                )}

                {/* Section 1: Specifications */}
                {currentSection === 1 && (
                  <div className="space-y-6">
                    <QualityTierSelector
                      value={watchedValues.qualityTier}
                      onChange={(value) => setValue('qualityTier', value)}
                      error={errors.qualityTier?.message}
                    />

                    <LocationSelector
                      value={watchedValues.location}
                      onChange={(value) => setValue('location', value)}
                      error={errors.location?.message}
                    />
                  </div>
                )}

                {/* Section 2: Additional Features */}
                {currentSection === 2 && (
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 gap-6">
                      <div className="space-y-4">
                        <motion.div 
                          className="flex items-center space-x-3"
                          whileTap={{ scale: 0.98 }}
                        >
                          <input
                            type="checkbox"
                            id="hasBasement"
                            {...register('hasBasement')}
                            className={cn(
                              'text-blue-600 border-gray-300 rounded transition-colors',
                              isMobile ? 'h-6 w-6' : 'h-4 w-4'
                            )}
                            onChange={(e) => {
                              register('hasBasement').onChange(e);
                              hapticFeedback.light();
                            }}
                          />
                          <Label 
                            htmlFor="hasBasement" 
                            className={cn(
                              'cursor-pointer select-none',
                              isMobile && 'text-base leading-relaxed'
                            )}
                          >
                            Include Basement
                          </Label>
                        </motion.div>

                        <motion.div 
                          className="flex items-center space-x-3"
                          whileTap={{ scale: 0.98 }}
                        >
                          <input
                            type="checkbox"
                            id="hasStilt"
                            {...register('hasStilt')}
                            className={cn(
                              'text-blue-600 border-gray-300 rounded transition-colors',
                              isMobile ? 'h-6 w-6' : 'h-4 w-4'
                            )}
                            onChange={(e) => {
                              register('hasStilt').onChange(e);
                              hapticFeedback.light();
                            }}
                          />
                          <Label 
                            htmlFor="hasStilt" 
                            className={cn(
                              'cursor-pointer select-none',
                              isMobile && 'text-base leading-relaxed'
                            )}
                          >
                            Stilt Parking
                          </Label>
                        </motion.div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="parkingType">Parking Type</Label>
                        <Select
                          value={watchedValues.parkingType}
                          onValueChange={(value) => {
                            setValue('parkingType', value as 'open' | 'covered' | 'none');
                            hapticFeedback.light();
                          }}
                        >
                          <SelectTrigger className={cn(
                            isMobile && mobileClasses.touchTarget,
                            'text-base' // 16px minimum for iOS
                          )}>
                            <SelectValue placeholder="Select parking" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="none" className={isMobile ? mobileClasses.touchTarget : ''}>
                              No Parking
                            </SelectItem>
                            <SelectItem value="open" className={isMobile ? mobileClasses.touchTarget : ''}>
                              Open Parking
                            </SelectItem>
                            <SelectItem value="covered" className={isMobile ? mobileClasses.touchTarget : ''}>
                              Covered Parking
                            </SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>
                )}
              </motion.div>
            </AnimatePresence>

            {/* Navigation buttons */}
            <div className={cn(
              'flex pt-6',
              isMobile ? 'flex-col space-y-3' : 'justify-between'
            )}>
              {isMobile ? (
                <>
                  {currentSection < formSections.length - 1 ? (
                    <>
                      <AnimatedButton 
                        type="button" 
                        onClick={nextSection}
                        className={cn(
                          'w-full order-1',
                          mobileClasses.largeTouchTarget
                        )}
                        size="lg"
                      >
                        Continue to {formSections[currentSection + 1]?.title}
                      </AnimatedButton>
                      
                      {currentSection > 0 && (
                        <AnimatedButton
                          type="button"
                          variant="outline"
                          onClick={prevSection}
                          className={cn(
                            'w-full order-2',
                            mobileClasses.touchTarget
                          )}
                        >
                          Back to {formSections[currentSection - 1]?.title}
                        </AnimatedButton>
                      )}
                    </>
                  ) : (
                    <>
                      <AnimatedButton
                        type="submit"
                        disabled={!isValid || isCalculating}
                        loading={isCalculating}
                        loadingText="Calculating..."
                        className={cn(
                          'w-full order-1',
                          mobileClasses.largeTouchTarget
                        )}
                        size="lg"
                      >
                        Calculate Construction Cost
                      </AnimatedButton>
                      
                      <AnimatedButton
                        type="button"
                        variant="outline"
                        onClick={prevSection}
                        className={cn(
                          'w-full order-2',
                          mobileClasses.touchTarget
                        )}
                      >
                        Back to {formSections[currentSection - 1]?.title}
                      </AnimatedButton>
                    </>
                  )}
                </>
              ) : (
                <>
                  <AnimatedButton
                    type="button"
                    variant="outline"
                    onClick={prevSection}
                    disabled={currentSection === 0}
                  >
                    Previous
                  </AnimatedButton>

                  {currentSection < formSections.length - 1 ? (
                    <AnimatedButton type="button" onClick={nextSection}>
                      Next
                    </AnimatedButton>
                  ) : (
                    <AnimatedButton
                      type="submit"
                      disabled={!isValid || isCalculating}
                      loading={isCalculating}
                      loadingText="Calculating..."
                      className="min-w-[200px]"
                    >
                      Calculate Construction Cost
                    </AnimatedButton>
                  )}
                </>
              )}
            </div>
          </CardContent>
        </Card>
      </form>
      </div>
    </CalculatorErrorBoundary>
  );
}

// Default export for backward compatibility
export default EnhancedCalculatorContainer;