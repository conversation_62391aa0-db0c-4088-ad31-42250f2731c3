/**
 * Accessibility Provider Component
 * Provides accessibility context and initialization for the entire application
 */

'use client';

import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { 
  initializeAccessibility,
  setupComponentAccessibility,
  auditAccessibility,
  checkAccessibilityStatus,
  createAccessibilityProvider,
  type AccessibilityThemeConfig,
  type VoiceNavigationConfig,
  type MobileTouchConfig
} from '@/lib/accessibility';

interface AccessibilityContextValue {
  // Status
  isInitialized: boolean;
  status: ReturnType<typeof checkAccessibilityStatus>;
  
  // Theme
  themeConfig: AccessibilityThemeConfig | null;
  updateTheme: (config: Partial<AccessibilityThemeConfig>) => void;
  
  // Voice
  voiceConfig: VoiceNavigationConfig | null;
  updateVoice: (config: Partial<VoiceNavigationConfig>) => void;
  toggleVoiceNavigation: () => void;
  
  // Touch
  touchConfig: MobileTouchConfig | null;
  updateTouch: (config: Partial<MobileTouchConfig>) => void;
  
  // Controls
  announceMessage: (message: string, priority?: 'polite' | 'assertive') => void;
  setFocus: (element: HTMLElement | string) => void;
  showKeyboardHelp: () => void;
  runAccessibilityAudit: () => Promise<void>;
  
  // Setup utilities
  setupComponent: typeof setupComponentAccessibility;
}

const AccessibilityContext = createContext<AccessibilityContextValue | null>(null);

interface AccessibilityProviderProps {
  children: React.ReactNode;
  enableAutoInit?: boolean;
  enableDevTools?: boolean;
  onInitialized?: () => void;
  onError?: (error: Error) => void;
}

export function AccessibilityProvider({
  children,
  enableAutoInit = true,
  enableDevTools = process.env.NODE_ENV === 'development',
  onInitialized,
  onError
}: AccessibilityProviderProps) {
  const [isInitialized, setIsInitialized] = useState(false);
  const [status, setStatus] = useState(checkAccessibilityStatus());
  const [accessibilitySystem, setAccessibilitySystem] = useState<ReturnType<typeof createAccessibilityProvider> | null>(null);
  const [themeConfig, setThemeConfig] = useState<AccessibilityThemeConfig | null>(null);
  const [voiceConfig, setVoiceConfig] = useState<VoiceNavigationConfig | null>(null);
  const [touchConfig, setTouchConfig] = useState<MobileTouchConfig | null>(null);

  // Initialize accessibility systems
  const initialize = useCallback(async () => {
    try {
      const systems = await initializeAccessibility();
      const provider = createAccessibilityProvider();
      
      setAccessibilitySystem(provider);
      setIsInitialized(true);
      setStatus(checkAccessibilityStatus());
      
      // Load configurations
      setThemeConfig(provider.theme.getConfig());
      setVoiceConfig(provider.voice.getConfig());
      setTouchConfig(provider.touch.getConfig());
      
      // Subscribe to theme changes
      provider.theme.subscribe((config) => {
        setThemeConfig(config);
      });
      
      onInitialized?.();
    } catch (error) {
      console.error('Failed to initialize accessibility systems:', error);
      onError?.(error as Error);
    }
  }, [onInitialized, onError]);

  // Auto-initialize on mount
  useEffect(() => {
    if (enableAutoInit && !isInitialized) {
      initialize();
    }
  }, [enableAutoInit, isInitialized, initialize]);

  // Theme management
  const updateTheme = useCallback((config: Partial<AccessibilityThemeConfig>) => {
    if (accessibilitySystem) {
      accessibilitySystem.theme.updateConfig(config);
    }
  }, [accessibilitySystem]);

  // Voice management
  const updateVoice = useCallback((config: Partial<VoiceNavigationConfig>) => {
    if (accessibilitySystem) {
      accessibilitySystem.voice.updateConfig(config);
      setVoiceConfig(accessibilitySystem.voice.getConfig());
    }
  }, [accessibilitySystem]);

  const toggleVoiceNavigation = useCallback(() => {
    if (accessibilitySystem) {
      const currentConfig = accessibilitySystem.voice.getConfig();
      if (currentConfig.enabled) {
        accessibilitySystem.voice.disable();
      } else {
        accessibilitySystem.voice.enable();
      }
      setVoiceConfig(accessibilitySystem.voice.getConfig());
    }
  }, [accessibilitySystem]);

  // Touch management
  const updateTouch = useCallback((config: Partial<MobileTouchConfig>) => {
    if (accessibilitySystem) {
      accessibilitySystem.touch.updateConfig(config);
      setTouchConfig(accessibilitySystem.touch.getConfig());
    }
  }, [accessibilitySystem]);

  // Control functions
  const announceMessage = useCallback((message: string, priority: 'polite' | 'assertive' = 'polite') => {
    if (accessibilitySystem) {
      accessibilitySystem.screenReader.announce(message, priority);
    }
  }, [accessibilitySystem]);

  const setFocus = useCallback((element: HTMLElement | string) => {
    if (accessibilitySystem) {
      accessibilitySystem.focus.focus(element);
    }
  }, [accessibilitySystem]);

  const showKeyboardHelp = useCallback(() => {
    if (accessibilitySystem) {
      accessibilitySystem.keyboard.showHelp();
    }
  }, [accessibilitySystem]);

  const runAccessibilityAudit = useCallback(async () => {
    try {
      const { results, report } = await auditAccessibility();
      
      if (enableDevTools) {
        console.group('🔍 Accessibility Audit');
        console.log(`Score: ${results.summary.score}%`);
        console.log(`Errors: ${results.summary.errors}`);
        console.log(`Warnings: ${results.summary.warnings}`);
        console.log('Report:', report);
        console.groupEnd();
      }
      
      announceMessage(`Accessibility audit complete. Score: ${results.summary.score}%`);
    } catch (error) {
      console.error('Accessibility audit failed:', error);
      announceMessage('Accessibility audit failed');
    }
  }, [announceMessage, enableDevTools]);

  // Dev tools
  useEffect(() => {
    if (enableDevTools && isInitialized && typeof window !== 'undefined') {
      // Add global accessibility utilities for development
      (window as any).__accessibility = {
        audit: runAccessibilityAudit,
        announce: announceMessage,
        focus: setFocus,
        showHelp: showKeyboardHelp,
        status: () => status,
        theme: accessibilitySystem?.theme,
        voice: accessibilitySystem?.voice,
        touch: accessibilitySystem?.touch
      };

      console.log('🔧 Accessibility dev tools available at window.__accessibility');
    }
  }, [enableDevTools, isInitialized, status, accessibilitySystem, runAccessibilityAudit, announceMessage, setFocus, showKeyboardHelp]);

  const contextValue: AccessibilityContextValue = {
    isInitialized,
    status,
    themeConfig,
    updateTheme,
    voiceConfig,
    updateVoice,
    toggleVoiceNavigation,
    touchConfig,
    updateTouch,
    announceMessage,
    setFocus,
    showKeyboardHelp,
    runAccessibilityAudit,
    setupComponent: setupComponentAccessibility
  };

  return (
    <AccessibilityContext.Provider value={contextValue}>
      {children}
      {/* Accessibility status indicator for development */}
      {enableDevTools && isInitialized && (
        <AccessibilityDevIndicator />
      )}
    </AccessibilityContext.Provider>
  );
}

/**
 * Hook to use accessibility context
 */
export function useAccessibility() {
  const context = useContext(AccessibilityContext);
  if (!context) {
    throw new Error('useAccessibility must be used within AccessibilityProvider');
  }
  return context;
}

/**
 * Development indicator component
 */
function AccessibilityDevIndicator() {
  const { status, runAccessibilityAudit, showKeyboardHelp } = useAccessibility();
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <div className="fixed bottom-4 left-4 z-50 bg-blue-600 text-white rounded-lg shadow-lg">
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="p-2 hover:bg-blue-700 rounded-lg transition-colors"
        aria-label="Toggle accessibility dev tools"
      >
        ♿ A11Y
      </button>
      
      {isExpanded && (
        <div className="absolute bottom-full left-0 mb-2 bg-white text-black rounded-lg shadow-lg border p-4 min-w-64">
          <h3 className="font-semibold mb-2">Accessibility Status</h3>
          
          <div className="space-y-1 text-sm mb-3">
            <div className="flex justify-between">
              <span>Focus Management:</span>
              <span className={status.focusManagement ? 'text-green-600' : 'text-red-600'}>
                {status.focusManagement ? '✓' : '✗'}
              </span>
            </div>
            <div className="flex justify-between">
              <span>Screen Reader:</span>
              <span className={status.screenReader ? 'text-green-600' : 'text-red-600'}>
                {status.screenReader ? '✓' : '✗'}
              </span>
            </div>
            <div className="flex justify-between">
              <span>Keyboard Nav:</span>
              <span className={status.keyboardNavigation ? 'text-green-600' : 'text-red-600'}>
                {status.keyboardNavigation ? '✓' : '✗'}
              </span>
            </div>
            <div className="flex justify-between">
              <span>Voice Nav:</span>
              <span className={status.voiceNavigation ? 'text-green-600' : 'text-orange-600'}>
                {status.voiceNavigation ? '✓' : '⚠'}
              </span>
            </div>
            <div className="flex justify-between">
              <span>Touch:</span>
              <span className={status.mobileTouch ? 'text-green-600' : 'text-orange-600'}>
                {status.mobileTouch ? '✓' : '⚠'}
              </span>
            </div>
          </div>
          
          <div className="flex gap-2">
            <button
              onClick={runAccessibilityAudit}
              className="px-2 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700"
            >
              Audit
            </button>
            <button
              onClick={showKeyboardHelp}
              className="px-2 py-1 bg-gray-600 text-white text-xs rounded hover:bg-gray-700"
            >
              Help
            </button>
          </div>
        </div>
      )}
    </div>
  );
}

/**
 * HOC to wrap components with accessibility setup
 */
export function withAccessibility<P extends object>(
  Component: React.ComponentType<P>,
  options: Parameters<typeof setupComponentAccessibility>[1] = {}
) {
  const WrappedComponent = React.forwardRef<any, P>((props, ref) => {
    const { setupComponent } = useAccessibility();
    const elementRef = React.useRef<HTMLElement>(null);

    React.useEffect(() => {
      if (elementRef.current) {
        const cleanup = setupComponent(elementRef.current, options);
        return cleanup;
      }
    }, [setupComponent]);

    return (
      <div ref={elementRef}>
        <Component ref={ref} {...props} />
      </div>
    );
  });

  WrappedComponent.displayName = `withAccessibility(${Component.displayName || Component.name})`;
  return WrappedComponent;
}

/**
 * Hook for component-level accessibility setup
 */
export function useComponentAccessibility(
  options: Parameters<typeof setupComponentAccessibility>[1] = {}
) {
  const { setupComponent, announceMessage, setFocus } = useAccessibility();
  const elementRef = React.useRef<HTMLElement>(null);

  React.useEffect(() => {
    if (elementRef.current) {
      const cleanup = setupComponent(elementRef.current, options);
      return cleanup;
    }
  }, [setupComponent, options]);

  return {
    ref: elementRef,
    announce: announceMessage,
    focus: setFocus
  };
}