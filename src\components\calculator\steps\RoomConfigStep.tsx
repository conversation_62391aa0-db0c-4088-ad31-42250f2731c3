/**
 * Room Configuration Step Component - PLACEHOLDER
 * This component will be replaced by the Room Configuration Agent
 */

import React, { useEffect, useState } from 'react';
import { motion, type Variants } from 'framer-motion';
import { Home, Bed, Bath, ChefHat, Sofa, BookOpen, Package, Wind } from 'lucide-react';
import { EnhancedCard } from '@/components/ui/enhanced-card';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { cn } from '@/lib/utils';
import { 
  StepComponentProps, 
  StepValidation,
  VALIDATION_LIMITS
} from '../types/wizard';

// Animation variants
const containerVariants: Variants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.4,
      staggerChildren: 0.1,
    },
  },
  exit: {
    opacity: 0,
    y: -20,
    transition: { duration: 0.3 },
  },
};

const cardVariants: Variants = {
  hidden: { opacity: 0, scale: 0.95 },
  visible: { 
    opacity: 1, 
    scale: 1,
    transition: { duration: 0.3 }
  },
};

const fieldVariants: Variants = {
  hidden: { opacity: 0, x: -20 },
  visible: { opacity: 1, x: 0 },
};

export function RoomConfigStep({
  data,
  updateData,
  errors,
  onValidation,
  isActive,
}: StepComponentProps) {
  const [localErrors, setLocalErrors] = useState<Record<string, string>>({});

  // Validation function
  const validateStep = (): StepValidation => {
    const stepErrors: Record<string, string> = {};
    
    // Bedrooms validation
    const bedroomsValue = parseInt(data.bedrooms || '');
    if (!data.bedrooms || isNaN(bedroomsValue) || bedroomsValue <= 0) {
      stepErrors.bedrooms = 'Please select number of bedrooms';
    } else if (bedroomsValue < VALIDATION_LIMITS.bedrooms.min) {
      stepErrors.bedrooms = `Minimum ${VALIDATION_LIMITS.bedrooms.min} bedroom`;
    } else if (bedroomsValue > VALIDATION_LIMITS.bedrooms.max) {
      stepErrors.bedrooms = `Maximum ${VALIDATION_LIMITS.bedrooms.max} bedrooms`;
    }

    // Bathrooms validation
    const bathroomsValue = parseInt(data.bathrooms || '');
    if (!data.bathrooms || isNaN(bathroomsValue) || bathroomsValue <= 0) {
      stepErrors.bathrooms = 'Please select number of bathrooms';
    } else if (bathroomsValue < VALIDATION_LIMITS.bathrooms.min) {
      stepErrors.bathrooms = `Minimum ${VALIDATION_LIMITS.bathrooms.min} bathroom`;
    } else if (bathroomsValue > VALIDATION_LIMITS.bathrooms.max) {
      stepErrors.bathrooms = `Maximum ${VALIDATION_LIMITS.bathrooms.max} bathrooms`;
    }

    // Kitchens validation
    const kitchensValue = parseInt(data.kitchens || '');
    if (!data.kitchens || isNaN(kitchensValue) || kitchensValue <= 0) {
      stepErrors.kitchens = 'Please select number of kitchens';
    } else if (kitchensValue < VALIDATION_LIMITS.kitchens.min) {
      stepErrors.kitchens = `Minimum ${VALIDATION_LIMITS.kitchens.min} kitchen`;
    } else if (kitchensValue > VALIDATION_LIMITS.kitchens.max) {
      stepErrors.kitchens = `Maximum ${VALIDATION_LIMITS.kitchens.max} kitchens`;
    }

    setLocalErrors(stepErrors);
    
    const validation: StepValidation = {
      isValid: Object.keys(stepErrors).length === 0,
      errors: stepErrors,
    };

    if (onValidation) {
      onValidation(validation);
    }

    return validation;
  };

  // Trigger validation when data changes
  useEffect(() => {
    if (isActive) {
      validateStep();
    }
  }, [data, isActive]);

  // Helper function to handle input changes
  const handleInputChange = (field: string, value: string) => {
    updateData({ [field]: value });
    
    // Clear error when user starts typing
    if (localErrors[field]) {
      setLocalErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  if (!isActive) return null;

  return (
    <motion.div
      className="space-y-8"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      exit="exit"
    >
      {/* Header */}
      <motion.div 
        className="text-center space-y-2"
        variants={fieldVariants}
      >
        <div className="flex items-center justify-center gap-2 text-primary-600">
          <Home className="h-6 w-6" />
          <h2 className="text-2xl font-bold">Room Configuration</h2>
        </div>
        <p className="text-secondary-600 max-w-2xl mx-auto">
          Define the layout and room count for your project. This helps us calculate accurate space requirements and costs.
        </p>
        <div className="inline-flex items-center gap-2 px-3 py-1 bg-yellow-100 text-yellow-800 rounded-full text-sm">
          <span className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse"></span>
          PLACEHOLDER - Will be enhanced by Room Configuration Agent
        </div>
      </motion.div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Primary Rooms */}
        <motion.div variants={cardVariants}>
          <EnhancedCard 
            variant="outlined" 
            size="lg"
            header={
              <div className="flex items-center gap-2">
                <Bed className="h-5 w-5 text-primary-600" />
                <h3 className="text-lg font-semibold">Primary Rooms</h3>
              </div>
            }
          >
            <div className="space-y-4">
              {/* Bedrooms */}
              <motion.div className="space-y-2" variants={fieldVariants}>
                <Label htmlFor="bedrooms" className="text-sm font-medium">
                  Bedrooms *
                </Label>
                <Select
                  value={data.bedrooms || ''}
                  onValueChange={(value) => handleInputChange('bedrooms', value)}
                >
                  <SelectTrigger 
                    className={cn(
                      localErrors.bedrooms || errors.bedrooms 
                        ? 'border-red-500 focus:border-red-500' 
                        : ''
                    )}
                  >
                    <SelectValue placeholder="Select bedrooms" />
                  </SelectTrigger>
                  <SelectContent>
                    {[1, 2, 3, 4, 5, 6, 7, 8].map((num) => (
                      <SelectItem key={num} value={num.toString()}>
                        {num} Bedroom{num > 1 ? 's' : ''}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {(localErrors.bedrooms || errors.bedrooms) && (
                  <p className="text-sm text-red-600">
                    {localErrors.bedrooms || errors.bedrooms}
                  </p>
                )}
              </motion.div>

              {/* Bathrooms */}
              <motion.div className="space-y-2" variants={fieldVariants}>
                <Label htmlFor="bathrooms" className="text-sm font-medium">
                  Bathrooms *
                </Label>
                <Select
                  value={data.bathrooms || ''}
                  onValueChange={(value) => handleInputChange('bathrooms', value)}
                >
                  <SelectTrigger 
                    className={cn(
                      localErrors.bathrooms || errors.bathrooms 
                        ? 'border-red-500 focus:border-red-500' 
                        : ''
                    )}
                  >
                    <SelectValue placeholder="Select bathrooms" />
                  </SelectTrigger>
                  <SelectContent>
                    {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((num) => (
                      <SelectItem key={num} value={num.toString()}>
                        {num} Bathroom{num > 1 ? 's' : ''}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {(localErrors.bathrooms || errors.bathrooms) && (
                  <p className="text-sm text-red-600">
                    {localErrors.bathrooms || errors.bathrooms}
                  </p>
                )}
              </motion.div>

              {/* Kitchens */}
              <motion.div className="space-y-2" variants={fieldVariants}>
                <Label htmlFor="kitchens" className="text-sm font-medium">
                  Kitchens *
                </Label>
                <Select
                  value={data.kitchens || ''}
                  onValueChange={(value) => handleInputChange('kitchens', value)}
                >
                  <SelectTrigger 
                    className={cn(
                      localErrors.kitchens || errors.kitchens 
                        ? 'border-red-500 focus:border-red-500' 
                        : ''
                    )}
                  >
                    <SelectValue placeholder="Select kitchens" />
                  </SelectTrigger>
                  <SelectContent>
                    {[1, 2, 3].map((num) => (
                      <SelectItem key={num} value={num.toString()}>
                        {num} Kitchen{num > 1 ? 's' : ''}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {(localErrors.kitchens || errors.kitchens) && (
                  <p className="text-sm text-red-600">
                    {localErrors.kitchens || errors.kitchens}
                  </p>
                )}
              </motion.div>
            </div>
          </EnhancedCard>
        </motion.div>

        {/* Additional Rooms */}
        <motion.div variants={cardVariants}>
          <EnhancedCard 
            variant="outlined" 
            size="lg"
            header={
              <div className="flex items-center gap-2">
                <Sofa className="h-5 w-5 text-primary-600" />
                <h3 className="text-lg font-semibold">Additional Spaces</h3>
              </div>
            }
          >
            <div className="space-y-4">
              {/* Living Rooms */}
              <motion.div className="space-y-2" variants={fieldVariants}>
                <Label htmlFor="livingRooms">Living Rooms</Label>
                <Select
                  value={data.livingRooms || '1'}
                  onValueChange={(value) => handleInputChange('livingRooms', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select living rooms" />
                  </SelectTrigger>
                  <SelectContent>
                    {[1, 2, 3].map((num) => (
                      <SelectItem key={num} value={num.toString()}>
                        {num} Living Room{num > 1 ? 's' : ''}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </motion.div>

              {/* Study Rooms */}
              <motion.div className="space-y-2" variants={fieldVariants}>
                <Label htmlFor="studyRooms">Study Rooms</Label>
                <Select
                  value={data.studyRooms || '0'}
                  onValueChange={(value) => handleInputChange('studyRooms', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select study rooms" />
                  </SelectTrigger>
                  <SelectContent>
                    {[0, 1, 2, 3].map((num) => (
                      <SelectItem key={num} value={num.toString()}>
                        {num === 0 ? 'No Study Room' : `${num} Study Room${num > 1 ? 's' : ''}`}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </motion.div>

              {/* Store Rooms */}
              <motion.div className="space-y-2" variants={fieldVariants}>
                <Label htmlFor="storeRooms">Store Rooms</Label>
                <Select
                  value={data.storeRooms || '1'}
                  onValueChange={(value) => handleInputChange('storeRooms', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select store rooms" />
                  </SelectTrigger>
                  <SelectContent>
                    {[0, 1, 2, 3, 4].map((num) => (
                      <SelectItem key={num} value={num.toString()}>
                        {num === 0 ? 'No Store Room' : `${num} Store Room${num > 1 ? 's' : ''}`}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </motion.div>

              {/* Balconies */}
              <motion.div className="space-y-2" variants={fieldVariants}>
                <Label htmlFor="balconies">Balconies</Label>
                <Select
                  value={data.balconies || '1'}
                  onValueChange={(value) => handleInputChange('balconies', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select balconies" />
                  </SelectTrigger>
                  <SelectContent>
                    {[0, 1, 2, 3, 4, 5].map((num) => (
                      <SelectItem key={num} value={num.toString()}>
                        {num === 0 ? 'No Balcony' : `${num} Balcon${num > 1 ? 'ies' : 'y'}`}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </motion.div>
            </div>
          </EnhancedCard>
        </motion.div>
      </div>

      {/* Room Summary */}
      {(data.bedrooms || data.bathrooms || data.kitchens) && (
        <motion.div
          className="p-6 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200"
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.5 }}
        >
          <h4 className="font-semibold text-blue-900 mb-3">Room Configuration Summary</h4>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            {data.bedrooms && (
              <div className="flex items-center gap-2">
                <Bed className="h-4 w-4 text-blue-600" />
                <span>{data.bedrooms} Bedroom{parseInt(data.bedrooms) > 1 ? 's' : ''}</span>
              </div>
            )}
            {data.bathrooms && (
              <div className="flex items-center gap-2">
                <Bath className="h-4 w-4 text-blue-600" />
                <span>{data.bathrooms} Bathroom{parseInt(data.bathrooms) > 1 ? 's' : ''}</span>
              </div>
            )}
            {data.kitchens && (
              <div className="flex items-center gap-2">
                <ChefHat className="h-4 w-4 text-blue-600" />
                <span>{data.kitchens} Kitchen{parseInt(data.kitchens) > 1 ? 's' : ''}</span>
              </div>
            )}
            {data.livingRooms && parseInt(data.livingRooms) > 0 && (
              <div className="flex items-center gap-2">
                <Sofa className="h-4 w-4 text-blue-600" />
                <span>{data.livingRooms} Living Room{parseInt(data.livingRooms) > 1 ? 's' : ''}</span>
              </div>
            )}
          </div>
        </motion.div>
      )}
    </motion.div>
  );
}

export default RoomConfigStep;