/**
 * Room Configuration Step Component - Visual Room Selector
 * Enhanced MVP implementation with visual cards, cost calculations, and animations
 * Day 2 Task 2.2 - Complete visual room configuration interface
 */

import React, { useEffect, useState, useMemo } from 'react';
import { motion, type Variants } from 'framer-motion';
import { 
  Home, Bed, Bath, ChefHat, Sofa, BookOpen, Package, Car, Trees,
  Star, TrendingUp, Zap, Heart, Shield, Sparkles, Crown, Award,
  DollarSign, Calculator, Info, CheckCircle2, Users, Lightbulb
} from 'lucide-react';
import { EnhancedCard } from '@/components/ui/enhanced-card';
import { EnhancedButton } from '@/components/ui/enhanced-button';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import { 
  StepComponentProps, 
  StepValidation,
  VALIDATION_LIMITS
} from '../types/wizard';
import { 
  SmartBadge, 
  ContextualTip, 
  ProgressiveDisclosure 
} from '@/components/ui/contextual-help';
import { useSmartDefaults } from '@/hooks/useSmartDefaults';
import { useRecommendations } from '@/hooks/useRecommendations';

// Room cost multipliers (per sqft impact)
const ROOM_COSTS = {
  bedrooms: { base: 0, additional: 150 }, // Additional bedrooms add cost
  bathrooms: { base: 0, additional: 200 }, // Additional bathrooms add significant cost
  kitchens: {
    modular: 300,
    'semi-modular': 200,
    basic: 100
  },
  living: {
    single: 0,
    double: 100,
    'open-plan': 80
  },
  parking: {
    none: 0,
    open: 150,
    covered: 300,
    both: 400
  },
  additional: {
    pooja: 180,
    study: 220,
    store: 120,
    servant: 160
  }
} as const;

// Popular combinations
const POPULAR_COMBINATIONS = {
  '2-2-modular': { bedrooms: 2, bathrooms: 2, kitchen: 'modular' },
  '3-2-modular': { bedrooms: 3, bathrooms: 2, kitchen: 'modular' },
  '3-3-modular': { bedrooms: 3, bathrooms: 3, kitchen: 'modular' }
} as const;

// Animation variants
const containerVariants: Variants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.4,
      staggerChildren: 0.08,
    },
  },
  exit: {
    opacity: 0,
    y: -20,
    transition: { duration: 0.3 },
  },
};

const sectionVariants: Variants = {
  hidden: { opacity: 0, y: 15 },
  visible: { 
    opacity: 1, 
    y: 0,
    transition: { 
      duration: 0.3,
      staggerChildren: 0.05
    }
  },
};

const cardVariants: Variants = {
  hidden: { opacity: 0, scale: 0.9, y: 10 },
  visible: { 
    opacity: 1, 
    scale: 1,
    y: 0,
    transition: { 
      duration: 0.2,
      ease: "easeOut"
    }
  },
  hover: {
    scale: 1.02,
    y: -2,
    transition: { duration: 0.15 }
  },
  tap: {
    scale: 0.98,
    transition: { duration: 0.1 }
  }
};

const badgeVariants: Variants = {
  hidden: { opacity: 0, scale: 0.8 },
  visible: { 
    opacity: 1, 
    scale: 1,
    transition: { delay: 0.2 }
  },
};

// Room option type definitions
interface RoomOption {
  value: string;
  label: string;
  icon: React.ElementType;
  description?: string;
  costImpact?: number;
  popular?: boolean;
  recommended?: boolean;
}

interface VisualSelectorProps {
  options: RoomOption[];
  value: string;
  onChange: (value: string) => void;
  title: string;
  description?: string;
  multiple?: boolean;
  grid?: number;
}

// Visual Selector Component
const VisualSelector: React.FC<VisualSelectorProps> = ({
  options,
  value,
  onChange,
  title,
  description,
  grid = 3
}) => {
  return (
    <motion.div 
      className="space-y-3"
      variants={sectionVariants}
    >
      <div className="space-y-1">
        <h4 className="font-medium text-secondary-900">{title}</h4>
        {description && (
          <p className="text-sm text-secondary-600">{description}</p>
        )}
      </div>
      
      <div className={cn(
        "grid gap-3",
        grid === 2 && "grid-cols-2",
        grid === 3 && "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3",
        grid === 4 && "grid-cols-2 lg:grid-cols-4",
        grid === 5 && "grid-cols-2 sm:grid-cols-3 lg:grid-cols-5"
      )}>
        {options.map((option) => {
          const isSelected = value === option.value;
          const Icon = option.icon;
          
          return (
            <TooltipProvider key={option.value}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <motion.button
                    type="button"
                    className={cn(
                      "relative p-4 rounded-lg border-2 transition-all duration-200",
                      "flex flex-col items-center gap-2 text-center min-h-[120px]",
                      "hover:shadow-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2",
                      isSelected
                        ? "border-primary-500 bg-primary-50 text-primary-700"
                        : "border-secondary-200 bg-white text-secondary-700 hover:border-secondary-300"
                    )}
                    variants={cardVariants}
                    whileHover="hover"
                    whileTap="tap"
                    onClick={() => onChange(option.value)}
                  >
                    {/* Popular Badge */}
                    {option.popular && (
                      <motion.div
                        className="absolute -top-2 -right-2"
                        variants={badgeVariants}
                      >
                        <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 text-xs">
                          <Star className="h-3 w-3 mr-1" />
                          Popular
                        </Badge>
                      </motion.div>
                    )}
                    
                    {/* Recommended Badge */}
                    {option.recommended && (
                      <motion.div
                        className="absolute -top-2 -right-2"
                        variants={badgeVariants}
                      >
                        <Badge variant="secondary" className="bg-green-100 text-green-800 text-xs">
                          <CheckCircle2 className="h-3 w-3 mr-1" />
                          Recommended
                        </Badge>
                      </motion.div>
                    )}
                    
                    {/* Icon */}
                    <Icon className={cn(
                      "h-8 w-8 transition-colors",
                      isSelected ? "text-primary-600" : "text-secondary-500"
                    )} />
                    
                    {/* Label */}
                    <span className={cn(
                      "font-medium text-sm",
                      isSelected ? "text-primary-700" : "text-secondary-700"
                    )}>
                      {option.label}
                    </span>
                    
                    {/* Cost Impact */}
                    {option.costImpact !== undefined && option.costImpact > 0 && (
                      <span className={cn(
                        "text-xs font-medium",
                        isSelected ? "text-primary-600" : "text-secondary-500"
                      )}>
                        +₹{option.costImpact}/sqft
                      </span>
                    )}
                    
                    {/* Selection Indicator */}
                    {isSelected && (
                      <motion.div
                        className="absolute inset-0 rounded-lg border-2 border-primary-500 bg-primary-500/5"
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ duration: 0.2 }}
                      />
                    )}
                  </motion.button>
                </TooltipTrigger>
                <TooltipContent>
                  <div className="space-y-1">
                    <p className="font-medium">{option.label}</p>
                    {option.description && (
                      <p className="text-sm">{option.description}</p>
                    )}
                    {option.costImpact !== undefined && (
                      <p className="text-sm font-medium">
                        Cost impact: ₹{option.costImpact}/sqft
                      </p>
                    )}
                  </div>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          );
        })}
      </div>
    </motion.div>
  );
};

export function RoomConfigStep({
  data,
  updateData,
  errors,
  onValidation,
  isActive,
}: StepComponentProps) {
  const [localErrors, setLocalErrors] = useState<Record<string, string>>({});
  const [selectedAdditional, setSelectedAdditional] = useState<string[]>([
    ...(data.studyRooms && parseInt(data.studyRooms) > 0 ? ['study'] : []),
    ...(data.storeRooms && parseInt(data.storeRooms) > 0 ? ['store'] : []),
  ]);

  // Smart defaults and recommendations
  const { suggestions, applySuggestion, dismissSuggestion } = useSmartDefaults(
    data,
    updateData,
    {
      context: { location: data.location, plotSize: parseFloat(data.plotSize || '0') },
      enabled: isActive,
      autoApply: false,
      confidenceThreshold: 0.6
    }
  );

  const { recommendations, peopleAlsoChose } = useRecommendations(data, updateData, {
    enabled: isActive,
    maxRecommendations: 4,
    typeFilter: ['feature-suggestion', 'tip'],
    categoryFilter: ['design', 'functionality']
  });

  // Room option configurations
  const bedroomOptions: RoomOption[] = [
    { value: '1', label: '1 Bedroom', icon: Bed, description: 'Compact single bedroom' },
    { value: '2', label: '2 Bedrooms', icon: Bed, description: 'Perfect for small families', popular: true },
    { value: '3', label: '3 Bedrooms', icon: Bed, description: 'Ideal family configuration', recommended: true },
    { value: '4', label: '4 Bedrooms', icon: Bed, description: 'Spacious family home', costImpact: 150 },
    { value: '5', label: '5+ Bedrooms', icon: Bed, description: 'Large family residence', costImpact: 300 },
  ];

  const bathroomOptions: RoomOption[] = [
    { value: '1', label: '1 Bathroom', icon: Bath, description: 'Basic configuration' },
    { value: '2', label: '2 Bathrooms', icon: Bath, description: 'One attached, one common', popular: true },
    { value: '3', label: '3 Bathrooms', icon: Bath, description: 'All bedrooms attached', recommended: true },
    { value: '4', label: '4+ Bathrooms', icon: Bath, description: 'Premium bathroom configuration', costImpact: 200 },
  ];

  const kitchenOptions: RoomOption[] = [
    { 
      value: 'basic', 
      label: 'Basic Kitchen', 
      icon: ChefHat, 
      description: 'Essential cooking setup',
      costImpact: ROOM_COSTS.kitchens.basic
    },
    { 
      value: 'semi-modular', 
      label: 'Semi-Modular', 
      icon: ChefHat, 
      description: 'Mixed traditional & modular',
      costImpact: ROOM_COSTS.kitchens['semi-modular'],
      popular: true
    },
    { 
      value: 'modular', 
      label: 'Modular Kitchen', 
      icon: ChefHat, 
      description: 'Full modular with appliances',
      costImpact: ROOM_COSTS.kitchens.modular,
      recommended: true
    },
  ];

  const livingOptions: RoomOption[] = [
    { 
      value: 'single', 
      label: '1 Living Room', 
      icon: Sofa, 
      description: 'Traditional living space',
      popular: true
    },
    { 
      value: 'double', 
      label: '2 Living Rooms', 
      icon: Sofa, 
      description: 'Formal + family living',
      costImpact: ROOM_COSTS.living.double
    },
    { 
      value: 'open-plan', 
      label: 'Open Plan', 
      icon: Sofa, 
      description: 'Open living-dining concept',
      costImpact: ROOM_COSTS.living['open-plan'],
      recommended: true
    },
  ];

  const parkingOptions: RoomOption[] = [
    { value: 'none', label: 'No Parking', icon: Car, description: 'Street parking only' },
    { 
      value: 'open', 
      label: 'Open Parking', 
      icon: Car, 
      description: 'Uncovered parking space',
      costImpact: ROOM_COSTS.parking.open
    },
    { 
      value: 'covered', 
      label: 'Covered Parking', 
      icon: Car, 
      description: 'Protected parking space',
      costImpact: ROOM_COSTS.parking.covered,
      popular: true
    },
    { 
      value: 'both', 
      label: 'Both Options', 
      icon: Car, 
      description: 'Covered + open spaces',
      costImpact: ROOM_COSTS.parking.both
    },
  ];

  const additionalOptions: RoomOption[] = [
    { 
      value: 'pooja', 
      label: 'Pooja Room', 
      icon: Crown, 
      description: 'Dedicated prayer space',
      costImpact: ROOM_COSTS.additional.pooja
    },
    { 
      value: 'study', 
      label: 'Study Room', 
      icon: BookOpen, 
      description: 'Home office/study space',
      costImpact: ROOM_COSTS.additional.study,
      popular: true
    },
    { 
      value: 'store', 
      label: 'Store Room', 
      icon: Package, 
      description: 'Storage/utility room',
      costImpact: ROOM_COSTS.additional.store
    },
    { 
      value: 'servant', 
      label: 'Servant Room', 
      icon: Home, 
      description: 'Separate quarters',
      costImpact: ROOM_COSTS.additional.servant
    },
  ];

  // Calculate total cost impact
  const calculateCostImpact = useMemo(() => {
    let totalImpact = 0;
    
    // Bedroom impact (additional bedrooms beyond 2)
    const bedrooms = parseInt(data.bedrooms || '2');
    if (bedrooms > 2) {
      totalImpact += (bedrooms - 2) * ROOM_COSTS.bedrooms.additional;
    }
    
    // Bathroom impact (additional bathrooms beyond 2)
    const bathrooms = parseInt(data.bathrooms || '2');
    if (bathrooms > 2) {
      totalImpact += (bathrooms - 2) * ROOM_COSTS.bathrooms.additional;
    }
    
    // Kitchen type impact
    const kitchenType = data.kitchens || 'modular';
    if (kitchenType in ROOM_COSTS.kitchens) {
      totalImpact += ROOM_COSTS.kitchens[kitchenType as keyof typeof ROOM_COSTS.kitchens];
    }
    
    // Living space impact
    const livingType = data.livingRooms || 'single';
    if (livingType in ROOM_COSTS.living) {
      totalImpact += ROOM_COSTS.living[livingType as keyof typeof ROOM_COSTS.living];
    }
    
    // Parking impact
    const parkingType = data.parkingSpaces || 'none';
    if (parkingType in ROOM_COSTS.parking) {
      totalImpact += ROOM_COSTS.parking[parkingType as keyof typeof ROOM_COSTS.parking];
    }
    
    // Additional rooms impact
    selectedAdditional.forEach(room => {
      if (room in ROOM_COSTS.additional) {
        totalImpact += ROOM_COSTS.additional[room as keyof typeof ROOM_COSTS.additional];
      }
    });
    
    return totalImpact;
  }, [data.bedrooms, data.bathrooms, data.kitchens, data.livingRooms, data.parkingSpaces, selectedAdditional]);

  // Validation function
  const validateStep = (): StepValidation => {
    const stepErrors: Record<string, string> = {};
    
    // Bedrooms validation
    const bedroomsValue = parseInt(data.bedrooms || '');
    if (!data.bedrooms || isNaN(bedroomsValue) || bedroomsValue <= 0) {
      stepErrors.bedrooms = 'Please select number of bedrooms';
    } else if (bedroomsValue < VALIDATION_LIMITS.bedrooms.min) {
      stepErrors.bedrooms = `Minimum ${VALIDATION_LIMITS.bedrooms.min} bedroom`;
    } else if (bedroomsValue > VALIDATION_LIMITS.bedrooms.max) {
      stepErrors.bedrooms = `Maximum ${VALIDATION_LIMITS.bedrooms.max} bedrooms`;
    }

    // Bathrooms validation
    const bathroomsValue = parseInt(data.bathrooms || '');
    if (!data.bathrooms || isNaN(bathroomsValue) || bathroomsValue <= 0) {
      stepErrors.bathrooms = 'Please select number of bathrooms';
    } else if (bathroomsValue < VALIDATION_LIMITS.bathrooms.min) {
      stepErrors.bathrooms = `Minimum ${VALIDATION_LIMITS.bathrooms.min} bathroom`;
    } else if (bathroomsValue > VALIDATION_LIMITS.bathrooms.max) {
      stepErrors.bathrooms = `Maximum ${VALIDATION_LIMITS.bathrooms.max} bathrooms`;
    }

    // Kitchen type validation
    if (!data.kitchens) {
      stepErrors.kitchens = 'Please select kitchen type';
    }

    // Living space validation
    if (!data.livingRooms) {
      stepErrors.livingRooms = 'Please select living space configuration';
    }

    // Logical validation: bathrooms shouldn't exceed bedrooms + 1
    if (bedroomsValue && bathroomsValue && bathroomsValue > bedroomsValue + 1) {
      stepErrors.bathrooms = 'Too many bathrooms for bedroom count';
    }

    setLocalErrors(stepErrors);
    
    const validation: StepValidation = {
      isValid: Object.keys(stepErrors).length === 0,
      errors: stepErrors,
      warnings: bedroomsValue > 4 ? {
        bedrooms: 'Large homes require additional structural considerations'
      } : undefined
    };

    if (onValidation) {
      onValidation(validation);
    }

    return validation;
  };

  // Trigger validation when data changes
  useEffect(() => {
    if (isActive) {
      validateStep();
    }
  }, [data, isActive]);

  // Helper function to handle input changes
  const handleInputChange = (field: string, value: string) => {
    updateData({ [field]: value });
    
    // Clear error when user starts typing
    if (localErrors[field]) {
      setLocalErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  // Handle additional room selection
  const handleAdditionalRoomToggle = (roomType: string) => {
    const newSelected = selectedAdditional.includes(roomType)
      ? selectedAdditional.filter(r => r !== roomType)
      : [...selectedAdditional, roomType];
    
    setSelectedAdditional(newSelected);
    
    // Update form data based on selections
    updateData({
      studyRooms: newSelected.includes('study') ? '1' : '0',
      storeRooms: newSelected.includes('store') ? '1' : '0',
    });
  };

  // Check if current selection is popular
  const isPopularCombination = useMemo(() => {
    const bedrooms = parseInt(data.bedrooms || '0');
    const bathrooms = parseInt(data.bathrooms || '0');
    const kitchen = data.kitchens;
    
    return Object.values(POPULAR_COMBINATIONS).some(combo => 
      combo.bedrooms === bedrooms && 
      combo.bathrooms === bathrooms && 
      combo.kitchen === kitchen
    );
  }, [data.bedrooms, data.bathrooms, data.kitchens]);

  if (!isActive) return null;

  return (
    <motion.div
      className="space-y-8"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      exit="exit"
    >
      {/* Header */}
      <motion.div 
        className="text-center space-y-4"
        variants={sectionVariants}
      >
        <div className="flex items-center justify-center gap-3 text-primary-600">
          <motion.div
            whileHover={{ rotate: 10, scale: 1.1 }}
            transition={{ duration: 0.2 }}
          >
            <Home className="h-8 w-8" />
          </motion.div>
          <h2 className="text-3xl font-bold bg-gradient-to-r from-primary-600 to-primary-800 bg-clip-text text-transparent">
            Room Configuration
          </h2>
        </div>
        <p className="text-secondary-600 max-w-3xl mx-auto text-lg">
          Configure your home's layout with our visual room selector. Each choice shows real-time cost impact to help you make informed decisions.
        </p>
        
        {/* Cost Impact Display */}
        {calculateCostImpact > 0 && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="inline-flex items-center gap-2 px-4 py-2 bg-green-100 text-green-800 rounded-full text-sm font-medium"
          >
            <TrendingUp className="h-4 w-4" />
            Total Impact: +₹{calculateCostImpact.toLocaleString()}/sqft
            {isPopularCombination && (
              <Badge variant="secondary" className="ml-2 bg-yellow-100 text-yellow-800">
                <Star className="h-3 w-3 mr-1" />
                Popular Choice
              </Badge>
            )}
          </motion.div>
        )}

        {/* People Also Chose */}
        {peopleAlsoChose.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-purple-50 border border-purple-200 rounded-lg p-4"
          >
            <h4 className="font-medium text-purple-900 flex items-center gap-2 mb-3">
              <Users className="h-4 w-4" />
              People like you also chose
            </h4>
            <div className="space-y-2">
              {peopleAlsoChose.map((choice, index) => (
                <div key={index} className="flex items-center justify-between text-sm">
                  <span className="text-purple-700">{choice.feature}</span>
                  <div className="flex items-center gap-2">
                    <span className="text-purple-600 font-medium">{choice.percentage}%</span>
                    <span className="text-purple-500 text-xs">{choice.description}</span>
                  </div>
                </div>
              ))}
            </div>
          </motion.div>
        )}
      </motion.div>

      {/* Main Content Grid */}
      <div className="space-y-12">
        {/* Essential Rooms */}
        <motion.section variants={sectionVariants}>
          <EnhancedCard 
            variant="glass" 
            size="lg"
            className="border-2 border-primary-200"
          >
            <div className="space-y-8">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-primary-100 rounded-lg">
                  <Home className="h-6 w-6 text-primary-600" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-primary-900">Essential Rooms</h3>
                  <p className="text-primary-600">Core spaces that define your home</p>
                </div>
              </div>

              {/* Bedrooms */}
              <div className="space-y-4">
                <VisualSelector
                  title="Bedrooms"
                  description="How many bedrooms do you need?"
                  options={bedroomOptions}
                  value={data.bedrooms || ''}
                  onChange={(value) => handleInputChange('bedrooms', value)}
                  grid={5}
                />

                {/* Smart Suggestions for Bedrooms */}
                {suggestions
                  .filter(s => s.field === 'bedrooms' && s.canApply)
                  .map(suggestion => (
                    <motion.div
                      key={suggestion.field}
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="flex items-center justify-between p-3 bg-blue-50 border border-blue-200 rounded-lg"
                    >
                      <div className="flex items-center gap-2">
                        <SmartBadge type="recommended" confidence={suggestion.confidence} />
                        <span className="text-sm text-blue-800">
                          {suggestion.reason}: {suggestion.value} bedrooms
                        </span>
                      </div>
                      <button
                        onClick={() => applySuggestion(suggestion.field, suggestion.value)}
                        className="text-xs text-blue-600 underline hover:text-blue-800 font-medium"
                      >
                        Apply
                      </button>
                    </motion.div>
                  ))}

                {(localErrors.bedrooms || errors.bedrooms) && (
                  <motion.p 
                    className="text-sm text-red-600 flex items-center gap-2"
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                  >
                    <Info className="h-4 w-4" />
                    {localErrors.bedrooms || errors.bedrooms}
                  </motion.p>
                )}
              </div>

              {/* Bathrooms */}
              <div className="space-y-4">
                <VisualSelector
                  title="Bathrooms"
                  description="How many bathrooms for optimal convenience?"
                  options={bathroomOptions}
                  value={data.bathrooms || ''}
                  onChange={(value) => handleInputChange('bathrooms', value)}
                  grid={4}
                />
                {(localErrors.bathrooms || errors.bathrooms) && (
                  <motion.p 
                    className="text-sm text-red-600 flex items-center gap-2"
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                  >
                    <Info className="h-4 w-4" />
                    {localErrors.bathrooms || errors.bathrooms}
                  </motion.p>
                )}
              </div>
            </div>
          </EnhancedCard>
        </motion.section>

        {/* Kitchen & Living */}
        <motion.section variants={sectionVariants}>
          <EnhancedCard 
            variant="gradient" 
            size="lg"
          >
            <div className="space-y-8">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-orange-100 rounded-lg">
                  <ChefHat className="h-6 w-6 text-orange-600" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-secondary-900">Kitchen & Living Spaces</h3>
                  <p className="text-secondary-600">Choose your lifestyle preferences</p>
                </div>
              </div>

              {/* Kitchen Type */}
              <div className="space-y-4">
                <VisualSelector
                  title="Kitchen Type"
                  description="Select the kitchen style that suits your cooking needs"
                  options={kitchenOptions}
                  value={data.kitchens || ''}
                  onChange={(value) => handleInputChange('kitchens', value)}
                  grid={3}
                />
                {(localErrors.kitchens || errors.kitchens) && (
                  <motion.p 
                    className="text-sm text-red-600 flex items-center gap-2"
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                  >
                    <Info className="h-4 w-4" />
                    {localErrors.kitchens || errors.kitchens}
                  </motion.p>
                )}
              </div>

              {/* Living Spaces */}
              <div className="space-y-4">
                <VisualSelector
                  title="Living Spaces"
                  description="How do you envision your living areas?"
                  options={livingOptions}
                  value={data.livingRooms || ''}
                  onChange={(value) => handleInputChange('livingRooms', value)}
                  grid={3}
                />
                {(localErrors.livingRooms || errors.livingRooms) && (
                  <motion.p 
                    className="text-sm text-red-600 flex items-center gap-2"
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                  >
                    <Info className="h-4 w-4" />
                    {localErrors.livingRooms || errors.livingRooms}
                  </motion.p>
                )}
              </div>
            </div>
          </EnhancedCard>
        </motion.section>

        {/* Parking */}
        <motion.section variants={sectionVariants}>
          <EnhancedCard 
            variant="outlined" 
            size="lg"
          >
            <div className="space-y-6">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Car className="h-6 w-6 text-blue-600" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-secondary-900">Parking & External</h3>
                  <p className="text-secondary-600">Vehicle parking and outdoor spaces</p>
                </div>
              </div>

              <VisualSelector
                title="Parking Options"
                description="Choose your vehicle parking requirements"
                options={parkingOptions}
                value={data.parkingSpaces || ''}
                onChange={(value) => handleInputChange('parkingSpaces', value)}
                grid={4}
              />
            </div>
          </EnhancedCard>
        </motion.section>

        {/* Additional Rooms */}
        <motion.section variants={sectionVariants}>
          <EnhancedCard 
            variant="elevated" 
            size="lg"
          >
            <div className="space-y-6">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <Package className="h-6 w-6 text-purple-600" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-secondary-900">Additional Rooms</h3>
                  <p className="text-secondary-600">Optional spaces to enhance your lifestyle</p>
                </div>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                {additionalOptions.map((option) => {
                  const isSelected = selectedAdditional.includes(option.value);
                  const Icon = option.icon;
                  
                  return (
                    <TooltipProvider key={option.value}>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <motion.button
                            type="button"
                            className={cn(
                              "relative p-4 rounded-lg border-2 transition-all duration-200",
                              "flex flex-col items-center gap-2 text-center min-h-[120px]",
                              "hover:shadow-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2",
                              isSelected
                                ? "border-primary-500 bg-primary-50 text-primary-700"
                                : "border-secondary-200 bg-white text-secondary-700 hover:border-secondary-300"
                            )}
                            variants={cardVariants}
                            whileHover="hover"
                            whileTap="tap"
                            onClick={() => handleAdditionalRoomToggle(option.value)}
                          >
                            {option.popular && (
                              <motion.div
                                className="absolute -top-2 -right-2"
                                variants={badgeVariants}
                              >
                                <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 text-xs">
                                  <Star className="h-3 w-3 mr-1" />
                                  Popular
                                </Badge>
                              </motion.div>
                            )}
                            
                            <Icon className={cn(
                              "h-8 w-8 transition-colors",
                              isSelected ? "text-primary-600" : "text-secondary-500"
                            )} />
                            
                            <span className={cn(
                              "font-medium text-sm",
                              isSelected ? "text-primary-700" : "text-secondary-700"
                            )}>
                              {option.label}
                            </span>
                            
                            {option.costImpact !== undefined && (
                              <span className={cn(
                                "text-xs font-medium",
                                isSelected ? "text-primary-600" : "text-secondary-500"
                              )}>
                                +₹{option.costImpact}/sqft
                              </span>
                            )}
                            
                            {isSelected && (
                              <motion.div
                                className="absolute inset-0 rounded-lg border-2 border-primary-500 bg-primary-500/5"
                                initial={{ opacity: 0, scale: 0.8 }}
                                animate={{ opacity: 1, scale: 1 }}
                                transition={{ duration: 0.2 }}
                              />
                            )}
                          </motion.button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <div className="space-y-1">
                            <p className="font-medium">{option.label}</p>
                            {option.description && (
                              <p className="text-sm">{option.description}</p>
                            )}
                            {option.costImpact !== undefined && (
                              <p className="text-sm font-medium">
                                Cost impact: ₹{option.costImpact}/sqft
                              </p>
                            )}
                          </div>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  );
                })}
              </div>
            </div>
          </EnhancedCard>
        </motion.section>
      </div>

      {/* Configuration Summary */}
      {(data.bedrooms || data.bathrooms || data.kitchens) && (
        <motion.section
          variants={sectionVariants}
          className="mt-8"
        >
          <EnhancedCard
            variant="gradient"
            size="lg"
            className="border-2 border-primary-300 bg-gradient-to-r from-primary-50 via-white to-primary-50"
          >
            <div className="space-y-6">
              {/* Header */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <motion.div
                    className="p-2 bg-primary-100 rounded-lg"
                    whileHover={{ rotate: 5, scale: 1.05 }}
                  >
                    <Calculator className="h-6 w-6 text-primary-600" />
                  </motion.div>
                  <div>
                    <h4 className="text-xl font-bold text-primary-900">Configuration Summary</h4>
                    <p className="text-primary-600">Your selected room layout and cost impact</p>
                  </div>
                </div>
                
                {/* Total Cost Impact */}
                {calculateCostImpact > 0 && (
                  <motion.div
                    className="text-right"
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                  >
                    <div className="text-2xl font-bold text-green-600">
                      +₹{calculateCostImpact.toLocaleString()}
                    </div>
                    <div className="text-sm text-green-700">per sqft impact</div>
                  </motion.div>
                )}
              </div>

              {/* Room Grid */}
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
                {data.bedrooms && (
                  <motion.div 
                    className="flex flex-col items-center gap-2 p-3 bg-white rounded-lg border border-primary-200"
                    whileHover={{ scale: 1.02 }}
                  >
                    <Bed className="h-5 w-5 text-primary-600" />
                    <span className="font-medium text-sm text-primary-900">
                      {data.bedrooms} Bedroom{parseInt(data.bedrooms) > 1 ? 's' : ''}
                    </span>
                  </motion.div>
                )}
                
                {data.bathrooms && (
                  <motion.div 
                    className="flex flex-col items-center gap-2 p-3 bg-white rounded-lg border border-primary-200"
                    whileHover={{ scale: 1.02 }}
                  >
                    <Bath className="h-5 w-5 text-primary-600" />
                    <span className="font-medium text-sm text-primary-900">
                      {data.bathrooms} Bathroom{parseInt(data.bathrooms) > 1 ? 's' : ''}
                    </span>
                  </motion.div>
                )}
                
                {data.kitchens && (
                  <motion.div 
                    className="flex flex-col items-center gap-2 p-3 bg-white rounded-lg border border-primary-200"
                    whileHover={{ scale: 1.02 }}
                  >
                    <ChefHat className="h-5 w-5 text-orange-600" />
                    <span className="font-medium text-sm text-primary-900 capitalize">
                      {data.kitchens.replace('-', ' ')} Kitchen
                    </span>
                  </motion.div>
                )}
                
                {data.livingRooms && (
                  <motion.div 
                    className="flex flex-col items-center gap-2 p-3 bg-white rounded-lg border border-primary-200"
                    whileHover={{ scale: 1.02 }}
                  >
                    <Sofa className="h-5 w-5 text-blue-600" />
                    <span className="font-medium text-sm text-primary-900 capitalize">
                      {data.livingRooms.replace('-', ' ')}
                    </span>
                  </motion.div>
                )}
                
                {data.parkingSpaces && data.parkingSpaces !== 'none' && (
                  <motion.div 
                    className="flex flex-col items-center gap-2 p-3 bg-white rounded-lg border border-primary-200"
                    whileHover={{ scale: 1.02 }}
                  >
                    <Car className="h-5 w-5 text-blue-600" />
                    <span className="font-medium text-sm text-primary-900 capitalize">
                      {data.parkingSpaces.replace('-', ' ')} Parking
                    </span>
                  </motion.div>
                )}
              </div>

              {/* Additional Rooms */}
              {selectedAdditional.length > 0 && (
                <div className="space-y-3">
                  <h5 className="font-medium text-primary-900">Additional Rooms</h5>
                  <div className="flex flex-wrap gap-2">
                    {selectedAdditional.map((room) => {
                      const option = additionalOptions.find(opt => opt.value === room);
                      if (!option) return null;
                      const Icon = option.icon;
                      
                      return (
                        <motion.div
                          key={room}
                          className="flex items-center gap-2 px-3 py-1 bg-primary-100 text-primary-700 rounded-full text-sm"
                          initial={{ opacity: 0, scale: 0.8 }}
                          animate={{ opacity: 1, scale: 1 }}
                          whileHover={{ scale: 1.05 }}
                        >
                          <Icon className="h-4 w-4" />
                          <span>{option.label}</span>
                          <span className="text-xs">+₹{option.costImpact}</span>
                        </motion.div>
                      );
                    })}
                  </div>
                </div>
              )}

              {/* Popular Choice Indicator */}
              {isPopularCombination && (
                <motion.div
                  className="flex items-center justify-center gap-2 p-3 bg-yellow-100 text-yellow-800 rounded-lg"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}
                >
                  <Award className="h-5 w-5" />
                  <span className="font-medium">Popular Choice - Great configuration for most families!</span>
                </motion.div>
              )}
            </div>
          </EnhancedCard>
        </motion.section>
      )}

      {/* AI Recommendations Section */}
      {recommendations.length > 0 && (
        <motion.section
          variants={sectionVariants}
          className="mt-8"
        >
          <ProgressiveDisclosure
            title="Smart Room Recommendations"
            level="intermediate"
            defaultOpen={false}
          >
            <div className="space-y-4">
              {recommendations.map((rec) => (
                <div key={rec.id} className="flex items-start gap-3 p-4 bg-gradient-to-r from-purple-50 to-blue-50 border border-purple-200 rounded-lg">
                  <Lightbulb className="h-5 w-5 text-purple-600 mt-0.5 flex-shrink-0" />
                  <div className="flex-1">
                    <h5 className="font-medium text-purple-900">{rec.title}</h5>
                    <p className="text-sm text-purple-700 mt-1">{rec.description}</p>
                    {rec.savings && (
                      <p className="text-xs text-green-600 mt-2 font-medium">
                        Potential savings: ₹{(rec.savings / 100000).toFixed(1)}L
                      </p>
                    )}
                    {rec.action && (
                      <button
                        onClick={() => applySuggestion(rec.action!.field, rec.action!.value)}
                        className="mt-2 text-xs text-purple-600 underline hover:text-purple-800 font-medium"
                      >
                        {rec.action.label}
                      </button>
                    )}
                  </div>
                  <SmartBadge type="ai-suggestion" confidence={rec.confidence} />
                </div>
              ))}
            </div>
          </ProgressiveDisclosure>
        </motion.section>
      )}
    </motion.div>
  );
}

export default RoomConfigStep;