/**
 * Enhanced Card Component for MVP Design System
 * Professional card with variants, hover effects, and animations
 */

import * as React from "react";
import { motion, type Variants } from "framer-motion";
import { cn } from "@/lib/utils";

type CardVariant = "default" | "outlined" | "elevated" | "glass" | "gradient";
type CardSize = "sm" | "md" | "lg" | "xl";

interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: CardVariant;
  size?: CardSize;
  interactive?: boolean;
  loading?: boolean;
  header?: React.ReactNode;
  footer?: React.ReactNode;
  children?: React.ReactNode;
}

// Animation variants
const cardVariants: Variants = {
  idle: {
    y: 0,
    scale: 1,
    boxShadow: "0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)",
  },
  hover: {
    y: -4,
    scale: 1.01,
    boxShadow: "0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)",
    transition: { duration: 0.2, ease: "easeOut" },
  },
  tap: {
    scale: 0.98,
    transition: { duration: 0.1 },
  },
};

const contentVariants: Variants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.3,
      ease: "easeOut",
      staggerChildren: 0.1,
    },
  },
};

const childVariants: Variants = {
  hidden: { opacity: 0, y: 10 },
  visible: { opacity: 1, y: 0 },
};

// Base card styles
const baseClasses = cn(
  "relative overflow-hidden rounded-xl transition-all duration-200",
  "border border-secondary-200 bg-white"
);

// Variant styles
const variantClasses: Record<CardVariant, string> = {
  default: cn(
    "bg-white border-secondary-200",
    "shadow-md"
  ),
  outlined: cn(
    "bg-white border-secondary-300 border-2",
    "shadow-sm"
  ),
  elevated: cn(
    "bg-white border-secondary-100",
    "shadow-xl shadow-secondary-500/10"
  ),
  glass: cn(
    "bg-white/80 backdrop-blur-lg border-white/20",
    "shadow-lg"
  ),
  gradient: cn(
    "bg-gradient-to-br from-white to-primary-50",
    "border-primary-200 shadow-lg shadow-primary-500/10"
  ),
};

// Size styles
const sizeClasses: Record<CardSize, string> = {
  sm: "p-4",
  md: "p-6",
  lg: "p-8", 
  xl: "p-10",
};

const headerSizeClasses: Record<CardSize, string> = {
  sm: "px-4 py-3",
  md: "px-6 py-4",
  lg: "px-8 py-5",
  xl: "px-10 py-6",
};

const footerSizeClasses: Record<CardSize, string> = {
  sm: "px-4 py-3",
  md: "px-6 py-4", 
  lg: "px-8 py-5",
  xl: "px-10 py-6",
};

export const EnhancedCard = React.forwardRef<HTMLDivElement, CardProps>(
  (
    {
      className,
      variant = "default",
      size = "md",
      interactive = false,
      loading = false,
      header,
      footer,
      children,
      onClick,
      ...props
    },
    ref
  ) => {
    const isClickable = interactive || !!onClick;

    return (
      <motion.div
        ref={ref}
        className={cn(
          baseClasses,
          variantClasses[variant],
          isClickable && "cursor-pointer",
          loading && "pointer-events-none",
          className
        )}
        variants={cardVariants}
        initial="idle"
        whileHover={isClickable && !loading ? "hover" : undefined}
        whileTap={isClickable && !loading ? "tap" : undefined}
        onClick={onClick}
        role={isClickable ? "button" : undefined}
        tabIndex={isClickable ? 0 : undefined}
        aria-busy={loading}
        {...props}
      >
        {/* Loading Overlay */}
        {loading && (
          <motion.div
            className="absolute inset-0 bg-white/50 backdrop-blur-sm z-10 flex items-center justify-center"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.2 }}
          >
            <motion.div
              animate={{ rotate: 360 }}
              transition={{
                duration: 1,
                repeat: Infinity,
                ease: "linear",
              }}
              className="h-8 w-8 border-4 border-primary-200 border-t-primary-600 rounded-full"
            />
          </motion.div>
        )}

        {/* Header */}
        {header && (
          <motion.div
            className={cn(
              "border-b border-secondary-200",
              headerSizeClasses[size]
            )}
            variants={childVariants}
          >
            {header}
          </motion.div>
        )}

        {/* Content */}
        <motion.div
          className={cn(
            !header && !footer && sizeClasses[size],
            header && !footer && "px-6 pb-6 pt-4",
            !header && footer && "px-6 pt-6 pb-4",
            header && footer && "px-6 py-4"
          )}
          variants={contentVariants}
          initial="hidden"
          animate="visible"
        >
          <motion.div variants={childVariants}>
            {children}
          </motion.div>
        </motion.div>

        {/* Footer */}
        {footer && (
          <motion.div
            className={cn(
              "border-t border-secondary-200 bg-secondary-50/50",
              footerSizeClasses[size]
            )}
            variants={childVariants}
          >
            {footer}
          </motion.div>
        )}

        {/* Gradient Overlay for Interactive Cards */}
        {isClickable && variant === "default" && (
          <motion.div
            className="absolute inset-0 bg-gradient-to-r from-transparent via-primary-500/5 to-transparent opacity-0"
            whileHover={{ opacity: 1 }}
            transition={{ duration: 0.3 }}
          />
        )}

        {/* Shine Effect for Gradient Cards */}
        {variant === "gradient" && isClickable && (
          <motion.div
            className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent"
            initial={{ x: "-100%" }}
            whileHover={{
              x: "100%",
              transition: { duration: 0.8, ease: "easeInOut" },
            }}
          />
        )}
      </motion.div>
    );
  }
);

EnhancedCard.displayName = "EnhancedCard";

// Compound Components
export const CardHeader = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex flex-col space-y-1.5", className)}
    {...props}
  />
));
CardHeader.displayName = "CardHeader";

export const CardTitle = React.forwardRef<
  HTMLHeadingElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, children, ...props }, ref) => (
  <h3
    ref={ref}
    className={cn(
      "text-xl font-semibold leading-none tracking-tight text-secondary-900",
      className
    )}
    {...props}
  >
    {children}
  </h3>
));
CardTitle.displayName = "CardTitle";

export const CardDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <p
    ref={ref}
    className={cn("text-sm text-secondary-600", className)}
    {...props}
  />
));
CardDescription.displayName = "CardDescription";

export const CardContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div ref={ref} className={cn("", className)} {...props} />
));
CardContent.displayName = "CardContent";

export const CardFooter = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex items-center justify-between", className)}
    {...props}
  />
));
CardFooter.displayName = "CardFooter";

// Export types
export type { CardProps, CardVariant, CardSize };