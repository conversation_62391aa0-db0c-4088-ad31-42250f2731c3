/**
 * Offline Manager
 * Handles offline functionality, data synchronization, and local storage
 */

interface OfflineData {
  id: string;
  type: 'calculation' | 'project' | 'user_preference';
  data: any;
  timestamp: number;
  synced: boolean;
}

interface OfflineConfig {
  maxStorageSize?: number; // in MB
  syncRetryAttempts?: number;
  syncRetryDelay?: number;
  enableBackground?: boolean;
}

class OfflineManager {
  private config: Required<OfflineConfig>;
  private storageKey = 'nirmaan_offline_data';
  private isOnline = navigator.onLine;
  private syncQueue: OfflineData[] = [];
  private callbacks: Map<string, Function[]> = new Map();
  private syncInProgress = false;

  constructor(config: Partial<OfflineConfig> = {}) {
    this.config = {
      maxStorageSize: 50, // 50MB
      syncRetryAttempts: 3,
      syncRetryDelay: 5000,
      enableBackground: true,
      ...config
    };

    this.init();
  }

  private init(): void {
    this.loadOfflineData();
    this.setupNetworkMonitoring();
    this.setupBackgroundSync();
    this.startPeriodicCleanup();
  }

  private setupNetworkMonitoring(): void {
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.emit('online', { isOnline: true });
      this.syncPendingData();
    });

    window.addEventListener('offline', () => {
      this.isOnline = false;
      this.emit('offline', { isOnline: false });
    });
  }

  private setupBackgroundSync(): void {
    if (!this.config.enableBackground || !('serviceWorker' in navigator)) return;

    navigator.serviceWorker.ready.then(registration => {
      if ('sync' in registration) {
        // Register for background sync
        registration.sync.register('background-sync');
      }
    });

    // Listen for sync events from service worker
    navigator.serviceWorker.addEventListener('message', event => {
      if (event.data.type === 'BACKGROUND_SYNC') {
        this.syncPendingData();
      }
    });
  }

  private startPeriodicCleanup(): void {
    // Clean up old data every hour
    setInterval(() => {
      this.cleanupOldData();
    }, 60 * 60 * 1000);
  }

  private loadOfflineData(): void {
    try {
      const storedData = localStorage.getItem(this.storageKey);
      if (storedData) {
        this.syncQueue = JSON.parse(storedData);
      }
    } catch (error) {
      console.warn('Failed to load offline data:', error);
      this.syncQueue = [];
    }
  }

  private saveOfflineData(): void {
    try {
      localStorage.setItem(this.storageKey, JSON.stringify(this.syncQueue));
    } catch (error) {
      console.warn('Failed to save offline data:', error);
      this.handleStorageError(error);
    }
  }

  private handleStorageError(error: any): void {
    if (error.name === 'QuotaExceededError') {
      this.cleanupOldData(true);
      this.emit('storageQuotaExceeded', { error });
    }
  }

  private cleanupOldData(force: boolean = false): void {
    const now = Date.now();
    const maxAge = force ? 24 * 60 * 60 * 1000 : 7 * 24 * 60 * 60 * 1000; // 1 day if forced, 7 days otherwise

    const initialLength = this.syncQueue.length;
    
    this.syncQueue = this.syncQueue.filter(item => {
      return (now - item.timestamp) < maxAge || !item.synced;
    });

    if (this.syncQueue.length !== initialLength) {
      this.saveOfflineData();
      this.emit('dataCleanup', { 
        removed: initialLength - this.syncQueue.length,
        remaining: this.syncQueue.length
      });
    }
  }

  // Public API
  async saveData(type: OfflineData['type'], data: any, id?: string): Promise<string> {
    const offlineData: OfflineData = {
      id: id || this.generateId(),
      type,
      data,
      timestamp: Date.now(),
      synced: false
    };

    // Check if data already exists and update it
    const existingIndex = this.syncQueue.findIndex(item => item.id === offlineData.id);
    
    if (existingIndex >= 0) {
      this.syncQueue[existingIndex] = offlineData;
    } else {
      this.syncQueue.push(offlineData);
    }

    this.saveOfflineData();
    this.emit('dataSaved', { id: offlineData.id, type, offline: !this.isOnline });

    // Try to sync immediately if online
    if (this.isOnline) {
      this.syncSingleItem(offlineData);
    }

    return offlineData.id;
  }

  getData(id: string): OfflineData | null {
    return this.syncQueue.find(item => item.id === id) || null;
  }

  getAllData(type?: OfflineData['type']): OfflineData[] {
    if (type) {
      return this.syncQueue.filter(item => item.type === type);
    }
    return [...this.syncQueue];
  }

  deleteData(id: string): boolean {
    const index = this.syncQueue.findIndex(item => item.id === id);
    if (index >= 0) {
      this.syncQueue.splice(index, 1);
      this.saveOfflineData();
      this.emit('dataDeleted', { id });
      return true;
    }
    return false;
  }

  async syncPendingData(): Promise<void> {
    if (this.syncInProgress || !this.isOnline) return;

    this.syncInProgress = true;
    this.emit('syncStarted', { pendingCount: this.getPendingCount() });

    const pendingItems = this.syncQueue.filter(item => !item.synced);
    let successCount = 0;
    let failureCount = 0;

    for (const item of pendingItems) {
      const success = await this.syncSingleItem(item);
      if (success) {
        successCount++;
      } else {
        failureCount++;
      }
    }

    this.syncInProgress = false;
    this.emit('syncCompleted', { 
      successCount, 
      failureCount, 
      totalPending: pendingItems.length 
    });
  }

  private async syncSingleItem(item: OfflineData): Promise<boolean> {
    let attempts = 0;
    
    while (attempts < this.config.syncRetryAttempts) {
      try {
        const success = await this.uploadToServer(item);
        
        if (success) {
          item.synced = true;
          this.saveOfflineData();
          this.emit('itemSynced', { id: item.id, type: item.type });
          return true;
        }
      } catch (error) {
        console.warn(`Sync attempt ${attempts + 1} failed for item ${item.id}:`, error);
      }
      
      attempts++;
      
      if (attempts < this.config.syncRetryAttempts) {
        await this.delay(this.config.syncRetryDelay * attempts);
      }
    }

    this.emit('itemSyncFailed', { id: item.id, type: item.type, attempts });
    return false;
  }

  private async uploadToServer(item: OfflineData): Promise<boolean> {
    const endpoint = this.getEndpointForType(item.type);
    
    try {
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: item.id,
          data: item.data,
          timestamp: item.timestamp
        })
      });

      return response.ok;
    } catch (error) {
      throw error;
    }
  }

  private getEndpointForType(type: OfflineData['type']): string {
    const endpoints = {
      calculation: '/api/calculations/sync',
      project: '/api/projects/sync',
      user_preference: '/api/preferences/sync'
    };

    return endpoints[type] || '/api/sync';
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private generateId(): string {
    return `offline_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Calculation-specific methods
  async saveCalculation(calculationData: any): Promise<string> {
    const id = this.generateId();
    await this.saveData('calculation', calculationData, id);
    return id;
  }

  getCalculations(): OfflineData[] {
    return this.getAllData('calculation');
  }

  async saveProject(projectData: any): Promise<string> {
    const id = projectData.id || this.generateId();
    await this.saveData('project', projectData, id);
    return id;
  }

  getProjects(): OfflineData[] {
    return this.getAllData('project');
  }

  async saveUserPreference(key: string, value: any): Promise<string> {
    const preferenceData = { key, value };
    await this.saveData('user_preference', preferenceData, key);
    return key;
  }

  getUserPreferences(): Record<string, any> {
    const preferences: Record<string, any> = {};
    const preferenceItems = this.getAllData('user_preference');
    
    preferenceItems.forEach(item => {
      preferences[item.data.key] = item.data.value;
    });
    
    return preferences;
  }

  // Storage management
  getStorageUsage(): Promise<{ used: number; quota: number; percentage: number }> {
    return new Promise((resolve) => {
      if ('storage' in navigator && 'estimate' in navigator.storage) {
        navigator.storage.estimate().then(estimate => {
          const used = estimate.usage || 0;
          const quota = estimate.quota || 0;
          const percentage = quota > 0 ? (used / quota) * 100 : 0;
          
          resolve({
            used: Math.round(used / 1024 / 1024), // Convert to MB
            quota: Math.round(quota / 1024 / 1024), // Convert to MB
            percentage: Math.round(percentage)
          });
        });
      } else {
        // Fallback estimation
        const dataSize = JSON.stringify(this.syncQueue).length;
        resolve({
          used: Math.round(dataSize / 1024 / 1024),
          quota: this.config.maxStorageSize,
          percentage: (dataSize / (this.config.maxStorageSize * 1024 * 1024)) * 100
        });
      }
    });
  }

  getPendingCount(): number {
    return this.syncQueue.filter(item => !item.synced).length;
  }

  getSyncedCount(): number {
    return this.syncQueue.filter(item => item.synced).length;
  }

  isOnlineMode(): boolean {
    return this.isOnline;
  }

  async clearAllData(): Promise<void> {
    this.syncQueue = [];
    this.saveOfflineData();
    this.emit('allDataCleared', {});
  }

  async exportData(): Promise<string> {
    return JSON.stringify({
      timestamp: Date.now(),
      data: this.syncQueue,
      version: '1.0'
    }, null, 2);
  }

  async importData(jsonData: string): Promise<boolean> {
    try {
      const imported = JSON.parse(jsonData);
      
      if (imported.data && Array.isArray(imported.data)) {
        this.syncQueue = imported.data;
        this.saveOfflineData();
        this.emit('dataImported', { count: imported.data.length });
        return true;
      }
    } catch (error) {
      console.warn('Failed to import data:', error);
    }
    
    return false;
  }

  // Event system
  on(eventName: string, callback: Function): void {
    if (!this.callbacks.has(eventName)) {
      this.callbacks.set(eventName, []);
    }
    this.callbacks.get(eventName)!.push(callback);
  }

  off(eventName: string, callback?: Function): void {
    if (!callback) {
      this.callbacks.delete(eventName);
      return;
    }

    const callbacks = this.callbacks.get(eventName);
    if (callbacks) {
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  private emit(eventName: string, data: any): void {
    const callbacks = this.callbacks.get(eventName);
    if (callbacks) {
      callbacks.forEach(callback => callback(data));
    }
  }
}

// Create singleton instance
const offlineManager = new OfflineManager();

export default offlineManager;
export { OfflineManager };
export type { OfflineData, OfflineConfig };