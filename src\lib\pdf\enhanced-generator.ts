/**
 * Enhanced PDF Export with Better UX
 * Provides improved user experience with loading states, progress tracking, and error handling
 */

import { generatePDFReport } from './generator';
import type { CalculationInput, CalculationResult } from '@/core/calculator/types';

export interface PDFExportOptions {
  filename?: string;
  includeTimeline?: boolean;
  includeMaterials?: boolean;
  includeDetailedBreakdown?: boolean;
  onProgress?: (progress: number) => void;
  onSuccess?: (filename: string) => void;
  onError?: (error: Error) => void;
}

export interface PDFExportResult {
  success: boolean;
  filename?: string;
  error?: Error;
  duration?: number;
}

/**
 * Enhanced PDF export with progress tracking and better error handling
 */
export async function exportCalculationToPDFEnhanced(
  input: CalculationInput,
  result: CalculationResult,
  options: PDFExportOptions = {}
): Promise<PDFExportResult> {
  const startTime = Date.now();
  
  const {
    filename: customFilename,
    includeTimeline = true,
    includeMaterials = true,
    includeDetailedBreakdown = true,
    onProgress,
    onSuccess,
    onError
  } = options;

  try {
    // Step 1: Initialize (10%)
    onProgress?.(10);
    
    // Validate inputs
    if (!input || !result) {
      throw new Error('Invalid input or result data');
    }

    if (!input.builtUpArea || input.builtUpArea <= 0) {
      throw new Error('Built-up area must be greater than 0');
    }

    // Step 2: Generate filename (20%)
    onProgress?.(20);
    
    const filename = customFilename || generateEnhancedFilename(input);

    // Step 3: Prepare data (40%)
    onProgress?.(40);
    
    // Add delay for better UX perception
    await new Promise(resolve => setTimeout(resolve, 200));

    // Step 4: Generate PDF (70%)
    onProgress?.(70);
    
    await generatePDFReport(input, result, {
      filename,
      includeTimeline,
      includeMaterials,
      includeDetailedBreakdown
    });

    // Step 5: Complete (100%)
    onProgress?.(100);
    
    const duration = Date.now() - startTime;
    
    // Success callback
    onSuccess?.(filename);
    
    return {
      success: true,
      filename,
      duration
    };

  } catch (error) {
    const err = error instanceof Error ? error : new Error('PDF generation failed');
    
    // Error callback
    onError?.(err);
    
    return {
      success: false,
      error: err,
      duration: Date.now() - startTime
    };
  }
}

/**
 * Generate enhanced filename with project details
 */
function generateEnhancedFilename(input: CalculationInput): string {
  const date = new Date().toISOString().split('T')[0];
  const time = new Date().toTimeString().split(' ')[0].replace(/:/g, '');
  
  const location = input.location.charAt(0).toUpperCase() + input.location.slice(1);
  const tier = input.qualityTier.charAt(0).toUpperCase() + input.qualityTier.slice(1);
  
  return `ClarityEngine_${location}_${tier}_${input.builtUpArea}sqft_${date}_${time}.pdf`;
}

/**
 * Get PDF export analytics data
 */
export function getPDFExportAnalytics(input: CalculationInput, result: CalculationResult) {
  return {
    projectSize: input.builtUpArea,
    location: input.location,
    qualityTier: input.qualityTier,
    totalCost: result.totalCost,
    costPerSqft: result.totalCost / input.builtUpArea,
    hasBasement: input.hasBasement,
    floors: input.floors,
    timestamp: new Date().toISOString()
  };
}

/**
 * Validate PDF export requirements
 */
export function validatePDFExportData(input: CalculationInput, result: CalculationResult): string[] {
  const errors: string[] = [];

  // Input validation
  if (!input.builtUpArea || input.builtUpArea <= 0) {
    errors.push('Built-up area is required and must be greater than 0');
  }

  if (!input.location || input.location.trim() === '') {
    errors.push('Location is required');
  }

  if (!input.qualityTier || input.qualityTier.trim() === '') {
    errors.push('Quality tier is required');
  }

  if (!input.floors || input.floors < 0) {
    errors.push('Number of floors must be specified');
  }

  // Result validation
  if (!result.totalCost || result.totalCost <= 0) {
    errors.push('Total cost calculation is invalid');
  }

  if (!result.breakdown || typeof result.breakdown !== 'object') {
    errors.push('Cost breakdown data is missing');
  }

  return errors;
}

/**
 * Estimate PDF generation time based on options
 */
export function estimatePDFGenerationTime(options: PDFExportOptions): number {
  let baseTime = 1000; // 1 second base time
  
  if (options.includeDetailedBreakdown) {
    baseTime += 500; // Add 0.5 seconds for detailed breakdown
  }
  
  if (options.includeMaterials) {
    baseTime += 300; // Add 0.3 seconds for materials
  }
  
  if (options.includeTimeline) {
    baseTime += 200; // Add 0.2 seconds for timeline
  }
  
  return baseTime;
}

/**
 * PDF export with retry logic
 */
export async function exportCalculationToPDFWithRetry(
  input: CalculationInput,
  result: CalculationResult,
  options: PDFExportOptions = {},
  maxRetries = 3
): Promise<PDFExportResult> {
  let lastError: Error | null = null;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const result = await exportCalculationToPDFEnhanced(input, result, {
        ...options,
        onProgress: (progress) => {
          // Adjust progress based on retry attempt
          const adjustedProgress = progress / maxRetries + ((attempt - 1) / maxRetries) * 100;
          options.onProgress?.(adjustedProgress);
        }
      });
      
      if (result.success) {
        return result;
      }
      
      lastError = result.error || new Error('Unknown error');
    } catch (error) {
      lastError = error instanceof Error ? error : new Error('PDF generation failed');
    }
    
    // Wait before retry
    if (attempt < maxRetries) {
      await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
    }
  }
  
  return {
    success: false,
    error: lastError || new Error('PDF generation failed after retries')
  };
}