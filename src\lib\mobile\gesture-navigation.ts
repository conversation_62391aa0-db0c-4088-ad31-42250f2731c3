/**
 * Gesture-based Navigation System
 * Implements swipe navigation and gesture controls for mobile UX
 */

import TouchHandler, { SwipeGesture } from './touch-handler';

interface GestureNavigationOptions {
  enableSwipeBack?: boolean;
  enableSwipeForward?: boolean;
  enablePullToRefresh?: boolean;
  swipeThreshold?: number;
  animationDuration?: number;
}

interface NavigationState {
  canGoBack: boolean;
  canGoForward: boolean;
  isRefreshing: boolean;
  currentRoute: string;
}

class GestureNavigation {
  private container: HTMLElement;
  private options: Required<GestureNavigationOptions>;
  private touchHandler: TouchHandler;
  private navigationState: NavigationState;
  private callbacks: Map<string, Function[]> = new Map();
  private pullToRefreshElement: HTMLElement | null = null;
  private refreshThreshold = 80;

  constructor(container: HTMLElement, options: Partial<GestureNavigationOptions> = {}) {
    this.container = container;
    this.options = {
      enableSwipeBack: true,
      enableSwipeForward: true,
      enablePullToRefresh: true,
      swipeThreshold: 100,
      animationDuration: 300,
      ...options
    };

    this.navigationState = {
      canGoBack: false,
      canGoForward: false,
      isRefreshing: false,
      currentRoute: window.location.pathname
    };

    this.init();
  }

  private init(): void {
    this.setupTouchHandler();
    this.setupPullToRefresh();
    this.updateNavigationState();
    this.addVisualFeedback();
  }

  private setupTouchHandler(): void {
    this.touchHandler = new TouchHandler(this.container, {
      swipeThreshold: this.options.swipeThreshold,
      enableHaptic: true
    });

    this.touchHandler.on('swiperight', this.handleSwipeRight.bind(this));
    this.touchHandler.on('swipeleft', this.handleSwipeLeft.bind(this));
    this.touchHandler.on('swipedown', this.handleSwipeDown.bind(this));
    this.touchHandler.on('touchmove', this.handleTouchMove.bind(this));
    this.touchHandler.on('touchend', this.handleTouchEnd.bind(this));
  }

  private setupPullToRefresh(): void {
    if (!this.options.enablePullToRefresh) return;

    this.pullToRefreshElement = document.createElement('div');
    this.pullToRefreshElement.className = 'pull-to-refresh-indicator';
    this.pullToRefreshElement.innerHTML = `
      <div class="refresh-spinner">
        <svg class="animate-spin" width="24" height="24" viewBox="0 0 24 24">
          <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" fill="none" opacity="0.3"/>
          <path d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" fill="currentColor"/>
        </svg>
      </div>
      <span class="refresh-text">Pull to refresh</span>
    `;

    this.pullToRefreshElement.style.cssText = `
      position: absolute;
      top: -60px;
      left: 50%;
      transform: translateX(-50%);
      padding: 10px 20px;
      background: rgba(255, 255, 255, 0.9);
      border-radius: 20px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      display: flex;
      align-items: center;
      gap: 8px;
      opacity: 0;
      transition: all 0.3s ease;
      z-index: 1000;
      backdrop-filter: blur(10px);
    `;

    this.container.style.position = 'relative';
    this.container.appendChild(this.pullToRefreshElement);
  }

  private handleSwipeRight(data: { gesture: SwipeGesture }): void {
    if (!this.options.enableSwipeBack || !this.navigationState.canGoBack) return;

    const { gesture } = data;
    if (gesture.velocity > 0.5 && gesture.distance > this.options.swipeThreshold) {
      this.triggerNavigation('back');
    }
  }

  private handleSwipeLeft(data: { gesture: SwipeGesture }): void {
    if (!this.options.enableSwipeForward || !this.navigationState.canGoForward) return;

    const { gesture } = data;
    if (gesture.velocity > 0.5 && gesture.distance > this.options.swipeThreshold) {
      this.triggerNavigation('forward');
    }
  }

  private handleSwipeDown(data: { gesture: SwipeGesture }): void {
    if (!this.options.enablePullToRefresh || window.scrollY > 0) return;

    const { gesture } = data;
    if (gesture.distance > this.refreshThreshold) {
      this.triggerRefresh();
    }
  }

  private handleTouchMove(data: any): void {
    if (!this.options.enablePullToRefresh || !this.pullToRefreshElement) return;

    const { touchData } = data;
    const scrollTop = window.scrollY;
    
    if (scrollTop === 0 && touchData.deltaY > 0) {
      const pullDistance = Math.min(touchData.deltaY, this.refreshThreshold * 1.5);
      const opacity = Math.min(pullDistance / this.refreshThreshold, 1);
      const translateY = Math.min(pullDistance * 0.5, 40);

      this.pullToRefreshElement.style.opacity = opacity.toString();
      this.pullToRefreshElement.style.transform = `translateX(-50%) translateY(${translateY}px)`;

      if (pullDistance >= this.refreshThreshold) {
        this.pullToRefreshElement.classList.add('ready-to-refresh');
        this.updateRefreshText('Release to refresh');
      } else {
        this.pullToRefreshElement.classList.remove('ready-to-refresh');
        this.updateRefreshText('Pull to refresh');
      }
    }
  }

  private handleTouchEnd(): void {
    if (!this.pullToRefreshElement) return;

    this.pullToRefreshElement.style.opacity = '0';
    this.pullToRefreshElement.style.transform = 'translateX(-50%) translateY(-60px)';
    this.pullToRefreshElement.classList.remove('ready-to-refresh');
    this.updateRefreshText('Pull to refresh');
  }

  private updateRefreshText(text: string): void {
    if (!this.pullToRefreshElement) return;
    const textElement = this.pullToRefreshElement.querySelector('.refresh-text');
    if (textElement) {
      textElement.textContent = text;
    }
  }

  private triggerNavigation(direction: 'back' | 'forward'): void {
    this.addNavigationAnimation(direction);
    
    setTimeout(() => {
      if (direction === 'back') {
        window.history.back();
      } else {
        window.history.forward();
      }
      this.emit('navigate', { direction });
    }, 100);
  }

  private triggerRefresh(): void {
    if (this.navigationState.isRefreshing) return;

    this.navigationState.isRefreshing = true;
    this.showRefreshSpinner();
    
    this.emit('refresh', {
      callback: () => {
        this.hideRefreshSpinner();
        this.navigationState.isRefreshing = false;
      }
    });
  }

  private showRefreshSpinner(): void {
    if (!this.pullToRefreshElement) return;

    this.pullToRefreshElement.style.opacity = '1';
    this.pullToRefreshElement.style.transform = 'translateX(-50%) translateY(20px)';
    this.pullToRefreshElement.classList.add('refreshing');
    this.updateRefreshText('Refreshing...');
  }

  private hideRefreshSpinner(): void {
    if (!this.pullToRefreshElement) return;

    setTimeout(() => {
      this.pullToRefreshElement!.style.opacity = '0';
      this.pullToRefreshElement!.style.transform = 'translateX(-50%) translateY(-60px)';
      this.pullToRefreshElement!.classList.remove('refreshing');
      this.updateRefreshText('Pull to refresh');
    }, 1000);
  }

  private addNavigationAnimation(direction: 'back' | 'forward'): void {
    const overlay = document.createElement('div');
    overlay.className = `navigation-overlay navigation-${direction}`;
    overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, 
        ${direction === 'back' ? 'rgba(0, 0, 0, 0.1) 0%, transparent 100%' : 'transparent 0%, rgba(0, 0, 0, 0.1) 100%'}
      );
      z-index: 9999;
      pointer-events: none;
      opacity: 0;
      transition: opacity ${this.options.animationDuration}ms ease;
    `;

    document.body.appendChild(overlay);
    
    requestAnimationFrame(() => {
      overlay.style.opacity = '1';
    });

    setTimeout(() => {
      overlay.style.opacity = '0';
      setTimeout(() => {
        document.body.removeChild(overlay);
      }, this.options.animationDuration);
    }, 200);
  }

  private addVisualFeedback(): void {
    const style = document.createElement('style');
    style.textContent = `
      .pull-to-refresh-indicator {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        font-size: 14px;
        color: #333;
      }
      
      .pull-to-refresh-indicator.ready-to-refresh .refresh-text {
        color: #007AFF;
        font-weight: 500;
      }
      
      .pull-to-refresh-indicator.refreshing .refresh-spinner {
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
      }
      
      .navigation-overlay {
        animation: fadeInOut ${this.options.animationDuration}ms ease;
      }
      
      @keyframes fadeInOut {
        0% { opacity: 0; }
        50% { opacity: 1; }
        100% { opacity: 0; }
      }
      
      /* Touch feedback for interactive elements */
      .touch-feedback:active {
        transform: scale(0.95);
        opacity: 0.8;
        transition: all 0.1s ease;
      }
      
      /* Improved touch targets */
      .touch-target {
        min-width: 44px;
        min-height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    `;
    
    document.head.appendChild(style);
  }

  private updateNavigationState(): void {
    this.navigationState.canGoBack = window.history.length > 1;
    this.navigationState.canGoForward = false; // Browser doesn't expose this
    this.navigationState.currentRoute = window.location.pathname;
  }

  private emit(eventName: string, data: any): void {
    const callbacks = this.callbacks.get(eventName);
    if (callbacks) {
      callbacks.forEach(callback => callback(data));
    }
  }

  // Public API
  on(eventName: string, callback: Function): void {
    if (!this.callbacks.has(eventName)) {
      this.callbacks.set(eventName, []);
    }
    this.callbacks.get(eventName)!.push(callback);
  }

  off(eventName: string, callback?: Function): void {
    if (!callback) {
      this.callbacks.delete(eventName);
      return;
    }

    const callbacks = this.callbacks.get(eventName);
    if (callbacks) {
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  updateOptions(newOptions: Partial<GestureNavigationOptions>): void {
    this.options = { ...this.options, ...newOptions };
  }

  getNavigationState(): NavigationState {
    return { ...this.navigationState };
  }

  destroy(): void {
    this.touchHandler.destroy();
    if (this.pullToRefreshElement) {
      this.container.removeChild(this.pullToRefreshElement);
    }
    this.callbacks.clear();
  }
}

export default GestureNavigation;
export type { GestureNavigationOptions, NavigationState };