/**
 * Mobile Touch Interaction Handler
 * Optimizes touch interactions for construction calculator
 */

interface TouchEventData {
  startX: number;
  startY: number;
  currentX: number;
  currentY: number;
  deltaX: number;
  deltaY: number;
  timestamp: number;
  velocity: number;
}

interface SwipeGesture {
  direction: 'left' | 'right' | 'up' | 'down';
  distance: number;
  velocity: number;
  duration: number;
}

interface TouchHandlerOptions {
  swipeThreshold?: number;
  velocityThreshold?: number;
  preventScrolling?: boolean;
  enableHaptic?: boolean;
  minTouchTarget?: number;
}

class TouchHandler {
  private element: HTMLElement;
  private options: Required<TouchHandlerOptions>;
  private touchData: TouchEventData | null = null;
  private isTracking = false;
  private callbacks: Map<string, Function[]> = new Map();

  constructor(element: HTMLElement, options: Partial<TouchHandlerOptions> = {}) {
    this.element = element;
    this.options = {
      swipeThreshold: 50,
      velocityThreshold: 0.3,
      preventScrolling: false,
      enableHaptic: true,
      minTouchTarget: 44,
      ...options
    };

    this.init();
  }

  private init(): void {
    // Ensure minimum touch target size
    this.ensureTouchTargetSize();
    
    // Add touch event listeners
    this.element.addEventListener('touchstart', this.handleTouchStart.bind(this), { passive: false });
    this.element.addEventListener('touchmove', this.handleTouchMove.bind(this), { passive: false });
    this.element.addEventListener('touchend', this.handleTouchEnd.bind(this), { passive: false });
    this.element.addEventListener('touchcancel', this.handleTouchCancel.bind(this));

    // Add pointer events for better cross-device support
    this.element.addEventListener('pointerdown', this.handlePointerDown.bind(this));
    this.element.addEventListener('pointermove', this.handlePointerMove.bind(this));
    this.element.addEventListener('pointerup', this.handlePointerUp.bind(this));
  }

  private ensureTouchTargetSize(): void {
    const rect = this.element.getBoundingClientRect();
    if (rect.width < this.options.minTouchTarget || rect.height < this.options.minTouchTarget) {
      this.element.style.minWidth = `${this.options.minTouchTarget}px`;
      this.element.style.minHeight = `${this.options.minTouchTarget}px`;
      this.element.style.padding = '8px';
    }
  }

  private handleTouchStart(event: TouchEvent): void {
    if (this.options.preventScrolling) {
      event.preventDefault();
    }

    const touch = event.touches[0];
    this.touchData = {
      startX: touch.clientX,
      startY: touch.clientY,
      currentX: touch.clientX,
      currentY: touch.clientY,
      deltaX: 0,
      deltaY: 0,
      timestamp: Date.now(),
      velocity: 0
    };
    
    this.isTracking = true;
    this.triggerHaptic('light');
    this.emit('touchstart', { originalEvent: event, touchData: this.touchData });
  }

  private handleTouchMove(event: TouchEvent): void {
    if (!this.isTracking || !this.touchData) return;

    if (this.options.preventScrolling) {
      event.preventDefault();
    }

    const touch = event.touches[0];
    const currentTime = Date.now();
    const deltaTime = currentTime - this.touchData.timestamp;

    this.touchData.currentX = touch.clientX;
    this.touchData.currentY = touch.clientY;
    this.touchData.deltaX = touch.clientX - this.touchData.startX;
    this.touchData.deltaY = touch.clientY - this.touchData.startY;
    
    if (deltaTime > 0) {
      const distance = Math.sqrt(
        Math.pow(this.touchData.deltaX, 2) + Math.pow(this.touchData.deltaY, 2)
      );
      this.touchData.velocity = distance / deltaTime;
    }

    this.emit('touchmove', { originalEvent: event, touchData: this.touchData });
  }

  private handleTouchEnd(event: TouchEvent): void {
    if (!this.isTracking || !this.touchData) return;

    const gesture = this.detectSwipeGesture();
    if (gesture) {
      this.triggerHaptic('medium');
      this.emit('swipe', { gesture, touchData: this.touchData });
      this.emit(`swipe${gesture.direction}`, { gesture, touchData: this.touchData });
    }

    this.emit('touchend', { originalEvent: event, touchData: this.touchData });
    this.reset();
  }

  private handleTouchCancel(): void {
    this.emit('touchcancel', { touchData: this.touchData });
    this.reset();
  }

  private handlePointerDown(event: PointerEvent): void {
    this.element.setPointerCapture(event.pointerId);
  }

  private handlePointerMove(event: PointerEvent): void {
    // Handle pointer move for better precision
  }

  private handlePointerUp(event: PointerEvent): void {
    this.element.releasePointerCapture(event.pointerId);
  }

  private detectSwipeGesture(): SwipeGesture | null {
    if (!this.touchData) return null;

    const { deltaX, deltaY, velocity, timestamp } = this.touchData;
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
    const duration = Date.now() - timestamp;

    if (distance < this.options.swipeThreshold || velocity < this.options.velocityThreshold) {
      return null;
    }

    let direction: SwipeGesture['direction'];
    if (Math.abs(deltaX) > Math.abs(deltaY)) {
      direction = deltaX > 0 ? 'right' : 'left';
    } else {
      direction = deltaY > 0 ? 'down' : 'up';
    }

    return {
      direction,
      distance,
      velocity,
      duration
    };
  }

  private triggerHaptic(intensity: 'light' | 'medium' | 'heavy'): void {
    if (!this.options.enableHaptic || !('vibrate' in navigator)) return;

    const patterns = {
      light: [10],
      medium: [30],
      heavy: [50, 20, 50]
    };

    navigator.vibrate(patterns[intensity]);
  }

  private emit(eventName: string, data: any): void {
    const callbacks = this.callbacks.get(eventName);
    if (callbacks) {
      callbacks.forEach(callback => callback(data));
    }
  }

  private reset(): void {
    this.touchData = null;
    this.isTracking = false;
  }

  // Public API
  on(eventName: string, callback: Function): void {
    if (!this.callbacks.has(eventName)) {
      this.callbacks.set(eventName, []);
    }
    this.callbacks.get(eventName)!.push(callback);
  }

  off(eventName: string, callback?: Function): void {
    if (!callback) {
      this.callbacks.delete(eventName);
      return;
    }

    const callbacks = this.callbacks.get(eventName);
    if (callbacks) {
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  destroy(): void {
    this.element.removeEventListener('touchstart', this.handleTouchStart.bind(this));
    this.element.removeEventListener('touchmove', this.handleTouchMove.bind(this));
    this.element.removeEventListener('touchend', this.handleTouchEnd.bind(this));
    this.element.removeEventListener('touchcancel', this.handleTouchCancel.bind(this));
    this.element.removeEventListener('pointerdown', this.handlePointerDown.bind(this));
    this.element.removeEventListener('pointermove', this.handlePointerMove.bind(this));
    this.element.removeEventListener('pointerup', this.handlePointerUp.bind(this));
    this.callbacks.clear();
  }
}

// Touch utilities
export const touchUtils = {
  isTouchDevice: (): boolean => {
    return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
  },

  getOptimalTouchTargetSize: (): number => {
    const dpr = window.devicePixelRatio || 1;
    return Math.max(44, 44 / dpr); // iOS HIG minimum
  },

  preventIOSBounce: (element: HTMLElement): void => {
    element.style.overscrollBehavior = 'none';
    element.style.webkitOverflowScrolling = 'touch';
  },

  enableSafeAreaSupport: (): void => {
    document.documentElement.style.paddingTop = 'env(safe-area-inset-top)';
    document.documentElement.style.paddingBottom = 'env(safe-area-inset-bottom)';
    document.documentElement.style.paddingLeft = 'env(safe-area-inset-left)';
    document.documentElement.style.paddingRight = 'env(safe-area-inset-right)';
  },

  optimizeScrolling: (element: HTMLElement): void => {
    element.style.scrollBehavior = 'smooth';
    element.style.webkitOverflowScrolling = 'touch';
    element.style.overscrollBehavior = 'contain';
  }
};

export default TouchHandler;
export type { TouchEventData, SwipeGesture, TouchHandlerOptions };