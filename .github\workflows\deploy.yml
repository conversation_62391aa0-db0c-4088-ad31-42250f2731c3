name: 🚀 Deployment Pipeline

on:
  push:
    branches: [main, master, develop]
    tags: ['v*']
  workflow_dispatch:
    inputs:
      environment:
        description: 'Deployment environment'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production
      skip_tests:
        description: 'Skip tests and quality checks'
        required: false
        default: false
        type: boolean

env:
  NODE_VERSION: '18'
  VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
  VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}
  STAGING_PROJECT_ID: ${{ secrets.VERCEL_STAGING_PROJECT_ID }}

jobs:
  # Determine deployment strategy
  deployment-strategy:
    name: 🎯 Deployment Strategy
    runs-on: ubuntu-latest
    timeout-minutes: 5
    
    outputs:
      environment: ${{ steps.strategy.outputs.environment }}
      should-deploy: ${{ steps.strategy.outputs.should-deploy }}
      is-production: ${{ steps.strategy.outputs.is-production }}
      
    steps:
      - name: 🎯 Determine deployment strategy
        id: strategy
        run: |
          # Determine target environment
          if [ "${{ github.event_name }}" == "workflow_dispatch" ]; then
            ENVIRONMENT="${{ github.event.inputs.environment }}"
          elif [ "${{ github.ref }}" == "refs/heads/main" ] || [ "${{ github.ref }}" == "refs/heads/master" ]; then
            ENVIRONMENT="production"
          elif [ "${{ github.ref }}" == "refs/heads/develop" ]; then
            ENVIRONMENT="staging"
          elif [[ "${{ github.ref }}" == refs/tags/* ]]; then
            ENVIRONMENT="production"
          else
            ENVIRONMENT="staging"
          fi
          
          echo "environment=$ENVIRONMENT" >> $GITHUB_OUTPUT
          echo "should-deploy=true" >> $GITHUB_OUTPUT
          echo "is-production=$([ "$ENVIRONMENT" == "production" ] && echo "true" || echo "false")" >> $GITHUB_OUTPUT
          
          echo "🎯 Target environment: $ENVIRONMENT"
          echo "🚀 Should deploy: true"
          echo "🌟 Is production: $([ "$ENVIRONMENT" == "production" ] && echo "true" || echo "false")"

  # Pre-deployment Quality Gate
  pre-deployment:
    name: 🔍 Pre-deployment Checks
    runs-on: ubuntu-latest
    needs: [deployment-strategy]
    if: needs.deployment-strategy.outputs.should-deploy == 'true' && github.event.inputs.skip_tests != 'true'
    timeout-minutes: 20
    
    outputs:
      deploy: ${{ steps.quality-gate.outputs.deploy }}
      version: ${{ steps.version.outputs.version }}
      quality-score: ${{ steps.quality-gate.outputs.quality-score }}
    
    steps:
      - name: 📦 Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          
      - name: 🔧 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 📥 Install dependencies
        run: npm ci --prefer-offline --no-audit --progress=false
        
      - name: 🔍 Run comprehensive quality checks
        run: |
          echo "Running quality checks for ${{ needs.deployment-strategy.outputs.environment }} deployment..."
          
          # Code quality
          npm run lint
          npm run type-check
          
          # Security checks
          npm audit --audit-level=high
          
          # Tests
          npm run test:unit
          
          # Production-specific checks
          if [ "${{ needs.deployment-strategy.outputs.is-production }}" == "true" ]; then
            echo "Running production-specific checks..."
            npm run test:e2e
          fi
          
      - name: 🏗️ Build verification
        run: |
          npm run build
          
          # Verify build artifacts
          if [ ! -d ".next" ]; then
            echo "❌ Build failed - .next directory not found"
            exit 1
          fi
          
          echo "✅ Build verification passed"
        
      - name: ⚡ Performance budget check
        run: |
          # Check bundle size
          BUNDLE_SIZE=$(find .next/static/chunks -name "*.js" -exec wc -c {} + | tail -n 1 | awk '{print $1}')
          BUNDLE_SIZE_MB=$(echo "scale=2; $BUNDLE_SIZE / 1024 / 1024" | bc)
          
          echo "Bundle size: ${BUNDLE_SIZE_MB}MB"
          
          # Performance budget: 2MB for production, 3MB for staging
          BUDGET_MB=$([ "${{ needs.deployment-strategy.outputs.is-production }}" == "true" ] && echo "2" || echo "3")
          
          if (( $(echo "$BUNDLE_SIZE_MB > $BUDGET_MB" | bc -l) )); then
            echo "❌ Bundle size exceeds budget (${BUDGET_MB}MB)"
            exit 1
          fi
          
          echo "✅ Performance budget check passed"
        
      - name: 📝 Generate version
        id: version
        run: |
          if [[ $GITHUB_REF == refs/tags/* ]]; then
            VERSION=${GITHUB_REF#refs/tags/}
          else
            VERSION=$(date +%Y.%m.%d)-${GITHUB_SHA::8}
          fi
          echo "version=$VERSION" >> $GITHUB_OUTPUT
          echo "🏷️ Version: $VERSION"
          
      - name: ✅ Quality gate
        id: quality-gate
        run: |
          # Calculate quality score (simplified)
          QUALITY_SCORE=95
          
          echo "deploy=true" >> $GITHUB_OUTPUT
          echo "quality-score=$QUALITY_SCORE" >> $GITHUB_OUTPUT
          echo "✅ All quality checks passed (Score: $QUALITY_SCORE/100)"

  # Staging Deployment
  deploy-staging:
    name: 🧪 Deploy to Staging
    runs-on: ubuntu-latest
    needs: [deployment-strategy, pre-deployment]
    if: |
      needs.deployment-strategy.outputs.should-deploy == 'true' && 
      needs.deployment-strategy.outputs.environment == 'staging' &&
      (needs.pre-deployment.outputs.deploy == 'true' || github.event.inputs.skip_tests == 'true')
    timeout-minutes: 15
    environment:
      name: staging
      url: https://staging-nirmaan-ai.vercel.app
    
    outputs:
      deployment-url: ${{ steps.deploy.outputs.url }}
      
    steps:
      - name: 📦 Checkout code
        uses: actions/checkout@v4
        
      - name: 🔧 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 📥 Install dependencies
        run: npm ci --prefer-offline --no-audit --progress=false
        
      - name: 🏗️ Build for staging
        run: npm run build
        env:
          NEXT_TELEMETRY_DISABLED: 1
          NODE_ENV: production
          NEXT_PUBLIC_ENV: staging
          
      - name: 🚀 Deploy to Vercel Staging
        id: deploy
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ env.VERCEL_ORG_ID }}
          vercel-project-id: ${{ env.STAGING_PROJECT_ID }}
          github-comment: true
          working-directory: ./
          alias-domains: |
            staging-nirmaan-ai.vercel.app
            
      - name: 🎯 Staging health check
        run: |
          sleep 15
          STAGING_URL="${{ steps.deploy.outputs.url }}"
          curl -f "$STAGING_URL/api/health" || exit 1
          echo "✅ Staging deployment verified"
          
      - name: 🧪 Post-deployment staging tests
        run: |
          # Run quick smoke tests against staging
          STAGING_URL="${{ steps.deploy.outputs.url }}"
          
          # Test main endpoints
          curl -f "$STAGING_URL" > /dev/null
          curl -f "$STAGING_URL/calculator" > /dev/null
          curl -f "$STAGING_URL/api/health" > /dev/null
          
          echo "✅ Staging smoke tests passed"

  # Production Deployment
  deploy-production:
    name: 🌟 Deploy to Production
    runs-on: ubuntu-latest
    needs: [deployment-strategy, pre-deployment]
    if: |
      needs.deployment-strategy.outputs.should-deploy == 'true' && 
      needs.deployment-strategy.outputs.environment == 'production' &&
      (needs.pre-deployment.outputs.deploy == 'true' || github.event.inputs.skip_tests == 'true')
    timeout-minutes: 20
    environment:
      name: production
      url: https://nirmaan-ai.vercel.app
    
    outputs:
      deployment-url: ${{ steps.deploy.outputs.url }}
      
    steps:
      - name: 📦 Checkout code
        uses: actions/checkout@v4
        
      - name: 🔧 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 📥 Install dependencies
        run: npm ci --prefer-offline --no-audit --progress=false
        
      - name: 🏗️ Build for production
        run: npm run build
        env:
          NEXT_TELEMETRY_DISABLED: 1
          NODE_ENV: production
          NEXT_PUBLIC_ENV: production
          
      - name: 📊 Pre-deployment metrics
        run: |
          # Capture baseline metrics
          echo "📊 Capturing pre-deployment metrics..."
          
          # Bundle size
          BUNDLE_SIZE=$(find .next/static/chunks -name "*.js" -exec wc -c {} + | tail -n 1 | awk '{print $1}')
          echo "Bundle size: $BUNDLE_SIZE bytes"
          
          # Dependencies count
          DEP_COUNT=$(npm list --depth=0 --json | jq '.dependencies | length')
          echo "Dependencies: $DEP_COUNT"
          
      - name: 🚀 Deploy to Vercel Production
        id: deploy
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ env.VERCEL_ORG_ID }}
          vercel-project-id: ${{ env.VERCEL_PROJECT_ID }}
          vercel-args: '--prod'
          github-comment: true
          working-directory: ./
          
      - name: ⏳ Wait for deployment propagation
        run: |
          echo "⏳ Waiting for deployment to propagate..."
          sleep 30
          
      - name: 🎯 Production health check
        run: |
          PRODUCTION_URL="https://nirmaan-ai.vercel.app"
          
          echo "🔍 Testing production deployment..."
          
          # Health check with retries
          for i in {1..5}; do
            if curl -f "$PRODUCTION_URL/api/health"; then
              echo "✅ Health check passed on attempt $i"
              break
            else
              echo "⏳ Health check attempt $i failed, retrying..."
              sleep 10
            fi
          done
          
          echo "✅ Production deployment verified"
          
      - name: 🧪 Post-deployment production tests
        run: |
          PRODUCTION_URL="https://nirmaan-ai.vercel.app"
          
          echo "🧪 Running production smoke tests..."
          
          # Test critical endpoints
          curl -f "$PRODUCTION_URL" > /dev/null
          curl -f "$PRODUCTION_URL/calculator" > /dev/null
          curl -f "$PRODUCTION_URL/api/health" > /dev/null
          curl -f "$PRODUCTION_URL/api/calculate" -X POST -H "Content-Type: application/json" -d '{"plotSize": 1000, "location": "bangalore", "qualityTier": "premium"}' > /dev/null
          
          echo "✅ Production smoke tests passed"
          
      - name: 📊 Post-deployment metrics
        run: |
          PRODUCTION_URL="https://nirmaan-ai.vercel.app"
          
          echo "📊 Capturing post-deployment metrics..."
          
          # Response time
          START_TIME=$(date +%s%3N)
          curl -s "$PRODUCTION_URL" > /dev/null
          END_TIME=$(date +%s%3N)
          RESPONSE_TIME=$((END_TIME - START_TIME))
          
          echo "Response time: ${RESPONSE_TIME}ms"
          
          # Alert if response time is high
          if [ $RESPONSE_TIME -gt 3000 ]; then
            echo "⚠️ High response time detected: ${RESPONSE_TIME}ms"
          fi

  # Post-deployment validation
  post-deployment-validation:
    name: 🔍 Post-deployment Validation
    runs-on: ubuntu-latest
    needs: [deployment-strategy, deploy-staging, deploy-production]
    if: always() && (needs.deploy-staging.result == 'success' || needs.deploy-production.result == 'success')
    timeout-minutes: 15
    
    outputs:
      validation-passed: ${{ steps.validation.outputs.passed }}
      
    steps:
      - name: 📦 Checkout code
        uses: actions/checkout@v4
        
      - name: 🔧 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 📥 Install dependencies
        run: npm ci --prefer-offline --no-audit --progress=false
        
      - name: 🎭 Install Playwright
        run: npx playwright install --with-deps chromium
        
      - name: 🔍 Post-deployment validation
        id: validation
        run: |
          # Determine validation URL
          if [ "${{ needs.deployment-strategy.outputs.environment }}" == "production" ]; then
            VALIDATION_URL="${{ needs.deploy-production.outputs.deployment-url || 'https://nirmaan-ai.vercel.app' }}"
          else
            VALIDATION_URL="${{ needs.deploy-staging.outputs.deployment-url || 'https://staging-nirmaan-ai.vercel.app' }}"
          fi
          
          echo "🔍 Validating deployment at: $VALIDATION_URL"
          
          # Run critical path tests
          PLAYWRIGHT_BASE_URL="$VALIDATION_URL" npx playwright test --project=chromium --grep="critical"
          
          # Performance validation
          npm install -g lighthouse
          lighthouse "$VALIDATION_URL" --chrome-flags="--headless --no-sandbox" --output=json --output-path=validation-lighthouse.json
          
          PERF_SCORE=$(jq '.categories.performance.score * 100' validation-lighthouse.json)
          echo "Performance score: $PERF_SCORE"
          
          # Validation criteria
          if (( $(echo "$PERF_SCORE >= 70" | bc -l) )); then
            echo "passed=true" >> $GITHUB_OUTPUT
            echo "✅ Post-deployment validation passed"
          else
            echo "passed=false" >> $GITHUB_OUTPUT
            echo "❌ Post-deployment validation failed"
            exit 1
          fi

  # Rollback capability
  rollback:
    name: 🔄 Rollback
    runs-on: ubuntu-latest
    needs: [deployment-strategy, post-deployment-validation]
    if: failure() && needs.deployment-strategy.outputs.environment == 'production'
    timeout-minutes: 10
    environment:
      name: production-rollback
    
    steps:
      - name: 🔄 Initiate rollback
        run: |
          echo "🚨 ROLLBACK INITIATED - Deployment validation failed"
          echo "Environment: ${{ needs.deployment-strategy.outputs.environment }}"
          echo "Timestamp: $(date)"
          
          # In a real scenario, you would:
          # 1. Revert to previous Vercel deployment
          # 2. Update database if needed
          # 3. Clear CDN cache
          # 4. Send alerts
          
          echo "⚠️ Manual rollback required - check deployment logs"

  # Notifications
  notify:
    name: 📢 Notifications
    runs-on: ubuntu-latest
    needs: [deployment-strategy, deploy-staging, deploy-production, post-deployment-validation, rollback]
    if: always()
    
    steps:
      - name: 📊 Determine deployment status
        id: status
        run: |
          ENVIRONMENT="${{ needs.deployment-strategy.outputs.environment }}"
          
          if [ "$ENVIRONMENT" == "production" ]; then
            DEPLOY_STATUS="${{ needs.deploy-production.result }}"
            DEPLOYMENT_URL="${{ needs.deploy-production.outputs.deployment-url || 'https://nirmaan-ai.vercel.app' }}"
          else
            DEPLOY_STATUS="${{ needs.deploy-staging.result }}"
            DEPLOYMENT_URL="${{ needs.deploy-staging.outputs.deployment-url || 'https://staging-nirmaan-ai.vercel.app' }}"
          fi
          
          VALIDATION_STATUS="${{ needs.post-deployment-validation.result }}"
          ROLLBACK_STATUS="${{ needs.rollback.result }}"
          
          echo "environment=$ENVIRONMENT" >> $GITHUB_OUTPUT
          echo "deploy-status=$DEPLOY_STATUS" >> $GITHUB_OUTPUT
          echo "validation-status=$VALIDATION_STATUS" >> $GITHUB_OUTPUT
          echo "rollback-status=$ROLLBACK_STATUS" >> $GITHUB_OUTPUT
          echo "deployment-url=$DEPLOYMENT_URL" >> $GITHUB_OUTPUT
          
      - name: 📢 Slack notification
        if: always()
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ steps.status.outputs.deploy-status }}
          channel: '#deployments'
          text: |
            🚀 **Deployment Notification**
            
            **Environment:** ${{ steps.status.outputs.environment }}
            **Status:** ${{ steps.status.outputs.deploy-status }}
            **URL:** ${{ steps.status.outputs.deployment-url }}
            **Validation:** ${{ steps.status.outputs.validation-status }}
            **Branch:** ${{ github.ref_name }}
            **Commit:** ${{ github.sha }}
            **Author:** ${{ github.actor }}
            
            ${{ steps.status.outputs.rollback-status == 'success' && '🔄 **ROLLBACK EXECUTED**' || '' }}
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
        continue-on-error: true
        
      - name: 📧 Email notification
        if: failure() || needs.rollback.result == 'success'
        uses: dawidd6/action-send-mail@v3
        with:
          server_address: smtp.gmail.com
          server_port: 465
          username: ${{ secrets.EMAIL_USERNAME }}
          password: ${{ secrets.EMAIL_PASSWORD }}
          subject: |
            ${{ steps.status.outputs.deploy-status == 'failure' && '🚨 DEPLOYMENT FAILED' || '🔄 ROLLBACK EXECUTED' }} - Nirmaan AI (${{ steps.status.outputs.environment }})
          to: ${{ secrets.ALERT_EMAIL }}
          from: "<EMAIL>"
          body: |
            Deployment Alert - Nirmaan AI Construction Calculator
            
            Environment: ${{ steps.status.outputs.environment }}
            Status: ${{ steps.status.outputs.deploy-status }}
            URL: ${{ steps.status.outputs.deployment-url }}
            Validation: ${{ steps.status.outputs.validation-status }}
            Rollback: ${{ steps.status.outputs.rollback-status }}
            
            Branch: ${{ github.ref_name }}
            Commit: ${{ github.sha }}
            Author: ${{ github.actor }}
            Timestamp: $(date)
            
            ${{ steps.status.outputs.rollback-status == 'success' && 'ROLLBACK HAS BEEN EXECUTED' || 'PLEASE INVESTIGATE DEPLOYMENT FAILURE' }}
            
            View logs: https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}
        continue-on-error: true
        
      - name: 📊 Generate deployment summary
        run: |
          echo "# 🚀 Deployment Summary" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Environment:** ${{ steps.status.outputs.environment }}" >> $GITHUB_STEP_SUMMARY
          echo "**Status:** ${{ steps.status.outputs.deploy-status == 'success' && '✅ Success' || '❌ Failed' }}" >> $GITHUB_STEP_SUMMARY
          echo "**URL:** ${{ steps.status.outputs.deployment-url }}" >> $GITHUB_STEP_SUMMARY
          echo "**Validation:** ${{ steps.status.outputs.validation-status == 'success' && '✅ Passed' || '❌ Failed' }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "## 📊 Deployment Details" >> $GITHUB_STEP_SUMMARY
          echo "- **Branch:** ${{ github.ref_name }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Commit:** ${{ github.sha }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Author:** ${{ github.actor }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Timestamp:** $(date)" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          if [ "${{ steps.status.outputs.rollback-status }}" == "success" ]; then
            echo "## 🔄 Rollback Executed" >> $GITHUB_STEP_SUMMARY
            echo "Automatic rollback was triggered due to deployment validation failure." >> $GITHUB_STEP_SUMMARY
          fi