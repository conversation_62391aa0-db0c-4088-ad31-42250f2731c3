/**
 * Advanced Haptic Feedback System
 * Provides contextual haptic feedback for mobile interactions
 */

interface HapticPattern {
  pattern: number[];
  description: string;
}

interface HapticFeedbackOptions {
  enabled?: boolean;
  intensity?: 'light' | 'medium' | 'heavy';
  patterns?: Record<string, HapticPattern>;
}

class HapticFeedback {
  private options: Required<HapticFeedbackOptions>;
  private isSupported: boolean;
  private lastHapticTime: number = 0;
  private throttleDelay: number = 50; // Minimum time between haptic events

  private defaultPatterns: Record<string, HapticPattern> = {
    tap: {
      pattern: [10],
      description: 'Light tap feedback'
    },
    success: {
      pattern: [50, 30, 50],
      description: 'Success confirmation'
    },
    error: {
      pattern: [100, 50, 100, 50, 100],
      description: 'Error notification'
    },
    warning: {
      pattern: [80, 40, 80],
      description: 'Warning alert'
    },
    button: {
      pattern: [15],
      description: 'Button press feedback'
    },
    toggle: {
      pattern: [20, 10, 20],
      description: 'Toggle switch feedback'
    },
    swipe: {
      pattern: [30],
      description: 'Swipe gesture feedback'
    },
    longPress: {
      pattern: [50, 20, 30],
      description: 'Long press confirmation'
    },
    calculation: {
      pattern: [40, 20, 40, 20, 60],
      description: 'Calculation completion'
    },
    navigation: {
      pattern: [25, 15, 25],
      description: 'Navigation feedback'
    },
    selection: {
      pattern: [20],
      description: 'Item selection feedback'
    },
    refresh: {
      pattern: [60, 40, 60],
      description: 'Pull to refresh feedback'
    }
  };

  constructor(options: Partial<HapticFeedbackOptions> = {}) {
    this.options = {
      enabled: true,
      intensity: 'medium',
      patterns: {},
      ...options
    };

    this.isSupported = this.checkSupport();
    this.init();
  }

  private checkSupport(): boolean {
    return (
      'vibrate' in navigator &&
      typeof navigator.vibrate === 'function' &&
      !this.isDesktop()
    );
  }

  private isDesktop(): boolean {
    return !/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
      navigator.userAgent
    );
  }

  private init(): void {
    // Merge default patterns with custom patterns
    this.options.patterns = {
      ...this.defaultPatterns,
      ...this.options.patterns
    };

    // Add global haptic styles
    this.addHapticStyles();
  }

  private addHapticStyles(): void {
    const style = document.createElement('style');
    style.textContent = `
      .haptic-button {
        transition: transform 0.1s ease, box-shadow 0.1s ease;
      }
      
      .haptic-button:active {
        transform: scale(0.95);
        box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
      }
      
      .haptic-feedback-enabled {
        cursor: pointer;
        user-select: none;
        -webkit-user-select: none;
        -webkit-touch-callout: none;
      }
      
      .haptic-ripple {
        position: relative;
        overflow: hidden;
      }
      
      .haptic-ripple::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.3);
        transform: translate(-50%, -50%);
        transition: width 0.3s ease, height 0.3s ease;
      }
      
      .haptic-ripple.active::after {
        width: 100px;
        height: 100px;
      }
    `;
    
    document.head.appendChild(style);
  }

  private isThrottled(): boolean {
    const now = Date.now();
    return now - this.lastHapticTime < this.throttleDelay;
  }

  private updateLastHapticTime(): void {
    this.lastHapticTime = Date.now();
  }

  // Public API
  trigger(patternName: string, customPattern?: number[]): boolean {
    if (!this.options.enabled || !this.isSupported || this.isThrottled()) {
      return false;
    }

    let pattern: number[];
    
    if (customPattern) {
      pattern = customPattern;
    } else if (this.options.patterns[patternName]) {
      pattern = this.options.patterns[patternName].pattern;
    } else {
      console.warn(`Haptic pattern '${patternName}' not found`);
      return false;
    }

    // Adjust pattern intensity
    const adjustedPattern = this.adjustIntensity(pattern);
    
    try {
      navigator.vibrate(adjustedPattern);
      this.updateLastHapticTime();
      return true;
    } catch (error) {
      console.warn('Haptic feedback failed:', error);
      return false;
    }
  }

  private adjustIntensity(pattern: number[]): number[] {
    const intensityMultipliers = {
      light: 0.5,
      medium: 1.0,
      heavy: 1.5
    };

    const multiplier = intensityMultipliers[this.options.intensity];
    return pattern.map(duration => Math.round(duration * multiplier));
  }

  // Convenience methods for common interactions
  tap(): boolean {
    return this.trigger('tap');
  }

  success(): boolean {
    return this.trigger('success');
  }

  error(): boolean {
    return this.trigger('error');
  }

  warning(): boolean {
    return this.trigger('warning');
  }

  button(): boolean {
    return this.trigger('button');
  }

  toggle(): boolean {
    return this.trigger('toggle');
  }

  swipe(): boolean {
    return this.trigger('swipe');
  }

  longPress(): boolean {
    return this.trigger('longPress');
  }

  calculation(): boolean {
    return this.trigger('calculation');
  }

  navigation(): boolean {
    return this.trigger('navigation');
  }

  selection(): boolean {
    return this.trigger('selection');
  }

  refresh(): boolean {
    return this.trigger('refresh');
  }

  // Element enhancement methods
  enhanceElement(element: HTMLElement, patternName: string = 'button'): void {
    if (!element) return;

    element.classList.add('haptic-feedback-enabled', 'haptic-button');
    
    const handleInteraction = (event: Event) => {
      this.trigger(patternName);
      this.addRippleEffect(element, event);
    };

    // Add both touch and click events for compatibility
    element.addEventListener('touchstart', handleInteraction, { passive: true });
    element.addEventListener('click', handleInteraction);

    // Store the handler for potential cleanup
    (element as any).__hapticHandler = handleInteraction;
  }

  removeElementEnhancement(element: HTMLElement): void {
    if (!element) return;

    element.classList.remove('haptic-feedback-enabled', 'haptic-button', 'haptic-ripple');
    
    const handler = (element as any).__hapticHandler;
    if (handler) {
      element.removeEventListener('touchstart', handler);
      element.removeEventListener('click', handler);
      delete (element as any).__hapticHandler;
    }
  }

  private addRippleEffect(element: HTMLElement, event: Event): void {
    element.classList.add('haptic-ripple');
    
    setTimeout(() => {
      element.classList.add('active');
    }, 10);

    setTimeout(() => {
      element.classList.remove('active', 'haptic-ripple');
    }, 300);
  }

  // Batch enhancement for multiple elements
  enhanceElements(selector: string, patternName: string = 'button'): void {
    const elements = document.querySelectorAll(selector);
    elements.forEach(element => {
      this.enhanceElement(element as HTMLElement, patternName);
    });
  }

  // Pattern management
  addPattern(name: string, pattern: HapticPattern): void {
    this.options.patterns[name] = pattern;
  }

  removePattern(name: string): void {
    delete this.options.patterns[name];
  }

  getPatterns(): Record<string, HapticPattern> {
    return { ...this.options.patterns };
  }

  // Settings management
  enable(): void {
    this.options.enabled = true;
  }

  disable(): void {
    this.options.enabled = false;
    navigator.vibrate([]); // Stop any ongoing vibration
  }

  setIntensity(intensity: 'light' | 'medium' | 'heavy'): void {
    this.options.intensity = intensity;
  }

  isEnabled(): boolean {
    return this.options.enabled && this.isSupported;
  }

  isDeviceSupported(): boolean {
    return this.isSupported;
  }

  // Advanced haptic sequences
  playSequence(patternNames: string[], delay: number = 100): Promise<void> {
    return new Promise(resolve => {
      let index = 0;
      
      const playNext = () => {
        if (index >= patternNames.length) {
          resolve();
          return;
        }
        
        this.trigger(patternNames[index]);
        index++;
        
        setTimeout(playNext, delay);
      };
      
      playNext();
    });
  }

  stop(): void {
    if (this.isSupported) {
      navigator.vibrate([]);
    }
  }
}

// Create a singleton instance
const hapticFeedback = new HapticFeedback();

// Auto-enhance common elements
if (typeof window !== 'undefined') {
  document.addEventListener('DOMContentLoaded', () => {
    // Auto-enhance buttons with haptic feedback
    hapticFeedback.enhanceElements('button:not(.no-haptic)', 'button');
    hapticFeedback.enhanceElements('input[type="submit"]:not(.no-haptic)', 'button');
    hapticFeedback.enhanceElements('.haptic-enabled', 'tap');
  });
}

export default hapticFeedback;
export { HapticFeedback };
export type { HapticPattern, HapticFeedbackOptions };