'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  webVitalsMonitor, 
  performanceBudget, 
  WebVitalsData, 
  PerformanceReport, 
  PerformanceAlert 
} from '@/lib/performance/web-vitals';
import { 
  Activity, 
  AlertTriangle, 
  CheckCircle, 
  XCircle, 
  TrendingUp, 
  TrendingDown, 
  Zap, 
  Clock, 
  Smartphone, 
  Monitor, 
  Wifi, 
  Download,
  Eye,
  AlertCircle,
  RefreshCw
} from 'lucide-react';

interface MetricCardProps {
  metric: WebVitalsData | null;
  title: string;
  unit: string;
  thresholds: { good: number; poor: number };
  icon: React.ReactNode;
  description: string;
}

const MetricCard: React.FC<MetricCardProps> = ({ 
  metric, 
  title, 
  unit, 
  thresholds, 
  icon, 
  description 
}) => {
  const getRatingColor = (rating: string | undefined) => {
    switch (rating) {
      case 'good': return 'text-green-600 bg-green-50';
      case 'needs-improvement': return 'text-yellow-600 bg-yellow-50';
      case 'poor': return 'text-red-600 bg-red-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getRatingIcon = (rating: string | undefined) => {
    switch (rating) {
      case 'good': return <CheckCircle className="w-4 h-4" />;
      case 'needs-improvement': return <AlertTriangle className="w-4 h-4" />;
      case 'poor': return <XCircle className="w-4 h-4" />;
      default: return <Activity className="w-4 h-4" />;
    }
  };

  const formatValue = (value: number | undefined) => {
    if (!value) return 'N/A';
    
    if (unit === 'ms') {
      return `${value.toFixed(0)}ms`;
    }
    if (unit === 's') {
      return `${(value / 1000).toFixed(2)}s`;
    }
    if (unit === '') {
      return value.toFixed(3);
    }
    return `${value.toFixed(0)}${unit}`;
  };

  return (
    <Card className="p-6 hover:shadow-md transition-shadow">
      <div className="flex items-start justify-between">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-blue-100 rounded-lg">
            {icon}
          </div>
          <div>
            <h3 className="font-semibold text-gray-900">{title}</h3>
            <p className="text-sm text-gray-600">{description}</p>
          </div>
        </div>
        <div className={`flex items-center space-x-1 px-2 py-1 rounded-full ${getRatingColor(metric?.rating)}`}>
          {getRatingIcon(metric?.rating)}
          <span className="text-xs font-medium">
            {metric?.rating?.replace('-', ' ') || 'N/A'}
          </span>
        </div>
      </div>
      
      <div className="mt-4">
        <div className="flex items-baseline space-x-2">
          <span className="text-2xl font-bold text-gray-900">
            {formatValue(metric?.value)}
          </span>
          {metric?.delta && (
            <span className={`text-sm ${metric.delta > 0 ? 'text-red-600' : 'text-green-600'}`}>
              {metric.delta > 0 ? '+' : ''}{formatValue(metric.delta)}
            </span>
          )}
        </div>
        
        <div className="mt-2 flex items-center space-x-4 text-xs text-gray-500">
          <span>Good: ≤{formatValue(thresholds.good)}</span>
          <span>Poor: &gt;{formatValue(thresholds.poor)}</span>
        </div>
        
        {metric && (
          <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
            <div 
              className={`h-2 rounded-full ${getRatingColor(metric.rating).includes('green') ? 'bg-green-500' : 
                getRatingColor(metric.rating).includes('yellow') ? 'bg-yellow-500' : 'bg-red-500'}`}
              style={{ 
                width: `${Math.min(100, (metric.value / (thresholds.poor * 1.5)) * 100)}%` 
              }}
            />
          </div>
        )}
      </div>
    </Card>
  );
};

interface AlertItemProps {
  alert: PerformanceAlert;
  onDismiss: () => void;
}

const AlertItem: React.FC<AlertItemProps> = ({ alert, onDismiss }) => {
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'low': return 'border-blue-200 bg-blue-50';
      case 'medium': return 'border-yellow-200 bg-yellow-50';
      case 'high': return 'border-orange-200 bg-orange-50';
      case 'critical': return 'border-red-200 bg-red-50';
      default: return 'border-gray-200 bg-gray-50';
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'low': return <AlertCircle className="w-4 h-4 text-blue-600" />;
      case 'medium': return <AlertTriangle className="w-4 h-4 text-yellow-600" />;
      case 'high': return <AlertTriangle className="w-4 h-4 text-orange-600" />;
      case 'critical': return <XCircle className="w-4 h-4 text-red-600" />;
      default: return <AlertCircle className="w-4 h-4 text-gray-600" />;
    }
  };

  return (
    <div className={`p-4 rounded-lg border ${getSeverityColor(alert.severity)}`}>
      <div className="flex items-start justify-between">
        <div className="flex items-start space-x-3">
          {getSeverityIcon(alert.severity)}
          <div>
            <h4 className="font-medium text-gray-900">{alert.metric}</h4>
            <p className="text-sm text-gray-600">{alert.message}</p>
            <p className="text-xs text-gray-500 mt-1">
              {new Date(alert.timestamp).toLocaleTimeString()}
            </p>
          </div>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={onDismiss}
          className="text-gray-400 hover:text-gray-600"
        >
          <XCircle className="w-4 h-4" />
        </Button>
      </div>
    </div>
  );
};

export const PerformanceDashboard: React.FC = () => {
  const [metrics, setMetrics] = useState<Map<string, WebVitalsData>>(new Map());
  const [report, setReport] = useState<PerformanceReport | null>(null);
  const [alerts, setAlerts] = useState<PerformanceAlert[]>([]);
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [performanceScore, setPerformanceScore] = useState(0);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    // Initialize monitoring
    webVitalsMonitor.onReport((newReport) => {
      setReport(newReport);
      setMetrics(webVitalsMonitor.getMetrics());
      setPerformanceScore(webVitalsMonitor.getPerformanceScore());
    });

    webVitalsMonitor.onAlert((alert) => {
      setAlerts(prev => [...prev, alert]);
    });

    // Start real-time monitoring
    webVitalsMonitor.startRealTimeMonitoring();

    // Update metrics every 5 seconds
    intervalRef.current = setInterval(() => {
      setMetrics(webVitalsMonitor.getMetrics());
      setPerformanceScore(webVitalsMonitor.getPerformanceScore());
    }, 5000);

    setIsMonitoring(true);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  const dismissAlert = (index: number) => {
    setAlerts(prev => prev.filter((_, i) => i !== index));
  };

  const clearAllAlerts = () => {
    setAlerts([]);
    webVitalsMonitor.clearAlerts();
  };

  const refreshMetrics = () => {
    setMetrics(webVitalsMonitor.getMetrics());
    setReport(webVitalsMonitor.generateReport());
    setPerformanceScore(webVitalsMonitor.getPerformanceScore());
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreLabel = (score: number) => {
    if (score >= 90) return 'Excellent';
    if (score >= 70) return 'Good';
    if (score >= 50) return 'Needs Improvement';
    return 'Poor';
  };

  const webVitalsConfig = [
    {
      key: 'LCP',
      title: 'Largest Contentful Paint',
      unit: 'ms',
      thresholds: { good: 2500, poor: 4000 },
      icon: <Eye className="w-5 h-5 text-blue-600" />,
      description: 'Time to render the largest content element'
    },
    {
      key: 'FID',
      title: 'First Input Delay',
      unit: 'ms',
      thresholds: { good: 100, poor: 300 },
      icon: <Zap className="w-5 h-5 text-blue-600" />,
      description: 'Time from first user interaction to response'
    },
    {
      key: 'CLS',
      title: 'Cumulative Layout Shift',
      unit: '',
      thresholds: { good: 0.1, poor: 0.25 },
      icon: <Activity className="w-5 h-5 text-blue-600" />,
      description: 'Visual stability during page load'
    },
    {
      key: 'FCP',
      title: 'First Contentful Paint',
      unit: 'ms',
      thresholds: { good: 1800, poor: 3000 },
      icon: <Clock className="w-5 h-5 text-blue-600" />,
      description: 'Time to render the first content element'
    },
    {
      key: 'TTFB',
      title: 'Time to First Byte',
      unit: 'ms',
      thresholds: { good: 800, poor: 1800 },
      icon: <Download className="w-5 h-5 text-blue-600" />,
      description: 'Time from request to first byte of response'
    },
    {
      key: 'INP',
      title: 'Interaction to Next Paint',
      unit: 'ms',
      thresholds: { good: 200, poor: 500 },
      icon: <TrendingUp className="w-5 h-5 text-blue-600" />,
      description: 'Responsiveness to user interactions'
    }
  ];

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Performance Dashboard</h1>
          <p className="text-gray-600">Real-time Core Web Vitals monitoring</p>
        </div>
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <div className={`w-2 h-2 rounded-full ${isMonitoring ? 'bg-green-500' : 'bg-red-500'}`} />
            <span className="text-sm text-gray-600">
              {isMonitoring ? 'Monitoring' : 'Offline'}
            </span>
          </div>
          <Button onClick={refreshMetrics} variant="outline" size="sm">
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Performance Score */}
      <Card className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Performance Score</h2>
            <p className="text-gray-600">Overall performance rating</p>
          </div>
          <div className="text-right">
            <div className={`text-4xl font-bold ${getScoreColor(performanceScore)}`}>
              {performanceScore}
            </div>
            <div className={`text-sm ${getScoreColor(performanceScore)}`}>
              {getScoreLabel(performanceScore)}
            </div>
          </div>
        </div>
        <div className="mt-4 w-full bg-gray-200 rounded-full h-3">
          <div 
            className={`h-3 rounded-full transition-all duration-300 ${
              performanceScore >= 90 ? 'bg-green-500' :
              performanceScore >= 70 ? 'bg-yellow-500' : 'bg-red-500'
            }`}
            style={{ width: `${performanceScore}%` }}
          />
        </div>
      </Card>

      {/* Alerts */}
      {alerts.length > 0 && (
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-gray-900">Performance Alerts</h2>
            <Button onClick={clearAllAlerts} variant="outline" size="sm">
              Clear All
            </Button>
          </div>
          <div className="space-y-3">
            {alerts.slice(-5).map((alert, index) => (
              <AlertItem
                key={alert.timestamp}
                alert={alert}
                onDismiss={() => dismissAlert(index)}
              />
            ))}
          </div>
        </Card>
      )}

      {/* Core Web Vitals */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Core Web Vitals</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {webVitalsConfig.map((config) => (
            <MetricCard
              key={config.key}
              metric={metrics.get(config.key) || null}
              title={config.title}
              unit={config.unit}
              thresholds={config.thresholds}
              icon={config.icon}
              description={config.description}
            />
          ))}
        </div>
      </div>

      {/* Additional Metrics */}
      {report && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="p-6">
            <div className="flex items-center space-x-3">
              <Clock className="w-5 h-5 text-blue-600" />
              <div>
                <h3 className="font-semibold text-gray-900">Page Load Time</h3>
                <p className="text-2xl font-bold text-gray-900">
                  {(report.loadTime / 1000).toFixed(2)}s
                </p>
              </div>
            </div>
          </Card>

          <Card className="p-6">
            <div className="flex items-center space-x-3">
              <Download className="w-5 h-5 text-blue-600" />
              <div>
                <h3 className="font-semibold text-gray-900">Resources</h3>
                <p className="text-2xl font-bold text-gray-900">
                  {report.resourceCount}
                </p>
              </div>
            </div>
          </Card>

          <Card className="p-6">
            <div className="flex items-center space-x-3">
              <TrendingUp className="w-5 h-5 text-blue-600" />
              <div>
                <h3 className="font-semibold text-gray-900">Cache Hit Rate</h3>
                <p className="text-2xl font-bold text-gray-900">
                  {report.cacheHitRate.toFixed(1)}%
                </p>
              </div>
            </div>
          </Card>

          <Card className="p-6">
            <div className="flex items-center space-x-3">
              <Activity className="w-5 h-5 text-blue-600" />
              <div>
                <h3 className="font-semibold text-gray-900">Memory Usage</h3>
                <p className="text-2xl font-bold text-gray-900">
                  {report.memoryUsage ? 
                    `${(report.memoryUsage.usedJSHeapSize / 1024 / 1024).toFixed(1)}MB` : 
                    'N/A'
                  }
                </p>
              </div>
            </div>
          </Card>
        </div>
      )}

      {/* Device & Network Info */}
      {report?.deviceInfo && report?.networkInfo && (
        <Card className="p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Environment</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-medium text-gray-900 mb-2">Device Information</h3>
              <div className="space-y-2 text-sm">
                <div className="flex items-center space-x-2">
                  {report.deviceInfo.type === 'mobile' ? <Smartphone className="w-4 h-4" /> : <Monitor className="w-4 h-4" />}
                  <span>Type: {report.deviceInfo.type}</span>
                </div>
                <div>Viewport: {report.deviceInfo.viewport.width}x{report.deviceInfo.viewport.height}</div>
                <div>Screen: {report.deviceInfo.screen.width}x{report.deviceInfo.screen.height}</div>
                <div>Pixel Ratio: {report.deviceInfo.pixelRatio}</div>
              </div>
            </div>
            <div>
              <h3 className="font-medium text-gray-900 mb-2">Network Information</h3>
              <div className="space-y-2 text-sm">
                <div className="flex items-center space-x-2">
                  <Wifi className="w-4 h-4" />
                  <span>Type: {report.networkInfo.effectiveType}</span>
                </div>
                <div>Downlink: {report.networkInfo.downlink} Mbps</div>
                <div>RTT: {report.networkInfo.rtt}ms</div>
                <div>Save Data: {report.networkInfo.saveData ? 'On' : 'Off'}</div>
              </div>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};

export default PerformanceDashboard;