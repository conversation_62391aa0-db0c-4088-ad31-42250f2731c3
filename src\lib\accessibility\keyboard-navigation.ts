/**
 * Keyboard Navigation System for Enhanced Accessibility
 * Provides comprehensive keyboard shortcuts and navigation for WCAG 2.1 AA compliance
 */

import { focusManager } from './focus-management';

export interface KeyboardShortcut {
  key: string;
  ctrlKey?: boolean;
  altKey?: boolean;
  shiftKey?: boolean;
  metaKey?: boolean;
  description: string;
  action: (event: KeyboardEvent) => void;
  global?: boolean;
  disabled?: boolean;
}

export interface NavigationContext {
  name: string;
  shortcuts: KeyboardShortcut[];
  priority: number;
}

/**
 * Comprehensive keyboard navigation manager
 */
export class KeyboardNavigationManager {
  private static instance: KeyboardNavigationManager;
  private contexts: Map<string, NavigationContext> = new Map();
  private activeContexts: Set<string> = new Set();
  private isEnabled = true;
  private helpModal: HTMLElement | null = null;

  constructor() {
    this.setupGlobalListeners();
    this.registerDefaultShortcuts();
  }

  static getInstance(): KeyboardNavigationManager {
    if (!KeyboardNavigationManager.instance) {
      KeyboardNavigationManager.instance = new KeyboardNavigationManager();
    }
    return KeyboardNavigationManager.instance;
  }

  /**
   * Register a navigation context with shortcuts
   */
  registerContext(context: NavigationContext): void {
    this.contexts.set(context.name, context);
  }

  /**
   * Activate a navigation context
   */
  activateContext(contextName: string): void {
    if (this.contexts.has(contextName)) {
      this.activeContexts.add(contextName);
    }
  }

  /**
   * Deactivate a navigation context
   */
  deactivateContext(contextName: string): void {
    this.activeContexts.delete(contextName);
  }

  /**
   * Enable/disable keyboard navigation
   */
  setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
    if (enabled) {
      focusManager.announce('Keyboard navigation enabled');
    } else {
      focusManager.announce('Keyboard navigation disabled');
    }
  }

  /**
   * Show keyboard shortcuts help
   */
  showHelp(): void {
    if (this.helpModal) {
      this.closeHelp();
      return;
    }

    this.createHelpModal();
  }

  /**
   * Close keyboard shortcuts help
   */
  closeHelp(): void {
    if (this.helpModal) {
      const focusTrap = this.helpModal.getAttribute('data-focus-trap');
      if (focusTrap) {
        // Deactivate focus trap
        const trap = (this.helpModal as any).__focusTrap;
        if (trap) {
          trap.deactivate();
        }
      }
      
      document.body.removeChild(this.helpModal);
      this.helpModal = null;
      focusManager.restoreFocus();
      focusManager.announce('Keyboard shortcuts help closed');
    }
  }

  private setupGlobalListeners(): void {
    document.addEventListener('keydown', (event) => {
      if (!this.isEnabled) return;

      // Handle global shortcuts first
      if (this.handleGlobalShortcuts(event)) {
        return;
      }

      // Handle context-specific shortcuts
      this.handleContextShortcuts(event);
    });

    // Handle visibility change
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        this.closeHelp();
      }
    });
  }

  private handleGlobalShortcuts(event: KeyboardEvent): boolean {
    const shortcuts = [
      {
        key: 'F1',
        description: 'Show keyboard shortcuts help',
        action: () => this.showHelp()
      },
      {
        key: '?',
        shiftKey: true,
        description: 'Show keyboard shortcuts help',
        action: () => this.showHelp()
      },
      {
        key: 'Escape',
        description: 'Close modals and overlays',
        action: () => {
          if (this.helpModal) {
            this.closeHelp();
          } else {
            // Let other escape handlers work
            return false;
          }
        }
      },
      {
        key: 'Tab',
        description: 'Navigate to next element',
        action: (e) => {
          // Enhanced tab navigation with announcements
          const activeElement = document.activeElement as HTMLElement;
          if (activeElement && activeElement.getAttribute('aria-label')) {
            setTimeout(() => {
              const newActive = document.activeElement as HTMLElement;
              if (newActive && newActive !== activeElement) {
                const label = newActive.getAttribute('aria-label') || 
                             newActive.getAttribute('title') ||
                             newActive.textContent?.trim();
                if (label) {
                  focusManager.announce(`Focused on ${label}`);
                }
              }
            }, 50);
          }
          return false; // Let default tab behavior work
        }
      },
      {
        key: 'Home',
        ctrlKey: true,
        description: 'Go to top of page',
        action: () => {
          window.scrollTo({ top: 0, behavior: 'smooth' });
          const mainContent = document.querySelector('main, [role="main"]') as HTMLElement;
          if (mainContent) {
            focusManager.focus(mainContent);
          }
          focusManager.announce('Moved to top of page');
        }
      },
      {
        key: 'End',
        ctrlKey: true,
        description: 'Go to bottom of page',
        action: () => {
          window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' });
          focusManager.announce('Moved to bottom of page');
        }
      }
    ];

    for (const shortcut of shortcuts) {
      if (this.matchesShortcut(event, shortcut)) {
        const result = shortcut.action(event);
        if (result !== false) {
          event.preventDefault();
          return true;
        }
      }
    }

    return false;
  }

  private handleContextShortcuts(event: KeyboardEvent): void {
    const activeContextList = Array.from(this.activeContexts)
      .map(name => this.contexts.get(name))
      .filter(Boolean)
      .sort((a, b) => b!.priority - a!.priority);

    for (const context of activeContextList) {
      for (const shortcut of context!.shortcuts) {
        if (!shortcut.disabled && this.matchesShortcut(event, shortcut)) {
          event.preventDefault();
          shortcut.action(event);
          return;
        }
      }
    }
  }

  private matchesShortcut(event: KeyboardEvent, shortcut: KeyboardShortcut): boolean {
    return (
      event.key === shortcut.key &&
      !!event.ctrlKey === !!shortcut.ctrlKey &&
      !!event.altKey === !!shortcut.altKey &&
      !!event.shiftKey === !!shortcut.shiftKey &&
      !!event.metaKey === !!shortcut.metaKey
    );
  }

  private registerDefaultShortcuts(): void {
    // Calculator context shortcuts
    this.registerContext({
      name: 'calculator',
      priority: 100,
      shortcuts: [
        {
          key: 'Enter',
          ctrlKey: true,
          description: 'Calculate construction cost',
          action: () => {
            const calculateButton = document.querySelector('[data-testid="calculate-button"], button:contains("Calculate")') as HTMLElement;
            if (calculateButton && !calculateButton.hasAttribute('disabled')) {
              calculateButton.click();
              focusManager.announce('Calculating construction cost');
            }
          }
        },
        {
          key: 'r',
          ctrlKey: true,
          description: 'Reset calculator form',
          action: (e) => {
            e.preventDefault();
            const resetButton = document.querySelector('[data-testid="reset-button"], button:contains("Reset")') as HTMLElement;
            if (resetButton) {
              resetButton.click();
              focusManager.announce('Calculator form reset');
            }
          }
        },
        {
          key: 'ArrowDown',
          altKey: true,
          description: 'Open dropdown/select',
          action: () => {
            const activeElement = document.activeElement as HTMLElement;
            if (activeElement && activeElement.getAttribute('role') === 'combobox') {
              const trigger = activeElement.closest('[data-radix-select-trigger]') as HTMLElement;
              if (trigger) {
                trigger.click();
              }
            }
          }
        },
        {
          key: 'p',
          ctrlKey: true,
          shiftKey: true,
          description: 'Export PDF report',
          action: (e) => {
            e.preventDefault();
            const pdfButton = document.querySelector('[data-testid="pdf-export"], button:contains("PDF")') as HTMLElement;
            if (pdfButton) {
              pdfButton.click();
              focusManager.announce('Exporting PDF report');
            }
          }
        }
      ]
    });

    // Form navigation shortcuts
    this.registerContext({
      name: 'form',
      priority: 90,
      shortcuts: [
        {
          key: 'n',
          altKey: true,
          description: 'Next form field',
          action: () => {
            focusManager.focusNext();
          }
        },
        {
          key: 'p',
          altKey: true,
          description: 'Previous form field',
          action: () => {
            focusManager.focusPrevious();
          }
        },
        {
          key: 'Home',
          description: 'First form field',
          action: () => {
            const form = document.querySelector('form, [role="form"]') as HTMLElement;
            if (form) {
              focusManager.focusFirst(form);
            }
          }
        },
        {
          key: 'End',
          description: 'Last form field',
          action: () => {
            const form = document.querySelector('form, [role="form"]') as HTMLElement;
            if (form) {
              focusManager.focusLast(form);
            }
          }
        }
      ]
    });

    // Modal/Dialog shortcuts
    this.registerContext({
      name: 'modal',
      priority: 200,
      shortcuts: [
        {
          key: 'Escape',
          description: 'Close modal',
          action: () => {
            const modal = document.querySelector('[role="dialog"]:not([aria-hidden="true"])') as HTMLElement;
            if (modal) {
              const closeButton = modal.querySelector('[aria-label*="close"], [data-testid="close-button"]') as HTMLElement;
              if (closeButton) {
                closeButton.click();
              }
            }
          }
        }
      ]
    });

    // Navigation shortcuts
    this.registerContext({
      name: 'navigation',
      priority: 80,
      shortcuts: [
        {
          key: 'h',
          description: 'Go to home page',
          action: () => {
            const homeLink = document.querySelector('a[href="/"], a[href=""]') as HTMLElement;
            if (homeLink) {
              homeLink.click();
            }
          }
        },
        {
          key: 'c',
          description: 'Go to calculator',
          action: () => {
            const calcLink = document.querySelector('a[href*="calculator"]') as HTMLElement;
            if (calcLink) {
              calcLink.click();
            } else {
              window.location.href = '/calculator';
            }
          }
        },
        {
          key: 'm',
          description: 'Open main menu',
          action: () => {
            const menuButton = document.querySelector('[aria-label*="menu"], [data-testid="menu-button"]') as HTMLElement;
            if (menuButton) {
              menuButton.click();
              focusManager.announce('Main menu opened');
            }
          }
        }
      ]
    });
  }

  private createHelpModal(): void {
    // Create modal structure
    this.helpModal = document.createElement('div');
    this.helpModal.className = 'fixed inset-0 z-50 flex items-center justify-center bg-black/50';
    this.helpModal.setAttribute('role', 'dialog');
    this.helpModal.setAttribute('aria-modal', 'true');
    this.helpModal.setAttribute('aria-labelledby', 'keyboard-help-title');

    const modalContent = document.createElement('div');
    modalContent.className = 'bg-white rounded-lg shadow-xl max-w-2xl max-h-[80vh] overflow-y-auto p-6 m-4';

    // Modal header
    const header = document.createElement('div');
    header.className = 'flex justify-between items-center mb-4';
    
    const title = document.createElement('h2');
    title.id = 'keyboard-help-title';
    title.className = 'text-xl font-semibold text-gray-900';
    title.textContent = 'Keyboard Shortcuts';

    const closeButton = document.createElement('button');
    closeButton.className = 'p-2 hover:bg-gray-100 rounded-md';
    closeButton.setAttribute('aria-label', 'Close keyboard shortcuts help');
    closeButton.innerHTML = '&times;';
    closeButton.onclick = () => this.closeHelp();

    header.appendChild(title);
    header.appendChild(closeButton);

    // Modal body
    const body = document.createElement('div');
    body.className = 'space-y-6';

    // Get all shortcuts from active contexts
    const allShortcuts: { context: string; shortcuts: KeyboardShortcut[] }[] = [];
    
    // Add global shortcuts
    allShortcuts.push({
      context: 'Global',
      shortcuts: [
        { key: 'F1 or ?', description: 'Show keyboard shortcuts help', action: () => {} },
        { key: 'Ctrl+Home', description: 'Go to top of page', action: () => {} },
        { key: 'Ctrl+End', description: 'Go to bottom of page', action: () => {} },
        { key: 'Tab', description: 'Navigate to next element', action: () => {} },
        { key: 'Shift+Tab', description: 'Navigate to previous element', action: () => {} }
      ]
    });

    // Add context shortcuts
    Array.from(this.activeContexts).forEach(contextName => {
      const context = this.contexts.get(contextName);
      if (context) {
        allShortcuts.push({
          context: this.capitalizeFirst(contextName),
          shortcuts: context.shortcuts.filter(s => !s.disabled)
        });
      }
    });

    // Create shortcut sections
    allShortcuts.forEach(({ context, shortcuts }) => {
      if (shortcuts.length === 0) return;

      const section = document.createElement('div');
      
      const sectionTitle = document.createElement('h3');
      sectionTitle.className = 'text-lg font-medium text-gray-800 mb-2';
      sectionTitle.textContent = `${context} Shortcuts`;
      
      const shortcutList = document.createElement('dl');
      shortcutList.className = 'space-y-2';

      shortcuts.forEach(shortcut => {
        const item = document.createElement('div');
        item.className = 'flex justify-between items-center';

        const key = document.createElement('dt');
        key.className = 'font-mono text-sm bg-gray-100 px-2 py-1 rounded';
        key.textContent = this.formatShortcut(shortcut);

        const description = document.createElement('dd');
        description.className = 'text-sm text-gray-600 ml-4';
        description.textContent = shortcut.description;

        item.appendChild(key);
        item.appendChild(description);
        shortcutList.appendChild(item);
      });

      section.appendChild(sectionTitle);
      section.appendChild(shortcutList);
      body.appendChild(section);
    });

    // Assembly
    modalContent.appendChild(header);
    modalContent.appendChild(body);
    this.helpModal.appendChild(modalContent);
    document.body.appendChild(this.helpModal);

    // Setup focus trap
    const focusTrap = focusManager.createFocusTrap(modalContent);
    (this.helpModal as any).__focusTrap = focusTrap;
    focusTrap.activate();

    focusManager.announce('Keyboard shortcuts help opened');
  }

  private formatShortcut(shortcut: KeyboardShortcut): string {
    const parts = [];
    if (shortcut.ctrlKey) parts.push('Ctrl');
    if (shortcut.altKey) parts.push('Alt');
    if (shortcut.shiftKey) parts.push('Shift');
    if (shortcut.metaKey) parts.push('Cmd');
    parts.push(shortcut.key);
    return parts.join('+');
  }

  private capitalizeFirst(str: string): string {
    return str.charAt(0).toUpperCase() + str.slice(1);
  }
}

// Export singleton instance
export const keyboardNav = KeyboardNavigationManager.getInstance();

/**
 * React hook for keyboard navigation
 */
export function useKeyboardNavigation(contextName?: string) {
  const manager = KeyboardNavigationManager.getInstance();

  return {
    registerContext: manager.registerContext.bind(manager),
    activateContext: manager.activateContext.bind(manager),
    deactivateContext: manager.deactivateContext.bind(manager),
    showHelp: manager.showHelp.bind(manager),
    setEnabled: manager.setEnabled.bind(manager),
    
    // Convenience method for component contexts
    useContext: (context: NavigationContext) => {
      manager.registerContext(context);
      if (contextName) {
        manager.activateContext(contextName);
        return () => manager.deactivateContext(contextName);
      }
    }
  };
}