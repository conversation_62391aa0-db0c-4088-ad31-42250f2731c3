/**
 * Mobile Touch Accessibility Enhancement
 * Provides comprehensive touch accessibility features for mobile devices
 */

import { focusManager } from './focus-management';
import { screenReader } from './screen-reader';

export interface TouchGesture {
  type: 'tap' | 'double-tap' | 'long-press' | 'swipe-left' | 'swipe-right' | 'swipe-up' | 'swipe-down';
  element: HTMLElement;
  callback: (event: TouchEvent | PointerEvent) => void;
  options?: {
    threshold?: number;
    timeout?: number;
    preventDefault?: boolean;
  };
}

export interface MobileTouchConfig {
  enabled: boolean;
  minTouchSize: number;
  doubleTapDelay: number;
  longPressDelay: number;
  swipeThreshold: number;
  enableHapticFeedback: boolean;
  announceTouch: boolean;
  enhanceScrolling: boolean;
}

/**
 * Mobile Touch Accessibility Manager
 */
export class MobileTouchAccessibilityManager {
  private static instance: MobileTouchAccessibilityManager;
  private config: MobileTouchConfig;
  private gestures: Map<HTMLElement, TouchGesture[]> = new Map();
  private touchStartTime = 0;
  private touchStartPosition = { x: 0, y: 0 };
  private lastTapTime = 0;
  private lastTapElement: HTMLElement | null = null;

  constructor() {
    this.config = this.getDefaultConfig();
    this.setupGlobalTouchHandlers();
    this.enhanceExistingElements();
    this.loadConfig();
  }

  static getInstance(): MobileTouchAccessibilityManager {
    if (!MobileTouchAccessibilityManager.instance) {
      MobileTouchAccessibilityManager.instance = new MobileTouchAccessibilityManager();
    }
    return MobileTouchAccessibilityManager.instance;
  }

  /**
   * Check if device supports touch
   */
  isTouchDevice(): boolean {
    return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
  }

  /**
   * Get current configuration
   */
  getConfig(): MobileTouchConfig {
    return { ...this.config };
  }

  /**
   * Update configuration
   */
  updateConfig(updates: Partial<MobileTouchConfig>): void {
    this.config = { ...this.config, ...updates };
    this.saveConfig();
    this.applyConfiguration();
  }

  /**
   * Enable mobile touch accessibility
   */
  enable(): void {
    this.config.enabled = true;
    this.applyConfiguration();
    screenReader.announce('Mobile touch accessibility enabled');
  }

  /**
   * Disable mobile touch accessibility
   */
  disable(): void {
    this.config.enabled = false;
    screenReader.announce('Mobile touch accessibility disabled');
  }

  /**
   * Register touch gesture
   */
  registerGesture(gesture: TouchGesture): void {
    const existingGestures = this.gestures.get(gesture.element) || [];
    existingGestures.push(gesture);
    this.gestures.set(gesture.element, existingGestures);
  }

  /**
   * Remove touch gesture
   */
  removeGesture(element: HTMLElement, type?: TouchGesture['type']): void {
    const gestures = this.gestures.get(element);
    if (!gestures) return;

    if (type) {
      const filtered = gestures.filter(g => g.type !== type);
      this.gestures.set(element, filtered);
    } else {
      this.gestures.delete(element);
    }
  }

  /**
   * Enhance element for touch accessibility
   */
  enhanceElement(element: HTMLElement): void {
    if (!this.config.enabled) return;

    // Ensure minimum touch target size
    this.ensureMinimumTouchSize(element);

    // Add touch feedback
    this.addTouchFeedback(element);

    // Add gesture support for interactive elements
    if (this.isInteractiveElement(element)) {
      this.addInteractiveGestures(element);
    }

    // Add scroll enhancements
    if (this.isScrollableElement(element)) {
      this.enhanceScrolling(element);
    }
  }

  /**
   * Create accessible touch controls
   */
  createTouchControls(container: HTMLElement): void {
    const controlsContainer = document.createElement('div');
    controlsContainer.className = 'touch-accessibility-controls fixed bottom-4 right-4 z-50 md:hidden';
    controlsContainer.setAttribute('role', 'toolbar');
    controlsContainer.setAttribute('aria-label', 'Touch accessibility controls');

    // Voice control toggle
    const voiceButton = this.createControlButton('🎤', 'Toggle voice control', () => {
      // Implementation would integrate with voice navigation
      screenReader.announce('Voice control toggled');
    });

    // Reading mode toggle
    const readingButton = this.createControlButton('📖', 'Toggle reading mode', () => {
      this.toggleReadingMode();
    });

    // High contrast toggle
    const contrastButton = this.createControlButton('🌓', 'Toggle high contrast', () => {
      // Implementation would integrate with theme manager
      screenReader.announce('High contrast toggled');
    });

    // Text size controls
    const textSizeContainer = document.createElement('div');
    textSizeContainer.className = 'flex flex-col gap-2';

    const increaseFontButton = this.createControlButton('A+', 'Increase text size', () => {
      this.adjustTextSize(1.1);
    });

    const decreaseFontButton = this.createControlButton('A-', 'Decrease text size', () => {
      this.adjustTextSize(0.9);
    });

    textSizeContainer.appendChild(increaseFontButton);
    textSizeContainer.appendChild(decreaseFontButton);

    controlsContainer.appendChild(voiceButton);
    controlsContainer.appendChild(readingButton);
    controlsContainer.appendChild(contrastButton);
    controlsContainer.appendChild(textSizeContainer);

    document.body.appendChild(controlsContainer);
  }

  /**
   * Add haptic feedback if supported
   */
  addHapticFeedback(intensity: 'light' | 'medium' | 'heavy' = 'light'): void {
    if (!this.config.enableHapticFeedback) return;

    if ('vibrate' in navigator) {
      const patterns = {
        light: [10],
        medium: [20],
        heavy: [30]
      };
      navigator.vibrate(patterns[intensity]);
    }

    // For iOS devices with haptic feedback API
    if ('ontouchstart' in window) {
      const tapticEngine = (window as any).TapticEngine;
      if (tapticEngine) {
        const types = {
          light: 'impact:light',
          medium: 'impact:medium',
          heavy: 'impact:heavy'
        };
        tapticEngine.impact({ style: types[intensity] });
      }
    }
  }

  /**
   * Create touch-optimized form field
   */
  enhanceFormField(field: HTMLElement): void {
    if (!this.isFormField(field)) return;

    // Increase touch target size
    field.style.minHeight = `${this.config.minTouchSize}px`;
    field.style.minWidth = `${this.config.minTouchSize}px`;
    field.style.padding = '12px';

    // Add touch feedback
    this.addTouchFeedback(field);

    // Add gesture support
    this.registerGesture({
      type: 'double-tap',
      element: field,
      callback: () => {
        if (field instanceof HTMLInputElement) {
          field.select();
          screenReader.announce('Text selected');
        }
      }
    });

    // Add voice input support for text fields
    if (field instanceof HTMLInputElement && field.type === 'text') {
      this.addVoiceInputSupport(field);
    }
  }

  /**
   * Create swipeable card interface
   */
  createSwipeableCards(container: HTMLElement, cards: HTMLElement[]): void {
    container.style.overflow = 'hidden';
    container.style.position = 'relative';

    cards.forEach((card, index) => {
      card.style.position = 'absolute';
      card.style.width = '100%';
      card.style.transform = `translateX(${index * 100}%)`;
      card.style.transition = 'transform 0.3s ease';

      // Add swipe gestures
      this.registerGesture({
        type: 'swipe-left',
        element: card,
        callback: () => this.navigateCards(container, cards, 'next')
      });

      this.registerGesture({
        type: 'swipe-right',
        element: card,
        callback: () => this.navigateCards(container, cards, 'previous')
      });
    });

    // Add navigation dots
    this.createSwipeIndicators(container, cards.length);
  }

  /**
   * Enhanced drag and drop for touch
   */
  enhanceDragAndDrop(element: HTMLElement): void {
    let isDragging = false;
    let dragStartPosition = { x: 0, y: 0 };
    let elementStartPosition = { x: 0, y: 0 };

    const handleTouchStart = (e: TouchEvent) => {
      if (!this.config.enabled) return;

      isDragging = true;
      const touch = e.touches[0];
      dragStartPosition = { x: touch.clientX, y: touch.clientY };
      
      const rect = element.getBoundingClientRect();
      elementStartPosition = { x: rect.left, y: rect.top };

      element.style.zIndex = '1000';
      element.style.transition = 'none';
      
      screenReader.announce('Dragging started');
      this.addHapticFeedback('light');
    };

    const handleTouchMove = (e: TouchEvent) => {
      if (!isDragging) return;
      e.preventDefault();

      const touch = e.touches[0];
      const deltaX = touch.clientX - dragStartPosition.x;
      const deltaY = touch.clientY - dragStartPosition.y;

      element.style.transform = `translate(${deltaX}px, ${deltaY}px)`;
    };

    const handleTouchEnd = (e: TouchEvent) => {
      if (!isDragging) return;

      isDragging = false;
      element.style.transition = 'transform 0.3s ease';
      element.style.zIndex = '';

      // Check for drop target
      const dropTarget = this.findDropTarget(e.changedTouches[0]);
      if (dropTarget) {
        screenReader.announce(`Dropped on ${dropTarget.getAttribute('aria-label') || 'target'}`);
        this.addHapticFeedback('medium');
      } else {
        // Snap back to original position
        element.style.transform = '';
        screenReader.announce('Dragging cancelled');
      }
    };

    element.addEventListener('touchstart', handleTouchStart, { passive: false });
    element.addEventListener('touchmove', handleTouchMove, { passive: false });
    element.addEventListener('touchend', handleTouchEnd, { passive: false });
  }

  private getDefaultConfig(): MobileTouchConfig {
    return {
      enabled: true,
      minTouchSize: 44, // WCAG AA minimum
      doubleTapDelay: 300,
      longPressDelay: 500,
      swipeThreshold: 50,
      enableHapticFeedback: true,
      announceTouch: true,
      enhanceScrolling: true
    };
  }

  private setupGlobalTouchHandlers(): void {
    if (!this.isTouchDevice()) return;

    document.addEventListener('touchstart', this.handleTouchStart.bind(this), { passive: false });
    document.addEventListener('touchmove', this.handleTouchMove.bind(this), { passive: false });
    document.addEventListener('touchend', this.handleTouchEnd.bind(this), { passive: false });
  }

  private handleTouchStart(e: TouchEvent): void {
    if (!this.config.enabled) return;

    this.touchStartTime = Date.now();
    const touch = e.touches[0];
    this.touchStartPosition = { x: touch.clientX, y: touch.clientY };

    const target = e.target as HTMLElement;
    if (this.config.announceTouch && target) {
      const label = target.getAttribute('aria-label') || 
                   target.getAttribute('title') ||
                   target.textContent?.trim();
      
      if (label) {
        screenReader.announce(`Touching ${label}`);
      }
    }
  }

  private handleTouchMove(e: TouchEvent): void {
    // Handle custom gestures during move
  }

  private handleTouchEnd(e: TouchEvent): void {
    if (!this.config.enabled) return;

    const target = e.target as HTMLElement;
    const touchDuration = Date.now() - this.touchStartTime;
    const touch = e.changedTouches[0];
    const deltaX = touch.clientX - this.touchStartPosition.x;
    const deltaY = touch.clientY - this.touchStartPosition.y;
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

    // Detect gestures
    if (distance < 10) { // Tap
      if (touchDuration > this.config.longPressDelay) {
        this.handleLongPress(target, e);
      } else {
        this.handleTap(target, e);
      }
    } else if (distance > this.config.swipeThreshold) {
      this.handleSwipe(target, deltaX, deltaY, e);
    }
  }

  private handleTap(element: HTMLElement, e: TouchEvent): void {
    const now = Date.now();
    const isDoubleTap = now - this.lastTapTime < this.config.doubleTapDelay && 
                       element === this.lastTapElement;

    if (isDoubleTap) {
      this.handleDoubleTap(element, e);
    } else {
      this.executeTapGestures(element, e);
    }

    this.lastTapTime = now;
    this.lastTapElement = element;
    this.addHapticFeedback('light');
  }

  private handleDoubleTap(element: HTMLElement, e: TouchEvent): void {
    this.executeGestures(element, 'double-tap', e);
    this.addHapticFeedback('medium');
  }

  private handleLongPress(element: HTMLElement, e: TouchEvent): void {
    this.executeGestures(element, 'long-press', e);
    this.addHapticFeedback('heavy');
  }

  private handleSwipe(element: HTMLElement, deltaX: number, deltaY: number, e: TouchEvent): void {
    let direction: TouchGesture['type'];

    if (Math.abs(deltaX) > Math.abs(deltaY)) {
      direction = deltaX > 0 ? 'swipe-right' : 'swipe-left';
    } else {
      direction = deltaY > 0 ? 'swipe-down' : 'swipe-up';
    }

    this.executeGestures(element, direction, e);
    this.addHapticFeedback('medium');
  }

  private executeGestures(element: HTMLElement, type: TouchGesture['type'], e: TouchEvent): void {
    const gestures = this.gestures.get(element) || [];
    const matchingGestures = gestures.filter(g => g.type === type);

    matchingGestures.forEach(gesture => {
      if (gesture.options?.preventDefault) {
        e.preventDefault();
      }
      gesture.callback(e);
    });
  }

  private executeTapGestures(element: HTMLElement, e: TouchEvent): void {
    this.executeGestures(element, 'tap', e);
  }

  private enhanceExistingElements(): void {
    // Enhance all interactive elements
    const interactiveElements = document.querySelectorAll(
      'button, input, select, textarea, a, [tabindex], [role="button"], [role="link"]'
    );

    interactiveElements.forEach(element => {
      this.enhanceElement(element as HTMLElement);
    });

    // Watch for new elements
    const observer = new MutationObserver(mutations => {
      mutations.forEach(mutation => {
        mutation.addedNodes.forEach(node => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            const element = node as HTMLElement;
            if (this.isInteractiveElement(element)) {
              this.enhanceElement(element);
            }
          }
        });
      });
    });

    observer.observe(document.body, { childList: true, subtree: true });
  }

  private ensureMinimumTouchSize(element: HTMLElement): void {
    const rect = element.getBoundingClientRect();
    
    if (rect.width < this.config.minTouchSize || rect.height < this.config.minTouchSize) {
      element.style.minWidth = `${this.config.minTouchSize}px`;
      element.style.minHeight = `${this.config.minTouchSize}px`;
      
      // Add padding if needed
      if (rect.width < this.config.minTouchSize) {
        const paddingX = (this.config.minTouchSize - rect.width) / 2;
        element.style.paddingLeft = `${paddingX}px`;
        element.style.paddingRight = `${paddingX}px`;
      }
      
      if (rect.height < this.config.minTouchSize) {
        const paddingY = (this.config.minTouchSize - rect.height) / 2;
        element.style.paddingTop = `${paddingY}px`;
        element.style.paddingBottom = `${paddingY}px`;
      }
    }
  }

  private addTouchFeedback(element: HTMLElement): void {
    element.style.transition = 'all 0.15s ease';

    const handleTouchStart = () => {
      element.style.transform = 'scale(0.98)';
      element.style.opacity = '0.8';
    };

    const handleTouchEnd = () => {
      element.style.transform = '';
      element.style.opacity = '';
    };

    element.addEventListener('touchstart', handleTouchStart, { passive: true });
    element.addEventListener('touchend', handleTouchEnd, { passive: true });
    element.addEventListener('touchcancel', handleTouchEnd, { passive: true });
  }

  private addInteractiveGestures(element: HTMLElement): void {
    // Add double-tap to activate for non-button elements
    if (!element.matches('button, input, select, textarea')) {
      this.registerGesture({
        type: 'double-tap',
        element,
        callback: () => {
          element.click();
          screenReader.announce('Activated');
        }
      });
    }

    // Add long-press for context menu
    this.registerGesture({
      type: 'long-press',
      element,
      callback: (e) => {
        e.preventDefault();
        this.showContextMenu(element, e as TouchEvent);
      }
    });
  }

  private enhanceScrolling(element: HTMLElement): void {
    if (!this.config.enhanceScrolling) return;

    (element.style as any).webkitOverflowScrolling = 'touch';
    element.style.overscrollBehavior = 'contain';

    // Add scroll indicators
    const scrollIndicator = document.createElement('div');
    scrollIndicator.className = 'scroll-indicator';
    scrollIndicator.style.cssText = `
      position: absolute;
      right: 2px;
      top: 0;
      width: 4px;
      background: rgba(0,0,0,0.3);
      border-radius: 2px;
      opacity: 0;
      transition: opacity 0.3s ease;
      pointer-events: none;
    `;
    
    element.style.position = 'relative';
    element.appendChild(scrollIndicator);

    let scrollTimeout: number;
    element.addEventListener('scroll', () => {
      scrollIndicator.style.opacity = '1';
      
      clearTimeout(scrollTimeout);
      scrollTimeout = window.setTimeout(() => {
        scrollIndicator.style.opacity = '0';
      }, 1000);

      // Update indicator position
      const scrollPercentage = element.scrollTop / (element.scrollHeight - element.clientHeight);
      const indicatorTop = scrollPercentage * (element.clientHeight - 20);
      scrollIndicator.style.top = `${indicatorTop}px`;
      scrollIndicator.style.height = '20px';
    });
  }

  private isInteractiveElement(element: HTMLElement): boolean {
    return element.matches('button, input, select, textarea, a, [tabindex], [role="button"], [role="link"]');
  }

  private isScrollableElement(element: HTMLElement): boolean {
    const computed = window.getComputedStyle(element);
    return computed.overflow === 'auto' || computed.overflow === 'scroll' ||
           computed.overflowY === 'auto' || computed.overflowY === 'scroll';
  }

  private isFormField(element: HTMLElement): boolean {
    return element.matches('input, select, textarea');
  }

  private createControlButton(text: string, ariaLabel: string, onClick: () => void): HTMLElement {
    const button = document.createElement('button');
    button.textContent = text;
    button.setAttribute('aria-label', ariaLabel);
    button.className = 'w-12 h-12 bg-blue-600 text-white rounded-full shadow-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500';
    button.addEventListener('click', onClick);
    
    this.enhanceElement(button);
    return button;
  }

  private toggleReadingMode(): void {
    const body = document.body;
    const isReadingMode = body.classList.contains('reading-mode');
    
    if (isReadingMode) {
      body.classList.remove('reading-mode');
      screenReader.announce('Reading mode disabled');
    } else {
      body.classList.add('reading-mode');
      this.applyReadingModeStyles();
      screenReader.announce('Reading mode enabled');
    }
  }

  private applyReadingModeStyles(): void {
    const style = document.createElement('style');
    style.textContent = `
      body.reading-mode {
        font-size: 1.2em !important;
        line-height: 1.6 !important;
        letter-spacing: 0.1em !important;
      }
      
      body.reading-mode * {
        font-family: serif !important;
      }
      
      body.reading-mode p {
        margin-bottom: 1em !important;
      }
    `;
    document.head.appendChild(style);
  }

  private adjustTextSize(multiplier: number): void {
    const rootElement = document.documentElement;
    const currentSize = parseFloat(getComputedStyle(rootElement).fontSize);
    const newSize = currentSize * multiplier;
    
    // Limit size between 12px and 24px
    const clampedSize = Math.max(12, Math.min(24, newSize));
    rootElement.style.fontSize = `${clampedSize}px`;
    
    screenReader.announce(`Text size adjusted to ${Math.round(clampedSize)}px`);
  }

  private navigateCards(container: HTMLElement, cards: HTMLElement[], direction: 'next' | 'previous'): void {
    const currentIndex = parseInt(container.getAttribute('data-current-card') || '0');
    let newIndex = direction === 'next' ? currentIndex + 1 : currentIndex - 1;
    
    newIndex = Math.max(0, Math.min(cards.length - 1, newIndex));
    
    cards.forEach((card, index) => {
      card.style.transform = `translateX(${(index - newIndex) * 100}%)`;
    });
    
    container.setAttribute('data-current-card', newIndex.toString());
    screenReader.announce(`Card ${newIndex + 1} of ${cards.length}`);
  }

  private createSwipeIndicators(container: HTMLElement, count: number): void {
    const indicators = document.createElement('div');
    indicators.className = 'flex justify-center gap-2 mt-4';
    
    for (let i = 0; i < count; i++) {
      const dot = document.createElement('div');
      dot.className = `w-2 h-2 rounded-full ${i === 0 ? 'bg-blue-600' : 'bg-gray-300'}`;
      indicators.appendChild(dot);
    }
    
    container.appendChild(indicators);
  }

  private addVoiceInputSupport(field: HTMLInputElement): void {
    if (!((window as any).webkitSpeechRecognition || (window as any).SpeechRecognition)) {
      return;
    }

    const voiceButton = document.createElement('button');
    voiceButton.textContent = '🎤';
    voiceButton.setAttribute('aria-label', 'Voice input');
    voiceButton.className = 'absolute right-2 top-1/2 transform -translate-y-1/2 w-8 h-8 text-gray-500 hover:text-blue-600';
    
    field.style.paddingRight = '40px';
    field.parentElement?.style.setProperty('position', 'relative');
    field.parentElement?.appendChild(voiceButton);

    voiceButton.addEventListener('click', () => {
      this.startVoiceInput(field);
    });
  }

  private startVoiceInput(field: HTMLInputElement): void {
    const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;
    const recognition = new SpeechRecognition();
    
    recognition.lang = 'en-US';
    recognition.continuous = false;
    recognition.interimResults = false;

    recognition.onstart = () => {
      screenReader.announce('Voice input started. Speak now.');
      this.addHapticFeedback('light');
    };

    recognition.onresult = (event) => {
      const transcript = event.results[0][0].transcript;
      field.value = transcript;
      field.dispatchEvent(new Event('input', { bubbles: true }));
      screenReader.announce(`Entered: ${transcript}`);
    };

    recognition.onerror = () => {
      screenReader.announce('Voice input error. Please try again.');
    };

    recognition.start();
  }

  private showContextMenu(element: HTMLElement, e: TouchEvent): void {
    const menu = document.createElement('div');
    menu.className = 'fixed bg-white shadow-lg rounded-lg border z-50 min-w-32';
    menu.style.left = `${e.changedTouches[0].clientX}px`;
    menu.style.top = `${e.changedTouches[0].clientY}px`;
    
    const options = [
      { label: 'Copy', action: () => this.copyElement(element) },
      { label: 'Share', action: () => this.shareElement(element) },
      { label: 'Read aloud', action: () => this.readElementAloud(element) }
    ];

    options.forEach(option => {
      const item = document.createElement('button');
      item.textContent = option.label;
      item.className = 'block w-full text-left px-4 py-2 hover:bg-gray-100';
      item.addEventListener('click', () => {
        option.action();
        document.body.removeChild(menu);
      });
      menu.appendChild(item);
    });

    document.body.appendChild(menu);

    // Remove menu on outside touch
    const removeMenu = () => {
      if (document.body.contains(menu)) {
        document.body.removeChild(menu);
      }
      document.removeEventListener('touchstart', removeMenu);
    };

    setTimeout(() => {
      document.addEventListener('touchstart', removeMenu);
    }, 100);
  }

  private copyElement(element: HTMLElement): void {
    const text = element.textContent || element.getAttribute('aria-label') || '';
    navigator.clipboard.writeText(text).then(() => {
      screenReader.announce('Copied to clipboard');
    });
  }

  private shareElement(element: HTMLElement): void {
    if ('share' in navigator) {
      const text = element.textContent || '';
      navigator.share({ text });
    }
  }

  private readElementAloud(element: HTMLElement): void {
    const text = element.textContent || element.getAttribute('aria-label') || '';
    if ('speechSynthesis' in window) {
      const utterance = new SpeechSynthesisUtterance(text);
      speechSynthesis.speak(utterance);
    }
  }

  private findDropTarget(touch: Touch): HTMLElement | null {
    const element = document.elementFromPoint(touch.clientX, touch.clientY) as HTMLElement;
    return element?.closest('[data-drop-target]') || null;
  }

  private applyConfiguration(): void {
    if (this.config.enabled) {
      document.body.classList.add('touch-accessibility-enabled');
    } else {
      document.body.classList.remove('touch-accessibility-enabled');
    }
  }

  private loadConfig(): void {
    try {
      const saved = localStorage.getItem('mobile-touch-accessibility-config');
      if (saved) {
        this.config = { ...this.config, ...JSON.parse(saved) };
      }
    } catch (error) {
      console.warn('Failed to load mobile touch accessibility config:', error);
    }
  }

  private saveConfig(): void {
    try {
      localStorage.setItem('mobile-touch-accessibility-config', JSON.stringify(this.config));
    } catch (error) {
      console.warn('Failed to save mobile touch accessibility config:', error);
    }
  }
}

// Export singleton instance
export const mobileTouchAccessibility = MobileTouchAccessibilityManager.getInstance();

/**
 * React hook for mobile touch accessibility
 */
export function useMobileTouchAccessibility() {
  return {
    isTouchDevice: mobileTouchAccessibility.isTouchDevice.bind(mobileTouchAccessibility),
    getConfig: mobileTouchAccessibility.getConfig.bind(mobileTouchAccessibility),
    updateConfig: mobileTouchAccessibility.updateConfig.bind(mobileTouchAccessibility),
    enable: mobileTouchAccessibility.enable.bind(mobileTouchAccessibility),
    disable: mobileTouchAccessibility.disable.bind(mobileTouchAccessibility),
    enhanceElement: mobileTouchAccessibility.enhanceElement.bind(mobileTouchAccessibility),
    registerGesture: mobileTouchAccessibility.registerGesture.bind(mobileTouchAccessibility),
    removeGesture: mobileTouchAccessibility.removeGesture.bind(mobileTouchAccessibility),
    addHapticFeedback: mobileTouchAccessibility.addHapticFeedback.bind(mobileTouchAccessibility),
    enhanceFormField: mobileTouchAccessibility.enhanceFormField.bind(mobileTouchAccessibility),
    createTouchControls: mobileTouchAccessibility.createTouchControls.bind(mobileTouchAccessibility),
    createSwipeableCards: mobileTouchAccessibility.createSwipeableCards.bind(mobileTouchAccessibility),
    enhanceDragAndDrop: mobileTouchAccessibility.enhanceDragAndDrop.bind(mobileTouchAccessibility)
  };
}