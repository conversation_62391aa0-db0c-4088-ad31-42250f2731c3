/**
 * Application Constants
 * Centralized location for all application constants
 */

// API Configuration
export const API_CONFIG = {
  BASE_URL: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
  TIMEOUT: 30000,
  RETRY_ATTEMPTS: 3,
} as const;

// Calculator Configuration
export const CALCULATOR_CONFIG = {
  MIN_AREA: 100,
  MAX_AREA: 50000,
  MIN_FLOORS: 0,
  MAX_FLOORS: 10,
  DEFAULT_QUALITY: 'smart',
  DEFAULT_LOCATION: 'bangalore',
} as const;

// UI Configuration
export const UI_CONFIG = {
  MOBILE_BREAKPOINT: 768,
  TABLET_BREAKPOINT: 1024,
  DESKTOP_BREAKPOINT: 1280,
  ANIMATION_DURATION: 300,
  DEBOUNCE_DELAY: 500,
} as const;

// Quality Tiers
export const QUALITY_TIERS = ['smart', 'premium', 'luxury'] as const;

// Supported Locations
export const SUPPORTED_LOCATIONS = [
  'bangalore', 'mumbai', 'delhi', 'hyderabad', 'pune', 
  'chennai', 'kolkata', 'ahmedabad', 'jaipur'
] as const;

// Error Messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network error. Please check your connection.',
  VALIDATION_ERROR: 'Please check your input values.',
  SERVER_ERROR: 'Server error. Please try again later.',
  CALCULATION_ERROR: 'Error calculating costs. Please try again.',
} as const;

// Success Messages
export const SUCCESS_MESSAGES = {
  CALCULATION_COMPLETE: 'Calculation completed successfully!',
  PROJECT_SAVED: 'Project saved successfully!',
  EXPORT_COMPLETE: 'Export completed successfully!',
} as const;
