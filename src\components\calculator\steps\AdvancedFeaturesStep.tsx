/**
 * Advanced Features Step Component
 * Final step for additional features and customizations
 */

import React, { useEffect, useState } from 'react';
import { motion, type Variants } from 'framer-motion';
import { 
  Zap, 
  Droplets, 
  Car, 
  Trees, 
  Shield, 
  Wifi, 
  Sun, 
  Waves,
  ArrowUp,
  Settings,
  Leaf,
  Home
} from 'lucide-react';
import { EnhancedCard } from '@/components/ui/enhanced-card';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { cn } from '@/lib/utils';
import { 
  StepComponentProps, 
  StepValidation,
  VALIDATION_LIMITS
} from '../types/wizard';

// Animation variants
const containerVariants: Variants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.4,
      staggerChildren: 0.1,
    },
  },
  exit: {
    opacity: 0,
    y: -20,
    transition: { duration: 0.3 },
  },
};

const cardVariants: Variants = {
  hidden: { opacity: 0, scale: 0.95 },
  visible: { 
    opacity: 1, 
    scale: 1,
    transition: { duration: 0.3 }
  },
};

const fieldVariants: Variants = {
  hidden: { opacity: 0, x: -20 },
  visible: { opacity: 1, x: 0 },
};

const featureCardVariants: Variants = {
  hidden: { opacity: 0, y: 10 },
  visible: { opacity: 1, y: 0 },
  hover: { 
    scale: 1.02, 
    y: -2,
    transition: { duration: 0.2 }
  },
};

export function AdvancedFeaturesStep({
  data,
  updateData,
  errors,
  onValidation,
  isActive,
}: StepComponentProps) {
  const [localErrors, setLocalErrors] = useState<Record<string, string>>({});

  // Validation function
  const validateStep = (): StepValidation => {
    const stepErrors: Record<string, string> = {};
    
    // Parking spaces validation
    const parkingValue = parseInt(data.parkingSpaces || '');
    if (data.parkingSpaces && (isNaN(parkingValue) || parkingValue < 0)) {
      stepErrors.parkingSpaces = 'Please enter a valid number of parking spaces';
    } else if (parkingValue > VALIDATION_LIMITS.parkingSpaces.max) {
      stepErrors.parkingSpaces = `Maximum ${VALIDATION_LIMITS.parkingSpaces.max} parking spaces`;
    }

    setLocalErrors(stepErrors);
    
    const validation: StepValidation = {
      isValid: Object.keys(stepErrors).length === 0,
      errors: stepErrors,
    };

    if (onValidation) {
      onValidation(validation);
    }

    return validation;
  };

  // Trigger validation when data changes
  useEffect(() => {
    if (isActive) {
      validateStep();
    }
  }, [data, isActive]);

  // Helper function to handle input changes
  const handleInputChange = (field: string, value: string | boolean) => {
    updateData({ [field]: value });
    
    // Clear error when user starts typing
    if (localErrors[field]) {
      setLocalErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  // Feature categories
  const infrastructureFeatures = [
    {
      key: 'garden',
      icon: Trees,
      title: 'Garden & Landscaping',
      description: 'Beautiful outdoor spaces with plants and lawn',
      cost: '₹2-5L',
    },
    {
      key: 'swimmingPool',
      icon: Waves,
      title: 'Swimming Pool',
      description: 'Private swimming pool with filtration system',
      cost: '₹8-15L',
    },
    {
      key: 'elevator',
      icon: ArrowUp,
      title: 'Elevator/Lift',
      description: 'Passenger elevator for multi-story buildings',
      cost: '₹12-20L',
    },
  ];

  const sustainabilityFeatures = [
    {
      key: 'solarPanels',
      icon: Sun,
      title: 'Solar Panel System',
      description: 'Renewable energy with grid-tie capability',
      cost: '₹3-8L',
    },
    {
      key: 'rainwaterHarvesting',
      icon: Droplets,
      title: 'Rainwater Harvesting',
      description: 'Sustainable water collection and storage',
      cost: '₹1-3L',
    },
  ];

  const technologyFeatures = [
    {
      key: 'homeAutomation',
      icon: Settings,
      title: 'Home Automation',
      description: 'Smart controls for lighting, climate, and security',
      cost: '₹2-6L',
    },
    {
      key: 'securitySystem',
      icon: Shield,
      title: 'Security System',
      description: 'CCTV, alarms, and access control',
      cost: '₹1-4L',
    },
    {
      key: 'internetCabling',
      icon: Wifi,
      title: 'Structured Cabling',
      description: 'Cat6 network cables throughout the house',
      cost: '₹50k-1L',
    },
  ];

  const utilityFeatures = [
    {
      key: 'generator',
      icon: Zap,
      title: 'Backup Generator',
      description: 'Automatic power backup for essential loads',
      cost: '₹2-5L',
    },
  ];

  // Calculate estimated additional cost
  const calculateAdditionalCost = () => {
    let additionalCost = 0;
    
    if (data.garden) additionalCost += 350000; // 3.5L average
    if (data.swimmingPool) additionalCost += 1150000; // 11.5L average
    if (data.solarPanels) additionalCost += 550000; // 5.5L average
    if (data.rainwaterHarvesting) additionalCost += 200000; // 2L average
    if (data.homeAutomation) additionalCost += 400000; // 4L average
    if (data.securitySystem) additionalCost += 250000; // 2.5L average
    if (data.internetCabling) additionalCost += 75000; // 75k average
    if (data.elevator) additionalCost += 1600000; // 16L average
    if (data.generator) additionalCost += 350000; // 3.5L average

    return additionalCost;
  };

  const additionalCost = calculateAdditionalCost();

  if (!isActive) return null;

  return (
    <motion.div
      className="space-y-8"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      exit="exit"
    >
      {/* Header */}
      <motion.div 
        className="text-center space-y-2"
        variants={fieldVariants}
      >
        <div className="flex items-center justify-center gap-2 text-primary-600">
          <Zap className="h-6 w-6" />
          <h2 className="text-2xl font-bold">Advanced Features</h2>
        </div>
        <p className="text-secondary-600 max-w-2xl mx-auto">
          Enhance your project with modern amenities, sustainability features, and smart technology. 
          These optional features can significantly improve comfort and value.
        </p>
      </motion.div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Parking & Basic Infrastructure */}
        <motion.div variants={cardVariants}>
          <EnhancedCard 
            variant="outlined" 
            size="lg"
            header={
              <div className="flex items-center gap-2">
                <Car className="h-5 w-5 text-primary-600" />
                <h3 className="text-lg font-semibold">Basic Infrastructure</h3>
              </div>
            }
          >
            <div className="space-y-6">
              {/* Parking Spaces */}
              <motion.div className="space-y-2" variants={fieldVariants}>
                <Label htmlFor="parkingSpaces" className="text-sm font-medium">
                  Parking Spaces
                </Label>
                <Select
                  value={data.parkingSpaces || '1'}
                  onValueChange={(value) => handleInputChange('parkingSpaces', value)}
                >
                  <SelectTrigger 
                    className={cn(
                      localErrors.parkingSpaces || errors.parkingSpaces 
                        ? 'border-red-500 focus:border-red-500' 
                        : ''
                    )}
                  >
                    <SelectValue placeholder="Select parking spaces" />
                  </SelectTrigger>
                  <SelectContent>
                    {[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((num) => (
                      <SelectItem key={num} value={num.toString()}>
                        {num === 0 ? 'No Parking' : `${num} Space${num > 1 ? 's' : ''}`}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {(localErrors.parkingSpaces || errors.parkingSpaces) && (
                  <p className="text-sm text-red-600">
                    {localErrors.parkingSpaces || errors.parkingSpaces}
                  </p>
                )}
                <p className="text-xs text-secondary-500">
                  Covered parking spaces within the compound
                </p>
              </motion.div>

              {/* Infrastructure Features */}
              <div className="space-y-4">
                {infrastructureFeatures.map((feature) => {
                  const Icon = feature.icon;
                  const isEnabled = Boolean(data[feature.key as keyof typeof data]);
                  
                  return (
                    <motion.div
                      key={feature.key}
                      className={cn(
                        'p-4 rounded-lg border transition-all duration-200 cursor-pointer',
                        isEnabled 
                          ? 'border-primary-500 bg-primary-50' 
                          : 'border-secondary-200 hover:border-primary-300'
                      )}
                      variants={featureCardVariants}
                      whileHover="hover"
                      onClick={() => handleInputChange(feature.key, !isEnabled)}
                    >
                      <div className="flex items-start gap-3">
                        <Icon className={cn(
                          'h-5 w-5 mt-1',
                          isEnabled ? 'text-primary-600' : 'text-secondary-400'
                        )} />
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <h4 className="font-medium">{feature.title}</h4>
                            <div className="flex items-center gap-2">
                              <span className="text-sm text-secondary-600">{feature.cost}</span>
                              <Switch 
                                checked={isEnabled}
                                onCheckedChange={(checked) => handleInputChange(feature.key, checked)}
                              />
                            </div>
                          </div>
                          <p className="text-sm text-secondary-600 mt-1">
                            {feature.description}
                          </p>
                        </div>
                      </div>
                    </motion.div>
                  );
                })}
              </div>
            </div>
          </EnhancedCard>
        </motion.div>

        {/* Sustainability Features */}
        <motion.div variants={cardVariants}>
          <EnhancedCard 
            variant="outlined" 
            size="lg"
            header={
              <div className="flex items-center gap-2">
                <Leaf className="h-5 w-5 text-green-600" />
                <h3 className="text-lg font-semibold">Sustainability</h3>
              </div>
            }
          >
            <div className="space-y-4">
              {sustainabilityFeatures.map((feature) => {
                const Icon = feature.icon;
                const isEnabled = Boolean(data[feature.key as keyof typeof data]);
                
                return (
                  <motion.div
                    key={feature.key}
                    className={cn(
                      'p-4 rounded-lg border transition-all duration-200 cursor-pointer',
                      isEnabled 
                        ? 'border-green-500 bg-green-50' 
                        : 'border-secondary-200 hover:border-green-300'
                    )}
                    variants={featureCardVariants}
                    whileHover="hover"
                    onClick={() => handleInputChange(feature.key, !isEnabled)}
                  >
                    <div className="flex items-start gap-3">
                      <Icon className={cn(
                        'h-5 w-5 mt-1',
                        isEnabled ? 'text-green-600' : 'text-secondary-400'
                      )} />
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <h4 className="font-medium">{feature.title}</h4>
                          <div className="flex items-center gap-2">
                            <span className="text-sm text-secondary-600">{feature.cost}</span>
                            <Switch 
                              checked={isEnabled}
                              onCheckedChange={(checked) => handleInputChange(feature.key, checked)}
                            />
                          </div>
                        </div>
                        <p className="text-sm text-secondary-600 mt-1">
                          {feature.description}
                        </p>
                      </div>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </EnhancedCard>
        </motion.div>

        {/* Technology Features */}
        <motion.div variants={cardVariants}>
          <EnhancedCard 
            variant="outlined" 
            size="lg"
            header={
              <div className="flex items-center gap-2">
                <Settings className="h-5 w-5 text-blue-600" />
                <h3 className="text-lg font-semibold">Smart Technology</h3>
              </div>
            }
          >
            <div className="space-y-4">
              {technologyFeatures.map((feature) => {
                const Icon = feature.icon;
                const isEnabled = Boolean(data[feature.key as keyof typeof data]);
                
                return (
                  <motion.div
                    key={feature.key}
                    className={cn(
                      'p-4 rounded-lg border transition-all duration-200 cursor-pointer',
                      isEnabled 
                        ? 'border-blue-500 bg-blue-50' 
                        : 'border-secondary-200 hover:border-blue-300'
                    )}
                    variants={featureCardVariants}
                    whileHover="hover"
                    onClick={() => handleInputChange(feature.key, !isEnabled)}
                  >
                    <div className="flex items-start gap-3">
                      <Icon className={cn(
                        'h-5 w-5 mt-1',
                        isEnabled ? 'text-blue-600' : 'text-secondary-400'
                      )} />
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <h4 className="font-medium">{feature.title}</h4>
                          <div className="flex items-center gap-2">
                            <span className="text-sm text-secondary-600">{feature.cost}</span>
                            <Switch 
                              checked={isEnabled}
                              onCheckedChange={(checked) => handleInputChange(feature.key, checked)}
                            />
                          </div>
                        </div>
                        <p className="text-sm text-secondary-600 mt-1">
                          {feature.description}
                        </p>
                      </div>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </EnhancedCard>
        </motion.div>

        {/* Utility Features */}
        <motion.div variants={cardVariants}>
          <EnhancedCard 
            variant="outlined" 
            size="lg"
            header={
              <div className="flex items-center gap-2">
                <Zap className="h-5 w-5 text-yellow-600" />
                <h3 className="text-lg font-semibold">Utilities</h3>
              </div>
            }
          >
            <div className="space-y-4">
              {utilityFeatures.map((feature) => {
                const Icon = feature.icon;
                const isEnabled = Boolean(data[feature.key as keyof typeof data]);
                
                return (
                  <motion.div
                    key={feature.key}
                    className={cn(
                      'p-4 rounded-lg border transition-all duration-200 cursor-pointer',
                      isEnabled 
                        ? 'border-yellow-500 bg-yellow-50' 
                        : 'border-secondary-200 hover:border-yellow-300'
                    )}
                    variants={featureCardVariants}
                    whileHover="hover"
                    onClick={() => handleInputChange(feature.key, !isEnabled)}
                  >
                    <div className="flex items-start gap-3">
                      <Icon className={cn(
                        'h-5 w-5 mt-1',
                        isEnabled ? 'text-yellow-600' : 'text-secondary-400'
                      )} />
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <h4 className="font-medium">{feature.title}</h4>
                          <div className="flex items-center gap-2">
                            <span className="text-sm text-secondary-600">{feature.cost}</span>
                            <Switch 
                              checked={isEnabled}
                              onCheckedChange={(checked) => handleInputChange(feature.key, checked)}
                            />
                          </div>
                        </div>
                        <p className="text-sm text-secondary-600 mt-1">
                          {feature.description}
                        </p>
                      </div>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </EnhancedCard>
        </motion.div>
      </div>

      {/* Features Summary */}
      {additionalCost > 0 && (
        <motion.div
          className="p-6 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg border border-green-200"
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.5 }}
        >
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-semibold text-green-900 mb-2">Selected Features Summary</h4>
              <p className="text-sm text-green-800">
                These additional features will enhance your project's value and functionality
              </p>
            </div>
            <div className="text-right">
              <p className="text-sm text-green-700">Additional Cost</p>
              <p className="text-2xl font-bold text-green-900">
                ₹{(additionalCost / 100000).toFixed(1)}L
              </p>
            </div>
          </div>
        </motion.div>
      )}
    </motion.div>
  );
}

export default AdvancedFeaturesStep;