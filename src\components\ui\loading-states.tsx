/**
 * Loading States Components
 * Provides consistent loading indicators and empty states
 */

'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { useState, useEffect, useCallback, useRef } from 'react';
import { 
  Loader2, 
  Search, 
  FileText, 
  Calculator, 
  Home, 
  Building,
  AlertCircle
} from 'lucide-react';

import { Card, CardContent, CardHeader, CardTitle } from './card';
import { Button } from './button';
import { Skeleton } from './skeleton';
import { cn } from '@/lib/utils';
import { loadingSpinner, fadeIn } from '@/lib/animations';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  text?: string;
}

export function LoadingSpinner({ 
  size = 'md', 
  className, 
  text 
}: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8'
  };

  return (
    <motion.div
      className={cn('flex items-center gap-2 text-gray-600', className)}
      variants={fadeIn}
      initial="initial"
      animate="animate"
    >
      <motion.div {...loadingSpinner}>
        <Loader2 className={cn(sizeClasses[size], 'animate-spin')} />
      </motion.div>
      {text && <span className="text-sm">{text}</span>}
    </motion.div>
  );
}

interface LoadingCardProps {
  title?: string;
  description?: string;
  className?: string;
}

export function LoadingCard({ 
  title = 'Loading...', 
  description = 'Please wait while we process your request',
  className 
}: LoadingCardProps) {
  return (
    <motion.div
      variants={fadeIn}
      initial="initial"
      animate="animate"
    >
      <Card className={cn('text-center', className)}>
        <CardContent className="p-6">
          <LoadingSpinner size="lg" className="justify-center mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">{title}</h3>
          <p className="text-sm text-gray-600">{description}</p>
        </CardContent>
      </Card>
    </motion.div>
  );
}

interface EmptyStateProps {
  icon?: React.ReactNode;
  title: string;
  description: string;
  action?: {
    label: string;
    onClick: () => void;
    variant?: 'default' | 'outline' | 'ghost';
  };
  className?: string;
}

export function EmptyState({ 
  icon, 
  title, 
  description, 
  action, 
  className 
}: EmptyStateProps) {
  return (
    <motion.div
      variants={fadeIn}
      initial="initial"
      animate="animate"
      className={cn('text-center py-12', className)}
    >
      <div className="mx-auto mb-4 p-3 bg-gray-100 rounded-full w-fit">
        {icon || <FileText className="h-8 w-8 text-gray-400" />}
      </div>
      
      <h3 className="text-lg font-semibold text-gray-900 mb-2">{title}</h3>
      <p className="text-sm text-gray-600 mb-6 max-w-md mx-auto">{description}</p>
      
      {action && (
        <Button
          onClick={action.onClick}
          variant={action.variant || 'default'}
          size="sm"
        >
          {action.label}
        </Button>
      )}
    </motion.div>
  );
}

// Specific empty states for different sections
export function EmptyCalculationState({ onStartCalculation }: { onStartCalculation: () => void }) {
  return (
    <EmptyState
      icon={<Calculator className="h-8 w-8 text-gray-400" />}
      title="No calculations yet"
      description="Start by creating your first construction cost calculation to see results here."
      action={{
        label: 'Start Calculation',
        onClick: onStartCalculation
      }}
    />
  );
}

export function EmptyProjectsState({ onCreateProject }: { onCreateProject: () => void }) {
  return (
    <EmptyState
      icon={<Building className="h-8 w-8 text-gray-400" />}
      title="No projects found"
      description="Create your first project to start tracking construction costs and progress."
      action={{
        label: 'Create Project',
        onClick: onCreateProject
      }}
    />
  );
}

export function EmptySearchState({ onClearSearch }: { onClearSearch: () => void }) {
  return (
    <EmptyState
      icon={<Search className="h-8 w-8 text-gray-400" />}
      title="No results found"
      description="We couldn't find anything matching your search. Try different keywords or clear your search."
      action={{
        label: 'Clear Search',
        onClick: onClearSearch,
        variant: 'outline'
      }}
    />
  );
}

// Loading overlay for full-screen loading
interface LoadingOverlayProps {
  show: boolean;
  text?: string;
  className?: string;
}

export function LoadingOverlay({ show, text, className }: LoadingOverlayProps) {
  if (!show) return null;

  return (
    <motion.div
      className={cn(
        'fixed inset-0 bg-black/50 flex items-center justify-center z-50',
        className
      )}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
    >
      <Card className="bg-white">
        <CardContent className="p-6">
          <LoadingSpinner size="lg" text={text || 'Loading...'} className="justify-center" />
        </CardContent>
      </Card>
    </motion.div>
  );
}

// Loading skeleton for specific components
export function CalculatorFormSkeleton() {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {Array.from({ length: 4 }, (_, i) => (
          <div key={i} className="space-y-2">
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-10 w-full" />
          </div>
        ))}
      </div>
      
      <div className="space-y-2">
        <Skeleton className="h-4 w-32" />
        <Skeleton className="h-40 w-full" />
      </div>
      
      <div className="flex justify-between">
        <Skeleton className="h-10 w-24" />
        <Skeleton className="h-10 w-32" />
      </div>
    </div>
  );
}

export function ResultsLoadingSkeleton() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <Skeleton className="h-8 w-48" />
        <div className="flex gap-2">
          <Skeleton className="h-8 w-20" />
          <Skeleton className="h-8 w-32" />
        </div>
      </div>
      
      {/* Hero card */}
      <Card>
        <CardContent className="p-6">
          <div className="text-center space-y-4">
            <Skeleton className="h-4 w-32 mx-auto" />
            <Skeleton className="h-12 w-48 mx-auto" />
            <div className="grid grid-cols-2 gap-4 pt-4">
              <div className="text-center">
                <Skeleton className="h-3 w-16 mx-auto" />
                <Skeleton className="h-6 w-20 mx-auto mt-1" />
              </div>
              <div className="text-center">
                <Skeleton className="h-3 w-16 mx-auto" />
                <Skeleton className="h-6 w-20 mx-auto mt-1" />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
      
      {/* Breakdown cards */}
      <div className="space-y-4">
        {Array.from({ length: 5 }, (_, i) => (
          <Card key={i}>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Skeleton className="h-8 w-8 rounded-full" />
                  <div>
                    <Skeleton className="h-4 w-32" />
                    <Skeleton className="h-3 w-24 mt-1" />
                  </div>
                </div>
                <div className="text-right">
                  <Skeleton className="h-6 w-20" />
                  <Skeleton className="h-3 w-16 mt-1" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}

// Enhanced error states with retry mechanisms
interface ErrorStateProps {
  title: string;
  description: string;
  error?: Error | string;
  onRetry?: () => void;
  onReset?: () => void;
  onReport?: (error: Error | string) => void;
  className?: string;
  showDetails?: boolean;
  retryCount?: number;
  maxRetries?: number;
  isRetrying?: boolean;
}

export function ErrorState({ 
  title, 
  description, 
  error,
  onRetry, 
  onReset,
  onReport,
  className,
  showDetails = false,
  retryCount = 0,
  maxRetries = 3,
  isRetrying = false
}: ErrorStateProps) {
  const [showErrorDetails, setShowErrorDetails] = useState(false);
  const [countdown, setCountdown] = useState(0);

  // Auto-retry countdown
  useEffect(() => {
    if (isRetrying && countdown > 0) {
      const timer = setTimeout(() => setCountdown(prev => prev - 1), 1000);
      return () => clearTimeout(timer);
    } else if (isRetrying && countdown === 0 && onRetry) {
      onRetry();
    }
  }, [isRetrying, countdown, onRetry]);

  const canRetry = retryCount < maxRetries;
  const errorMessage = error instanceof Error ? error.message : error;

  return (
    <motion.div
      variants={fadeIn}
      initial="initial"
      animate="animate"
      className={cn('text-center py-12', className)}
    >
      <motion.div 
        className="mx-auto mb-4 p-3 bg-red-100 rounded-full w-fit"
        animate={isRetrying ? { rotate: [0, 10, -10, 0] } : {}}
        transition={{ repeat: isRetrying ? Infinity : 0, duration: 0.5 }}
      >
        <AlertCircle className="h-8 w-8 text-red-600" />
      </motion.div>
      
      <h3 className="text-lg font-semibold text-gray-900 mb-2">{title}</h3>
      <p className="text-sm text-gray-600 mb-4 max-w-md mx-auto">{description}</p>
      
      {retryCount > 0 && (
        <p className="text-xs text-gray-500 mb-4">
          Retry attempt {retryCount} of {maxRetries}
        </p>
      )}

      {isRetrying && countdown > 0 && (
        <p className="text-sm text-blue-600 mb-4">
          Retrying in {countdown} seconds...
        </p>
      )}
      
      <div className="flex flex-col sm:flex-row justify-center gap-2 mb-4">
        {onRetry && canRetry && !isRetrying && (
          <Button onClick={onRetry} variant="default" size="sm">
            Try Again {retryCount > 0 && `(${maxRetries - retryCount} left)`}
          </Button>
        )}
        {onReset && (
          <Button onClick={onReset} variant="outline" size="sm">
            Reset
          </Button>
        )}
        {onReport && error && (
          <Button 
            onClick={() => onReport(error)} 
            variant="ghost" 
            size="sm"
          >
            Report Issue
          </Button>
        )}
      </div>

      {showDetails && errorMessage && (
        <div className="mt-4">
          <button
            onClick={() => setShowErrorDetails(!showErrorDetails)}
            className="text-xs text-gray-500 hover:text-gray-700 underline"
          >
            {showErrorDetails ? 'Hide' : 'Show'} Error Details
          </button>
          
          <AnimatePresence>
            {showErrorDetails && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="mt-2 p-3 bg-gray-100 rounded text-xs font-mono text-left overflow-auto"
              >
                {errorMessage}
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      )}
    </motion.div>
  );
}

// Network error state
interface NetworkErrorStateProps extends Omit<ErrorStateProps, 'title' | 'description'> {
  type?: 'offline' | 'timeout' | 'server' | 'connection';
}

export function NetworkErrorState({ 
  type = 'connection',
  ...props 
}: NetworkErrorStateProps) {
  const getErrorContent = () => {
    switch (type) {
      case 'offline':
        return {
          title: 'You\'re offline',
          description: 'Please check your internet connection and try again.',
          icon: <svg className="h-8 w-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 5.636l-12.728 12.728m0 0L12 12m-6.364 6.364L12 12m6.364-6.364L12 12" />
          </svg>
        };
      case 'timeout':
        return {
          title: 'Request timed out',
          description: 'The request took too long to complete. Please try again.',
          icon: <svg className="h-8 w-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        };
      case 'server':
        return {
          title: 'Server error',
          description: 'Something went wrong on our end. Please try again later.',
          icon: <svg className="h-8 w-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        };
      default:
        return {
          title: 'Connection failed',
          description: 'Unable to connect to the server. Please check your connection.',
          icon: <AlertCircle className="h-8 w-8 text-red-600" />
        };
    }
  };

  const { title, description, icon } = getErrorContent();

  return (
    <ErrorState
      title={title}
      description={description}
      {...props}
    />
  );
}

// Smart retry hook
interface UseSmartRetryOptions {
  maxRetries?: number;
  initialDelay?: number;
  backoffMultiplier?: number;
  maxDelay?: number;
  retryCondition?: (error: any) => boolean;
  onRetry?: (attempt: number) => void;
  onMaxRetriesReached?: () => void;
}

export function useSmartRetry<T, E = Error>({
  maxRetries = 3,
  initialDelay = 1000,
  backoffMultiplier = 2,
  maxDelay = 10000,
  retryCondition = () => true,
  onRetry,
  onMaxRetriesReached
}: UseSmartRetryOptions = {}) {
  const [retryCount, setRetryCount] = useState(0);
  const [isRetrying, setIsRetrying] = useState(false);
  const [error, setError] = useState<E | null>(null);
  const timeoutRef = useRef<NodeJS.Timeout>();

  const calculateDelay = useCallback((attempt: number) => {
    const delay = initialDelay * Math.pow(backoffMultiplier, attempt - 1);
    return Math.min(delay, maxDelay);
  }, [initialDelay, backoffMultiplier, maxDelay]);

  const executeWithRetry = useCallback(async (
    operation: () => Promise<T>
  ): Promise<T> => {
    try {
      const result = await operation();
      setError(null);
      setRetryCount(0);
      setIsRetrying(false);
      return result;
    } catch (err) {
      const error = err as E;
      setError(error);

      if (retryCount < maxRetries && retryCondition(error)) {
        setRetryCount(prev => prev + 1);
        setIsRetrying(true);
        onRetry?.(retryCount + 1);

        const delay = calculateDelay(retryCount + 1);
        
        return new Promise((resolve, reject) => {
          timeoutRef.current = setTimeout(async () => {
            try {
              const result = await executeWithRetry(operation);
              resolve(result);
            } catch (retryError) {
              reject(retryError);
            }
          }, delay);
        });
      } else {
        setIsRetrying(false);
        if (retryCount >= maxRetries) {
          onMaxRetriesReached?.();
        }
        throw error;
      }
    }
  }, [retryCount, maxRetries, retryCondition, onRetry, calculateDelay, onMaxRetriesReached]);

  const reset = useCallback(() => {
    setRetryCount(0);
    setIsRetrying(false);
    setError(null);
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
  }, []);

  const retry = useCallback(async (operation: () => Promise<T>) => {
    if (error && retryCount < maxRetries) {
      return executeWithRetry(operation);
    }
    throw new Error('Cannot retry: max retries reached or no error to retry');
  }, [error, retryCount, maxRetries, executeWithRetry]);

  return {
    executeWithRetry,
    retry,
    reset,
    retryCount,
    isRetrying,
    error,
    canRetry: retryCount < maxRetries,
    nextRetryDelay: retryCount < maxRetries ? calculateDelay(retryCount + 1) : 0
  };
}

// Success states
interface SuccessStateProps {
  title: string;
  description: string;
  onContinue?: () => void;
  onNew?: () => void;
  className?: string;
}

export function SuccessState({ 
  title, 
  description, 
  onContinue, 
  onNew, 
  className 
}: SuccessStateProps) {
  return (
    <motion.div
      variants={fadeIn}
      initial="initial"
      animate="animate"
      className={cn('text-center py-12', className)}
    >
      <div className="mx-auto mb-4 p-3 bg-green-100 rounded-full w-fit">
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.2, type: 'spring', stiffness: 200 }}
        >
          <svg className="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        </motion.div>
      </div>
      
      <h3 className="text-lg font-semibold text-gray-900 mb-2">{title}</h3>
      <p className="text-sm text-gray-600 mb-6 max-w-md mx-auto">{description}</p>
      
      <div className="flex justify-center gap-2">
        {onContinue && (
          <Button onClick={onContinue} variant="default" size="sm">
            Continue
          </Button>
        )}
        {onNew && (
          <Button onClick={onNew} variant="outline" size="sm">
            Start New
          </Button>
        )}
      </div>
    </motion.div>
  );
}