/**
 * Calculator Type Definitions for Clarity Engine
 * Based on Indian construction standards and practices
 */

export interface CalculationInput {
  plotArea?: number;
  builtUpArea: number;
  floors: number; // 0 = Ground only, 1 = G+1, etc.
  qualityTier: 'smart' | 'premium' | 'luxury';
  location: string;
  hasStilt?: boolean;
  parkingType?: 'open' | 'covered' | 'none';
  hasBasement?: boolean;
  specialFeatures?: SpecialFeature[];
}

export interface SpecialFeature {
  name: string;
  cost: number;
  description: string;
}

export interface CalculationResult {
  totalCost: number;
  costPerSqft: number;
  breakdown: CostBreakdown;
  materials: MaterialQuantity[];
  timeline: ConstructionPhase[];
  summary: ProjectSummary;
}

export interface CostBreakdown {
  structure: CategoryCost;
  finishing: CategoryCost;
  mep: CategoryCost; // Mechanical, Electrical, Plumbing
  external: CategoryCost;
  other: CategoryCost;
  total: number;
}

export interface CategoryCost {
  amount: number;
  percentage: number;
  subCategories: SubCategory[];
}

export interface SubCategory {
  name: string;
  amount: number;
  percentage?: number;
  description?: string;
}

export interface MaterialQuantity {
  category: string;
  name: string;
  quantity: number;
  unit: string;
  rate?: number;
  totalCost?: number;
  purpose: string;
  specifications?: string;
}

export interface ConstructionPhase {
  name: string;
  duration: number; // in weeks
  startAfter: number; // weeks from project start
  dependencies?: string[];
  description: string;
}

export interface ProjectSummary {
  totalBuiltUpArea: number; // including all floors
  carpetArea: number; // usable area
  constructionDuration: number; // in months
  totalCost: number;
  costPerSqft: number;
  qualityTier: 'smart' | 'premium' | 'luxury';
  location: string;
  estimateAccuracy: string; // e.g., "±10%"
}

// Regional and Quality Tier Constants
export interface QualityTierRates {
  smart: number;
  premium: number;
  luxury: number;
}

export interface RegionalMultiplier {
  [location: string]: number;
}

export interface MaterialConsumptionRate {
  material: string;
  unit: string;
  consumptionPerSqft: number;
  qualityVariation?: {
    smart: number;
    premium: number;
    luxury: number;
  };
}

// Cost calculation specific types
export interface CostCalculationParams {
  baseArea: number;
  totalArea: number;
  qualityMultiplier: number;
  locationMultiplier: number;
  floorMultiplier: number;
  specialAdditions: number;
}

// Error and validation types
export interface ValidationError {
  field: string;
  message: string;
  code: string;
}

export interface CalculationError {
  type: 'validation' | 'calculation' | 'data' | 'rate_limit' | 'server';
  message: string;
  code?: string;
  details?: unknown;
}

// API response types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: CalculationError;
  timestamp: string;
  requestId?: string;
  performance?: {
    validationTime: number;
    calculationTime: number;
    totalTime: number;
  };
}

export type CalculationApiResponse = ApiResponse<CalculationResult>;
