// INSTRUCTIONS FOR CLAUDE CODE:
// 1. This is a COMPREHENSIVE test suite that validates EVERYTHING from Days 1-9
// 2. Create file: tests/e2e/comprehensive-ui-validation.spec.ts
// 3. Run with: npx playwright test tests/e2e/comprehensive-ui-validation.spec.ts --headed
// 4. Generate detailed report: test-results/COMPREHENSIVE_UI_REPORT.md
// 5. Take screenshots of EVERY test for visual proof
// 6. DO NOT claim success unless ALL tests actually pass

import { test, expect, Page } from '@playwright/test';
import fs from 'fs';
import path from 'path';

// Test configuration
const SCREENSHOT_DIR = 'test-results/comprehensive-screenshots';
const VIDEO_DIR = 'test-results/videos';

// Create directories
if (!fs.existsSync(SCREENSHOT_DIR)) {
  fs.mkdirSync(SCREENSHOT_DIR, { recursive: true });
}

// Comprehensive test data
const testData = {
  validInputs: {
    plotSize: ['500', '1000', '2000', '5000', '10000'],
    floors: ['1', '2', '3', '4'],
    qualityTiers: ['smart', 'premium', 'luxury'],
    locations: ['delhi', 'mumbai', 'bangalore', 'chennai', 'hyderabad'],
    buildingTypes: ['residential', 'commercial'] // if implemented
  },
  invalidInputs: {
    plotSize: ['0', '-100', '100', '100000', 'abc', '!@#', ''],
    floors: ['0', '5', '10', '-1']
  },
  expectedElements: {
    formFields: ['plot-size', 'floors', 'quality', 'location', 'building-type'],
    buttons: ['calculate', 'reset', 'save', 'download-pdf'],
    results: ['total-cost', 'cost-breakdown', 'materials-list', 'timeline']
  }
};

// Helper functions
async function takeScreenshot(page: Page, name: string) {
  await page.screenshot({ 
    path: `${SCREENSHOT_DIR}/${name}.png`,
    fullPage: true 
  });
}

async function checkElementText(page: Page, selector: string, expectedText: string) {
  const element = page.locator(selector);
  await expect(element).toContainText(expectedText, { ignoreCase: true });
}

async function fillCalculatorForm(page: Page, data: any) {
  // Fill plot size
  await page.fill('[data-testid="plot-size-input"]', data.plotSize);
  
  // Select floors
  await page.click('[data-testid="floors-select"]');
  await page.locator(`text="${data.floors} Floor${data.floors !== '1' ? 's' : ''}"`).click();
  
  // Select quality tier
  await page.click('[data-testid="quality-select"]');
  const qualityMap: { [key: string]: string } = {
    'smart': 'Smart Choice (₹1,600-2,000/sqft)',
    'premium': 'Premium Selection (₹2,200-2,800/sqft)',
    'luxury': 'Luxury Collection (₹3,000-4,000/sqft)'
  };
  await page.locator(`text="${qualityMap[data.quality]}"`).click();
  
  // Select location
  await page.click('[data-testid="location-select"]');
  const locationMap: { [key: string]: string } = {
    'bangalore': 'Bangalore',
    'delhi': 'Delhi NCR',
    'mumbai': 'Mumbai',
    'chennai': 'Chennai',
    'hyderabad': 'Hyderabad'
  };
  await page.locator(`text="${locationMap[data.location] || data.location}"`).click();
}

// COMPREHENSIVE TEST SUITE
test.describe('COMPREHENSIVE UI VALIDATION - Days 1-9', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/calculator');
    // Wait for app to fully load
    await page.waitForLoadState('networkidle');
  });

  // SECTION 1: BASIC FUNCTIONALITY
  test.describe('Section 1: Basic Page Loading and Structure', () => {
    test('1.1 Application loads without errors', async ({ page }) => {
      // Check console for errors
      const consoleErrors: string[] = [];
      page.on('console', msg => {
        if (msg.type() === 'error') {
          consoleErrors.push(msg.text());
        }
      });
      
      await page.goto('/calculator');
      await page.waitForTimeout(2000);
      
      expect(consoleErrors).toHaveLength(0);
      await takeScreenshot(page, '01-app-loaded');
    });

    test('1.2 Correct page title and metadata', async ({ page }) => {
      await expect(page).toHaveTitle(/Clarity Engine|Construction Cost Calculator|Nirmaan/i);
      
      // Check calculator container is present
      await expect(page.getByTestId('calculator-container')).toBeVisible();
      
      await takeScreenshot(page, '02-page-metadata');
    });

    test('1.3 All navigation elements present', async ({ page }) => {
      // Check form container is present
      await expect(page.getByTestId('form-container')).toBeVisible();
      
      // Check form title
      await expect(page.getByTestId('form-title')).toBeVisible();
      
      await takeScreenshot(page, '03-navigation-elements');
    });
  });

  // SECTION 2: FORM ELEMENTS AND LABELS
  test.describe('Section 2: Form Elements Validation', () => {
    test('2.1 All form fields are present with correct labels', async ({ page }) => {
      await page.goto('/calculator');
      
      // Check plot size field
      const plotSizeLabel = page.locator('label:has-text("Plot Size")');
      await expect(plotSizeLabel).toBeVisible();
      
      const plotSizeInput = page.getByTestId('plot-size-input');
      await expect(plotSizeInput).toBeVisible();
      
      // Check floors dropdown
      const floorsLabel = page.locator('label:has-text("Number of Floors")');
      await expect(floorsLabel).toBeVisible();
      
      const floorsSelect = page.getByTestId('floors-select');
      await expect(floorsSelect).toBeVisible();
      
      // Check quality tier
      const qualityLabel = page.locator('label:has-text("Quality Tier")');
      await expect(qualityLabel).toBeVisible();
      
      // Check location
      const locationLabel = page.locator('label:has-text("Location")');
      await expect(locationLabel).toBeVisible();
      
      // Check building type
      const buildingTypeLabel = page.locator('label:has-text("Building Type")');
      await expect(buildingTypeLabel).toBeVisible();
      
      await takeScreenshot(page, '04-form-fields');
    });

    test('2.2 No duplicate field names or IDs', async ({ page }) => {
      await page.goto('/calculator');
      
      // Get all input IDs
      const inputIds = await page.locator('input[id], button[id]').evaluateAll(
        elements => elements.map(el => el.id).filter(id => id)
      );
      
      // Check for duplicates
      const duplicates = inputIds.filter((id, index) => inputIds.indexOf(id) !== index);
      expect(duplicates).toHaveLength(0);
      
      // Get all data-testid attributes
      const testIds = await page.locator('[data-testid]').evaluateAll(
        elements => elements.map(el => el.getAttribute('data-testid')).filter(id => id)
      );
      
      // Check for duplicate test IDs
      const duplicateTestIds = testIds.filter((id, index) => testIds.indexOf(id) !== index);
      expect(duplicateTestIds).toHaveLength(0);
      
      await takeScreenshot(page, '05-field-uniqueness');
    });

    test('2.3 Form field placeholders and helper text', async ({ page }) => {
      await page.goto('/calculator');
      
      // Check for helpful placeholders
      const plotInput = page.getByTestId('plot-size-input');
      const placeholder = await plotInput.getAttribute('placeholder');
      expect(placeholder).toBeTruthy();
      
      // Check for min/max attributes
      const min = await plotInput.getAttribute('min');
      const max = await plotInput.getAttribute('max');
      expect(min).toBeTruthy();
      expect(max).toBeTruthy();
      
      await takeScreenshot(page, '06-field-helpers');
    });
  });

  // SECTION 3: INPUT VALIDATION
  test.describe('Section 3: Input Validation and Error Handling', () => {
    test('3.1 Plot size validation - minimum value', async ({ page }) => {
      await page.goto('/calculator');
      
      const plotInput = page.getByTestId('plot-size-input');
      await plotInput.fill('100'); // Below minimum
      
      // Try to calculate
      await page.getByTestId('calculate-button').click();
      
      // Check for error message
      await expect(page.getByTestId('validation-error')).toBeVisible({ timeout: 5000 });
      await expect(page.getByText(/at least 500/i)).toBeVisible();
      
      await takeScreenshot(page, '07-min-validation');
    });

    test('3.2 Plot size validation - maximum value', async ({ page }) => {
      await page.goto('/calculator');
      
      const plotInput = page.getByTestId('plot-size-input');
      await plotInput.fill('100000'); // Above maximum (max is 50000)
      
      // Input should be constrained by max attribute
      const value = await plotInput.inputValue();
      expect(parseInt(value)).toBeLessThanOrEqual(50000);
      
      await takeScreenshot(page, '08-max-validation');
    });

    test('3.3 Invalid input handling', async ({ page }) => {
      await page.goto('/calculator');
      
      const plotInput = page.getByTestId('plot-size-input');
      
      // Try to input text
      await plotInput.fill('');
      await plotInput.type('abc');
      const value = await plotInput.inputValue();
      
      // Number input should not accept text
      expect(value === '' || !isNaN(Number(value))).toBeTruthy();
      
      await takeScreenshot(page, '09-invalid-input');
    });
  });

  // SECTION 4: QUALITY TIER TESTING
  test.describe('Section 4: Quality Tier Selection and Pricing', () => {
    test('4.1 All quality tiers are selectable', async ({ page }) => {
      await page.goto('/calculator');
      
      // Test each quality tier
      for (const tier of ['smart', 'premium', 'luxury']) {
        // Open dropdown
        await page.click('[data-testid="quality-select"]');
        
        // Select tier
        await page.click(`[data-testid="quality-${tier}"]`);
        
        // Verify selection by checking dropdown text
        const tierNames = {
          smart: 'Smart Choice',
          premium: 'Premium Selection',
          luxury: 'Luxury Collection'
        };
        
        await expect(page.getByTestId('quality-select')).toContainText(tierNames[tier as keyof typeof tierNames]);
        
        await takeScreenshot(page, `10-quality-${tier}`);
      }
    });

    test('4.2 Quality tier pricing is displayed', async ({ page }) => {
      await page.goto('/calculator');
      
      // Open quality dropdown to see pricing
      await page.click('[data-testid="quality-select"]');
      
      // Check for price ranges in dropdown options
      await expect(page.locator('text=/1,600.*2,000/')).toBeVisible(); // Smart
      await expect(page.locator('text=/2,200.*2,800/')).toBeVisible(); // Premium
      await expect(page.locator('text=/3,000.*4,000/')).toBeVisible(); // Luxury
      
      await takeScreenshot(page, '11-quality-pricing');
    });
  });

  // SECTION 5: CALCULATION FLOW
  test.describe('Section 5: Complete Calculation Flow', () => {
    test('5.1 Basic calculation produces results', async ({ page }) => {
      await page.goto('/calculator');
      
      // Fill form
      await fillCalculatorForm(page, {
        plotSize: '1000',
        floors: '2',
        quality: 'premium',
        location: 'bangalore'
      });
      
      // Click calculate
      const calculateButton = page.getByTestId('calculate-button');
      await calculateButton.click();
      
      // Wait for results
      await expect(page.getByTestId('results-container')).toBeVisible({ timeout: 10000 });
      
      // Verify cost is displayed
      const totalCost = page.getByTestId('total-cost');
      await expect(totalCost).toBeVisible();
      
      const costText = await totalCost.textContent();
      expect(costText).toMatch(/₹\s*[\d,]+/);
      
      await takeScreenshot(page, '12-calculation-results');
    });

    test('5.2 Cost breakdown is complete', async ({ page }) => {
      await page.goto('/calculator');
      
      // Perform calculation
      await fillCalculatorForm(page, {
        plotSize: '1500',
        floors: '2',
        quality: 'smart',
        location: 'delhi'
      });
      
      await page.getByTestId('calculate-button').click();
      await expect(page.getByTestId('results-container')).toBeVisible();
      
      // Check all breakdown categories
      await expect(page.getByTestId('breakdown-structure')).toBeVisible();
      await expect(page.getByTestId('breakdown-finishing')).toBeVisible();
      await expect(page.getByTestId('breakdown-mep')).toBeVisible();
      await expect(page.getByTestId('breakdown-external')).toBeVisible();
      await expect(page.getByTestId('breakdown-other')).toBeVisible();
      
      // Check percentages are displayed
      await expect(page.getByText('35%')).toBeVisible();
      await expect(page.getByText('30%')).toBeVisible();
      await expect(page.getByText('20%')).toBeVisible();
      await expect(page.getByText('10%')).toBeVisible();
      await expect(page.getByText('5%')).toBeVisible();
      
      await takeScreenshot(page, '13-cost-breakdown');
    });
  });

  // SECTION 6: ANIMATIONS AND TRANSITIONS
  test.describe('Section 6: Animations and UI Polish', () => {
    test('6.1 Loading states and animations', async ({ page }) => {
      await page.goto('/calculator');
      
      // Fill form
      await fillCalculatorForm(page, {
        plotSize: '2000',
        floors: '3',
        quality: 'luxury',
        location: 'mumbai'
      });
      
      // Start recording for animation
      const calculateButton = page.getByTestId('calculate-button');
      
      // Check if button shows loading state
      await calculateButton.click();
      
      // Look for loading indicators
      const loadingSpinner = page.getByTestId('loading-spinner');
      const hasLoading = await loadingSpinner.isVisible({ timeout: 1000 }).catch(() => false);
      
      if (hasLoading) {
        await takeScreenshot(page, '14-loading-state');
      }
      
      // Wait for results
      await expect(page.getByTestId('results-container')).toBeVisible({ timeout: 10000 });
      
      // Check for fade-in animation
      const resultsSection = page.getByTestId('results-container');
      const opacity = await resultsSection.evaluate(el => 
        window.getComputedStyle(el).opacity
      );
      
      expect(Number(opacity)).toBeGreaterThan(0);
      
      await takeScreenshot(page, '15-results-animation');
    });

    test('6.2 Hover states and interactions', async ({ page }) => {
      await page.goto('/calculator');
      
      // Test button hover
      const button = page.getByTestId('calculate-button');
      await button.hover();
      
      // Get hover styles
      const hoverStyles = await button.evaluate(el => {
        const styles = window.getComputedStyle(el);
        return {
          backgroundColor: styles.backgroundColor,
          transform: styles.transform,
          cursor: styles.cursor
        };
      });
      
      expect(hoverStyles.cursor).toBe('pointer');
      
      await takeScreenshot(page, '16-hover-states');
    });

    test('6.3 Number count-up animation', async ({ page }) => {
      await page.goto('/calculator');
      
      // Perform calculation
      await fillCalculatorForm(page, {
        plotSize: '1000',
        floors: '1',
        quality: 'smart',
        location: 'bangalore'
      });
      
      await page.getByTestId('calculate-button').click();
      
      // Wait for results
      await expect(page.getByTestId('total-cost')).toBeVisible({ timeout: 10000 });
      
      // Check if numbers are displayed correctly
      const costElement = page.getByTestId('total-cost');
      const finalText = await costElement.textContent();
      
      expect(finalText).toMatch(/₹\s*[\d,]+/);
      
      await takeScreenshot(page, '17-number-animation');
    });
  });

  // SECTION 7: MOBILE RESPONSIVENESS
  test.describe('Section 7: Mobile Responsive Design', () => {
    test('7.1 Mobile viewport - iPhone SE', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 });
      await page.goto(`${BASE_URL}/calculator`);
      
      // Check no horizontal scroll
      const bodyWidth = await page.evaluate(() => document.body.scrollWidth);
      expect(bodyWidth).toBeLessThanOrEqual(375);
      
      // Check form is accessible
      const formFields = page.locator('input, select, button');
      const count = await formFields.count();
      expect(count).toBeGreaterThan(0);
      
      // Test form interaction on mobile
      const plotInput = page.getByTestId('plot-size-input');
      await plotInput.fill('1000');
      await expect(plotInput).toHaveValue('1000');
      
      await takeScreenshot(page, '18-mobile-iphone-se');
    });

    test('7.2 Mobile viewport - iPad', async ({ page }) => {
      await page.setViewportSize({ width: 768, height: 1024 });
      await page.goto(`${BASE_URL}/calculator`);
      
      // Check layout adapts
      const calculator = page.getByTestId('calculator-container');
      const width = await calculator.evaluate(el => el.offsetWidth);
      
      expect(width).toBeLessThanOrEqual(768);
      expect(width).toBeGreaterThan(300);
      
      await takeScreenshot(page, '19-mobile-ipad');
    });

    test('7.3 Touch interactions', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 });
      await page.goto(`${BASE_URL}/calculator`);
      
      // Test dropdown on mobile
      const select = page.getByTestId('floors-select');
      await select.tap();
      
      // Verify dropdown is accessible
      const isVisible = await select.isVisible();
      expect(isVisible).toBeTruthy();
      
      await takeScreenshot(page, '20-mobile-touch');
    });
  });

  // SECTION 8: ADVANCED FEATURES (Days 6-9)
  test.describe('Section 8: Advanced Features Testing', () => {
    test('8.1 Save calculation feature', async ({ page }) => {
      await page.goto(`${BASE_URL}/calculator`);
      
      // Perform calculation first
      await fillCalculatorForm(page, {
        plotSize: '2000',
        floors: '2',
        quality: 'premium',
        location: 'delhi'
      });
      
      await page.click('button:has-text("Calculate")');
      await page.waitForSelector('[data-testid*="result"], .results-section');
      
      // Look for save button
      const saveButton = page.getByTestId('save-button');
      const saveExists = await saveButton.isVisible({ timeout: 2000 }).catch(() => false);
      
      if (saveExists) {
        await saveButton.click();
        // Check for login prompt or success message
        await page.waitForTimeout(1000);
        await takeScreenshot(page, '21-save-feature');
      }
    });

    test('8.2 PDF download feature', async ({ page }) => {
      await page.goto(`${BASE_URL}/calculator`);
      
      // Perform calculation
      await fillCalculatorForm(page, {
        plotSize: '1500',
        floors: '1',
        quality: 'smart',
        location: 'bangalore'
      });
      
      await page.click('button:has-text("Calculate")');
      await page.waitForSelector('[data-testid*="result"], .results-section');
      
      // Look for PDF button
      const pdfButton = page.getByTestId('download-pdf-button');
      const pdfExists = await pdfButton.isVisible({ timeout: 2000 }).catch(() => false);
      
      if (pdfExists) {
        // Set up download promise before clicking
        const downloadPromise = page.waitForEvent('download', { timeout: 5000 }).catch(() => null);
        await pdfButton.click();
        
        const download = await downloadPromise;
        if (download) {
          await takeScreenshot(page, '22-pdf-download');
        }
      }
    });

    test('8.3 Materials list display', async ({ page }) => {
      await page.goto(`${BASE_URL}/calculator`);
      
      // Perform calculation
      await fillCalculatorForm(page, {
        plotSize: '1000',
        floors: '2',
        quality: 'premium',
        location: 'mumbai'
      });
      
      await page.getByTestId('calculate-button').click();
      await expect(page.getByTestId('results-container')).toBeVisible();
      
      // Check materials section exists
      await expect(page.getByTestId('materials-list')).toBeVisible();
      
      // Check if materials are displayed or placeholder text
      const materialsContent = page.getByTestId('materials-content');
      const materialsEmpty = page.getByTestId('materials-empty');
      
      // Either materials are loaded or placeholder is shown
      const hasContent = await materialsContent.isVisible().catch(() => false);
      const hasPlaceholder = await materialsEmpty.isVisible().catch(() => false);
      
      expect(hasContent || hasPlaceholder).toBeTruthy();
      
      await takeScreenshot(page, '23-materials-list');
    });
  });

  // SECTION 9: ACCESSIBILITY
  test.describe('Section 9: Accessibility Testing', () => {
    test('9.1 Keyboard navigation', async ({ page }) => {
      await page.goto(`${BASE_URL}/calculator`);
      
      // Tab through form
      await page.keyboard.press('Tab');
      await page.keyboard.press('Tab');
      
      // Check focus is visible
      const focusedElement = await page.evaluate(() => {
        const el = document.activeElement;
        return {
          tagName: el?.tagName,
          hasOutline: window.getComputedStyle(el!).outline !== 'none'
        };
      });
      
      expect(focusedElement.tagName).toBeTruthy();
      
      await takeScreenshot(page, '24-keyboard-navigation');
    });

    test('9.2 ARIA labels and roles', async ({ page }) => {
      await page.goto(`${BASE_URL}/calculator`);
      
      // Check calculator container
      await expect(page.getByTestId('calculator-container')).toBeVisible();
      
      // Check form inputs have proper ARIA labels
      await expect(page.getByTestId('plot-size-input')).toHaveAttribute('aria-label');
      await expect(page.getByTestId('floors-select')).toHaveAttribute('aria-label');
      await expect(page.getByTestId('quality-select')).toHaveAttribute('aria-label');
      await expect(page.getByTestId('location-select')).toHaveAttribute('aria-label');
      await expect(page.getByTestId('building-type-select')).toHaveAttribute('aria-label');
      
      await takeScreenshot(page, '25-accessibility');
    });
  });

  // SECTION 10: PERFORMANCE
  test.describe('Section 10: Performance Testing', () => {
    test('10.1 Page load performance', async ({ page }) => {
      const startTime = Date.now();
      
      await page.goto('/calculator');
      await page.waitForLoadState('domcontentloaded');
      
      const loadTime = Date.now() - startTime;
      
      expect(loadTime).toBeLessThan(5000); // Should load in under 5 seconds
      
      // Check for web vitals
      const metrics = await page.evaluate(() => {
        return {
          hasVitals: 'web-vitals' in window || 'PerformanceObserver' in window
        };
      });
      
      expect(metrics.hasVitals).toBeTruthy();
    });

    test('10.2 Calculation performance', async ({ page }) => {
      await page.goto(`${BASE_URL}/calculator`);
      
      // Fill form
      await fillCalculatorForm(page, {
        plotSize: '5000',
        floors: '4',
        quality: 'luxury',
        location: 'delhi'
      });
      
      const startTime = Date.now();
      await page.getByTestId('calculate-button').click();
      
      // Wait for results
      await expect(page.getByTestId('results-container')).toBeVisible({ timeout: 10000 });
      
      const calcTime = Date.now() - startTime;
      expect(calcTime).toBeLessThan(3000); // Should calculate in under 3 seconds
    });
  });

  // SECTION 11: EDGE CASES
  test.describe('Section 11: Edge Cases and Error Scenarios', () => {
    test('11.1 Empty form submission', async ({ page }) => {
      await page.goto(`${BASE_URL}/calculator`);
      
      // Try to submit without filling
      const calculateButton = page.getByTestId('calculate-button');
      
      // Button should be disabled when no plot size is entered
      const isDisabled = await calculateButton.isDisabled();
      expect(isDisabled).toBeTruthy();
      
      await takeScreenshot(page, '26-empty-form');
    });

    test('11.2 Boundary value testing', async ({ page }) => {
      await page.goto(`${BASE_URL}/calculator`);
      
      // Test minimum values
      await fillCalculatorForm(page, {
        plotSize: '500', // Minimum
        floors: '1',     // Minimum
        quality: 'smart',
        location: 'bangalore'
      });
      
      await page.click('button:has-text("Calculate")');
      await page.waitForSelector('[data-testid*="result"], .results-section');
      
      const minCost = await page.locator('text=/₹/').first().textContent();
      expect(minCost).toMatch(/₹/);
      
      // Test maximum values
      await page.reload();
      await fillCalculatorForm(page, {
        plotSize: '50000', // Maximum
        floors: '4',       // Maximum
        quality: 'luxury',
        location: 'mumbai'
      });
      
      await page.click('button:has-text("Calculate")');
      await page.waitForSelector('[data-testid*="result"], .results-section');
      
      const maxCost = await page.locator('text=/₹/').first().textContent();
      expect(maxCost).toMatch(/₹/);
      
      await takeScreenshot(page, '27-boundary-values');
    });
  });

  // SECTION 12: VISUAL CONSISTENCY
  test.describe('Section 12: Visual Consistency and Polish', () => {
    test('12.1 Consistent styling across components', async ({ page }) => {
      await page.goto(`${BASE_URL}/calculator`);
      
      // Check button styles
      const calculateButton = page.getByTestId('calculate-button');
      const resetButton = page.getByTestId('reset-button');
      
      for (const button of [calculateButton, resetButton]) {
        const styles = await button.evaluate(el => {
          const computed = window.getComputedStyle(el);
          return {
            borderRadius: computed.borderRadius,
            fontFamily: computed.fontFamily,
            cursor: computed.cursor
          };
        });
        
        expect(styles.cursor).toBe('pointer');
        expect(styles.borderRadius).not.toBe('0px');
      }
      
      await takeScreenshot(page, '28-visual-consistency');
    });

    test('12.2 Color scheme and contrast', async ({ page }) => {
      await page.goto(`${BASE_URL}/calculator`);
      
      // Check text contrast on key elements
      const formTitle = page.getByTestId('form-title');
      const plotSizeLabel = page.locator('label:has-text("Plot Size")');
      
      for (const element of [formTitle, plotSizeLabel]) {
        const styles = await element.evaluate(el => {
          const computed = window.getComputedStyle(el);
          return {
            color: computed.color,
            backgroundColor: computed.backgroundColor,
            fontSize: computed.fontSize
          };
        });
        
        // Basic check - text should not be same color as background
        expect(styles.color).not.toBe(styles.backgroundColor);
      }
      
      await takeScreenshot(page, '29-color-contrast');
    });

    test('12.3 Final visual polish check', async ({ page }) => {
      await page.goto(`${BASE_URL}/calculator`);
      
      // Perform a complete calculation for final screenshot
      await fillCalculatorForm(page, {
        plotSize: '2500',
        floors: '3',
        quality: 'premium',
        location: 'delhi'
      });
      
      await page.getByTestId('calculate-button').click();
      await expect(page.getByTestId('results-container')).toBeVisible();
      
      // Take final comprehensive screenshot
      await takeScreenshot(page, '30-final-complete-view');
      
      // Scroll to show full results
      await page.evaluate(() => window.scrollTo(0, document.body.scrollHeight));
      await page.waitForTimeout(500);
      
      await takeScreenshot(page, '31-final-results-view');
    });
  });
});

// GENERATE COMPREHENSIVE REPORT
test.afterAll(async () => {
  generateComprehensiveReport();
});

function generateComprehensiveReport() {
  const timestamp = new Date().toISOString();
  const screenshots = fs.readdirSync(SCREENSHOT_DIR);
  
  const report = `# COMPREHENSIVE UI TESTING REPORT - Clarity Engine
Generated: ${timestamp}

## Executive Summary

This report provides COMPLETE validation of ALL UI/UX features implemented in Days 1-9.

### Overall Result: [TO BE DETERMINED BY ACTUAL TEST RESULTS]

## Test Categories Covered

### 1. Basic Functionality
- ✓ Page loading without errors
- ✓ Correct metadata and title
- ✓ Navigation elements present

### 2. Form Elements
- ✓ All required fields present
- ✓ Correct labels and placeholders
- ✓ No duplicate IDs or names

### 3. Input Validation
- ✓ Minimum value validation
- ✓ Maximum value validation
- ✓ Invalid input handling

### 4. Quality Tiers
- ✓ All tiers selectable
- ✓ Pricing displayed correctly
- ✓ Visual feedback on selection

### 5. Calculation Flow
- ✓ Complete calculation process
- ✓ Results displayed correctly
- ✓ Cost breakdown complete

### 6. Animations & Polish
- ✓ Loading states
- ✓ Hover effects
- ✓ Smooth transitions

### 7. Mobile Responsiveness
- ✓ iPhone SE (375px)
- ✓ iPad (768px)
- ✓ Touch interactions

### 8. Advanced Features
- ✓ Save calculation
- ✓ PDF download
- ✓ Materials list

### 9. Accessibility
- ✓ Keyboard navigation
- ✓ ARIA labels
- ✓ Focus indicators

### 10. Performance
- ✓ Page load time
- ✓ Calculation speed
- ✓ Responsive interactions

### 11. Edge Cases
- ✓ Empty form handling
- ✓ Boundary values
- ✓ Error scenarios

### 12. Visual Consistency
- ✓ Consistent styling
- ✓ Color contrast
- ✓ Professional appearance

## Screenshots Generated

Total Screenshots: ${screenshots.length}

${screenshots.map(s => `- ${s}`).join('\n')}

## Critical Issues Found

[TO BE FILLED BASED ON TEST RESULTS]

## Recommendations

[TO BE FILLED BASED ON TEST RESULTS]

## Conclusion

This comprehensive test suite validates EVERY aspect of the UI/UX implementation from Days 1-9. Any issues found should be addressed before proceeding to Day 10.

---

**Note**: This is an ACTUAL comprehensive test that checks EVERYTHING. Do not claim success unless ALL tests pass with screenshots as proof.
`;

  fs.writeFileSync('test-results/COMPREHENSIVE_UI_REPORT.md', report);
  console.log('\nComprehensive UI Testing Complete!');
  console.log(`Report generated: test-results/COMPREHENSIVE_UI_REPORT.md`);
  console.log(`Screenshots: ${screenshots.length} captured in ${SCREENSHOT_DIR}`);
}