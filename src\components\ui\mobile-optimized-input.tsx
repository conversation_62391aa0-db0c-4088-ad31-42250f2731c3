"use client";

import React, { useState, useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';
import hapticFeedback from '@/lib/mobile/haptic-feedback';

interface MobileOptimizedInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  helpText?: string;
  enableHaptic?: boolean;
  autoCorrectDisabled?: boolean;
  touchOptimized?: boolean;
  validationPattern?: RegExp;
  formatValue?: (value: string) => string;
  onValidation?: (isValid: boolean, value: string) => void;
}

const MobileOptimizedInput = React.forwardRef<HTMLInputElement, MobileOptimizedInputProps>(
  ({
    className,
    type = 'text',
    label,
    error,
    helpText,
    enableHaptic = true,
    autoCorrectDisabled = true,
    touchOptimized = true,
    validationPattern,
    formatValue,
    onValidation,
    onFocus,
    onBlur,
    onChange,
    onKeyDown,
    value,
    ...props
  }, ref) => {
    const [isFocused, setIsFocused] = useState(false);
    const [isValid, setIsValid] = useState(true);
    const [internalValue, setInternalValue] = useState(value || '');
    const inputRef = useRef<HTMLInputElement>(null);
    const containerRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
      if (value !== undefined) {
        setInternalValue(value);
      }
    }, [value]);

    useEffect(() => {
      if (touchOptimized && containerRef.current) {
        // Ensure minimum touch target size
        const container = containerRef.current;
        const rect = container.getBoundingClientRect();
        
        if (rect.height < 44) {
          container.style.minHeight = '44px';
        }
      }
    }, [touchOptimized]);

    const handleFocus = (event: React.FocusEvent<HTMLInputElement>) => {
      setIsFocused(true);
      
      if (enableHaptic) {
        hapticFeedback.tap();
      }
      
      // Scroll into view for mobile keyboards
      if (touchOptimized) {
        setTimeout(() => {
          event.target.scrollIntoView({ 
            behavior: 'smooth', 
            block: 'center' 
          });
        }, 300);
      }
      
      onFocus?.(event);
    };

    const handleBlur = (event: React.FocusEvent<HTMLInputElement>) => {
      setIsFocused(false);
      
      if (validationPattern) {
        const valid = validationPattern.test(event.target.value);
        setIsValid(valid);
        onValidation?.(valid, event.target.value);
      }
      
      onBlur?.(event);
    };

    const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
      let newValue = event.target.value;
      
      if (formatValue) {
        newValue = formatValue(newValue);
        event.target.value = newValue;
      }
      
      setInternalValue(newValue);
      
      if (validationPattern) {
        const valid = validationPattern.test(newValue);
        setIsValid(valid);
        onValidation?.(valid, newValue);
      }
      
      onChange?.(event);
    };

    const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
      if (enableHaptic && (event.key === 'Enter' || event.key === 'Tab')) {
        hapticFeedback.button();
      }
      
      onKeyDown?.(event);
    };

    const inputProps = {
      ...props,
      type,
      value: internalValue,
      onFocus: handleFocus,
      onBlur: handleBlur,
      onChange: handleChange,
      onKeyDown: handleKeyDown,
      ref: ref || inputRef,
      // Mobile optimizations
      autoCapitalize: autoCorrectDisabled ? 'none' : undefined,
      autoCorrect: autoCorrectDisabled ? 'off' : undefined,
      spellCheck: autoCorrectDisabled ? false : undefined,
      // Improve mobile input experience
      inputMode: type === 'number' ? 'numeric' : 
                type === 'email' ? 'email' : 
                type === 'tel' ? 'tel' : 'text',
      // Accessibility
      'aria-invalid': error ? 'true' : 'false',
      'aria-describedby': error ? `${props.id}-error` : 
                         helpText ? `${props.id}-help` : undefined,
    };

    return (
      <div 
        ref={containerRef}
        className={cn(
          'mobile-input-container',
          'relative w-full',
          touchOptimized && 'touch-optimized',
          className
        )}
      >
        {label && (
          <label
            htmlFor={props.id}
            className={cn(
              'block text-sm font-medium mb-2 transition-colors',
              isFocused ? 'text-blue-600' : 'text-gray-700',
              error && 'text-red-600'
            )}
          >
            {label}
          </label>
        )}
        
        <div className="relative">
          <input
            {...inputProps}
            className={cn(
              // Base styles
              'w-full px-4 py-3 text-base border rounded-lg transition-all duration-200',
              'placeholder:text-gray-400 focus:outline-none focus:ring-2',
              
              // Mobile optimizations
              touchOptimized && [
                'min-h-[44px]', // iOS minimum touch target
                'text-[16px]', // Prevent zoom on iOS
                'appearance-none', // Remove default styling
                '-webkit-appearance-none',
              ],
              
              // States
              isFocused && [
                'border-blue-500 focus:ring-blue-500/20',
                'shadow-lg shadow-blue-500/10'
              ],
              
              !isFocused && !error && [
                'border-gray-300 hover:border-gray-400'
              ],
              
              error && [
                'border-red-500 focus:ring-red-500/20',
                'shadow-lg shadow-red-500/10'
              ],
              
              !isValid && !error && [
                'border-orange-400 focus:ring-orange-400/20'
              ],
              
              props.disabled && [
                'bg-gray-50 text-gray-500 cursor-not-allowed',
                'border-gray-200'
              ],
              
              // Touch feedback
              enableHaptic && 'cursor-pointer'
            )}
          />
          
          {/* Validation indicator */}
          {(isValid === false || error) && (
            <div className="absolute inset-y-0 right-0 flex items-center pr-3">
              <svg
                className={cn(
                  'h-5 w-5',
                  error ? 'text-red-500' : 'text-orange-500'
                )}
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
          )}
          
          {isValid && !error && internalValue && (
            <div className="absolute inset-y-0 right-0 flex items-center pr-3">
              <svg
                className="h-5 w-5 text-green-500"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
          )}
        </div>
        
        {/* Error message */}
        {error && (
          <p
            id={`${props.id}-error`}
            className="mt-2 text-sm text-red-600 flex items-center gap-1"
          >
            <svg className="h-4 w-4 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
              <path
                fillRule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                clipRule="evenodd"
              />
            </svg>
            {error}
          </p>
        )}
        
        {/* Help text */}
        {helpText && !error && (
          <p
            id={`${props.id}-help`}
            className="mt-2 text-sm text-gray-600"
          >
            {helpText}
          </p>
        )}
        
        {/* Focus indicator for accessibility */}
        {isFocused && (
          <div 
            className="absolute inset-0 pointer-events-none rounded-lg ring-2 ring-blue-500 ring-offset-2" 
            aria-hidden="true"
          />
        )}
      </div>
    );
  }
);

MobileOptimizedInput.displayName = 'MobileOptimizedInput';

export { MobileOptimizedInput };
export type { MobileOptimizedInputProps };