import Link from 'next/link';

export function SimpleFooter() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="border-t bg-gray-50" data-testid="footer" role="contentinfo">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Company Info */}
          <div className="space-y-4" data-testid="footer-company-info">
            <div className="flex items-center space-x-2" data-testid="footer-logo">
              <div className="h-8 w-8 rounded bg-blue-600 flex items-center justify-center" data-testid="footer-logo-icon">
                <span className="text-white font-bold text-lg">C</span>
              </div>
              <span className="font-bold text-xl" data-testid="footer-logo-text">Clarity Engine</span>
            </div>
            <p className="text-sm text-gray-600 max-w-xs" data-testid="footer-company-description">
              India's most trusted construction intelligence platform,
              empowering families to build their dream homes with complete
              financial clarity.
            </p>
          </div>

          {/* Quick Links */}
          <div className="space-y-4" data-testid="footer-quick-links">
            <h3 className="text-sm font-semibold" data-testid="footer-quick-links-title">Quick Links</h3>
            <ul className="space-y-2" data-testid="footer-quick-links-list">
              <li>
                <Link href="/about" className="text-sm text-gray-600 hover:text-blue-600" data-testid="footer-link-about" aria-label="About Clarity Engine">
                  About
                </Link>
              </li>
              <li>
                <Link href="/contact" className="text-sm text-gray-600 hover:text-blue-600" data-testid="footer-link-contact" aria-label="Contact us">
                  Contact
                </Link>
              </li>
              <li>
                <Link href="/privacy" className="text-sm text-gray-600 hover:text-blue-600" data-testid="footer-link-privacy" aria-label="Privacy Policy">
                  Privacy Policy
                </Link>
              </li>
              <li>
                <Link href="/terms" className="text-sm text-gray-600 hover:text-blue-600" data-testid="footer-link-terms" aria-label="Terms of Service">
                  Terms of Service
                </Link>
              </li>
            </ul>
          </div>

          {/* Contact Info */}
          <div className="space-y-4" data-testid="footer-contact-info">
            <h3 className="text-sm font-semibold" data-testid="footer-contact-title">Connect With Us</h3>
            <div className="space-y-2" data-testid="footer-contact-details">
              <p className="text-sm text-gray-600" data-testid="footer-email"><EMAIL></p>
              <p className="text-sm text-gray-600" data-testid="footer-phone">+91 1234567890</p>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="mt-8 pt-8 border-t flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0" data-testid="footer-bottom">
          <p className="text-sm text-gray-600" data-testid="footer-copyright">
            © {currentYear} Clarity Engine. All rights reserved.
          </p>
          <p className="text-sm text-gray-600" data-testid="footer-tagline">
            Building India's Construction Future
          </p>
        </div>
      </div>
    </footer>
  );
}