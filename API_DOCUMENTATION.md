# Nirmaan AI Construction Calculator - API Documentation

## Overview

The Nirmaan AI Construction Calculator API provides comprehensive construction cost calculation services for the Indian market. This RESTful API offers endpoints for cost calculations, project management, performance monitoring, and analytics.

**Base URL**: `https://your-domain.com/api`  
**Version**: 1.0.0  
**Environment**: Production ready  

## Table of Contents

1. [Authentication](#authentication)
2. [Rate Limiting](#rate-limiting)
3. [Error Handling](#error-handling)
4. [Core Endpoints](#core-endpoints)
5. [Project Management](#project-management)
6. [Performance & Monitoring](#performance--monitoring)
7. [Analytics](#analytics)
8. [Support Endpoints](#support-endpoints)
9. [Interactive Testing](#interactive-testing)

---

## Authentication

The API uses JWT-based authentication with Supabase. Some endpoints require authentication while others are public.

### Authentication Headers

```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

### Authentication Flow

1. **Login/Signup**: Use Supabase Auth to obtain JWT token
2. **Token Usage**: Include token in Authorization header
3. **Token Refresh**: Handle token expiration with refresh tokens

### Public Endpoints
- `GET /api/calculate` - API documentation
- `POST /api/calculate` - Cost calculation (with rate limiting)
- `GET /api/health` - Health check
- `GET /api/monitoring` - Basic monitoring metrics

### Protected Endpoints
- All `/api/projects/*` endpoints
- `POST /api/projects/save` - Save project
- `DELETE /api/projects` - Delete project

---

## Rate Limiting

All API endpoints implement rate limiting to ensure fair usage and system stability.

### Rate Limits

| Endpoint | Limit | Window | Headers |
|----------|-------|--------|---------|
| `/api/calculate` | 100 requests | 1 minute | `X-RateLimit-*` |
| `/api/projects/*` | 60 requests | 1 minute | `X-RateLimit-*` |
| `/api/monitoring` | 30 requests | 1 minute | `X-RateLimit-*` |

### Rate Limit Headers

```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 85
X-RateLimit-Reset: **********
```

### Rate Limit Exceeded Response

```json
{
  "success": false,
  "error": {
    "type": "rate_limit",
    "message": "Too many requests. Please try again later.",
    "code": "RATE_LIMIT_EXCEEDED",
    "retryAfter": 45
  },
  "timestamp": "2025-01-15T10:30:00.000Z",
  "requestId": "calc_**********_abc123"
}
```

---

## Error Handling

The API uses consistent error response format across all endpoints.

### Standard Error Response

```json
{
  "success": false,
  "error": {
    "type": "validation|calculation|rate_limit|server|authentication",
    "message": "Human readable error message",
    "code": "ERROR_CODE",
    "details": "Optional additional details"
  },
  "timestamp": "2025-01-15T10:30:00.000Z",
  "requestId": "unique_request_id"
}
```

### HTTP Status Codes

| Code | Description | Example |
|------|-------------|---------|
| `200` | Success | Request completed successfully |
| `400` | Bad Request | Invalid input parameters |
| `401` | Unauthorized | Missing or invalid authentication |
| `403` | Forbidden | Access denied |
| `422` | Unprocessable Entity | Calculation engine error |
| `429` | Too Many Requests | Rate limit exceeded |
| `500` | Internal Server Error | Server-side error |
| `503` | Service Unavailable | Health check failed |

### Error Codes

| Code | Description | Type |
|------|-------------|------|
| `RATE_LIMIT_EXCEEDED` | Rate limit exceeded | `rate_limit` |
| `INVALID_JSON` | Invalid JSON in request | `validation` |
| `MISSING_REQUIRED_FIELDS` | Required fields missing | `validation` |
| `INVALID_DATA_TYPES` | Invalid data types | `validation` |
| `VALIDATION_FAILED` | Input validation failed | `validation` |
| `CALCULATION_ERROR` | Calculation engine error | `calculation` |
| `INTERNAL_SERVER_ERROR` | Server error | `server` |
| `AUTHENTICATION_REQUIRED` | Authentication required | `authentication` |

---

## Core Endpoints

### POST /api/calculate

**Description**: Calculate construction costs for Indian construction projects.

**Rate Limit**: 100 requests/minute  
**Authentication**: Not required  
**Content-Type**: `application/json`

#### Request Parameters

```json
{
  "builtUpArea": 1200,          // Required: Built-up area in sqft
  "floors": 1,                  // Required: Number of floors (0=Ground only)
  "qualityTier": "premium",     // Required: smart|premium|luxury
  "location": "bangalore",      // Required: City name
  "plotArea": 1500,            // Optional: Plot area in sqft
  "hasStilt": false,           // Optional: Has stilt parking
  "parkingType": "open",       // Optional: open|covered|none
  "hasBasement": false,        // Optional: Has basement
  "specialFeatures": [         // Optional: Special features array
    {
      "name": "Swimming Pool",
      "cost": 500000,
      "description": "Standard swimming pool"
    }
  ]
}
```

#### Response

```json
{
  "success": true,
  "data": {
    "totalCost": 2400000,
    "costPerSqft": 2000,
    "breakdown": {
      "structure": {
        "amount": 840000,
        "percentage": 35,
        "subCategories": [
          {
            "name": "Foundation",
            "amount": 168000,
            "percentage": 7,
            "description": "RCC foundation with M20 concrete"
          }
        ]
      },
      "finishing": {
        "amount": 720000,
        "percentage": 30,
        "subCategories": [
          {
            "name": "Flooring",
            "amount": 216000,
            "percentage": 9,
            "description": "Vitrified tiles flooring"
          }
        ]
      },
      "mep": {
        "amount": 480000,
        "percentage": 20,
        "subCategories": [
          {
            "name": "Electrical",
            "amount": 240000,
            "percentage": 10,
            "description": "Complete electrical installation"
          }
        ]
      },
      "external": {
        "amount": 240000,
        "percentage": 10,
        "subCategories": [
          {
            "name": "Compound Wall",
            "amount": 120000,
            "percentage": 5,
            "description": "Boundary wall construction"
          }
        ]
      },
      "other": {
        "amount": 120000,
        "percentage": 5,
        "subCategories": [
          {
            "name": "Professional Fees",
            "amount": 72000,
            "percentage": 3,
            "description": "Architect and engineer fees"
          }
        ]
      },
      "total": 2400000
    },
    "materials": [
      {
        "category": "Structure",
        "name": "Cement",
        "quantity": 240,
        "unit": "bags",
        "rate": 350,
        "totalCost": 84000,
        "purpose": "RCC work and masonry",
        "specifications": "OPC 53 grade cement"
      }
    ],
    "timeline": [
      {
        "name": "Foundation",
        "duration": 3,
        "startAfter": 0,
        "dependencies": [],
        "description": "Excavation and foundation work"
      }
    ],
    "summary": {
      "totalBuiltUpArea": 1200,
      "carpetArea": 1020,
      "constructionDuration": 8,
      "totalCost": 2400000,
      "costPerSqft": 2000,
      "qualityTier": "premium",
      "location": "bangalore",
      "estimateAccuracy": "±10%"
    }
  },
  "timestamp": "2025-01-15T10:30:00.000Z",
  "requestId": "calc_**********_abc123",
  "performance": {
    "validationTime": 2.5,
    "calculationTime": 15.8,
    "totalTime": 18.3
  }
}
```

#### Example Usage

```bash
curl -X POST "https://your-domain.com/api/calculate" \
  -H "Content-Type: application/json" \
  -d '{
    "builtUpArea": 1200,
    "floors": 1,
    "qualityTier": "premium",
    "location": "bangalore"
  }'
```

### GET /api/calculate

**Description**: Get API documentation and supported parameters.

**Rate Limit**: Not limited  
**Authentication**: Not required

#### Response

```json
{
  "name": "Clarity Engine Calculator API",
  "version": "1.0.0",
  "description": "Construction cost calculation API for Indian market",
  "endpoints": {
    "POST": {
      "description": "Calculate construction costs",
      "required_fields": ["builtUpArea", "qualityTier", "location"],
      "optional_fields": [
        "floors", "plotArea", "hasStilt", "parkingType", 
        "hasBasement", "specialFeatures"
      ],
      "response": "CalculationResult with cost breakdown and materials list"
    }
  },
  "supported_locations": [
    "bangalore", "mumbai", "delhi", "pune", "hyderabad",
    "chennai", "kolkata", "ahmedabad", "jaipur", "lucknow",
    "bhubaneswar"
  ],
  "quality_tiers": {
    "smart": "₹1,600-2,000 per sqft - Standard finishes",
    "premium": "₹2,200-2,800 per sqft - Branded materials",
    "luxury": "₹3,000-4,000+ per sqft - Premium finishes"
  }
}
```

---

## Project Management

### GET /api/projects

**Description**: Get user's saved projects with pagination.

**Rate Limit**: 60 requests/minute  
**Authentication**: Required  
**Query Parameters**: `limit`, `offset`

#### Request

```bash
curl -X GET "https://your-domain.com/api/projects?limit=10&offset=0" \
  -H "Authorization: Bearer <jwt_token>"
```

#### Response

```json
{
  "projects": [
    {
      "id": "proj_abc123",
      "name": "My Dream Home",
      "location": "bangalore",
      "area_sqft": 1200,
      "floors": 1,
      "quality_tier": "premium",
      "created_at": "2025-01-15T10:30:00.000Z",
      "updated_at": "2025-01-15T10:30:00.000Z",
      "totalCost": 2400000,
      "costPerSqft": 2000,
      "timeline": 8
    }
  ],
  "pagination": {
    "offset": 0,
    "limit": 10,
    "hasMore": false
  }
}
```

### POST /api/projects/save

**Description**: Save a new project calculation.

**Rate Limit**: 60 requests/minute  
**Authentication**: Required

#### Request

```json
{
  "name": "My Dream Home",
  "location": "bangalore",
  "area_sqft": 1200,
  "floors": 1,
  "quality_tier": "premium",
  "calculation_data": {
    "formData": {
      "builtUpArea": 1200,
      "plotSize": 1500,
      "floors": 1,
      "qualityTier": "premium",
      "location": "bangalore",
      "hasParking": true,
      "parkingType": "open"
    },
    "results": {
      "totalCost": 2400000,
      "costPerSqft": 2000,
      "costBreakdown": {},
      "materials": [],
      "timeline": {
        "totalDuration": 8,
        "phases": []
      },
      "summary": {
        "projectType": "Residential",
        "constructionType": "RCC",
        "totalBuiltUpArea": 1200,
        "estimatedTimeline": "8 months"
      }
    },
    "calculatedAt": "2025-01-15T10:30:00.000Z",
    "version": "1.0.0"
  }
}
```

#### Response

```json
{
  "success": true,
  "project": {
    "id": "proj_abc123",
    "name": "My Dream Home",
    "location": "bangalore",
    "area_sqft": 1200,
    "quality_tier": "premium",
    "created_at": "2025-01-15T10:30:00.000Z"
  }
}
```

### DELETE /api/projects

**Description**: Delete a project by ID.

**Rate Limit**: 60 requests/minute  
**Authentication**: Required  
**Query Parameters**: `id` (required)

#### Request

```bash
curl -X DELETE "https://your-domain.com/api/projects?id=proj_abc123" \
  -H "Authorization: Bearer <jwt_token>"
```

#### Response

```json
{
  "success": true
}
```

---

## Performance & Monitoring

### GET /api/health

**Description**: Comprehensive health check with system metrics.

**Rate Limit**: 30 requests/minute  
**Authentication**: Not required

#### Response

```json
{
  "status": "healthy",
  "timestamp": "2025-01-15T10:30:00.000Z",
  "version": "1.0.0",
  "environment": "production",
  "uptime": 86400,
  "checks": {
    "database": true,
    "api": true,
    "calculations": true,
    "memory": {
      "used": 256,
      "total": 512,
      "percentage": 50
    }
  },
  "performance": {
    "responseTime": 45,
    "requestCount": 1247,
    "errorRate": 0.8
  }
}
```

### GET /api/monitoring

**Description**: Detailed monitoring metrics and system performance.

**Rate Limit**: 30 requests/minute  
**Authentication**: Not required  
**Query Parameters**: `detail` (basic|detailed)

#### Request

```bash
curl -X GET "https://your-domain.com/api/monitoring?detail=detailed"
```

#### Response

```json
{
  "status": "healthy",
  "timestamp": "2025-01-15T10:30:00.000Z",
  "metrics": {
    "requests": {
      "total": 1247,
      "errors": 10,
      "errorRate": 0.8,
      "throughput": 52.3
    },
    "performance": {
      "responseTime": {
        "avg": 157,
        "max": 2340,
        "min": 23
      },
      "memory": {
        "usage": 256,
        "unit": "MB"
      }
    },
    "system": {
      "uptime": 86400,
      "environment": "production",
      "version": "1.0.0"
    }
  },
  "endpoints": [
    {
      "path": "/api/calculate",
      "requests": 856,
      "averageResponseTime": 145,
      "errorRate": 0.5
    }
  ],
  "recentErrors": [
    {
      "timestamp": "2025-01-15T10:25:00.000Z",
      "error": "Validation failed",
      "endpoint": "/api/calculate",
      "statusCode": 400
    }
  ],
  "slowRequests": [
    {
      "timestamp": "2025-01-15T10:20:00.000Z",
      "endpoint": "/api/calculate",
      "responseTime": 2340,
      "threshold": 1000
    }
  ]
}
```

### POST /api/performance/metrics

**Description**: Submit performance metrics for monitoring.

**Rate Limit**: 100 requests/minute  
**Authentication**: Not required

#### Request

```json
{
  "metric": {
    "name": "LCP",
    "value": 1234,
    "rating": "good",
    "url": "https://your-domain.com/calculator",
    "userAgent": "Mozilla/5.0...",
    "sessionId": "session_abc123",
    "deviceType": "desktop",
    "connectionType": "4g",
    "route": "/calculator"
  },
  "page": "/calculator",
  "viewport": {
    "width": 1920,
    "height": 1080
  }
}
```

#### Response

```json
{
  "status": "received",
  "alerts": 0
}
```

### GET /api/performance/metrics

**Description**: Retrieve performance metrics data.

**Rate Limit**: 60 requests/minute  
**Authentication**: Not required  
**Query Parameters**: `metric`, `start`, `end`, `limit`

#### Request

```bash
curl -X GET "https://your-domain.com/api/performance/metrics?metric=LCP&limit=100"
```

#### Response

```json
{
  "metrics": [
    {
      "name": "LCP",
      "value": 1234,
      "rating": "good",
      "timestamp": **********000,
      "url": "https://your-domain.com/calculator",
      "userAgent": "Mozilla/5.0...",
      "deviceType": "desktop"
    }
  ],
  "stats": {
    "count": 245,
    "average": 1456.78,
    "median": 1234,
    "p95": 2456,
    "p99": 3456,
    "min": 567,
    "max": 4567
  },
  "totalCount": 245,
  "timestamp": **********000
}
```

---

## Analytics

### POST /api/analytics/web-vitals

**Description**: Submit Web Vitals metrics for analysis.

**Rate Limit**: 200 requests/minute  
**Authentication**: Not required

#### Request

```json
{
  "metrics": [
    {
      "metric": "LCP",
      "value": 1234,
      "rating": "good",
      "url": "https://your-domain.com/calculator",
      "timestamp": **********000,
      "userAgent": "Mozilla/5.0...",
      "deviceType": "desktop",
      "connectionType": "4g",
      "route": "/calculator",
      "sessionId": "session_abc123",
      "userId": "user_xyz789"
    }
  ]
}
```

#### Response

```json
{
  "status": "received",
  "processed": 1,
  "timestamp": **********000
}
```

### GET /api/analytics/web-vitals

**Description**: Retrieve Web Vitals analytics data.

**Rate Limit**: 60 requests/minute  
**Authentication**: Not required  
**Query Parameters**: `metric`, `timeRange`, `deviceType`, `route`, `aggregated`

#### Request

```bash
curl -X GET "https://your-domain.com/api/analytics/web-vitals?timeRange=24h&aggregated=true"
```

#### Response

```json
{
  "stats": {
    "overview": {
      "totalMetrics": 1247,
      "uniqueUsers": 234,
      "uniquePages": 12,
      "ratingPercentages": {
        "good": 78,
        "needs-improvement": 15,
        "poor": 7
      }
    },
    "byMetric": {
      "LCP": {
        "count": 245,
        "average": 1456.78,
        "median": 1234,
        "p95": 2456,
        "p99": 3456
      },
      "FID": {
        "count": 234,
        "average": 45.67,
        "median": 23,
        "p95": 156,
        "p99": 234
      }
    },
    "performanceScore": 82,
    "trends": {
      "performanceScore": {
        "current": 82,
        "previous": 79,
        "change": 3,
        "trend": "up"
      }
    }
  },
  "timeRange": "24h",
  "totalMetrics": 1247,
  "timestamp": **********000
}
```

---

## Support Endpoints

### GET /api/robots

**Description**: Robots.txt for search engine crawling.

#### Response

```
User-agent: *
Allow: /
Disallow: /api/
Disallow: /dashboard/
Sitemap: https://your-domain.com/api/sitemap
```

### GET /api/sitemap

**Description**: XML sitemap for search engines.

#### Response

```xml
<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>https://your-domain.com/</loc>
    <lastmod>2025-01-15</lastmod>
    <changefreq>weekly</changefreq>
    <priority>1.0</priority>
  </url>
  <url>
    <loc>https://your-domain.com/calculator</loc>
    <lastmod>2025-01-15</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.9</priority>
  </url>
</urlset>
```

---

## Interactive Testing

### Using curl

#### Basic Cost Calculation
```bash
curl -X POST "https://your-domain.com/api/calculate" \
  -H "Content-Type: application/json" \
  -d '{
    "builtUpArea": 1200,
    "floors": 1,
    "qualityTier": "premium",
    "location": "bangalore"
  }'
```

#### Save Project (with Authentication)
```bash
curl -X POST "https://your-domain.com/api/projects/save" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "name": "Test Project",
    "location": "bangalore",
    "area_sqft": 1200,
    "floors": 1,
    "quality_tier": "premium",
    "calculation_data": {...}
  }'
```

#### Health Check
```bash
curl -X GET "https://your-domain.com/api/health"
```

### Using JavaScript/Fetch

#### Cost Calculation
```javascript
async function calculateCost() {
  try {
    const response = await fetch('/api/calculate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        builtUpArea: 1200,
        floors: 1,
        qualityTier: 'premium',
        location: 'bangalore'
      })
    });
    
    const data = await response.json();
    
    if (data.success) {
      console.log('Total Cost:', data.data.totalCost);
      console.log('Cost per sqft:', data.data.costPerSqft);
    } else {
      console.error('Error:', data.error);
    }
  } catch (error) {
    console.error('Network error:', error);
  }
}
```

#### Get Projects (with Authentication)
```javascript
async function getProjects() {
  try {
    const response = await fetch('/api/projects', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('jwt_token')}`,
        'Content-Type': 'application/json',
      }
    });
    
    const data = await response.json();
    console.log('Projects:', data.projects);
  } catch (error) {
    console.error('Error fetching projects:', error);
  }
}
```

### Using Python

#### Cost Calculation
```python
import requests
import json

def calculate_cost():
    url = "https://your-domain.com/api/calculate"
    
    payload = {
        "builtUpArea": 1200,
        "floors": 1,
        "qualityTier": "premium",
        "location": "bangalore"
    }
    
    response = requests.post(url, json=payload)
    
    if response.status_code == 200:
        data = response.json()
        if data['success']:
            print(f"Total Cost: ₹{data['data']['totalCost']:,}")
            print(f"Cost per sqft: ₹{data['data']['costPerSqft']:,}")
        else:
            print(f"Error: {data['error']['message']}")
    else:
        print(f"HTTP Error: {response.status_code}")

calculate_cost()
```

### Testing Different Scenarios

#### 1. Smart Quality Tier
```json
{
  "builtUpArea": 1000,
  "floors": 0,
  "qualityTier": "smart",
  "location": "bangalore"
}
```

#### 2. Luxury with Special Features
```json
{
  "builtUpArea": 2000,
  "floors": 2,
  "qualityTier": "luxury",
  "location": "mumbai",
  "hasBasement": true,
  "parkingType": "covered",
  "specialFeatures": [
    {
      "name": "Swimming Pool",
      "cost": 500000,
      "description": "Premium swimming pool"
    }
  ]
}
```

#### 3. Rate Limit Testing
```bash
# Test rate limiting (will fail after 100 requests)
for i in {1..105}; do
  curl -X POST "https://your-domain.com/api/calculate" \
    -H "Content-Type: application/json" \
    -d '{"builtUpArea": 1000, "floors": 1, "qualityTier": "smart", "location": "bangalore"}'
  echo "Request $i completed"
done
```

---

## Best Practices

### Request Guidelines

1. **Always include required fields**: `builtUpArea`, `qualityTier`, `location`
2. **Use proper data types**: Numbers for areas, strings for locations
3. **Handle rate limiting**: Implement exponential backoff for rate limit errors
4. **Validate inputs**: Check constraints before sending requests
5. **Cache responses**: Cache calculation results for identical inputs

### Error Handling

```javascript
async function robustCalculation(input) {
  const maxRetries = 3;
  let retryCount = 0;
  
  while (retryCount < maxRetries) {
    try {
      const response = await fetch('/api/calculate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(input)
      });
      
      if (response.status === 429) {
        // Rate limited - wait and retry
        const retryAfter = response.headers.get('Retry-After');
        await new Promise(resolve => setTimeout(resolve, retryAfter * 1000));
        retryCount++;
        continue;
      }
      
      const data = await response.json();
      if (data.success) {
        return data.data;
      } else {
        throw new Error(data.error.message);
      }
    } catch (error) {
      retryCount++;
      if (retryCount >= maxRetries) {
        throw error;
      }
      await new Promise(resolve => setTimeout(resolve, 1000 * retryCount));
    }
  }
}
```

### Performance Optimization

1. **Batch requests**: Group multiple calculations when possible
2. **Use caching**: Cache results for identical inputs
3. **Monitor metrics**: Track API performance and usage
4. **Implement retry logic**: Handle transient failures gracefully

---

## Supported Locations

| City | Code | Regional Multiplier |
|------|------|-------------------|
| Bangalore | `bangalore` | 1.0x (Base) |
| Mumbai | `mumbai` | 1.2x |
| Delhi | `delhi` | 1.05x |
| Pune | `pune` | 0.95x |
| Hyderabad | `hyderabad` | 0.9x |
| Chennai | `chennai` | 0.95x |
| Kolkata | `kolkata` | 0.85x |
| Ahmedabad | `ahmedabad` | 0.8x |
| Jaipur | `jaipur` | 0.75x |
| Lucknow | `lucknow` | 0.7x |
| Bhubaneswar | `bhubaneswar` | 0.65x |

## Quality Tiers

| Tier | Price Range | Description |
|------|-------------|-------------|
| **Smart** | ₹1,600-2,000/sqft | Standard finishes, basic fittings |
| **Premium** | ₹2,200-2,800/sqft | Branded materials, good finishes |
| **Luxury** | ₹3,000-4,000+/sqft | Premium finishes, high-end fittings |

---

## Changelog

### Version 1.0.0 (2025-01-15)
- Initial API release
- Core calculation engine
- Project management endpoints
- Performance monitoring
- Analytics integration
- Comprehensive error handling
- Rate limiting implementation

---

## Support

For API support, please contact:
- **Email**: <EMAIL>
- **Documentation**: https://docs.nirmaan.ai
- **GitHub**: https://github.com/nirmaan-ai/calculator-api

---

*This documentation is automatically generated and updated. Last updated: 2025-01-15*