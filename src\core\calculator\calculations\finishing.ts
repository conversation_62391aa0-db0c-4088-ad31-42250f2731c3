/**
 * Finishing Cost Calculations
 * Includes flooring, painting, doors, windows, and interior finishes
 */

import type { CalculationInput, CategoryCost, SubCategory } from '../types';
import {
  COST_BREAKDOWN_PERCENTAGES,
  QUALITY_TIER_SPECS,
} from '../constants';

export function calculateFinishingCost(input: CalculationInput): CategoryCost {
  const { builtUpArea, floors, qualityTier, hasBasement = false } = input;

  // Quality tier specific finishing rates per sqft
  const finishingBaseRates = {
    smart: 540, // ₹540/sqft for Smart Choice finishing
    premium: 750, // ₹750/sqft for Premium Selection finishing
    luxury: 1050, // ₹1050/sqft for Luxury Collection finishing
  };

  // Calculate total area including all floors and basement
  let totalArea = builtUpArea * (floors + 1); // +1 for ground floor
  if (hasBasement) {
    totalArea += builtUpArea * 0.8; // Basement finishing is 80% of normal area
  }

  // Get base finishing rate for quality tier
  const finishingCostPerSqft = finishingBaseRates[qualityTier];

  // Total finishing cost
  const totalFinishingCost = totalArea * finishingCostPerSqft;

  // Calculate sub-categories for finishing work
  const subCategories: SubCategory[] = [
    {
      name: 'Flooring',
      amount: Math.round(totalFinishingCost * 0.35),
      percentage: 35,
      description: getFlooringDescription(qualityTier),
    },
    {
      name: 'Painting',
      amount: Math.round(totalFinishingCost * 0.2),
      percentage: 20,
      description: getPaintingDescription(qualityTier),
    },
    {
      name: 'Doors & Windows',
      amount: Math.round(totalFinishingCost * 0.25),
      percentage: 25,
      description: getDoorsWindowsDescription(qualityTier),
    },
    {
      name: 'Kitchen & Bathrooms',
      amount: Math.round(totalFinishingCost * 0.15),
      percentage: 15,
      description: getKitchenBathroomDescription(qualityTier),
    },
    {
      name: 'Interior Finishes',
      amount: Math.round(totalFinishingCost * 0.05),
      percentage: 5,
      description: 'False ceiling, wardrobes, and other interior work',
    },
  ];

  return {
    amount: Math.round(totalFinishingCost),
    percentage: COST_BREAKDOWN_PERCENTAGES.finishing * 100,
    subCategories,
  };
}

function getFlooringDescription(
  qualityTier: 'smart' | 'premium' | 'luxury'
): string {
  const specs = QUALITY_TIER_SPECS[qualityTier];
  return `${specs.flooringOptions.join(' or ')} with skirting`;
}

function getPaintingDescription(
  qualityTier: 'smart' | 'premium' | 'luxury'
): string {
  const specs = QUALITY_TIER_SPECS[qualityTier];
  return `${specs.paintBrand} with primer and putty`;
}

function getDoorsWindowsDescription(
  qualityTier: 'smart' | 'premium' | 'luxury'
): string {
  const doorSpecs = {
    smart: 'Wooden doors with standard hardware, UPVC windows',
    premium: 'Hardwood doors with quality hardware, UPVC/Aluminum windows',
    luxury: 'Premium hardwood doors with designer hardware, Aluminum windows',
  };
  return doorSpecs[qualityTier];
}

function getKitchenBathroomDescription(
  qualityTier: 'smart' | 'premium' | 'luxury'
): string {
  const specs = QUALITY_TIER_SPECS[qualityTier];
  return `Kitchen tiles, bathroom fittings (${specs.fixtures}), sanitary ware`;
}

/**
 * Calculate flooring cost based on area and quality tier
 */
export function calculateFlooringCost(
  area: number,
  qualityTier: 'smart' | 'premium' | 'luxury',
  roomType: 'living' | 'bedroom' | 'kitchen' | 'bathroom' = 'living'
): number {
  const flooringRates = {
    smart: {
      living: 85, // Vitrified tiles
      bedroom: 85, // Vitrified tiles
      kitchen: 95, // Anti-skid tiles
      bathroom: 105, // Anti-skid + waterproof
    },
    premium: {
      living: 150, // Premium vitrified/granite
      bedroom: 140, // Premium tiles
      kitchen: 160, // Premium kitchen tiles
      bathroom: 180, // Premium bathroom tiles
    },
    luxury: {
      living: 300, // Italian marble/imported tiles
      bedroom: 280, // Premium hardwood/marble
      kitchen: 320, // Imported kitchen tiles
      bathroom: 350, // Premium bathroom tiles
    },
  };

  return area * flooringRates[qualityTier][roomType];
}

/**
 * Calculate painting cost including preparation work
 */
export function calculatePaintingCost(
  wallArea: number,
  qualityTier: 'smart' | 'premium' | 'luxury',
  isExterior: boolean = false
): number {
  const paintingRates = {
    smart: {
      interior: 35, // Basic emulsion with putty
      exterior: 45, // Weather shield paint
    },
    premium: {
      interior: 55, // Premium emulsion with primer
      exterior: 70, // Premium exterior paint
    },
    luxury: {
      interior: 85, // Designer paint with texture
      exterior: 110, // Premium weather protection
    },
  };

  const rateType = isExterior ? 'exterior' : 'interior';
  return wallArea * paintingRates[qualityTier][rateType];
}

/**
 * Calculate doors and windows cost
 */
export function calculateDoorsWindowsCost(input: {
  doors: number;
  windows: number;
  qualityTier: 'smart' | 'premium' | 'luxury';
}): { doorsCost: number; windowsCost: number; total: number } {
  const { doors, windows, qualityTier } = input;

  const doorRates = {
    smart: 8500, // Standard wooden door with frame
    premium: 15000, // Hardwood door with quality hardware
    luxury: 25000, // Premium hardwood with designer hardware
  };

  const windowRates = {
    smart: 4500, // UPVC window per sqm
    premium: 7500, // Premium UPVC/Aluminum
    luxury: 12000, // Premium aluminum with mosquito mesh
  };

  const doorsCost = doors * doorRates[qualityTier];
  const windowsCost = windows * windowRates[qualityTier];

  return {
    doorsCost,
    windowsCost,
    total: doorsCost + windowsCost,
  };
}
