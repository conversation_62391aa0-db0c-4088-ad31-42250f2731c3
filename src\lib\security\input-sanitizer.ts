/**
 * Advanced Input Sanitization and Validation
 * Comprehensive protection against injection attacks and malicious input
 */

// Note: DOMPurify and validator are optional dependencies
// The sanitizer will work with basic implementations if not available
let DOMPurify: any = null;
let validator: any = null;

try {
  DOMPurify = require('dompurify');
} catch {
  // DOMPurify not available, will use fallback
}

try {
  validator = require('validator');
} catch {
  // Validator not available, will use fallback
  validator = null;
}

interface SanitizationConfig {
  allowHTML: boolean;
  allowedTags: string[];
  allowedAttributes: Record<string, string[]>;
  maxLength: number;
  stripWhitespace: boolean;
  normalizeUnicode: boolean;
}

interface ValidationRule {
  name: string;
  validator: (value: any) => boolean;
  message: string;
  sanitizer?: (value: any) => any;
}

interface SanitizationResult {
  sanitized: any;
  isValid: boolean;
  warnings: string[];
  errors: string[];
  originalLength: number;
  sanitizedLength: number;
}

export class InputSanitizer {
  private defaultConfig: SanitizationConfig = {
    allowHTML: false,
    allowedTags: ['b', 'i', 'em', 'strong', 'p', 'br'],
    allowedAttributes: {
      'a': ['href', 'title'],
      'img': ['src', 'alt', 'title'],
    },
    maxLength: 10000,
    stripWhitespace: true,
    normalizeUnicode: true,
  };

  private dangerousPatterns = [
    // SQL Injection patterns
    /(\bunion\b.+\bselect\b)|(\bselect\b.+\bunion\b)/i,
    /(\binsert\b|\bupdate\b|\bdelete\b|\bdrop\b|\bcreate\b|\balter\b)/i,
    /(sleep|benchmark|pg_sleep|waitfor delay)/i,
    /(extractvalue|updatexml|floor|rand)/i,
    
    // XSS patterns
    /<script[^>]*>.*?<\/script>/gi,
    /<iframe[^>]*>.*?<\/iframe>/gi,
    /<object[^>]*>.*?<\/object>/gi,
    /<embed[^>]*>/gi,
    /javascript\s*:/gi,
    /on\w+\s*=/gi,
    /expression\s*\(/gi,
    
    // Command injection patterns
    /(;|\||&|`|\$\(|eval|exec|system)/i,
    /(rm\s|del\s|format\s|shutdown\s)/i,
    
    // Path traversal patterns
    /(\.\.[\/\\]|%2e%2e[\/\\]|\.%2e[\/\\])/i,
    
    // Template injection patterns
    /(\{\{.*\}\}|\{%.*%\}|<%.*%>|\$\{.*\})/i,
    
    // LDAP injection patterns
    /(\*\)|\(\||\)\(|&\(|\!\()/i,
    
    // XML injection patterns
    /(<\?xml|<!DOCTYPE|<!--.*-->|<!\[CDATA\[)/i,
  ];

  private sqlKeywords = [
    'SELECT', 'INSERT', 'UPDATE', 'DELETE', 'DROP', 'CREATE', 'ALTER',
    'UNION', 'WHERE', 'FROM', 'JOIN', 'HAVING', 'GROUP BY', 'ORDER BY',
    'EXEC', 'EXECUTE', 'CAST', 'CONVERT', 'SUBSTRING', 'CONCAT',
  ];

  /**
   * Sanitize input based on configuration
   */
  sanitize(input: any, config: Partial<SanitizationConfig> = {}): SanitizationResult {
    const finalConfig = { ...this.defaultConfig, ...config };
    const warnings: string[] = [];
    const errors: string[] = [];
    
    if (input === null || input === undefined) {
      return {
        sanitized: input,
        isValid: true,
        warnings,
        errors,
        originalLength: 0,
        sanitizedLength: 0,
      };
    }

    let sanitized = input;
    const originalLength = String(input).length;

    try {
      // Convert to string for processing
      if (typeof sanitized !== 'string') {
        sanitized = String(sanitized);
      }

      // Normalize Unicode
      if (finalConfig.normalizeUnicode) {
        sanitized = sanitized.normalize('NFKC');
      }

      // Check for dangerous patterns
      const dangerousFound = this.detectDangerousPatterns(sanitized);
      if (dangerousFound.length > 0) {
        errors.push(`Dangerous patterns detected: ${dangerousFound.join(', ')}`);
        // Remove dangerous patterns
        sanitized = this.removeDangerousPatterns(sanitized);
        warnings.push('Dangerous patterns removed from input');
      }

      // HTML sanitization
      if (finalConfig.allowHTML) {
        sanitized = this.sanitizeHTML(sanitized, finalConfig);
      } else {
        // Strip all HTML
        sanitized = this.stripHTML(sanitized);
      }

      // SQL injection protection
      sanitized = this.protectAgainstSQLInjection(sanitized);

      // XSS protection
      sanitized = this.protectAgainstXSS(sanitized);

      // Command injection protection
      sanitized = this.protectAgainstCommandInjection(sanitized);

      // Path traversal protection
      sanitized = this.protectAgainstPathTraversal(sanitized);

      // Length validation
      if (sanitized.length > finalConfig.maxLength) {
        warnings.push(`Input truncated from ${sanitized.length} to ${finalConfig.maxLength} characters`);
        sanitized = sanitized.substring(0, finalConfig.maxLength);
      }

      // Whitespace handling
      if (finalConfig.stripWhitespace) {
        sanitized = sanitized.trim();
        sanitized = sanitized.replace(/\s+/g, ' '); // Normalize whitespace
      }

      // Final validation
      const isValid = errors.length === 0;

      return {
        sanitized,
        isValid,
        warnings,
        errors,
        originalLength,
        sanitizedLength: sanitized.length,
      };

    } catch (error) {
      errors.push(`Sanitization error: ${error instanceof Error ? error.message : 'Unknown error'}`);
      
      return {
        sanitized: '',
        isValid: false,
        warnings,
        errors,
        originalLength,
        sanitizedLength: 0,
      };
    }
  }

  /**
   * Sanitize specific data types
   */
  sanitizeEmail(email: string): SanitizationResult {
    const result = this.sanitize(email, { maxLength: 254, stripWhitespace: true });
    
    if (result.isValid) {
      const isValidEmail = validator 
        ? validator.isEmail(result.sanitized)
        : this.basicEmailValidation(result.sanitized);
      
      if (!isValidEmail) {
        result.errors.push('Invalid email format');
        result.isValid = false;
      }
    }
    
    return result;
  }

  sanitizeURL(url: string): SanitizationResult {
    const result = this.sanitize(url, { maxLength: 2048, stripWhitespace: true });
    
    if (result.isValid) {
      try {
        const parsedURL = new URL(result.sanitized);
        
        // Block dangerous protocols
        const allowedProtocols = ['http:', 'https:', 'mailto:', 'tel:'];
        if (!allowedProtocols.includes(parsedURL.protocol)) {
          result.errors.push(`Protocol '${parsedURL.protocol}' is not allowed`);
          result.isValid = false;
        }
        
        // Block suspicious domains
        const suspiciousDomains = ['localhost', '127.0.0.1', '0.0.0.0'];
        if (suspiciousDomains.includes(parsedURL.hostname) && process.env.NODE_ENV === 'production') {
          result.errors.push('Suspicious domain detected');
          result.isValid = false;
        }
        
      } catch (error) {
        result.errors.push('Invalid URL format');
        result.isValid = false;
      }
    }
    
    return result;
  }

  sanitizePhoneNumber(phone: string): SanitizationResult {
    let sanitized = phone.replace(/[^\d+\-\s\(\)]/g, ''); // Keep only numbers and basic formatting
    
    const result = this.sanitize(sanitized, { maxLength: 20, stripWhitespace: true });
    
    // Basic phone number validation
    if (result.isValid && !/^[\+]?[\d\s\-\(\)]{7,20}$/.test(result.sanitized)) {
      result.errors.push('Invalid phone number format');
      result.isValid = false;
    }
    
    return result;
  }

  sanitizeConstructionInput(input: any): SanitizationResult {
    if (typeof input === 'object' && input !== null) {
      const sanitizedInput: any = {};
      const warnings: string[] = [];
      const errors: string[] = [];
      let isValid = true;

      // Sanitize each field
      for (const [key, value] of Object.entries(input)) {
        const fieldResult = this.sanitizeConstructionField(key, value);
        sanitizedInput[key] = fieldResult.sanitized;
        warnings.push(...fieldResult.warnings);
        errors.push(...fieldResult.errors);
        
        if (!fieldResult.isValid) {
          isValid = false;
        }
      }

      return {
        sanitized: sanitizedInput,
        isValid,
        warnings,
        errors,
        originalLength: JSON.stringify(input).length,
        sanitizedLength: JSON.stringify(sanitizedInput).length,
      };
    }

    return this.sanitize(input);
  }

  /**
   * Detect dangerous patterns in input
   */
  private detectDangerousPatterns(input: string): string[] {
    const found: string[] = [];
    
    this.dangerousPatterns.forEach((pattern, index) => {
      if (pattern.test(input)) {
        found.push(`Pattern ${index + 1}`);
      }
    });

    // Check for SQL keywords
    const upperInput = input.toUpperCase();
    this.sqlKeywords.forEach(keyword => {
      if (upperInput.includes(keyword)) {
        found.push(`SQL keyword: ${keyword}`);
      }
    });

    return found;
  }

  /**
   * Remove dangerous patterns from input
   */
  private removeDangerousPatterns(input: string): string {
    let cleaned = input;
    
    this.dangerousPatterns.forEach(pattern => {
      cleaned = cleaned.replace(pattern, '');
    });

    return cleaned;
  }

  /**
   * Sanitize HTML content
   */
  private sanitizeHTML(input: string, config: SanitizationConfig): string {
    if (typeof window === 'undefined' || !DOMPurify) {
      // Server-side or DOMPurify not available: basic HTML stripping
      return this.stripHTML(input);
    }

    // Client-side: use DOMPurify if available
    const purifyConfig = {
      ALLOWED_TAGS: config.allowedTags,
      ALLOWED_ATTR: Object.values(config.allowedAttributes).flat(),
      RETURN_DOM: false,
      RETURN_DOM_FRAGMENT: false,
      RETURN_DOM_IMPORT: false,
    };

    return DOMPurify.sanitize(input, purifyConfig);
  }

  /**
   * Basic email validation fallback
   */
  private basicEmailValidation(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email) && email.length <= 254;
  }

  /**
   * Strip all HTML tags
   */
  private stripHTML(input: string): string {
    return input.replace(/<[^>]*>/g, '');
  }

  /**
   * Protect against SQL injection
   */
  private protectAgainstSQLInjection(input: string): string {
    // Remove SQL comments
    let cleaned = input.replace(/--.*$/gm, '');
    cleaned = cleaned.replace(/\/\*.*?\*\//gs, '');
    
    // Escape SQL special characters
    cleaned = cleaned.replace(/'/g, "''");
    cleaned = cleaned.replace(/;/g, '\\;');
    
    return cleaned;
  }

  /**
   * Protect against XSS
   */
  private protectAgainstXSS(input: string): string {
    // Encode dangerous characters
    const entityMap: Record<string, string> = {
      '&': '&amp;',
      '<': '&lt;',
      '>': '&gt;',
      '"': '&quot;',
      "'": '&#39;',
      '/': '&#x2F;',
      '`': '&#x60;',
      '=': '&#x3D;',
    };

    return input.replace(/[&<>"'`=\/]/g, (char) => entityMap[char]);
  }

  /**
   * Protect against command injection
   */
  private protectAgainstCommandInjection(input: string): string {
    // Remove command injection characters
    return input.replace(/[;|&`$(){}[\]\\]/g, '');
  }

  /**
   * Protect against path traversal
   */
  private protectAgainstPathTraversal(input: string): string {
    // Remove path traversal sequences
    let cleaned = input.replace(/\.\.[\/\\]/g, '');
    cleaned = cleaned.replace(/%2e%2e[\/\\]/gi, '');
    cleaned = cleaned.replace(/\.%2e[\/\\]/gi, '');
    
    return cleaned;
  }

  /**
   * Sanitize specific construction calculator fields
   */
  private sanitizeConstructionField(key: string, value: any): SanitizationResult {
    switch (key) {
      case 'builtUpArea':
      case 'plotSize':
      case 'floors':
        return this.sanitizeNumber(value, 1, 100000);
      
      case 'location':
      case 'qualityTier':
        return this.sanitize(value, { maxLength: 100, stripWhitespace: true });
      
      case 'email':
        return this.sanitizeEmail(value);
      
      case 'phone':
        return this.sanitizePhoneNumber(value);
      
      case 'projectName':
      case 'description':
        return this.sanitize(value, { maxLength: 500, stripWhitespace: true });
      
      default:
        return this.sanitize(value);
    }
  }

  /**
   * Sanitize numeric input
   */
  private sanitizeNumber(value: any, min?: number, max?: number): SanitizationResult {
    const warnings: string[] = [];
    const errors: string[] = [];
    
    let sanitized = value;
    
    // Convert to number
    if (typeof sanitized !== 'number') {
      sanitized = Number(sanitized);
    }
    
    // Validate number
    if (isNaN(sanitized) || !isFinite(sanitized)) {
      errors.push('Invalid number format');
      sanitized = 0;
    }
    
    // Check range
    if (min !== undefined && sanitized < min) {
      warnings.push(`Value ${sanitized} is below minimum ${min}`);
      sanitized = min;
    }
    
    if (max !== undefined && sanitized > max) {
      warnings.push(`Value ${sanitized} is above maximum ${max}`);
      sanitized = max;
    }
    
    return {
      sanitized,
      isValid: errors.length === 0,
      warnings,
      errors,
      originalLength: String(value).length,
      sanitizedLength: String(sanitized).length,
    };
  }

  /**
   * Validate and sanitize file uploads
   */
  sanitizeFileUpload(file: File, allowedTypes: string[], maxSize: number): SanitizationResult {
    const warnings: string[] = [];
    const errors: string[] = [];
    
    // Check file type
    if (!allowedTypes.includes(file.type)) {
      errors.push(`File type ${file.type} is not allowed`);
    }
    
    // Check file size
    if (file.size > maxSize) {
      errors.push(`File size ${file.size} exceeds maximum ${maxSize}`);
    }
    
    // Check filename
    const filenameResult = this.sanitize(file.name, { maxLength: 255 });
    if (!filenameResult.isValid) {
      errors.push('Invalid filename');
    }
    
    warnings.push(...filenameResult.warnings);
    
    return {
      sanitized: file,
      isValid: errors.length === 0,
      warnings,
      errors,
      originalLength: file.size,
      sanitizedLength: file.size,
    };
  }
}

// Export singleton instance
export const inputSanitizer = new InputSanitizer();

// Export types
export type { SanitizationConfig, ValidationRule, SanitizationResult };