/**
 * Materials Module Entry Point
 * Exports all material-related functionality for the construction calculator
 */

// Core material management
export { materialManager, MaterialManager } from './loader';
export {
  getAllMaterials,
  getMaterialById,
  searchMaterials,
  getMaterialPrice,
  calculateMaterialCost,
  getPopularMaterials,
  getCategories,
  getBrands
} from './loader';

// Export enhanced search functions
export const searchBySpecifications = (key: string, value: string) => materialManager.searchBySpecifications(key, value);
export const filterByPriceRange = (minPrice: number, maxPrice: number, region?: string, purchaseType?: any) => materialManager.filterByPriceRange(minPrice, maxPrice, region, purchaseType);
export const getMaterialsByAvailability = (availability: any) => materialManager.getMaterialsByAvailability(availability);
export const searchMaterialsByRegion = (region: string) => materialManager.searchMaterialsByRegion(region);
export const getMaterialsWithSeasonalPricing = () => materialManager.getMaterialsWithSeasonalPricing();
export const getSeasonalPrice = (materialId: string, season: 'summer' | 'monsoon' | 'winter', region?: string, purchaseType?: any) => materialManager.getSeasonalPrice(materialId, season, region, purchaseType);
export const advancedSearch = (filters: any) => materialManager.advancedSearch(filters);

// Material consumption and calculations
export {
  calculateMaterialRequirements,
  calculateConstructionAreas,
  getMaterialAlternatives,
  MaterialConsumptionUtils,
  QUALITY_TIER_MULTIPLIERS,
  MATERIAL_CONSUMPTION_RATES
} from './consumption';

// Integration with calculation engine
export {
  MaterialCostCalculator,
  calculateProjectMaterialCosts,
  generateMaterialCostBreakdown,
  generateMaterialQuantities,
  getSupplierRecommendations,
  calculateCostOptimization
} from './integration';

// Types
export type {
  Material,
  MaterialDatabase,
  MaterialSearchCriteria,
  MaterialSearchResult,
  MaterialCostBreakdown,
  MaterialConsumption,
  RegionalPricing,
  MaterialSpecifications,
  MaterialPricing,
  ProjectMaterialRequirements,
  MaterialAlternative,
  MaterialMarketTrend,
  MaterialSupplier,
  BulkPricingTier,
  MaterialWithBulkPricing,
  QualityCertification,
  CertifiedMaterial,
  MaterialUsageStats,
  MaterialsData
} from '../../types/materials';

// Import Material type for use in this file
import type { Material } from '../../types/materials';

// Enums
export { MaterialCategory, PurchaseType, AvailabilityLevel } from '../../types/materials';

// Version and metadata
export const MATERIALS_VERSION = '1.0.0';
export const MATERIALS_LAST_UPDATED = '2025-07-13';

/**
 * Quick material cost estimation function
 */
export async function quickMaterialEstimate(
  builtUpArea: number,
  qualityTier: 'smart' | 'premium' | 'luxury' = 'smart',
  region: string = 'bangalore'
): Promise<{
  totalMaterialCost: number;
  costPerSqft: number;
  categoryBreakdown: Record<string, number>;
  qualityScore: number;
}> {
  try {
    const { MaterialCostCalculator } = await import('./integration');

    const mockInput = {
      builtUpArea,
      floors: 0,
      qualityTier,
      location: region
    };

    const result = MaterialCostCalculator.calculateProjectMaterialCosts(mockInput);

    return {
      totalMaterialCost: result.totalMaterialCost,
      costPerSqft: result.costPerSqft,
      categoryBreakdown: result.categoryTotals,
      qualityScore: result.qualityAnalysis.averageQualityScore
    };
  } catch (error) {
    throw new Error(
      `Quick material estimate failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

/**
 * Get material database statistics
 */
export function getMaterialDatabaseStats(): {
  totalMaterials: number;
  categories: number;
  brands: number;
  regions: number;
  averageQuality: number;
  priceRange: { min: number; max: number };
} {
  // Import at function level to avoid circular dependency
  const loader = require('./loader');
  const manager = loader.materialManager;
  const materials = manager.getAllMaterials();
  const categories = manager.getCategories();
  const brands = manager.getBrands();
  const regions = manager.getAvailableRegions();

  const qualityScores = materials.map((m: Material) => m.qualityScore);
  const averageQuality = qualityScores.reduce((sum: number, score: number) => sum + score, 0) / qualityScores.length;

  const prices = materials.map((m: Material) => m.pricing.default.retail);
  const priceRange = {
    min: Math.min(...prices),
    max: Math.max(...prices)
  };

  return {
    totalMaterials: materials.length,
    categories: categories.length,
    brands: brands.length,
    regions: regions.length,
    averageQuality: Math.round(averageQuality * 10) / 10,
    priceRange
  };
}

/**
 * Validate material configuration and data integrity
 */
export function validateMaterialDatabase(): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  stats: ReturnType<typeof getMaterialDatabaseStats>;
} {
  const loader = require('./loader');
  const manager = loader.materialManager;
  const validation = manager.validateMaterialData();
  const stats = getMaterialDatabaseStats();

  return {
    ...validation,
    stats
  };
}

/**
 * Get recommended materials for a construction project
 */
export function getRecommendedMaterials(
  projectType: 'residential' | 'commercial' | 'industrial',
  qualityTier: 'smart' | 'premium' | 'luxury',
  budget?: number
): Material[] {
  const loader = require('./loader');
  const manager = loader.materialManager;
  const materials = manager.getAllMaterials();

  // Quality tier filtering
  const minQualityScore = qualityTier === 'luxury' ? 9.0 : qualityTier === 'premium' ? 8.0 : 6.0;

  // Filter by quality and popularity
  let filtered = materials.filter((m: Material) =>
    m.qualityScore >= minQualityScore &&
    m.availability === 'High'
  );

  // Budget filtering if provided
  if (budget) {
    const avgBudgetPerMaterial = budget / 20; // Rough estimate
    filtered = filtered.filter((m: Material) => m.pricing.default.bulk <= avgBudgetPerMaterial);
  }

  // Sort by popularity and quality
  return filtered
    .sort((a: Material, b: Material) => {
      const popularityWeight = 0.3;
      const qualityWeight = 0.7;

      const scoreA = (a.popularityRank * popularityWeight) + (a.qualityScore * qualityWeight);
      const scoreB = (b.popularityRank * popularityWeight) + (b.qualityScore * qualityWeight);

      return scoreB - scoreA;
    })
    .slice(0, 15); // Top 15 recommendations
}

// Export default
const materialsModule = {
  quickMaterialEstimate,
  getMaterialDatabaseStats,
  validateMaterialDatabase,
  getRecommendedMaterials,
  MATERIALS_VERSION,
  MATERIALS_LAST_UPDATED
};

export default materialsModule;