/**
 * CORS Configuration and IP Geofencing
 * Advanced Cross-Origin Resource Sharing and geographical access control
 */

interface CORSConfig {
  allowedOrigins: string[];
  allowedMethods: string[];
  allowedHeaders: string[];
  exposedHeaders: string[];
  credentials: boolean;
  maxAge: number;
  preflightContinue: boolean;
  optionsSuccessStatus: number;
}

interface GeoFencingConfig {
  allowedCountries: string[];
  blockedCountries: string[];
  allowedRegions: Record<string, string[]>;
  blockedRegions: Record<string, string[]>;
  defaultAction: 'allow' | 'block';
  whitelistedIPs: string[];
  blacklistedIPs: string[];
}

interface IPInfo {
  ip: string;
  country?: string;
  region?: string;
  city?: string;
  isp?: string;
  organization?: string;
  isVPN?: boolean;
  isProxy?: boolean;
  isTor?: boolean;
  threatLevel: 'low' | 'medium' | 'high' | 'critical';
}

interface CORSResult {
  allowed: boolean;
  headers: Record<string, string>;
  reason?: string;
}

interface GeoResult {
  allowed: boolean;
  reason?: string;
  ipInfo: IPInfo;
  riskScore: number;
}

export class CORSManager {
  private config: CORSConfig;
  private geoConfig: GeoFencingConfig;

  constructor(config?: Partial<CORSConfig>, geoConfig?: Partial<GeoFencingConfig>) {
    this.config = {
      allowedOrigins: this.getDefaultAllowedOrigins(),
      allowedMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      allowedHeaders: [
        'Content-Type',
        'Authorization',
        'X-Requested-With',
        'X-CSRF-Token',
        'X-API-Key',
        'Accept',
        'Origin',
        'DNT',
        'User-Agent',
        'If-Modified-Since',
        'Cache-Control',
        'Range'
      ],
      exposedHeaders: [
        'X-RateLimit-Limit',
        'X-RateLimit-Remaining',
        'X-RateLimit-Reset',
        'X-Total-Count',
        'X-Request-ID'
      ],
      credentials: true,
      maxAge: 86400, // 24 hours
      preflightContinue: false,
      optionsSuccessStatus: 200,
      ...config,
    };

    this.geoConfig = {
      allowedCountries: ['IN', 'US', 'CA', 'GB', 'AU', 'DE', 'FR', 'JP', 'SG', 'AE', 'NL', 'SE', 'CH'],
      blockedCountries: [], // Can be populated based on security requirements
      allowedRegions: {},
      blockedRegions: {},
      defaultAction: 'allow',
      whitelistedIPs: [],
      blacklistedIPs: [],
      ...geoConfig,
    };
  }

  /**
   * Check CORS policy and return appropriate headers
   */
  checkCORS(origin: string, method: string, headers: string[]): CORSResult {
    // Check if origin is allowed
    if (!this.isOriginAllowed(origin)) {
      return {
        allowed: false,
        headers: {},
        reason: `Origin ${origin} not allowed`,
      };
    }

    // Check if method is allowed
    if (!this.config.allowedMethods.includes(method)) {
      return {
        allowed: false,
        headers: {},
        reason: `Method ${method} not allowed`,
      };
    }

    // Check if headers are allowed
    const disallowedHeaders = headers.filter(header => 
      !this.config.allowedHeaders.includes(header.toLowerCase())
    );

    if (disallowedHeaders.length > 0) {
      return {
        allowed: false,
        headers: {},
        reason: `Headers not allowed: ${disallowedHeaders.join(', ')}`,
      };
    }

    // Generate CORS headers
    const corsHeaders: Record<string, string> = {
      'Access-Control-Allow-Origin': this.getOriginHeader(origin),
      'Access-Control-Allow-Methods': this.config.allowedMethods.join(', '),
      'Access-Control-Allow-Headers': this.config.allowedHeaders.join(', '),
      'Access-Control-Expose-Headers': this.config.exposedHeaders.join(', '),
      'Access-Control-Max-Age': this.config.maxAge.toString(),
      'Vary': 'Origin, Access-Control-Request-Method, Access-Control-Request-Headers',
    };

    if (this.config.credentials) {
      corsHeaders['Access-Control-Allow-Credentials'] = 'true';
    }

    return {
      allowed: true,
      headers: corsHeaders,
    };
  }

  /**
   * Check geographical access restrictions
   */
  async checkGeoFencing(ip: string): Promise<GeoResult> {
    // Check whitelist first
    if (this.geoConfig.whitelistedIPs.includes(ip)) {
      return {
        allowed: true,
        ipInfo: { ip, threatLevel: 'low' },
        riskScore: 0,
      };
    }

    // Check blacklist
    if (this.geoConfig.blacklistedIPs.includes(ip)) {
      return {
        allowed: false,
        reason: 'IP is blacklisted',
        ipInfo: { ip, threatLevel: 'critical' },
        riskScore: 100,
      };
    }

    // Get IP information
    const ipInfo = await this.getIPInfo(ip);
    const riskScore = this.calculateRiskScore(ipInfo);

    // Check country restrictions
    if (ipInfo.country) {
      if (this.geoConfig.blockedCountries.includes(ipInfo.country)) {
        return {
          allowed: false,
          reason: `Country ${ipInfo.country} is blocked`,
          ipInfo,
          riskScore,
        };
      }

      if (this.geoConfig.allowedCountries.length > 0 && 
          !this.geoConfig.allowedCountries.includes(ipInfo.country)) {
        return {
          allowed: false,
          reason: `Country ${ipInfo.country} is not in allowed list`,
          ipInfo,
          riskScore,
        };
      }
    }

    // Check region restrictions
    if (ipInfo.country && ipInfo.region) {
      const blockedRegions = this.geoConfig.blockedRegions[ipInfo.country] || [];
      if (blockedRegions.includes(ipInfo.region)) {
        return {
          allowed: false,
          reason: `Region ${ipInfo.region} in ${ipInfo.country} is blocked`,
          ipInfo,
          riskScore,
        };
      }

      const allowedRegions = this.geoConfig.allowedRegions[ipInfo.country] || [];
      if (allowedRegions.length > 0 && !allowedRegions.includes(ipInfo.region)) {
        return {
          allowed: false,
          reason: `Region ${ipInfo.region} in ${ipInfo.country} is not allowed`,
          ipInfo,
          riskScore,
        };
      }
    }

    // Check threat level
    if (ipInfo.threatLevel === 'critical' || riskScore > 80) {
      return {
        allowed: false,
        reason: 'High threat level detected',
        ipInfo,
        riskScore,
      };
    }

    return {
      allowed: true,
      ipInfo,
      riskScore,
    };
  }

  /**
   * Handle preflight requests
   */
  handlePreflight(origin: string, method: string, headers: string[]): Response {
    const corsResult = this.checkCORS(origin, method, headers);
    
    if (!corsResult.allowed) {
      return new Response(null, {
        status: 403,
        statusText: 'CORS policy violation',
        headers: {
          'Content-Type': 'text/plain',
        },
      });
    }

    return new Response(null, {
      status: this.config.optionsSuccessStatus,
      headers: corsResult.headers,
    });
  }

  /**
   * Apply CORS headers to response
   */
  applyCORSHeaders(response: Response, origin: string): Response {
    const corsResult = this.checkCORS(origin, 'GET', []);
    
    if (corsResult.allowed) {
      Object.entries(corsResult.headers).forEach(([key, value]) => {
        response.headers.set(key, value);
      });
    }

    return response;
  }

  /**
   * Get default allowed origins based on environment
   */
  private getDefaultAllowedOrigins(): string[] {
    const origins = [];

    // Production origins
    if (process.env.NEXT_PUBLIC_DOMAIN) {
      origins.push(`https://${process.env.NEXT_PUBLIC_DOMAIN}`);
      origins.push(`https://www.${process.env.NEXT_PUBLIC_DOMAIN}`);
    }

    // Staging/development origins
    if (process.env.NODE_ENV === 'development') {
      origins.push('http://localhost:3000');
      origins.push('http://localhost:3001');
      origins.push('http://127.0.0.1:3000');
    }

    // Vercel preview URLs
    if (process.env.VERCEL_URL) {
      origins.push(`https://${process.env.VERCEL_URL}`);
    }

    return origins;
  }

  /**
   * Check if origin is allowed
   */
  private isOriginAllowed(origin: string): boolean {
    if (!origin) return false;

    // Exact match
    if (this.config.allowedOrigins.includes(origin)) {
      return true;
    }

    // Wildcard support
    return this.config.allowedOrigins.some(allowedOrigin => {
      if (allowedOrigin === '*') return true;
      if (allowedOrigin.includes('*')) {
        const regex = new RegExp(allowedOrigin.replace(/\*/g, '.*'));
        return regex.test(origin);
      }
      return false;
    });
  }

  /**
   * Get appropriate origin header value
   */
  private getOriginHeader(origin: string): string {
    // If credentials are enabled, we cannot use '*'
    if (this.config.credentials) {
      return origin;
    }

    // Check if we can use wildcard
    if (this.config.allowedOrigins.includes('*')) {
      return '*';
    }

    return origin;
  }

  /**
   * Get IP information (simplified implementation)
   */
  private async getIPInfo(ip: string): Promise<IPInfo> {
    // In production, you would use a real geolocation service
    // like MaxMind, IPInfo, or similar
    
    // Simplified implementation for common cases
    if (ip === '127.0.0.1' || ip === '::1' || ip.startsWith('192.168.') || ip.startsWith('10.')) {
      return {
        ip,
        country: 'US', // Default for local IPs
        threatLevel: 'low',
      };
    }

    // Mock implementation - in production, replace with actual geolocation service
    const mockIPInfo: IPInfo = {
      ip,
      country: 'IN', // Default to India for Nirmaan AI
      region: 'Delhi',
      city: 'New Delhi',
      threatLevel: 'low',
      isVPN: false,
      isProxy: false,
      isTor: false,
    };

    // Simple threat detection based on IP patterns
    if (this.isKnownMaliciousIP(ip)) {
      mockIPInfo.threatLevel = 'critical';
    } else if (this.isSuspiciousIP(ip)) {
      mockIPInfo.threatLevel = 'medium';
    }

    return mockIPInfo;
  }

  /**
   * Calculate risk score based on IP information
   */
  private calculateRiskScore(ipInfo: IPInfo): number {
    let score = 0;

    // Base threat level scoring
    switch (ipInfo.threatLevel) {
      case 'critical': score += 80; break;
      case 'high': score += 60; break;
      case 'medium': score += 30; break;
      case 'low': score += 0; break;
    }

    // VPN/Proxy scoring
    if (ipInfo.isVPN) score += 20;
    if (ipInfo.isProxy) score += 25;
    if (ipInfo.isTor) score += 40;

    // Country-based scoring
    const highRiskCountries = ['CN', 'RU', 'KP', 'IR'];
    if (ipInfo.country && highRiskCountries.includes(ipInfo.country)) {
      score += 30;
    }

    return Math.min(100, score);
  }

  /**
   * Check if IP is known to be malicious
   */
  private isKnownMaliciousIP(ip: string): boolean {
    // In production, this would check against threat intelligence feeds
    const knownMaliciousRanges: string[] = [
      // Add known malicious IP ranges
    ];

    return knownMaliciousRanges.some(range => ip.startsWith(range));
  }

  /**
   * Check if IP is suspicious
   */
  private isSuspiciousIP(ip: string): boolean {
    // Check for suspicious patterns
    const suspiciousPatterns = [
      /^0\./, // Invalid range
      /^127\./, // Localhost (suspicious if from external)
      /^169\.254\./, // Link-local
      /^224\./, // Multicast
    ];

    return suspiciousPatterns.some(pattern => pattern.test(ip));
  }

  /**
   * Update configuration
   */
  updateConfig(config: Partial<CORSConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * Update geo-fencing configuration
   */
  updateGeoConfig(geoConfig: Partial<GeoFencingConfig>): void {
    this.geoConfig = { ...this.geoConfig, ...geoConfig };
  }

  /**
   * Add IP to whitelist
   */
  whitelistIP(ip: string): void {
    if (!this.geoConfig.whitelistedIPs.includes(ip)) {
      this.geoConfig.whitelistedIPs.push(ip);
    }
  }

  /**
   * Add IP to blacklist
   */
  blacklistIP(ip: string): void {
    if (!this.geoConfig.blacklistedIPs.includes(ip)) {
      this.geoConfig.blacklistedIPs.push(ip);
    }
  }

  /**
   * Remove IP from whitelist
   */
  removeFromWhitelist(ip: string): void {
    this.geoConfig.whitelistedIPs = this.geoConfig.whitelistedIPs.filter(whitelistedIP => whitelistedIP !== ip);
  }

  /**
   * Remove IP from blacklist
   */
  removeFromBlacklist(ip: string): void {
    this.geoConfig.blacklistedIPs = this.geoConfig.blacklistedIPs.filter(blacklistedIP => blacklistedIP !== ip);
  }

  /**
   * Get current configuration
   */
  getConfig(): { cors: CORSConfig; geo: GeoFencingConfig } {
    return {
      cors: { ...this.config },
      geo: { ...this.geoConfig },
    };
  }

  /**
   * Validate configuration
   */
  validateConfig(): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Validate CORS config
    if (this.config.allowedOrigins.length === 0) {
      errors.push('At least one allowed origin must be specified');
    }

    if (this.config.allowedMethods.length === 0) {
      errors.push('At least one allowed method must be specified');
    }

    if (this.config.maxAge < 0) {
      errors.push('Max age cannot be negative');
    }

    // Validate geo config
    if (this.geoConfig.allowedCountries.length === 0 && this.geoConfig.defaultAction === 'block') {
      errors.push('When default action is block, at least one country must be allowed');
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }
}

// Export singleton instance
export const corsManager = new CORSManager();

// Export utility functions
export const corsUtils = {
  /**
   * Check if request is a preflight request
   */
  isPreflight: (method: string, headers: Headers): boolean => {
    return method === 'OPTIONS' && 
           headers.has('Access-Control-Request-Method');
  },

  /**
   * Extract requested headers from preflight request
   */
  getRequestedHeaders: (headers: Headers): string[] => {
    const requestedHeaders = headers.get('Access-Control-Request-Headers');
    return requestedHeaders ? requestedHeaders.split(',').map(h => h.trim()) : [];
  },

  /**
   * Extract requested method from preflight request
   */
  getRequestedMethod: (headers: Headers): string => {
    return headers.get('Access-Control-Request-Method') || '';
  },
};

// Export types
export type { CORSConfig, GeoFencingConfig, IPInfo, CORSResult, GeoResult };