/**
 * Voice Navigation and Speech Synthesis System
 * Provides voice navigation and announcements for enhanced accessibility
 */

import { focusManager } from './focus-management';
import { screenReader } from './screen-reader';

export interface VoiceCommand {
  phrase: string;
  action: () => void;
  description: string;
  context?: string;
  enabled: boolean;
}

export interface SpeechOptions {
  rate?: number;
  pitch?: number;
  volume?: number;
  voice?: SpeechSynthesisVoice;
  lang?: string;
}

export interface VoiceNavigationConfig {
  enabled: boolean;
  continuous: boolean;
  language: string;
  confidenceThreshold: number;
  speechRate: number;
  speechPitch: number;
  speechVolume: number;
  preferredVoice?: string;
}

/**
 * Voice Navigation Manager
 */
export class VoiceNavigationManager {
  private static instance: VoiceNavigationManager;
  private config: VoiceNavigationConfig;
  private recognition: any | null = null;
  private synthesis: SpeechSynthesis;
  private commands: Map<string, VoiceCommand> = new Map();
  private isListening = false;
  private currentContext = 'global';

  constructor() {
    this.config = this.getDefaultConfig();
    this.synthesis = window.speechSynthesis;
    this.initializeSpeechRecognition();
    this.registerDefaultCommands();
    this.loadConfig();
  }

  static getInstance(): VoiceNavigationManager {
    if (!VoiceNavigationManager.instance) {
      VoiceNavigationManager.instance = new VoiceNavigationManager();
    }
    return VoiceNavigationManager.instance;
  }

  /**
   * Check if voice navigation is supported
   */
  isSupported(): boolean {
    return 'speechSynthesis' in window && 
           ('SpeechRecognition' in window || 'webkitSpeechRecognition' in window);
  }

  /**
   * Get current configuration
   */
  getConfig(): VoiceNavigationConfig {
    return { ...this.config };
  }

  /**
   * Update configuration
   */
  updateConfig(updates: Partial<VoiceNavigationConfig>): void {
    this.config = { ...this.config, ...updates };
    this.saveConfig();
    
    if (this.recognition) {
      this.recognition.continuous = this.config.continuous;
      this.recognition.lang = this.config.language;
    }
  }

  /**
   * Enable voice navigation
   */
  enable(): void {
    if (!this.isSupported()) {
      this.speak('Voice navigation is not supported in this browser');
      return;
    }

    this.config.enabled = true;
    this.speak('Voice navigation enabled. Say "help" for available commands.');
  }

  /**
   * Disable voice navigation
   */
  disable(): void {
    this.config.enabled = false;
    this.stopListening();
    this.speak('Voice navigation disabled');
  }

  /**
   * Start listening for voice commands
   */
  startListening(): void {
    if (!this.config.enabled || !this.recognition || this.isListening) {
      return;
    }

    try {
      this.recognition.start();
      this.isListening = true;
      this.speak('Listening for voice commands');
    } catch (error) {
      console.error('Error starting voice recognition:', error);
      this.speak('Could not start voice recognition');
    }
  }

  /**
   * Stop listening for voice commands
   */
  stopListening(): void {
    if (!this.recognition || !this.isListening) {
      return;
    }

    this.recognition.stop();
    this.isListening = false;
    this.speak('Stopped listening');
  }

  /**
   * Toggle listening state
   */
  toggleListening(): void {
    if (this.isListening) {
      this.stopListening();
    } else {
      this.startListening();
    }
  }

  /**
   * Speak text using speech synthesis
   */
  speak(text: string, options: SpeechOptions = {}): void {
    if (!this.synthesis) {
      return;
    }

    // Cancel previous speech
    this.synthesis.cancel();

    const utterance = new SpeechSynthesisUtterance(text);
    
    // Apply configuration
    utterance.rate = options.rate ?? this.config.speechRate;
    utterance.pitch = options.pitch ?? this.config.speechPitch;
    utterance.volume = options.volume ?? this.config.speechVolume;
    utterance.lang = options.lang ?? this.config.language;

    // Set voice if specified
    if (options.voice) {
      utterance.voice = options.voice;
    } else if (this.config.preferredVoice) {
      const voice = this.getVoices().find(v => v.name === this.config.preferredVoice);
      if (voice) {
        utterance.voice = voice;
      }
    }

    this.synthesis.speak(utterance);
  }

  /**
   * Get available voices
   */
  getVoices(): SpeechSynthesisVoice[] {
    return this.synthesis.getVoices();
  }

  /**
   * Register a voice command
   */
  registerCommand(command: VoiceCommand): void {
    this.commands.set(command.phrase.toLowerCase(), command);
  }

  /**
   * Remove a voice command
   */
  removeCommand(phrase: string): void {
    this.commands.delete(phrase.toLowerCase());
  }

  /**
   * Set current context for contextual commands
   */
  setContext(context: string): void {
    this.currentContext = context;
  }

  /**
   * Get commands for help
   */
  getAvailableCommands(context?: string): VoiceCommand[] {
    return Array.from(this.commands.values()).filter(cmd => 
      cmd.enabled && (!context || !cmd.context || cmd.context === context)
    );
  }

  /**
   * Announce page content for screen readers
   */
  announcePageContent(): void {
    const title = document.title;
    const main = document.querySelector('main, [role="main"]');
    const headings = document.querySelectorAll('h1, h2, h3');
    
    let announcement = `Page: ${title}. `;
    
    if (headings.length > 0) {
      announcement += `This page has ${headings.length} headings. `;
      const firstHeading = headings[0].textContent?.trim();
      if (firstHeading) {
        announcement += `Main heading: ${firstHeading}. `;
      }
    }
    
    if (main) {
      const landmarks = main.querySelectorAll('[role], nav, section, article, aside');
      if (landmarks.length > 0) {
        announcement += `${landmarks.length} landmarks found. `;
      }
    }
    
    this.speak(announcement);
  }

  /**
   * Announce current focus
   */
  announceCurrentFocus(): void {
    const activeElement = document.activeElement as HTMLElement;
    if (!activeElement || activeElement === document.body) {
      this.speak('No element is currently focused');
      return;
    }

    const role = activeElement.getAttribute('role') || activeElement.tagName.toLowerCase();
    const label = activeElement.getAttribute('aria-label') || 
                  activeElement.getAttribute('title') ||
                  activeElement.textContent?.trim() ||
                  'unlabeled element';

    this.speak(`Currently focused on ${role}: ${label}`);
  }

  private getDefaultConfig(): VoiceNavigationConfig {
    return {
      enabled: false,
      continuous: false,
      language: 'en-US',
      confidenceThreshold: 0.7,
      speechRate: 1.0,
      speechPitch: 1.0,
      speechVolume: 1.0
    };
  }

  private initializeSpeechRecognition(): void {
    const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;
    
    if (!SpeechRecognition) {
      console.warn('Speech recognition not supported');
      return;
    }

    this.recognition = new SpeechRecognition();
    this.recognition.continuous = this.config.continuous;
    this.recognition.interimResults = false;
    this.recognition.lang = this.config.language;
    this.recognition.maxAlternatives = 3;

    this.recognition.onstart = () => {
      this.isListening = true;
    };

    this.recognition.onend = () => {
      this.isListening = false;
      if (this.config.continuous && this.config.enabled) {
        // Restart for continuous listening
        setTimeout(() => this.startListening(), 100);
      }
    };

    this.recognition.onresult = (event) => {
      this.handleSpeechResult(event);
    };

    this.recognition.onerror = (event) => {
      console.error('Speech recognition error:', event.error);
      this.isListening = false;
      
      if (event.error !== 'no-speech' && event.error !== 'aborted') {
        this.speak('Voice recognition error. Please try again.');
      }
    };
  }

  private handleSpeechResult(event: any): void {
    const results = Array.from(event.results);
    const lastResult = results[results.length - 1] as any;
    
    if (!lastResult.isFinal) {
      return;
    }

    const alternatives: Array<{ transcript: string; confidence: number }> = [];
    for (let i = 0; i < lastResult.length; i++) {
      const alternative = lastResult[i];
      alternatives.push({
        transcript: alternative.transcript.toLowerCase().trim(),
        confidence: alternative.confidence
      });
    }

    // Find the best matching command
    for (const alt of alternatives) {
      if (alt.confidence < this.config.confidenceThreshold) {
        continue;
      }

      const command = this.findMatchingCommand(alt.transcript);
      if (command) {
        this.executeCommand(command, alt.transcript);
        return;
      }
    }

    // No command found
    this.speak('Command not recognized. Say "help" for available commands.');
  }

  private findMatchingCommand(transcript: string): VoiceCommand | null {
    // Exact match first
    const exactMatch = this.commands.get(transcript);
    if (exactMatch && exactMatch.enabled) {
      return exactMatch;
    }

    // Partial match
    const commandEntries = Array.from(this.commands.entries());
    for (const [phrase, command] of commandEntries) {
      if (command && command.enabled && 
          (!command.context || command.context === this.currentContext) &&
          (transcript.includes(phrase) || phrase.includes(transcript))) {
        return command;
      }
    }

    return null;
  }

  private executeCommand(command: VoiceCommand, transcript: string): void {
    try {
      command.action();
      this.speak(`Executed: ${command.description}`);
    } catch (error) {
      console.error('Error executing voice command:', error);
      this.speak('Error executing command');
    }
  }

  private registerDefaultCommands(): void {
    // Navigation commands
    this.registerCommand({
      phrase: 'go home',
      action: () => window.location.href = '/',
      description: 'Navigate to home page',
      enabled: true
    });

    this.registerCommand({
      phrase: 'go to calculator',
      action: () => window.location.href = '/calculator',
      description: 'Navigate to calculator page',
      enabled: true
    });

    // Focus commands
    this.registerCommand({
      phrase: 'next element',
      action: () => focusManager.focusNext(),
      description: 'Move focus to next element',
      enabled: true
    });

    this.registerCommand({
      phrase: 'previous element',
      action: () => focusManager.focusPrevious(),
      description: 'Move focus to previous element',
      enabled: true
    });

    this.registerCommand({
      phrase: 'first element',
      action: () => focusManager.focusFirst(),
      description: 'Move focus to first element',
      enabled: true
    });

    this.registerCommand({
      phrase: 'last element',
      action: () => focusManager.focusLast(),
      description: 'Move focus to last element',
      enabled: true
    });

    // Information commands
    this.registerCommand({
      phrase: 'what is focused',
      action: () => this.announceCurrentFocus(),
      description: 'Announce current focused element',
      enabled: true
    });

    this.registerCommand({
      phrase: 'describe page',
      action: () => this.announcePageContent(),
      description: 'Describe page content',
      enabled: true
    });

    this.registerCommand({
      phrase: 'read headings',
      action: () => this.readHeadings(),
      description: 'Read all page headings',
      enabled: true
    });

    // Calculator commands
    this.registerCommand({
      phrase: 'calculate cost',
      action: () => {
        const button = document.querySelector('[data-testid="calculate-button"]') as HTMLElement;
        if (button) button.click();
      },
      description: 'Calculate construction cost',
      context: 'calculator',
      enabled: true
    });

    this.registerCommand({
      phrase: 'reset form',
      action: () => {
        const button = document.querySelector('[data-testid="reset-button"]') as HTMLElement;
        if (button) button.click();
      },
      description: 'Reset calculator form',
      context: 'calculator',
      enabled: true
    });

    // Help command
    this.registerCommand({
      phrase: 'help',
      action: () => this.announceHelp(),
      description: 'Show available commands',
      enabled: true
    });

    // Voice control commands
    this.registerCommand({
      phrase: 'stop listening',
      action: () => this.stopListening(),
      description: 'Stop voice recognition',
      enabled: true
    });

    this.registerCommand({
      phrase: 'start listening',
      action: () => this.startListening(),
      description: 'Start voice recognition',
      enabled: true
    });
  }

  private readHeadings(): void {
    const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
    if (headings.length === 0) {
      this.speak('No headings found on this page');
      return;
    }

    let announcement = `Found ${headings.length} headings: `;
    headings.forEach((heading, index) => {
      const level = heading.tagName.toLowerCase();
      const text = heading.textContent?.trim() || 'untitled';
      announcement += `${level} ${text}. `;
    });

    this.speak(announcement);
  }

  private announceHelp(): void {
    const commands = this.getAvailableCommands(this.currentContext);
    let announcement = `Available voice commands: `;
    
    commands.slice(0, 10).forEach(cmd => {
      announcement += `Say "${cmd.phrase}" to ${cmd.description}. `;
    });

    if (commands.length > 10) {
      announcement += `And ${commands.length - 10} more commands.`;
    }

    this.speak(announcement);
  }

  private loadConfig(): void {
    try {
      const saved = localStorage.getItem('voice-navigation-config');
      if (saved) {
        this.config = { ...this.config, ...JSON.parse(saved) };
      }
    } catch (error) {
      console.warn('Failed to load voice navigation config:', error);
    }
  }

  private saveConfig(): void {
    try {
      localStorage.setItem('voice-navigation-config', JSON.stringify(this.config));
    } catch (error) {
      console.warn('Failed to save voice navigation config:', error);
    }
  }
}

// Export singleton instance
export const voiceNav = VoiceNavigationManager.getInstance();

/**
 * React hook for voice navigation
 */
export function useVoiceNavigation() {
  return {
    isSupported: voiceNav.isSupported.bind(voiceNav),
    getConfig: voiceNav.getConfig.bind(voiceNav),
    updateConfig: voiceNav.updateConfig.bind(voiceNav),
    enable: voiceNav.enable.bind(voiceNav),
    disable: voiceNav.disable.bind(voiceNav),
    startListening: voiceNav.startListening.bind(voiceNav),
    stopListening: voiceNav.stopListening.bind(voiceNav),
    toggleListening: voiceNav.toggleListening.bind(voiceNav),
    speak: voiceNav.speak.bind(voiceNav),
    getVoices: voiceNav.getVoices.bind(voiceNav),
    registerCommand: voiceNav.registerCommand.bind(voiceNav),
    removeCommand: voiceNav.removeCommand.bind(voiceNav),
    setContext: voiceNav.setContext.bind(voiceNav),
    announcePageContent: voiceNav.announcePageContent.bind(voiceNav),
    announceCurrentFocus: voiceNav.announceCurrentFocus.bind(voiceNav)
  };
}