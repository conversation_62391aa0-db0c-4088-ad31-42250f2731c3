'use client';

import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card';
import { Skeleton, SkeletonCalculator, SkeletonBreakdown } from '@/components/ui/skeleton';

export function CalculatorSkeleton() {
  return (
    <div className="max-w-4xl mx-auto space-y-8">
      {/* Progress indicator skeleton */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-between mb-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="flex items-center">
                <Skeleton variant="circle" className="w-10 h-10" shimmer />
                {i < 3 && <Skeleton className="w-20 h-1 mx-2" shimmer />}
              </div>
            ))}
          </div>
          <div className="text-center space-y-2">
            <Skeleton variant="text" className="h-6 w-48 mx-auto" shimmer />
            <Skeleton variant="text" className="h-4 w-64 mx-auto" shimmer />
          </div>
        </CardContent>
      </Card>

      {/* Enhanced form skeleton */}
      <SkeletonCalculator />
    </div>
  );
}

export function ResultsSkeleton() {
  return (
    <div className="space-y-8">
      {/* Header skeleton */}
      <div className="flex items-center justify-between">
        <Skeleton variant="text" className="h-10 w-32" shimmer />
        <div className="flex gap-2">
          <Skeleton variant="button" className="h-8 w-20" shimmer />
          <Skeleton variant="button" className="h-8 w-32" shimmer />
        </div>
      </div>

      {/* Hero cost display skeleton */}
      <Card>
        <CardContent className="p-8">
          <div className="text-center space-y-4">
            <Skeleton variant="text" className="h-4 w-48 mx-auto" shimmer />
            <Skeleton variant="text" className="h-12 w-64 mx-auto" shimmer />
            <Skeleton variant="text" className="h-6 w-32 mx-auto" shimmer />

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-6">
              {[1, 2, 3].map((i) => (
                <div key={i} className="text-center space-y-2">
                  <Skeleton variant="text" className="h-4 w-24 mx-auto" shimmer />
                  <Skeleton variant="text" className="h-6 w-16 mx-auto" shimmer />
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Enhanced breakdown skeleton */}
      <SkeletonBreakdown />
    </div>
  );
}