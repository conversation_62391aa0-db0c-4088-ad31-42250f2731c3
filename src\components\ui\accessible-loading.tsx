/**
 * Accessible Loading Components with Screen Reader Support
 * Provides WCAG-compliant loading states with comprehensive accessibility features
 */

'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import { fadeIn } from '@/lib/animations';

// Live region types for announcements
type LiveRegionPoliteness = 'polite' | 'assertive' | 'off';

// Accessible loading announcement hook
export function useLoadingAnnouncements() {
  const [announcements, setAnnouncements] = useState<string[]>([]);
  const timeoutRef = useRef<NodeJS.Timeout>();

  const announce = useCallback((message: string, politeness: LiveRegionPoliteness = 'polite') => {
    // Clear previous timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // Add announcement
    setAnnouncements(prev => [...prev, message]);

    // Remove announcement after it's been read
    timeoutRef.current = setTimeout(() => {
      setAnnouncements(prev => prev.slice(1));
    }, 3000);
  }, []);

  const clearAnnouncements = useCallback(() => {
    setAnnouncements([]);
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
  }, []);

  return {
    announcements,
    announce,
    clearAnnouncements
  };
}

// Live region component for screen reader announcements
interface LiveRegionProps {
  announcements: string[];
  politeness?: LiveRegionPoliteness;
  atomic?: boolean;
  relevant?: 'additions' | 'removals' | 'text' | 'all';
  className?: string;
}

export function LiveRegion({
  announcements,
  politeness = 'polite',
  atomic = true,
  relevant = 'additions',
  className
}: LiveRegionProps) {
  return (
    <div
      aria-live={politeness}
      aria-atomic={atomic}
      aria-relevant={relevant}
      className={cn('sr-only', className)}
    >
      {announcements.map((announcement, index) => (
        <div key={index}>{announcement}</div>
      ))}
    </div>
  );
}

// Accessible loading spinner with proper ARIA attributes
interface AccessibleLoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  label?: string;
  description?: string;
  className?: string;
  valueText?: string;
  valueNow?: number;
  valueMin?: number;
  valueMax?: number;
  showLabel?: boolean;
}

export function AccessibleLoadingSpinner({
  size = 'md',
  label = 'Loading',
  description,
  className,
  valueText,
  valueNow,
  valueMin = 0,
  valueMax = 100,
  showLabel = false
}: AccessibleLoadingSpinnerProps) {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8'
  };

  const spinnerProps = {
    role: 'progressbar',
    'aria-label': label,
    'aria-describedby': description ? 'loading-description' : undefined,
    'aria-valuetext': valueText,
    'aria-valuenow': valueNow,
    'aria-valuemin': valueMin,
    'aria-valuemax': valueMax
  };

  return (
    <div className={cn('flex items-center gap-2', className)}>
      <motion.div
        className={cn(
          'border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin',
          sizeClasses[size]
        )}
        {...spinnerProps}
      />
      
      {showLabel && (
        <span className="text-sm text-gray-600" id="loading-label">
          {label}
        </span>
      )}
      
      {description && (
        <span className="sr-only" id="loading-description">
          {description}
        </span>
      )}
    </div>
  );
}

// Accessible skeleton with proper ARIA attributes
interface AccessibleSkeletonProps {
  label?: string;
  description?: string;
  variant?: 'text' | 'circle' | 'rectangle';
  width?: string;
  height?: string;
  className?: string;
  animate?: boolean;
}

export function AccessibleSkeleton({
  label = 'Loading content',
  description,
  variant = 'text',
  width,
  height,
  className,
  animate = true
}: AccessibleSkeletonProps) {
  const baseClasses = "bg-gray-200 rounded";
  
  const variantClasses = {
    text: "h-4 w-full",
    circle: "rounded-full w-10 h-10",
    rectangle: "h-32 w-full rounded-lg"
  };

  return (
    <div
      className={cn(
        baseClasses,
        variantClasses[variant],
        animate && 'animate-pulse',
        className
      )}
      style={{ width, height }}
      role="status"
      aria-label={label}
      aria-describedby={description ? 'skeleton-description' : undefined}
    >
      <span className="sr-only">{label}</span>
      {description && (
        <span className="sr-only" id="skeleton-description">
          {description}
        </span>
      )}
    </div>
  );
}

// Accessible progress bar
interface AccessibleProgressProps {
  value: number;
  max?: number;
  label?: string;
  description?: string;
  showPercentage?: boolean;
  showValue?: boolean;
  className?: string;
  barClassName?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'success' | 'warning' | 'error';
}

export function AccessibleProgress({
  value,
  max = 100,
  label = 'Progress',
  description,
  showPercentage = false,
  showValue = false,
  className,
  barClassName,
  size = 'md',
  variant = 'default'
}: AccessibleProgressProps) {
  const percentage = Math.round((value / max) * 100);
  
  const sizeClasses = {
    sm: 'h-1',
    md: 'h-2',
    lg: 'h-3'
  };

  const variantClasses = {
    default: 'bg-blue-500',
    success: 'bg-green-500',
    warning: 'bg-yellow-500',
    error: 'bg-red-500'
  };

  return (
    <div className={cn('space-y-2', className)}>
      {/* Label and value */}
      <div className="flex justify-between items-center">
        <span className="text-sm font-medium text-gray-700" id="progress-label">
          {label}
        </span>
        
        {(showPercentage || showValue) && (
          <span className="text-sm text-gray-600">
            {showValue && `${value}${max !== 100 ? `/${max}` : ''}`}
            {showValue && showPercentage && ' '}
            {showPercentage && `(${percentage}%)`}
          </span>
        )}
      </div>

      {/* Progress bar */}
      <div
        className={cn('w-full bg-gray-200 rounded-full', sizeClasses[size])}
        role="progressbar"
        aria-labelledby="progress-label"
        aria-describedby={description ? 'progress-description' : undefined}
        aria-valuenow={value}
        aria-valuemin={0}
        aria-valuemax={max}
        aria-valuetext={`${percentage}% complete`}
      >
        <motion.div
          className={cn(
            'h-full rounded-full transition-all duration-300',
            variantClasses[variant],
            barClassName
          )}
          initial={{ width: 0 }}
          animate={{ width: `${percentage}%` }}
          transition={{ duration: 0.5, ease: 'easeOut' }}
        />
      </div>

      {/* Description */}
      {description && (
        <p className="text-xs text-gray-500" id="progress-description">
          {description}
        </p>
      )}
    </div>
  );
}

// Accessible loading state manager
interface AccessibleLoadingStateProps {
  isLoading: boolean;
  loadingContent: React.ReactNode;
  children: React.ReactNode;
  loadingLabel?: string;
  loadingDescription?: string;
  announceStateChanges?: boolean;
  className?: string;
}

export function AccessibleLoadingState({
  isLoading,
  loadingContent,
  children,
  loadingLabel = 'Content is loading',
  loadingDescription = 'Please wait while the content loads',
  announceStateChanges = true,
  className
}: AccessibleLoadingStateProps) {
  const { announcements, announce } = useLoadingAnnouncements();
  const [previousLoadingState, setPreviousLoadingState] = useState(isLoading);

  // Announce state changes
  useEffect(() => {
    if (announceStateChanges && isLoading !== previousLoadingState) {
      if (isLoading) {
        announce(loadingLabel);
      } else {
        announce('Content has finished loading');
      }
      setPreviousLoadingState(isLoading);
    }
  }, [isLoading, previousLoadingState, announceStateChanges, loadingLabel, announce]);

  return (
    <div className={className}>
      <LiveRegion announcements={announcements} />
      
      <div
        aria-busy={isLoading}
        aria-live="polite"
        aria-atomic="true"
      >
        <AnimatePresence mode="wait">
          {isLoading ? (
            <motion.div
              key="loading"
              variants={fadeIn}
              initial="initial"
              animate="animate"
              exit="exit"
              role="status"
              aria-label={loadingLabel}
              aria-describedby="loading-description"
            >
              {loadingContent}
              <span className="sr-only" id="loading-description">
                {loadingDescription}
              </span>
            </motion.div>
          ) : (
            <motion.div
              key="content"
              variants={fadeIn}
              initial="initial"
              animate="animate"
              exit="exit"
            >
              {children}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
}

// Focus management for loading states
export function useFocusManagement() {
  const focusRef = useRef<HTMLElement | null>(null);
  const previousFocusRef = useRef<HTMLElement | null>(null);

  const saveFocus = useCallback(() => {
    previousFocusRef.current = document.activeElement as HTMLElement;
  }, []);

  const restoreFocus = useCallback(() => {
    if (previousFocusRef.current && typeof previousFocusRef.current.focus === 'function') {
      previousFocusRef.current.focus();
    }
  }, []);

  const setFocus = useCallback((element: HTMLElement | null) => {
    if (element && typeof element.focus === 'function') {
      element.focus();
      focusRef.current = element;
    }
  }, []);

  const trapFocus = useCallback((container: HTMLElement) => {
    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    
    const firstElement = focusableElements[0] as HTMLElement;
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

    const handleTabKey = (e: KeyboardEvent) => {
      if (e.key === 'Tab') {
        if (e.shiftKey) {
          if (document.activeElement === firstElement) {
            lastElement.focus();
            e.preventDefault();
          }
        } else {
          if (document.activeElement === lastElement) {
            firstElement.focus();
            e.preventDefault();
          }
        }
      }
    };

    container.addEventListener('keydown', handleTabKey);
    
    return () => {
      container.removeEventListener('keydown', handleTabKey);
    };
  }, []);

  return {
    saveFocus,
    restoreFocus,
    setFocus,
    trapFocus,
    focusRef
  };
}

// Accessible modal loading overlay
interface AccessibleLoadingModalProps {
  isOpen: boolean;
  onClose?: () => void;
  title: string;
  description?: string;
  children: React.ReactNode;
  closeOnEscape?: boolean;
  closeOnBackdropClick?: boolean;
  className?: string;
}

export function AccessibleLoadingModal({
  isOpen,
  onClose,
  title,
  description,
  children,
  closeOnEscape = true,
  closeOnBackdropClick = false,
  className
}: AccessibleLoadingModalProps) {
  const modalRef = useRef<HTMLDivElement>(null);
  const { saveFocus, restoreFocus, trapFocus } = useFocusManagement();

  // Handle escape key
  useEffect(() => {
    if (!isOpen || !closeOnEscape) return;

    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && onClose) {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [isOpen, closeOnEscape, onClose]);

  // Manage focus
  useEffect(() => {
    if (isOpen) {
      saveFocus();
      
      // Focus modal after animation
      setTimeout(() => {
        if (modalRef.current) {
          modalRef.current.focus();
        }
      }, 100);
    } else {
      restoreFocus();
    }
  }, [isOpen, saveFocus, restoreFocus]);

  // Trap focus within modal
  useEffect(() => {
    if (isOpen && modalRef.current) {
      return trapFocus(modalRef.current);
    }
  }, [isOpen, trapFocus]);

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
        onClick={closeOnBackdropClick ? onClose : undefined}
        role="dialog"
        aria-modal="true"
        aria-labelledby="modal-title"
        aria-describedby={description ? "modal-description" : undefined}
      >
        <motion.div
          ref={modalRef}
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          className={cn(
            'bg-white rounded-lg shadow-xl max-w-md w-full max-h-[80vh] overflow-auto',
            className
          )}
          onClick={(e) => e.stopPropagation()}
          tabIndex={-1}
        >
          <div className="p-6">
            <h2 id="modal-title" className="text-lg font-semibold text-gray-900 mb-2">
              {title}
            </h2>
            
            {description && (
              <p id="modal-description" className="text-sm text-gray-600 mb-4">
                {description}
              </p>
            )}
            
            {children}
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
}

// Keyboard navigation helpers
export function useKeyboardNavigation() {
  const handleKeyboardNavigation = useCallback((
    e: React.KeyboardEvent,
    onEnter?: () => void,
    onSpace?: () => void,
    onEscape?: () => void,
    onArrowKeys?: (direction: 'up' | 'down' | 'left' | 'right') => void
  ) => {
    switch (e.key) {
      case 'Enter':
        if (onEnter) {
          e.preventDefault();
          onEnter();
        }
        break;
      case ' ':
        if (onSpace) {
          e.preventDefault();
          onSpace();
        }
        break;
      case 'Escape':
        if (onEscape) {
          e.preventDefault();
          onEscape();
        }
        break;
      case 'ArrowUp':
        if (onArrowKeys) {
          e.preventDefault();
          onArrowKeys('up');
        }
        break;
      case 'ArrowDown':
        if (onArrowKeys) {
          e.preventDefault();
          onArrowKeys('down');
        }
        break;
      case 'ArrowLeft':
        if (onArrowKeys) {
          e.preventDefault();
          onArrowKeys('left');
        }
        break;
      case 'ArrowRight':
        if (onArrowKeys) {
          e.preventDefault();
          onArrowKeys('right');
        }
        break;
    }
  }, []);

  return { handleKeyboardNavigation };
}

// Screen reader utilities
export const ScreenReaderUtils = {
  // Hide content from screen readers
  hideFromScreenReader: (element: HTMLElement) => {
    element.setAttribute('aria-hidden', 'true');
  },

  // Show content to screen readers
  showToScreenReader: (element: HTMLElement) => {
    element.removeAttribute('aria-hidden');
  },

  // Announce to screen readers
  announce: (message: string, priority: 'polite' | 'assertive' = 'polite') => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', priority);
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    
    document.body.appendChild(announcement);
    
    setTimeout(() => {
      document.body.removeChild(announcement);
    }, 1000);
  }
};