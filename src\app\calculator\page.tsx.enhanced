'use client';

import { Suspense } from 'react';
import { motion, type Variants } from 'framer-motion';
import { LightweightCalculatorContainer } from '@/components/calculator/LightweightCalculatorContainer';
import { Container } from '@/components/layout/Container';
import { Header } from '@/components/layout/Header';
import { Footer } from '@/components/layout/Footer';
import { EnhancedCard } from '@/components/ui/enhanced-card';
import { Calculator, TrendingUp, Shield, Clock } from 'lucide-react';

// Enhanced loading skeleton with professional animations
const PageSkeleton = () => (
  <div className="max-w-6xl mx-auto space-y-8" data-testid="page-skeleton">
    {/* Header Skeleton */}
    <div className="text-center space-y-6" data-testid="skeleton-header">
      <motion.div 
        className="h-12 w-96 mx-auto bg-gradient-to-r from-secondary-200 via-secondary-100 to-secondary-200 rounded-xl"
        animate={{ backgroundPosition: ['0% 50%', '100% 50%', '0% 50%'] }}
        transition={{ duration: 2, repeat: Infinity, ease: 'easeInOut' }}
        style={{ backgroundSize: '200% 100%' }}
      />
      <motion.div 
        className="h-6 w-[500px] mx-auto bg-gradient-to-r from-secondary-200 via-secondary-100 to-secondary-200 rounded-lg"
        animate={{ backgroundPosition: ['0% 50%', '100% 50%', '0% 50%'] }}
        transition={{ duration: 2, repeat: Infinity, ease: 'easeInOut', delay: 0.2 }}
        style={{ backgroundSize: '200% 100%' }}
      />
      <motion.div 
        className="h-6 w-[400px] mx-auto bg-gradient-to-r from-secondary-200 via-secondary-100 to-secondary-200 rounded-lg"
        animate={{ backgroundPosition: ['0% 50%', '100% 50%', '0% 50%'] }}
        transition={{ duration: 2, repeat: Infinity, ease: 'easeInOut', delay: 0.4 }}
        style={{ backgroundSize: '200% 100%' }}
      />
    </div>
    
    {/* Feature Cards Skeleton */}
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      {[0, 1, 2].map((i) => (
        <motion.div
          key={i}
          className="h-32 bg-gradient-to-r from-secondary-200 via-secondary-100 to-secondary-200 rounded-xl"
          animate={{ backgroundPosition: ['0% 50%', '100% 50%', '0% 50%'] }}
          transition={{ duration: 2, repeat: Infinity, ease: 'easeInOut', delay: i * 0.2 }}
          style={{ backgroundSize: '200% 100%' }}
        />
      ))}
    </div>
    
    {/* Form Skeleton */}
    <div className="space-y-6" data-testid="skeleton-form">
      <motion.div 
        className="h-24 w-full bg-gradient-to-r from-secondary-200 via-secondary-100 to-secondary-200 rounded-xl"
        animate={{ backgroundPosition: ['0% 50%', '100% 50%', '0% 50%'] }}
        transition={{ duration: 2, repeat: Infinity, ease: 'easeInOut', delay: 0.6 }}
        style={{ backgroundSize: '200% 100%' }}
      />
      <motion.div 
        className="h-48 w-full bg-gradient-to-r from-secondary-200 via-secondary-100 to-secondary-200 rounded-xl"
        animate={{ backgroundPosition: ['0% 50%', '100% 50%', '0% 50%'] }}
        transition={{ duration: 2, repeat: Infinity, ease: 'easeInOut', delay: 0.8 }}
        style={{ backgroundSize: '200% 100%' }}
      />
      <motion.div 
        className="h-14 w-full bg-gradient-to-r from-secondary-200 via-secondary-100 to-secondary-200 rounded-xl"
        animate={{ backgroundPosition: ['0% 50%', '100% 50%', '0% 50%'] }}
        transition={{ duration: 2, repeat: Infinity, ease: 'easeInOut', delay: 1.0 }}
        style={{ backgroundSize: '200% 100%' }}
      />
    </div>
  </div>
);

// Animation variants for page elements
const containerVariants: Variants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      duration: 0.6,
      staggerChildren: 0.1
    }
  }
};

const itemVariants: Variants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.5 }
  }
};

const featureCards = [
  {
    icon: Calculator,
    title: "Smart Calculations",
    description: "AI-powered cost estimation with Indian construction standards"
  },
  {
    icon: TrendingUp,
    title: "Market Accurate",
    description: "Real-time pricing from 200+ verified suppliers across India"
  },
  {
    icon: Shield,
    title: "IS Code Compliant",
    description: "BIS standards compliant with M20+ concrete specifications"
  }
];

export default function CalculatorPage() {
  return (
    <>
      <Header />
      <Container>
        <motion.main 
          className='py-12 lg:py-16' 
          data-testid="calculator-page" 
          role="main"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {/* Enhanced Hero Section */}
          <motion.div 
            className='text-center mb-12 lg:mb-16' 
            data-testid="page-header"
            variants={itemVariants}
          >
            <motion.div
              className="inline-flex items-center gap-2 bg-primary-50 text-primary-700 px-4 py-2 rounded-full text-sm font-medium mb-6"
              variants={itemVariants}
            >
              <Clock className="h-4 w-4" />
              Get results in under 30 seconds
            </motion.div>
            
            <motion.h1 
              className='heading-1 text-gradient-primary mb-6 max-w-4xl mx-auto' 
              data-testid="page-title"
              variants={itemVariants}
            >
              India's Most Trusted Construction Cost Calculator
            </motion.h1>
            
            <motion.p 
              className='body-large text-secondary-600 max-w-3xl mx-auto mb-8' 
              data-testid="page-description"
              variants={itemVariants}
            >
              Get accurate construction cost estimates for your dream home with our AI-powered calculator. 
              Professional-grade calculations based on current market rates, IS codes, and 50+ city-specific pricing.
            </motion.p>
            
            {/* Trust Indicators */}
            <motion.div 
              className="flex flex-wrap justify-center items-center gap-6 text-sm text-secondary-500"
              variants={itemVariants}
            >
              <div className="flex items-center gap-1">
                <Shield className="h-4 w-4 text-success-500" />
                <span>BIS Compliant</span>
              </div>
              <div className="flex items-center gap-1">
                <TrendingUp className="h-4 w-4 text-success-500" />
                <span>Market Accurate</span>
              </div>
              <div className="flex items-center gap-1">
                <Calculator className="h-4 w-4 text-success-500" />
                <span>Professional Grade</span>
              </div>
            </motion.div>
          </motion.div>

          {/* Feature Cards */}
          <motion.div 
            className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12 lg:mb-16"
            variants={itemVariants}
          >
            {featureCards.map((feature, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                whileHover={{ y: -4, scale: 1.02 }}
                transition={{ duration: 0.2 }}
              >
                <EnhancedCard 
                  variant="glass" 
                  size="md"
                  className="text-center h-full"
                >
                  <div className="p-6">
                    <div className="inline-flex items-center justify-center w-12 h-12 bg-primary-100 rounded-xl mb-4">
                      <feature.icon className="h-6 w-6 text-primary-600" />
                    </div>
                    <h3 className="heading-6 mb-2">{feature.title}</h3>
                    <p className="body-small">{feature.description}</p>
                  </div>
                </EnhancedCard>
              </motion.div>
            ))}
          </motion.div>

          {/* Calculator Container */}
          <motion.div variants={itemVariants}>
            <Suspense fallback={<PageSkeleton />}>
              <LightweightCalculatorContainer />
            </Suspense>
          </motion.div>
        </motion.main>
      </Container>
      <Footer />
    </>
  );
}
