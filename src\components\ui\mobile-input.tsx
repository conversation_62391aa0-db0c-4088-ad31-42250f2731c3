'use client';

import { forwardRef, useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { 
  getMobileInputType, 
  mobileClasses, 
  preventZoomOnFocus, 
  restoreZoom,
  hapticFeedback 
} from '@/lib/mobile';

export interface MobileInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  helper?: string;
  icon?: React.ReactNode;
  inputMode?: 'none' | 'text' | 'tel' | 'url' | 'email' | 'numeric' | 'decimal' | 'search';
  showClearButton?: boolean;
  onClear?: () => void;
  fieldType?: 'text' | 'number' | 'email' | 'phone' | 'url' | 'area' | 'cost';
}

export const MobileInput = forwardRef<HTMLInputElement, MobileInputProps>(
  ({ 
    className, 
    label, 
    error, 
    helper, 
    icon, 
    inputMode,
    showClearButton,
    onClear,
    fieldType = 'text',
    onFocus,
    onBlur,
    ...props 
  }, ref) => {
    const [isFocused, setIsFocused] = useState(false);
    const [hasValue, setHasValue] = useState(Boolean(props.value || props.defaultValue));

    useEffect(() => {
      setHasValue(Boolean(props.value));
    }, [props.value]);

    const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
      setIsFocused(true);
      preventZoomOnFocus();
      hapticFeedback.light();
      onFocus?.(e);
    };

    const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
      setIsFocused(false);
      restoreZoom();
      onBlur?.(e);
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      setHasValue(Boolean(e.target.value));
      props.onChange?.(e);
    };

    const handleClear = () => {
      setHasValue(false);
      hapticFeedback.light();
      onClear?.();
    };

    const inputType = getMobileInputType(fieldType);

    return (
      <div className="space-y-1">
        {/* Label */}
        {label && (
          <motion.label
            className={cn(
              'block text-sm font-medium transition-colors duration-200',
              error ? 'text-red-600' : 'text-gray-700',
              isFocused && !error && 'text-blue-600'
            )}
            animate={{
              color: error ? '#dc2626' : isFocused ? '#2563eb' : '#374151'
            }}
          >
            {label}
          </motion.label>
        )}

        {/* Input container */}
        <div className="relative">
          {/* Icon */}
          {icon && (
            <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
              {icon}
            </div>
          )}

          {/* Input */}
          <input
            ref={ref}
            type={inputType}
            inputMode={inputMode}
            className={cn(
              // Base styles
              'w-full rounded-lg border transition-all duration-200',
              'text-base leading-relaxed', // 16px minimum for iOS
              'placeholder-gray-400',
              
              // Mobile-friendly sizing
              mobileClasses.touchTarget,
              'px-4 py-3 sm:py-2',
              
              // Icon spacing
              icon && 'pl-10',
              showClearButton && hasValue && 'pr-10',
              
              // States
              'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
              'disabled:bg-gray-50 disabled:text-gray-500 disabled:cursor-not-allowed',
              
              // Error states
              error 
                ? 'border-red-300 text-red-900 placeholder-red-300 focus:ring-red-500 focus:border-red-500'
                : 'border-gray-300 text-gray-900',
              
              className
            )}
            onFocus={handleFocus}
            onBlur={handleBlur}
            onChange={handleChange}
            {...props}
          />

          {/* Clear button */}
          {showClearButton && hasValue && (
            <motion.button
              type="button"
              onClick={handleClear}
              className={cn(
                'absolute right-3 top-1/2 transform -translate-y-1/2',
                'text-gray-400 hover:text-gray-600 transition-colors',
                mobileClasses.touchTarget,
                'flex items-center justify-center w-6 h-6'
              )}
              initial={{ opacity: 0, scale: 0 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0 }}
              whileTap={{ scale: 0.9 }}
            >
              <svg
                className="w-4 h-4"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                  clipRule="evenodd"
                />
              </svg>
            </motion.button>
          )}
        </div>

        {/* Helper text or error */}
        {(helper || error) && (
          <motion.div
            initial={{ opacity: 0, y: -5 }}
            animate={{ opacity: 1, y: 0 }}
            className={cn(
              'text-xs',
              error ? 'text-red-600' : 'text-gray-500'
            )}
          >
            {error || helper}
          </motion.div>
        )}
      </div>
    );
  }
);

MobileInput.displayName = 'MobileInput';

interface MobileTextAreaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label?: string;
  error?: string;
  helper?: string;
  showCharCount?: boolean;
  maxLength?: number;
}

export const MobileTextArea = forwardRef<HTMLTextAreaElement, MobileTextAreaProps>(
  ({ 
    className, 
    label, 
    error, 
    helper, 
    showCharCount,
    maxLength,
    onFocus,
    onBlur,
    ...props 
  }, ref) => {
    const [isFocused, setIsFocused] = useState(false);
    const [charCount, setCharCount] = useState(String(props.value || props.defaultValue || '').length);

    const handleFocus = (e: React.FocusEvent<HTMLTextAreaElement>) => {
      setIsFocused(true);
      preventZoomOnFocus();
      hapticFeedback.light();
      onFocus?.(e);
    };

    const handleBlur = (e: React.FocusEvent<HTMLTextAreaElement>) => {
      setIsFocused(false);
      restoreZoom();
      onBlur?.(e);
    };

    const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      setCharCount(e.target.value.length);
      props.onChange?.(e);
    };

    return (
      <div className="space-y-1">
        {/* Label */}
        {label && (
          <motion.label
            className={cn(
              'block text-sm font-medium transition-colors duration-200',
              error ? 'text-red-600' : 'text-gray-700',
              isFocused && !error && 'text-blue-600'
            )}
          >
            {label}
          </motion.label>
        )}

        {/* Textarea */}
        <textarea
          ref={ref}
          className={cn(
            // Base styles
            'w-full rounded-lg border transition-all duration-200',
            'text-base leading-relaxed resize-none', // 16px minimum for iOS
            'placeholder-gray-400',
            
            // Mobile-friendly sizing
            'px-4 py-3 min-h-[100px]',
            
            // States
            'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
            'disabled:bg-gray-50 disabled:text-gray-500 disabled:cursor-not-allowed',
            
            // Error states
            error 
              ? 'border-red-300 text-red-900 placeholder-red-300 focus:ring-red-500 focus:border-red-500'
              : 'border-gray-300 text-gray-900',
            
            className
          )}
          maxLength={maxLength}
          onFocus={handleFocus}
          onBlur={handleBlur}
          onChange={handleChange}
          {...props}
        />

        {/* Footer */}
        <div className="flex justify-between items-center">
          {/* Helper text or error */}
          <div className={cn(
            'text-xs',
            error ? 'text-red-600' : 'text-gray-500'
          )}>
            {error || helper}
          </div>

          {/* Character count */}
          {showCharCount && (
            <div className={cn(
              'text-xs',
              maxLength && charCount > maxLength * 0.9 ? 'text-yellow-600' : 'text-gray-500',
              maxLength && charCount >= maxLength && 'text-red-600'
            )}>
              {maxLength ? `${charCount}/${maxLength}` : charCount}
            </div>
          )}
        </div>
      </div>
    );
  }
);

MobileTextArea.displayName = 'MobileTextArea';