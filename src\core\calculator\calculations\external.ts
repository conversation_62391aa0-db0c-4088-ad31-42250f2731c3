/**
 * External Works Cost Calculations
 * Includes compound wall, landscaping, parking, and site development
 */

import type { CalculationInput, CategoryCost, SubCategory } from '../types';
import { COST_BREAKDOWN_PERCENTAGES } from '../constants';

export function calculateExternalCost(input: CalculationInput): CategoryCost {
  const {
    builtUpArea,
    plotArea = builtUpArea * 1.5,
    qualityTier,
    parkingType = 'open',
  } = input;

  // External works rates per sqft of plot area by quality tier
  const externalBaseRates = {
    smart: 180, // ₹180/sqft plot area for Smart Choice external works
    premium: 250, // ₹250/sqft plot area for Premium Selection external works
    luxury: 350, // ₹350/sqft plot area for Luxury Collection external works
  };

  // Get base external rate for quality tier
  const externalCostPerSqft = externalBaseRates[qualityTier];

  // Total external cost based on plot area
  const totalExternalCost = plotArea * externalCostPerSqft;

  // Calculate parking cost adjustment
  const parkingAdjustment = calculateParkingCost(
    builtUpArea,
    parkingType || 'none',
    qualityTier
  );

  // Calculate sub-categories for external work
  const subCategories: SubCategory[] = [
    {
      name: 'Compound Wall & Gate',
      amount: Math.round(totalExternalCost * 0.35),
      percentage: 35,
      description: getCompoundWallDescription(qualityTier),
    },
    {
      name: 'Landscaping & Garden',
      amount: Math.round(totalExternalCost * 0.25),
      percentage: 25,
      description: getLandscapingDescription(qualityTier),
    },
    {
      name: 'Parking Area',
      amount: Math.round(totalExternalCost * 0.2) + parkingAdjustment,
      percentage: 20,
      description: getParkingDescription(parkingType || 'none', qualityTier),
    },
    {
      name: 'External Utilities',
      amount: Math.round(totalExternalCost * 0.15),
      percentage: 15,
      description: 'Water connection, electrical connection, sewage connection',
    },
    {
      name: 'Site Development',
      amount: Math.round(totalExternalCost * 0.05),
      percentage: 5,
      description: 'Site leveling, boundary marking, and miscellaneous works',
    },
  ];

  return {
    amount: Math.round(totalExternalCost + parkingAdjustment),
    percentage: COST_BREAKDOWN_PERCENTAGES.external * 100,
    subCategories,
  };
}

function getCompoundWallDescription(
  qualityTier: 'smart' | 'premium' | 'luxury'
): string {
  const wallSpecs = {
    smart: '6ft height brick wall with basic gate, cement plaster finish',
    premium: '6ft height brick wall with steel gate, good quality finish',
    luxury: '7ft height block wall with designer gate, premium finish',
  };
  return wallSpecs[qualityTier];
}

function getLandscapingDescription(
  qualityTier: 'smart' | 'premium' | 'luxury'
): string {
  const landscapeSpecs = {
    smart: 'Basic lawn, local plants, simple pathways',
    premium: 'Designed garden, mixed plants, paved pathways',
    luxury: 'Professional landscaping, imported plants, premium pathways',
  };
  return landscapeSpecs[qualityTier];
}

function getParkingDescription(
  parkingType: 'open' | 'covered' | 'none',
  qualityTier: 'smart' | 'premium' | 'luxury'
): string {
  if (parkingType === 'none') {
    return 'No dedicated parking area';
  } else if (parkingType === 'open') {
    const openSpecs = {
      smart: 'Plain concrete parking with basic marking',
      premium: 'Interlocking tiles parking with proper drainage',
      luxury: 'Premium tiles parking with landscaping',
    };
    return openSpecs[qualityTier];
  } else {
    const coveredSpecs = {
      smart: 'Basic MS structure covered parking',
      premium: 'Good quality MS structure with proper roofing',
      luxury: 'Designer covered parking with premium materials',
    };
    return coveredSpecs[qualityTier];
  }
}

function calculateParkingCost(
  builtUpArea: number,
  parkingType: 'open' | 'covered' | 'none',
  qualityTier: 'smart' | 'premium' | 'luxury'
): number {
  // Return 0 if no parking
  if (parkingType === 'none') {
    return 0;
  }

  // Assume 2-car parking (300 sqft)
  const parkingArea = 300;

  if (parkingType === 'open') {
    const openRates = {
      smart: 120, // Plain concrete per sqft
      premium: 180, // Interlocking tiles per sqft
      luxury: 250, // Premium tiles per sqft
    };
    return parkingArea * openRates[qualityTier];
  } else {
    const coveredRates = {
      smart: 450, // Basic structure per sqft
      premium: 650, // Good quality structure per sqft
      luxury: 900, // Premium structure per sqft
    };
    return parkingArea * coveredRates[qualityTier];
  }
}

/**
 * Calculate compound wall cost based on perimeter
 */
export function calculateCompoundWallCost(
  plotArea: number,
  qualityTier: 'smart' | 'premium' | 'luxury'
): { perimeter: number; cost: number; gateIncluded: boolean } {
  // Assume square plot for perimeter calculation
  const sideLength = Math.sqrt(plotArea);
  const perimeter = sideLength * 4;

  const wallRates = {
    smart: 850, // per running foot for 6ft height brick wall
    premium: 1200, // per running foot for 6ft height with better finish
    luxury: 1600, // per running foot for 7ft height premium wall
  };

  const wallCost = perimeter * wallRates[qualityTier];

  // Gate cost included in the rate
  return {
    perimeter: Math.round(perimeter),
    cost: Math.round(wallCost),
    gateIncluded: true,
  };
}

/**
 * Calculate landscaping cost based on open area
 */
export function calculateLandscapingCost(
  openArea: number,
  qualityTier: 'smart' | 'premium' | 'luxury',
  landscapeType: 'basic' | 'designed' | 'premium' = 'basic'
): number {
  const landscapeRates = {
    smart: {
      basic: 45, // Basic lawn and local plants
      designed: 65, // Simple designed garden
      premium: 85, // Better plants and design
    },
    premium: {
      basic: 75, // Good quality lawn and plants
      designed: 110, // Professional design
      premium: 150, // Premium plants and materials
    },
    luxury: {
      basic: 120, // High-end materials
      designed: 180, // Professional landscaping
      premium: 250, // Luxury landscaping with imports
    },
  };

  return openArea * landscapeRates[qualityTier][landscapeType];
}

/**
 * Calculate external utilities connection cost
 */
export function calculateUtilitiesCost(
  qualityTier: 'smart' | 'premium' | 'luxury'
): {
  water: number;
  electricity: number;
  sewage: number;
  total: number;
} {
  const utilityCosts = {
    smart: {
      water: 15000, // Basic water connection
      electricity: 25000, // Standard electrical connection
      sewage: 20000, // Basic sewage connection
    },
    premium: {
      water: 25000, // Better quality water connection
      electricity: 35000, // Three-phase electrical connection
      sewage: 30000, // Better sewage connection
    },
    luxury: {
      water: 40000, // Premium water connection with backup
      electricity: 50000, // High-capacity electrical connection
      sewage: 45000, // Premium sewage treatment connection
    },
  };

  const costs = utilityCosts[qualityTier];
  return {
    ...costs,
    total: costs.water + costs.electricity + costs.sewage,
  };
}
