/**
 * Screen Reader Support and ARIA Enhancement Utilities
 * Provides comprehensive screen reader support for WCAG 2.1 AA compliance
 */

import { focusManager } from './focus-management';

export interface AriaLiveRegion {
  element: HTMLElement;
  level: 'polite' | 'assertive';
  atomic: boolean;
}

export interface ScreenReaderContent {
  label?: string;
  description?: string;
  role?: string;
  state?: Record<string, string | boolean>;
  properties?: Record<string, string | boolean>;
  live?: 'polite' | 'assertive' | 'off';
}

/**
 * Screen Reader Support Manager
 */
export class ScreenReaderManager {
  private static instance: ScreenReaderManager;
  private liveRegions: Map<string, AriaLiveRegion> = new Map();
  private announceQueue: string[] = [];
  private isProcessingQueue = false;

  constructor() {
    this.createDefaultLiveRegions();
    this.setupGlobalListeners();
  }

  static getInstance(): ScreenReaderManager {
    if (!ScreenReaderManager.instance) {
      ScreenReaderManager.instance = new ScreenReaderManager();
    }
    return ScreenReaderManager.instance;
  }

  /**
   * Create a live region for announcements
   */
  createLiveRegion(
    id: string, 
    level: 'polite' | 'assertive' = 'polite',
    atomic = true
  ): HTMLElement {
    const existing = this.liveRegions.get(id);
    if (existing) {
      return existing.element;
    }

    const element = document.createElement('div');
    element.id = `sr-${id}`;
    element.className = 'sr-only';
    element.setAttribute('aria-live', level);
    element.setAttribute('aria-atomic', atomic.toString());
    element.setAttribute('aria-relevant', 'additions text');
    
    document.body.appendChild(element);
    
    this.liveRegions.set(id, { element, level, atomic });
    return element;
  }

  /**
   * Announce text to screen readers
   */
  announce(
    message: string, 
    priority: 'polite' | 'assertive' = 'polite',
    delay = 0
  ): void {
    if (!message.trim()) return;

    if (delay > 0) {
      setTimeout(() => this.announce(message, priority), delay);
      return;
    }

    const regionId = priority === 'assertive' ? 'alerts' : 'announcements';
    const region = this.liveRegions.get(regionId);
    
    if (region) {
      // Queue announcements to avoid overwhelming screen readers
      this.announceQueue.push(message);
      this.processAnnounceQueue(region);
    }
  }

  /**
   * Announce form validation errors
   */
  announceValidationError(fieldName: string, error: string): void {
    const message = `Error in ${fieldName}: ${error}`;
    this.announce(message, 'assertive');
  }

  /**
   * Announce successful actions
   */
  announceSuccess(message: string): void {
    this.announce(`Success: ${message}`, 'polite');
  }

  /**
   * Announce loading states
   */
  announceLoading(action: string): void {
    this.announce(`Loading: ${action}`, 'polite');
  }

  /**
   * Announce progress updates
   */
  announceProgress(current: number, total: number, action: string): void {
    const percentage = Math.round((current / total) * 100);
    this.announce(`${action}: ${percentage}% complete, ${current} of ${total}`, 'polite');
  }

  /**
   * Enhanced element labeling
   */
  enhanceElement(element: HTMLElement, content: ScreenReaderContent): void {
    // Apply ARIA label
    if (content.label) {
      element.setAttribute('aria-label', content.label);
    }

    // Apply ARIA description
    if (content.description) {
      const descId = this.createDescription(content.description);
      element.setAttribute('aria-describedby', descId);
    }

    // Apply ARIA role
    if (content.role) {
      element.setAttribute('role', content.role);
    }

    // Apply ARIA state
    if (content.state) {
      Object.entries(content.state).forEach(([key, value]) => {
        element.setAttribute(`aria-${key}`, value.toString());
      });
    }

    // Apply ARIA properties
    if (content.properties) {
      Object.entries(content.properties).forEach(([key, value]) => {
        element.setAttribute(`aria-${key}`, value.toString());
      });
    }

    // Apply live region
    if (content.live) {
      element.setAttribute('aria-live', content.live);
    }
  }

  /**
   * Create accessible form field
   */
  enhanceFormField(
    field: HTMLElement,
    label: string,
    options: {
      description?: string;
      required?: boolean;
      invalid?: boolean;
      errorMessage?: string;
      helpText?: string;
    } = {}
  ): void {
    const fieldId = field.id || `field-${Date.now()}`;
    field.id = fieldId;

    // Create or update label
    let labelElement = document.querySelector(`label[for="${fieldId}"]`) as HTMLLabelElement;
    if (!labelElement) {
      labelElement = document.createElement('label');
      labelElement.setAttribute('for', fieldId);
      field.parentNode?.insertBefore(labelElement, field);
    }
    labelElement.textContent = label;

    // Handle required state
    if (options.required) {
      field.setAttribute('aria-required', 'true');
      if (!labelElement.textContent.includes('*')) {
        labelElement.innerHTML = `${labelElement.textContent} <span aria-label="required">*</span>`;
      }
    }

    // Handle invalid state
    if (options.invalid) {
      field.setAttribute('aria-invalid', 'true');
      if (options.errorMessage) {
        const errorId = this.createErrorMessage(fieldId, options.errorMessage);
        field.setAttribute('aria-describedby', errorId);
      }
    } else {
      field.setAttribute('aria-invalid', 'false');
    }

    // Handle help text
    if (options.helpText) {
      const helpId = this.createHelpText(fieldId, options.helpText);
      const describedBy = field.getAttribute('aria-describedby');
      field.setAttribute(
        'aria-describedby',
        describedBy ? `${describedBy} ${helpId}` : helpId
      );
    }
  }

  /**
   * Create accessible table
   */
  enhanceTable(table: HTMLTableElement, caption?: string): void {
    // Add table role if not present
    if (!table.getAttribute('role')) {
      table.setAttribute('role', 'table');
    }

    // Add caption if provided
    if (caption && !table.caption) {
      const captionElement = table.createCaption();
      captionElement.textContent = caption;
    }

    // Enhance headers
    const headers = table.querySelectorAll('th');
    headers.forEach((header, index) => {
      if (!header.id) {
        header.id = `table-header-${index}`;
      }
      header.setAttribute('scope', header.closest('thead') ? 'col' : 'row');
    });

    // Enhance data cells
    const cells = table.querySelectorAll('td');
    cells.forEach(cell => {
      const row = cell.closest('tr');
      const headers = row?.querySelectorAll('th');
      if (headers && headers.length > 0) {
        const headerIds = Array.from(headers).map(h => h.id).join(' ');
        cell.setAttribute('headers', headerIds);
      }
    });
  }

  /**
   * Create accessible modal
   */
  enhanceModal(modal: HTMLElement, title: string): void {
    modal.setAttribute('role', 'dialog');
    modal.setAttribute('aria-modal', 'true');
    
    // Create or find title
    let titleElement = modal.querySelector('h1, h2, h3, [role="heading"]') as HTMLElement;
    if (!titleElement) {
      titleElement = document.createElement('h2');
      titleElement.textContent = title;
      modal.insertBefore(titleElement, modal.firstChild);
    }
    
    if (!titleElement.id) {
      titleElement.id = `modal-title-${Date.now()}`;
    }
    
    modal.setAttribute('aria-labelledby', titleElement.id);

    // Ensure modal is properly hidden/shown
    const observer = new MutationObserver(mutations => {
      mutations.forEach(mutation => {
        if (mutation.type === 'attributes' && mutation.attributeName === 'aria-hidden') {
          const isHidden = modal.getAttribute('aria-hidden') === 'true';
          if (!isHidden) {
            this.announce(`Dialog opened: ${title}`);
          }
        }
      });
    });
    
    observer.observe(modal, { attributes: true, attributeFilter: ['aria-hidden'] });
  }

  /**
   * Create accessible navigation
   */
  enhanceNavigation(nav: HTMLElement, label: string): void {
    nav.setAttribute('role', 'navigation');
    nav.setAttribute('aria-label', label);

    // Enhance navigation links
    const links = nav.querySelectorAll('a');
    links.forEach(link => {
      if (link.getAttribute('aria-current')) {
        // Already marked as current
        return;
      }

      // Check if current page
      if (link.href === window.location.href) {
        link.setAttribute('aria-current', 'page');
      }
    });
  }

  /**
   * Create screen reader-only text
   */
  createSROnlyText(text: string): HTMLElement {
    const span = document.createElement('span');
    span.className = 'sr-only';
    span.textContent = text;
    return span;
  }

  /**
   * Get screen reader detection
   */
  detectScreenReader(): boolean {
    // Check for common screen reader indicators
    const indicators = [
      'speechSynthesis' in window,
      'webkitSpeechSynthesis' in window,
      navigator.userAgent.includes('NVDA'),
      navigator.userAgent.includes('JAWS'),
      navigator.userAgent.includes('WindowEyes'),
      navigator.userAgent.includes('VoiceOver'),
      navigator.userAgent.includes('Orca'),
      navigator.userAgent.includes('Talkback')
    ];

    return indicators.some(Boolean);
  }

  private createDefaultLiveRegions(): void {
    this.createLiveRegion('announcements', 'polite');
    this.createLiveRegion('alerts', 'assertive');
    this.createLiveRegion('status', 'polite');
  }

  private setupGlobalListeners(): void {
    // Listen for focus changes to announce context
    document.addEventListener('focusin', (event) => {
      const target = event.target as HTMLElement;
      if (target && target.getAttribute('aria-label')) {
        const role = target.getAttribute('role') || target.tagName.toLowerCase();
        const label = target.getAttribute('aria-label');
        
        // Announce role and label for complex widgets
        if (['button', 'link', 'menuitem', 'tab', 'option'].includes(role)) {
          // Small delay to let other focus events settle
          setTimeout(() => {
            this.announce(`${role}, ${label}`, 'polite');
          }, 100);
        }
      }
    });

    // Listen for page navigation
    window.addEventListener('popstate', () => {
      this.announce('Page changed', 'polite', 500);
    });
  }

  private processAnnounceQueue(region: AriaLiveRegion): void {
    if (this.isProcessingQueue || this.announceQueue.length === 0) {
      return;
    }

    this.isProcessingQueue = true;
    const message = this.announceQueue.shift()!;
    
    // Clear and set new content
    region.element.textContent = '';
    setTimeout(() => {
      region.element.textContent = message;
      
      // Process next message after delay
      setTimeout(() => {
        this.isProcessingQueue = false;
        if (this.announceQueue.length > 0) {
          this.processAnnounceQueue(region);
        }
      }, 1000);
    }, 50);
  }

  private createDescription(text: string): string {
    const id = `desc-${Date.now()}`;
    const element = document.createElement('div');
    element.id = id;
    element.className = 'sr-only';
    element.textContent = text;
    document.body.appendChild(element);
    return id;
  }

  private createErrorMessage(fieldId: string, error: string): string {
    const id = `${fieldId}-error`;
    let element = document.getElementById(id);
    
    if (!element) {
      element = document.createElement('div');
      element.id = id;
      element.className = 'text-red-600 text-sm mt-1';
      element.setAttribute('role', 'alert');
      
      const field = document.getElementById(fieldId);
      if (field && field.parentNode) {
        field.parentNode.insertBefore(element, field.nextSibling);
      }
    }
    
    element.textContent = error;
    return id;
  }

  private createHelpText(fieldId: string, help: string): string {
    const id = `${fieldId}-help`;
    let element = document.getElementById(id);
    
    if (!element) {
      element = document.createElement('div');
      element.id = id;
      element.className = 'text-gray-600 text-sm mt-1';
      
      const field = document.getElementById(fieldId);
      if (field && field.parentNode) {
        field.parentNode.insertBefore(element, field.nextSibling);
      }
    }
    
    element.textContent = help;
    return id;
  }
}

// Export singleton instance
export const screenReader = ScreenReaderManager.getInstance();

/**
 * React hook for screen reader support
 */
export function useScreenReader() {
  return {
    announce: screenReader.announce.bind(screenReader),
    announceSuccess: screenReader.announceSuccess.bind(screenReader),
    announceError: screenReader.announceValidationError.bind(screenReader),
    announceLoading: screenReader.announceLoading.bind(screenReader),
    announceProgress: screenReader.announceProgress.bind(screenReader),
    enhanceElement: screenReader.enhanceElement.bind(screenReader),
    enhanceFormField: screenReader.enhanceFormField.bind(screenReader),
    enhanceTable: screenReader.enhanceTable.bind(screenReader),
    enhanceModal: screenReader.enhanceModal.bind(screenReader),
    enhanceNavigation: screenReader.enhanceNavigation.bind(screenReader),
    createSROnlyText: screenReader.createSROnlyText.bind(screenReader),
    detectScreenReader: screenReader.detectScreenReader.bind(screenReader)
  };
}