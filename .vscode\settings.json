{
  // Editor settings
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit",
    "source.organizeImports": "never"
  },
  "editor.rulers": [80, 120],
  "editor.tabSize": 2,
  "editor.insertSpaces": true,
  "editor.trimAutoWhitespace": true,
  "files.trimTrailingWhitespace": true,
  "files.insertFinalNewline": true,

  // TypeScript settings
  "typescript.preferences.importModuleSpecifier": "relative",
  "typescript.suggest.autoImports": true,
  "typescript.format.enable": false, // Use Prettier instead
  "javascript.format.enable": false, // Use Prettier instead

  // ESLint settings
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact"
  ],
  "eslint.workingDirectories": ["."],

  // Prettier settings
  "prettier.requireConfig": true,
  "prettier.useEditorConfig": false,

  // Tailwind CSS settings
  "tailwindCSS.includeLanguages": {
    "typescript": "javascript",
    "typescriptreact": "javascript"
  },
  "tailwindCSS.experimental.classRegex": [
    ["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"],
    ["cx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]
  ],

  // File associations
  "files.associations": {
    "*.css": "tailwindcss"
  },

  // Emmet settings
  "emmet.includeLanguages": {
    "typescript": "html",
    "typescriptreact": "html"
  },

  // Search settings
  "search.exclude": {
    "**/node_modules": true,
    "**/.next": true,
    "**/dist": true,
    "**/.git": true,
    "**/coverage": true,
    "**/.nyc_output": true
  },

  // Explorer settings
  "explorer.fileNesting.enabled": true,
  "explorer.fileNesting.patterns": {
    "*.ts": "${capture}.js",
    "*.tsx": "${capture}.js",
    "package.json": "package-lock.json, yarn.lock, pnpm-lock.yaml",
    "*.config.js": "*.config.*.js",
    "tsconfig.json": "tsconfig.*.json",
    "next.config.js": "next-env.d.ts"
  },

  // Git settings
  "git.enableSmartCommit": true,
  "git.confirmSync": false,

  // Terminal settings
  "terminal.integrated.defaultProfile.linux": "bash",
  "terminal.integrated.cwd": "${workspaceFolder}"
}
